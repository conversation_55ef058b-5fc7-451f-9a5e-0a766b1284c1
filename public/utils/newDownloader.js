/**
 * 新的弹窗式下载器
 * 参考播放功能的实现方式
 */

class DownloadManager {
  constructor() {
    this.currentDownload = null;
    this.downloadHistory = new Map();
    this.init();
  }

  init() {
    // 绑定下载按钮点击事件 - 参考播放按钮的实现
    this.bindDownloadButtons();

    // 绑定弹窗事件
    this.bindModalEvents();
  }

  bindDownloadButtons() {
    // 使用事件委托来处理动态添加的下载按钮
    document.addEventListener('click', (event) => {
      const button = event.target.closest('[data-action="download"]');
      if (!button) return;

      // 获取曲目信息 - 参考播放功能的数据获取方式
      const container = button.closest('.download-button-container');
      if (!container) {
        console.error('Download container not found');
        return;
      }

      const trackId = container.dataset.trackId;
      const audioUrl = container.dataset.trackAudio;
      const originalUrl = container.dataset.trackOriginalUrl;
      const originalContent = container.dataset.trackOriginalContent;

      // 优先从数据属性获取歌曲标题
      let title = container.dataset.trackTitle;

      // 如果数据属性中没有，则尝试从DOM中获取
      if (!title) {
        const trackRow = container.closest('.grid, tr, .track-row');

        // 尝试多种方式获取标题
        let titleElement = trackRow?.querySelector('.text-white');
        if (!titleElement) {
          titleElement = trackRow?.querySelector('.track-title');
        }
        if (!titleElement) {
          titleElement = trackRow?.querySelector('[class*="title"]');
        }
        if (!titleElement) {
          // 查找包含歌曲名的元素（通常是第一个白色文字）
          const whiteTexts = trackRow?.querySelectorAll('.text-white');
          titleElement = whiteTexts?.[0];
        }
        title = titleElement?.textContent?.trim() || 'Unknown Track';
      }

      console.log('Extracted track title:', title);
      console.log('Container dataset:', container.dataset);

      // 获取所有可能的下载URL
      const allUrls = this.getAllDownloadUrls(container);

      const trackInfo = {
        id: trackId,
        title: title || 'Unknown Track',
        audioUrl: allUrls[0], // 主要URL
        allUrls: allUrls, // 所有备用URL
        originalUrl,
        originalContent
      };

      console.log('Track info for download:', trackInfo);

      if (!trackInfo.audioUrl) {
        this.showError('Download URL not found');
        return;
      }

      // 开始下载
      this.startDownload(trackInfo);
    });
  }

  getAllDownloadUrls(container) {
    // 优先级：url > originalUrl > originalContent.url > downloadUrl
    const urls = [];

    // 1. 优先使用API下载链接
    if (container.dataset.trackAudio) {
      urls.push(container.dataset.trackAudio);
    }

    // 2. 使用原始URL
    if (container.dataset.trackOriginalUrl) {
      urls.push(container.dataset.trackOriginalUrl);
    }

    // 3. 尝试从originalContent获取URL
    try {
      const originalContentStr = container.dataset.trackOriginalContent;
      if (originalContentStr) {
        const originalContent = JSON.parse(originalContentStr);
        if (originalContent.url) {
          urls.push(originalContent.url);
        }
      }
    } catch (e) {
      console.warn('Failed to parse originalContent:', e);
    }

    // 4. 回退到downloadUrl
    if (container.dataset.downloadUrl) {
      urls.push(container.dataset.downloadUrl);
    }

    // 去重
    const uniqueUrls = [...new Set(urls)];
    console.log('Available URLs for download:', uniqueUrls);
    return uniqueUrls;
  }

  getDownloadUrl(container) {
    const urls = this.getAllDownloadUrls(container);
    return urls[0]; // 返回第一个可用的URL
  }

  bindModalEvents() {
    // 取消按钮
    const cancelBtn = document.getElementById('cancel-download');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        this.cancelDownload();
      });
    }

    // 点击背景关闭
    const modal = document.getElementById('download-modal');
    const backdrop = modal?.querySelector('.modal-backdrop');
    if (backdrop) {
      backdrop.addEventListener('click', () => {
        this.cancelDownload();
      });
    }

    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal?.classList.contains('hidden')) {
        this.cancelDownload();
      }
    });
  }

  async startDownload(trackInfo) {
    console.log('Starting download for:', trackInfo);

    // 检查是否已有下载在进行
    if (this.currentDownload) {
      this.showError('Another download is in progress');
      return;
    }

    // 显示下载弹窗
    this.showModal(trackInfo);

    // 立即显示随机5-10%进度，让用户感知到下载已经开始
    const initialProgress = Math.floor(Math.random() * 6) + 5; // 5-10%
    this.updateProgress(initialProgress, 'Starting download...');

    // 设置当前下载
    this.currentDownload = {
      ...trackInfo,
      status: 'downloading',
      startTime: Date.now(),
      abortController: new AbortController(),
      initialProgress: initialProgress // 记录初始进度
    };

    try {
      // 开始实际下载
      await this.performDownload(trackInfo);
    } catch (error) {
      console.error('Download failed:', error);
      this.showError(error.message);
    }
  }

  async performDownload(trackInfo) {
    const { allUrls, title } = trackInfo;
    const urlsToTry = allUrls || [trackInfo.audioUrl];

    // 不重置进度，保持之前的随机初始进度
    // this.updateProgress(0); // 删除这行，保持初始进度

    // 尝试每个URL直到成功
    for (let i = 0; i < urlsToTry.length; i++) {
      const audioUrl = urlsToTry[i];
      console.log(`Trying download URL ${i + 1}/${urlsToTry.length}:`, audioUrl);

      try {
        // 更新状态显示当前尝试的URL
        if (i > 0) {
          this.updateProgress(this.currentDownload.initialProgress, `Trying source ${i + 1}...`);
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 根据URL域名设置不同的headers
        const headers = {
          'Accept': 'audio/*,*/*;q=0.9',
          'Accept-Language': 'en-US,en;q=0.5',
          'DNT': '1',
          'Connection': 'keep-alive'
        };

        // 为不同域名设置特定的headers
        if (audioUrl.includes('pillowcase.su')) {
          headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
          headers['Referer'] = 'https://pillowcase.su/';
          headers['Accept-Encoding'] = 'gzip, deflate, br';
          headers['Upgrade-Insecure-Requests'] = '1';
        } else if (audioUrl.includes('music.froste.lol')) {
          headers['User-Agent'] = 'Mozilla/5.0 (compatible; TrackerHive/1.0)';
          headers['Origin'] = window.location.origin;
          headers['Referer'] = window.location.href;
        } else {
          headers['User-Agent'] = 'Mozilla/5.0 (compatible; TrackerHive/1.0)';
        }

        const response = await fetch(audioUrl, {
          signal: this.currentDownload.abortController.signal,
          headers: headers,
          mode: 'cors'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentLength = response.headers.get('content-length');
        const total = contentLength ? parseInt(contentLength, 10) : 0;

        const reader = response.body.getReader();
        const chunks = [];
        let loaded = 0;
        let lastTime = Date.now();
        let lastLoaded = 0;

        // 不重置进度，保持初始的随机进度
        // this.updateProgress(0); // 删除这行

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          chunks.push(value);
          loaded += value.length;

          // 计算进度和速度
          let progress;
          if (total > 0) {
            // 真实下载进度：从初始进度到95%
            const downloadProgress = (loaded / total) * 100;
            const initialProgress = this.currentDownload.initialProgress || 5;
            progress = Math.min(95, initialProgress + Math.round(downloadProgress * (95 - initialProgress) / 100));
          } else {
            // 模拟进度：从初始进度开始慢慢增长
            const elapsed = Date.now() - this.currentDownload.startTime;
            const initialProgress = this.currentDownload.initialProgress || 5;
            progress = Math.min(90, initialProgress + Math.round(elapsed / 1000) * 3); // 每秒增加3%
          }

          const currentTime = Date.now();
          const timeDiff = currentTime - lastTime;

          if (timeDiff > 500) { // 每500ms更新一次
            this.updateProgress(progress, 'Downloading...');
            lastTime = currentTime;
            lastLoaded = loaded;
          }
        }

        // 下载完成，显示处理状态
        this.updateProgress(95, 'Processing file...');

        // 创建并触发下载
        const blob = new Blob(chunks);
        const cleanFileName = this.sanitizeFileName(trackInfo.title);

        // 短暂延迟，让用户看到处理状态
        await new Promise(resolve => setTimeout(resolve, 500));

        this.updateProgress(100, 'Download complete!');
        this.triggerDownload(blob, `${cleanFileName}.mp3`);

        // 显示成功消息
        setTimeout(() => {
          this.hideModal();
          this.showNotification(`Downloaded: ${trackInfo.title}`, 'success');
        }, 1000);

        return; // 成功下载，退出循环

      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Download was cancelled');
          this.updateProgress(0, 'Cancelled');
          return;
        }

        console.error(`Download failed for URL ${i + 1}:`, error);

        // 如果这是最后一个URL，抛出错误
        if (i === urlsToTry.length - 1) {
          throw new Error(`All download sources failed. Last error: ${error.message}`);
        }

        // 否则继续尝试下一个URL
        continue;
      } finally {
        // 只在最后一次尝试或成功时清理
        if (i === urlsToTry.length - 1) {
          this.currentDownload = null;
        }
      }
    }
  }

  updateProgress(percent) {
    const progressText = document.getElementById('download-progress-percent');
    const progressFill = document.querySelector('.progress-fill');

    if (progressText) {
      progressText.textContent = `${percent}%`;
    }

    if (progressFill) {
      const circumference = 2 * Math.PI * 40; // r=40 (for 90px circle)
      const offset = circumference - (percent / 100) * circumference;
      progressFill.style.strokeDashoffset = offset;
    }
  }

  // 简化版本，不显示详细信息

  showModal(trackInfo) {
    const modal = document.getElementById('download-modal');
    const titleElement = document.getElementById('download-track-title');

    // 更新歌曲标题
    if (titleElement) {
      titleElement.textContent = trackInfo.title || 'Unknown Track';
    }

    if (modal) {
      modal.classList.remove('hidden');
    }

    // 不重置进度，让调用者控制初始进度
  }

  hideModal() {
    const modal = document.getElementById('download-modal');
    if (modal) {
      modal.classList.add('hidden');
    }

    // 如果有正在进行的下载，取消它
    if (this.currentDownload) {
      this.cancelDownload();
    }
  }

  cancelDownload() {
    if (this.currentDownload && this.currentDownload.abortController) {
      this.currentDownload.abortController.abort();
      this.currentDownload = null;
      this.updateProgress(0, 'Cancelled');
      setTimeout(() => this.hideModal(), 500); // 短暂显示取消状态
    }
  }

  // 工具函数
  sanitizeFileName(filename) {
    return filename
      .replace(/[<>:"/\\|?*]/g, '')
      .replace(/[\[\]]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 200) || 'Unknown Track';
  }

  triggerDownload(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.textContent = message;
    
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 16px',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '500',
      fontSize: '14px',
      zIndex: '10001',
      transform: 'translateX(100%)',
      transition: 'transform 0.3s ease',
      backgroundColor: type === 'success' ? '#1DB954' : type === 'error' ? '#f44336' : '#333'
    });

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  showError(message) {
    console.error('Download error:', message);
    this.showNotification(`Download failed: ${message}`, 'error');
    this.hideModal();
  }
}

// 自动初始化
let downloadManager;

function initializeDownloadManager() {
  if (!downloadManager) {
    downloadManager = new DownloadManager();
    window.downloadManager = downloadManager; // 全局访问
  }
}

// 多种初始化方式
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeDownloadManager);
} else {
  initializeDownloadManager();
}

// 延迟初始化确保所有组件加载完成
setTimeout(initializeDownloadManager, 1000);

export default DownloadManager;
