# Allow Google AdSense bot
User-agent: Mediapartners-Google
Allow: /
Allow: /ads.txt

User-agent: *
Allow: /
Allow: /ads.txt

# Disallow Cloudflare system directories
Disallow: /cdn-cgi/

# Allow all languages (except English which is default without prefix)
Allow: /ar/
Allow: /pt/

# Allow artist pages
Allow: /artists/
Allow: /artists/ye/
Allow: /artists/playboi-carti/

# Allow non-English artist pages
Allow: /*/artists/
Allow: /*/artists/ye/
Allow: /*/artists/playboi-carti/

# Allow Ye category pages with data (high priority)
Allow: /artists/ye/art/
Allow: /artists/ye/recent/
Allow: /artists/ye/best-of/
Allow: /artists/ye/unreleased/

# Allow non-English Ye category pages
Allow: /*/artists/ye/art/
Allow: /*/artists/ye/recent/
Allow: /*/artists/ye/best-of/
Allow: /*/artists/ye/unreleased/

# Allow Ye era pages (only for unreleased category)
Allow: /artists/ye/unreleased/eras/
Allow: /artists/ye/unreleased/eras/*/

# Allow non-English Ye era pages
Allow: /*/artists/ye/unreleased/eras/
Allow: /*/artists/ye/unreleased/eras/*/

# Allow Playboi Carti category pages with data (high priority)
Allow: /artists/playboi-carti/recent/
Allow: /artists/playboi-carti/best-of/
Allow: /artists/playboi-carti/unreleased/

# Allow non-English Playboi Carti category pages
Allow: /*/artists/playboi-carti/recent/
Allow: /*/artists/playboi-carti/best-of/
Allow: /*/artists/playboi-carti/unreleased/

# Allow Playboi Carti era pages (only for unreleased category)
Allow: /artists/playboi-carti/unreleased/eras/
Allow: /artists/playboi-carti/unreleased/eras/*/

# Allow non-English Playboi Carti era pages
Allow: /*/artists/playboi-carti/unreleased/eras/
Allow: /*/artists/playboi-carti/unreleased/eras/*/

# Sitemaps
Sitemap: https://aitrackerhive.com/sitemap-index.xml
Sitemap: https://aitrackerhive.com/sitemap.xml
Sitemap: https://aitrackerhive.com/sitemap-artists.xml
Sitemap: https://aitrackerhive.com/sitemap-categories.xml
Sitemap: https://aitrackerhive.com/sitemap-eras.xml
Sitemap: https://aitrackerhive.com/sitemap-carti-eras.xml

# 搜索引擎爬虫设置
# Google使用自己的抓取控制机制，通过Search Console管理
User-agent: Googlebot
Allow: /

User-agent: bingbot
Crawl-delay: 1

User-agent: Baiduspider
Crawl-delay: 1

User-agent: YandexBot
Crawl-delay: 1

User-agent: Sogou spider
Crawl-delay: 1

User-agent: Sosospider
Crawl-delay: 1

User-agent: YoudaoBot
Crawl-delay: 1

User-agent: YetiBot
Crawl-delay: 1

User-agent: Yahoo! Slurp
Crawl-delay: 1

User-agent: rdfbot
Crawl-delay: 1

User-agent: Seznambot
# 使用标准的Crawl-delay而不是Request-rate
Crawl-delay: 1
