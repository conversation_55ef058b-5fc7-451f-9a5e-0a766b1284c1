{"id": "donda-v2", "name": "<PERSON><PERSON> [V2]", "description": "After taking a break from music in early 2021, <PERSON> went to work at the Pio Pico studio in LA with an entirely new vision for the album. While the name remained the same, the sound shifted to be more experimental and less soulful. Working with producers such as <PERSON><PERSON>, <PERSON><PERSON>, and Digital Nas, <PERSON> went through hundreds of beats, laying down vocals and trying to come up with ideas for songs. This era would continue until <PERSON><PERSON><PERSON> decided to finalize the album, shifting to the more minimalistic release sound.", "backgroundColor": "rgb(57, 138, 150)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17G1Sw82ikh5ZtaULNJVXXU6U_bfObWh8SUZgJmxwmokodFyqHmzDsgGxTmQiOec0jZ-pWS9Gr3OWr33w8OONGLafkevRB7L3btudrVN_s5W6N7leTPYfkln-ciobIEl4smakVzuvVYdJPMe7m13cg?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "88-idea-2", "name": "88 Idea 2", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: 210513 Gallery - 88 Idea 2\nMumble freestyle done during the May 13th session.", "length": "58.78", "fileDate": 16531776, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/96ea9f93be42c5ccfdd40b2aa37abcc2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/96ea9f93be42c5ccfdd40b2aa37abcc2\", \"key\": \"88 Idea 2\", \"title\": \"88 Idea 2\", \"artists\": \"(prod. 88-Keys)\", \"description\": \"OG Filename: 210513 Gallery - 88 Idea 2\\nMumble freestyle done during the May 13th session.\", \"date\": 16531776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1fd44579aa36ed49d1e250ffcf2e148a\", \"url\": \"https://api.pillowcase.su/api/download/1fd44579aa36ed49d1e250ffcf2e148a\", \"size\": \"7.53 MB\", \"duration\": 58.78}", "aliases": [], "size": "7.53 MB"}, {"id": "9-10", "name": "9.10", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - 9.10\nMumble freestyle. Original snippet leaked December 22nd, 2022, with the full song originally leaked sped up, but then later at original speed.", "length": "101.93", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/18ff35450f441d25727e068b36c39d8b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/18ff35450f441d25727e068b36c39d8b\", \"key\": \"9.10\", \"title\": \"9.10\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - 9.10\\nMumble freestyle. Original snippet leaked December 22nd, 2022, with the full song originally leaked sped up, but then later at original speed.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ebb3af4d476b0309f9dcb9ce5fed0458\", \"url\": \"https://api.pillowcase.su/api/download/ebb3af4d476b0309f9dcb9ce5fed0458\", \"size\": \"8.22 MB\", \"duration\": 101.93}", "aliases": [], "size": "8.22 MB"}, {"id": "aboriginal", "name": "Aboriginal", "artists": [], "producers": ["Ojivolta", "88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 04 Aboriginal\nFreestyle with some mumble. Shares lines with \"Go Left\".", "length": "89.57", "fileDate": 16695936, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/08c9d0103086b00f9871448a0beffed0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08c9d0103086b00f9871448a0beffed0\", \"key\": \"Aboriginal\", \"title\": \"Aboriginal\", \"artists\": \"(prod. Ojivolta & 88-Keys)\", \"description\": \"OG Filename: 210510 Gallery Freestyles - 04 Aboriginal\\nFreestyle with some mumble. Shares lines with \\\"Go Left\\\".\", \"date\": 16695936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8e3b6a64eb3f3ea3535913dae88f39bf\", \"url\": \"https://api.pillowcase.su/api/download/8e3b6a64eb3f3ea3535913dae88f39bf\", \"size\": \"8.02 MB\", \"duration\": 89.57}", "aliases": [], "size": "8.02 MB"}, {"id": "ac-raw-materials", "name": "AC Raw Materials", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN AC Raw Materials\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "51.96", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/80a40accd239df4caf408b6c1b043cb6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80a40accd239df4caf408b6c1b043cb6\", \"key\": \"AC Raw Materials\", \"title\": \"AC Raw Materials\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN AC Raw Materials\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ad24b907816395d8523ad7ad2a8866df\", \"url\": \"https://api.pillowcase.su/api/download/ad24b907816395d8523ad7ad2a8866df\", \"size\": \"7.42 MB\", \"duration\": 51.96}", "aliases": [], "size": "7.42 MB"}, {"id": "back-then", "name": "Back Then", "artists": [], "producers": ["Ojivolta", "88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 00 Back Then\nFreestyle over an improvised beat that would later become the \"Thunder\" beat. Filename indicates this was the first of the three known freestyles for \"Thunder\" and the first overall freestyle from May 10th. \"Thunder\" hook vocals originate from this version. Only confirmed producer is <PERSON><PERSON><PERSON>, but it's likely that most of the production comes from Ojivolta as they have done similar improvisatory work in the past.", "length": "169.13", "fileDate": 16924896, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/be7de5646925d1280dc6521b571304c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/be7de5646925d1280dc6521b571304c0\", \"key\": \"Back Then\", \"title\": \"Back Then\", \"artists\": \"(prod. Ojivolta & 88-Keys)\", \"aliases\": [\"<PERSON>\"], \"description\": \"OG Filename: 210510 Gallery Freestyles - 00 Back Then\\nFreestyle over an improvised beat that would later become the \\\"Thunder\\\" beat. Filename indicates this was the first of the three known freestyles for \\\"Thunder\\\" and the first overall freestyle from May 10th. \\\"Thunder\\\" hook vocals originate from this version. Only confirmed producer is 88-Keys, but it's likely that most of the production comes from Ojivolta as they have done similar improvisatory work in the past.\", \"date\": 16924896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eb6383b68c65a13a7d7d3459d461b18f\", \"url\": \"https://api.pillowcase.su/api/download/eb6383b68c65a13a7d7d3459d461b18f\", \"size\": \"9.3 MB\", \"duration\": 169.13}", "aliases": ["Thunder"], "size": "9.3 MB"}, {"id": "bart", "name": "<PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210525 Gallery - Boogz 2 (<PERSON> 93bpm)\nRough BoogzDaBeast produced Gallery freestyle.", "length": "40.44", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d9d510362979b2e91a72465b9bdb4e13", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9d510362979b2e91a72465b9bdb4e13\", \"key\": \"<PERSON>\", \"title\": \"<PERSON>\", \"artists\": \"(prod. <PERSON>ogzDaBeast)\", \"description\": \"OG Filename: 210525 Gallery - Boogz 2 (Bart 93bpm)\\nRough BoogzDaBeast produced Gallery freestyle.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"881696246a0b41f02634d0030a711fca\", \"url\": \"https://api.pillowcase.su/api/download/881696246a0b41f02634d0030a711fca\", \"size\": \"7.24 MB\", \"duration\": 40.44}", "aliases": [], "size": "7.24 MB"}, {"id": "believe-what-i-say", "name": "Believe What I Say [V13]", "artists": ["<PERSON><PERSON><PERSON>", "STALONE"], "producers": ["Dem <PERSON>z", "BoogzDaBeast", "FnZ"], "notes": "Version with STALONE and <PERSON><PERSON><PERSON> on the hook and different production. Snippet leaked March 15th, 2021.", "length": "7.29", "fileDate": 16157664, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f37dd90770ce529fc684c23ee0924789", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f37dd90770ce529fc684c23ee0924789\", \"key\": \"Believe What I Say\", \"title\": \"Believe What I Say [V13]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON> Banton & STALONE) (prod. <PERSON><PERSON>z, BoogzDaBeast & FnZ)\", \"description\": \"Version with <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on the hook and different production. Snippet leaked March 15th, 2021.\", \"date\": 16157664, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e4be614acc2fa3de5c458c9e0a03886f\", \"url\": \"https://api.pillowcase.su/api/download/e4be614acc2fa3de5c458c9e0a03886f\", \"size\": \"6.71 MB\", \"duration\": 7.29}", "aliases": [], "size": "6.71 MB"}, {"id": "believe-what-i-say-8", "name": "Believe What I Say [V14]", "artists": ["<PERSON><PERSON><PERSON>", "STALONE"], "producers": ["Dem <PERSON>z", "BoogzDaBeast", "FnZ"], "notes": "OG Filename: Believe What I Say - 21.03.19\nFilename shown by a trusted source. Nothing else is known.", "length": "10.87", "fileDate": 16452288, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/62f895f263513f6d6c5cec89e3b77f91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/62f895f263513f6d6c5cec89e3b77f91\", \"key\": \"Believe What I Say\", \"title\": \"Believe What I Say [V14]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. B<PERSON><PERSON> Banton & STALONE) (prod. <PERSON><PERSON>, BoogzDaBeast & FnZ)\", \"description\": \"OG Filename: Believe What I Say - 21.03.19\\nFilename shown by a trusted source. Nothing else is known.\", \"date\": 16452288, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"615b089e744f3fb685771bc7d058db3d\", \"url\": \"https://api.pillowcase.su/api/download/615b089e744f3fb685771bc7d058db3d\", \"size\": \"6.76 MB\", \"duration\": 10.87}", "aliases": [], "size": "6.76 MB"}, {"id": "believe-what-i-say-9", "name": "Believe What I Say [V16]", "artists": [], "producers": ["Digital Nas"], "notes": "Version with Digital Nas production. From May 2021. Original snippet leaked on March 17th, 2022. Leaked as a bonus to the DONDA magazine groupbuy. The drums and extra production elements were in the leak, but the missing rest of the beat and vocals were edited on using release stems.", "length": "242.49", "fileDate": 16539552, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/57bd67894bf07d5677825deb5172279c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/57bd67894bf07d5677825deb5172279c\", \"key\": \"Believe What I Say\", \"title\": \"Believe What I Say [V16]\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"Version with Digital Nas production. From May 2021. Original snippet leaked on March 17th, 2022. Leaked as a bonus to the DONDA magazine groupbuy. The drums and extra production elements were in the leak, but the missing rest of the beat and vocals were edited on using release stems.\", \"date\": 16539552, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7388c2a770eb52c5dc0f2d2f1e7792a3\", \"url\": \"https://api.pillowcase.su/api/download/7388c2a770eb52c5dc0f2d2f1e7792a3\", \"size\": \"10.5 MB\", \"duration\": 242.49}", "aliases": [], "size": "10.5 MB"}, {"id": "benetic-hylota", "name": "Benetic Hylota", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Benetic Hylota\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "255.84", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fb18b913f825c8550189cc83a821aff7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fb18b913f825c8550189cc83a821aff7\", \"key\": \"Benetic Hylota\", \"title\": \"Benetic Hylota\", \"artists\": \"(prod. <PERSON><PERSON>z<PERSON>aBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Benetic Hylota\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8a884df44376bf588688e8091a693aab\", \"url\": \"https://api.pillowcase.su/api/download/8a884df44376bf588688e8091a693aab\", \"size\": \"10.7 MB\", \"duration\": 255.84}", "aliases": [], "size": "10.7 MB"}, {"id": "city-of-lost-angels", "name": "City Of Lost Angels [V7]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: kanye west cole digital nas 3 (Master)\nHas production by Digital Nas. Stems say \"cole\" instead of \"COLA\". Leaked after a groupbuy.", "length": "193.39", "fileDate": 16664832, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/64cb3cda9788f4a5075043876c4afc66", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/64cb3cda9788f4a5075043876c4afc66\", \"key\": \"City Of Lost Angels\", \"title\": \"City Of Lost Angels [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Digital Nas)\", \"aliases\": [\"City Of Los Angels\", \"Judgemental\"], \"description\": \"OG Filename: kanye west cole digital nas 3 (Master)\\nHas production by Digital Nas. Stems say \\\"cole\\\" instead of \\\"COLA\\\". Leaked after a groupbuy.\", \"date\": 16664832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b9193fd5abb41287bacafd0113520bc5\", \"url\": \"https://api.pillowcase.su/api/download/b9193fd5abb41287bacafd0113520bc5\", \"size\": \"9.68 MB\", \"duration\": 193.39}", "aliases": ["City Of Los Angels", "Judgemental"], "size": "9.68 MB"}, {"id": "cracked-dancefloor", "name": "Cracked Dancefloor", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Cracked Dancefloor\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "35.83", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8084490b56b8979665217ff271145f37", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8084490b56b8979665217ff271145f37\", \"key\": \"Cracked Dancefloor\", \"title\": \"Cracked Dancefloor\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Cracked Dancefloor\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b98d392a978da2245ea23888d1679d34\", \"url\": \"https://api.pillowcase.su/api/download/b98d392a978da2245ea23888d1679d34\", \"size\": \"7.16 MB\", \"duration\": 35.83}", "aliases": [], "size": "7.16 MB"}, {"id": "curify-scrupul", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Curify Scrupul\nRough Gallery freestyle over a Patternarium Generation sample looped by Boogz. Shares lines with the May 2021 \"Petals\" freestyle.", "length": "70.3", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/0b38a6e3924a3e0763bd670bb840cb14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b38a6e3924a3e0763bd670bb840cb14\", \"key\": \"Curify Scrupul\", \"title\": \"<PERSON>urify Scrupul\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Curify Scrupul\\nRough Gallery freestyle over a Patternarium Generation sample looped by Boogz. Shares lines with the May 2021 \\\"Petals\\\" freestyle.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"aa0be50aed39d3613758889901fb1d7b\", \"url\": \"https://api.pillowcase.su/api/download/aa0be50aed39d3613758889901fb1d7b\", \"size\": \"7.71 MB\", \"duration\": 70.3}", "aliases": [], "size": "7.71 MB"}, {"id": "cuban-links", "name": "Cuban Links", "artists": [], "producers": [], "notes": "OG Filename: Cuban Links_21.04.26 Gallery Freestyles\nRough freestyle with voice memo vocals, made during the Gallery sessions.", "length": "97.76", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a908df358ccd81898b5628e768ebdeb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a908df358ccd81898b5628e768ebdeb0\", \"key\": \"Cuban Links\", \"title\": \"Cuban Links\", \"description\": \"OG Filename: Cuban Links_21.04.26 Gallery Freestyles\\nRough freestyle with voice memo vocals, made during the Gallery sessions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"45aa46e02ee6625df6d58a17513da579\", \"url\": \"https://api.pillowcase.su/api/download/45aa46e02ee6625df6d58a17513da579\", \"size\": \"8.15 MB\", \"duration\": 97.76}", "aliases": [], "size": "8.15 MB"}, {"id": "dangerous-liaison", "name": "🗑️ Dangerous Liaison", "artists": [], "producers": ["Ojivolta", "88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 03 Dangerous Liaison\nRough freestyle, in which <PERSON><PERSON><PERSON> raps about not being impressed by someone partaking in incest, also talking about friends and shoes.", "length": "256.68", "fileDate": 16699392, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/53dc8dac48446317ba85b3a83781e2af", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53dc8dac48446317ba85b3a83781e2af\", \"key\": \"Dangerous Liaison\", \"title\": \"\\ud83d\\uddd1\\ufe0f Dangerous Liaison\", \"artists\": \"(prod. Ojivolta & 88-Keys)\", \"description\": \"OG Filename: 210510 Gallery Freestyles - 03 Dangerous Liaison\\nRough freestyle, in which <PERSON><PERSON><PERSON> raps about not being impressed by someone partaking in incest, also talking about friends and shoes.\", \"date\": 16699392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3da42378f3f44b8fa2175161b2b5e4f2\", \"url\": \"https://api.pillowcase.su/api/download/3da42378f3f44b8fa2175161b2b5e4f2\", \"size\": \"10.7 MB\", \"duration\": 256.68}", "aliases": [], "size": "10.7 MB"}, {"id": "die", "name": "🗑️ Die [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Die\nRough mumble freestyle.", "length": "110.84", "fileDate": 16358976, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5ac24d3f08d20ea9f11ae15b397f44e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ac24d3f08d20ea9f11ae15b397f44e1\", \"key\": \"Die\", \"title\": \"\\ud83d\\uddd1\\ufe0f Die [V1]\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"OG Filename: 210526 Gallery - Die\\nRough mumble freestyle.\", \"date\": 16358976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a2c7d1bcd939948618cd6f91f2f9f2aa\", \"url\": \"https://api.pillowcase.su/api/download/a2c7d1bcd939948618cd6f91f2f9f2aa\", \"size\": \"8.36 MB\", \"duration\": 110.84}", "aliases": [], "size": "8.36 MB"}, {"id": "different-codes", "name": "Different Codes [V1]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 02 Different Codes\nThe third and longest of the three freestyles later used to create \"Thunder\". Recorded to an improvised instrumental. Vocals were used to make most of \"Thunder\" and the entirety of \"Top\". Recorded on May 10th, along with both other \"Thunder\" freestyles. Has a similar \"mmm mmm\" flow to \"Hot Shit\" and \"<PERSON>ya\", and shares the \"Scott Ridley\" line from \"Life Of The Party\".", "length": "572.64", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f7cd4c06f686c8e9ea55c80ae38ad0c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f7cd4c06f686c8e9ea55c80ae38ad0c0\", \"key\": \"Different Codes\", \"title\": \"Different Codes [V1]\", \"artists\": \"(prod. 88-Keys)\", \"aliases\": [\"<PERSON>\", \"Don't Miss Me\"], \"description\": \"OG Filename: 210510 Gallery Freestyles - 02 Different Codes\\nThe third and longest of the three freestyles later used to create \\\"Thunder\\\". Recorded to an improvised instrumental. Vocals were used to make most of \\\"Thunder\\\" and the entirety of \\\"Top\\\". Recorded on May 10th, along with both other \\\"Thunder\\\" freestyles. Has a similar \\\"mmm mmm\\\" flow to \\\"Hot Shit\\\" and \\\"Junya\\\", and shares the \\\"Scott Ridley\\\" line from \\\"Life Of The Party\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"89d7510511cabb546401816bb4de429c\", \"url\": \"https://api.pillowcase.su/api/download/89d7510511cabb546401816bb4de429c\", \"size\": \"15.8 MB\", \"duration\": 572.64}", "aliases": ["Thunder", "Don't Miss Me"], "size": "15.8 MB"}, {"id": "don-t-miss-me", "name": "Don't <PERSON> Me [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: Don't Miss Me - 21.05.11 Edit\nA trimmed down version of the \"Different Codes\" freestyle. Titled \"Don't Miss Me\".", "length": "210.72", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c8312271bc9302b4bb07f22791656c01", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8312271bc9302b4bb07f22791656c01\", \"key\": \"Don't Miss Me\", \"title\": \"Don't Miss Me [V2]\", \"artists\": \"(prod. 88-<PERSON>)\", \"aliases\": [\"<PERSON>\", \"Different Codes\"], \"description\": \"OG Filename: Don't Miss Me - 21.05.11 Edit\\nA trimmed down version of the \\\"Different Codes\\\" freestyle. Titled \\\"Don't Miss Me\\\".\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eb380bb438dc78d7af36e611e884a63f\", \"url\": \"https://api.pillowcase.su/api/download/eb380bb438dc78d7af36e611e884a63f\", \"size\": \"9.96 MB\", \"duration\": 210.72}", "aliases": ["Thunder", "Different Codes"], "size": "9.96 MB"}, {"id": "don-t-go-away", "name": "⭐ Don't Go Away [V2]", "artists": [], "producers": ["Dem <PERSON>z"], "notes": "Beat built around repeated vocal sample resembling Street Fighter that is chopped up and repeated, with added drums. <PERSON><PERSON><PERSON> has an autotuned singing hook. Contains punch-ins. Said to be found in a Gallery sessions folder. Reuses lines from \"Fun Mistakes\".", "length": "84", "fileDate": 16955136, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/625912e54f270d416c61e2eeb9e2dcc5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/625912e54f270d416c61e2eeb9e2dcc5\", \"key\": \"Don't Go Away\", \"title\": \"\\u2b50 Don't Go Away [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Dem <PERSON>z)\", \"description\": \"Beat built around repeated vocal sample resembling Street Fighter that is chopped up and repeated, with added drums. <PERSON><PERSON><PERSON> has an autotuned singing hook. Contains punch-ins. Said to be found in a Gallery sessions folder. Reuses lines from \\\"Fun Mistakes\\\".\", \"date\": 16955136, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"24deb044ba1a0176c1abccca102289c2\", \"url\": \"https://api.pillowcase.su/api/download/24deb044ba1a0176c1abccca102289c2\", \"size\": \"7.93 MB\", \"duration\": 84}", "aliases": [], "size": "7.93 MB"}, {"id": "duran-duran", "name": "<PERSON><PERSON>", "artists": [], "producers": [], "notes": "OG Filename: duran duran_21.04.26 Gallery Freestyles\nRough freestyle with voice memo vocals.", "length": "94.39", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/75296b7877fb5f220233bdf804bf51f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/75296b7877fb5f220233bdf804bf51f8\", \"key\": \"Duran Duran\", \"title\": \"Duran Duran\", \"description\": \"OG Filename: duran duran_21.04.26 Gallery Freestyles\\nRough freestyle with voice memo vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"83025a83ed10fffeee80336afb51d488\", \"url\": \"https://api.pillowcase.su/api/download/83025a83ed10fffeee80336afb51d488\", \"size\": \"8.1 MB\", \"duration\": 94.39}", "aliases": [], "size": "8.1 MB"}, {"id": "eternal", "name": "Eternal [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Eternal\nMay 2021 freestyle. <PERSON><PERSON><PERSON> can be heard in the background telling <PERSON><PERSON><PERSON> what to say.", "length": "158.46", "fileDate": 16473024, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2e944a836d7debaf3e92c0fe5fbadee5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2e944a836d7debaf3e92c0fe5fbadee5\", \"key\": \"Eternal\", \"title\": \"Eternal [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"The Energy\"], \"description\": \"OG Filename: 210526 Gallery - Eternal\\nMay 2021 freestyle. <PERSON><PERSON><PERSON> can be heard in the background telling <PERSON><PERSON><PERSON> what to say.\", \"date\": 16473024, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f0ca9e76d768e405bd6232bbea4d7833\", \"url\": \"https://api.pillowcase.su/api/download/f0ca9e76d768e405bd6232bbea4d7833\", \"size\": \"9.13 MB\", \"duration\": 158.46}", "aliases": ["The Energy"], "size": "9.13 MB"}, {"id": "merrygoround", "name": "🗑️ MerryGoRound [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - MerryGoRound\nMumble freestyle.", "length": "102.05", "fileDate": 16350336, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/03e6a91b390e9474a40741b379e5c148", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03e6a91b390e9474a40741b379e5c148\", \"key\": \"MerryGoRound\", \"title\": \"\\ud83d\\uddd1\\ufe0f MerryGoRound [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Aye Ye Ye Ye\", \"<PERSON><PERSON><PERSON><PERSON> <PERSON> Ye Ye\"], \"description\": \"OG Filename: 210526 Gallery - MerryGoRound\\nMumble freestyle.\", \"date\": 16350336, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5f9efed11f9f7d3de352d861bf58e098\", \"url\": \"https://api.pillowcase.su/api/download/5f9efed11f9f7d3de352d861bf58e098\", \"size\": \"8.22 MB\", \"duration\": 102.05}", "aliases": ["Aye Ye Ye Ye", "<PERSON><PERSON><PERSON>y Ye Ye Ye"], "size": "8.22 MB"}, {"id": "", "name": "??? [V1]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210521 Gallery - J<PERSON> and OxV_MK edit\nEarliest known version. Has <PERSON> singing rough reference vocals. Unknown who \"M<PERSON>\" in the filename is refering to.", "length": "304.18", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6095cca842b0cbf14b4cbc0109ce0bd3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6095cca842b0cbf14b4cbc0109ce0bd3\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"aliases\": [\"Find Out\", \"High Up\"], \"description\": \"OG Filename: 210521 Gallery - JW and OxV_MK edit\\nEarliest known version. Has <PERSON> singing rough reference vocals. Unknown who \\\"MK\\\" in the filename is refering to.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f61000e851fd83bf1ef302f67a84fe6f\", \"url\": \"https://api.pillowcase.su/api/download/f61000e851fd83bf1ef302f67a84fe6f\", \"size\": \"11.5 MB\", \"duration\": 304.18}", "aliases": ["Find Out", "High Up"], "size": "11.5 MB"}, {"id": "high-up", "name": "High Up [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210525 Gallery - <PERSON><PERSON><PERSON> (High Up 75bpm)\nOriginal Ye freestyle, without punch-ins. Features a further along beat than the <PERSON> reference.", "length": "533.81", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/0dab290245c72787209c96b6be52bfb1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0dab290245c72787209c96b6be52bfb1\", \"key\": \"High Up\", \"title\": \"High Up [V2]\", \"artists\": \"(prod. Ojivolta)\", \"aliases\": [\"Find Out\"], \"description\": \"OG Filename: 210525 Gallery - <PERSON><PERSON><PERSON> (High Up 75bpm)\\nOriginal Ye freestyle, without punch-ins. Features a further along beat than the <PERSON> reference.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6a87c5684826e5d4863272f18c205bd7\", \"url\": \"https://api.pillowcase.su/api/download/6a87c5684826e5d4863272f18c205bd7\", \"size\": \"15.1 MB\", \"duration\": 533.81}", "aliases": ["Find Out"], "size": "15.1 MB"}, {"id": "hawk", "name": "Digital Nas - Hawk [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: Digital Nas - Hawk\nOriginal Digital Nas version, with reference vocals (mistaken for Chief <PERSON><PERSON>). Unknown when it was made, as the beat allegedly spans back to 2017.", "length": "138.06", "fileDate": 16995744, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/addfeb6593a645b0057d1d0bf374e39d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/addfeb6593a645b0057d1d0bf374e39d\", \"key\": \"<PERSON>\", \"title\": \"Digital Nas - Hawk [V1]\", \"artists\": \"(prod. Digital Nas)\", \"aliases\": [\"All These Friends of Mine\", \"Friends\"], \"description\": \"OG Filename: Digital Nas - Hawk\\nOriginal Digital Nas version, with reference vocals (mistaken for Chief <PERSON><PERSON>). Unknown when it was made, as the beat allegedly spans back to 2017.\", \"date\": 16995744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"52724bce166e478fcfb30b4f8f07100a\", \"url\": \"https://api.pillowcase.su/api/download/52724bce166e478fcfb30b4f8f07100a\", \"size\": \"8.8 MB\", \"duration\": 138.06}", "aliases": ["All These Friends of Mine", "Friends"], "size": "8.8 MB"}, {"id": "hawk-26", "name": "Hawk [V2]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Hawk\nOriginal Kanye freestyle.", "length": "411.53", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/07474fa5c6a8490178eccbe85fedf542", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/07474fa5c6a8490178eccbe85fedf542\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V2]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"<PERSON>\", \"All These Friends Of Mine\"], \"description\": \"OG Filename: 210526 Gallery - Hawk\\nOriginal Kanye freestyle.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9c32efb3ab888867530097dd63784082\", \"url\": \"https://api.pillowcase.su/api/download/9c32efb3ab888867530097dd63784082\", \"size\": \"13.2 MB\", \"duration\": 411.53}", "aliases": ["Friends", "All These Friends Of Mine"], "size": "13.2 MB"}, {"id": "all-these-friends-of-mine", "name": "All These Friends of Mine [V3]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210526 Gallery - All These Friends of Mine_OxV\nSecond freestyle, with Ojivolta recording synth pads for the song while <PERSON><PERSON><PERSON> re-records some of the lyrics.", "length": "214.83", "fileDate": 16943040, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/461e8e9d3dc844cd68c9d1adc5eb22e8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/461e8e9d3dc844cd68c9d1adc5eb22e8\", \"key\": \"All These Friends of Mine\", \"title\": \"All These Friends of Mine [V3]\", \"artists\": \"(prod. Ojivolta)\", \"aliases\": [\"<PERSON>\", \"<PERSON>\"], \"description\": \"OG Filename: 210526 Gallery - All These Friends of Mine_OxV\\nSecond freestyle, with Ojivolta recording synth pads for the song while <PERSON><PERSON><PERSON> re-records some of the lyrics.\", \"date\": 16943040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ad12406f0306afddd52e9ac5c6df056a\", \"url\": \"https://api.pillowcase.su/api/download/ad12406f0306afddd52e9ac5c6df056a\", \"size\": \"10 MB\", \"duration\": 214.83}", "aliases": ["Friends", "Hawk"], "size": "10 MB"}, {"id": "friends-list", "name": "Friends List", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 01 Friends List\n2nd freestyle from May 10th that would have some vocals reused for \"Thunder.\" Features an improvised beat, as well as a vocal sample that would later be used on \"HIIII WYD\".", "length": "89.52", "fileDate": 16924896, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/baf5db5c7910625d79023cb4c23984d9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/baf5db5c7910625d79023cb4c23984d9\", \"key\": \"Friends List\", \"title\": \"Friends List\", \"artists\": \"(prod. 88-Keys)\", \"aliases\": [\"<PERSON>\"], \"description\": \"OG Filename: 210510 Gallery Freestyles - 01 Friends List\\n2nd freestyle from May 10th that would have some vocals reused for \\\"Thunder.\\\" Features an improvised beat, as well as a vocal sample that would later be used on \\\"HIIII WYD\\\".\", \"date\": 16924896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eff12ad99ec01d32cf1c126624e6e46a\", \"url\": \"https://api.pillowcase.su/api/download/eff12ad99ec01d32cf1c126624e6e46a\", \"size\": \"8.02 MB\", \"duration\": 89.52}", "aliases": ["Thunder"], "size": "8.02 MB"}, {"id": "fairytale", "name": "Fairytale [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210429 Gallery Refs w JW - 10 Ye Fairytale_DT Ref\nInitial Don Toliver reference track. Low quality, untuned version from <PERSON> originally leaked October 10th, 2023.", "length": "160.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/06b8ae38d883d60d90de6db15078ce2f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/06b8ae38d883d60d90de6db15078ce2f\", \"key\": \"Fairytale\", \"title\": \"Fairytale [V1]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: 210429 Gallery Refs w JW - 10 Ye Fairytale_DT Ref\\nInitial Don Toliver reference track. Low quality, untuned version from <PERSON> originally leaked October 10th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0567e4cae0fbc080db65729a4a0ae402\", \"url\": \"https://api.pillowcase.su/api/download/0567e4cae0fbc080db65729a4a0ae402\", \"size\": \"9.15 MB\", \"duration\": 160.06}", "aliases": [], "size": "9.15 MB"}, {"id": "fairytale-30", "name": "Fairytale [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: kw gallery with jw 1\nRougher, untuned bounce of the initial freestyle.", "length": "171.43", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/7a4a40eb29b6ae5065c2297c305d6adf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a4a40eb29b6ae5065c2297c305d6adf\", \"key\": \"Fairytale\", \"title\": \"Fairytale [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: kw gallery with jw 1\\nRougher, untuned bounce of the initial freestyle.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0347d841f4f30c41dd5d4d1394547c81\", \"url\": \"https://api.pillowcase.su/api/download/0347d841f4f30c41dd5d4d1394547c81\", \"size\": \"9.33 MB\", \"duration\": 171.43}", "aliases": [], "size": "9.33 MB"}, {"id": "fairytale-31", "name": "Fairytale [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: kw gallery with jw 2\nRough, untuned bounce of the second freestyle for \"Fairytale\" done by <PERSON>.", "length": "52.51", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a2bafeea16fe63ef4d86411baa76a7bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2bafeea16fe63ef4d86411baa76a7bc\", \"key\": \"Fairytale\", \"title\": \"Fairytale [V3]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: kw gallery with jw 2\\nRough, untuned bounce of the second freestyle for \\\"Fairytale\\\" done by <PERSON>.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c4793a49f052d67f38d31e28298f8d4d\", \"url\": \"https://api.pillowcase.su/api/download/c4793a49f052d67f38d31e28298f8d4d\", \"size\": \"7.43 MB\", \"duration\": 52.51}", "aliases": [], "size": "7.43 MB"}, {"id": "gi-joe", "name": "GI Joe", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - GI <PERSON> gallery freestyle from May 2021.", "length": "93.18", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/aabc4e3c1a292d7db306f94386da3440", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aabc4e3c1a292d7db306f94386da3440\", \"key\": \"GI Joe\", \"title\": \"GI Joe\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - <PERSON><PERSON>\\nRough gallery freestyle from May 2021.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea8940e4fdb550df97387f41dfd8aff1\", \"url\": \"https://api.pillowcase.su/api/download/ea8940e4fdb550df97387f41dfd8aff1\", \"size\": \"8.08 MB\", \"duration\": 93.18}", "aliases": [], "size": "8.08 MB"}, {"id": "i-know-god-breathed-on-this", "name": "I Know God Breathed On This [V26]", "artists": ["Victory", "<PERSON>"], "producers": ["Ojivolta"], "notes": "OG Filename: I Know God Breathed On This - Chords - 21.05.24 Ye Edits add Victory\nHas Ojivolta production, similar production to the release version of the song, and also features <PERSON>.", "length": "240.05", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9c4267332299d455ae018e96272ce7f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c4267332299d455ae018e96272ce7f2\", \"key\": \"I Know God Breathed On This\", \"title\": \"I Know God Breathed On This [V26]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. Ojivolta)\", \"aliases\": [\"Breathed On This\", \"God Breathed\", \"God Breathed On This\", \"I Know God\", \"I Know He Got His Hands On This\"], \"description\": \"OG Filename: I Know God Breathed On This - Chords - 21.05.24 Ye Edits add Victory\\nHas Ojivolta production, similar production to the release version of the song, and also features <PERSON>.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"270af4344e85b0cff9634c33e7901af2\", \"url\": \"https://api.pillowcase.su/api/download/270af4344e85b0cff9634c33e7901af2\", \"size\": \"10.4 MB\", \"duration\": 240.05}", "aliases": ["Breathed On This", "God Breathed", "God Breathed On This", "I Know God", "I Know He Got His Hands On This"], "size": "10.4 MB"}, {"id": "god-got-me", "name": "⭐ God Got Me [V1]", "artists": [], "producers": ["88-<PERSON>", "Ojivolta"], "notes": "OG Filename: <PERSON> Got Me - 21.05.13 Tracking Ref\nOriginal freestyle. Some lyrics were reused for the released version of \"Lord I Need You\" and an unreleased version of \"Donda\". Has 14 minutes of vocals (5-7 non-mumble) and 6 minutes of an open verse where he screams, providing choir reference vocals. Has lyrics about a girl from Palestine, abortion, the industry, and his own black identity. Samples \"Enlightenment\" by <PERSON>. Low quality rebounce originally leaked September 13th, 2023 after a kind of successful groupbuy - with the real lossless file leaking later.", "length": "1222.1", "fileDate": 17112384, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c4f2f240b130615e70ab507073e730d5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4f2f240b130615e70ab507073e730d5\", \"key\": \"God Got Me\", \"title\": \"\\u2b50 God Got Me [V1]\", \"artists\": \"(prod. 88-Keys & Ojivolta)\", \"aliases\": [\"God's Got Me\"], \"description\": \"OG Filename: God Got Me - 21.05.13 Tracking Ref\\nOriginal freestyle. Some lyrics were reused for the released version of \\\"Lord I Need You\\\" and an unreleased version of \\\"Donda\\\". Has 14 minutes of vocals (5-7 non-mumble) and 6 minutes of an open verse where he screams, providing choir reference vocals. Has lyrics about a girl from Palestine, abortion, the industry, and his own black identity. Samples \\\"Enlightenment\\\" by <PERSON>. Low quality rebounce originally leaked September 13th, 2023 after a kind of successful groupbuy - with the real lossless file leaking later.\", \"date\": 17112384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c2f7338a9472d9406b7b5a734f595b90\", \"url\": \"https://api.pillowcase.su/api/download/c2f7338a9472d9406b7b5a734f595b90\", \"size\": \"26.1 MB\", \"duration\": 1222.1}", "aliases": ["God's Got Me"], "size": "26.1 MB"}, {"id": "god-got-me-35", "name": "<PERSON> Got Me [V2]", "artists": [], "producers": ["88-<PERSON>", "Ojivolta"], "notes": "OG Filename: <PERSON> Got Me - 21.05.13 Shorter Re<PERSON>er version, cutting out the open verse, made on the same day as the actual song. Used on July Donda tracklists. Low quality rebounce originally leaked September 13th, 2023 after a kind of successful groupbuy - with the real lossless file leaking later.", "length": "878.56", "fileDate": 17112384, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/4dacdda976cfc0293a9e15d1f2e16d51", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4dacdda976cfc0293a9e15d1f2e16d51\", \"key\": \"God Got Me\", \"title\": \"God Got Me [V2]\", \"artists\": \"(prod. 88-Keys & Ojivolta)\", \"aliases\": [\"God's Got Me\"], \"description\": \"OG Filename: God Got Me - 21.05.13 Shorter Ref\\nShorter version, cutting out the open verse, made on the same day as the actual song. Used on July Donda tracklists. Low quality rebounce originally leaked September 13th, 2023 after a kind of successful groupbuy - with the real lossless file leaking later.\", \"date\": 17112384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"96bf3463d0dfb0fcda60835d6948dbd0\", \"url\": \"https://api.pillowcase.su/api/download/96bf3463d0dfb0fcda60835d6948dbd0\", \"size\": \"20.6 MB\", \"duration\": 878.56}", "aliases": ["God's Got Me"], "size": "20.6 MB"}, {"id": "really-fun-mistakes", "name": "Really Fun Mistakes [V1]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 07 Really Fun Mistakes\nInitial freestyle. Features lyrics that would later be reused in \"Eazy\". Leaked as a bonus for the \"Never Forgive Yourself\" & \"Forever\" GB.", "length": "524.64", "fileDate": 16749504, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6519ace82fec80fcb8617baa335247e6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6519ace82fec80fcb8617baa335247e6\", \"key\": \"Really Fun Mistakes\", \"title\": \"Really Fun Mistakes [V1]\", \"artists\": \"(prod. 88-Keys) \", \"aliases\": [\"Fun Mistakes\"], \"description\": \"OG Filename: 210510 Gallery Freestyles - 07 Really Fun Mistakes\\nInitial freestyle. Features lyrics that would later be reused in \\\"Eazy\\\". Leaked as a bonus for the \\\"Never Forgive Yourself\\\" & \\\"Forever\\\" GB.\", \"date\": 16749504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e1d2bf50d083b6a557c3f63bde74b183\", \"url\": \"https://api.pillowcase.su/api/download/e1d2bf50d083b6a557c3f63bde74b183\", \"size\": \"15 MB\", \"duration\": 524.64}", "aliases": ["Fun Mistakes"], "size": "15 MB"}, {"id": "fun-mistakes", "name": "Fun Mistakes [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: Fun Mistakes - 21.05.11 Open Verses\nCut-down version of the initial freestyle. Mostly open verse, with short, minimal mumble vocals acting as a hook.", "length": "186.99", "fileDate": 16537824, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3f11689a45b8fa354ddbb3c68f0b877f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f11689a45b8fa354ddbb3c68f0b877f\", \"key\": \"Fun Mistakes\", \"title\": \"Fun Mistakes [V2]\", \"artists\": \"(prod. 88-<PERSON>)\", \"aliases\": [\"Really Fun Mistakes\"], \"description\": \"OG Filename: Fun Mistakes - 21.05.11 Open Verses\\nCut-down version of the initial freestyle. Mostly open verse, with short, minimal mumble vocals acting as a hook.\", \"date\": 16537824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d0573f0970da9a1651ad7d0ae6814ca5\", \"url\": \"https://api.pillowcase.su/api/download/d0573f0970da9a1651ad7d0ae6814ca5\", \"size\": \"9.58 MB\", \"duration\": 186.99}", "aliases": ["Really Fun Mistakes"], "size": "9.58 MB"}, {"id": "grammy", "name": "Grammy", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - <PERSON>\nSamples the main theme from the film \"Fear Is The Key\" composed by <PERSON>.", "length": "331.4", "fileDate": 16699392, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b94a53a157f70602346b0eee48b9dea0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b94a53a157f70602346b0eee48b9dea0\", \"key\": \"<PERSON>\", \"title\": \"<PERSON>\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Grammy\\nSamples the main theme from the film \\\"Fear Is The Key\\\" composed by <PERSON>\", \"date\": 16699392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"586ade23077965c5ea2ff98c594404bd\", \"url\": \"https://api.pillowcase.su/api/download/586ade23077965c5ea2ff98c594404bd\", \"size\": \"11.9 MB\", \"duration\": 331.4}", "aliases": [], "size": "11.9 MB"}, {"id": "greatest", "name": "Greatest", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Greatest\n23 second freestyle. <PERSON> has 10 seconds of vocals and is entirely mumble.", "length": "22.96", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/ba7e58769ff8d76aa5e52361132411ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ba7e58769ff8d76aa5e52361132411ac\", \"key\": \"Greatest\", \"title\": \"Greatest\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Greatest\\n23 second freestyle. <PERSON> has 10 seconds of vocals and is entirely mumble.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"523cb320f9b6073fcc0c8235e8bda63d\", \"url\": \"https://api.pillowcase.su/api/download/523cb320f9b6073fcc0c8235e8bda63d\", \"size\": \"6.96 MB\", \"duration\": 22.96}", "aliases": [], "size": "6.96 MB"}, {"id": "grinder", "name": "Grinder", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Grinder\nRough gallery freestyle from May 2021.", "length": "111.41", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/91cee9792550a70263bcc0df647e30b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91cee9792550a70263bcc0df647e30b6\", \"key\": \"Grinder\", \"title\": \"Grinder\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"OG Filename: 210526 Gallery - Grinder\\nRough gallery freestyle from May 2021.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"96f05f832c7dd63c38727f53edbe2e7d\", \"url\": \"https://api.pillowcase.su/api/download/96f05f832c7dd63c38727f53edbe2e7d\", \"size\": \"8.37 MB\", \"duration\": 111.41}", "aliases": [], "size": "8.37 MB"}, {"id": "hard-rock", "name": "Hard Rock", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Hard Rock\nRough gallery freestyle from May 2021.", "length": "95.79", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9b540a469364b81e3d7e2275f5beffdb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9b540a469364b81e3d7e2275f5beffdb\", \"key\": \"Hard Rock\", \"title\": \"Hard Rock\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Hard Rock\\nRough gallery freestyle from May 2021.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6a605e2c35b7497cf08bae6a61b1701c\", \"url\": \"https://api.pillowcase.su/api/download/6a605e2c35b7497cf08bae6a61b1701c\", \"size\": \"8.12 MB\", \"duration\": 95.79}", "aliases": [], "size": "8.12 MB"}, {"id": "heaven-and-hell", "name": "Heaven and Hell [V2]", "artists": [], "producers": ["88-<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: Heaven And Hell - 21.05.25 Extended Bass\nEarliest available version. Includes alternate production and very rough vocals.", "length": "133.08", "fileDate": 16343424, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8ca5a06c0861d165230afba9590a5cf5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ca5a06c0861d165230afba9590a5cf5\", \"key\": \"Heaven and Hell\", \"title\": \"Heaven and Hell [V2]\", \"artists\": \"(prod. 88-<PERSON> & BoogzDaBeast)\", \"description\": \"OG Filename: Heaven And Hell - 21.05.25 Extended Bass\\nEarliest available version. Includes alternate production and very rough vocals.\", \"date\": 16343424, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6db6141e7ae3e3f37f63935c1298d7eb\", \"url\": \"https://api.pillowcase.su/api/download/6db6141e7ae3e3f37f63935c1298d7eb\", \"size\": \"8.72 MB\", \"duration\": 133.08}", "aliases": [], "size": "8.72 MB"}, {"id": "high-step", "name": "High Step", "artists": [], "producers": [], "notes": "OG Filename: high step_21.04.26\nRough freestyle with voice memo vocals, made during the Gallery sessions.", "length": "127.5", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/18757d982126c66ed8131cbb11eb5425", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/18757d982126c66ed8131cbb11eb5425\", \"key\": \"High Step\", \"title\": \"High Step\", \"description\": \"OG Filename: high step_21.04.26\\nRough freestyle with voice memo vocals, made during the Gallery sessions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0bd8408ec421844f08338f1cf5734c98\", \"url\": \"https://api.pillowcase.su/api/download/0bd8408ec421844f08338f1cf5734c98\", \"size\": \"8.63 MB\", \"duration\": 127.5}", "aliases": [], "size": "8.63 MB"}, {"id": "holy-ghost", "name": "Holy Ghost", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210521 Gallery - OV DT 7 Holy Ghost\nRough Don Toliver reference track over an Ojivolta piano instrumental.", "length": "317.76", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/7386d4cbf71c0af5f3049e4380dc99ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7386d4cbf71c0af5f3049e4380dc99ac\", \"key\": \"Holy Ghost\", \"title\": \"Holy Ghost\", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: 210521 Gallery - OV DT 7 Holy Ghost\\nRough Don Toliver reference track over an Ojivolta piano instrumental.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f2bab993afffc3a9ea7f04ff09e02c77\", \"url\": \"https://api.pillowcase.su/api/download/f2bab993afffc3a9ea7f04ff09e02c77\", \"size\": \"11.7 MB\", \"duration\": 317.76}", "aliases": [], "size": "11.7 MB"}, {"id": "human-kind", "name": "Human Kind", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Human Kind\nFreestyle from May 2021.", "length": "95.5", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/841bdb623074a58e8ffcbb55d4ae93ad", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/841bdb623074a58e8ffcbb55d4ae93ad\", \"key\": \"Human Kind\", \"title\": \"Human Kind\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Human Kind\\nFreestyle from May 2021.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a358b46ca40da7ecff2151ef85a9a16b\", \"url\": \"https://api.pillowcase.su/api/download/a358b46ca40da7ecff2151ef85a9a16b\", \"size\": \"8.12 MB\", \"duration\": 95.5}", "aliases": [], "size": "8.12 MB"}, {"id": "-46", "name": "??? [V1]", "artists": [], "producers": ["Plain Pat"], "notes": "OG Filename: 210525 Gallery - <PERSON> x Ye 1\nOriginal freestyle of \"Hype\" without punch-ins. Instrumental is the sample used in \"Hype\".", "length": "205.75", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c4a9e441412f34ddc8ac5cd514515e13", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4a9e441412f34ddc8ac5cd514515e13\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Hype\"], \"description\": \"OG Filename: 210525 Gallery - Pat x Ye 1\\nOriginal freestyle of \\\"Hype\\\" without punch-ins. Instrumental is the sample used in \\\"Hype\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d9ad034516bccc051df000624097eb60\", \"url\": \"https://api.pillowcase.su/api/download/d9ad034516bccc051df000624097eb60\", \"size\": \"9.88 MB\", \"duration\": 205.75}", "aliases": ["Hype"], "size": "9.88 MB"}, {"id": "on-his-head", "name": "On His Head [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: 210526 Gallery - On His Head pt. 1\nEarly version that features a mumble <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> freestyle. <PERSON>ny<PERSON> ask's for Ojivolta to do live pad production around 1:30 into the file.", "length": "275.67", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/196c38a446e2a6cca4af3fd129e3be4e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/196c38a446e2a6cca4af3fd129e3be4e\", \"key\": \"On His Head\", \"title\": \"On His Head [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Ojivolta)\", \"aliases\": [\"I Let Go\"], \"description\": \"OG Filename: 210526 Gallery - On His Head pt. 1\\nEarly version that features a mumble <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> freestyle. Kany<PERSON> ask's for Ojivolta to do live pad production around 1:30 into the file.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7c3f2a3bb32327b9a291ff4f72f13e9e\", \"url\": \"https://api.pillowcase.su/api/download/7c3f2a3bb32327b9a291ff4f72f13e9e\", \"size\": \"11 MB\", \"duration\": 275.67}", "aliases": ["I Let Go"], "size": "11 MB"}, {"id": "on-his-head-48", "name": "On His Head [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Ojiovlta"], "notes": "OG Filename: 210526 Gallery - On His Head pt. 2\nAnother early version that features a mumble <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> freestyle. Has different live Ojivolta production compared to the initial freestyle.", "length": "314.17", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8737d74463d6e520c56521b62a1521af", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8737d74463d6e520c56521b62a1521af\", \"key\": \"On His Head\", \"title\": \"On His Head [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Ojiovlta)\", \"aliases\": [\"I Let Go\"], \"description\": \"OG Filename: 210526 Gallery - On His Head pt. 2\\nAnother early version that features a mumble <PERSON>nye and <PERSON><PERSON><PERSON> freestyle. Has different live Ojivolta production compared to the initial freestyle.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f026284cefb465edf69ea56287e70b32\", \"url\": \"https://api.pillowcase.su/api/download/f026284cefb465edf69ea56287e70b32\", \"size\": \"11.6 MB\", \"duration\": 314.17}", "aliases": ["I Let Go"], "size": "11.6 MB"}, {"id": "insane", "name": "Insane", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Insane\nFreestyle from May 2021.", "length": "94.87", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3fbf209851e8e56ddbff51aceae62bbb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3fbf209851e8e56ddbff51aceae62bbb\", \"key\": \"Insane\", \"title\": \"Insane\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"OG Filename: 210526 Gallery - Insane\\nFreestyle from May 2021.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"68f736248fc1102faf757a2c34f0aa07\", \"url\": \"https://api.pillowcase.su/api/download/68f736248fc1102faf757a2c34f0aa07\", \"size\": \"8.11 MB\", \"duration\": 94.87}", "aliases": [], "size": "8.11 MB"}, {"id": "intimacy", "name": "Intimacy", "artists": [], "producers": [], "notes": "OG Filename: intimacy (loop)_21.04.26 Gallery Freestyles\nRough freestyle with voice memo vocals.", "length": "63.43", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d950cce7acfd90e1fe71a913b77115e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d950cce7acfd90e1fe71a913b77115e3\", \"key\": \"Intimacy\", \"title\": \"Intimacy\", \"description\": \"OG Filename: intimacy (loop)_21.04.26 Gallery Freestyles\\nRough freestyle with voice memo vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"09470eb37632ba5235baf9914f997924\", \"url\": \"https://api.pillowcase.su/api/download/09470eb37632ba5235baf9914f997924\", \"size\": \"7.61 MB\", \"duration\": 63.43}", "aliases": [], "size": "7.61 MB"}, {"id": "goin-to-jail-tonight", "name": "Goin To Jail Tonight [V1]", "artists": [], "producers": ["88-<PERSON>", "Ojivolta"], "notes": "OG Filenames: 210513 Gallery - Goin To Jail Tonight & \n210513 NOID Piano w OxV Bass\nOriginal freestyle, featuring mumble with mostly the same lines as released besides some alternate lines.", "length": "354.78", "fileDate": 16531776, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/bb6df2920f76b6f413290ea29bdd55b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb6df2920f76b6f413290ea29bdd55b7\", \"key\": \"Goin To Jail Tonight\", \"title\": \"Goin To Jail Tonight [V1]\", \"artists\": \"(prod. 88-Keys & Ojivolta)\", \"aliases\": [\"Jail\", \"Goin 2 Jail\", \"88 Idea 3\"], \"description\": \"OG Filenames: 210513 Gallery - Goin To Jail Tonight & \\n210513 NOID Piano w OxV Bass\\nOriginal freestyle, featuring mumble with mostly the same lines as released besides some alternate lines.\", \"date\": 16531776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1191ae97490d3e49d9215f71b2204424\", \"url\": \"https://api.pillowcase.su/api/download/1191ae97490d3e49d9215f71b2204424\", \"size\": \"12.3 MB\", \"duration\": 354.78}", "aliases": ["Jail", "Goin 2 Jail", "88 Idea 3"], "size": "12.3 MB"}, {"id": "goin-2-jail", "name": "Goin 2 Jail [V3]", "artists": [], "producers": ["Dem <PERSON>z"], "notes": "OG Filename: GOIN 2 JAIL (YE - Dem Jointz Edit 107bpm) ST\n\"Finished\" version of the song that includes punch-ins and production from Dem Jointz. Includes lines not heard in the final version.", "length": "188.47", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6b06564eb3bae4d43a178f8cd9ef8cb1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6b06564eb3bae4d43a178f8cd9ef8cb1\", \"key\": \"Goin 2 Jail\", \"title\": \"Goin 2 Jail [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Dem Jointz)\", \"aliases\": [\"Goin To Jail Tonight\", \"Jail\", \"88 Idea 3\"], \"description\": \"OG Filename: GOIN 2 JAIL (YE - Dem Jointz Edit 107bpm) ST\\n\\\"Finished\\\" version of the song that includes punch-ins and production from Dem Jointz. Includes lines not heard in the final version.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"03a83e9ebf55724cfa4d34d079246157\", \"url\": \"https://api.pillowcase.su/api/download/03a83e9ebf55724cfa4d34d079246157\", \"size\": \"9.61 MB\", \"duration\": 188.47}", "aliases": ["Goin To Jail Tonight", "Jail", "88 Idea 3"], "size": "9.61 MB"}, {"id": "judo", "name": "🗑️ Judo", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - <PERSON><PERSON>style from May 2021. Original snippet leaked December 12th, 2022. Later forceleaked. <PERSON> does a high pitched vocals and has only mumble.", "length": "39.8", "fileDate": 16943040, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f3f2b3af4610a2707aa2d70df1e7ffcb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f3f2b3af4610a2707aa2d70df1e7ffcb\", \"key\": \"Judo\", \"title\": \"\\ud83d\\uddd1\\ufe0f Judo\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"OG Filename: 210526 Gallery - Judo\\nFreestyle from May 2021. Original snippet leaked December 12th, 2022. Later forceleaked. Ye does a high pitched vocals and has only mumble.\", \"date\": 16943040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"38937979f62b3f2bd474a5221b56827e\", \"url\": \"https://api.pillowcase.su/api/download/38937979f62b3f2bd474a5221b56827e\", \"size\": \"7.23 MB\", \"duration\": 39.8}", "aliases": [], "size": "7.23 MB"}, {"id": "godspeed", "name": "Godspeed [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Godspeed\nEarlist known version, features earlier production and a longer Kanye mumble freestyle. \"Godspeed\" is the title of the beat, and not the actual song.", "length": "240.05", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/7e85cbccc58e886bc40376b5b4d51de2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e85cbccc58e886bc40376b5b4d51de2\", \"key\": \"Godspeed\", \"title\": \"Godspeed [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: 210526 Gallery - Godspeed\\nEarlist known version, features earlier production and a longer Kanye mumble freestyle. \\\"Godspeed\\\" is the title of the beat, and not the actual song.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0b390d01f833453cb0fde5c6a4221466\", \"url\": \"https://api.pillowcase.su/api/download/0b390d01f833453cb0fde5c6a4221466\", \"size\": \"10.4 MB\", \"duration\": 240.05}", "aliases": ["<PERSON><PERSON>"], "size": "10.4 MB"}, {"id": "keep-em-in-check", "name": "Keep Em In Check", "artists": [], "producers": ["Ojivolta", "88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 05 Keep Em In Check\nMumble freestyle, from which some lines were reused and re-recorded for \"Go Left\". Leaked with stems alongside \"Go Left\" after a groupbuy.", "length": "133.75", "fileDate": 16468704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5f35243fe3c7e26c3b041e2801c5a02e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f35243fe3c7e26c3b041e2801c5a02e\", \"key\": \"Keep Em In Check\", \"title\": \"Keep Em In Check\", \"artists\": \"(prod. Ojivolta & 88-Keys)\", \"description\": \"OG Filename: 210510 Gallery Freestyles - 05 Keep Em In Check\\nMumble freestyle, from which some lines were reused and re-recorded for \\\"Go Left\\\". Leaked with stems alongside \\\"Go Left\\\" after a groupbuy.\", \"date\": 16468704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"14adce2466b66d9675e46e64ea013eae\", \"url\": \"https://api.pillowcase.su/api/download/14adce2466b66d9675e46e64ea013eae\", \"size\": \"8.73 MB\", \"duration\": 133.75}", "aliases": [], "size": "8.73 MB"}, {"id": "keep-em-in-check-56", "name": "Keep Em In Check", "artists": [], "producers": ["Ojivolta", "88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 05 Keep Em In Check\nMumble freestyle, from which some lines were reused and re-recorded for \"Go Left\". Leaked with stems alongside \"Go Left\" after a groupbuy.", "length": "133.75", "fileDate": 16468704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5f35243fe3c7e26c3b041e2801c5a02e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f35243fe3c7e26c3b041e2801c5a02e\", \"key\": \"Keep Em In Check\", \"title\": \"Keep Em In Check\", \"artists\": \"(prod. Ojivolta & 88-Keys)\", \"description\": \"OG Filename: 210510 Gallery Freestyles - 05 Keep Em In Check\\nMumble freestyle, from which some lines were reused and re-recorded for \\\"Go Left\\\". Leaked with stems alongside \\\"Go Left\\\" after a groupbuy.\", \"date\": 16468704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"14adce2466b66d9675e46e64ea013eae\", \"url\": \"https://api.pillowcase.su/api/download/14adce2466b66d9675e46e64ea013eae\", \"size\": \"8.73 MB\", \"duration\": 133.75}", "aliases": [], "size": "8.73 MB"}, {"id": "la-llave", "name": "la llave", "artists": [], "producers": [], "notes": "OG Filename: la llave_21.04.26 Gallery Freestyles\nRough freestyle with voice memo vocals, made in late April 2021 during the Gallery sessions.", "length": "202.68", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c8e7845fa647ca915a8273368a2b7e4b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8e7845fa647ca915a8273368a2b7e4b\", \"key\": \"la llave\", \"title\": \"la llave\", \"description\": \"OG Filename: la llave_21.04.26 Gallery Freestyles\\nRough freestyle with voice memo vocals, made in late April 2021 during the Gallery sessions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"aebea915ca69d9a57b2ecbe4b48a4cf8\", \"url\": \"https://api.pillowcase.su/api/download/aebea915ca69d9a57b2ecbe4b48a4cf8\", \"size\": \"9.83 MB\", \"duration\": 202.68}", "aliases": [], "size": "9.83 MB"}, {"id": "leave", "name": "Leave", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Leave\nBeat later given by Digital Nas to <PERSON><PERSON> for his song \"Back Back Back\". Samples \"Souvlaki Space Station\" by Slowdive.", "length": "", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/195584d1878331b8db396484151222db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/195584d1878331b8db396484151222db\", \"key\": \"Leave\", \"title\": \"Leave\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Leave\\nBeat later given by Digital Nas to <PERSON><PERSON> for his song \\\"Back Back Back\\\". Samples \\\"Souvlaki Space Station\\\" by Slowdive.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "let-me-know", "name": "Let Me Know [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Let Me Know\nFreestyle made during the May 2021 sessions. Samples \"Space Song\" by Beach House.", "length": "", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5d4b1919bb13d18d44112f9980e07792", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d4b1919bb13d18d44112f9980e07792\", \"key\": \"Let Me Know\", \"title\": \"Let Me Know [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Let Me Know\\nFreestyle made during the May 2021 sessions. Samples \\\"Space Song\\\" by Beach House.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "let-me-know-60", "name": "Let Me Know [V2]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Let Me Know_117bpm\nLonger version of \"Let Me Know\" with over 2 minutes of an open outro.", "length": "205.97", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/42bcb9d4c3cb708300f88bfd01443af3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/42bcb9d4c3cb708300f88bfd01443af3\", \"key\": \"Let Me Know\", \"title\": \"Let Me Know [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210526 Gallery - Let Me Know_117bpm\\nLonger version of \\\"Let Me Know\\\" with over 2 minutes of an open outro.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3fab85c9cf2c967e49d51061df4db446\", \"url\": \"https://api.pillowcase.su/api/download/3fab85c9cf2c967e49d51061df4db446\", \"size\": \"9.89 MB\", \"duration\": 205.97}", "aliases": [], "size": "9.89 MB"}, {"id": "let-s-go", "name": "Let's Go [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Let's Go\nFreestyle made during the May 2021 sessions.", "length": "192.84", "fileDate": 16563744, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/60e8a76af20ae0432feec23a8d2254e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/60e8a76af20ae0432feec23a8d2254e1\", \"key\": \"Let's Go\", \"title\": \"Let's Go [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Let's Go\\nFreestyle made during the May 2021 sessions.\", \"date\": 16563744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1bccbf9264cd181a2fe2b7764358b2ae\", \"url\": \"https://api.pillowcase.su/api/download/1bccbf9264cd181a2fe2b7764358b2ae\", \"size\": \"9.68 MB\", \"duration\": 192.84}", "aliases": [], "size": "9.68 MB"}, {"id": "let-s-go-62", "name": "Let's Go [V2]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Let's Go_124.5Bpm\nLonger version, with an extra minute of an open outro.", "length": "249.55", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6d1b93d1d6b33f097304392d3083ad47", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d1b93d1d6b33f097304392d3083ad47\", \"key\": \"Let's Go\", \"title\": \"Let's Go [V2]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Let's Go_124.5Bpm\\nLonger version, with an extra minute of an open outro.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cc1e27397bb1b31526fcc18c42e321d8\", \"url\": \"https://api.pillowcase.su/api/download/cc1e27397bb1b31526fcc18c42e321d8\", \"size\": \"10.6 MB\", \"duration\": 249.55}", "aliases": [], "size": "10.6 MB"}, {"id": "life-of-the-party", "name": "Life Of The Party [V1]", "artists": [], "producers": ["88-<PERSON>", "Ojivolta"], "notes": "OG Filename: 210524 Gallery 1 - Life Of The Party - 88 OxV Flip Ye Freestyle\nA Gallery freestyle titled \"Life Of The Party.\" The instrumental shares the same chords as \"Life Of The Party\". Possibly Ojivolta's take on the beat, according to trusted sources. Later chopped up to make \"NASDAQ\". Lines from the freestyle were also re-recorded in a later freestyle for \"HIIII WYD\".", "length": "1001.89", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5938a71c78ea89f1393d84128487ec67", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5938a71c78ea89f1393d84128487ec67\", \"key\": \"Life Of The Party\", \"title\": \"Life Of The Party [V1]\", \"artists\": \"(prod. 88-Keys & Ojivolta)\", \"aliases\": [\"NASDAQ\"], \"description\": \"OG Filename: 210524 Gallery 1 - Life Of The Party - 88 OxV Flip Ye Freestyle\\nA Gallery freestyle titled \\\"Life Of The Party.\\\" The instrumental shares the same chords as \\\"Life Of The Party\\\". Possibly Ojivolta's take on the beat, according to trusted sources. Later chopped up to make \\\"NASDAQ\\\". Lines from the freestyle were also re-recorded in a later freestyle for \\\"HIIII WYD\\\".\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ba2d714c0bf1fdb3bcfb6aefc2de5427\", \"url\": \"https://api.pillowcase.su/api/download/ba2d714c0bf1fdb3bcfb6aefc2de5427\", \"size\": \"22.6 MB\", \"duration\": 1001.89}", "aliases": ["NASDAQ"], "size": "22.6 MB"}, {"id": "nasdaq", "name": "⭐ NASDAQ [V3]", "artists": ["STALONE", "Dem <PERSON>z"], "producers": ["Dem <PERSON>z"], "notes": "<PERSON><PERSON> rework of the Gallery \"Life Of The Party\" freestyle. Was described as \"industrial\" sounding, and was compared to the sound of Yeezus. Has Dem <PERSON>z punch-ins and two lines of mumble, and STALON<PERSON> backing vocals. Samples \"African Mist Voice 11\" from GarageBand Jam Pack: World Music - African Mist Voice Samples.", "length": "146.31", "fileDate": 16860096, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9c9e59bd29b110f6a87882fd1c07ad41", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c9e59bd29b110f6a87882fd1c07ad41\", \"key\": \"NASDAQ\", \"title\": \"\\u2b50 NASDAQ [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. STALONE & Dem Jointz) (prod. Dem Jointz)\", \"aliases\": [\"Life Of The Party\"], \"description\": \"Dem Jointz rework of the Gallery \\\"Life Of The Party\\\" freestyle. Was described as \\\"industrial\\\" sounding, and was compared to the sound of Yeezus. Has Dem Jointz punch-ins and two lines of mumble, and STALON<PERSON> backing vocals. Samples \\\"African Mist Voice 11\\\" from GarageBand Jam Pack: World Music - African Mist Voice Samples.\", \"date\": 16860096, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f76e7089516028a5a8991141862e9909\", \"url\": \"https://api.pillowcase.su/api/download/f76e7089516028a5a8991141862e9909\", \"size\": \"8.93 MB\", \"duration\": 146.31}", "aliases": ["Life Of The Party"], "size": "8.93 MB"}, {"id": "life-of-the-party-65", "name": "Life Of The Party [V13]", "artists": [], "producers": ["AllDay", "<PERSON>", "The Twilite Tone", "Dem <PERSON>z"], "notes": "OG Filename: Life of the Party_Dem Jointz - 21.05.25 Review\nHas a beat closer to the July/August 2021 versions of the song, but with the original 2020 finished take. Features finalized mixing as well.", "length": "200.31", "fileDate": 17087328, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/07aeb256c325a74a0a94f83d8408ace5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/07aeb256c325a74a0a94f83d8408ace5\", \"key\": \"Life Of The Party\", \"title\": \"Life Of The Party [V13]\", \"artists\": \"(prod. <PERSON>, <PERSON>, The Twilite Tone & Dem Jointz)\", \"description\": \"OG Filename: Life of the Party_Dem Jointz - 21.05.25 Review\\nHas a beat closer to the July/August 2021 versions of the song, but with the original 2020 finished take. Features finalized mixing as well.\", \"date\": 17087328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fe3b41d1860a1e3ab53b90cb78873dc2\", \"url\": \"https://api.pillowcase.su/api/download/fe3b41d1860a1e3ab53b90cb78873dc2\", \"size\": \"9.8 MB\", \"duration\": 200.31}", "aliases": [], "size": "9.8 MB"}, {"id": "life-of-the-party-66", "name": "Life Of The Party [V14]", "artists": [], "producers": ["AllDay", "<PERSON>", "The Twilite Tone", "Dem <PERSON>z"], "notes": "OG Filename: Life of the Party_Dem Jointz - 21.05.25 Review WM for Clearance\nIdentical to the previous version, but with a sample clearance tag throughout.", "length": "200.41", "fileDate": 16954272, "leakDate": "", "availableLength": "Tagged", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e780995440d75c1532373b6f974ee166", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e780995440d75c1532373b6f974ee166\", \"key\": \"Life Of The Party\", \"title\": \"Life Of The Party [V14]\", \"artists\": \"(prod. <PERSON>, <PERSON>, The Twilite Tone & Dem Jointz)\", \"description\": \"OG Filename: Life of the Party_Dem Jointz - 21.05.25 Review WM for Clearance\\nIdentical to the previous version, but with a sample clearance tag throughout.\", \"date\": 16954272, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7d16c6aa01bd962dc92a51372c43d36a\", \"url\": \"https://api.pillowcase.su/api/download/7d16c6aa01bd962dc92a51372c43d36a\", \"size\": \"9.8 MB\", \"duration\": 200.41}", "aliases": [], "size": "9.8 MB"}, {"id": "livin-it-up", "name": "Livin It Up [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "notes": "OG Filename: <PERSON>in It Up - 21.05.19 Stretch\nCreated initially as a reference track for the Pusha T song \"Rock N Roll\", this version contains added production elements. Confirmed to be used by <PERSON><PERSON><PERSON>.", "length": "141.61", "fileDate": 16404768, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/28560a1369542243dad7335f24f0fe5b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/28560a1369542243dad7335f24f0fe5b\", \"key\": \"Livin It Up\", \"title\": \"Livin It Up [V1]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: Livin It Up - 21.05.19 <PERSON><PERSON><PERSON>\\nCreated initially as a reference track for the Pusha T song \\\"Rock N Roll\\\", this version contains added production elements. Confirmed to be used by Ka<PERSON>e.\", \"date\": 16404768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1347464e707cebdbf62c537e3d8d7255\", \"url\": \"https://api.pillowcase.su/api/download/1347464e707cebdbf62c537e3d8d7255\", \"size\": \"8.86 MB\", \"duration\": 141.61}", "aliases": [], "size": "8.86 MB"}, {"id": "love-attacks", "name": "✨ Love Attacks", "artists": [], "producers": [], "notes": "OG Filename: Love Attacks_21.04.26 Gallery Freestyles\nRough freestyle with extremely low quality voice memo vocals.", "length": "173.75", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/66510016d3ac8f6e42fc235142963150", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/66510016d3ac8f6e42fc235142963150\", \"key\": \"Love Attacks\", \"title\": \"\\u2728 Love Attacks\", \"description\": \"OG Filename: Love Attacks_21.04.26 Gallery Freestyles\\nRough freestyle with extremely low quality voice memo vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"45cbd54ddab03522ec4915969b90c826\", \"url\": \"https://api.pillowcase.su/api/download/45cbd54ddab03522ec4915969b90c826\", \"size\": \"9.37 MB\", \"duration\": 173.75}", "aliases": [], "size": "9.37 MB"}, {"id": "new-hunnids", "name": "New Hunnids", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - New Hunnids\nGallery freestyle from May 2021.", "length": "15.94", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/48e94b69552e9bbbc0ae04c1069a941c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48e94b69552e9bbbc0ae04c1069a941c\", \"key\": \"New Hunnids\", \"title\": \"New Hunnids\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - New Hunnids\\nGallery freestyle from May 2021.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"de8de2c58d806cc90187e3175f23cb0d\", \"url\": \"https://api.pillowcase.su/api/download/de8de2c58d806cc90187e3175f23cb0d\", \"size\": \"6.85 MB\", \"duration\": 15.94}", "aliases": [], "size": "6.85 MB"}, {"id": "nigga", "name": "🗑️ Nigga", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - <PERSON><PERSON><PERSON>\nShort freestyle over a really bad Digital Nas beat. Yes, this is the full song, not just a snippet. <PERSON><PERSON><PERSON> can be heard asking for the next beat after freestyling for nine seconds.", "length": "13.49", "fileDate": 16548192, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5c0835b6c48352ec326d2e2627657d1c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5c0835b6c48352ec326d2e2627657d1c\", \"key\": \"Nigga\", \"title\": \"\\ud83d\\uddd1\\ufe0f Nigga\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"OG Filename: 210526 Gallery - Nigga\\nShort freestyle over a really bad Digital Nas beat. Yes, this is the full song, not just a snippet. <PERSON><PERSON><PERSON> can be heard asking for the next beat after freestyling for nine seconds.\", \"date\": 16548192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"208cd18373dba7aafbfffe4b1167cd35\", \"url\": \"https://api.pillowcase.su/api/download/208cd18373dba7aafbfffe4b1167cd35\", \"size\": \"6.81 MB\", \"duration\": 13.49}", "aliases": [], "size": "6.81 MB"}, {"id": "ninja", "name": "Ninja", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Ninja\nFreestyle from May 2021. Original snippet leaked December 22nd, 2022.", "length": "57.81", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9818cb56d1163f3737e44cbf11999b38", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9818cb56d1163f3737e44cbf11999b38\", \"key\": \"<PERSON>\", \"title\": \"<PERSON>\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Ninja\\nFreestyle from May 2021. Original snippet leaked December 22nd, 2022.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fb2819fbb8890bce01ea7f9768032d40\", \"url\": \"https://api.pillowcase.su/api/download/fb2819fbb8890bce01ea7f9768032d40\", \"size\": \"7.52 MB\", \"duration\": 57.81}", "aliases": [], "size": "7.52 MB"}, {"id": "nowhere", "name": "✨ Nowhere", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Nowhere\nRough but finished freestyle with heartfelt singing over a Digital Nas shoegaze sample flip.", "length": "83.72", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/dcd1060a844faf069d27790c745ec004", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dcd1060a844faf069d27790c745ec004\", \"key\": \"Nowhere\", \"title\": \"\\u2728 Nowhere\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"OG Filename: 210526 Gallery - Nowhere\\nRough but finished freestyle with heartfelt singing over a Digital Nas shoegaze sample flip.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"026c2bc5d0995ac37f945c7e45c1568d\", \"url\": \"https://api.pillowcase.su/api/download/026c2bc5d0995ac37f945c7e45c1568d\", \"size\": \"7.93 MB\", \"duration\": 83.72}", "aliases": [], "size": "7.93 MB"}, {"id": "ops-block", "name": "Ops Block [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Ops Block\nInitial 12 minute freestyle. Vocals from this freestyle were reused in later versions of the song. \"Ops Block\" is the title of the beat, and not the actual song. <PERSON><PERSON><PERSON> gets off beat about halfway through.", "length": "732.23", "fileDate": 16351200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/63a0ffd3ba0978c5f037935280cacb44", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63a0ffd3ba0978c5f037935280cacb44\", \"key\": \"Ops Block\", \"title\": \"Ops Block [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas)\", \"aliases\": [\"Hovercraft\", \"Remote\", \"Remote Control\"], \"description\": \"OG Filename: 210526 Gallery - Ops Block\\nInitial 12 minute freestyle. Vocals from this freestyle were reused in later versions of the song. \\\"Ops Block\\\" is the title of the beat, and not the actual song. <PERSON><PERSON><PERSON> gets off beat about halfway through.\", \"date\": 16351200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f954c97bd43c239fef7808c0e2de0a24\", \"url\": \"https://api.pillowcase.su/api/download/f954c97bd43c239fef7808c0e2de0a24\", \"size\": \"18.3 MB\", \"duration\": 732.23}", "aliases": ["Hovercraft", "Remote", "Remote Control"], "size": "18.3 MB"}, {"id": "petals", "name": "Petals [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210513 Gallery - PETALS 89 -[KW x BoogzDaBeast] \nA May 2021 freestyle that samples \"Petals\" by Bibio.", "length": "251.01", "fileDate": 16722720, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e570b4e51bbef503584d20c52083c0b2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e570b4e51bbef503584d20c52083c0b2\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 210513 Gallery - PETALS 89 -[KW x BoogzDaBeast] \\nA May 2021 freestyle that samples \\\"Petals\\\" by Bibio.\", \"date\": 16722720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d3c4e502be9c8863b3d423f806012c3d\", \"url\": \"https://api.pillowcase.su/api/download/d3c4e502be9c8863b3d423f806012c3d\", \"size\": \"10.6 MB\", \"duration\": 251.01}", "aliases": [], "size": "10.6 MB"}, {"id": "pick-a-side", "name": "Pick A Side", "artists": [], "producers": ["Ojivolta", "88-<PERSON>"], "notes": "OG Filename: 210510 Gallery Freestyles - 06 Pick A Side\nGallery freestyle. 7th freestyle recorded on May 10th, 2021.", "length": "149.63", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5ec5c900fd08ca740c0819ce5df3ccd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ec5c900fd08ca740c0819ce5df3ccd7\", \"key\": \"Pick A Side\", \"title\": \"Pick A Side\", \"artists\": \"(prod. Ojivolta & 88-Keys)\", \"description\": \"OG Filename: 210510 Gallery Freestyles - 06 Pick A Side\\nGallery freestyle. 7th freestyle recorded on May 10th, 2021.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ce4d7a8cfa019691b5e4ac45a522f142\", \"url\": \"https://api.pillowcase.su/api/download/ce4d7a8cfa019691b5e4ac45a522f142\", \"size\": \"8.98 MB\", \"duration\": 149.63}", "aliases": [], "size": "8.98 MB"}, {"id": "pray-it-and-leave-it", "name": "Pray It and Leave It [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: kw gallery with jw 2-2\nDon Toliver reference track.", "length": "123.74", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/875fb91c9f7cb2f79c56c52317954323", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/875fb91c9f7cb2f79c56c52317954323\", \"key\": \"Pray It and Leave It\", \"title\": \"Pray It and Leave It [V1]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: kw gallery with jw 2-2\\nDon Toliver reference track.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9f82d4969ce3927722b30d8b34871bf7\", \"url\": \"https://api.pillowcase.su/api/download/9f82d4969ce3927722b30d8b34871bf7\", \"size\": \"8.57 MB\", \"duration\": 123.74}", "aliases": [], "size": "8.57 MB"}, {"id": "proximious-nonded", "name": "Proximious Nonded", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Proximious Nonded_Comp\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "33.17", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/df978a0347f2596b2ba24a62b9ca1f78", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/df978a0347f2596b2ba24a62b9ca1f78\", \"key\": \"Proximious Nonded\", \"title\": \"Proximious Nonded\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Proximious Nonded_Comp\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd0d2e86500ee93a578e244c398378bb\", \"url\": \"https://api.pillowcase.su/api/download/bd0d2e86500ee93a578e244c398378bb\", \"size\": \"7.12 MB\", \"duration\": 33.17}", "aliases": [], "size": "7.12 MB"}, {"id": "red-dead-range", "name": "Red Dead Range", "artists": [], "producers": [], "notes": "OG Filename: Red Dead Range_21.04.26 Gallery Freestyles\nRough gallery freestyle.", "length": "107.74", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/ae113a621ffebc9b267ce59cfe2a989a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae113a621ffebc9b267ce59cfe2a989a\", \"key\": \"Red Dead Range\", \"title\": \"Red Dead Range\", \"description\": \"OG Filename: Red Dead Range_21.04.26 Gallery Freestyles\\nRough gallery freestyle.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"31b357debea6bc19f8ed3e3ce92ee6e8\", \"url\": \"https://api.pillowcase.su/api/download/31b357debea6bc19f8ed3e3ce92ee6e8\", \"size\": \"8.31 MB\", \"duration\": 107.74}", "aliases": [], "size": "8.31 MB"}, {"id": "futures", "name": "Futures [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210513 Gallery - Futures 98 -KW x BoogzDaBeast\nInitial freestyle. Samples \"I Wanna Know, Is It Over\" by The Futures.", "length": "587.81", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/abd2203de7e24d6e5a4d80199f946434", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/abd2203de7e24d6e5a4d80199f946434\", \"key\": \"Futures\", \"title\": \"Futures [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>DaBeas<PERSON>)\", \"aliases\": [\"Run It Up\", \"No Child Left Behind\"], \"description\": \"OG Filename: 210513 Gallery - Futures 98 -KW x BoogzDaBeast\\nInitial freestyle. Samples \\\"I Wanna Know, Is It Over\\\" by The Futures.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4061890f2537d8b45227452fa528220f\", \"url\": \"https://api.pillowcase.su/api/download/4061890f2537d8b45227452fa528220f\", \"size\": \"16 MB\", \"duration\": 587.81}", "aliases": ["Run It Up", "No Child Left Behind"], "size": "16 MB"}, {"id": "run-it-up", "name": "Run It Up [V2]", "artists": [], "producers": ["BoogzDaBeast", "Dem <PERSON>z", "Ojivolta"], "notes": "OG Filename: RUN IT UP (YE_Dem Jointz Edit 98bpm) ST\nDem Jointz rework, cut-down and with punch-ins. Leaked after a groupbuy.", "length": "163.51", "fileDate": 16664832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5d48d63d7079223cb025edcc33650f3a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d48d63d7079223cb025edcc33650f3a\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Bo<PERSON>z<PERSON>aB<PERSON>t, Dem Jointz & Ojivolta)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: RUN IT UP (YE_Dem Jointz Edit 98bpm) ST\\nDem Jointz rework, cut-down and with punch-ins. Leaked after a groupbuy.\", \"date\": 16664832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"65c343587e2a204d245fcee69204bf7e\", \"url\": \"https://api.pillowcase.su/api/download/65c343587e2a204d245fcee69204bf7e\", \"size\": \"9.21 MB\", \"duration\": 163.51}", "aliases": ["No Child Left Behind"], "size": "9.21 MB"}, {"id": "run-it-up-81", "name": "Run It Up [V3]", "artists": [], "producers": ["BoogzDaBeast", "Dem <PERSON>z", "Ojivolta", "<PERSON>", "<PERSON>"], "notes": "OG Filename: RUN IT UP (YE_Dem Jointz Edit 98bpm) - 21.05.18 RxD\nHas added production from RxD.", "length": "160.49", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2180d87c7b1f6d0fc68d5ad66d7b9de0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2180d87c7b1f6d0fc68d5ad66d7b9de0\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ojivolta, Rico <PERSON> & Darius <PERSON>)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: RUN IT UP (YE_Dem Jointz Edit 98bpm) - 21.05.18 RxD\\nHas added production from RxD.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"92724e654853ddeaf5a1298a4562c7ce\", \"url\": \"https://api.pillowcase.su/api/download/92724e654853ddeaf5a1298a4562c7ce\", \"size\": \"9.16 MB\", \"duration\": 160.49}", "aliases": ["No Child Left Behind"], "size": "9.16 MB"}, {"id": "saccofight-ciliodon", "name": "Saccofight Ciliodon", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - <PERSON><PERSON>z Microtonic PB - PTN Saccofight Ciliodon\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>. The shortest known Ye freestyle.", "length": "11.5", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/93ade6c29db5f808d630bff93bedd9ee", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/93ade6c29db5f808d630bff93bedd9ee\", \"key\": \"Saccofight Ciliodon\", \"title\": \"Saccofight Ciliodon\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>DaBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Saccofight Ciliodon\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>. The shortest known Ye freestyle.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"61c6297eea1016e71a0c04a080ab943c\", \"url\": \"https://api.pillowcase.su/api/download/61c6297eea1016e71a0c04a080ab943c\", \"size\": \"6.77 MB\", \"duration\": 11.5}", "aliases": [], "size": "6.77 MB"}, {"id": "scolona-mispute", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Scolona Mispute\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "54.14", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c9a32b24c61de39c76570654bdcb37e8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9a32b24c61de39c76570654bdcb37e8\", \"key\": \"Scolona Mispute\", \"title\": \"Scolona Mispute\", \"artists\": \"(prod. <PERSON><PERSON>z<PERSON>aBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Scolona Mispute\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a0928409a867a10b9255288fb8ff4e08\", \"url\": \"https://api.pillowcase.su/api/download/a0928409a867a10b9255288fb8ff4e08\", \"size\": \"7.46 MB\", \"duration\": 54.14}", "aliases": [], "size": "7.46 MB"}, {"id": "smooth", "name": "Smooth", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Smooth\nFreestyle over a Digital Nas beat. VC recorded snippet leaked by a fake insider who falsely claimed it was made for Donda 2. Beat was reused for the Tesu<PERSON>reme song \"Illes<PERSON>\". Original snippet leaked March 2022.", "length": "129.88", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9d0e048a632c3bc6fc6697761f098e1b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d0e048a632c3bc6fc6697761f098e1b\", \"key\": \"Smooth\", \"title\": \"Smooth\", \"artists\": \"(prod. Digital Nas)\", \"aliases\": [\"Illest\"], \"description\": \"OG Filename: 210526 Gallery - Smooth\\nFreestyle over a Digital Nas beat. VC recorded snippet leaked by a fake insider who falsely claimed it was made for Donda 2. Beat was reused for the Tesupreme song \\\"Illest\\\". Original snippet leaked March 2022.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"875dbaf729ddafde6778ca5718a0d4b1\", \"url\": \"https://api.pillowcase.su/api/download/875dbaf729ddafde6778ca5718a0d4b1\", \"size\": \"8.67 MB\", \"duration\": 129.88}", "aliases": ["<PERSON><PERSON><PERSON>"], "size": "8.67 MB"}, {"id": "swole", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - Swole\nDigital Nas-produced mumble freestyle.", "length": "105.33", "fileDate": 16383168, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3eabeeec9320e9a99763daf0dc4ffb5f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3eabeeec9320e9a99763daf0dc4ffb5f\", \"key\": \"Swole\", \"title\": \"<PERSON>wole\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - Swole\\nDigital Nas-produced mumble freestyle.\", \"date\": 16383168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1c0de433e9972cb8a2628c35f3b9492e\", \"url\": \"https://api.pillowcase.su/api/download/1c0de433e9972cb8a2628c35f3b9492e\", \"size\": \"8.28 MB\", \"duration\": 105.33}", "aliases": [], "size": "8.28 MB"}, {"id": "take-your-time", "name": "Take Your Time ", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210521 Gallery - OV DT 9 Take Your Time\nRough Don Toliver reference track over an Ojivolta synth instrumental.", "length": "472.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8418935b3672d57bb673494432980cd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8418935b3672d57bb673494432980cd7\", \"key\": \"Take Your Time\", \"title\": \"Take Your Time \", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: 210521 Gallery - OV DT 9 Take Your Time\\nRough Don Toliver reference track over an Ojivolta synth instrumental.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5ac26ff36c1d07ee27ffdbcc91bab99b\", \"url\": \"https://api.pillowcase.su/api/download/5ac26ff36c1d07ee27ffdbcc91bab99b\", \"size\": \"14.1 MB\", \"duration\": 472.06}", "aliases": [], "size": "14.1 MB"}, {"id": "tell-my-story", "name": "Tell My Story", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210521 Gallery - OV DT 8 Tell My Story\nRough Don Toliver reference track over an Ojivolta piano instrumental. Shares some flow and lyrics with the previous freestyle, \"Holy Ghost\".", "length": "318.98", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/1e7592119f503c31379065ef3611842d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e7592119f503c31379065ef3611842d\", \"key\": \"Tell My Story\", \"title\": \"Tell My Story\", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: 210521 Gallery - OV DT 8 Tell My Story\\nRough Don Toliver reference track over an Ojivolta piano instrumental. Shares some flow and lyrics with the previous freestyle, \\\"Holy Ghost\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6c736f235f605f40d9afefc19f279755\", \"url\": \"https://api.pillowcase.su/api/download/6c736f235f605f40d9afefc19f279755\", \"size\": \"11.7 MB\", \"duration\": 318.98}", "aliases": [], "size": "11.7 MB"}, {"id": "thunder", "name": "✨ Thunder", "artists": [], "producers": ["Dem <PERSON>z", "88-<PERSON>"], "notes": "<PERSON><PERSON> rework of the \"Back Then\", \"Friends List\" and \"Different Codes\" freestyles, using elements of the instrumental to those and vocals from \"Back Then\" and \"Different Codes\". Features punch-ins from <PERSON><PERSON> meant as reference vocals for <PERSON><PERSON><PERSON>. Produced by <PERSON><PERSON> and <PERSON>-<PERSON>. Incorrectly believed to be produced by <PERSON><PERSON><PERSON> due to the 4-count intro. Samples \"African King Ensemble\" by <PERSON>.", "length": "123.33", "fileDate": 16924896, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9b0c2f97b0948bb229c56836329fd845", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9b0c2f97b0948bb229c56836329fd845\", \"key\": \"Thunder\", \"title\": \"\\u2728 Thunder\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Dem Jointz & 88-Keys)\", \"aliases\": [\"Different Codes\", \"Don't Miss Me\", \"Back Then\", \"Friends List\"], \"description\": \"Dem Jointz rework of the \\\"Back Then\\\", \\\"Friends List\\\" and \\\"Different Codes\\\" freestyles, using elements of the instrumental to those and vocals from \\\"Back Then\\\" and \\\"Different Codes\\\". Features punch-ins from <PERSON><PERSON> meant as reference vocals for <PERSON><PERSON><PERSON>. Produced by <PERSON><PERSON>z and 88-Keys. Incorrectly believed to be produced by <PERSON><PERSON><PERSON> due to the 4-count intro. Samples \\\"African King Ensemble\\\" by <PERSON>.\", \"date\": 16924896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b541acd256ee240a35d30d0da24ba994\", \"url\": \"https://api.pillowcase.su/api/download/b541acd256ee240a35d30d0da24ba994\", \"size\": \"8.56 MB\", \"duration\": 123.33}", "aliases": ["Different Codes", "Don't Miss Me", "Back Then", "Friends List"], "size": "8.56 MB"}, {"id": "to-the-roof", "name": "To The Roof [V3]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: KW - To The Roof - 21.04.11 Stems OxV 130bpm Stem Ref\nVersion with <PERSON> vocals. Uses a similar beat to the Boots reference track. Has less effects on <PERSON><PERSON><PERSON>'s vocals than the later version of the song. Leaked in full as a bonus for the \"Skeletons\" groupbuy.", "length": "324.99", "fileDate": 16561152, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d91c6283c196b525d8608836df944894", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d91c6283c196b525d8608836df944894\", \"key\": \"To The Roof\", \"title\": \"To The Roof [V3]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"OG Filename: KW - To The Roof - 21.04.11 Stems OxV 130bpm Stem Ref\\nVersion with Ye vocals. Uses a similar beat to the Boots reference track. Has less effects on <PERSON><PERSON><PERSON>'s vocals than the later version of the song. Leaked in full as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16561152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c6af366f7a2bfb78e0a337006f9395be\", \"url\": \"https://api.pillowcase.su/api/download/c6af366f7a2bfb78e0a337006f9395be\", \"size\": \"11.8 MB\", \"duration\": 324.99}", "aliases": [], "size": "11.8 MB"}, {"id": "to-the-roof-90", "name": "🗑️ To The Roof [V4]", "artists": [], "producers": ["Ojivolta", "<PERSON>"], "notes": "A reference track for this song by <PERSON> leaked in 2020, but many people thought it was not intended for <PERSON><PERSON><PERSON> due to a false claim by <PERSON><PERSON><PERSON> about the song being made during 808's era.", "length": "214.18", "fileDate": 16330464, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e205ba14789cec0fff8076b96736ce87", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e205ba14789cec0fff8076b96736ce87\", \"key\": \"To The Roof\", \"title\": \"\\ud83d\\uddd1\\ufe0f To The Roof [V4]\", \"artists\": \"(prod. O<PERSON> & <PERSON>)\", \"description\": \"A reference track for this song by <PERSON> leaked in 2020, but many people thought it was not intended for <PERSON><PERSON><PERSON> due to a false claim by <PERSON><PERSON><PERSON> about the song being made during 808's era.\", \"date\": 16330464, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5548fa81c54d617fc3840984f14734c8\", \"url\": \"https://api.pillowcase.su/api/download/5548fa81c54d617fc3840984f14734c8\", \"size\": \"10 MB\", \"duration\": 214.18}", "aliases": [], "size": "10 MB"}, {"id": "top", "name": "Top", "artists": [], "producers": ["Dem <PERSON>z", "88-<PERSON>"], "notes": "Another Dem Jointz rework of \"Different Codes\", with an entirely different beat from other versions of it. Features reference punch ins from Dem Jointz meant for <PERSON> to record over. Produced by <PERSON><PERSON> Jointz and 88-<PERSON>. Leaked after a groupbuy.", "length": "110.99", "fileDate": 16377120, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/94b35b32d3c75dc850f036696467d82d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/94b35b32d3c75dc850f036696467d82d\", \"key\": \"Top\", \"title\": \"Top\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Dem Jointz & 88-Keys)\", \"aliases\": [\"Different Codes\"], \"description\": \"Another Dem Jointz rework of \\\"Different Codes\\\", with an entirely different beat from other versions of it. Features reference punch ins from Dem Jointz meant for <PERSON> to record over. Produced by <PERSON><PERSON>z and <PERSON>-<PERSON>. Leaked after a groupbuy.\", \"date\": 16377120, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9ce3da48e690c915c2a435b41adb1f3d\", \"url\": \"https://api.pillowcase.su/api/download/9ce3da48e690c915c2a435b41adb1f3d\", \"size\": \"8.37 MB\", \"duration\": 110.99}", "aliases": ["Different Codes"], "size": "8.37 MB"}, {"id": "travigable-subrepel", "name": "Travigable Subrepel [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Travigable Subrepel\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "93.38", "fileDate": 16341696, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c9765d575d3b2a9882461b26d0654b1d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9765d575d3b2a9882461b26d0654b1d\", \"key\": \"Travigable Subrepel\", \"title\": \"Travigable Subrepel [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Travigable Subrepel\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16341696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"09fd7acdd90bfbad6feb758f639c342b\", \"url\": \"https://api.pillowcase.su/api/download/09fd7acdd90bfbad6feb758f639c342b\", \"size\": \"8.08 MB\", \"duration\": 93.38}", "aliases": [], "size": "8.08 MB"}, {"id": "scolona-mispute-93", "name": "<PERSON><PERSON><PERSON> Mi<PERSON>ute [V2]", "artists": [], "producers": ["BoogzDaBeast", "Ojivolta"], "notes": "OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Scolona Mispute_DT_OxV\nLater version of \"Travigable Subrepel\", weirdly under the title of another, different Boogz freestyle from this day. Features extra production from Ojivolta, as well as <PERSON> reference vocals.", "length": "320.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fdc28a04b2d37c82668e61df1d20cf00", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fdc28a04b2d37c82668e61df1d20cf00\", \"key\": \"Scolona Mispute\", \"title\": \"Scolona Mispute [V2]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast & Ojivolta)\", \"aliases\": [\"Travigable Subrepel\"], \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Scolona Mispute_DT_OxV\\nLater version of \\\"Travigable Subrepel\\\", weirdly under the title of another, different Boogz freestyle from this day. Features extra production from Ojivolta, as well as <PERSON> reference vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eddda770d849bd6c7f0477d1bb60bc43\", \"url\": \"https://api.pillowcase.su/api/download/eddda770d849bd6c7f0477d1bb60bc43\", \"size\": \"11.7 MB\", \"duration\": 320.06}", "aliases": ["Travigable Subrepel"], "size": "11.7 MB"}, {"id": "urgent-ss", "name": "Urgent SS", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: 210526 Gallery - Urgent SS\nGallery freestyle from May 2021. \"SS\" stands for \"screenshot\". Samples \"Ocean Of Thoughts And Dreams\" by The Dramatics. Has Ojivolta synth production on the second half.", "length": "244.49", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/39cc0b21d73820d9606b903efedc297c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/39cc0b21d73820d9606b903efedc297c\", \"key\": \"Urgent SS\", \"title\": \"Urgent SS\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"description\": \"OG Filename: 210526 Gallery - Urgent SS\\nGallery freestyle from May 2021. \\\"SS\\\" stands for \\\"screenshot\\\". <PERSON><PERSON> \\\"Ocean Of Thoughts And Dreams\\\" by The Dramatics. Has Ojivolta synth production on the second half.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e6c02c24bd5d8a6ee1d56d306b5093f3\", \"url\": \"https://api.pillowcase.su/api/download/e6c02c24bd5d8a6ee1d56d306b5093f3\", \"size\": \"10.5 MB\", \"duration\": 244.49}", "aliases": [], "size": "10.5 MB"}, {"id": "vax", "name": "Vax", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 210521 Gallery - Bo<PERSON>z Microtonic PB - PTN Vax\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.", "length": "60.82", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5f256e1f6a805257f5a398c81fbcee0c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f256e1f6a805257f5a398c81fbcee0c\", \"key\": \"Vax\", \"title\": \"Vax\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 210521 Gallery - Boogz Microtonic PB - PTN Vax\\nRough Gallery freestyle over a Patternarium Generation sample looped by <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1e20c35820a312ed0b247450cb986ab2\", \"url\": \"https://api.pillowcase.su/api/download/1e20c35820a312ed0b247450cb986ab2\", \"size\": \"7.56 MB\", \"duration\": 60.82}", "aliases": [], "size": "7.56 MB"}, {"id": "what-a-dream", "name": "What A Dream", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: 210526 Gallery - What A Dream\nLeaked for free as a bonus in the \"Lord I Need You\" buy. Samples \"Calm Oceans (Loverboys)\" by MIND SLUMS. Recorded on May 26th, 2021. This song was misattributed as \"Dreaming Of The Past\" before any information on that song was known about.", "length": "24.24", "fileDate": 16410816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2de917ed887e655c02ae423b97deb319", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2de917ed887e655c02ae423b97deb319\", \"key\": \"What A Dream\", \"title\": \"What A Dream\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: 210526 Gallery - What A Dream\\nLeaked for free as a bonus in the \\\"Lord I Need You\\\" buy. Samples \\\"Calm Oceans (Loverboys)\\\" by MIND SLUMS. Recorded on May 26th, 2021. This song was misattributed as \\\"Dreaming Of The Past\\\" before any information on that song was known about.\", \"date\": 16410816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a6f8c25d8e8e3556490052bd1abcac3e\", \"url\": \"https://api.pillowcase.su/api/download/a6f8c25d8e8e3556490052bd1abcac3e\", \"size\": \"6.98 MB\", \"duration\": 24.24}", "aliases": [], "size": "6.98 MB"}, {"id": "-97", "name": "??? [V1]", "artists": [], "producers": ["Plain Pat"], "notes": "OG Filename: 210525 Gallery - <PERSON> x <PERSON> 3\nRough Plain Pat freestyle. Vocals were reused on \"Where They At\" by French <PERSON>.", "length": "170.76", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5a6192147f661cb36f2d281c78dbca7f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5a6192147f661cb36f2d281c78dbca7f\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Where They At\"], \"description\": \"OG Filename: 210525 Gallery - Pat x Ye 3\\nRough Plain Pat freestyle. Vocals were reused on \\\"Where They At\\\" by French Montana.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b4292a153980fd289e6448b4f56402f9\", \"url\": \"https://api.pillowcase.su/api/download/b4292a153980fd289e6448b4f56402f9\", \"size\": \"9.32 MB\", \"duration\": 170.76}", "aliases": ["Where They At"], "size": "9.32 MB"}, {"id": "where-dey-at", "name": "Where Dey At [V2]", "artists": [], "producers": ["Dem <PERSON>z", "BoogzDaBeast"], "notes": "OG Filename: WHERE DEY AT (YE_Edit 163.5bpm) ST\n'Solo' version with different punch-ins from Dem Jointz. Stems rebounced in September 2023 to send to French Montana, but the original cutdown is from 2021 as <PERSON>m Jointz wasn't working with <PERSON> during this time. Leaked as a bonus for the second \"THIRSTY\" groupbuy.", "length": "115.46", "fileDate": 17344800, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d146a85273eb90e8e4b0766bdfe14b5e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d146a85273eb90e8e4b0766bdfe14b5e\", \"key\": \"Where Dey At\", \"title\": \"Where Dey At [V2]\", \"artists\": \"(ref. <PERSON><PERSON>z) (prod. Dem Jointz & BoogzDaBeast)\", \"aliases\": [\"Where They At\"], \"description\": \"OG Filename: WHERE DEY AT (YE_Edit 163.5bpm) ST\\n'Solo' version with different punch-ins from Dem Jointz. Stems rebounced in September 2023 to send to French Montana, but the original cutdown is from 2021 as <PERSON><PERSON> Jointz wasn't working with <PERSON> during this time. Leaked as a bonus for the second \\\"THIRSTY\\\" groupbuy.\", \"date\": 17344800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"91829e1b585eaa3e1791b50aafc567c3\", \"url\": \"https://api.pillowcase.su/api/download/91829e1b585eaa3e1791b50aafc567c3\", \"size\": \"2.78 MB\", \"duration\": 115.46}", "aliases": ["Where They At"], "size": "2.78 MB"}, {"id": "-99", "name": "???", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210521 Gallery - OV DT 2\nRough untitled Don Toliver reference track over an Ojivolta piano instrumental.", "length": "284.98", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/93a3d4fb1c06e87ba5860fa2b4d3ac25", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/93a3d4fb1c06e87ba5860fa2b4d3ac25\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: 210521 Gallery - OV DT 2\\nRough untitled Don Toliver reference track over an Ojivolta piano instrumental.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4637d79a34f80fb39ebaad60e84ef10d\", \"url\": \"https://api.pillowcase.su/api/download/4637d79a34f80fb39ebaad60e84ef10d\", \"size\": \"11.1 MB\", \"duration\": 284.98}", "aliases": [], "size": "11.1 MB"}, {"id": "-100", "name": "???", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: 210521 Gallery - OV DT 6\nRough Don Toliver reference track over an Ojivolta piano instrumental.", "length": "284.4", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d6a92fdac441f3d98db8b0e99d95595e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d6a92fdac441f3d98db8b0e99d95595e\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(ref. <PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: 210521 Gallery - OV DT 6\\nRough Don Toliver reference track over an Ojivolta piano instrumental.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"50927c4746ad12b91f7498726b7cd240\", \"url\": \"https://api.pillowcase.su/api/download/50927c4746ad12b91f7498726b7cd240\", \"size\": \"11.1 MB\", \"duration\": 284.4}", "aliases": [], "size": "11.1 MB"}, {"id": "-101", "name": "???", "artists": [], "producers": ["Plain Pat"], "notes": "OG Filename: 210525 Gallery - <PERSON> x <PERSON> 2\nRough Plain Pat freestyle, continues the flow and lyrics from the original \"Hype\" freestyle. None of the vocals on this version were used by Dem Jointz.", "length": "178.32", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8886e80fdac4eaf25b7485438aef47cc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8886e80fdac4eaf25b7485438aef47cc\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210525 Gallery - <PERSON> x Ye 2\\nRough Plain Pat freestyle, continues the flow and lyrics from the original \\\"Hype\\\" freestyle. None of the vocals on this version were used by Dem Jointz.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7cdc07c61859bd9154605b8ab32825dc\", \"url\": \"https://api.pillowcase.su/api/download/7cdc07c61859bd9154605b8ab32825dc\", \"size\": \"9.44 MB\", \"duration\": 178.32}", "aliases": [], "size": "9.44 MB"}, {"id": "superstar-in-the-hood", "name": "Consequence - Superstar In The Hood [V1]", "artists": [], "producers": ["Consequence"], "notes": "Original version of \"Blood Stains\", before it was reworked. A music video was made for this. Samples \"The Look In Your Eyes\" by <PERSON>ze. Snippet was posted to Consequence's Instagram.", "length": "", "fileDate": 16897248, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/269b28e7ea8c21e358aa3478ac71ac84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/269b28e7ea8c21e358aa3478ac71ac84\", \"key\": \"Superstar In The Hood\", \"title\": \"Consequence - Superstar In The Hood [V1]\", \"artists\": \"(prod. Consequence)\", \"aliases\": [\"Blood Stain\", \"Bloodstains\"], \"description\": \"Original version of \\\"Blood Stains\\\", before it was reworked. A music video was made for this. <PERSON><PERSON> \\\"The Look In Your Eyes\\\" by <PERSON><PERSON>. Snippet was posted to Consequence's Instagram.\", \"date\": 16897248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Blood Stain", "Bloodstains"], "size": ""}, {"id": "bloodstains", "name": "Consequence - Bloodstains [V3]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON><PERSON>", "Ojivolta"], "notes": "OG Filename: Bloodstains - 21.04.28 <PERSON><PERSON><PERSON> <PERSON><PERSON> version of \"Bloodstains\" featuring a slower tempo. Is only instrumental.", "length": "118.65", "fileDate": 16548192, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b7929684ab7705669c06e1d3b7cc841c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b7929684ab7705669c06e1d3b7cc841c\", \"key\": \"Bloodstains\", \"title\": \"Consequence - Bloodstains [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Williams & Ojivolta)\", \"aliases\": [\"Blood Stain\", \"Superstar In The Hood\"], \"description\": \"OG Filename: Bloodstains - 21.04.28 <PERSON><PERSON><PERSON>f\\n<PERSON><PERSON><PERSON> version of \\\"Bloodstains\\\" featuring a slower tempo. Is only instrumental.\", \"date\": 16548192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"26bc5ceef49602030408f4084b47bd8d\", \"url\": \"https://api.pillowcase.su/api/download/26bc5ceef49602030408f4084b47bd8d\", \"size\": \"8.49 MB\", \"duration\": 118.65}", "aliases": ["Blood Stain", "Superstar In The Hood"], "size": "8.49 MB"}, {"id": "bloodstains-104", "name": "Consequence - Bloodstains [V4]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON><PERSON>", "Ojivolta"], "notes": "OG Filename: Cons - SSITH Bloodstains_Ye Edit\nVersion of \"Bloodstains\", featuring a faster tempo. Is significantly shorter than the released version.", "length": "125.28", "fileDate": 16548192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f231250431a4f62c51fc7a4c794867f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f231250431a4f62c51fc7a4c794867f4\", \"key\": \"Bloodstains\", \"title\": \"Consequence - Bloodstains [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Williams & Ojivolta)\", \"aliases\": [\"Blood Stain\", \"Superstar In The Hood\"], \"description\": \"OG Filename: Cons - SSITH Bloodstains_Ye Edit\\nVersion of \\\"Bloodstains\\\", featuring a faster tempo. Is significantly shorter than the released version.\", \"date\": 16548192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7b6daf94ca97a596e323690a4d19817a\", \"url\": \"https://api.pillowcase.su/api/download/7b6daf94ca97a596e323690a4d19817a\", \"size\": \"8.59 MB\", \"duration\": 125.28}", "aliases": ["Blood Stain", "Superstar In The Hood"], "size": "8.59 MB"}, {"id": "industry-baby", "name": "Lil Nas X - Industry Baby [V13]", "artists": [], "producers": ["Take A Daytrip", "<PERSON>"], "notes": "OG Filename: industry baby [demo 11]\nDemo 11. Similar to Demo 9, but with better structure. Leaked by Viper months before the song would release.", "length": "136.07", "fileDate": 16690752, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e5754e358a4d83904bc235a82d893efb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e5754e358a4d83904bc235a82d893efb\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V13]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"OG Filename: industry baby [demo 11]\\nDemo 11. Similar to Demo 9, but with better structure. Leaked by Viper months before the song would release.\", \"date\": 16690752, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f0ae095adfd1539764a50bb9512ef19\", \"url\": \"https://api.pillowcase.su/api/download/8f0ae095adfd1539764a50bb9512ef19\", \"size\": \"4.59 MB\", \"duration\": 136.07}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "4.59 MB"}, {"id": "industry-baby-106", "name": "Lil Nas X - Industry Baby [V14]", "artists": [], "producers": ["Take A Daytrip", "<PERSON>"], "notes": "OG Filename: industry baby [demo 12]\nDemo 12 of \"Industry Baby\". Previewed on TikTok when <PERSON> Nas X wanted to show off the OG ending for the song. He would later release an extended mix with this outro because many liked it.", "length": "35.94", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3f57cbbf1ca44bbb84630ad61d85d768", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f57cbbf1ca44bbb84630ad61d85d768\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V14]\", \"artists\": \"(prod. <PERSON> Daytrip & <PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"OG Filename: industry baby [demo 12]\\nDemo 12 of \\\"Industry Baby\\\". Previewed on TikTok when Lil Nas X wanted to show off the OG ending for the song. He would later release an extended mix with this outro because many liked it.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"700bac75a61e22ec3efe85587258ddb6\", \"url\": \"https://api.pillowcase.su/api/download/700bac75a61e22ec3efe85587258ddb6\", \"size\": \"2.94 MB\", \"duration\": 35.94}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "2.94 MB"}, {"id": "we-made-it", "name": "Pop Smoke - We Made It [V20]", "artists": ["2 Chainz", "<PERSON> the Machine", "Kanye West"], "producers": ["BoogzDaBeast", "FnZ", "<PERSON>", "<PERSON>"], "notes": "OG Filename: Pop Smoke - We Made It ft. <PERSON><PERSON><PERSON> - 03.26.21\nVersion of \"Tell The Vision\" featuring <PERSON> <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>. Very similar to the released version, with <PERSON> vocals but no <PERSON><PERSON><PERSON>tains <PERSON> Smoke's \"she sucking dick\" line instead of the \"now it's <PERSON><PERSON>\" line.", "length": "294.22", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6ea5ac12f82fafb074a2d0e67fadf27e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ea5ac12f82fafb074a2d0e67fadf27e\", \"key\": \"We Made It\", \"title\": \"Pop Smoke - We Made It [V20]\", \"artists\": \"(feat. 2 <PERSON><PERSON>, <PERSON> the Machine & Kanye <PERSON>) (prod. <PERSON><PERSON><PERSON>, FnZ, <PERSON> & <PERSON>)\", \"aliases\": [\"RIP Pop Smoke\", \"Television\", \"We Made It\"], \"description\": \"OG Filename: Pop Smoke - We Made It ft. Kanye West - 03.26.21\\nVersion of \\\"Tell The Vision\\\" featuring 2 <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>. Very similar to the released version, with <PERSON> vocals but no <PERSON><PERSON><PERSON>. Maintains Pop Smoke's \\\"she sucking dick\\\" line instead of the \\\"now it's <PERSON><PERSON>\\\" line.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1697ef7ebf0c8d7c6f078d3aa4d2931a\", \"url\": \"https://api.pillowcase.su/api/download/1697ef7ebf0c8d7c6f078d3aa4d2931a\", \"size\": \"11.3 MB\", \"duration\": 294.22}", "aliases": ["RIP Pop Smoke", "Television", "We Made It"], "size": "11.3 MB"}, {"id": "i-pray-for-you", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON> Pray For You [V1]", "artists": ["Kanye West", "Lab<PERSON>th"], "producers": [], "notes": "OG Filename: 210426 Gallery Freestyles pt 2 - Labrinth Ye Ref 1\nFirst bounce of the Ye freestyle for \"I Pray For You\", cutting the song at 40 seconds. Features the same <PERSON><PERSON>th vocals as the release version.", "length": "40.58", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b51b6a849321f6e1c9da1c4b74e11272", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b51b6a849321f6e1c9da1c4b74e11272\", \"key\": \"I Pray For You\", \"title\": \"Pusha T - <PERSON> Pray For You [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Labrinth)\", \"description\": \"OG Filename: 210426 Gallery Freestyles pt 2 - Labrinth Ye Ref 1\\nFirst bounce of the Ye freestyle for \\\"I Pray For You\\\", cutting the song at 40 seconds. Features the same Labrinth vocals as the release version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"04f2490d2f4081993e4469a57c1812ae\", \"url\": \"https://api.pillowcase.su/api/download/04f2490d2f4081993e4469a57c1812ae\", \"size\": \"7.24 MB\", \"duration\": 40.58}", "aliases": [], "size": "7.24 MB"}, {"id": "i-pray-for-you-109", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON> Pray For You [V2]", "artists": ["Kanye West", "Lab<PERSON>th"], "producers": [], "notes": "OG Filename: 210426 Gallery Freestyles pt 2 - <PERSON><PERSON><PERSON> Ye Ref 2_full\nSecond bounce of the Ye freestyle for \"I Pray For You\", this time containing the full song. Features the same <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> vocals as the release version.", "length": "237.24", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a3d0c18596367e8e9b9010070baee74c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a3d0c18596367e8e9b9010070baee74c\", \"key\": \"I Pray For You\", \"title\": \"<PERSON><PERSON><PERSON> T - <PERSON> Pray For You [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Labrinth)\", \"description\": \"OG Filename: 210426 Gallery Freestyles pt 2 - <PERSON>rinth Ye Ref 2_full\\nSecond bounce of the Ye freestyle for \\\"I Pray For You\\\", this time containing the full song. Features the same <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> vocals as the release version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0f914fe8a1d82250042db25fdf99b41b\", \"url\": \"https://api.pillowcase.su/api/download/0f914fe8a1d82250042db25fdf99b41b\", \"size\": \"10.4 MB\", \"duration\": 237.24}", "aliases": [], "size": "10.4 MB"}, {"id": "rock-n-roll", "name": "Pusha T - Rock N Roll [V1]", "artists": ["Kanye West"], "producers": [], "notes": "A Kanye mumble verse can be heard in the vocal bleed of the Don Toliver reference track. Possibly a reference track for Don Toliver, but it may be an early version of his feature. It's unclear.", "length": "6.34", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fd1585c07792097c7e1aa4506b26f33d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fd1585c07792097c7e1aa4506b26f33d\", \"key\": \"Rock N Roll\", \"title\": \"Pusha T - Rock N Roll [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"description\": \"A Kanye mumble verse can be heard in the vocal bleed of the Don Toliver reference track. Possibly a reference track for <PERSON>, but it may be an early version of his feature. It's unclear.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"8ad3cd966b014dd7fbe2fbac174df604\", \"url\": \"https://api.pillowcase.su/api/download/8ad3cd966b014dd7fbe2fbac174df604\", \"size\": \"6.69 MB\", \"duration\": 6.34}", "aliases": [], "size": "6.69 MB"}, {"id": "rock-n-roll-111", "name": "Pusha T - Rock N Roll [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210429 Gallery JW DT - Rock N Roll_DT better-St\nOriginal reference track by <PERSON>. This reference track, with added production, ended up being turned into a song titled \"Livin It Up\".", "length": "143.98", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a9feddd047e61f7b73f0756e07539742", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a9feddd047e61f7b73f0756e07539742\", \"key\": \"Rock N Roll\", \"title\": \"Pusha T - Rock N Roll [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Livin It Up\"], \"description\": \"OG Filename: 210429 Gallery JW DT - Rock N Roll_DT better-St\\nOriginal reference track by <PERSON>. This reference track, with added production, ended up being turned into a song titled \\\"Livin It Up\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"42110b3f92d92cf94f759a6a3ecbade5\", \"url\": \"https://api.pillowcase.su/api/download/42110b3f92d92cf94f759a6a3ecbade5\", \"size\": \"7.74 MB\", \"duration\": 143.98}", "aliases": ["Livin It Up"], "size": "7.74 MB"}, {"id": "rock-n-roll-112", "name": "Pusha T - Rock N Roll [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: kw gallery with jw 3\nRougher, untuned bounce of <PERSON>'s \"Rock N Roll\" reference.", "length": "152.5", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/53f158c00f925c037785ea00dc0faf3b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53f158c00f925c037785ea00dc0faf3b\", \"key\": \"Rock N Roll\", \"title\": \"Pusha T - Rock N Roll [V3]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Livin It Up\"], \"description\": \"OG Filename: kw gallery with jw 3\\nRougher, untuned bounce of <PERSON>'s \\\"Rock N Roll\\\" reference.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c43e1dbeb72f1627c356bf68f83e5c0\", \"url\": \"https://api.pillowcase.su/api/download/6c43e1dbeb72f1627c356bf68f83e5c0\", \"size\": \"9.03 MB\", \"duration\": 152.5}", "aliases": ["Livin It Up"], "size": "9.03 MB"}, {"id": "rock-n-roll-113", "name": "Pusha T - Rock N Roll [V4]", "artists": [], "producers": [], "notes": "OG Filename: Rock N Roll - 21.05.18 RxD_edit 3\nFirst version of the Don Toliver reference track for Rock N Roll with production from <PERSON> & <PERSON>. Has a slightly different vocal arrangment and different production from \"Livin It Up\".", "length": "145.1", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/7f25997f783d94d7a89805bcab42cd82", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7f25997f783d94d7a89805bcab42cd82\", \"key\": \"Rock N Roll\", \"title\": \"Pusha T - Rock N Roll [V4]\", \"aliases\": [\"Livin It Up\"], \"description\": \"OG Filename: Rock N Roll - 21.05.18 RxD_edit 3\\nFirst version of the Don Toliver reference track for Rock N Roll with production from Rico Nichols & Darius <PERSON>. Has a slightly different vocal arrangment and different production from \\\"Livin It Up\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eff0d9ef98212c8f99b162838a1ba751\", \"url\": \"https://api.pillowcase.su/api/download/eff0d9ef98212c8f99b162838a1ba751\", \"size\": \"8.91 MB\", \"duration\": 145.1}", "aliases": ["Livin It Up"], "size": "8.91 MB"}, {"id": "scrape-it-off", "name": "<PERSON>usha T - Scrape It Off", "artists": ["<PERSON>", "Kanye West"], "producers": [], "notes": "Early version of \"Scrape It Off\" with a mumble Kanye verse.", "length": "39.03", "fileDate": 17101152, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/db632a5fdfa77439ad0b44dceb1acc78", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/db632a5fdfa77439ad0b44dceb1acc78\", \"key\": \"Scrape It Off\", \"title\": \"Pusha T - Scrape It Off\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"Early version of \\\"Scrape It Off\\\" with a mumble Kanye verse.\", \"date\": 17101152, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9be050685342eef8d3684cc34f710fe1\", \"url\": \"https://api.pillowcase.su/api/download/9be050685342eef8d3684cc34f710fe1\", \"size\": \"6.9 MB\", \"duration\": 39.03}", "aliases": [], "size": "6.9 MB"}, {"id": "shotta", "name": "<PERSON><PERSON><PERSON> [V2]", "artists": ["Kanye West"], "producers": ["BoogzDaBeast", "Kanye West"], "notes": "OG Filename: 210426 Gallery Freestyles pt 2 - Shotta Ye Ref 1\nRough \"Hear Me Clearly\" verse done by <PERSON> in 2021, features <PERSON><PERSON><PERSON> on the hook. Unknown if this verse was a reference for himself, or <PERSON><PERSON><PERSON>.", "length": "127.92", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/54aa6f267f54f76eb6ed634f50168f69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/54aa6f267f54f76eb6ed634f50168f69\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. BoogzDaBeast & Kanye West)\", \"aliases\": [\"Hear Me Clearly\"], \"description\": \"OG Filename: 210426 Gallery Freestyles pt 2 - <PERSON>ta Ye Ref 1\\nRough \\\"Hear Me Clearly\\\" verse done by <PERSON> in 2021, features <PERSON><PERSON><PERSON> <PERSON> on the hook. Unknown if this verse was a reference for himself, or <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5e26530667e3437b9ba3aec348619ab2\", \"url\": \"https://api.pillowcase.su/api/download/5e26530667e3437b9ba3aec348619ab2\", \"size\": \"8.64 MB\", \"duration\": 127.92}", "aliases": ["Hear Me Clearly"], "size": "8.64 MB"}, {"id": "aye-ye-ye-ye", "name": "<PERSON> Ye Ye Ye [V2]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: <PERSON> <PERSON> Ye - 21.06.25 ojivolta piano_FIXED TUNING 152bpm\nSeen on a June or July tracklist for Donda. Has <PERSON><PERSON> Grizzley reference verses, and added production from Ojivolta.", "length": "154.8", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c010e27a6abb051aa2f6b674365b2058", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c010e27a6abb051aa2f6b674365b2058\", \"key\": \"Aye Ye Ye Ye\", \"title\": \"Aye Ye Ye Ye [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Digital Nas & Ojivolta)\", \"aliases\": [\"<PERSON><PERSON><PERSON><PERSON> Ye Ye Ye\", \"MerryGoRound\"], \"description\": \"OG Filename: Aye Ye Ye Ye - 21.06.25 ojivolta piano_FIXED TUNING 152bpm\\nSeen on a June or July tracklist for Donda. Has Tee Grizzley reference verses, and added production from Ojivolta.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4773e7d2559216a4f6791f72b2419cd8\", \"url\": \"https://api.pillowcase.su/api/download/4773e7d2559216a4f6791f72b2419cd8\", \"size\": \"9.07 MB\", \"duration\": 154.8}", "aliases": ["<PERSON><PERSON><PERSON>y Ye Ye Ye", "MerryGoRound"], "size": "9.07 MB"}, {"id": "believe-what-i-say-117", "name": "Believe What I Say [V17]", "artists": ["<PERSON><PERSON><PERSON>", "STALONE"], "producers": ["E.VAX"], "notes": "OG Filename: believe what i say Evan V.2 - 6.21\nVersion of \"Believe What I Say\" with production from E.VAX. Found in a 2020-2021 copy of <PERSON><PERSON>.", "length": "232.86", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/500b6c220131a981f8e3334a5fcf025e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/500b6c220131a981f8e3334a5fcf025e\", \"key\": \"Believe What I Say\", \"title\": \"Believe What I Say [V17]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. Buju Banton & STALONE) (prod. E.VAX)\", \"description\": \"OG Filename: believe what i say Evan V.2 - 6.21\\nVersion of \\\"Believe What I Say\\\" with production from E.VAX. Found in a 2020-2021 copy of Don<PERSON>.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"52febaab4adb978bd627cb51865723b0\", \"url\": \"https://api.pillowcase.su/api/download/52febaab4adb978bd627cb51865723b0\", \"size\": \"10.3 MB\", \"duration\": 232.86}", "aliases": [], "size": "10.3 MB"}, {"id": "believe-what-i-say-118", "name": "Believe What I Say [V18]", "artists": ["<PERSON><PERSON><PERSON>", "STALONE"], "producers": ["E.VAX"], "notes": "OG Filename: believe what i say stems 1-believe what i say Evan v.3 (from florida to berlin) 7_1\nVersion of \"Believe What I Say\" with production from E.VAX. Has the same <PERSON><PERSON><PERSON> vocals as previous versions, and a very similar instrumental to the <PERSON> reference track.", "length": "232.63", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f1b360b60748bdbaa32663af3fee7e9c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f1b360b60748bdbaa32663af3fee7e9c\", \"key\": \"Believe What I Say\", \"title\": \"Believe What I Say [V18]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. B<PERSON><PERSON> Banton & STALONE) (prod. E.VAX)\", \"description\": \"OG Filename: believe what i say stems 1-believe what i say Evan v.3 (from florida to berlin) 7_1\\nVersion of \\\"Believe What I Say\\\" with production from E.VAX. Has the same Kanye vocals as previous versions, and a very similar instrumental to the <PERSON> reference track.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"353721e3708a397a6733189d27543437\", \"url\": \"https://api.pillowcase.su/api/download/353721e3708a397a6733189d27543437\", \"size\": \"10.3 MB\", \"duration\": 232.63}", "aliases": [], "size": "10.3 MB"}, {"id": "believe-what-i-say-119", "name": "✨ Believe What I Say [V19]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON>", "STALONE", "SAINt JHN"], "producers": ["E.VAX"], "notes": "OG Filename: Believe What I Say SL\nVersion with <PERSON>. Has the SAINt JHN adlib from the version he was on. Original LQ snippet leaked in January 2022, with an HQ snippet leaking January 26th, 2023.", "length": "232.52", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2b85060444ccfadd795dbccd252c159d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b85060444ccfadd795dbccd252c159d\", \"key\": \"Believe What I Say\", \"title\": \"\\u2728 Believe What I Say [V19]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON>, <PERSON><PERSON><PERSON>, STALONE & SAINt JHN) (prod. E.VAX)\", \"description\": \"OG Filename: Believe What I Say SL\\nVersion with <PERSON>. Has the SAINt JHN adlib from the version he was on. Original LQ snippet leaked in January 2022, with an HQ snippet leaking January 26th, 2023.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c82dcd5566d3f4b12a24d0bd0d0a69d7\", \"url\": \"https://api.pillowcase.su/api/download/c82dcd5566d3f4b12a24d0bd0d0a69d7\", \"size\": \"10.3 MB\", \"duration\": 232.52}", "aliases": [], "size": "10.3 MB"}, {"id": "broke-my-own-curse", "name": "Broke My Own Curse", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Broke My Own Curse\nOne of ten piano freestyles. Has singing and some mumble.", "length": "191.64", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/03f26b6defc6058c88abe0d214aa8cf6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03f26b6defc6058c88abe0d214aa8cf6\", \"key\": \"Broke My Own Curse\", \"title\": \"Broke My Own Curse\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Broke My Own Curse\\nOne of ten piano freestyles. Has singing and some mumble.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"60bed2e5cfb1656cb4edc5a1bc387ce8\", \"url\": \"https://api.pillowcase.su/api/download/60bed2e5cfb1656cb4edc5a1bc387ce8\", \"size\": \"9.66 MB\", \"duration\": 191.64}", "aliases": [], "size": "9.66 MB"}, {"id": "christ-is-my-friend", "name": "<PERSON> Is My Friend [V4]", "artists": [], "producers": [], "notes": "<PERSON> and Sunday Service Choir \"Christ Is My Friend\" reference track.", "length": "87", "fileDate": 16480800, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f45dfb2585d9a5bb77d5bcb91545c183", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f45dfb2585d9a5bb77d5bcb91545c183\", \"key\": \"Christ Is My Friend\", \"title\": \"<PERSON> Is My Friend [V4]\", \"artists\": \"(ref. <PERSON> & Sunday Service Choir)\", \"description\": \"<PERSON> and Sunday Service Choir \\\"Christ Is My Friend\\\" reference track.\", \"date\": 16480800, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7b1fb9c344f4b4fc4eac5bd556a2da9b\", \"url\": \"https://api.pillowcase.su/api/download/7b1fb9c344f4b4fc4eac5bd556a2da9b\", \"size\": \"7.98 MB\", \"duration\": 87}", "aliases": [], "size": "7.98 MB"}, {"id": "christ-is-my-friend-122", "name": "<PERSON> Is My Friend [V5]", "artists": [], "producers": [], "notes": "OG Filename: christ is my friend start SL BG\nVersion of \"Christ Is My Friend\" with vocals from <PERSON>. Is sped up.", "length": "76.8", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/221c02c844f35fa9d3ea679a42410bca", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/221c02c844f35fa9d3ea679a42410bca\", \"key\": \"Christ Is My Friend\", \"title\": \"<PERSON> Is My Friend [V5]\", \"artists\": \"(ref. <PERSON>, <PERSON> & Sunday Service Choir)\", \"description\": \"OG Filename: christ is my friend start SL BG\\nVersion of \\\"Christ Is My Friend\\\" with vocals from <PERSON>. Is sped up.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b1962bec9170e784933458fa2873a9ad\", \"url\": \"https://api.pillowcase.su/api/download/b1962bec9170e784933458fa2873a9ad\", \"size\": \"7.82 MB\", \"duration\": 76.8}", "aliases": [], "size": "7.82 MB"}, {"id": "city-of-lost-angels-123", "name": "City Of Lost Angels [V8]", "artists": [], "producers": ["Ojivolta", "Dem <PERSON>z"], "notes": "OG Filename: City Of Lost Angels - 210617 Oji Piano BOUNCE v1\nVersion of \"City Of Lost Angels\" with piano from Ojivolta.", "length": "145.04", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5f5dca9d8635f9f7ac486586bfb2fc02", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f5dca9d8635f9f7ac486586bfb2fc02\", \"key\": \"City Of Lost Angels\", \"title\": \"City Of Lost Angels [V8]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Ojivolta & Dem Jointz)\", \"aliases\": [\"City Of Los Angels\", \"Judgemental\"], \"description\": \"OG Filename: City Of Lost Angels - 210617 Oji Piano BOUNCE v1\\nVersion of \\\"City Of Lost Angels\\\" with piano from Ojivolta.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"80b97d2cab110d2a4a8c2da29130d280\", \"url\": \"https://api.pillowcase.su/api/download/80b97d2cab110d2a4a8c2da29130d280\", \"size\": \"8.91 MB\", \"duration\": 145.04}", "aliases": ["City Of Los Angels", "Judgemental"], "size": "8.91 MB"}, {"id": "city-of-lost-angels-124", "name": "City Of Lost Angels [V9]", "artists": [], "producers": ["Ojivolta", "Dem <PERSON>z", "<PERSON>"], "notes": "OG Filename: city of lost angels ref\nVersion of \"City Of Lost Angels\" likely made in June 2021. Has new drums, and different production from previous versions of the song.", "length": "145.01", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e33d6c3636336c677d84fdad999f0632", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e33d6c3636336c677d84fdad999f0632\", \"key\": \"City Of Lost Angels\", \"title\": \"City Of Lost Angels [V9]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>, <PERSON><PERSON>z & Steve <PERSON>)\", \"aliases\": [\"City Of Los Angels\", \"Judgemental\"], \"description\": \"OG Filename: city of lost angels ref\\nVersion of \\\"City Of Lost Angels\\\" likely made in June 2021. Has new drums, and different production from previous versions of the song.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1a8632b73469ffed23c1f8f18b2176f4\", \"url\": \"https://api.pillowcase.su/api/download/1a8632b73469ffed23c1f8f18b2176f4\", \"size\": \"8.91 MB\", \"duration\": 145.01}", "aliases": ["City Of Los Angels", "Judgemental"], "size": "8.91 MB"}, {"id": "city-of-lost-angels-125", "name": "City Of Lost Angels [V10]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "88-<PERSON>"], "notes": "Version of \"City Of Lost Angels\" that uses parts of <PERSON>'s \"Livin It Up\" reference track, and has <PERSON> backing vocals. Has a differently chopped verse. The vocals from this version are the ones used for the LP1 version of \"Hurricane\". From June 2021. Leaked after a groupbuy.", "length": "184.01", "fileDate": 16664832, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8d83db75c99f28ac1e0687ff5f7c133c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8d83db75c99f28ac1e0687ff5f7c133c\", \"key\": \"City Of Lost Angels\", \"title\": \"City Of Lost Angels [V10]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON> & <PERSON>) (prod. <PERSON> & 88-Keys)\", \"aliases\": [\"City Of Los Angels\", \"Judgemental\", \"Livin It Up\"], \"description\": \"Version of \\\"City Of Lost Angels\\\" that uses parts of <PERSON>'s \\\"Livin It Up\\\" reference track, and has <PERSON> backing vocals. Has a differently chopped verse. The vocals from this version are the ones used for the LP1 version of \\\"Hurricane\\\". From June 2021. Leaked after a groupbuy.\", \"date\": 16664832, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8cbda87d69956de22f87c6dc1fced1e5\", \"url\": \"https://api.pillowcase.su/api/download/8cbda87d69956de22f87c6dc1fced1e5\", \"size\": \"9.53 MB\", \"duration\": 184.01}", "aliases": ["City Of Los Angels", "Judgemental", "Livin It Up"], "size": "9.53 MB"}, {"id": "come-to-life", "name": "Come to Life [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON><PERSON>", "E.VAX", "Ojivolta"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Come To Life\nOne of ten piano freestyles. Version of \"Come To Life\" previewed at a private listening session. Has mostly freestyle singing with long instrumental portions using the released version of the instrumental. Most of the finished track's lines come from this version, and there is cheering/clapping at the end of the freestyle.", "length": "714.95", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/cb230d778c0774f5c866d1eaaee493bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cb230d778c0774f5c866d1eaaee493bc\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V1]\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON>, E.VAX & Ojivolta)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Come To Life\\nOne of ten piano freestyles. Version of \\\"Come To Life\\\" previewed at a private listening session. Has mostly freestyle singing with long instrumental portions using the released version of the instrumental. Most of the finished track's lines come from this version, and there is cheering/clapping at the end of the freestyle.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"223b8238ae734948ed0a3d18d5715957\", \"url\": \"https://api.pillowcase.su/api/download/223b8238ae734948ed0a3d18d5715957\", \"size\": \"18 MB\", \"duration\": 714.95}", "aliases": [], "size": "18 MB"}, {"id": "come-to-life-127", "name": "Come to Life [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: Come To Life Freestyle ojivolta Chop [2021-06-22 134524]\n5 minute cut-down of the 12 minute \"Come To Life\" demo by Ojivolta, from mid June. Leaked alongside stems. Features a roughly cut version of the <PERSON> instrumental.", "length": "306.17", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3ea6fde1ed3c691b1801de77b50480bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ea6fde1ed3c691b1801de77b50480bc\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V2]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"OG Filename: Come To Life Freestyle ojivolta Chop [2021-06-22 134524]\\n5 minute cut-down of the 12 minute \\\"Come To Life\\\" demo by Ojivolta, from mid June. Leaked alongside stems. Features a roughly cut version of the <PERSON> instrumental.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"235d691140b894b5b831ab85aef8aafa\", \"url\": \"https://api.pillowcase.su/api/download/235d691140b894b5b831ab85aef8aafa\", \"size\": \"11.5 MB\", \"duration\": 306.17}", "aliases": [], "size": "11.5 MB"}, {"id": "come-to-life-128", "name": "Come to Life [V3]", "artists": [], "producers": [], "notes": "OG Filename: come to life SL BG\nVersion of \"Come To Life\" with vocals from <PERSON>. Has multiple takes from <PERSON> stacked ontop of each other.", "length": "924.73", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/4299ffff450109c501a2e37521c5e753", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4299ffff450109c501a2e37521c5e753\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V3]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"OG Filename: come to life SL BG\\nVersion of \\\"Come To Life\\\" with vocals from <PERSON>. Has multiple takes from Ye stacked ontop of each other.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"90163172adff33f16a25b4a6f2d5f494\", \"url\": \"https://api.pillowcase.su/api/download/90163172adff33f16a25b4a6f2d5f494\", \"size\": \"21.4 MB\", \"duration\": 924.73}", "aliases": [], "size": "21.4 MB"}, {"id": "come-to-life-129", "name": "Come to Life [V4]", "artists": [], "producers": ["Ojivolta"], "notes": "Video of Digital Nas listening to Ojivolta's Come to Life during Mexico sessions. Has rerecorded piano and guitars added. No official bounce exists, but the stem for the redone piano leaked alongside the initial freestyle chop. Very close to the early July versions of the song.", "length": "", "fileDate": 16955136, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/ddd286631b068d4cb07e9d1590436e9e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ddd286631b068d4cb07e9d1590436e9e\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V4]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"Video of Digital Nas listening to Ojivolta's Come to Life during Mexico sessions. Has rerecorded piano and guitars added. No official bounce exists, but the stem for the redone piano leaked alongside the initial freestyle chop. Very close to the early July versions of the song.\", \"date\": 16955136, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "come-to-life-130", "name": "Come to Life [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "Version of \"Come To Life\" with <PERSON><PERSON><PERSON> doing extra vocals.", "length": "41.95", "fileDate": 16440192, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/bc0c696040ffd06a50652a3d63195267", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bc0c696040ffd06a50652a3d63195267\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"Version of \\\"Come To Life\\\" with <PERSON><PERSON><PERSON> doing extra vocals.\", \"date\": 16440192, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7b7223654943c0c1e95db0e72c552e4b\", \"url\": \"https://api.pillowcase.su/api/download/7b7223654943c0c1e95db0e72c552e4b\", \"size\": \"7.26 MB\", \"duration\": 41.95}", "aliases": [], "size": "7.26 MB"}, {"id": "come-to-life-131", "name": "Come to Life [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "Version of \"Come To Life\" with <PERSON><PERSON><PERSON> doing extra vocals.", "length": "8.72", "fileDate": 16440192, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d067268b3dd13c3d187e04443ba997f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d067268b3dd13c3d187e04443ba997f8\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"Version of \\\"Come To Life\\\" with <PERSON><PERSON><PERSON> doing extra vocals.\", \"date\": 16440192, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"96503430b03040afb0ca63a20848b6f0\", \"url\": \"https://api.pillowcase.su/api/download/96503430b03040afb0ca63a20848b6f0\", \"size\": \"6.73 MB\", \"duration\": 8.72}", "aliases": [], "size": "6.73 MB"}, {"id": "come-to-life-132", "name": "Come to Life [V7]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: Come To Life LW Choir Full Mix 1 0630\nVersion of \"Come To Life\", featuring the chopped up freestyle over a vocal instrumental done by <PERSON>. The filename \"LW\" refers to <PERSON>, the engineer for the recording session.", "length": "305.23", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/de20bc489506c7faed95363cb9cbed69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/de20bc489506c7faed95363cb9cbed69\", \"key\": \"Come to Life\", \"title\": \"Come to Life [V7]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: Come To Life LW Choir Full Mix 1 0630\\nVersion of \\\"Come To Life\\\", featuring the chopped up freestyle over a vocal instrumental done by <PERSON>. The filename \\\"LW\\\" refers to <PERSON>, the engineer for the recording session.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"46f10b783826b6f6a5b808ce6be9bf5e\", \"url\": \"https://api.pillowcase.su/api/download/46f10b783826b6f6a5b808ce6be9bf5e\", \"size\": \"11.5 MB\", \"duration\": 305.23}", "aliases": [], "size": "11.5 MB"}, {"id": "cut-loose", "name": "✨ Cut Loose", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - <PERSON> Loose\nOne of ten piano freestyles. Made in the same session as the first version of \"Come To Life\".", "length": "423.01", "fileDate": 16929216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/0d6cdbbaf931f6f29f1d3c6655b6fe37", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d6cdbbaf931f6f29f1d3c6655b6fe37\", \"key\": \"Cut Loose\", \"title\": \"\\u2728 Cut Loose\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Cut Loose\\nOne of ten piano freestyles. Made in the same session as the first version of \\\"Come To Life\\\".\", \"date\": 16929216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a5dc8bad7e99a23825a474c3532d848b\", \"url\": \"https://api.pillowcase.su/api/download/a5dc8bad7e99a23825a474c3532d848b\", \"size\": \"13.4 MB\", \"duration\": 423.01}", "aliases": [], "size": "13.4 MB"}, {"id": "daylight", "name": "Daylight [V3]", "artists": ["Vory"], "producers": ["88-<PERSON>", "Ojivolta", "E.VAX"], "notes": "OG Filename: Daylight - OxV - 21.06.30 Vory_Edit\nFeatures one extra Ka<PERSON><PERSON> line, down on later versions. Has <PERSON>ory background vocals, and alternate mixing. This song was the reward for the highest payer in the \"Fighting Fires\" GB.", "length": "205.3", "fileDate": 16385760, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/541227b0ccfe2b80629f94672e1f7035", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/541227b0ccfe2b80629f94672e1f7035\", \"key\": \"Daylight\", \"title\": \"Daylight [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. 88-<PERSON>, Ojivolta & E.VAX)\", \"aliases\": [\"Day Light\"], \"description\": \"OG Filename: Daylight - OxV - 21.06.30 Vory_Edit\\nFeatures one extra Kanye line, down on later versions. Has <PERSON>ory background vocals, and alternate mixing. This song was the reward for the highest payer in the \\\"Fighting Fires\\\" GB.\", \"date\": 16385760, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b40754b95102e5ad88caf4875dac94f7\", \"url\": \"https://api.pillowcase.su/api/download/b40754b95102e5ad88caf4875dac94f7\", \"size\": \"9.87 MB\", \"duration\": 205.3}", "aliases": ["Day Light"], "size": "9.87 MB"}, {"id": "dreaming-of-the-past", "name": "Dreaming of the Past [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Dreaming of thePast - 21.07.04 Chop 70bpm\nFeatured on a leaked Donda tracklist, and the filename was shown by Waterfalls. Given to <PERSON><PERSON><PERSON> for his record It's Almost Dry. Apparently just an instrumental, however, <PERSON><PERSON><PERSON> said \"[<PERSON><PERSON><PERSON>] hadn't written to it in so long\", implying he had recorded at some point. Slower BPM than other versions.", "length": "132.05", "fileDate": 16961184, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/81aea135602241f49fd0159699633e0b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/81aea135602241f49fd0159699633e0b\", \"key\": \"Dreaming of the Past\", \"title\": \"Dreaming of the Past [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Dreamin Of The Past\"], \"description\": \"OG Filename: Dreaming of thePast - 21.07.04 Chop 70bpm\\nFeatured on a leaked Donda tracklist, and the filename was shown by Waterfalls. Given to <PERSON><PERSON><PERSON> for his record It's Almost Dry. Apparently just an instrumental, however, <PERSON><PERSON><PERSON> said \\\"[<PERSON><PERSON><PERSON>] hadn't written to it in so long\\\", implying he had recorded at some point. Slower BPM than other versions.\", \"date\": 16961184, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd03eceb4f2012fdf1bde235a7dd5bae\", \"url\": \"https://api.pillowcase.su/api/download/bd03eceb4f2012fdf1bde235a7dd5bae\", \"size\": \"8.7 MB\", \"duration\": 132.05}", "aliases": ["Dreamin Of The Past"], "size": "8.7 MB"}, {"id": "eternal-life", "name": "Eternal Life [V16]", "artists": [], "producers": [], "notes": "Reference track for \"Eternal Life\" by <PERSON><PERSON>, <PERSON><PERSON>'s daughter. Recorded at the St. Regis hotel sessions. Snippet was posted to Al<PERSON>'s Instagram Story in June 2023.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2152c51c18d0ed4b02b8c87b5fd631f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2152c51c18d0ed4b02b8c87b5fd631f5\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V16]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"Reference track for \\\"Eternal Life\\\" by <PERSON><PERSON>, <PERSON><PERSON>'s daughter. Recorded at the St. Regis hotel sessions. Snippet was posted to Albe's Instagram Story in June 2023.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": ""}, {"id": "eternal-life-137", "name": "Eternal Life [V18]", "artists": [], "producers": ["BoogzDaBeast", "Digital Nas"], "notes": "OG Filename: ETERNAL LIFE (Master)\nVersion of \"Eternal Life\" with new production by Digital Nas.", "length": "159.28", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e7ef28ca5cf3561718563614850897f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7ef28ca5cf3561718563614850897f5\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V18]\", \"artists\": \"(ref. <PERSON> Do<PERSON> & Pusha T) (prod. BoogzDaBeast & Digital Nas)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"OG Filename: ETERNAL LIFE (Master)\\nVersion of \\\"Eternal Life\\\" with new production by Digital Nas.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1793175ae9aa181c8d8e199b6b0a26e2\", \"url\": \"https://api.pillowcase.su/api/download/1793175ae9aa181c8d8e199b6b0a26e2\", \"size\": \"9.14 MB\", \"duration\": 159.28}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "9.14 MB"}, {"id": "fairytale-138", "name": "Fairytale [V5]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: fairytale ref drum \nVersion with additional production from <PERSON>.", "length": "191.42", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/796c64b139f04be6bde04efae241e489", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/796c64b139f04be6bde04efae241e489\", \"key\": \"Fairytale\", \"title\": \"Fairytale [V5]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: fairytale ref drum \\nVersion with additional production from <PERSON>.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"047c3542159cb47e35450d8e5c2ca321\", \"url\": \"https://api.pillowcase.su/api/download/047c3542159cb47e35450d8e5c2ca321\", \"size\": \"9.65 MB\", \"duration\": 191.42}", "aliases": [], "size": "9.65 MB"}, {"id": "find-a-way", "name": "Find A Way", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Find A Way\nOne of ten piano freestyle. Made in the same session as the first version of \"Come To Life\".", "length": "615.49", "fileDate": 16554240, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/ab0901cff93efab1a74381ad29ad5199", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab0901cff93efab1a74381ad29ad5199\", \"key\": \"Find A Way\", \"title\": \"Find A Way\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Find A Way\\nOne of ten piano freestyle. Made in the same session as the first version of \\\"Come To Life\\\".\", \"date\": 16554240, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9bbf021c6f944a116a9357a987261a8b\", \"url\": \"https://api.pillowcase.su/api/download/9bbf021c6f944a116a9357a987261a8b\", \"size\": \"16.4 MB\", \"duration\": 615.49}", "aliases": [], "size": "16.4 MB"}, {"id": "all-these-friends", "name": "All These Friends [V4]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: kanye all these friends digital nas\nEarly structured version with minimal production over the base Ojivolta beat.", "length": "214.83", "fileDate": 16963776, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/33f8febf8bbf075eac17fae84c30c9d3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/33f8febf8bbf075eac17fae84c30c9d3\", \"key\": \"All These Friends\", \"title\": \"All These Friends [V4]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Friends\"], \"description\": \"OG Filename: kanye all these friends digital nas\\nEarly structured version with minimal production over the base Ojivolta beat.\", \"date\": 16963776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b84335f34fe43d100d3ba6673a222939\", \"url\": \"https://api.pillowcase.su/api/download/b84335f34fe43d100d3ba6673a222939\", \"size\": \"10 MB\", \"duration\": 214.83}", "aliases": ["Friends"], "size": "10 MB"}, {"id": "all-these-friends-of-mine-141", "name": "⭐ All These Friends of Mine [V5]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: All These Friends of Mine - OxV - 21.05.30 Vocal Restructure\nCut-down version from late May. Seen on tracklists throughout 2021. Combines the production from the two prior freestyles for the song. Originally leaked with a fake filedate of 21.11.30", "length": "123.48", "fileDate": 16943040, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f4b722b267219133377dca37374d1ce4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f4b722b267219133377dca37374d1ce4\", \"key\": \"All These Friends of Mine\", \"title\": \"\\u2b50 All These Friends of Mine [V5]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Friends\"], \"description\": \"OG Filename: All These Friends of Mine - OxV - 21.05.30 Vocal Restructure\\nCut-down version from late May. Seen on tracklists throughout 2021. Combines the production from the two prior freestyles for the song. Originally leaked with a fake filedate of 21.11.30\", \"date\": 16943040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9043973760ce1069c702269f58c60e45\", \"url\": \"https://api.pillowcase.su/api/download/9043973760ce1069c702269f58c60e45\", \"size\": \"8.57 MB\", \"duration\": 123.48}", "aliases": ["Friends"], "size": "8.57 MB"}, {"id": "friends", "name": "Friends [V6]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: kanye friends (Master)\nVersion of \"Friends\" with different Digital Nas drums.", "length": "123.74", "fileDate": 16963776, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a25c34f6d26464da1442f4c7c1f6c246", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a25c34f6d26464da1442f4c7c1f6c246\", \"key\": \"Friends\", \"title\": \"Friends [V6]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"All These Friends of Mine\"], \"description\": \"OG Filename: kanye friends (Master)\\nVersion of \\\"Friends\\\" with different Digital Nas drums.\", \"date\": 16963776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c01b739e00759f79e086d29abee84afa\", \"url\": \"https://api.pillowcase.su/api/download/c01b739e00759f79e086d29abee84afa\", \"size\": \"8.57 MB\", \"duration\": 123.74}", "aliases": ["All These Friends of Mine"], "size": "8.57 MB"}, {"id": "friends-143", "name": "Friends [V8]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: Friends - OxV - 21.06.12 Acca Update & no hats/clps\nNewer version of \"Friends\" with some different vocal takes being used. All drums from the song are removed.", "length": "123.48", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9fb17ebe2af493e8f040460b14d6773f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9fb17ebe2af493e8f040460b14d6773f\", \"key\": \"Friends\", \"title\": \"Friends [V8]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"All These Friends Of Mine\"], \"description\": \"OG Filename: Friends - OxV - 21.06.12 Acca Update & no hats/clps\\nNewer version of \\\"Friends\\\" with some different vocal takes being used. All drums from the song are removed.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"28fc2d8ce607111a67fb83732ed9fc58\", \"url\": \"https://api.pillowcase.su/api/download/28fc2d8ce607111a67fb83732ed9fc58\", \"size\": \"8.57 MB\", \"duration\": 123.48}", "aliases": ["All These Friends Of Mine"], "size": "8.57 MB"}, {"id": "friends-144", "name": "Friends [V9]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: kanye friends digital nas version1000\nLater version of \"Friends\". Has really bad vocal mixing.", "length": "123.74", "fileDate": 17101152, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/28ba992975a3076c46befda9648abab7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/28ba992975a3076c46befda9648abab7\", \"key\": \"Friends\", \"title\": \"Friends [V9]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"All These Friends Of Mine\"], \"description\": \"OG Filename: kanye friends digital nas version1000\\nLater version of \\\"Friends\\\". Has really bad vocal mixing.\", \"date\": 17101152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"217194857dfb5a2f61e800ec83f6b0c5\", \"url\": \"https://api.pillowcase.su/api/download/217194857dfb5a2f61e800ec83f6b0c5\", \"size\": \"8.57 MB\", \"duration\": 123.74}", "aliases": ["All These Friends Of Mine"], "size": "8.57 MB"}, {"id": "all-these-friends-of-mine-145", "name": "All These Friends Of Mine [V10]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: All These Friends of Mine OxV No Snare Perc 140bpm v.1\nVersion of \"Friends\" with only kick drum and some extra synth layers added. Has some re-recorded no mumble vocal takes added. Unknown when it's dated. Has really bad mixing.", "length": "123.43", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d9067c5d157d80e9a16737bf0daca0b1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9067c5d157d80e9a16737bf0daca0b1\", \"key\": \"All These Friends Of Mine\", \"title\": \"All These Friends Of Mine [V10]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Friends\"], \"description\": \"OG Filename: All These Friends of Mine OxV No Snare Perc 140bpm v.1\\nVersion of \\\"Friends\\\" with only kick drum and some extra synth layers added. Has some re-recorded no mumble vocal takes added. Unknown when it's dated. Has really bad mixing.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6dc3f0e33658336c09382c3b7cadcf24\", \"url\": \"https://api.pillowcase.su/api/download/6dc3f0e33658336c09382c3b7cadcf24\", \"size\": \"8.57 MB\", \"duration\": 123.43}", "aliases": ["Friends"], "size": "8.57 MB"}, {"id": "all-these-friends-of-mine-146", "name": "All These Friends Of Mine [V11]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: All These Friends of Mine CAILIN\nVersion of \"Friends\" with additional Digital Nas production and rearranged vocals. Contains vocals from <PERSON> Nas on the hook. The filename mentions <PERSON><PERSON><PERSON>, however she has no clear contributions to this version of the song, so <PERSON> Nas' vocals could be a reference for her.", "length": "138", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d6ca032cc86d9c4cbabbfa9069256bce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d6ca032cc86d9c4cbabbfa9069256bce\", \"key\": \"All These Friends Of Mine\", \"title\": \"All These Friends Of Mine [V11]\", \"artists\": \"(ref. <PERSON>) (prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Friends\"], \"description\": \"OG Filename: All These Friends of Mine CAILIN\\nVersion of \\\"Friends\\\" with additional Digital Nas production and rearranged vocals. Contains vocals from <PERSON> Nas on the hook. The filename mentions <PERSON><PERSON><PERSON>, however she has no clear contributions to this version of the song, so <PERSON> Nas' vocals could be a reference for her.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b35abb5140fab93c8a2e330924b6c243\", \"url\": \"https://api.pillowcase.su/api/download/b35abb5140fab93c8a2e330924b6c243\", \"size\": \"8.8 MB\", \"duration\": 138}", "aliases": ["Friends"], "size": "8.8 MB"}, {"id": "find-out", "name": "Find Out [V3]", "artists": [], "producers": ["BoogzDaBeast", "Dem <PERSON>z", "Ojivolta"], "notes": "Samples \"High Up\" by half·alive. <PERSON><PERSON><PERSON> references T-Pain, specifically his viral feature on the DJ <PERSON><PERSON><PERSON> song \"All I Do Is Win\". Includes De<PERSON>z punch-ins and weird autotuned vocal warping.", "length": "267.2", "fileDate": 16531776, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/927776b1b221c248f8e1cd4ee46f3d9f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/927776b1b221c248f8e1cd4ee46f3d9f\", \"key\": \"Find Out\", \"title\": \"Find Out [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Dem Jointz & Ojivolta)\", \"description\": \"Samples \\\"High Up\\\" by half\\u00b7alive. <PERSON><PERSON><PERSON> references T<PERSON><PERSON>, specifically his viral feature on the DJ <PERSON><PERSON><PERSON> song \\\"All I Do Is Win\\\". Includes Dem Jointz punch-ins and weird autotuned vocal warping.\", \"date\": 16531776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0fca882c82a8dd29e19dd3e931dbc427\", \"url\": \"https://api.pillowcase.su/api/download/0fca882c82a8dd29e19dd3e931dbc427\", \"size\": \"10.9 MB\", \"duration\": 267.2}", "aliases": [], "size": "10.9 MB"}, {"id": "i-know-god-breathed-on-this-148", "name": "I Know God Breathed On This [V27]", "artists": ["Vory", "<PERSON>"], "producers": ["E.VAX", "Ojivolta"], "notes": "OG Filename: IKGBOT Evan new update - 210629 - Vory\nFirst version which adds Vory. Found in a 2020-2021 copy of <PERSON><PERSON>.", "length": "181.01", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8e1f73709db04cbd9b6e529966ea1b24", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8e1f73709db04cbd9b6e529966ea1b24\", \"key\": \"I Know God Breathed On This\", \"title\": \"I Know God Breathed On This [V27]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. E.VAX & Ojivolta)\", \"aliases\": [\"Breathed On This\", \"God Breathed\", \"God Breathed On This\", \"I Know God\", \"I Know He Got His Hands On This\"], \"description\": \"OG Filename: IKGBOT Evan new update - 210629 - Vory\\nFirst version which adds <PERSON>ory. Found in a 2020-2021 copy of <PERSON><PERSON>.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9e785f9261e3f5381f04bc9a9f9317df\", \"url\": \"https://api.pillowcase.su/api/download/9e785f9261e3f5381f04bc9a9f9317df\", \"size\": \"9.49 MB\", \"duration\": 181.01}", "aliases": ["Breathed On This", "God Breathed", "God Breathed On This", "I Know God", "I Know He Got His Hands On This"], "size": "9.49 MB"}, {"id": "god-s-got-me", "name": "<PERSON>'s Got Me [V3]", "artists": [], "producers": ["88-<PERSON>", "Ojivolta"], "notes": "OG Filename: Gods God Me - 210613 - Cons Caiden...\nConsequence and Caiden reference track, from June 2021. Con<PERSON><PERSON> does a short verse, while his son <PERSON><PERSON><PERSON>, does the hook and his own verse. In leaked handwritten notes, <PERSON><PERSON><PERSON> wrote \"second verse as Adult\". The instrumental of this version has different drums, outro, and chop of the sample. Original VC recorded snippet leaked in May 2022, later snippets were played on Consequence's live stream November 23rd, 2022, and a CDQ snippet leaked March 12th, 2023.", "length": "180.17", "fileDate": 17001792, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a81cfc444d562a2b23f900b7fb542236", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a81cfc444d562a2b23f900b7fb542236\", \"key\": \"<PERSON>'s Got Me\", \"title\": \"<PERSON>'s Got Me [V3]\", \"artists\": \"(ref. Consequence & Caiden The Crownholder) (prod. 88-Keys & Ojivolta)\", \"aliases\": [\"God Got Me\"], \"description\": \"OG Filename: Gods God Me - 210613 - Con<PERSON>aiden...\\nConsequence and Caiden reference track, from June 2021. Conse<PERSON> does a short verse, while his son <PERSON><PERSON><PERSON>, does the hook and his own verse. In leaked handwritten notes, <PERSON><PERSON><PERSON> wrote \\\"second verse as Adult\\\". The instrumental of this version has different drums, outro, and chop of the sample. Original VC recorded snippet leaked in May 2022, later snippets were played on Consequence's live stream November 23rd, 2022, and a CDQ snippet leaked March 12th, 2023.\", \"date\": 17001792, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"709064391426a5f9629be949698cc638\", \"url\": \"https://api.pillowcase.su/api/download/709064391426a5f9629be949698cc638\", \"size\": \"9.47 MB\", \"duration\": 180.17}", "aliases": ["God Got Me"], "size": "9.47 MB"}, {"id": "go-left", "name": "Go Left [V2]", "artists": [], "producers": ["Dem <PERSON>z", "BoogzDaBeast"], "notes": "Originally described as being similar to \"old Ye.\" Has <PERSON><PERSON> punch-ins. Leaked with stems after a groupbuy.", "length": "124.78", "fileDate": 16464384, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2d3af27941f086511385dc88d6f7d3b3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d3af27941f086511385dc88d6f7d3b3\", \"key\": \"Go Left\", \"title\": \"Go Left [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Dem Jointz & BoogzDaBeast)\", \"aliases\": [\"Clout\"], \"description\": \"Originally described as being similar to \\\"old Ye.\\\" Has Dem Jointz punch-ins. Leaked with stems after a groupbuy.\", \"date\": 16464384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"aeb80c134b584f2e202679d8504a1c35\", \"url\": \"https://api.pillowcase.su/api/download/aeb80c134b584f2e202679d8504a1c35\", \"size\": \"8.59 MB\", \"duration\": 124.78}", "aliases": ["<PERSON><PERSON><PERSON>"], "size": "8.59 MB"}, {"id": "clout", "name": "<PERSON><PERSON><PERSON> [V3]", "artists": [], "producers": ["Dem <PERSON>z", "BoogzDaBeast"], "notes": "OG Filename: Cons - Clout Ref 21.06.13\nConsequence reference for \"Go Left\", being renamed to \"Clout\".", "length": "171.55", "fileDate": 16468704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/053cdf8292e20729a422de26b93a2840", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/053cdf8292e20729a422de26b93a2840\", \"key\": \"Clout\", \"title\": \"<PERSON><PERSON><PERSON> [V3]\", \"artists\": \"(ref. Consequence) (prod. Dem Jointz & BoogzDaBeast)\", \"aliases\": [\"Go Left\"], \"description\": \"OG Filename: Cons - Clout Ref 21.06.13\\nConsequence reference for \\\"Go Left\\\", being renamed to \\\"Clout\\\".\", \"date\": 16468704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c128e610418a169acb00d09a64aefa3\", \"url\": \"https://api.pillowcase.su/api/download/6c128e610418a169acb00d09a64aefa3\", \"size\": \"9.34 MB\", \"duration\": 171.55}", "aliases": ["Go Left"], "size": "9.34 MB"}, {"id": "heaven-and-hell-152", "name": "Heaven and Hell [V3]", "artists": [], "producers": ["88-<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: Heaven And Hell - 21.05.29 End Clean ADMIX.1\nHas the new vocals mixed in with the old. Found in a 2020-2021 copy of <PERSON><PERSON>.", "length": "147.47", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8f9ff4983f82533e209bd018f0b0c8fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8f9ff4983f82533e209bd018f0b0c8fb\", \"key\": \"Heaven and Hell\", \"title\": \"Heaven and Hell [V3]\", \"artists\": \"(prod. 88-<PERSON> & BoogzDaBeast)\", \"description\": \"OG Filename: Heaven And Hell - 21.05.29 End Clean ADMIX.1\\nHas the new vocals mixed in with the old. Found in a 2020-2021 copy of <PERSON><PERSON>.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"28911509cab7fd810c90fc1a63cc5070\", \"url\": \"https://api.pillowcase.su/api/download/28911509cab7fd810c90fc1a63cc5070\", \"size\": \"8.95 MB\", \"duration\": 147.47}", "aliases": [], "size": "8.95 MB"}, {"id": "-153", "name": "??? [V4]", "artists": ["Symba"], "producers": ["Dem <PERSON>z"], "notes": "Version with a completely different beat from later versions. Likely just a one-off Dem Jointz version of the song, as it features punch ins from him and that <PERSON><PERSON><PERSON>, the feature on this track, has connections with <PERSON><PERSON>. Likely not titled \"Heaven And Hell\", as namesake sample is missing from the track.", "length": "127.76", "fileDate": 16540416, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3a674596bbc6f11de534c8ab5fad4849", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a674596bbc6f11de534c8ab5fad4849\", \"key\": \"???\", \"title\": \"??? [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>z)\", \"aliases\": [\"Heaven and Hell\"], \"description\": \"Version with a completely different beat from later versions. Likely just a one-off Dem Jointz version of the song, as it features punch ins from him and that <PERSON><PERSON><PERSON>, the feature on this track, has connections with <PERSON><PERSON>. Likely not titled \\\"Heaven And Hell\\\", as namesake sample is missing from the track.\", \"date\": 16540416, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"50582b4aeecc4e5a6964832ce322edd2\", \"url\": \"https://api.pillowcase.su/api/download/50582b4aeecc4e5a6964832ce322edd2\", \"size\": \"8.63 MB\", \"duration\": 127.76}", "aliases": ["Heaven and Hell"], "size": "8.63 MB"}, {"id": "-154", "name": "??? [V4]", "artists": ["Symba"], "producers": ["Dem <PERSON>z"], "notes": "Version with a completely different beat from later versions. Likely just a one-off Dem Jointz version of the song, as it features punch ins from him and that <PERSON><PERSON><PERSON>, the feature on this track, has connections with <PERSON><PERSON>. Likely not titled \"Heaven And Hell\", as namesake sample is missing from the track.", "length": "", "fileDate": 16540416, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2f5cfb0088808c6ef8f150dd04453e9e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2f5cfb0088808c6ef8f150dd04453e9e\", \"key\": \"???\", \"title\": \"??? [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>z)\", \"aliases\": [\"Heaven and Hell\"], \"description\": \"Version with a completely different beat from later versions. Likely just a one-off Dem Jointz version of the song, as it features punch ins from him and that <PERSON><PERSON><PERSON>, the feature on this track, has connections with <PERSON><PERSON>. Likely not titled \\\"Heaven And Hell\\\", as namesake sample is missing from the track.\", \"date\": 16540416, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Heaven and Hell"], "size": ""}, {"id": "hiiii-wyd", "name": "⭐ HIIII WYD [V2]", "artists": ["STALONE"], "producers": ["BoogzDaBeast", "Dem <PERSON>z"], "notes": "Original version of \"New Again\" with a different instrumental that bleeds through on the final version. May version of the song. Includes the verse played at the first Donda listening party, in addition to 35 seconds of new vocals. Snippet leaked from dbree. Samples \"As 1\" by Map<PERSON>, and \"I'll Say It Again\" by <PERSON>. Features very faint vocals from STALONE. Leaked after a groupbuy.", "length": "127.93", "fileDate": 16956000, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a61779b06b3d42313d7c4a8edea7f362", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a61779b06b3d42313d7c4a8edea7f362\", \"key\": \"HIIII WYD\", \"title\": \"\\u2b50 HIIII WYD [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. STALON<PERSON>) (prod. BoogzDaBeast & Dem Jointz)\", \"aliases\": [\"New Again\"], \"description\": \"Original version of \\\"New Again\\\" with a different instrumental that bleeds through on the final version. May version of the song. Includes the verse played at the first Donda listening party, in addition to 35 seconds of new vocals. Snippet leaked from dbree. <PERSON><PERSON> \\\"As 1\\\" by Mapei, and \\\"I'll Say It Again\\\" by <PERSON>. Features very faint vocals from <PERSON><PERSON><PERSON><PERSON>. Leaked after a groupbuy.\", \"date\": 16956000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"504b06a45a20ecd9e0814e283632a363\", \"url\": \"https://api.pillowcase.su/api/download/504b06a45a20ecd9e0814e283632a363\", \"size\": \"8.64 MB\", \"duration\": 127.93}", "aliases": ["New Again"], "size": "8.64 MB"}, {"id": "hold-me", "name": "Hold Me", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Hold Me\nOne of ten piano freestyles. Made in the same session as the first version of \"Come To Life\".", "length": "391.26", "fileDate": 16929216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/ff767491d81c317459729101f5c29709", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ff767491d81c317459729101f5c29709\", \"key\": \"Hold Me\", \"title\": \"Hold Me\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Hold Me\\nOne of ten piano freestyles. Made in the same session as the first version of \\\"Come To Life\\\".\", \"date\": 16929216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cd235c5f9adfb1af9283e4220f2f5e6c\", \"url\": \"https://api.pillowcase.su/api/download/cd235c5f9adfb1af9283e4220f2f5e6c\", \"size\": \"12.9 MB\", \"duration\": 391.26}", "aliases": [], "size": "12.9 MB"}, {"id": "how-could-you", "name": "How Could You [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: How Could You wip ojivolta\nSecond version of \"Never Abandon Your Family\" titled \"How Could You\". Seen on June 2021 tracklists. Described as \"a voice memo with loud ahh synths\".", "length": "15.23", "fileDate": 16854048, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3dda861eb87083d88c7960f7d279c1f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3dda861eb87083d88c7960f7d279c1f2\", \"key\": \"How Could You\", \"title\": \"How Could You [V2]\", \"artists\": \"(prod. Ojivolta)\", \"aliases\": [\"Never Abandon Your Family\"], \"description\": \"OG Filename: How Could You wip ojivolta\\nSecond version of \\\"Never Abandon Your Family\\\" titled \\\"How Could You\\\". Seen on June 2021 tracklists. Described as \\\"a voice memo with loud ahh synths\\\".\", \"date\": 16854048, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"13120b01c8a39016de156cf4431d8d8c\", \"url\": \"https://api.pillowcase.su/api/download/13120b01c8a39016de156cf4431d8d8c\", \"size\": \"6.83 MB\", \"duration\": 15.23}", "aliases": ["Never Abandon Your Family"], "size": "6.83 MB"}, {"id": "how-could-you-158", "name": "How Could You [V3]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "A version of \"How Could You\" with Digital Nas production. The drums stem leaked in full in January 2023.", "length": "4.05", "fileDate": 16725312, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d23f602bdafb4bd082db8e71e65199be", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d23f602bdafb4bd082db8e71e65199be\", \"key\": \"How Could You\", \"title\": \"How Could You [V3]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Never Abandon Your Family\"], \"description\": \"A version of \\\"How Could You\\\" with Digital Nas production. The drums stem leaked in full in January 2023.\", \"date\": 16725312, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c1b2178f762c1bf47dc81aa6dc0385c\", \"url\": \"https://api.pillowcase.su/api/download/6c1b2178f762c1bf47dc81aa6dc0385c\", \"size\": \"6.66 MB\", \"duration\": 4.05}", "aliases": ["Never Abandon Your Family"], "size": "6.66 MB"}, {"id": "how-could-you-159", "name": "How Could You [V3]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "A version of \"How Could You\" with Digital Nas production. The drums stem leaked in full in January 2023.", "length": "173.28", "fileDate": 16725312, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a366bbe4e1542a34385f85c3cdbbcf55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a366bbe4e1542a34385f85c3cdbbcf55\", \"key\": \"How Could You\", \"title\": \"How Could You [V3]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Never Abandon Your Family\"], \"description\": \"A version of \\\"How Could You\\\" with Digital Nas production. The drums stem leaked in full in January 2023.\", \"date\": 16725312, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d7e2609891325ddaa6510d78287e6171\", \"url\": \"https://api.pillowcase.su/api/download/d7e2609891325ddaa6510d78287e6171\", \"size\": \"9.36 MB\", \"duration\": 173.28}", "aliases": ["Never Abandon Your Family"], "size": "9.36 MB"}, {"id": "hurricane", "name": "Hurricane [V44]", "artists": ["<PERSON>", "Kay<PERSON>y<PERSON>"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J", "???"], "notes": "Version of \"Hurricane\" from June 2021 that has a different instrumental which features the OG sample pitched down. Possibly features <PERSON><PERSON><PERSON><PERSON> on the chorus but this is unconfirmed.", "length": "8.62", "fileDate": 16612128, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/4ad4c044955e352933ee85a53bf6333d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4ad4c044955e352933ee85a53bf6333d\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V44]\", \"artists\": \"(ref. ???) (feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, R<PERSON><PERSON><PERSON> J & ???)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Version of \\\"Hurricane\\\" from June 2021 that has a different instrumental which features the OG sample pitched down. Possibly features STALONE on the chorus but this is unconfirmed.\", \"date\": 16612128, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a2f7b56d52bf72d8ce17a17bd37c1714\", \"url\": \"https://api.pillowcase.su/api/download/a2f7b56d52bf72d8ce17a17bd37c1714\", \"size\": \"6.73 MB\", \"duration\": 8.62}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "6.73 MB"}, {"id": "hybrid", "name": "✨ Hybrid [V2]", "artists": [], "producers": ["Dem <PERSON>z"], "notes": "A song described to sound like \"an 80's song\". Has also been described as a \"House\" / \"Synth Pop\" song. According to Pacifist, it contains the following lyrics: \"Asked the best milf what she had for lunch, better put her breast milk in my cap n crunch\". Described by a community member as \"Find Out if it was faster pace\" and as a \"Blinding Lights type song.\" Contains punch-ins.", "length": "161.86", "fileDate": 16956000, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3d401eb4721742ca74545a7a4bb3c4eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3d401eb4721742ca74545a7a4bb3c4eb\", \"key\": \"Hybrid\", \"title\": \"\\u2728 Hybrid [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>m <PERSON>z)\", \"description\": \"A song described to sound like \\\"an 80's song\\\". Has also been described as a \\\"House\\\" / \\\"Synth Pop\\\" song. According to Pacifist, it contains the following lyrics: \\\"Asked the best milf what she had for lunch, better put her breast milk in my cap n crunch\\\". Described by a community member as \\\"Find Out if it was faster pace\\\" and as a \\\"Blinding Lights type song.\\\" Contains punch-ins.\", \"date\": 16956000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6297acf39959105e100f565892f3e56b\", \"url\": \"https://api.pillowcase.su/api/download/6297acf39959105e100f565892f3e56b\", \"size\": \"9.18 MB\", \"duration\": 161.86}", "aliases": [], "size": "9.18 MB"}, {"id": "hype", "name": "Hype [V2]", "artists": [], "producers": ["Dem <PERSON>z", "Plain Pat"], "notes": "A completely finished song that was originally described as sounding like \"Facts\", but evil. Contains a lot of punch ins. Leaked after a groupbuy.", "length": "159.47", "fileDate": 16468704, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5723c791a53d65aa3d136c39fdcd9c29", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5723c791a53d65aa3d136c39fdcd9c29\", \"key\": \"Hype\", \"title\": \"Hype [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Dem Jointz & Plain Pat)\", \"description\": \"A completely finished song that was originally described as sounding like \\\"Facts\\\", but evil. Contains a lot of punch ins. Leaked after a groupbuy.\", \"date\": 16468704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c9bb3b17a2063eef30b8c55dcb14fe21\", \"url\": \"https://api.pillowcase.su/api/download/c9bb3b17a2063eef30b8c55dcb14fe21\", \"size\": \"9.14 MB\", \"duration\": 159.47}", "aliases": [], "size": "9.14 MB"}, {"id": "i-let-go", "name": "I Let Go [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: I Let Go - 21.05.27 Short Ref for Ye\nFirst cutdown of the \"On His Head\" freestyles to create \"I Let Go\".", "length": "82.34", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/23b6e12176a9b02a9ccd2f35d247c5ba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23b6e12176a9b02a9ccd2f35d247c5ba\", \"key\": \"I Let Go\", \"title\": \"I Let Go [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas)\", \"description\": \"OG Filename: I Let Go - 21.05.27 Short Ref for Ye\\nFirst cutdown of the \\\"On His Head\\\" freestyles to create \\\"I Let Go\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"44b380ca29657bd335b599112e7a3b03\", \"url\": \"https://api.pillowcase.su/api/download/44b380ca29657bd335b599112e7a3b03\", \"size\": \"7.91 MB\", \"duration\": 82.34}", "aliases": [], "size": "7.91 MB"}, {"id": "let-go", "name": "Let Go [V4]", "artists": [], "producers": ["Digital Nas"], "notes": "Version which features a slightly different mumble <PERSON><PERSON><PERSON> take. It is unknown what <PERSON><PERSON><PERSON>'s contributions to this song were at the time. The recording was made on June 1, 2021.", "length": "", "fileDate": 16287264, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://www.youtube.com/watch?v=6OgxvV599Rw", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=6OgxvV599Rw\", \"key\": \"Let Go\", \"title\": \"Let Go [V4]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"I Let Go\"], \"description\": \"Version which features a slightly different mumble Ka<PERSON><PERSON> take. It is unknown what <PERSON><PERSON><PERSON>'s contributions to this song were at the time. The recording was made on June 1, 2021.\", \"date\": 16287264, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["I Let Go"], "size": ""}, {"id": "if-i-let-go", "name": "If I Let Go [V6]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: If I Let Go\nFrom June 2021. Has different mixing.", "length": "173.37", "fileDate": 16612992, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d352d41cf9b458f3adb943bfb4e870bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d352d41cf9b458f3adb943bfb4e870bc\", \"key\": \"If I Let Go\", \"title\": \"If I Let Go [V6]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Let Go\", \"I Let Go\"], \"description\": \"OG Filename: If I Let Go\\nFrom June 2021. Has different mixing.\", \"date\": 16612992, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bb039fa2b432af8e7d8b21fb4077f2e7\", \"url\": \"https://api.pillowcase.su/api/download/bb039fa2b432af8e7d8b21fb4077f2e7\", \"size\": \"9.36 MB\", \"duration\": 173.37}", "aliases": ["Let Go", "I Let Go"], "size": "9.36 MB"}, {"id": "i-let-go-166", "name": "I Let Go [V7]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: I Let Go 1f\nTy Dolla $ign reference track for \"I Let Go\", contains about a minute of Ty Dolla $ign vocals alongside <PERSON>'s.", "length": "174.58", "fileDate": 16670880, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9dfeb4a2ffcde5269ed4554fb2d6e876", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9dfeb4a2ffcde5269ed4554fb2d6e876\", \"key\": \"I Let Go\", \"title\": \"I Let Go [V7]\", \"artists\": \"(ref. <PERSON> $ign) (prod. Digital Nas & Ojivolta)\", \"description\": \"OG Filename: I Let Go 1f\\nTy Dolla $ign reference track for \\\"I Let Go\\\", contains about a minute of Ty <PERSON> $ign vocals alongside <PERSON>'s.\", \"date\": 16670880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"34f12216c30fe6fabf8b5995de8f4a3b\", \"url\": \"https://api.pillowcase.su/api/download/34f12216c30fe6fabf8b5995de8f4a3b\", \"size\": \"9.38 MB\", \"duration\": 174.58}", "aliases": [], "size": "9.38 MB"}, {"id": "i-let-go-167", "name": "I Let Go [V9]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: I Let Go - 21.06.22 Ref Match_Trim End\nFound in a 2020-2021 copy of Donda. Has different delay effects.", "length": "169.23", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/df0da85fb8647b99117807f37cb7a1cc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/df0da85fb8647b99117807f37cb7a1cc\", \"key\": \"I Let Go\", \"title\": \"I Let Go [V9]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"description\": \"OG Filename: I Let Go - 21.06.22 Ref Match_Trim End\\nFound in a 2020-2021 copy of Donda. Has different delay effects.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6a8e3930917e72a8cc9225b64becf14b\", \"url\": \"https://api.pillowcase.su/api/download/6a8e3930917e72a8cc9225b64becf14b\", \"size\": \"9.3 MB\", \"duration\": 169.23}", "aliases": [], "size": "9.3 MB"}, {"id": "i-wish-you-the-best", "name": "I Wish You The Best", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - I Wish You The Best\nOne of ten piano freestyle from June. Made in the same session as the first version of \"Come To Life\".", "length": "157.62", "fileDate": 16929216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5cdb647f95d81da77890273c4648e0da", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5cdb647f95d81da77890273c4648e0da\", \"key\": \"I Wish You The Best\", \"title\": \"I Wish You The Best\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - I Wish You The Best\\nOne of ten piano freestyle from June. Made in the same session as the first version of \\\"Come To Life\\\".\", \"date\": 16929216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0ff173141f17c68df29ffb00616f39be\", \"url\": \"https://api.pillowcase.su/api/download/0ff173141f17c68df29ffb00616f39be\", \"size\": \"9.11 MB\", \"duration\": 157.62}", "aliases": [], "size": "9.11 MB"}, {"id": "idea-2-freestyle", "name": "Idea 2 Freestyle", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Idea 2 Freestyle\nOne of ten piano freestyles. Made in the same session as the first version of \"Come To Life\".", "length": "168.96", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/0c6780d8b06d9c5c0cc03c69ae427998", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0c6780d8b06d9c5c0cc03c69ae427998\", \"key\": \"Idea 2 Freestyle\", \"title\": \"Idea 2 Freestyle\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Idea 2 Freestyle\\nOne of ten piano freestyles. Made in the same session as the first version of \\\"Come To Life\\\".\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c35edbfb462500f9ecde2f50e247c4fb\", \"url\": \"https://api.pillowcase.su/api/download/c35edbfb462500f9ecde2f50e247c4fb\", \"size\": \"9.29 MB\", \"duration\": 168.96}", "aliases": [], "size": "9.29 MB"}, {"id": "jail", "name": "Jail [V4]", "artists": [], "producers": ["<PERSON>", "Dem <PERSON>z"], "notes": "Version of \"Jail\" featuring very different production from the released version, as well as alternate lyrics.", "length": "174.73", "fileDate": 16656192, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b2a0889b7e339abfcb721df1999d8375", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b2a0889b7e339abfcb721df1999d8375\", \"key\": \"Jail\", \"title\": \"Jail [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON> & Dem Jointz)\", \"aliases\": [\"Goin To Jail Tonight\", \"Goin 2 Jail\", \"88 Idea 3\"], \"description\": \"Version of \\\"Jail\\\" featuring very different production from the released version, as well as alternate lyrics.\", \"date\": 16656192, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"715fe25b886ca2c045d07a3625682d60\", \"url\": \"https://api.pillowcase.su/api/download/715fe25b886ca2c045d07a3625682d60\", \"size\": \"9.39 MB\", \"duration\": 174.73}", "aliases": ["Goin To Jail Tonight", "Goin 2 Jail", "88 Idea 3"], "size": "9.39 MB"}, {"id": "jail-171", "name": "Jail [V5]", "artists": [], "producers": ["<PERSON>", "Dem <PERSON>z"], "notes": "OG Filename: Jail - 21.05.31 Rename_muted line\nVersion of \"Jail\" found in a 2020-2021 copy of Don<PERSON>.", "length": "176.79", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/32e58d075a28562852d8eb08cb81a5ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/32e58d075a28562852d8eb08cb81a5ff\", \"key\": \"Jail\", \"title\": \"Jail [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON> & Dem Jointz)\", \"aliases\": [\"Goin To Jail Tonight\", \"Goin 2 Jail\", \"88 Idea 3\"], \"description\": \"OG Filename: Jail - 21.05.31 Rename_muted line\\nVersion of \\\"Jail\\\" found in a 2020-2021 copy of <PERSON><PERSON>.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7697671a198e9b8892e7439cd13d9da3\", \"url\": \"https://api.pillowcase.su/api/download/7697671a198e9b8892e7439cd13d9da3\", \"size\": \"9.42 MB\", \"duration\": 176.79}", "aliases": ["Goin To Jail Tonight", "Goin 2 Jail", "88 Idea 3"], "size": "9.42 MB"}, {"id": "jam-freestyle", "name": "Jam Freestyle", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Jam Freestyle\nOne of ten piano freestyles. Made in the same session as the first version of \"Come To Life\". Vocals start at 7:12.", "length": "476.61", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5f8d7f638ae1ea230263b9847e0e78b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f8d7f638ae1ea230263b9847e0e78b6\", \"key\": \"Jam Freestyle\", \"title\": \"Jam Freestyle\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Jam Freestyle\\nOne of ten piano freestyles. Made in the same session as the first version of \\\"Come To Life\\\". Vocals start at 7:12.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5092c56d16b9ff1d8f696ccc6076c72b\", \"url\": \"https://api.pillowcase.su/api/download/5092c56d16b9ff1d8f696ccc6076c72b\", \"size\": \"14.2 MB\", \"duration\": 476.61}", "aliases": [], "size": "14.2 MB"}, {"id": "jesus-lord", "name": "Jesus Lord [V1]", "artists": ["Swizz Beatz"], "producers": [], "notes": "OG Filename: <PERSON>wizz - Jesus Lord_temp loop\nContains Swizz Beatz vocals. and a basic instrumental that was fully changed by release.", "length": "187.37", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/333282f7d7589a0085b80bf0b3350404", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/333282f7d7589a0085b80bf0b3350404\", \"key\": \"Jesus Lord\", \"title\": \"Jesus Lord [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) prod. Swizz <PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>wizz - <PERSON> Lord_temp loop\\nContains Swizz Beatz vocals. and a basic instrumental that was fully changed by release.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1544ff9db3ae61f2d2fa1dff19f82ef1\", \"url\": \"https://api.pillowcase.su/api/download/1544ff9db3ae61f2d2fa1dff19f82ef1\", \"size\": \"9.59 MB\", \"duration\": 187.37}", "aliases": [], "size": "9.59 MB"}, {"id": "jr-wa<PERSON><PERSON>", "name": "<PERSON> [V2]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON> - 21.05.27 Edit 1\nFirst cutdown of the \"Godspeed\" freestyle into \"<PERSON>ya\". Whoever bounced the file mistook <PERSON> saying \"<PERSON><PERSON>\" as being \"junior\".", "length": "155.4", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6f97709605612deec0a79bd4f1cc10bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f97709605612deec0a79bd4f1cc10bc\", \"key\": \"<PERSON> Watanabe\", \"title\": \"<PERSON> Watanabe [V2]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: Jr Wantanabe - 21.05.27 Edit 1\\nFirst cutdown of the \\\"Godspeed\\\" freestyle into \\\"<PERSON>ya\\\". Whoever bounced the file mistook Ye saying \\\"Junya\\\" as being \\\"junior\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"435aed4ffac990c03a086fde5cbe5abf\", \"url\": \"https://api.pillowcase.su/api/download/435aed4ffac990c03a086fde5cbe5abf\", \"size\": \"9.08 MB\", \"duration\": 155.4}", "aliases": ["<PERSON><PERSON>"], "size": "9.08 MB"}, {"id": "junya-wa<PERSON>be", "name": "<PERSON><PERSON> [V3]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON> - 21.06.03 Ty$ V1 ref\nTy recorded his reference for \"<PERSON><PERSON>\", which would later be reused for his verse on \"Junya pt 2\". The reference is on the original beat.", "length": "155.29", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3401de34fefd4a9567117f47450e66c1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3401de34fefd4a9567117f47450e66c1\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V3]\", \"artists\": \"(ref. <PERSON> $ign) (prod. Digital Nas)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: <PERSON><PERSON> wantanabe - 21.06.03 Ty$ V1 ref\\nTy recorded his reference for \\\"<PERSON>ya\\\", which would later be reused for his verse on \\\"<PERSON>ya pt 2\\\". The reference is on the original beat.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6cd8f8f18b90f3f48094d50cbbd33411\", \"url\": \"https://api.pillowcase.su/api/download/6cd8f8f18b90f3f48094d50cbbd33411\", \"size\": \"9.08 MB\", \"duration\": 155.29}", "aliases": ["<PERSON><PERSON>"], "size": "9.08 MB"}, {"id": "junya", "name": "<PERSON><PERSON> [V4]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: junya 4 (Master)\nOriginal version of \"<PERSON>ya\" that features different production and mumble <PERSON><PERSON><PERSON> vocals.", "length": "155.75", "fileDate": 16334784, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/ddf8b69d3aa35eac82aa61c2c674bdc4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ddf8b69d3aa35eac82aa61c2c674bdc4\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V4]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"description\": \"OG Filename: junya 4 (Master)\\nOriginal version of \\\"Junya\\\" that features different production and mumble Kanye vocals.\", \"date\": 16334784, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"17c1db96929030fde1d087226b0abc47\", \"url\": \"https://api.pillowcase.su/api/download/17c1db96929030fde1d087226b0abc47\", \"size\": \"9.08 MB\", \"duration\": 155.75}", "aliases": [], "size": "9.08 MB"}, {"id": "junya-watanabe-177", "name": "<PERSON><PERSON> [V5]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON> v.1 Digital Nas Drums OxV Synths\nAlternate version of \"<PERSON><PERSON>\". Similar to previous version except it features different drums.", "length": "155.29", "fileDate": 16334784, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d52d9f2a8b47fe6d184434272bff238b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d52d9f2a8b47fe6d184434272bff238b\", \"key\": \"<PERSON><PERSON>ata<PERSON>\", \"title\": \"<PERSON><PERSON> [V5]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: Junya Watanabe v.1 Digital Nas Drums OxV Synths\\nAlternate version of \\\"Junya\\\". Similar to previous version except it features different drums.\", \"date\": 16334784, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"67529744f0da1a2d9f08c985d9d598e7\", \"url\": \"https://api.pillowcase.su/api/download/67529744f0da1a2d9f08c985d9d598e7\", \"size\": \"9.08 MB\", \"duration\": 155.29}", "aliases": ["<PERSON><PERSON>"], "size": "9.08 MB"}, {"id": "junya-watanabe-178", "name": "✨ <PERSON><PERSON> [V6]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON> v.5 OxV 148bpm\nAlternate versio. Similar to previous version except it features a different melody and faster BPM.", "length": "102.17", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/1ce710b2541a979297cb301ea39d9656", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ce710b2541a979297cb301ea39d9656\", \"key\": \"<PERSON><PERSON> Watana<PERSON>\", \"title\": \"\\u2728 <PERSON><PERSON> Watanabe [V6]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: Junya Watanabe v.5 OxV 148bpm\\nAlternate versio. Similar to previous version except it features a different melody and faster BPM.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"deac2c3b94a14a8a080a8010964c82c5\", \"url\": \"https://api.pillowcase.su/api/download/deac2c3b94a14a8a080a8010964c82c5\", \"size\": \"8.22 MB\", \"duration\": 102.17}", "aliases": ["<PERSON><PERSON>"], "size": "8.22 MB"}, {"id": "junya-watanabe-179", "name": "<PERSON><PERSON> [V7]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON> - 21.06.08 Malik_Vocal Up\n<PERSON> reference for <PERSON><PERSON> leaked from early Donda July copy.", "length": "98.88", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6e8731f23c7d1ab79869c4436afc4f68", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e8731f23c7d1ab79869c4436afc4f68\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V7]\", \"artists\": \"(ref. <PERSON>) (prod. Digital Nas)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: <PERSON><PERSON>be - 21.06.08 <PERSON>_Vocal Up\\nMalik <PERSON> reference for <PERSON><PERSON> leaked from early Donda July copy.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"298168890e54884b0582e5d3adcb91f9\", \"url\": \"https://api.pillowcase.su/api/download/298168890e54884b0582e5d3adcb91f9\", \"size\": \"8.17 MB\", \"duration\": 98.88}", "aliases": ["<PERSON><PERSON>"], "size": "8.17 MB"}, {"id": "junya-180", "name": "<PERSON><PERSON> [V8]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON> - 21.06.11 Ref Match\nCutdown Kanye version of <PERSON><PERSON> over the original Digital Nas beat. Found in early July Donda copy.", "length": "98.88", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fa21ff63201e99b095354cae78e5939a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa21ff63201e99b095354cae78e5939a\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V8]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"<PERSON><PERSON> Watana<PERSON>\"], \"description\": \"OG Filename: Junya - 21.06.11 Ref Match\\nCutdown Kanye version of Junya over the original Digital Nas beat. Found in early July Donda copy.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8e4e893701b2e787c66e932c36f7274f\", \"url\": \"https://api.pillowcase.su/api/download/8e4e893701b2e787c66e932c36f7274f\", \"size\": \"8.17 MB\", \"duration\": 98.88}", "aliases": ["<PERSON><PERSON>"], "size": "8.17 MB"}, {"id": "junya-181", "name": "<PERSON><PERSON> [V9]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: Junya - 210618 - T Grizzley - KV\nTee Grizzley's reference for <PERSON><PERSON> over a new Ojivolta beat. Found in early July Donda copy.", "length": "106.49", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a83fb9c0302da04ec1fc9f1d02c9585d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a83fb9c0302da04ec1fc9f1d02c9585d\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V9]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Ojivolta)\", \"aliases\": [\"<PERSON>ya Watanabe\"], \"description\": \"OG Filename: Junya - 210618 - T Grizzley - KV\\nTee Grizzley's reference for <PERSON><PERSON> over a new Ojivolta beat. Found in early July Donda copy.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"22b49c37a4fc836eff76533209184e4d\", \"url\": \"https://api.pillowcase.su/api/download/22b49c37a4fc836eff76533209184e4d\", \"size\": \"8.29 MB\", \"duration\": 106.49}", "aliases": ["<PERSON><PERSON>"], "size": "8.29 MB"}, {"id": "junya-182", "name": "<PERSON><PERSON> [V10]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: <PERSON><PERSON> - OxV 148 - 21.06.23 Ref Match_voc edit\n<PERSON><PERSON><PERSON> cutdown vocals over the newer Ojivolta beat. Found in early July Donda copy.", "length": "111.96", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8ada1792850c20133254d8ab56878f93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ada1792850c20133254d8ab56878f93\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V10]\", \"artists\": \"(prod. Ojivolta)\", \"aliases\": [\"<PERSON><PERSON> Watana<PERSON>\"], \"description\": \"OG Filename: Junya - OxV 148 - 21.06.23 Ref Match_voc edit\\nKanye cutdown vocals over the newer Ojivolta beat. Found in early July Donda copy.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fe60a7a19ad1a724d720b7c4994dd15a\", \"url\": \"https://api.pillowcase.su/api/download/fe60a7a19ad1a724d720b7c4994dd15a\", \"size\": \"8.38 MB\", \"duration\": 111.96}", "aliases": ["<PERSON><PERSON>"], "size": "8.38 MB"}, {"id": "livin-it-up-183", "name": "Livin It Up [V2]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "notes": "OG Filename: Livin It Up - 21.06.02 Don <PERSON>ver\nHas better mixing. Found in a 2020-2021 copy of <PERSON><PERSON>.", "length": "144.72", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fc54a4c3618a6a5216fdd6640765ce34", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fc54a4c3618a6a5216fdd6640765ce34\", \"key\": \"Livin It Up\", \"title\": \"Livin It Up [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: Livin It Up - 21.06.02 <PERSON>\\nHas better mixing. Found in a 2020-2021 copy of <PERSON><PERSON>.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5d8216a61acadc4550d67c06fbd4d040\", \"url\": \"https://api.pillowcase.su/api/download/5d8216a61acadc4550d67c06fbd4d040\", \"size\": \"8.91 MB\", \"duration\": 144.72}", "aliases": [], "size": "8.91 MB"}, {"id": "livin-it-up-184", "name": "Livin It Up [V3]", "artists": ["Sunday Service Choir"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "notes": "OG Filename: Livin It Up - 21.06.04f\nVersion of Livin It Up with rewritten religious reference vocals done by an unknown artist. Has a more a developed instrumental from the previous Don Toliver reference, with additional organ, piano, RxD drums, and Sunday Service Choir vocals throughout.", "length": "224.45", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/065c2323e8867210ab560d518a15b63e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/065c2323e8867210ab560d518a15b63e\", \"key\": \"Livin It Up\", \"title\": \"Livin It Up [V3]\", \"artists\": \"(ref. ???) (feat. Sunday Service Choir) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: Livin It Up - 21.06.04f\\nVersion of Livin It Up with rewritten religious reference vocals done by an unknown artist. Has a more a developed instrumental from the previous Don Toliver reference, with additional organ, piano, RxD drums, and Sunday Service Choir vocals throughout.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"43582ebd690e1d144eb91ffa0793d7aa\", \"url\": \"https://api.pillowcase.su/api/download/43582ebd690e1d144eb91ffa0793d7aa\", \"size\": \"10.2 MB\", \"duration\": 224.45}", "aliases": [], "size": "10.2 MB"}, {"id": "miracles", "name": "Miracles", "artists": [], "producers": ["Digital Nas"], "notes": "Mexico-era freestyle. Previewed in a Digital Nas vlog released by urnotsellingye, with the instrumental leaking after the contents of his MacBook were posted in YZYCORD. Described by Digital Nas to be his favorite freestyle that <PERSON> did over his beats. A cutdown version could exist as <PERSON> Nas is seen in the video asking an engineer to start working on \"Miracles\".", "length": "350.33", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/2b048d52f2a28f5cc1d11db6f8bdd410", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b048d52f2a28f5cc1d11db6f8bdd410\", \"key\": \"Miracles\", \"title\": \"Miracles\", \"artists\": \"(prod. Digital Nas)\", \"description\": \"Mexico-era freestyle. Previewed in a Digital Nas vlog released by <PERSON><PERSON><PERSON><PERSON><PERSON>, with the instrumental leaking after the contents of his MacBook were posted in YZYCORD. Described by <PERSON> Nas to be his favorite freestyle that <PERSON> did over his beats. A cutdown version could exist as Digital Nas is seen in the video asking an engineer to start working on \\\"Miracles\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f98aa2ba1fd943d931855571fbba8adf\", \"url\": \"https://api.pillowcase.su/api/download/f98aa2ba1fd943d931855571fbba8adf\", \"size\": \"9.39 MB\", \"duration\": 350.33}", "aliases": [], "size": "9.39 MB"}, {"id": "miracles-186", "name": "Miracles", "artists": [], "producers": ["Digital Nas"], "notes": "Mexico-era freestyle. Previewed in a Digital Nas vlog released by urnotsellingye, with the instrumental leaking after the contents of his MacBook were posted in YZYCORD. Described by Digital Nas to be his favorite freestyle that <PERSON> did over his beats. A cutdown version could exist as <PERSON> Nas is seen in the video asking an engineer to start working on \"Miracles\".", "length": "284.54", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3562b2eacc75cc295268e5257bd94cec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3562b2eacc75cc295268e5257bd94cec\", \"key\": \"Miracles\", \"title\": \"Miracles\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"Mexico-era freestyle. Previewed in a Digital Nas vlog released by <PERSON><PERSON><PERSON><PERSON><PERSON>, with the instrumental leaking after the contents of his MacBook were posted in YZYCORD. Described by <PERSON> Nas to be his favorite freestyle that <PERSON> did over his beats. A cutdown version could exist as Digital Nas is seen in the video asking an engineer to start working on \\\"Miracles\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ba7b80776ee3b348eda904bb77572601\", \"url\": \"https://api.pillowcase.su/api/download/ba7b80776ee3b348eda904bb77572601\", \"size\": \"11.1 MB\", \"duration\": 284.54}", "aliases": [], "size": "11.1 MB"}, {"id": "new", "name": "New [V3]", "artists": [], "producers": ["Ojivolta", "Dem <PERSON>z", "BoogzDaBeast"], "notes": "OG Filename: New - 210618 Edit First Two Lines Out\nJune version of \"New Again\". Features production similar to the later versions, but the same vocals as \"HIIII WYD\". Unknown if the STALONE vocals are still present on this version.", "length": "13.42", "fileDate": 16730496, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/7dcf497e724b465ea1a576da1e7454af", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7dcf497e724b465ea1a576da1e7454af\", \"key\": \"New\", \"title\": \"New [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Ojivolta, Dem Jointz & BoogzDaBeast)\", \"aliases\": [\"HIIII WYD\"], \"description\": \"OG Filename: New - 210618 Edit First Two Lines Out\\nJune version of \\\"New Again\\\". Features production similar to the later versions, but the same vocals as \\\"HIIII WYD\\\". Unknown if the STALONE vocals are still present on this version.\", \"date\": 16730496, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"abbdff2344e5e7bd77a6a251f4da359d\", \"url\": \"https://api.pillowcase.su/api/download/abbdff2344e5e7bd77a6a251f4da359d\", \"size\": \"6.8 MB\", \"duration\": 13.42}", "aliases": ["HIIII WYD"], "size": "6.8 MB"}, {"id": "new-188", "name": "New [V5]", "artists": [], "producers": [], "notes": "OG Filename: New - 21.07.01 Basic Elements_\nVersion played at the Donda listening party in Las Vegas. Has both verses from LP2.", "length": "141.36", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/dc2fff3c44be93e70a0951692b027445", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc2fff3c44be93e70a0951692b027445\", \"key\": \"New\", \"title\": \"New [V5]\", \"aliases\": [\"New Again\"], \"description\": \"OG Filename: New - 21.07.01 Basic Elements_\\nVersion played at the Donda listening party in Las Vegas. Has both verses from LP2.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ad8fda2365531a31b950a94e714fd72b\", \"url\": \"https://api.pillowcase.su/api/download/ad8fda2365531a31b950a94e714fd72b\", \"size\": \"8.85 MB\", \"duration\": 141.36}", "aliases": ["New Again"], "size": "8.85 MB"}, {"id": "piano-freestyle", "name": "Piano Freestyle", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - Piano Freestyle\nOne of ten piano freestyles. Made in the same session as the first version of \"Come To Life\". Has no vocals.", "length": "502.25", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6b3dae1d655137949f42edd12aa60c32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6b3dae1d655137949f42edd12aa60c32\", \"key\": \"Piano Freestyle\", \"title\": \"Piano Freestyle\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - Piano Freestyle\\nOne of ten piano freestyles. Made in the same session as the first version of \\\"Come To Life\\\". Has no vocals.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ed417585f6f706b152aad070c1c96b47\", \"url\": \"https://api.pillowcase.su/api/download/ed417585f6f706b152aad070c1c96b47\", \"size\": \"14.6 MB\", \"duration\": 502.25}", "aliases": [], "size": "14.6 MB"}, {"id": "praise-god", "name": "Praise God [V25]", "artists": ["<PERSON>"], "producers": ["30 Roc", "Zentachi", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON>se <PERSON> - 210605 - Shorter_Fix Chords\nPlayed at a listening party in Las Vegas, before the Baby Keem reference track. Version of \"Praise God\" featuring different production, <PERSON><PERSON><PERSON>'s reference track and a feature from <PERSON>. Beginning of snippet featuring <PERSON><PERSON><PERSON> <PERSON> was played during a listening party on accident.", "length": "112.64", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/0df05cacb0292671b1fbf20b674164dd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0df05cacb0292671b1fbf20b674164dd\", \"key\": \"Praise God\", \"title\": \"Praise <PERSON> [V25]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. 30 Roc, Zentachi & Ojivolta)\", \"aliases\": [\"Praise\"], \"description\": \"OG Filename: Praise God - 210605 - Shorter_Fix Chords\\nPlayed at a listening party in Las Vegas, before the Baby Keem reference track. Version of \\\"Praise God\\\" featuring different production, <PERSON><PERSON><PERSON>'s reference track and a feature from <PERSON>. Beginning of snippet featuring <PERSON><PERSON><PERSON> was played during a listening party on accident.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1dbde566eb8cf36d163b41f0cc67c8ae\", \"url\": \"https://api.pillowcase.su/api/download/1dbde566eb8cf36d163b41f0cc67c8ae\", \"size\": \"8.39 MB\", \"duration\": 112.64}", "aliases": ["<PERSON>raise"], "size": "8.39 MB"}, {"id": "praise-god-191", "name": "Praise God [V26]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["30 Roc", "Zentachi", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - 210618 - <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Baby <PERSON>\nProbably first bounce with <PERSON>. Has really quiet snare in the mix.", "length": "238.03", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/779620dca3d3e4312659b7b70457bd56", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/779620dca3d3e4312659b7b70457bd56\", \"key\": \"Praise <PERSON>\", \"title\": \"Praise <PERSON> [V26]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON> & <PERSON>) (feat. <PERSON>, <PERSON>) (prod. 30 Roc, Zentachi & Ojivolta)\", \"aliases\": [\"Praise\"], \"description\": \"OG Filename: Praise God - 210618 - Trav <PERSON>usha <PERSON>\\nProbably first bounce with <PERSON>. Has really quiet snare in the mix.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3c92cd5637c641a8fc05b103d30557ee\", \"url\": \"https://api.pillowcase.su/api/download/3c92cd5637c641a8fc05b103d30557ee\", \"size\": \"10.4 MB\", \"duration\": 238.03}", "aliases": ["<PERSON>raise"], "size": "10.4 MB"}, {"id": "praise-god-192", "name": "Praise God [V27]", "artists": ["<PERSON>"], "producers": ["30 Roc", "Zentachi", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - 210618 - <PERSON>rav <PERSON><PERSON><PERSON> <PERSON> intro trim\nSecond bounce with <PERSON>, intro has been trimmed and snare is removed completely. Very close to Las Vegas version, but the sample is arranged slightly differently.", "length": "235.68", "fileDate": 16961184, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/cbc7c629ac7034030c81f8cf3e926bc7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cbc7c629ac7034030c81f8cf3e926bc7\", \"key\": \"Praise <PERSON>\", \"title\": \"Praise <PERSON> [V27]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON> & <PERSON>) (feat. <PERSON>) (prod. 30 Roc, Zentachi & Ojivolta)\", \"aliases\": [\"Praise\"], \"description\": \"OG Filename: Praise God - 210618 - Trav <PERSON>usha Baby Keem intro trim\\nSecond bounce with <PERSON> Keem, intro has been trimmed and snare is removed completely. Very close to Las Vegas version, but the sample is arranged slightly differently.\", \"date\": 16961184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c10c3a058d69cecac24e99d0c3254680\", \"url\": \"https://api.pillowcase.su/api/download/c10c3a058d69cecac24e99d0c3254680\", \"size\": \"10.4 MB\", \"duration\": 235.68}", "aliases": ["<PERSON>raise"], "size": "10.4 MB"}, {"id": "praise-god-193", "name": "Praise God [V28]", "artists": ["<PERSON>"], "producers": ["30 Roc", "Zentachi", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - 210618 - Trav <PERSON><PERSON><PERSON> Baby Keem intro trim_Edit 1_118 BPM\nContains Rhymefest reference vocals for <PERSON><PERSON><PERSON>'s hook. Leaked randomly on <PERSON><PERSON><PERSON>.", "length": "226.42", "fileDate": 17302464, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b45e7e15aaf7d6a04e41eda879e191c9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b45e7e15aaf7d6a04e41eda879e191c9\", \"key\": \"Praise <PERSON>\", \"title\": \"Praise <PERSON> [V28]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (feat. <PERSON>) (prod. 30 Roc, Zentachi & Ojivolta)\", \"description\": \"OG Filename: Praise God - 210618 - Trav Pusha Baby Keem intro trim_Edit 1_118 BPM\\nContains Rhymefest reference vocals for <PERSON><PERSON><PERSON>'s hook. Leaked randomly on Dbree.\", \"date\": 17302464, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f326ff3434ad379a7b0087e41374be32\", \"url\": \"https://api.pillowcase.su/api/download/f326ff3434ad379a7b0087e41374be32\", \"size\": \"4.59 MB\", \"duration\": 226.42}", "aliases": [], "size": "4.59 MB"}, {"id": "pray-and-leave-it-there", "name": "Pray and Leave It There [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> and Leave It There 0702e\nReference track by <PERSON> for \"Pray And Leave It There\".", "length": "198.29", "fileDate": 16460928, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/9b7f8bddcc8a481e9204c5acb1fd24bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9b7f8bddcc8a481e9204c5acb1fd24bb\", \"key\": \"<PERSON><PERSON> and Leave It There\", \"title\": \"<PERSON><PERSON> and Leave It There [V2]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Pray It And Leave It\"], \"description\": \"OG Filename: Pray and Leave It There 0702e\\nReference track by <PERSON> for \\\"Pray And Leave It There\\\".\", \"date\": 16460928, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3c72665ff6d0ba9bbbb60802fdc8eee2\", \"url\": \"https://api.pillowcase.su/api/download/3c72665ff6d0ba9bbbb60802fdc8eee2\", \"size\": \"9.76 MB\", \"duration\": 198.29}", "aliases": ["Pray It And Leave It"], "size": "9.76 MB"}, {"id": "hovercraft", "name": "Hovercraft [V2]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "OG Filename: Hovercraft (topline edit 2 oxv)\nEarliest cut-down version of the \"Ops Block\" freestyle.", "length": "94.77", "fileDate": 16352064, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/989a61421293e983ee8d8c686e2368fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/989a61421293e983ee8d8c686e2368fd\", \"key\": \"Hovercraft\", \"title\": \"Hovercraft [V2]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Remote\", \"Remote Control\"], \"description\": \"OG Filename: Hovercraft (topline edit 2 oxv)\\nEarliest cut-down version of the \\\"Ops Block\\\" freestyle.\", \"date\": 16352064, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5172ba020ddf93f8e2b53fe0f175e529\", \"url\": \"https://api.pillowcase.su/api/download/5172ba020ddf93f8e2b53fe0f175e529\", \"size\": \"8.11 MB\", \"duration\": 94.77}", "aliases": ["Remote", "Remote Control"], "size": "8.11 MB"}, {"id": "hovercraft-196", "name": "Hovercraft [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Ojivolta"], "notes": "The same beat and vocals as V2, but with a feature believed to be <PERSON><PERSON><PERSON>.", "length": "98.71", "fileDate": 16352064, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d0aac5a3ba22e5821e07f2134d73a9ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d0aac5a3ba22e5821e07f2134d73a9ac\", \"key\": \"Hovercraft\", \"title\": \"Hovercraft [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Remote\", \"Remote Control\"], \"description\": \"The same beat and vocals as V2, but with a feature believed to be <PERSON><PERSON><PERSON>\", \"date\": 16352064, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ba64cb0c5b0fbec253fdf1b8da58d657\", \"url\": \"https://api.pillowcase.su/api/download/ba64cb0c5b0fbec253fdf1b8da58d657\", \"size\": \"8.17 MB\", \"duration\": 98.71}", "aliases": ["Remote", "Remote Control"], "size": "8.17 MB"}, {"id": "hovercraft-197", "name": "Hovercraft [V4]", "artists": [], "producers": ["Digital Nas", "Ojivolta"], "notes": "Snippet of the demo with the whistle was previewed by Digital Nas on their Instagram story. Unknown what the features were at the time.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b07fe667ff56d7a60d3109a15f474ec7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b07fe667ff56d7a60d3109a15f474ec7\", \"key\": \"Hovercraft\", \"title\": \"Hovercraft [V4]\", \"artists\": \"(prod. Digital Nas & Ojivolta)\", \"aliases\": [\"Remote\", \"Remote Control\"], \"description\": \"Snippet of the demo with the whistle was previewed by Digital Nas on their Instagram story. Unknown what the features were at the time.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Remote", "Remote Control"], "size": ""}, {"id": "remote-control", "name": "Remote Control [V5]", "artists": [], "producers": ["<PERSON>"], "notes": "Another version from June featuring different production, notably different drums.", "length": "107.7", "fileDate": 16656192, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b7bca61043890105dc08dbd2980ebaf3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b7bca61043890105dc08dbd2980ebaf3\", \"key\": \"Remote Control\", \"title\": \"Remote Control [V5]\", \"artists\": \"(prod. <PERSON>) \", \"aliases\": [\"Remote\"], \"description\": \"Another version from June featuring different production, notably different drums.\", \"date\": 16656192, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"08d8b8713e62195fd63270463e1daed7\", \"url\": \"https://api.pillowcase.su/api/download/08d8b8713e62195fd63270463e1daed7\", \"size\": \"8.31 MB\", \"duration\": 107.7}", "aliases": ["Remote"], "size": "8.31 MB"}, {"id": "remote-control-199", "name": "Remote Control [V7]", "artists": [], "producers": ["Digital Nas", "88-<PERSON>"], "notes": "OG Filename: Remote Control - 21.05.31\nVersion that has different production such as 88-Keys drums. Leaked after <PERSON> Nas got his Mac leaked.", "length": "108.82", "fileDate": 17096832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/d032e6bb6d229a0c464bfc7b07e908df", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d032e6bb6d229a0c464bfc7b07e908df\", \"key\": \"Remote Control\", \"title\": \"Remote Control [V7]\", \"artists\": \"(prod. Digital Nas & 88-Keys)\", \"aliases\": [\"Remote\"], \"description\": \"OG Filename: Remote Control - 21.05.31\\nVersion that has different production such as 88-Keys drums. Leaked after Digital Nas got his Mac leaked.\", \"date\": 17096832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6133ae12ba3e056b85f14b82509783fb\", \"url\": \"https://api.pillowcase.su/api/download/6133ae12ba3e056b85f14b82509783fb\", \"size\": \"8.33 MB\", \"duration\": 108.82}", "aliases": ["Remote"], "size": "8.33 MB"}, {"id": "remote-control-200", "name": "Remote Control [V8]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: Remote Control - 21.06.02 Nas Drum Edit\nVersion with alternate production.", "length": "98.76", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/74e0c1c74b5228bcb1af98cb8ff6848b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/74e0c1c74b5228bcb1af98cb8ff6848b\", \"key\": \"Remote Control\", \"title\": \"Remote Control [V8]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Remote\"], \"description\": \"OG Filename: Remote Control - 21.06.02 Nas Drum Edit\\nVersion with alternate production.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"11f0b082e621bb3d4b957aa4872a3e09\", \"url\": \"https://api.pillowcase.su/api/download/11f0b082e621bb3d4b957aa4872a3e09\", \"size\": \"8.17 MB\", \"duration\": 98.76}", "aliases": ["Remote"], "size": "8.17 MB"}, {"id": "remote-control-201", "name": "Remote Control [V9]", "artists": [], "producers": ["Ojivolta", "<PERSON>"], "notes": "OG Filename: Remote Control - OxV - 21.06.22 Producer Approved\nVersion with different production from the released done by Ojivolta and <PERSON>. Original snippet leaked September 27th, 2022.", "length": "104.78", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/76c2374e22fc5719a9d1d490fcda90d0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76c2374e22fc5719a9d1d490fcda90d0\", \"key\": \"Remote Control\", \"title\": \"Remote Control [V9]\", \"artists\": \"(prod. Ojivolta & <PERSON>)\", \"aliases\": [\"Remote\"], \"description\": \"OG Filename: Remote Control - OxV - 21.06.22 Producer Approved\\nVersion with different production from the released done by Ojivolta and <PERSON>. Original snippet leaked September 27th, 2022.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"219e8092b0650904cabbd44bb58474bb\", \"url\": \"https://api.pillowcase.su/api/download/219e8092b0650904cabbd44bb58474bb\", \"size\": \"8.27 MB\", \"duration\": 104.78}", "aliases": ["Remote"], "size": "8.27 MB"}, {"id": "run-it-up-202", "name": "Run It Up [V4]", "artists": [], "producers": ["BoogzDaBeast", "Ojivolta"], "notes": "OG Filename: runitup (AUDIO_4583)\nVersion with no drums and additional backing vocals and production from <PERSON><PERSON>z. From late May. Tagged with \"Property of Kanye <PERSON>\" tags. Leaked during the \"Karate Kid\" groupbuy.", "length": "180.05", "fileDate": 16713216, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c52c1c843c26fa91f371244a26089830", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c52c1c843c26fa91f371244a26089830\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast & Ojivolta)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: runitup (AUDIO_4583)\\nVersion with no drums and additional backing vocals and production from Dem Jointz. From late May. Tagged with \\\"Property of Kanye West\\\" tags. Leaked during the \\\"Karate Kid\\\" groupbuy.\", \"date\": 16713216, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"465baf06013d491a617836320a9b705f\", \"url\": \"https://api.pillowcase.su/api/download/465baf06013d491a617836320a9b705f\", \"size\": \"9.47 MB\", \"duration\": 180.05}", "aliases": ["No Child Left Behind"], "size": "9.47 MB"}, {"id": "run-it-up-203", "name": "Run It Up [V5]", "artists": [], "producers": ["BoogzDaBeast", "Digital Nas", "Ojivolta"], "notes": "OG Filename: dn run it up\nA version with Digital Nas drums. Drums were done over the drumless version from late May. Leaked during the \"Karate Kid\" groupbuy.", "length": "180.05", "fileDate": 16712352, "leakDate": "", "availableLength": "Tagged", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/f961ff9e7ab73a0f15c5961a5e874c51", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f961ff9e7ab73a0f15c5961a5e874c51\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>B<PERSON>, Digital Nas & Ojivolta)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: dn run it up\\nA version with Digital Nas drums. Drums were done over the drumless version from late May. Leaked during the \\\"Karate Kid\\\" groupbuy.\", \"date\": 16712352, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3a16aeb56688d0271fbb924fa7a7c1d0\", \"url\": \"https://api.pillowcase.su/api/download/3a16aeb56688d0271fbb924fa7a7c1d0\", \"size\": \"9.47 MB\", \"duration\": 180.05}", "aliases": ["No Child Left Behind"], "size": "9.47 MB"}, {"id": "run-it-up-204", "name": "Run It Up [V6]", "artists": [], "producers": ["BoogzDaBeast", "Digital Nas", "Ojivolta"], "notes": "OG Filename: kanye <PERSON> run it up (Master)\nAnother version with more Digital Nas drums. Leaked during the \"Karate Kid\" groupbuy.", "length": "180.06", "fileDate": 16713216, "leakDate": "", "availableLength": "Tagged", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8da576c6806001a0a6cb3701c0a7238f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8da576c6806001a0a6cb3701c0a7238f\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>aB<PERSON>, Digital Nas & Ojivolta)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: kanye west run it up (Master)\\nAnother version with more Digital Nas drums. Leaked during the \\\"Karate Kid\\\" groupbuy.\", \"date\": 16713216, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"aec28da861c0e26c2f2fafa9609f7f27\", \"url\": \"https://api.pillowcase.su/api/download/aec28da861c0e26c2f2fafa9609f7f27\", \"size\": \"9.47 MB\", \"duration\": 180.06}", "aliases": ["No Child Left Behind"], "size": "9.47 MB"}, {"id": "run-it-up-205", "name": "Run It Up [V7]", "artists": [], "producers": ["BoogzDaBeast", "Dem <PERSON>z", "Ojivolta", "<PERSON>"], "notes": "OG Filename: run it up ref\nJune version. Contains less vocals than the May version, a different instrumental, and an open with 4 random snares (likely a bounce error). Features drums from <PERSON>. Has a cleaner mix than the previous leaked version. Leaked after a groupbuy.", "length": "244.9", "fileDate": 16665696, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c40c7aa23bb043b559df84c0b13d66cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c40c7aa23bb043b559df84c0b13d66cf\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ojivolta & Steve <PERSON>)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: run it up ref\\nJune version. Contains less vocals than the May version, a different instrumental, and an open with 4 random snares (likely a bounce error). Features drums from <PERSON>. Has a cleaner mix than the previous leaked version. Leaked after a groupbuy.\", \"date\": 16665696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1341315ab0532514647eece63598f578\", \"url\": \"https://api.pillowcase.su/api/download/1341315ab0532514647eece63598f578\", \"size\": \"10.5 MB\", \"duration\": 244.9}", "aliases": ["No Child Left Behind"], "size": "10.5 MB"}, {"id": "run-it-up-206", "name": "Run It Up [V8]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast", "Dem <PERSON>z", "Ojivolta"], "notes": "OG Filename: Run It Up 20 v2\nVersion with trap drums and some other elements added by <PERSON>. Likely from early July 2021.", "length": "177.63", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c3b9623f03689d7edcd37b4dccf08f14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3b9623f03689d7edcd37b4dccf08f14\", \"key\": \"Run It Up\", \"title\": \"Run It Up [V8]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dem Jointz & Ojivolta)\", \"aliases\": [\"No Child Left Behind\"], \"description\": \"OG Filename: Run It Up 20 v2\\nVersion with trap drums and some other elements added by <PERSON>. Likely from early July 2021.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cfdae2dba3285461548205890c7611dd\", \"url\": \"https://api.pillowcase.su/api/download/cfdae2dba3285461548205890c7611dd\", \"size\": \"9.43 MB\", \"duration\": 177.63}", "aliases": ["No Child Left Behind"], "size": "9.43 MB"}, {"id": "soul-de-la", "name": "✨ Soul De La [V2]", "artists": [], "producers": ["BoogzDaBeast", "Dem <PERSON>z"], "notes": "Dem Jointz version of the original \"Pure Souls\" freestyle, \"Sheep\", from May. Has a beat completely different from \"Pure Souls,\" but includes some lyrics reused for later versions of the song, as well as a lot of alternate lines and <PERSON><PERSON>z punch ins. <PERSON><PERSON>'s hook is taken from lyrics <PERSON><PERSON><PERSON> performs in this version.", "length": "192.99", "fileDate": 16911936, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/13a21dd4e7766eb0125b28b9cdbc6be7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/13a21dd4e7766eb0125b28b9cdbc6be7\", \"key\": \"Soul De La\", \"title\": \"\\u2728 Soul De La [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast & Dem Jointz)\", \"aliases\": [\"Pure Souls\", \"Sheep\"], \"description\": \"Dem Jointz version of the original \\\"Pure Souls\\\" freestyle, \\\"Sheep\\\", from May. Has a beat completely different from \\\"Pure Souls,\\\" but includes some lyrics reused for later versions of the song, as well as a lot of alternate lines and De<PERSON> Jointz punch ins. <PERSON><PERSON>'s hook is taken from lyrics <PERSON><PERSON><PERSON> performs in this version.\", \"date\": 16911936, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"83bdd7d9ae9a4690b2d79356582197e2\", \"url\": \"https://api.pillowcase.su/api/download/83bdd7d9ae9a4690b2d79356582197e2\", \"size\": \"9.68 MB\", \"duration\": 192.99}", "aliases": ["Pure Souls", "Sheep"], "size": "9.68 MB"}, {"id": "sheep", "name": "Sheep [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: She<PERSON> (210525 Gallery - Boogz 7) - 210611 CyHi\nCyHi reference track. Uses the OG production used in the version played at LP1. Original snippet leaked October 6th, 2021, with two more leaking November 27th, 2022. Leaked as a bonus to the \"Hybrid\" groupbuy.", "length": "94.08", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/4a19c5b571dcb839cd98e46e85b6caae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a19c5b571dcb839cd98e46e85b6caae\", \"key\": \"Sheep\", \"title\": \"Sheep [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>zDaBeast)\", \"aliases\": [\"Pure Souls\", \"Soul De La\"], \"description\": \"OG Filename: Sheep (210525 Gallery - Boogz 7) - 210611 CyHi\\nCyHi reference track. Uses the OG production used in the version played at LP1. Original snippet leaked October 6th, 2021, with two more leaking November 27th, 2022. Leaked as a bonus to the \\\"Hybrid\\\" groupbuy.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"39d8a5359c367894880301a8d2916a07\", \"url\": \"https://api.pillowcase.su/api/download/39d8a5359c367894880301a8d2916a07\", \"size\": \"8.1 MB\", \"duration\": 94.08}", "aliases": ["Pure Souls", "Soul De La"], "size": "8.1 MB"}, {"id": "sheep-209", "name": "✨ Sheep [V4]", "artists": [], "producers": ["BoogzDaBeast", "Ojivolta", "<PERSON>"], "notes": "OG Filename: sheep freestyle drum ref\nVersion from June 2021 containing freestyle vocals not found in \"Soul De La\" or \"Pure Souls\". Features live synth production from Ojivolta and drums done by <PERSON> over an instrumental similar to LP1 \"Pure Souls\".", "length": "264.85", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/bfab562249c2b92c2883678adfd63989", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bfab562249c2b92c2883678adfd63989\", \"key\": \"Sheep\", \"title\": \"\\u2728 Sheep [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>, Ojivolta & <PERSON>)\", \"aliases\": [\"Pure Souls\", \"Soul De La\"], \"description\": \"OG Filename: sheep freestyle drum ref\\nVersion from June 2021 containing freestyle vocals not found in \\\"Soul De La\\\" or \\\"Pure Souls\\\". Features live synth production from Ojivolta and drums done by <PERSON> over an instrumental similar to LP1 \\\"Pure Souls\\\".\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"58c7d56c13d9229b835ab3c98bf65c4c\", \"url\": \"https://api.pillowcase.su/api/download/58c7d56c13d9229b835ab3c98bf65c4c\", \"size\": \"10.8 MB\", \"duration\": 264.85}", "aliases": ["Pure Souls", "Soul De La"], "size": "10.8 MB"}, {"id": "south-carolina", "name": "South Carolina [V15]", "artists": ["STALONE", "Sunday Service Choir"], "producers": [], "notes": "OG Filename: South Carolina - For Digital nas 78.5\nDrumless version of the December 2020 version of \"South Carolina\" bounced in May 2021. Meant for Digital Nas to place drums over, and thus contains a quiet tag as a result. Features an additional Pusha T punch-in. Leaked after a groupbuy.", "length": "271.7", "fileDate": 16460928, "leakDate": "", "availableLength": "Tagged", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5f8de33c83b69efc72a75d66e9557c79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f8de33c83b69efc72a75d66e9557c79\", \"key\": \"South Carolina\", \"title\": \"South Carolina [V15]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>E & Sunday Service Choir)\", \"aliases\": [\"<PERSON><PERSON>\", \"<PERSON><PERSON>'s Glory\", \"Glory\"], \"description\": \"OG Filename: South Carolina - For Digital nas 78.5\\nDrumless version of the December 2020 version of \\\"South Carolina\\\" bounced in May 2021. Meant for Digital Nas to place drums over, and thus contains a quiet tag as a result. Features an additional <PERSON><PERSON><PERSON> punch-in. Leaked after a groupbuy.\", \"date\": 16460928, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e71bb16741d4e910fcca800a085a53ec\", \"url\": \"https://api.pillowcase.su/api/download/e71bb16741d4e910fcca800a085a53ec\", \"size\": \"10.9 MB\", \"duration\": 271.7}", "aliases": ["<PERSON><PERSON>", "Donda's Glory", "Glory"], "size": "10.9 MB"}, {"id": "south-carolina-211", "name": "South Carolina [V16]", "artists": ["STALONE", "Sunday Service Choir"], "producers": ["Digital Nas"], "notes": "May 2021 version of \"South Carolina\" with production from Digital Nas. Features the same tag as the previous version, but even quieter. Leaked as a bonus for the \"Let The Spirit Go Wild\" groupbuy.", "length": "275.2", "fileDate": 16473888, "leakDate": "", "availableLength": "Tagged", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/4265d01a56e42d2e01a22954e89d284e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4265d01a56e42d2e01a22954e89d284e\", \"key\": \"South Carolina\", \"title\": \"South Carolina [V16]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>E & Sunday Service Choir) (prod. Digital Nas)\", \"aliases\": [\"<PERSON><PERSON>\", \"Don<PERSON>'s Glory\", \"Glory\"], \"description\": \"May 2021 version of \\\"South Carolina\\\" with production from Digital Nas. Features the same tag as the previous version, but even quieter. Leaked as a bonus for the \\\"Let The Spirit Go Wild\\\" groupbuy.\", \"date\": 16473888, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8253cd20772a664c027fb5ff38f4ee09\", \"url\": \"https://api.pillowcase.su/api/download/8253cd20772a664c027fb5ff38f4ee09\", \"size\": \"11 MB\", \"duration\": 275.2}", "aliases": ["<PERSON><PERSON>", "Donda's Glory", "Glory"], "size": "11 MB"}, {"id": "south-carolina-212", "name": "✨ South Carolina [V17]", "artists": ["STALONE", "Sunday Service Choir"], "producers": ["Digital Nas"], "notes": "OG Filename: South Carolina - 210617 <PERSON>_<PERSON><PERSON>a <PERSON>ll Ins_83BPM\nHas more Kanye vocals and new drums that would be used on later versions. Found in a 2020-2021 copy of <PERSON><PERSON>.", "length": "271.66", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e65c568067d5b30a9e1d68260c9c4cb2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e65c568067d5b30a9e1d68260c9c4cb2\", \"key\": \"South Carolina\", \"title\": \"\\u2728 South Carolina [V17]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. STALONE & Sunday Service Choir) (prod. Digital Nas)\", \"aliases\": [\"<PERSON><PERSON>\", \"<PERSON><PERSON>'s Glory\", \"Glory\"], \"description\": \"OG Filename: South Carolina - 210617 Ye Verse_Pusha Fill Ins_83BPM\\nHas more Kanye vocals and new drums that would be used on later versions. Found in a 2020-2021 copy of <PERSON><PERSON>.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"783b7b2b7cc01457d65639903a84e674\", \"url\": \"https://api.pillowcase.su/api/download/783b7b2b7cc01457d65639903a84e674\", \"size\": \"10.9 MB\", \"duration\": 271.66}", "aliases": ["<PERSON><PERSON>", "Donda's Glory", "Glory"], "size": "10.9 MB"}, {"id": "south-carolina-213", "name": "⭐ South Carolina [V18]", "artists": ["Sunday Service Choir", "MUSYCA Children's Choir"], "producers": [], "notes": "OG Filename: South Carolina - 21.06.23 dR3\nFeatures the sample at its original pitch and new lyrics that would go on to be reused for \"Lord I Need You\". Leaked after a groupbuy.", "length": "266.09", "fileDate": 16373664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e209efb54b4b71ccecbee79fbe74b20b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e209efb54b4b71ccecbee79fbe74b20b\", \"key\": \"South Carolina\", \"title\": \"\\u2b50 South Carolina [V18]\", \"artists\": \"(feat. Sunday Service Choir & MUSYCA Children's Choir)\", \"aliases\": [\"<PERSON><PERSON>\", \"<PERSON><PERSON>'s Glory\", \"Glory\"], \"description\": \"OG Filename: South Carolina - 21.06.23 dR3\\nFeatures the sample at its original pitch and new lyrics that would go on to be reused for \\\"Lord I Need You\\\". Leaked after a groupbuy.\", \"date\": 16373664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5f0f1140c8c3d683bafa7867b3e2ced7\", \"url\": \"https://api.pillowcase.su/api/download/5f0f1140c8c3d683bafa7867b3e2ced7\", \"size\": \"10.8 MB\", \"duration\": 266.09}", "aliases": ["<PERSON><PERSON>", "Donda's Glory", "Glory"], "size": "10.8 MB"}, {"id": "this-is-the-glory", "name": "This Is The Glory [V8]", "artists": ["Dem <PERSON>z"], "producers": ["Dem <PERSON>z", "Dr. <PERSON><PERSON>"], "notes": "OG Filename: THIS IS THE GLORY (Dem Jointz Mix N Edit FOUR No Hats 162bpm)\nA Dem Jointz produced version of \"This Is The Glory\". Metadata states it was bounced in 2021. Outside of that, it is unknown when it's from. Dem Jointz's \"Clear\" vocals are more prominent on this version.", "length": "105.68", "fileDate": 16638048, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a0d7f533a524832f4bff4c47778e4e6c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0d7f533a524832f4bff4c47778e4e6c\", \"key\": \"This Is The Glory\", \"title\": \"This Is The Glory [V8]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. Dem Jointz & Dr. Dre)\", \"aliases\": [\"Glory\"], \"description\": \"OG Filename: THIS IS THE GLORY (Dem Jointz Mix N Edit FOUR No Hats 162bpm)\\nA Dem Jointz produced version of \\\"This Is The Glory\\\". Metadata states it was bounced in 2021. Outside of that, it is unknown when it's from. <PERSON>m Jointz's \\\"Clear\\\" vocals are more prominent on this version.\", \"date\": 16638048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4e2804e10e9902c021bb320a58805c9a\", \"url\": \"https://api.pillowcase.su/api/download/4e2804e10e9902c021bb320a58805c9a\", \"size\": \"8.28 MB\", \"duration\": 105.68}", "aliases": ["Glory"], "size": "8.28 MB"}, {"id": "wait", "name": "Wait [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "Snippet of an earlier version of \"Wait\" with slightly different prod and different vocals. Leaked alongside the further along freestyle.", "length": "61.18", "fileDate": 16532640, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/04654ab63b061ef241e66827ac7c3373", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/04654ab63b061ef241e66827ac7c3373\", \"key\": \"Wait\", \"title\": \"Wait [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"Snippet of an earlier version of \\\"Wait\\\" with slightly different prod and different vocals. Leaked alongside the further along freestyle.\", \"date\": 16532640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7f856cadba4cae5e16f8c8f1d0e51f3b\", \"url\": \"https://api.pillowcase.su/api/download/7f856cadba4cae5e16f8c8f1d0e51f3b\", \"size\": \"7.57 MB\", \"duration\": 61.18}", "aliases": [], "size": "7.57 MB"}, {"id": "wait-216", "name": "Wait [V2]", "artists": [], "producers": ["Digital Nas", "E.VAX"], "notes": "OG Filename: wait freestyle <PERSON> chop\nEdited down version of the original \"Wait\" freestyle, arranged by <PERSON><PERSON>.", "length": "156.22", "fileDate": 16532640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c87b662c6649a885df5d79973b386cb3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c87b662c6649a885df5d79973b386cb3\", \"key\": \"Wait\", \"title\": \"Wait [V2]\", \"artists\": \"(prod. Digital Nas & E.VAX)\", \"description\": \"OG Filename: wait freestyle Evan chop\\nEdited down version of the original \\\"Wait\\\" freestyle, arranged by <PERSON><PERSON>.\", \"date\": 16532640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"748fdf30abcb38e9f6216ec1e7bf4386\", \"url\": \"https://api.pillowcase.su/api/download/748fdf30abcb38e9f6216ec1e7bf4386\", \"size\": \"9.09 MB\", \"duration\": 156.22}", "aliases": [], "size": "9.09 MB"}, {"id": "we-want-the-lord", "name": "We Want The Lord", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: 210616 Gallery - Piano Freestyles - We Want The Lord\nJune piano freestyle. Leaked after the private group that bought it disbanded.", "length": "403.38", "fileDate": 16383168, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/906c4d401097dfc954755b191db091bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/906c4d401097dfc954755b191db091bb\", \"key\": \"We Want The Lord\", \"title\": \"We Want The Lord\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: 210616 Gallery - Piano Freestyles - We Want The Lord\\nJune piano freestyle. Leaked after the private group that bought it disbanded.\", \"date\": 16383168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bedcf8cf38b5fee1cebfd4ac1b724485\", \"url\": \"https://api.pillowcase.su/api/download/bedcf8cf38b5fee1cebfd4ac1b724485\", \"size\": \"13 MB\", \"duration\": 403.38}", "aliases": [], "size": "13 MB"}, {"id": "woke-up", "name": "Woke Up", "artists": [], "producers": [], "notes": "OG Filename: woke up final\nJune 2021 Digital Nas reference track. Completely different to the 2014-2015 song of the same name.", "length": "93.05", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e20e584f19e7080ea8cd92b8e7d3e003", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e20e584f19e7080ea8cd92b8e7d3e003\", \"key\": \"Woke Up\", \"title\": \"Woke Up\", \"artists\": \"(ref. Digital Nas)\", \"description\": \"OG Filename: woke up final\\nJune 2021 Digital Nas reference track. Completely different to the 2014-2015 song of the same name.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b9e687e0e5af1a8a5d07dbe59632a5bc\", \"url\": \"https://api.pillowcase.su/api/download/b9e687e0e5af1a8a5d07dbe59632a5bc\", \"size\": \"8.08 MB\", \"duration\": 93.05}", "aliases": [], "size": "8.08 MB"}, {"id": "-219", "name": "???", "artists": [], "producers": [], "notes": "Unknown early 2021 song.", "length": "301.24", "fileDate": 17101152, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/816149f71cad73ab91f9498ec8fd3e2d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/816149f71cad73ab91f9498ec8fd3e2d\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown early 2021 song.\", \"date\": 17101152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"db735d794c690d35beb841a9fb796619\", \"url\": \"https://api.pillowcase.su/api/download/db735d794c690d35beb841a9fb796619\", \"size\": \"9 MB\", \"duration\": 301.24}", "aliases": [], "size": "9 MB"}, {"id": "-220", "name": "???", "artists": [], "producers": [], "notes": "Unknown early 2021 song.", "length": "114.29", "fileDate": 17101152, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fe404cdd93e7872027fddc454dab8847", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe404cdd93e7872027fddc454dab8847\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Tell Me\"], \"description\": \"Unknown early 2021 song.\", \"date\": 17101152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5189869815bc66cbc3a9b55e83131672\", \"url\": \"https://api.pillowcase.su/api/download/5189869815bc66cbc3a9b55e83131672\", \"size\": \"7.5 MB\", \"duration\": 114.29}", "aliases": ["Tell Me"], "size": "7.5 MB"}, {"id": "-221", "name": "???", "artists": ["Vory"], "producers": [], "notes": "OG Filename: Ye x Vory 1\nRough mumble freestyle between <PERSON> and <PERSON><PERSON>, exact date is unknown but it was most likely made when they first met up in late June. Actual name was thought to be called \"<PERSON><PERSON>\". Original snippet leaked December 2022.", "length": "192.73", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6924e0eeb4ec4f9e42e72317fbc11b0f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6924e0eeb4ec4f9e42e72317fbc11b0f\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"OG Filename: Ye x Vory 1\\nRough mumble freestyle between <PERSON> and <PERSON><PERSON>, exact date is unknown but it was most likely made when they first met up in late June. Actual name was thought to be called \\\"<PERSON><PERSON><PERSON>\\\". Original snippet leaked December 2022.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"906cf7105a6125f9ad9d0f01e130c889\", \"url\": \"https://api.pillowcase.su/api/download/906cf7105a6125f9ad9d0f01e130c889\", \"size\": \"9.68 MB\", \"duration\": 192.73}", "aliases": ["<PERSON><PERSON>"], "size": "9.68 MB"}, {"id": "a-wise-tale", "name": "Abstract Mindstate - A Wise Tale [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: A Wise Tale ADMIX.2 16bit loud ref\nOG file for \"A Wise Tale\". 2 seconds shorter than release.", "length": "144.34", "fileDate": 16749504, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/c50c134ab376fa7946300b7eacf5d8e5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c50c134ab376fa7946300b7eacf5d8e5\", \"key\": \"A Wise Tale\", \"title\": \"Abstract Mindstate - A Wise Tale [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: A Wise Tale ADMIX.2 16bit loud ref\\nOG file for \\\"A Wise Tale\\\". 2 seconds shorter than release.\", \"date\": 16749504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4a4c6137e301d1ca89b4a55628d08d88\", \"url\": \"https://api.pillowcase.su/api/download/4a4c6137e301d1ca89b4a55628d08d88\", \"size\": \"8.9 MB\", \"duration\": 144.34}", "aliases": [], "size": "8.9 MB"}, {"id": "i-feel-good", "name": "Abstract Mindstate - I Feel Good [V4]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: I Feel Good ADMIX.4 16bit loud ref\nOG file for \"I Feel Good\". 1 second shorter than release.", "length": "148.06", "fileDate": 16749504, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/95c32748456e3b29d4721b4bd8d8fd32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/95c32748456e3b29d4721b4bd8d8fd32\", \"key\": \"I Feel Good\", \"title\": \"Abstract Mindstate - I Feel Good [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: I Feel Good ADMIX.4 16bit loud ref\\nOG file for \\\"I Feel Good\\\". 1 second shorter than release.\", \"date\": 16749504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eb211565aacad4be60ef3e4b49196f2f\", \"url\": \"https://api.pillowcase.su/api/download/eb211565aacad4be60ef3e4b49196f2f\", \"size\": \"8.96 MB\", \"duration\": 148.06}", "aliases": [], "size": "8.96 MB"}, {"id": "hot-shit", "name": "Cardi B - Hot Shit [V6]", "artists": [], "producers": ["<PERSON><PERSON>", "BanBwoi"], "notes": "Solo version of \"Hot Shit\" with the release beat. Leaked August 21, 2022.", "length": "162.59", "fileDate": 16610400, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/b9757e14bbd1ba56e8880ee72de9e0b8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b9757e14bbd1ba56e8880ee72de9e0b8\", \"key\": \"Hot Shit\", \"title\": \"Cardi B - Hot Shit [V6]\", \"artists\": \"(prod. <PERSON><PERSON> & BanBwoi)\", \"description\": \"Solo version of \\\"Hot Shit\\\" with the release beat. Leaked August 21, 2022.\", \"date\": 16610400, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3aabffed410e30de4a5041855f7e4dfd\", \"url\": \"https://api.pillowcase.su/api/download/3aabffed410e30de4a5041855f7e4dfd\", \"size\": \"9.19 MB\", \"duration\": 162.59}", "aliases": [], "size": "9.19 MB"}, {"id": "tower", "name": "Digital Nas - Tower [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: TOWER_Current\nEarly version of \"Made It This Far\" that is shorter actual song wise compared to later versions but is 'longer' due to the silence at the end. <PERSON> probably made pre-<PERSON><PERSON> involvement and may have been shown to <PERSON><PERSON><PERSON> due to the file date and him recording \"Miracles\" on the same day, this is speculation however.", "length": "241.66", "fileDate": 17346528, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/508a834df3b7834c445eaa65d1f14855", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/508a834df3b7834c445eaa65d1f14855\", \"key\": \"Tower\", \"title\": \"Digital Nas - Tower [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Made It This Far\", \"Made It\"], \"description\": \"OG Filename: TOWER_Current\\nEarly version of \\\"Made It This Far\\\" that is shorter actual song wise compared to later versions but is 'longer' due to the silence at the end. Was probably made pre-<PERSON><PERSON> involvement and may have been shown to <PERSON><PERSON><PERSON> due to the file date and him recording \\\"Miracles\\\" on the same day, this is speculation however.\", \"date\": 17346528, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"57b152a32aec875afcc17675d3541b87\", \"url\": \"https://api.pillowcase.su/api/download/57b152a32aec875afcc17675d3541b87\", \"size\": \"10.5 MB\", \"duration\": 241.66}", "aliases": ["Made It This Far", "Made It"], "size": "10.5 MB"}, {"id": "ruff-ryders-anthem", "name": "DMX - <PERSON><PERSON>' Anthem (Remix)", "artists": [], "producers": ["Kanye West", "Ojivolta"], "notes": "OG Filename: <PERSON><PERSON> Anthem - Memorial Edition\nStudio version of the \"Ruff Ryders' Anthem\" remix played at <PERSON><PERSON>'s funeral.", "length": "226.22", "fileDate": 16648416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/7d8aafafa89380a2e6a74db2d05d5b14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7d8aafafa89380a2e6a74db2d05d5b14\", \"key\": \"Ruff Ryders' Anthem (Remix)\", \"title\": \"DMX - Ruff Ryders' Anthem (Remix)\", \"artists\": \"(prod. <PERSON><PERSON>e West & Ojivolta)\", \"description\": \"OG Filename: <PERSON>uff Ryders Anthem - Memorial Edition\\nStudio version of the \\\"Ruff Ryders' Anthem\\\" remix played at <PERSON><PERSON>'s funeral.\", \"date\": 16648416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"16e307805dbad3cc6ece5f3211e29865\", \"url\": \"https://api.pillowcase.su/api/download/16e307805dbad3cc6ece5f3211e29865\", \"size\": \"10.2 MB\", \"duration\": 226.22}", "aliases": [], "size": "10.2 MB"}, {"id": "promotion", "name": "✨ Future - Promotion [V2]", "artists": ["Ty Dolla $ign"], "producers": ["DJ <PERSON><PERSON>"], "notes": "<PERSON> $ign recorded for this song in 2021, potentially during the Pico Pico sessions as Ty & Future were both present. Potential long snippet for this version leaked 8/14/24. Leaked in full-CDQ after a groupbuy.", "length": "191.54", "fileDate": 17329248, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/1189777ff29b65d8a191538f6f14895e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1189777ff29b65d8a191538f6f14895e\", \"key\": \"Promotion\", \"title\": \"\\u2728 Future - Promotion [V2]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON>)\", \"aliases\": [\"Gorgeous\"], \"description\": \"Ty Dolla $ign recorded for this song in 2021, potentially during the Pico Pico sessions as Ty & Future were both present. Potential long snippet for this version leaked 8/14/24. Leaked in full-CDQ after a groupbuy.\", \"date\": 17329248, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"063b2e514fc70dd7d91ee87dd9a6c448\", \"url\": \"https://api.pillowcase.su/api/download/063b2e514fc70dd7d91ee87dd9a6c448\", \"size\": \"9.65 MB\", \"duration\": 191.54}", "aliases": ["Gorgeous"], "size": "9.65 MB"}, {"id": "freedom", "name": "KayCyy - FREEDOM [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Previewed by <PERSON><PERSON><PERSON><PERSON> on an Instagram story, revealing it was produced by <PERSON><PERSON><PERSON>. Features additional production and lack of vocals compared to V2", "length": "11.52", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a954405b25a6f0971e0fdee3394c0a0d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a954405b25a6f0971e0fdee3394c0a0d\", \"key\": \"FREEDOM\", \"title\": \"Kay<PERSON>yy - FREEDOM [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Depression Relief\"], \"description\": \"Previewed by <PERSON><PERSON><PERSON><PERSON> on an Instagram story, revealing it was produced by <PERSON><PERSON><PERSON>. Features additional production and lack of vocals compared to V2\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3acf5a18d39c98a0f5d968aee056cbca\", \"url\": \"https://api.pillowcase.su/api/download/3acf5a18d39c98a0f5d968aee056cbca\", \"size\": \"6.77 MB\", \"duration\": 11.52}", "aliases": ["Depression Relief"], "size": "6.77 MB"}, {"id": "freedom-229", "name": "KayCyy - FREEDOM [V3]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "Snippet shared on <PERSON><PERSON><PERSON><PERSON>'s twitter of a Ye produced song, features <PERSON><PERSON><PERSON> talking in the start. <PERSON><PERSON> the synth heard in V1.", "length": "18.6", "fileDate": 16219008, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/898693c9111fb2cd8ce19d63d7ee08c7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/898693c9111fb2cd8ce19d63d7ee08c7\", \"key\": \"FREEDOM\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> - FREEDOM [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Depression Relief\"], \"description\": \"Snippet shared on <PERSON><PERSON><PERSON><PERSON>'s twitter of a Ye produced song, features <PERSON><PERSON><PERSON> talking in the start. <PERSON>ks the synth heard in V1.\", \"date\": 16219008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9b4067e172aa69d1024d52df0d401781\", \"url\": \"https://api.pillowcase.su/api/download/9b4067e172aa69d1024d52df0d401781\", \"size\": \"6.89 MB\", \"duration\": 18.6}", "aliases": ["Depression Relief"], "size": "6.89 MB"}, {"id": "depression-relief", "name": "KayCyy - Depression Relief [V4]", "artists": ["<PERSON><PERSON>", "Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: depression relief johan1\nVersion of the song featuring additional production from <PERSON>.", "length": "233.8", "fileDate": 16609536, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/a44dcf5bdedd7947733175194e52a959", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a44dcf5bdedd7947733175194e52a959\", \"key\": \"Depression Relief\", \"title\": \"KayCyy - Depression Relief [V4]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"FREEDOM\"], \"description\": \"OG Filename: depression relief johan1\\nVersion of the song featuring additional production from <PERSON>.\", \"date\": 16609536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e4d2c7d8151ca8e0d79d094684517853\", \"url\": \"https://api.pillowcase.su/api/download/e4d2c7d8151ca8e0d79d094684517853\", \"size\": \"10.3 MB\", \"duration\": 233.8}", "aliases": ["FREEDOM"], "size": "10.3 MB"}, {"id": "friends-don-t-hurt-friends", "name": "<PERSON><PERSON><PERSON><PERSON> - Friends Don't Hurt Friends [V7]", "artists": ["VIC MENSA", "<PERSON> and the Lights"], "producers": ["Kanye West"], "notes": "OG Filename: FRIENDS DONT HURT FRIENDS (WIP) - R2\nVersion of \"Friends Don't Hurt Friends\" with VIC MENSA. Was played by <PERSON><PERSON><PERSON><PERSON> on an Instagram Live.", "length": "210.57", "fileDate": 16537824, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3527a6ee09529b0816549643681a1f32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3527a6ee09529b0816549643681a1f32\", \"key\": \"Friends Don't Hurt Friends\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> - <PERSON> Don't Hurt Friends [V7]\", \"artists\": \"(feat. VIC MENSA & Francis and the Lights) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Don't Hurt Your Friends\"], \"description\": \"OG Filename: FRIENDS DONT HURT FRIENDS (WIP) - R2\\nVersion of \\\"Friends Don't Hurt Friends\\\" with VIC MENSA. Was played by <PERSON><PERSON><PERSON><PERSON> on an Instagram Live.\", \"date\": 16537824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d7c834b4552a30796d1a50e0fc16eda7\", \"url\": \"https://api.pillowcase.su/api/download/d7c834b4552a30796d1a50e0fc16eda7\", \"size\": \"9.96 MB\", \"duration\": 210.57}", "aliases": ["Don't Hurt Your Friends"], "size": "9.96 MB"}, {"id": "-232", "name": "<PERSON> Baby - ??? [V1]", "artists": [], "producers": ["FOREVEROLLING", "Flex on the Beat", "Audiovista", "Mattazik Muzik"], "notes": "Original version of \"Forever Rollin'\" made during the sessions for <PERSON> Baby and <PERSON>'s collaborative album \"The Voice of the Heroes\". Contains a second <PERSON> Baby verse.", "length": "11.55", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/e1bd3b9fbcb473fefb8643c1795f701c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e1bd3b9fbcb473fefb8643c1795f701c\", \"key\": \"???\", \"title\": \"<PERSON> Baby - ??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> on the Beat, Audiovista & Mattazik Muzik)\", \"aliases\": [\"Forever Rollin'\", \"Meant It\", \"FOREVER ROLLING\"], \"description\": \"Original version of \\\"Forever Rollin'\\\" made during the sessions for <PERSON> Baby and <PERSON> Durk's collaborative album \\\"The Voice of the Heroes\\\". Contains a second Lil Baby verse.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"110355114469fea5d50808fb893ab1f6\", \"url\": \"https://api.pillowcase.su/api/download/110355114469fea5d50808fb893ab1f6\", \"size\": \"6.68 MB\", \"duration\": 11.55}", "aliases": ["Forever Rollin'", "Meant It", "FOREVER ROLLING"], "size": "6.68 MB"}, {"id": "-233", "name": "<PERSON> Baby - ??? [V1]", "artists": [], "producers": ["FOREVEROLLING", "Flex on the Beat", "Audiovista", "Mattazik Muzik"], "notes": "Original version of \"Forever Rollin'\" made during the sessions for <PERSON> Baby and <PERSON>'s collaborative album \"The Voice of the Heroes\". Contains a second <PERSON> Baby verse.", "length": "18.42", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/cbbf92aafe11bc05a381b607f6d9c548", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cbbf92aafe11bc05a381b607f6d9c548\", \"key\": \"???\", \"title\": \"<PERSON> Baby - ??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> on the Beat, Audiovista & Mattazik Muzik)\", \"aliases\": [\"Forever Rollin'\", \"Meant It\", \"FOREVER ROLLING\"], \"description\": \"Original version of \\\"Forever Rollin'\\\" made during the sessions for <PERSON> Baby and <PERSON> Durk's collaborative album \\\"The Voice of the Heroes\\\". Contains a second Lil Baby verse.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ba38c7e4059526c010c1843e2dd2298a\", \"url\": \"https://api.pillowcase.su/api/download/ba38c7e4059526c010c1843e2dd2298a\", \"size\": \"6.74 MB\", \"duration\": 18.42}", "aliases": ["Forever Rollin'", "Meant It", "FOREVER ROLLING"], "size": "6.74 MB"}, {"id": "on-my-own", "name": "<PERSON> Baby - On My Own [V2]", "artists": [], "producers": ["FOREVEROLLING", "Flex on the Beat", "Audiovista", "Mattazik Muzik"], "notes": "Solo version of \"On My Own\", which would later become \"Forever Rolling\".", "length": "129.31", "fileDate": 17266176, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/3127aaa9c9ffb6bde8c8d737b7fc3fa8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3127aaa9c9ffb6bde8c8d737b7fc3fa8\", \"key\": \"On My Own\", \"title\": \"Lil Baby - On My Own [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> on the Beat, Audiovista & Mattazik Muzik)\", \"aliases\": [\"Forever Rollin'\", \"Meant It\", \"FOREVER ROLLING\"], \"description\": \"Solo version of \\\"On My Own\\\", which would later become \\\"Forever Rolling\\\".\", \"date\": 17266176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"17ec2b1aa70f56267708911d5c10266b\", \"url\": \"https://api.pillowcase.su/api/download/17ec2b1aa70f56267708911d5c10266b\", \"size\": \"8.66 MB\", \"duration\": 129.31}", "aliases": ["Forever Rollin'", "Meant It", "FOREVER ROLLING"], "size": "8.66 MB"}, {"id": "industry-baby-235", "name": "Lil Nas X - Industry Baby [V16]", "artists": [], "producers": ["Take A Daytrip", "<PERSON>"], "notes": "Version of \"Industry Baby\" which has a different moan at <PERSON>'s part in comparison to Demo 19.", "length": "9.38", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/bbb79261d3bd60d90aa1e87096c97145", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbb79261d3bd60d90aa1e87096c97145\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V16]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON> Daytrip & <PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"Version of \\\"Industry Baby\\\" which has a different moan at <PERSON>'s part in comparison to Demo 19.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a11ea357804405c0ef3bd0db9c077d7c\", \"url\": \"https://api.pillowcase.su/api/download/a11ea357804405c0ef3bd0db9c077d7c\", \"size\": \"2.57 MB\", \"duration\": 9.38}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "2.57 MB"}, {"id": "industry-baby-236", "name": "Lil Nas X - Industry Baby [V21]", "artists": ["<PERSON>"], "producers": ["Take A Daytrip", "<PERSON>", "Kanye West"], "notes": "OG Filename - 2.) industry baby [demo 19]\nDemo 19 of \"Industry Baby\", featuring <PERSON>. Has a distinct moan at 2:15 in the track. Leaked on an early copy of MONTERO.", "length": "214.47", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/91c6b0d961ef57f51237308e34f1fb37", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91c6b0d961ef57f51237308e34f1fb37\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V21]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON> & Ka<PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"OG Filename - 2.) industry baby [demo 19]\\nDemo 19 of \\\"Industry Baby\\\", featuring <PERSON>. Has a distinct moan at 2:15 in the track. Leaked on an early copy of MONTERO.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ac473747a68b8a3a43c3d7286964c73e\", \"url\": \"https://api.pillowcase.su/api/download/ac473747a68b8a3a43c3d7286964c73e\", \"size\": \"5.85 MB\", \"duration\": 214.47}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "5.85 MB"}, {"id": "industry-baby-237", "name": "Lil Nas X - Industry Baby [V22]", "artists": ["<PERSON>"], "producers": ["Take A Daytrip", "Kanye West", "<PERSON>"], "notes": "Version of \"Industry Baby\" with additional Kanye production. According to flab, the Kanye horns were in a folder titled \"YE\". Has mixing differences to released. Was previewed in the Montero trailer.", "length": "53.08", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/601314d1c55efe3072e504f5516102e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/601314d1c55efe3072e504f5516102e1\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V22]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"Version of \\\"Industry Baby\\\" with additional Kanye production. According to flab, the Kanye horns were in a folder titled \\\"YE\\\". Has mixing differences to released. Was previewed in the Montero trailer.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"110a3b90391943caab497b099c67aefb\", \"url\": \"https://api.pillowcase.su/api/download/110a3b90391943caab497b099c67aefb\", \"size\": \"3.26 MB\", \"duration\": 53.08}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "3.26 MB"}, {"id": "industry-baby-238", "name": "Lil Nas X - Industry Baby [V23]", "artists": ["<PERSON>"], "producers": ["Take A Daytrip", "Kanye West", "<PERSON>"], "notes": "Version of \"Industry Baby\" with more autotune and instrumental differences. Was previewed in the Montero trailer, and has been dubbed the \"Trailer Demo\".", "length": "37.46", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/8d99565726aa24047fdaefe4d205c05c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8d99565726aa24047fdaefe4d205c05c\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V23]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"Version of \\\"Industry Baby\\\" with more autotune and instrumental differences. <PERSON> previewed in the Montero trailer, and has been dubbed the \\\"Trailer Demo\\\".\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e85605595e9e1ac4ec7fa8ea9ddbaeb7\", \"url\": \"https://api.pillowcase.su/api/download/e85605595e9e1ac4ec7fa8ea9ddbaeb7\", \"size\": \"3.02 MB\", \"duration\": 37.46}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "3.02 MB"}, {"id": "industry-baby-239", "name": "Lil Nas X - Industry Baby [V24]", "artists": ["<PERSON>"], "producers": ["Take A Daytrip", "Kanye West", "<PERSON>"], "notes": "Another version of \"Industry Baby\" that was previewed on a trailer for the song. Has a different mix.", "length": "29.86", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/5aed5d6d4fc2af4dae1abedce3d6ad3d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5aed5d6d4fc2af4dae1abedce3d6ad3d\", \"key\": \"Industry Baby\", \"title\": \"Lil Nas X - Industry Baby [V24]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Tel Aviv\", \"The Industry Baby\"], \"description\": \"Another version of \\\"Industry Baby\\\" that was previewed on a trailer for the song. Has a different mix.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4fe3ef2b85ee1265683fdc5906640096\", \"url\": \"https://api.pillowcase.su/api/download/4fe3ef2b85ee1265683fdc5906640096\", \"size\": \"2.89 MB\", \"duration\": 29.86}", "aliases": ["Tel Aviv", "The Industry Baby"], "size": "2.89 MB"}, {"id": "industry-baby-240", "name": "<PERSON> Nas X - Industry Baby (Extended)", "artists": ["<PERSON>"], "producers": ["Take A Daytrip", "Kanye West", "<PERSON>"], "notes": "Random studio session creating the extended version of \"Industry Baby\".", "length": "27.66", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/fc0f48b6818b2768a2fdab1483eecc66", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fc0f48b6818b2768a2fdab1483eecc66\", \"key\": \"Industry Baby (Extended)\", \"title\": \"Lil Nas X - Industry Baby (Extended)\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Industry Baby\", \"Tel Aviv\", \"The Industry Baby\"], \"description\": \"Random studio session creating the extended version of \\\"Industry Baby\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"614dd1d2b4c5d776d38855b289983a7d\", \"url\": \"https://api.pillowcase.su/api/download/614dd1d2b4c5d776d38855b289983a7d\", \"size\": \"2.86 MB\", \"duration\": 27.66}", "aliases": ["Industry Baby", "Tel Aviv", "The Industry Baby"], "size": "2.86 MB"}, {"id": "delresto", "name": "<PERSON> - <PERSON><PERSON><PERSON> [V5]", "artists": ["Kanye West"], "producers": ["Hit-Boy"], "notes": "Mumble freestyle that is likely for \"Delresto\". <PERSON> mumbles a short verse idea, and then grumbles the melody. Recorded on the same day the verse from the zip was. Recorded in the Pio Pico studio; <PERSON><PERSON><PERSON><PERSON> and <PERSON> are directly seen in the video. Video leaked as a \"Karate Kid\" groupbuy bonus.", "length": "", "fileDate": 16716672, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/6772b30b23dec2f71b96d2c53a28cc69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6772b30b23dec2f71b96d2c53a28cc69\", \"key\": \"<PERSON><PERSON><PERSON>\", \"title\": \"<PERSON> [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Hit-Boy)\", \"aliases\": [\"I Had To Learn\", \"DELRESTO (ECHOES)\"], \"description\": \"Mumble freestyle that is likely for \\\"Delresto\\\". <PERSON> mumbles a short verse idea, and then grumbles the melody. Recorded on the same day the verse from the zip was. Recorded in the Pio Pico studio; <PERSON><PERSON><PERSON><PERSON> and <PERSON> are directly seen in the video. Video leaked as a \\\"Karate Kid\\\" groupbuy bonus.\", \"date\": 16716672, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["I Had To Learn", "DELRESTO (ECHOES)"], "size": ""}, {"id": "ski", "name": "Young Stoner Life, Young Thug & Gunna - Ski (Remix)", "artists": ["Kanye West"], "producers": ["BabyWave", "Outtatown", "Wheezy"], "notes": "OG Filename: 210607 Villa Ski Freestyle\nRecorded in June 2021, and features a lot of mumble. It's unknown if a more finished version exists. Original snippet leaked October 19, 2022. No official bounce.", "length": "93.69", "fileDate": 17063136, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "donda-v2", "originalUrl": "https://pillowcase.su/f/87bedd47b8677c559e79aaf9c93f721c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/87bedd47b8677c559e79aaf9c93f721c\", \"key\": \"<PERSON> (Remix)\", \"title\": \"<PERSON> Stoner Life, <PERSON> Thug & <PERSON><PERSON> (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>Wave, Outtatown & Wheezy)\", \"description\": \"OG Filename: 210607 Villa Ski Freestyle\\nRecorded in June 2021, and features a lot of mumble. It's unknown if a more finished version exists. Original snippet leaked October 19, 2022. No official bounce.\", \"date\": 17063136, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"16519f78bc7e6ba0fa8bebf98bc700b6\", \"url\": \"https://api.pillowcase.su/api/download/16519f78bc7e6ba0fa8bebf98bc700b6\", \"size\": \"8.09 MB\", \"duration\": 93.69}", "aliases": [], "size": "8.09 MB"}]}