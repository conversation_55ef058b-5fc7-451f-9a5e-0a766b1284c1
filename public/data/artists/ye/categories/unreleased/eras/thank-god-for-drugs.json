{"id": "thank-god-for-drugs", "name": "Thank God For Drugs", "description": "Before <PERSON><PERSON><PERSON> chose the name <PERSON><PERSON><PERSON> for his sixth solo album, the name was Thank God For Drugs. Recording started in 2012 and accelerated in early 2013, with <PERSON><PERSON><PERSON> and his producers producing material very quickly. The tracklist for the album boasted 20 songs, with around 3.5 hours of rough material created for the album. After changing the album's name to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> recruited <PERSON> to cut down this material and take the music in a minimal direction.", "backgroundColor": "rgb(239, 239, 239)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17GdIR--Yqz2lK7xaLQvbuIXaUSvIiHUkGvynRFxxKpHS-gb8KDDBsRg5cu467RPNnLD_3gnRKbfH-VZVro5v1dSGWLrLcCY189YB1tvB1bJkZJ_OWwagcfqyyZr4pLyKOe6EFq99KdP--di0iI?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "awesome", "name": "Awesome [V2]", "artists": [], "producers": ["Hit-Boy", "Sak Pase"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>\nHas no feature vocals, a rougher sample chop, and different drums from other versions.", "length": "255.88", "fileDate": 16723584, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/5e2b0359ec28c5353f829645953ddf72", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e2b0359ec28c5353f829645953ddf72\", \"key\": \"Awesome\", \"title\": \"Awesome [V2]\", \"artists\": \"(prod. <PERSON>-<PERSON> & <PERSON>k <PERSON>)\", \"description\": \"OG Filename: KW-<PERSON>wes<PERSON>-Ruff\\nHas no feature vocals, a rougher sample chop, and different drums from other versions.\", \"date\": 16723584, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"55c3d03779bd972bd01d504e018dbe4e\", \"url\": \"https://api.pillowcase.su/api/download/55c3d03779bd972bd01d504e018dbe4e\", \"size\": \"4.38 MB\", \"duration\": 255.88}", "aliases": [], "size": "4.38 MB"}, {"id": "awesome-2", "name": "Awesome [V3]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Hit-Boy", "Sak Pase"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> (3.31.13)\nSimilar to the March 28th version, now featuring <PERSON> and <PERSON>.", "length": "255.73", "fileDate": 17017344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/c4f3b47d8ad5671cc458c3c8af2ff70f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4f3b47d8ad5671cc458c3c8af2ff70f\", \"key\": \"Awesome\", \"title\": \"Awesome [V3]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON>-<PERSON> & Sak <PERSON>)\", \"description\": \"OG Filename: KW - Awesome Ref (3.31.13)\\nSimilar to the March 28th version, now featuring <PERSON> and <PERSON>.\", \"date\": 17017344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"898ab669ee69027a787a97385ecdd2bf\", \"url\": \"https://api.pillowcase.su/api/download/898ab669ee69027a787a97385ecdd2bf\", \"size\": \"4.38 MB\", \"duration\": 255.73}", "aliases": [], "size": "4.38 MB"}, {"id": "awesome-3", "name": "⭐ Awesome [V4]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Hit-Boy", "Sak Pase"], "notes": "OG Filename: KW AWESOME 04.09.13\nHas vocals from <PERSON> & <PERSON>, with different drums and production compared to other versions. Leaked along with stems.", "length": "274.67", "fileDate": 16723584, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/14f7aa5342e871454375fa7b7d007647", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/14f7aa5342e871454375fa7b7d007647\", \"key\": \"Awesome\", \"title\": \"\\u2b50 Awesome [V4]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON>-<PERSON> & Sak Pa<PERSON>)\", \"description\": \"OG Filename: KW AWESOME 04.09.13\\nHas vocals from <PERSON> & <PERSON>, with different drums and production compared to other versions. Leaked along with stems.\", \"date\": 16723584, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0397e3b6557be91c35dff91a5ce56b50\", \"url\": \"https://api.pillowcase.su/api/download/0397e3b6557be91c35dff91a5ce56b50\", \"size\": \"4.68 MB\", \"duration\": 274.67}", "aliases": [], "size": "4.68 MB"}, {"id": "awesome-4", "name": "Awesome [V5]", "artists": ["<PERSON>", "<PERSON>"], "producers": [], "notes": "Version dated May 1st, 2013. Nothing else is known.", "length": "9.48", "fileDate": 16850592, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/1c5e7deff716510265da66890cae01eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c5e7deff716510265da66890cae01eb\", \"key\": \"Awesome\", \"title\": \"Awesome [V5]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"Version dated May 1st, 2013. Nothing else is known.\", \"date\": 16850592, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8eb66e47d67b12e41d52c7315c7da4bf\", \"url\": \"https://api.pillowcase.su/api/download/8eb66e47d67b12e41d52c7315c7da4bf\", \"size\": \"437 kB\", \"duration\": 9.48}", "aliases": [], "size": "437 kB"}, {"id": "awesome-5", "name": "✨ Awesome [V6]", "artists": ["<PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: KW - Awesome 4.5.13\nHas vastly different production from other TGFD-era versions. Filename claims it's dated 4/5/13, but it's actually from 5/4/13. Found inside <PERSON><PERSON><PERSON>'s wedding playlist, but it's not the version featuring <PERSON><PERSON>.", "length": "274.61", "fileDate": 16724448, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/12c0981c4957260a3293c326455ec6d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/12c0981c4957260a3293c326455ec6d1\", \"key\": \"Awesome\", \"title\": \"\\u2728 Awesome [V6]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW - Awesome 4.5.13\\nHas vastly different production from other TGFD-era versions. Filename claims it's dated 4/5/13, but it's actually from 5/4/13. Found inside <PERSON><PERSON><PERSON>'s wedding playlist, but it's not the version featuring Ty<PERSON>.\", \"date\": 16724448, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9074d07d3407580df7204c1e7e878bc5\", \"url\": \"https://api.pillowcase.su/api/download/9074d07d3407580df7204c1e7e878bc5\", \"size\": \"4.68 MB\", \"duration\": 274.61}", "aliases": [], "size": "4.68 MB"}, {"id": "black-skinhead", "name": "Black Skinhead [V2]", "artists": [], "producers": ["Daft Punk", "Gesaffelstein"], "notes": "OG Filename: BSH GESA\nHas mumble, minor production differencies, and <PERSON><PERSON><PERSON> growling in the background.", "length": "230.77", "fileDate": 15712704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/0a94f91b9b6e0de9e7441725fc6b3a3f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a94f91b9b6e0de9e7441725fc6b3a3f\", \"key\": \"Black Skinhead\", \"title\": \"Black Skinhead [V2]\", \"artists\": \"(prod. Daft Punk & Gesaffelstein)\", \"description\": \"OG Filename: BSH GESA\\nHas mumble, minor production differencies, and <PERSON><PERSON><PERSON> growling in the background.\", \"date\": 15712704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"52232cbacaa4bdb95bf28028a9ada2fa\", \"url\": \"https://api.pillowcase.su/api/download/52232cbacaa4bdb95bf28028a9ada2fa\", \"size\": \"3.98 MB\", \"duration\": 230.77}", "aliases": [], "size": "3.98 MB"}, {"id": "black-skinhead-7", "name": "Black Skinhead [V3]", "artists": [], "producers": ["Daft Punk", "Gesaffelstein"], "notes": "Stem bounce of an even further along version.", "length": "204.93", "fileDate": 16956000, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/9b07d6cadc102afc28a382539760fb8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9b07d6cadc102afc28a382539760fb8d\", \"key\": \"Black Skinhead\", \"title\": \"Black Skinhead [V3]\", \"artists\": \"(prod. Daft Punk & Gesaffelstein)\", \"description\": \"Stem bounce of an even further along version.\", \"date\": 16956000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ad0efdfb335e528c37f4c7080584964a\", \"url\": \"https://api.pillowcase.su/api/download/ad0efdfb335e528c37f4c7080584964a\", \"size\": \"3.56 MB\", \"duration\": 204.93}", "aliases": [], "size": "3.56 MB"}, {"id": "black-skinhead-8", "name": "Black Skinhead [V4]", "artists": [], "producers": ["Daft Punk", "Gesaffelstein"], "notes": "Features a further along instrumental compared to the previous version.", "length": "203.18", "fileDate": 15596064, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/66203d3e26b48549334c42410e6c5e75", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/66203d3e26b48549334c42410e6c5e75\", \"key\": \"Black Skinhead\", \"title\": \"Black Skinhead [V4]\", \"artists\": \"(prod. Daft Punk & Gesaffelstein)\", \"description\": \"Features a further along instrumental compared to the previous version.\", \"date\": 15596064, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3dc6851e176b94e81d17080f889b780c\", \"url\": \"https://api.pillowcase.su/api/download/3dc6851e176b94e81d17080f889b780c\", \"size\": \"3.54 MB\", \"duration\": 203.18}", "aliases": [], "size": "3.54 MB"}, {"id": "black-skinhead-9", "name": "Black Skinhead [V6]", "artists": [], "producers": ["Daft Punk"], "notes": "Pre-SNL version. Has further along production, and a new verse.", "length": "11.4", "fileDate": 16658784, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/2c269bb8a600732629330df977bbdb20", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2c269bb8a600732629330df977bbdb20\", \"key\": \"Black Skinhead\", \"title\": \"Black Skinhead [V6]\", \"artists\": \"(prod. Daft Punk)\", \"description\": \"Pre-SNL version. Has further along production, and a new verse.\", \"date\": 16658784, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4aa34a3de96ca438d2646bb1ad51b00d\", \"url\": \"https://api.pillowcase.su/api/download/4aa34a3de96ca438d2646bb1ad51b00d\", \"size\": \"467 kB\", \"duration\": 11.4}", "aliases": [], "size": "467 kB"}, {"id": "black-skinhead-10", "name": "Black Skinhead [V7]", "artists": [], "producers": ["Daft Punk"], "notes": "OG Filename: <PERSON>W <PERSON> <PERSON> (5.17.13)\nHas an alternate vocal take, and <PERSON><PERSON><PERSON> filling in some of the yelling samples.", "length": "212.19", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/6602d78460f03f744f87ff2e5a4ce08a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6602d78460f03f744f87ff2e5a4ce08a\", \"key\": \"Black Skinhead\", \"title\": \"Black Skinhead [V7]\", \"artists\": \"(prod. Daft Punk)\", \"description\": \"OG Filename: KW - Black Skinhead Ref (5.17.13)\\nHas an alternate vocal take, and <PERSON><PERSON><PERSON> filling in some of the yelling samples.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9e5866e168c9dc6356ea2e9fc987337c\", \"url\": \"https://api.pillowcase.su/api/download/9e5866e168c9dc6356ea2e9fc987337c\", \"size\": \"3.68 MB\", \"duration\": 212.19}", "aliases": [], "size": "3.68 MB"}, {"id": "black-skinhead-11", "name": "Black Skinhead [V9]", "artists": [], "producers": ["Daft Punk", "Gesaffelstein"], "notes": "Version with alternate production, sold by Say<PERSON>. Could potentially be the SNL version, or recorded soon after it.", "length": "12.16", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/9184f6d4f30f5a3d83fbe7be60e72c79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9184f6d4f30f5a3d83fbe7be60e72c79\", \"key\": \"Black Skinhead\", \"title\": \"Black Skinhead [V9]\", \"artists\": \"(prod. Daft Punk & Gesaffelstein)\", \"description\": \"Version with alternate production, sold by Saya. Could potentially be the SNL version, or recorded soon after it.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"824bdd9bab9dca84a950da4c921980fc\", \"url\": \"https://api.pillowcase.su/api/download/824bdd9bab9dca84a950da4c921980fc\", \"size\": \"480 kB\", \"duration\": 12.16}", "aliases": [], "size": "480 kB"}, {"id": "blood-on-the-leaves", "name": "Blood On The Leaves [V1]", "artists": [], "producers": ["Kanye West", "Hudson Mohawke", "Lunice"], "notes": "OG Filename: <PERSON>W - Blood On The Leaves Ref (3.11.13)\nEarliest known version. Has only around 1:07 of actual vocals and the rest is just open verses. Heavily samples \"R U Ready\" by TNGHT and \"Strange Fruit\" by <PERSON>.", "length": "370.13", "fileDate": 16956000, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/3b4ddf312598bb89260a8bfa36f844a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3b4ddf312598bb89260a8bfa36f844a1\", \"key\": \"Blood On The Leaves\", \"title\": \"Blood On The Leaves [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, Hudson Mohawke & Lunice)\", \"description\": \"OG Filename: KW - Blood On The Leaves Ref (3.11.13)\\nEarliest known version. Has only around 1:07 of actual vocals and the rest is just open verses. Heavily samples \\\"R U Ready\\\" by TNGHT and \\\"Strange Fruit\\\" by <PERSON>.\", \"date\": 16956000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8f55f1829edc92d891a0c24ae2cc6ce8\", \"url\": \"https://api.pillowcase.su/api/download/8f55f1829edc92d891a0c24ae2cc6ce8\", \"size\": \"6.21 MB\", \"duration\": 370.13}", "aliases": [], "size": "6.21 MB"}, {"id": "blood-on-the-leaves-13", "name": "Blood On The Leaves [V2]", "artists": [], "producers": ["Kanye West", "Hudson Mohawke", "Lunice"], "notes": "OG Filename: Blood on the Leaves 86 bpm\nStem bounce, has no vocals.", "length": "369.42", "fileDate": 16930080, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/35f0f1294134299492ecd7c784516521", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/35f0f1294134299492ecd7c784516521\", \"key\": \"Blood On The Leaves\", \"title\": \"Blood On The Leaves [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, Hudson Mohawke & Lunice)\", \"description\": \"OG Filename: Blood on the Leaves 86 bpm\\nStem bounce, has no vocals.\", \"date\": 16930080, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3457307e5c7b41356c70dcd9de7749ad\", \"url\": \"https://api.pillowcase.su/api/download/3457307e5c7b41356c70dcd9de7749ad\", \"size\": \"6.2 MB\", \"duration\": 369.42}", "aliases": [], "size": "6.2 MB"}, {"id": "blood-on-the-leaves-14", "name": "Blood On The Leaves [V3]", "artists": [], "producers": ["Kanye West", "Hudson Mohawke", "Lunice"], "notes": "OG Filename: KW - Blood On The Leaves Ref (4.30.13)\nHas a mumble vocal take and an open verse in the middle of the song, some of which was reused in released version.", "length": "369.18", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/a50334602925d533eeec8328578aeda4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a50334602925d533eeec8328578aeda4\", \"key\": \"Blood On The Leaves\", \"title\": \"Blood On The Leaves [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, Hudson Mohawke & Lunice)\", \"description\": \"OG Filename: KW - Blood On The Leaves Ref (4.30.13)\\nHas a mumble vocal take and an open verse in the middle of the song, some of which was reused in released version.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a18ef74edc39652eb44e072a11bdd8bb\", \"url\": \"https://api.pillowcase.su/api/download/a18ef74edc39652eb44e072a11bdd8bb\", \"size\": \"6.2 MB\", \"duration\": 369.18}", "aliases": [], "size": "6.2 MB"}, {"id": "tangerine-dream", "name": "Tangerine Dream [V1]", "artists": [], "producers": ["Daft Punk"], "notes": "OG Filename: KW - Daft - Tangerine Dream Sample KW Re(12.20.12)\nOriginal reference over the Tangerine Dream sample for what would become \"Can't Get Over Me\". Samples \"Hyperborea\" by Tangerine Dream.", "length": "172.56", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/52defc318e1c30c9a35691eeae79a3a2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/52defc318e1c30c9a35691eeae79a3a2\", \"key\": \"Tangerine Dream\", \"title\": \"Tangerine Dream [V1]\", \"artists\": \"(prod. Daft Punk)\", \"aliases\": [\"Can't Get Over Me\", \"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Daft - Tangerine Dream Sample KW Re(12.20.12)\\nOriginal reference over the Tangerine Dream sample for what would become \\\"Can't Get Over Me\\\". <PERSON><PERSON> \\\"Hyperborea\\\" by Tangerine Dream.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a32fa62d7c8663db890253113f871533\", \"url\": \"https://api.pillowcase.su/api/download/a32fa62d7c8663db890253113f871533\", \"size\": \"3.05 MB\", \"duration\": 172.56}", "aliases": ["Can't Get Over Me", "Fall Out Of Heaven"], "size": "3.05 MB"}, {"id": "tangerine-dream-16", "name": "Tangerine Dream [V2]", "artists": [], "producers": ["Daft Punk", "<PERSON>"], "notes": "OG Filename: KW - <PERSON><PERSON><PERSON> (1.18.13)\nHas new drums and is longer than the previous version, sharing a structure close to that of later versions.", "length": "304.95", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/0ffeea95d436da190f0a36c5a41e6b52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0ffeea95d436da190f0a36c5a41e6b52\", \"key\": \"Tangerine Dream\", \"title\": \"Tangerine Dream [V2]\", \"artists\": \"(prod. <PERSON><PERSON> Punk & Travis <PERSON>)\", \"aliases\": [\"Can't Get Over Me\", \"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Tangerine Dream (1.18.13)\\nHas new drums and is longer than the previous version, sharing a structure close to that of later versions.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ddbcf2188f8881ded944e42e3a2b100e\", \"url\": \"https://api.pillowcase.su/api/download/ddbcf2188f8881ded944e42e3a2b100e\", \"size\": \"5.17 MB\", \"duration\": 304.95}", "aliases": ["Can't Get Over Me", "Fall Out Of Heaven"], "size": "5.17 MB"}, {"id": "tangerine-dream-17", "name": "Tangerine Dream [V3]", "artists": [], "producers": ["Daft Punk", "<PERSON>"], "notes": "OG Filename: KW - Tangerine Dream Ref (1.19.13)\nIdentical to the previous version, but the drums are slightly enhanced.", "length": "304.95", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/745bac7802c9a9c897d572ae9b291668", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/745bac7802c9a9c897d572ae9b291668\", \"key\": \"Tangerine Dream\", \"title\": \"Tangerine Dream [V3]\", \"artists\": \"(prod. <PERSON><PERSON> Punk & Travis <PERSON>)\", \"aliases\": [\"Can't Get Over Me\", \"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Tangerine Dream Ref (1.19.13)\\nIdentical to the previous version, but the drums are slightly enhanced.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"73376d6bada5f30dd4978323e0733b0e\", \"url\": \"https://api.pillowcase.su/api/download/73376d6bada5f30dd4978323e0733b0e\", \"size\": \"5.17 MB\", \"duration\": 304.95}", "aliases": ["Can't Get Over Me", "Fall Out Of Heaven"], "size": "5.17 MB"}, {"id": "can-t-get-over-me", "name": "Can't Get Over Me [V4]", "artists": [], "producers": ["Daft Punk"], "notes": "OG Filename: KW - Can't Get Over Me Ref (1.26.13)\nIdentical to the previous versions, but drums were removed, leaving only those that were already in the sample.", "length": "304.95", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/e70ef0d6d132cdc949c2aa3c9c0317a8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e70ef0d6d132cdc949c2aa3c9c0317a8\", \"key\": \"Can't Get Over Me\", \"title\": \"Can't Get Over Me [V4]\", \"artists\": \"(prod. Daft Punk)\", \"aliases\": [\"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Can't Get Over Me Ref (1.26.13)\\nIdentical to the previous versions, but drums were removed, leaving only those that were already in the sample.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3bb90f8a5871e6b028a399bb04784f5b\", \"url\": \"https://api.pillowcase.su/api/download/3bb90f8a5871e6b028a399bb04784f5b\", \"size\": \"5.17 MB\", \"duration\": 304.95}", "aliases": ["Fall Out Of Heaven"], "size": "5.17 MB"}, {"id": "can-t-get-over-me-19", "name": "Can't Get Over Me [V5]", "artists": [], "producers": ["Daft Punk"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> - Can't Get Over Me (Add Plane sfx)\nSessions for this version can be found in the leaked session folder.", "length": "304.47", "fileDate": 16646688, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/cb2369b3b08d144d1cd348358b47e045", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cb2369b3b08d144d1cd348358b47e045\", \"key\": \"Can't Get Over Me\", \"title\": \"Can't Get Over Me [V5]\", \"artists\": \"(prod. Daft Punk)\", \"aliases\": [\"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Daft - Can't Get Over Me (Add Plane sfx)\\nSessions for this version can be found in the leaked session folder.\", \"date\": 16646688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7642dc14de0ce6b53c6b1aea78b0a8a4\", \"url\": \"https://api.pillowcase.su/api/download/7642dc14de0ce6b53c6b1aea78b0a8a4\", \"size\": \"5.16 MB\", \"duration\": 304.47}", "aliases": ["Fall Out Of Heaven"], "size": "5.16 MB"}, {"id": "can-t-get-over-me-20", "name": "Can't Get Over Me [V8]", "artists": [], "producers": ["Daft Punk"], "notes": "OG Filename: KW - Can't Get Over Me (KW Tracking)\nSessions for this version can be found in the leaked session folder.", "length": "305.13", "fileDate": 16646688, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/68d3e9da1cd924e62d2cea1637f2938f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68d3e9da1cd924e62d2cea1637f2938f\", \"key\": \"Can't Get Over Me\", \"title\": \"Can't Get Over Me [V8]\", \"artists\": \"(prod. Daft Punk)\", \"aliases\": [\"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Can't Get Over Me (KW Tracking)\\nSessions for this version can be found in the leaked session folder.\", \"date\": 16646688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a72541cf6c0bfca3fb8c5aa359eaf3a3\", \"url\": \"https://api.pillowcase.su/api/download/a72541cf6c0bfca3fb8c5aa359eaf3a3\", \"size\": \"5.17 MB\", \"duration\": 305.13}", "aliases": ["Fall Out Of Heaven"], "size": "5.17 MB"}, {"id": "can-t-get-over-me-21", "name": "Can't Get Over Me [V9]", "artists": ["<PERSON>"], "producers": ["Daft Punk"], "notes": "OG Filename: KW - Can't Get Over Me.2 (Add Justin Parts)\nSessions for this version can be found in the leaked session folder.", "length": "304.47", "fileDate": 16646688, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/19f7c30f31cfc3f3d6a97393e8206428", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/19f7c30f31cfc3f3d6a97393e8206428\", \"key\": \"Can't Get Over Me\", \"title\": \"Can't Get Over Me [V9]\", \"artists\": \"(feat. <PERSON>) (prod. Daft Punk)\", \"aliases\": [\"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Can't Get Over Me.2 (Add Justin Parts)\\nSessions for this version can be found in the leaked session folder.\", \"date\": 16646688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d7d4425ce60a2a41a86775247206afab\", \"url\": \"https://api.pillowcase.su/api/download/d7d4425ce60a2a41a86775247206afab\", \"size\": \"5.16 MB\", \"duration\": 304.47}", "aliases": ["Fall Out Of Heaven"], "size": "5.16 MB"}, {"id": "can-t-get-over-me-22", "name": "⭐ Can't Get Over Me [V11]", "artists": ["The WRLDFMS <PERSON>", "<PERSON>", "The-Dream"], "producers": ["Daft Punk", "<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> Get Over Me Ref (4.30.13)\nVersion with a feature from The-Dream. This version doesn't have a <PERSON><PERSON><PERSON> verse, only reusing his take on the hook during one section of the song. <PERSON><PERSON><PERSON>'s vocals are the same as \"Fall Out Of Heaven\", but with a slower tempo. Features vocals from <PERSON> as well.", "length": "315.03", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/29100b9b783f952def8c984f5bd74e4b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/29100b9b783f952def8c984f5bd74e4b\", \"key\": \"Can't Get Over Me\", \"title\": \"\\u2b50 Can't Get Over Me [V11]\", \"artists\": \"(feat. The WRLDFMS <PERSON>, <PERSON> Iver & The-Dream) (prod. Daft Punk & Travis <PERSON>)\", \"aliases\": [\"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Cant Get Over Me Ref (4.30.13)\\nVersion with a feature from The-Dream. This version doesn't have a Kanye verse, only reusing his take on the hook during one section of the song. <PERSON><PERSON><PERSON>'s vocals are the same as \\\"Fall Out Of Heaven\\\", but with a slower tempo. Features vocals from <PERSON> as well.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8b28a9750d6774bcfb5054375d8342a3\", \"url\": \"https://api.pillowcase.su/api/download/8b28a9750d6774bcfb5054375d8342a3\", \"size\": \"5.33 MB\", \"duration\": 315.03}", "aliases": ["Fall Out Of Heaven"], "size": "5.33 MB"}, {"id": "emotions", "name": "Emotions", "artists": [], "producers": ["Sak Pase"], "notes": "OG Filename: RAP_CHANTAL_EMOTIONS_093012\n<PERSON><PERSON> reference track. Song was later given to <PERSON> for her 2014 album A.K.A.", "length": "252.84", "fileDate": 16956864, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/51bb48743e612e1e4100008dfdaba365", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/51bb48743e612e1e4100008dfdaba365\", \"key\": \"Emotions\", \"title\": \"Emotions\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: RAP_CHANTAL_EMOTIONS_093012\\nChan<PERSON> Kreviazu<PERSON> reference track. Song was later given to <PERSON> for her 2014 album A.K.A.\", \"date\": 16956864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c76481bf6cabe943d8d02145f7449aa\", \"url\": \"https://api.pillowcase.su/api/download/5c76481bf6cabe943d8d02145f7449aa\", \"size\": \"4.33 MB\", \"duration\": 252.84}", "aliases": [], "size": "4.33 MB"}, {"id": "good-things-don-t-last", "name": "Good Things Don't Last [V1]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: KW - Good Things Don't Last Ref (3.10.13)\nThank God For Drugs era throwaway. <PERSON> mumble singing. This version was later brought back for Lost Yeezus.", "length": "140.34", "fileDate": 14749344, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/8c446e0196f1cc158bc96866dde8d9db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8c446e0196f1cc158bc96866dde8d9db\", \"key\": \"Good Things Don't Last\", \"title\": \"Good Things Don't Last [V1]\", \"artists\": \"(prod. 88-Keys)\", \"aliases\": [\"Life Is This Way\"], \"description\": \"OG Filename: KW - Good Things Don't Last Ref (3.10.13)\\nThank God For Drugs era throwaway. Has mumble singing. This version was later brought back for Lost Yeezus.\", \"date\": 14749344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a1e9985cc4e72fc6dd1b5d1a17c5ae02\", \"url\": \"https://api.pillowcase.su/api/download/a1e9985cc4e72fc6dd1b5d1a17c5ae02\", \"size\": \"2.53 MB\", \"duration\": 140.34}", "aliases": ["Life Is This Way"], "size": "2.53 MB"}, {"id": "guilt-trip", "name": "⭐ Guilt Trip [V4] ", "artists": ["<PERSON>"], "producers": ["S1", "MIKE DEAN", "Kanye West", "Daft Punk", "<PERSON>"], "notes": "OG Filename: <PERSON>W - <PERSON><PERSON> <PERSON> (3.31.13)\nMarch version. Has alternate production and more vocals from <PERSON> and <PERSON><PERSON><PERSON>. Also has a slightly faster arrangement. This production can be heard in release through <PERSON><PERSON><PERSON>'s bleed. Samples \"Fly Like An Eagle\" by <PERSON>.", "length": "259.42", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/05af4713d18faa2c1b9594a5ac642e86", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/05af4713d18faa2c1b9594a5ac642e86\", \"key\": \"Guilt Trip\", \"title\": \"\\u2b50 Guilt Trip [V4] \", \"artists\": \"(feat. <PERSON>) (prod. S1, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> Punk & Travis <PERSON>)\", \"description\": \"OG Filename: KW - Guilt Trip Ref (3.31.13)\\nMarch version. Has alternate production and more vocals from <PERSON> and <PERSON><PERSON><PERSON>. Also has a slightly faster arrangement. This production can be heard in release through <PERSON><PERSON><PERSON>'s bleed. Samples \\\"Fly Like An Eagle\\\" by <PERSON>.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4f9798e6d8eebb4c990d1495b85ee47d\", \"url\": \"https://api.pillowcase.su/api/download/4f9798e6d8eebb4c990d1495b85ee47d\", \"size\": \"4.44 MB\", \"duration\": 259.42}", "aliases": [], "size": "4.44 MB"}, {"id": "guilt-trip-26", "name": "Guilt Trip [V5] ", "artists": ["<PERSON>"], "producers": ["S1", "MIKE DEAN", "Kanye West", "Daft Punk", "<PERSON>"], "notes": "OG Filename: GUILT TRIP 68.5\nStem bounce, with production from the March 31st version - but quieter.", "length": "270.5", "fileDate": 16930080, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/723fc65c8e448dc8328c843e6c13ebf3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/723fc65c8e448dc8328c843e6c13ebf3\", \"key\": \"Guilt Trip\", \"title\": \"Guilt Trip [V5] \", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Daft Punk & Travis <PERSON>)\", \"description\": \"OG Filename: GUILT TRIP 68.5\\nStem bounce, with production from the March 31st version - but quieter.\", \"date\": 16930080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1bbe2b1c1c7fef234625e008125a57b4\", \"url\": \"https://api.pillowcase.su/api/download/1bbe2b1c1c7fef234625e008125a57b4\", \"size\": \"4.61 MB\", \"duration\": 270.5}", "aliases": [], "size": "4.61 MB"}, {"id": "guilt-trip-27", "name": "Guilt Trip [V6]", "artists": ["<PERSON>"], "producers": ["S1", "MIKE DEAN", "Kanye West", "<PERSON>"], "notes": "OG Filename: KW - Guilt Trip Ref (4.13.13)\nVersion with minor production and mixing differences compared to release.", "length": "244.48", "fileDate": 16714944, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/e9ed057607cf6fd85a82d6469e85698f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9ed057607cf6fd85a82d6469e85698f\", \"key\": \"Guilt Trip\", \"title\": \"Guilt Trip [V6]\", \"artists\": \"(feat. <PERSON>) (prod. S1, <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW - Guilt Trip Ref (4.13.13)\\nVersion with minor production and mixing differences compared to release.\", \"date\": 16714944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8f7793052b268f92daa330af3e710141\", \"url\": \"https://api.pillowcase.su/api/download/8f7793052b268f92daa330af3e710141\", \"size\": \"4.2 MB\", \"duration\": 244.48}", "aliases": [], "size": "4.2 MB"}, {"id": "phone-home", "name": "Phone Home [V2]", "artists": [], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "Bon Iver reference track. Snippet leaked May 31st, 2024.", "length": "7.67", "fileDate": 17171136, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/fa1219f21e72472576eaeefd4736beee", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa1219f21e72472576eaeefd4736beee\", \"key\": \"Phone Home\", \"title\": \"Phone Home [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Hold My Liquor\"], \"description\": \"Bon Iver reference track. Snippet leaked May 31st, 2024.\", \"date\": 17171136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c5e253f146322c0d50f073efb23dc936\", \"url\": \"https://api.pillowcase.su/api/download/c5e253f146322c0d50f073efb23dc936\", \"size\": \"408 kB\", \"duration\": 7.67}", "aliases": ["Hold My Liquor"], "size": "408 kB"}, {"id": "hold-my-liquor", "name": "Hold My Liquor [V3]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "OG Filename: <PERSON>W - Hold My <PERSON>uo<PERSON> (3.13.13)\nHas additional <PERSON> vocals, and alternate production.", "length": "324.31", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/cd5064104c80773ca43950796fbd0b06", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cd5064104c80773ca43950796fbd0b06\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Hold My Liquor Ref (3.13.13)\\nHas additional <PERSON> Iver vocals, and alternate production.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"082cbf0300a1bc9c207daabf37226e8f\", \"url\": \"https://api.pillowcase.su/api/download/082cbf0300a1bc9c207daabf37226e8f\", \"size\": \"5.47 MB\", \"duration\": 324.31}", "aliases": [], "size": "5.47 MB"}, {"id": "hold-my-liquor-30", "name": "Hold My Liquor [V5]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "length": "3.24", "fileDate": 17399232, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/c67e7b8263039d217d71a9d4a2b4e8aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c67e7b8263039d217d71a9d4a2b4e8aa\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V5]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE <PERSON>)\", \"description\": \"A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.\", \"date\": 17399232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fc4ba01b70a6761ec3ef5a6b587b781c\", \"url\": \"https://api.pillowcase.su/api/download/fc4ba01b70a6761ec3ef5a6b587b781c\", \"size\": \"336 kB\", \"duration\": 3.24}", "aliases": [], "size": "336 kB"}, {"id": "hold-my-liquor-31", "name": "Hold My Liquor [V5]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "length": "5.2", "fileDate": 17399232, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/ad5f5b20ebd8baa9b8a1f7909206f9c6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad5f5b20ebd8baa9b8a1f7909206f9c6\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V5]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & <PERSON><PERSON>)\", \"description\": \"A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.\", \"date\": 17399232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8ae96db0a512e90411efcd599205356f\", \"url\": \"https://api.pillowcase.su/api/download/8ae96db0a512e90411efcd599205356f\", \"size\": \"368 kB\", \"duration\": 5.2}", "aliases": [], "size": "368 kB"}, {"id": "hold-my-liquor-32", "name": "Hold My Liquor [V5]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "length": "7.24", "fileDate": 17399232, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/cde14502a3c41b668a94679be3153b97", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cde14502a3c41b668a94679be3153b97\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V5]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE <PERSON>)\", \"description\": \"A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.\", \"date\": 17399232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"564f473600a492db4bdfb8869e8a5d4e\", \"url\": \"https://api.pillowcase.su/api/download/564f473600a492db4bdfb8869e8a5d4e\", \"size\": \"401 kB\", \"duration\": 7.24}", "aliases": [], "size": "401 kB"}, {"id": "hold-my-liquor-33", "name": "Hold My Liquor [V5]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "length": "10.27", "fileDate": 17399232, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/7a7ff039d9179ea67d6be8104f67a190", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a7ff039d9179ea67d6be8104f67a190\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V5]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & <PERSON>KE <PERSON>)\", \"description\": \"A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.\", \"date\": 17399232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"85cd7fb96374fe113902acff142dc1b3\", \"url\": \"https://api.pillowcase.su/api/download/85cd7fb96374fe113902acff142dc1b3\", \"size\": \"449 kB\", \"duration\": 10.27}", "aliases": [], "size": "449 kB"}, {"id": "hold-my-liquor-34", "name": "Hold My Liquor [V5]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "length": "25.44", "fileDate": 17399232, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/94f177a497627a914192e3a03576803f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/94f177a497627a914192e3a03576803f\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V5]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & <PERSON>KE <PERSON>)\", \"description\": \"A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.\", \"date\": 17399232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5d6590d555dfa8a659bd618bdfdd6936\", \"url\": \"https://api.pillowcase.su/api/download/5d6590d555dfa8a659bd618bdfdd6936\", \"size\": \"692 kB\", \"duration\": 25.44}", "aliases": [], "size": "692 kB"}, {"id": "hold-my-liquor-35", "name": "✨ Hold My Liquor [V6]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "OG Filename: KW - Hold My Liquor Ref (4.30.13)\nVersion with a different structure and an extended guitar solo on the outro. Was wrongfully named \"Yeezus\". Track 9 on an early copy of Yeez<PERSON>.", "length": "349.66", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/be092458189ea7be0b32b77b8e07876d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/be092458189ea7be0b32b77b8e07876d\", \"key\": \"Hold My Liquor\", \"title\": \"\\u2728 Hold My Liquor [V6]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE <PERSON>)\", \"description\": \"OG Filename: KW - Hold My Liquor Ref (4.30.13)\\nVersion with a different structure and an extended guitar solo on the outro. Was wrongfully named \\\"Yeezus\\\". Track 9 on an early copy of Yeezus.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f6a6964bac178307285934de38162f2\", \"url\": \"https://api.pillowcase.su/api/download/8f6a6964bac178307285934de38162f2\", \"size\": \"5.88 MB\", \"duration\": 349.66}", "aliases": [], "size": "5.88 MB"}, {"id": "hold-my-liquor-36", "name": "Hold My Liquor [V7]", "artists": ["Chief <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "Version played at the private Yeezus listening party on May 15th, 2013. Likely one of the last versions from the era, but the features and differences from previous versions are uncertain, though the person who was at the party said that he did not hear Chief <PERSON><PERSON> on the album. Likely mumble.", "length": "1.1", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/2003d25c72bb7e09e0b934163180c3ef", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2003d25c72bb7e09e0b934163180c3ef\", \"key\": \"Hold My Liquor\", \"title\": \"Hold My Liquor [V7]\", \"artists\": \"(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE <PERSON>)\", \"description\": \"Version played at the private Yeezus listening party on May 15th, 2013. Likely one of the last versions from the era, but the features and differences from previous versions are uncertain, though the person who was at the party said that he did not hear Chief <PERSON><PERSON> on the album. Likely mumble.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"abd137575e24f2a4ee5fdfb77a5bca10\", \"url\": \"https://api.pillowcase.su/api/download/abd137575e24f2a4ee5fdfb77a5bca10\", \"size\": \"302 kB\", \"duration\": 1.1}", "aliases": [], "size": "302 kB"}, {"id": "hollywood-be-thy-name", "name": "<PERSON><PERSON><PERSON> Be Thy Name [V1]", "artists": [], "producers": ["Sak Pase"], "notes": "OG Filename: RAP HollyWoodBeThyName 081612 90bom\nJustxn Paul reference track. A more developed version was officially released by Justxn Paul in 2014. Leaked after the \"Tulsa\" groupbuy.", "length": "192.05", "fileDate": 16336512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/0199a28f5d723ff1183ee8a0f3a66d07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0199a28f5d723ff1183ee8a0f3a66d07\", \"key\": \"<PERSON>Wood Be Thy Name\", \"title\": \"<PERSON>Wood Be Thy Name [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: RAP HollyWoodBeThyName 081612 90bom\\nJustxn Paul reference track. A more developed version was officially released by Justxn Paul in 2014. Leaked after the \\\"Tulsa\\\" groupbuy.\", \"date\": 16336512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"97aacbb1af69b0c9e61241b1005fe5d0\", \"url\": \"https://api.pillowcase.su/api/download/97aacbb1af69b0c9e61241b1005fe5d0\", \"size\": \"3.36 MB\", \"duration\": 192.05}", "aliases": [], "size": "3.36 MB"}, {"id": "hollywood-be-thy-name-38", "name": "<PERSON><PERSON><PERSON> Be Thy Name [V2]", "artists": [], "producers": ["Sak Pase", "<PERSON>"], "notes": "A snippet of an updated instrumental of the song, shown by <PERSON> to someone. Unknown if <PERSON><PERSON><PERSON> ever recorded on this, or if <PERSON><PERSON><PERSON> was still on the song. Same instrumental was released on \"Creme De La Crack\" by <PERSON><PERSON><PERSON> Paul.", "length": "40.92", "fileDate": 16993152, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/212f7cd0cdb96ca00fef14bc8679960b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/212f7cd0cdb96ca00fef14bc8679960b\", \"key\": \"HollyWood Be Thy Name\", \"title\": \"HollyWood Be Thy Name [V2]\", \"artists\": \"(prod. <PERSON><PERSON> & <PERSON>)\", \"description\": \"A snippet of an updated instrumental of the song, shown by <PERSON> to someone. Unknown if <PERSON><PERSON><PERSON> ever recorded on this, or if <PERSON><PERSON><PERSON> was still on the song. Same instrumental was released on \\\"Creme De La Crack\\\" by Justxn Paul.\", \"date\": 16993152, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e5d20a9a33d82698025b5a59c9c10d82\", \"url\": \"https://api.pillowcase.su/api/download/e5d20a9a33d82698025b5a59c9c10d82\", \"size\": \"940 kB\", \"duration\": 40.92}", "aliases": [], "size": "940 kB"}, {"id": "lord", "name": "Lord [V4] ", "artists": ["???"], "producers": [], "notes": "Version with vocals from an unknown artist. Snippet leaked November 30th, 2024.", "length": "14.3", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/cbfd33609b695c70c727b880cc5e0fa4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cbfd33609b695c70c727b880cc5e0fa4\", \"key\": \"Lord\", \"title\": \"Lord [V4] \", \"artists\": \"(feat. ???)\", \"aliases\": [\"I Am A God\"], \"description\": \"Version with vocals from an unknown artist. Snippet leaked November 30th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"90f99c89214ab7b81091308f6d988c77\", \"url\": \"https://api.pillowcase.su/api/download/90f99c89214ab7b81091308f6d988c77\", \"size\": \"514 kB\", \"duration\": 14.3}", "aliases": ["I Am A God"], "size": "514 kB"}, {"id": "lord-40", "name": "Lord [V5] ", "artists": ["The Weeknd", "<PERSON>"], "producers": ["Daft Punk"], "notes": "Version of \"I Am A God\" said to be called \"<PERSON>\". <PERSON> The Weeknd on the intro and adlibs from <PERSON>. Samples \"I'm A Fool To Want You\" by <PERSON>. Snippet leaked November 30th, 2024.", "length": "20.14", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/6d0466b6ec79833e00d28e88e28425b2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d0466b6ec79833e00d28e88e28425b2\", \"key\": \"Lord\", \"title\": \"Lord [V5] \", \"artists\": \"(feat. <PERSON> <PERSON><PERSON> & Travis <PERSON>) (prod. Daft Punk)\", \"aliases\": [\"I Am A God\"], \"description\": \"Version of \\\"I Am A God\\\" said to be called \\\"Lord\\\". Has The Weeknd on the intro and adlibs from <PERSON><PERSON> \\\"I'm A Fool To Want You\\\" by <PERSON>. Snippet leaked November 30th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"25b3d58acc3df455782fb99ee73f5f5f\", \"url\": \"https://api.pillowcase.su/api/download/25b3d58acc3df455782fb99ee73f5f5f\", \"size\": \"607 kB\", \"duration\": 20.14}", "aliases": ["I Am A God"], "size": "607 kB"}, {"id": "i-am-a-god", "name": "I Am A God [V18]", "artists": [], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West", "<PERSON>"], "notes": "Version of \"I Am A God\" containing an minimal and rough production, lacking several elements present in later versions. The drums are done by Daft Punk, and were reused and altered for the <PERSON> song \"MODERN JAM\". Snippet leaked July 28th, 2023 by Donda's Place on their Twitter.", "length": "", "fileDate": 16905024, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/8d14af5cf5a856ae5140aba584975922", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8d14af5cf5a856ae5140aba584975922\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V18]\", \"artists\": \"(prod. <PERSON><PERSON> Punk, <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Lord\"], \"description\": \"Version of \\\"I Am A God\\\" containing an minimal and rough production, lacking several elements present in later versions. The drums are done by Daft Punk, and were reused and altered for the <PERSON> song \\\"MODERN JAM\\\". Snippet leaked July 28th, 2023 by Donda's Place on their Twitter.\", \"date\": 16905024, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["Lord"], "size": ""}, {"id": "i-m-a-god", "name": "I'm A God [V19]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West", "Gesaffelstein"], "notes": "OG Filename: IM A GOD gesaffelstein version\nHas an alternate instrumental and only one verse. Contains a yelling sample from a Bollywood movie \"Seeta Aur Geeta\"; the same sample is also chopped up and slowed down to create the \"eh eh eh eh\" sample chop that was kept in the release version. Leaked in March 2020. Lossless leaked September 2020.", "length": "214.58", "fileDate": 15836256, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/433dd8f79c8c770c86a4e13e09fe3bf3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/433dd8f79c8c770c86a4e13e09fe3bf3\", \"key\": \"I'm A God\", \"title\": \"I'm A God [V19]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, <PERSON><PERSON>, <PERSON><PERSON>e West & G<PERSON>tein)\", \"aliases\": [\"I Am A God\", \"Lord\"], \"description\": \"OG Filename: IM A GOD ges<PERSON><PERSON><PERSON>tein version\\nHas an alternate instrumental and only one verse. Contains a yelling sample from a Bollywood movie \\\"Seeta Aur Geeta\\\"; the same sample is also chopped up and slowed down to create the \\\"eh eh eh eh\\\" sample chop that was kept in the release version. Leaked in March 2020. Lossless leaked September 2020.\", \"date\": 15836256, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2b98270c4a50d1b0bb0486b7ad8770d8\", \"url\": \"https://api.pillowcase.su/api/download/2b98270c4a50d1b0bb0486b7ad8770d8\", \"size\": \"3.72 MB\", \"duration\": 214.58}", "aliases": ["I Am A God", "Lord"], "size": "3.72 MB"}, {"id": "i-am-a-god-43", "name": "I Am A God [V20]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "OG Filename: <PERSON>W - <PERSON> Am A <PERSON> (3.30.13)\nUses a more fleshed out instrumental of V3. While it is missing the first verse, it does however include a verse that was not in the final version.", "length": "210.63", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/a9c8bf29a515e0f4268a3ab891efcd2b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a9c8bf29a515e0f4268a3ab891efcd2b\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V20]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"OG Filename: KW - I Am <PERSON> (3.30.13)\\nUses a more fleshed out instrumental of V3. While it is missing the first verse, it does however include a verse that was not in the final version.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"298afe3d1f4f5a0317c7185010685d24\", \"url\": \"https://api.pillowcase.su/api/download/298afe3d1f4f5a0317c7185010685d24\", \"size\": \"3.65 MB\", \"duration\": 210.63}", "aliases": ["Lord"], "size": "3.65 MB"}, {"id": "i-am-a-god-44", "name": "I Am A God [V21]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "OG Filename: KW - I Am A God Ref (#296EC\nUpdated mix of the previous version.", "length": "207.93", "fileDate": 17016480, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/bb29bdb34238927428f84eeb0ec73502", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb29bdb34238927428f84eeb0ec73502\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V21]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"OG Filename: KW - I Am A <PERSON>f (#296EC\\nUpdated mix of the previous version.\", \"date\": 17016480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7b339e98fa3f7bec30751503ab87e224\", \"url\": \"https://api.pillowcase.su/api/download/7b339e98fa3f7bec30751503ab87e224\", \"size\": \"3.61 MB\", \"duration\": 207.93}", "aliases": ["Lord"], "size": "3.61 MB"}, {"id": "i-am-a-god-45", "name": "I Am A God [V22]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "OG Filename: KW - I Am A God Ref (#299D8\nBetter mix of the March/early April versions. Has extra production.", "length": "210.08", "fileDate": 17016480, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/f1fcc21df2f873979674201b7123418d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f1fcc21df2f873979674201b7123418d\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V22]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"OG Filename: KW - I Am A God Ref (#299D8\\nBetter mix of the March/early April versions. Has extra production.\", \"date\": 17016480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fb6990c5bcc87de27d5f28926c48c795\", \"url\": \"https://api.pillowcase.su/api/download/fb6990c5bcc87de27d5f28926c48c795\", \"size\": \"3.65 MB\", \"duration\": 210.08}", "aliases": ["Lord"], "size": "3.65 MB"}, {"id": "i-am-a-god-46", "name": "I Am A God [V24]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> <PERSON> (4.27.13)\nHas alternate lyrics, and a slightly different instrumental.", "length": "235.58", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/332ab2a48a4791cf49afa44ef683ed52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/332ab2a48a4791cf49afa44ef683ed52\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V24]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"OG Filename: KW - I Am A <PERSON> (4.27.13)\\nHas alternate lyrics, and a slightly different instrumental.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"424d61ed845f0a5e2bbc9e526edfbb00\", \"url\": \"https://api.pillowcase.su/api/download/424d61ed845f0a5e2bbc9e526edfbb00\", \"size\": \"4.06 MB\", \"duration\": 235.58}", "aliases": ["Lord"], "size": "4.06 MB"}, {"id": "i-am-a-god-47", "name": "I Am A God [V25]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "OG Filename: KW - <PERSON> Am A God (Instrumental) @ 112 BPM\nInstrumental found within leaked \"I Am A God\" stems. Quite similar to released, but features noticeable differences.", "length": "242.14", "fileDate": "", "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/8f047bc175c54e858e4c5007743b6e95", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8f047bc175c54e858e4c5007743b6e95\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V25]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"OG Filename: KW - <PERSON> <PERSON> (Instrumental) @ 112 BPM\\nInstrumental found within leaked \\\"I Am A God\\\" stems. Quite similar to released, but features noticeable differences.\", \"date\": null, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"87cf232e84e99e6417ab8bd336e59745\", \"url\": \"https://api.pillowcase.su/api/download/87cf232e84e99e6417ab8bd336e59745\", \"size\": \"4.16 MB\", \"duration\": 242.14}", "aliases": ["Lord"], "size": "4.16 MB"}, {"id": "i-am-a-god-48", "name": "I Am A God [V26]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "Version played at the Met. Includes the original scrapped verse and other lyric differences. Also features instrumental differences. Has the lyrics \"How can I be humbler / When I'm the cause of tumblr / Pop a lil pill now / Tell me how it feel now\". Most snippets of this version are lost. Exported instrumental for the Met Gala performance was included within leaked \"I Am A God\" stems, dated May 6, 2013.", "length": "242.14", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/5540c8bd9f8ad4d095536aff73e32a23", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5540c8bd9f8ad4d095536aff73e32a23\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V26]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>ft Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"Version played at the Met. Includes the original scrapped verse and other lyric differences. Also features instrumental differences. Has the lyrics \\\"How can I be humbler / When I'm the cause of tumblr / Pop a lil pill now / Tell me how it feel now\\\". Most snippets of this version are lost. Exported instrumental for the Met Gala performance was included within leaked \\\"I Am A God\\\" stems, dated May 6, 2013.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"848db4804edea3d967bd13dc2c765a58\", \"url\": \"https://api.pillowcase.su/api/download/848db4804edea3d967bd13dc2c765a58\", \"size\": \"4.16 MB\", \"duration\": 242.14}", "aliases": ["Lord"], "size": "4.16 MB"}, {"id": "i-am-a-god-49", "name": "I Am A God [V26]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West"], "notes": "Version played at the Met. Includes the original scrapped verse and other lyric differences. Also features instrumental differences. Has the lyrics \"How can I be humbler / When I'm the cause of tumblr / Pop a lil pill now / Tell me how it feel now\". Most snippets of this version are lost. Exported instrumental for the Met Gala performance was included within leaked \"I Am A God\" stems, dated May 6, 2013.", "length": "26.12", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/8e570ba48133c51c30563b17260c8253", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8e570ba48133c51c30563b17260c8253\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V26]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, MIKE DEAN & Kanye West)\", \"aliases\": [\"Lord\"], \"description\": \"Version played at the Met. Includes the original scrapped verse and other lyric differences. Also features instrumental differences. Has the lyrics \\\"How can I be humbler / When I'm the cause of tumblr / Pop a lil pill now / Tell me how it feel now\\\". Most snippets of this version are lost. Exported instrumental for the Met Gala performance was included within leaked \\\"I Am A God\\\" stems, dated May 6, 2013.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4e5121261d12e66aab66b1e2ee4ae267\", \"url\": \"https://api.pillowcase.su/api/download/4e5121261d12e66aab66b1e2ee4ae267\", \"size\": \"494 kB\", \"duration\": 26.12}", "aliases": ["Lord"], "size": "494 kB"}, {"id": "i-am-a-god-50", "name": "I Am A God [V27]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "MIKE DEAN", "Kanye West", "Hudson Mohawke"], "notes": "Possibly recorded at the listening party for the album on May 15th, 2013. Has different vocal take with some mumble and alternate lines. Said to not have a \"Hurry up with my damn croissants\" line.", "length": "11.81", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/5729d48f77d9aa82ea3a5d5290808553", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5729d48f77d9aa82ea3a5d5290808553\", \"key\": \"I Am A God\", \"title\": \"I Am A God [V27]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, <PERSON><PERSON>, <PERSON><PERSON><PERSON> & Hudson Mohawke)\", \"aliases\": [\"Lord\"], \"description\": \"Possibly recorded at the listening party for the album on May 15th, 2013. Has different vocal take with some mumble and alternate lines. Said to not have a \\\"Hurry up with my damn croissants\\\" line.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"24b5d3166347f716c8fc71982cb149f5\", \"url\": \"https://api.pillowcase.su/api/download/24b5d3166347f716c8fc71982cb149f5\", \"size\": \"474 kB\", \"duration\": 11.81}", "aliases": ["Lord"], "size": "474 kB"}, {"id": "interlude", "name": "Interlude [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - Interlude Ref (4.11.13)\nInterlude used on a Thank God For Drugs copy. <PERSON><PERSON> vocals.", "length": "23.8", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/29b87b4b27031801e27d5cc697e06f62", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/29b87b4b27031801e27d5cc697e06f62\", \"key\": \"Interlude\", \"title\": \"Interlude [V3]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I Am Not Here\", \"I Am Not Home\"], \"description\": \"OG Filename: KW - Interlude Ref (4.11.13)\\nInterlude used on a Thank God For Drugs copy. Lacks vocals.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"82c8b4c108d4e973d8a513c0f5ba7623\", \"url\": \"https://api.pillowcase.su/api/download/82c8b4c108d4e973d8a513c0f5ba7623\", \"size\": \"666 kB\", \"duration\": 23.8}", "aliases": ["I Am Not Here", "I Am Not Home"], "size": "666 kB"}, {"id": "i-am-not-home", "name": "I Am Not Home [V4]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON>W - <PERSON> Am Not Home Ref (4.24.13)\nThank God For Drugs album version, open verse with only a hook, same as the tour version but with a different mix.", "length": "250.85", "fileDate": 16711488, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/4a055e6a13ea0a073ff2499d4b3eb331", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a055e6a13ea0a073ff2499d4b3eb331\", \"key\": \"I Am Not Home\", \"title\": \"I Am Not Home [V4]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I Am Not Here\"], \"description\": \"OG Filename: KW - I Am Not Home Ref (4.24.13)\\nThank God For Drugs album version, open verse with only a hook, same as the tour version but with a different mix.\", \"date\": 16711488, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fe1de519ac8f8d8829658c66fedbc4eb\", \"url\": \"https://api.pillowcase.su/api/download/fe1de519ac8f8d8829658c66fedbc4eb\", \"size\": \"4.3 MB\", \"duration\": 250.85}", "aliases": ["I Am Not Here"], "size": "4.3 MB"}, {"id": "imitation-of-christ", "name": "✨ Imitation Of Christ", "artists": [], "producers": [], "notes": "OG Filename: KW - IMITATION OF CHRIST\nThank God For Drugs throwaway believed to be 2012. Contains a \"soul sample beat\" with \"super autotuned <PERSON><PERSON><PERSON> vocals\". However, it's \"mostly mumble besides the hook\".", "length": "155.4", "fileDate": 16992288, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/2eaa0b5b4cfffe3f17a6ffcc510674dc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2eaa0b5b4cfffe3f17a6ffcc510674dc\", \"key\": \"Imitation Of Christ\", \"title\": \"\\u2728 Imitation Of Christ\", \"description\": \"OG Filename: KW - IMITATION OF CHRIST\\nThank God For Drugs throwaway believed to be 2012. Contains a \\\"soul sample beat\\\" with \\\"super autotuned Kanye vocals\\\". However, it's \\\"mostly mumble besides the hook\\\".\", \"date\": 16992288, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ba21132d400ea24ed041014eb292c665\", \"url\": \"https://api.pillowcase.su/api/download/ba21132d400ea24ed041014eb292c665\", \"size\": \"2.77 MB\", \"duration\": 155.4}", "aliases": [], "size": "2.77 MB"}, {"id": "lost-bicycle", "name": "Lost Bicycle", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: lost bicycle\nInstrumental that, acording to <PERSON>, was made in late 2012 and recorded on in early 2013. Leaked as a bonus for the second \"THIRSTY\" groupbuy", "length": "179.91", "fileDate": 17342208, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/06cb9670013f661f28bce13b3da5e9d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/06cb9670013f661f28bce13b3da5e9d2\", \"key\": \"Lost Bicycle\", \"title\": \"Lost Bicycle\", \"artists\": \"(prod. 88-Keys)\", \"description\": \"OG Filename: lost bicycle\\nInstrumental that, acording to <PERSON>, was made in late 2012 and recorded on in early 2013. Leaked as a bonus for the second \\\"THIRSTY\\\" groupbuy\", \"date\": 17342208, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a8981099fc926624255ee398fd68d01\", \"url\": \"https://api.pillowcase.su/api/download/4a8981099fc926624255ee398fd68d01\", \"size\": \"4.14 MB\", \"duration\": 179.91}", "aliases": [], "size": "4.14 MB"}, {"id": "new-slaves", "name": "New Slaves [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - <PERSON> Slaves Ref (12.19.12)\nHas mumble <PERSON><PERSON><PERSON> vocals and alternate production. Ends with 45 seconds of silence. File is a rebounce.", "length": "313.66", "fileDate": 16956000, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/dfd719541f6dcd56f3816c9f15c79f1c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dfd719541f6dcd56f3816c9f15c79f1c\", \"key\": \"New Slaves\", \"title\": \"New Slaves [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KW - New Slaves Ref (12.19.12)\\nHas mumble Kanye vocals and alternate production. Ends with 45 seconds of silence. File is a rebounce.\", \"date\": 16956000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3a573ce4cf21a803f2d27552329e0441\", \"url\": \"https://api.pillowcase.su/api/download/3a573ce4cf21a803f2d27552329e0441\", \"size\": \"5.3 MB\", \"duration\": 313.66}", "aliases": [], "size": "5.3 MB"}, {"id": "new-slaves-56", "name": "New Slaves [V4]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "Gesaffelstein"], "notes": "OG Filename: KW - New Slaves Ref (3.13.13)\nHas alternate production not heard in any other leaked version, but elements of the final version are present. Leaked along with its stems.", "length": "248.55", "fileDate": 15712704, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/c0b53c704e8602a4350341197c878666", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0b53c704e8602a4350341197c878666\", \"key\": \"New Slaves\", \"title\": \"New Slaves [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW - New Slaves Ref (3.13.13)\\nHas alternate production not heard in any other leaked version, but elements of the final version are present. Leaked along with its stems.\", \"date\": 15712704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"45513231728e8dcd87c74ff13bcc64e5\", \"url\": \"https://api.pillowcase.su/api/download/45513231728e8dcd87c74ff13bcc64e5\", \"size\": \"4.26 MB\", \"duration\": 248.55}", "aliases": [], "size": "4.26 MB"}, {"id": "new-slaves-57", "name": "New Slaves [V8]", "artists": [], "producers": ["<PERSON>"], "notes": "Played by <PERSON>. Featuring mumbles and alternate verses.", "length": "153.18", "fileDate": 13681440, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/1ff3391291739c9571c52d74ba27106d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ff3391291739c9571c52d74ba27106d\", \"key\": \"New Slaves\", \"title\": \"New Slaves [V8]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"Played by <PERSON>. Featuring mumbles and alternate verses.\", \"date\": 13681440, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"553185eaaa8bde65ca729c826682d0d3\", \"url\": \"https://api.pillowcase.su/api/download/553185eaaa8bde65ca729c826682d0d3\", \"size\": \"2.74 MB\", \"duration\": 153.18}", "aliases": [], "size": "2.74 MB"}, {"id": "new-slaves-58", "name": "New Slaves [V9]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>", "Sak Pase"], "notes": "OG Filename: KW - New Slaves Ref (5.6.13)\nVersion of \"New Slaves\" on a Thank God For Drugs album copy. Has a less minimalistic instrumental.", "length": "256.12", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/6ef2868c34b5026e48f7179ffd9f887b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ef2868c34b5026e48f7179ffd9f887b\", \"key\": \"New Slaves\", \"title\": \"New Slaves [V9]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - New Slaves Ref (5.6.13)\\nVersion of \\\"New Slaves\\\" on a Thank God For Drugs album copy. Has a less minimalistic instrumental.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"89ed6a9bd9e3c36273cde4233f80a5f9\", \"url\": \"https://api.pillowcase.su/api/download/89ed6a9bd9e3c36273cde4233f80a5f9\", \"size\": \"4.39 MB\", \"duration\": 256.12}", "aliases": [], "size": "4.39 MB"}, {"id": "new-slaves-59", "name": "New Slaves [V10]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>", "Sak Pase"], "notes": "Played at the private Yeezus listening party on May 15th, 2013. Seemingly identical to the version aired by projectors few days later. Features slightly further along mixing.", "length": "243.28", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/322585bb7d7ff925740f25f5e95b7bdd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/322585bb7d7ff925740f25f5e95b7bdd\", \"key\": \"New Slaves\", \"title\": \"New Slaves [V10]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"Played at the private Yeezus listening party on May 15th, 2013. Seemingly identical to the version aired by projectors few days later. Features slightly further along mixing.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"45a1e02f718ab812c525792719d69b92\", \"url\": \"https://api.pillowcase.su/api/download/45a1e02f718ab812c525792719d69b92\", \"size\": \"4.18 MB\", \"duration\": 243.28}", "aliases": [], "size": "4.18 MB"}, {"id": "no-more-games", "name": "No More Games", "artists": [], "producers": ["Sak Pase"], "notes": "OG Filename: RAP_CHANTAL_No more Games_100912\n<PERSON><PERSON> \"No More Games\" reference track. This was sampled in <PERSON><PERSON><PERSON>'s track, \"Intro\". Previously known as \"Louder\".", "length": "247.33", "fileDate": 16956864, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/088e043df97a17f156fbc1efcd65332b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/088e043df97a17f156fbc1efcd65332b\", \"key\": \"No More Games\", \"title\": \"No More Games\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: RAP_CHANTAL_No more Games_100912\\nChantal Kreviazuk \\\"No More Games\\\" reference track. This was sampled in <PERSON><PERSON><PERSON> T's track, \\\"Intro\\\". Previously known as \\\"Louder\\\".\", \"date\": 16956864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"dfc030a0818b8df12da0bb423b21fe42\", \"url\": \"https://api.pillowcase.su/api/download/dfc030a0818b8df12da0bb423b21fe42\", \"size\": \"4.24 MB\", \"duration\": 247.33}", "aliases": [], "size": "4.24 MB"}, {"id": "no-no-no-no", "name": "No No No No [V1]", "artists": ["2 Chainz"], "producers": ["<PERSON>", "Sak Pase"], "notes": "OG Filename: KW - No No No No Ref (3.31.13)\nSolo 2 Chainz version. A snippet surfaced from a now inactive seller early in June 2019.", "length": "279.3", "fileDate": 16956864, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/2dc9575c795ff6be158c7a12d15136c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2dc9575c795ff6be158c7a12d15136c3\", \"key\": \"No No No No\", \"title\": \"No No No No [V1]\", \"artists\": \"(feat. 2 <PERSON><PERSON>) (prod. <PERSON> & Sa<PERSON>)\", \"aliases\": [\"No No No\", \"NoNoNo\"], \"description\": \"OG Filename: KW - No No No No Ref (3.31.13)\\nSolo 2 Chainz version. A snippet surfaced from a now inactive seller early in June 2019.\", \"date\": 16956864, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"83a9b92e849036b335cbe5f9a9bb9631\", \"url\": \"https://api.pillowcase.su/api/download/83a9b92e849036b335cbe5f9a9bb9631\", \"size\": \"4.75 MB\", \"duration\": 279.3}", "aliases": ["No No No", "NoNoNo"], "size": "4.75 MB"}, {"id": "no-no-no-no-62", "name": "✨ No No No No [V3]", "artists": ["2 Chainz", "The-Dream"], "producers": ["<PERSON>", "Sak Pase"], "notes": "OG Filename: KW - No No No No Ref (4.11.13)\nHas <PERSON> reference vocals for <PERSON><PERSON><PERSON>, however he does only two lines. Original snippet leaked January 1st, 2023.", "length": "210.94", "fileDate": 16979328, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/edf522f786d0b4c37e8c4d5ac40e7c24", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edf522f786d0b4c37e8c4d5ac40e7c24\", \"key\": \"No No No No\", \"title\": \"\\u2728 No No No No [V3]\", \"artists\": \"(ref. <PERSON>) (feat. 2 <PERSON>z & The-Dream) (prod. <PERSON> & Sak <PERSON>)\", \"aliases\": [\"No No No\", \"NoNoNo\"], \"description\": \"OG Filename: KW - No No No No Ref (4.11.13)\\nHas <PERSON> reference vocals for <PERSON><PERSON><PERSON>, however he does only two lines. Original snippet leaked January 1st, 2023.\", \"date\": 16979328, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f3711cc8a2be4d0ee21cdff6fd48f401\", \"url\": \"https://api.pillowcase.su/api/download/f3711cc8a2be4d0ee21cdff6fd48f401\", \"size\": \"3.66 MB\", \"duration\": 210.94}", "aliases": ["No No No", "NoNoNo"], "size": "3.66 MB"}, {"id": "nobody-to-love", "name": "Nobody To Love [V5]", "artists": ["<PERSON>", "<PERSON>", "Elon R<PERSON>berg"], "producers": ["No I.D.", "<PERSON>", "The Creator", "Sak Pase", "MIKE DEAN"], "notes": "Earlier version of \"Nobody To Love\". Contains a missing line and less / different production. Snippets leaked August 17th, 2022. Full version leaked after being flash-sold for 500 dollars (19.5k less than how much it was originally offered).", "length": "283.48", "fileDate": 16696800, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/4723aeda12d81218fcbff87eba5e6f64", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4723aeda12d81218fcbff87eba5e6f64\", \"key\": \"Nobody To Love\", \"title\": \"Nobody To Love [V5]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON><PERSON>) (prod. <PERSON>.<PERSON>, <PERSON>, <PERSON> Creator, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Bound 2\"], \"description\": \"Earlier version of \\\"Nobody To Love\\\". Contains a missing line and less / different production. Snippets leaked August 17th, 2022. Full version leaked after being flash-sold for 500 dollars (19.5k less than how much it was originally offered).\", \"date\": 16696800, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a843cf91178bf9478c3c87e7a2e9c01b\", \"url\": \"https://api.pillowcase.su/api/download/a843cf91178bf9478c3c87e7a2e9c01b\", \"size\": \"4.82 MB\", \"duration\": 283.48}", "aliases": ["Bound 2"], "size": "4.82 MB"}, {"id": "nobody-to-love-64", "name": "Nobody To Love [V6]", "artists": ["<PERSON>", "<PERSON>", "Elon R<PERSON>berg"], "producers": ["No I.D.", "<PERSON>", "The Creator", "MIKE DEAN"], "notes": "Exact differences are unknown, but the snippet shows some extra Kany<PERSON> vocals on the outro, as well as the different mix.", "length": "8.02", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/114b6fb3bbf10bc00c5e66de24d03c50", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/114b6fb3bbf10bc00c5e66de24d03c50\", \"key\": \"Nobody To Love\", \"title\": \"Nobody To Love [V6]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON>.<PERSON>, <PERSON>, The Creator & MIKE DEAN)\", \"aliases\": [\"Bound 2\"], \"description\": \"Exact differences are unknown, but the snippet shows some extra Kanye vocals on the outro, as well as the different mix.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bfa122cd9a8efbe9dc0a0d71f6651f1a\", \"url\": \"https://api.pillowcase.su/api/download/bfa122cd9a8efbe9dc0a0d71f6651f1a\", \"size\": \"413 kB\", \"duration\": 8.02}", "aliases": ["Bound 2"], "size": "413 kB"}, {"id": "nobody-to-love-65", "name": "⭐ Nobody To Love [V7]", "artists": ["<PERSON>", "<PERSON>", "Elon R<PERSON>berg"], "producers": ["No I.D.", "<PERSON>", "The Creator", "MIKE DEAN"], "notes": "OG Filename: <PERSON>W - <PERSON> To <PERSON> (3.30.13)\nCommonly referred to as \"Bound 1\". Features a beat sampled from \"Aeroplane\" by <PERSON><PERSON>, beat breakdown, alternate lines with mumbling, and an alternate outro.", "length": "278.02", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/5f250237a68b2e5e17ee6338d9c8f709", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f250237a68b2e5e17ee6338d9c8f709\", \"key\": \"Nobody To Love\", \"title\": \"\\u2b50 Nobody To Love [V7]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON>, <PERSON>, The Creator & MIKE DEAN)\", \"aliases\": [\"Bound 2\"], \"description\": \"OG Filename: KW - Nobody To Love Ref (3.30.13)\\nCommonly referred to as \\\"Bound 1\\\". Features a beat sampled from \\\"Aeroplane\\\" by Wee, beat breakdown, alternate lines with mumbling, and an alternate outro.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"28e9c20af7d167e0e14b5b7f3b78ffd9\", \"url\": \"https://api.pillowcase.su/api/download/28e9c20af7d167e0e14b5b7f3b78ffd9\", \"size\": \"4.74 MB\", \"duration\": 278.02}", "aliases": ["Bound 2"], "size": "4.74 MB"}, {"id": "nobody-to-love-66", "name": "🗑️ Nobody To Love [V8]", "artists": ["<PERSON>", "<PERSON>", "Elon R<PERSON>berg"], "producers": ["Sak Pase", "No I.D.", "<PERSON>", "The Creator", "MIKE DEAN"], "notes": "OG Filename: Nobody To Love (sham)04.07.13\nHas added production from <PERSON><PERSON>, with seemingly random drums, and weird mixing.", "length": "256.59", "fileDate": 17016480, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/49322ad890cb6441aed2723d20c23a92", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/49322ad890cb6441aed2723d20c23a92\", \"key\": \"Nobody To Love\", \"title\": \"\\ud83d\\uddd1\\ufe0f Nobody To Love [V8]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON><PERSON>) (prod. <PERSON><PERSON>, No <PERSON>.<PERSON>, <PERSON>, The Creator & MIKE DEAN)\", \"aliases\": [\"Bound 2\"], \"description\": \"OG Filename: Nobody To Love (sham)04.07.13\\nHas added production from <PERSON><PERSON>, with seemingly random drums, and weird mixing.\", \"date\": 17016480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"542d252e226e78e0f7a6fb67072e394d\", \"url\": \"https://api.pillowcase.su/api/download/542d252e226e78e0f7a6fb67072e394d\", \"size\": \"4.4 MB\", \"duration\": 256.59}", "aliases": ["Bound 2"], "size": "4.4 MB"}, {"id": "on-sight", "name": "On Sight [V2]", "artists": [], "producers": ["Daft Punk", "<PERSON>"], "notes": "Version with a different vocal take and instrumental similar to the version below, played at the YZY SZN 10 casting event.", "length": "", "fileDate": 16825536, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/f3974358689335d34fbc33a25df81405", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f3974358689335d34fbc33a25df81405\", \"key\": \"On Sight\", \"title\": \"On Sight [V2]\", \"artists\": \"(prod. <PERSON><PERSON> Punk & Travis Scott)\", \"aliases\": [\"On Site\"], \"description\": \"Version with a different vocal take and instrumental similar to the version below, played at the YZY SZN 10 casting event.\", \"date\": 16825536, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["On Site"], "size": ""}, {"id": "on-site", "name": "✨ On Site [V3]", "artists": [], "producers": ["Daft Punk", "<PERSON>"], "notes": "OG Filename: On Site 4.2 \nVersion with a different vocal arrangement and production. From February 2013. Instrumental of this version was played at the YZY SZN 10 casting event, along with a re-recorded vocal take.", "length": "149.72", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/f9a1d883c24105fad936847e3bdc7fb7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f9a1d883c24105fad936847e3bdc7fb7\", \"key\": \"On Site\", \"title\": \"\\u2728 On Site [V3]\", \"artists\": \"(prod. <PERSON><PERSON> Punk & Travis <PERSON>)\", \"aliases\": [\"On Sight\"], \"description\": \"OG Filename: On Site 4.2 \\nVersion with a different vocal arrangement and production. From February 2013. Instrumental of this version was played at the YZY SZN 10 casting event, along with a re-recorded vocal take.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"861ffdd320138419bea893a1f87a2917\", \"url\": \"https://api.pillowcase.su/api/download/861ffdd320138419bea893a1f87a2917\", \"size\": \"2.68 MB\", \"duration\": 149.72}", "aliases": ["On Sight"], "size": "2.68 MB"}, {"id": "on-sight-69", "name": "On Sight [V5]", "artists": [], "producers": ["Kanye West", "Daft Punk", "MIKE DEAN"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> Sight Ref (3.13.13)\nIncludes minor beat differences and a mumble 2nd verse. Uses actual sample during the bridge, instead of a recreation.", "length": "161.22", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/53f625578a107e3c5b874738214fd38f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53f625578a107e3c5b874738214fd38f\", \"key\": \"On Sight\", \"title\": \"On Sight [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, Daft Punk & MIKE DEAN)\", \"aliases\": [\"On Site\"], \"description\": \"OG Filename: KW - On Sight Ref (3.13.13)\\nIncludes minor beat differences and a mumble 2nd verse. Uses actual sample during the bridge, instead of a recreation.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a415bbe160b6b8033bb544ae2b76f32f\", \"url\": \"https://api.pillowcase.su/api/download/a415bbe160b6b8033bb544ae2b76f32f\", \"size\": \"2.87 MB\", \"duration\": 161.22}", "aliases": ["On Site"], "size": "2.87 MB"}, {"id": "on-sight-70", "name": "On Sight [V7]", "artists": [], "producers": ["Kanye West", "Daft Punk", "MIKE DEAN"], "notes": "Has the same structure as release but still has a mumble second verse and the break is a sample and not an interpolation. Has some mixing differences compared to the March 13th version. Is a stem bounce.", "length": "170.43", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/86eefd513732e0859260d96953434234", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86eefd513732e0859260d96953434234\", \"key\": \"On Sight\", \"title\": \"On Sight [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, Daft Punk & MIKE DEAN)\", \"aliases\": [\"On Site\"], \"description\": \"Has the same structure as release but still has a mumble second verse and the break is a sample and not an interpolation. Has some mixing differences compared to the March 13th version. Is a stem bounce.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"54c990ec8fb86470082a33f807199a26\", \"url\": \"https://api.pillowcase.su/api/download/54c990ec8fb86470082a33f807199a26\", \"size\": \"3.01 MB\", \"duration\": 170.43}", "aliases": ["On Site"], "size": "3.01 MB"}, {"id": "on-site-71", "name": "On Site [V9]", "artists": [], "producers": ["Kanye West", "Daft Punk", "MIKE DEAN"], "notes": "OG Filename: ON Site New 808 New Louie V2 (5.19.13)\nKing Louie reference track. Has a different vocal take for the second verse from <PERSON><PERSON><PERSON>, as well as <PERSON> doing punch-in reference bars for it.", "length": "177.92", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/9c23cd2b16166be58a58e59936905d9f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c23cd2b16166be58a58e59936905d9f\", \"key\": \"On Site\", \"title\": \"On Site [V9]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>, Daft Punk & MIKE DEAN)\", \"aliases\": [\"On Sight\"], \"description\": \"OG Filename: ON Site New 808 New Louie V2 (5.19.13)\\nKing Louie reference track. Has a different vocal take for the second verse from <PERSON><PERSON><PERSON>, as well as <PERSON> doing punch-in reference bars for it.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0d3c27f23538c0f73700a8c7c9130ff6\", \"url\": \"https://api.pillowcase.su/api/download/0d3c27f23538c0f73700a8c7c9130ff6\", \"size\": \"4.93 MB\", \"duration\": 177.92}", "aliases": ["On Sight"], "size": "4.93 MB"}, {"id": "one-i-love", "name": "✨ One I Love", "artists": [], "producers": [], "notes": "OG Filename: KW - One I Love <PERSON> (3.12.13)\nScrapped demo with <PERSON><PERSON><PERSON> humming and singing along under heavy vocoding, similar to \"Runaway\". Reportedly, also later meant for the Lost Yeezus EP. Prominently samples \"Dedicated to the One I Love\" by <PERSON> Mamas and the Papas.", "length": "131.47", "fileDate": 14749344, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/1cb53f5ecdf8f0c0e7daca75e1bbef35", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1cb53f5ecdf8f0c0e7daca75e1bbef35\", \"key\": \"One I Love\", \"title\": \"\\u2728 One I Love\", \"description\": \"OG Filename: KW - One I Love <PERSON>f (3.12.13)\\nScrapped demo with <PERSON><PERSON><PERSON> humming and singing along under heavy vocoding, similar to \\\"Runaway\\\". Reportedly, also later meant for the Lost Yeezus EP. Prominently samples \\\"Dedicated to the One I Love\\\" by The Mamas and the Papas.\", \"date\": 14749344, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3d2301b5167db6372cbe8b4518e7fc2a\", \"url\": \"https://api.pillowcase.su/api/download/3d2301b5167db6372cbe8b4518e7fc2a\", \"size\": \"2.39 MB\", \"duration\": 131.47}", "aliases": [], "size": "2.39 MB"}, {"id": "greek-medusa", "name": "✨ Greek Medusa [V1]", "artists": [], "producers": ["Sak Pase"], "notes": "OG Filename: RAP KW GREEK MEDUSA 84bpm\nJustxn Paul reference track. Unknown if it has the same producers as \"Perfect Bitch\", but it's most likely <PERSON><PERSON>'s take on the song. <PERSON> later reused the lyrics for his song \"Castle In The Making\".", "length": "211", "fileDate": 16849728, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/37cd83e301a11099b15396fa53802699", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37cd83e301a11099b15396fa53802699\", \"key\": \"Greek Medusa\", \"title\": \"\\u2728 Greek Medusa [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Perfect Bitch\"], \"description\": \"OG Filename: RAP KW GREEK MEDUSA 84bpm\\nJustxn Paul reference track. Unknown if it has the same producers as \\\"Perfect Bitch\\\", but it's most likely <PERSON><PERSON>'s take on the song. <PERSON> later reused the lyrics for his song \\\"Castle In The Making\\\".\", \"date\": 16849728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ef1e5bdb35ec2c06adb0af391b7f1e86\", \"url\": \"https://api.pillowcase.su/api/download/ef1e5bdb35ec2c06adb0af391b7f1e86\", \"size\": \"3.67 MB\", \"duration\": 211}", "aliases": ["Perfect Bitch"], "size": "3.67 MB"}, {"id": "send-it-up", "name": "Send It Up [V1]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: KW - Send It Up Ref (3.13.13)\nVersion with a very different instrumental compared to the final version, as well as no vocals from <PERSON>.", "length": "179.36", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/ae9dc7b273d0a3900b87dbf8904cf8d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae9dc7b273d0a3900b87dbf8904cf8d1\", \"key\": \"Send It Up\", \"title\": \"Send It Up [V1]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: KW - Send It Up Ref (3.13.13)\\nVersion with a very different instrumental compared to the final version, as well as no vocals from <PERSON><PERSON>\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"584acad270168f5e28222f33d239306f\", \"url\": \"https://api.pillowcase.su/api/download/584acad270168f5e28222f33d239306f\", \"size\": \"3.15 MB\", \"duration\": 179.36}", "aliases": [], "size": "3.15 MB"}, {"id": "send-it-up-75", "name": "Send It Up [V2]", "artists": ["<PERSON>"], "producers": [], "notes": "Has early production and only the King <PERSON> hook. Is a stem bounce.", "length": "177.61", "fileDate": 17017344, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/c49d4af9813a01bbde522f630795bbe5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c49d4af9813a01bbde522f630795bbe5\", \"key\": \"Send It Up\", \"title\": \"Send It Up [V2]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"Has early production and only the <PERSON> hook. Is a stem bounce.\", \"date\": 17017344, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bb7d9f2191b2e6fd052c2aa3fc97444d\", \"url\": \"https://api.pillowcase.su/api/download/bb7d9f2191b2e6fd052c2aa3fc97444d\", \"size\": \"3.13 MB\", \"duration\": 177.61}", "aliases": [], "size": "3.13 MB"}, {"id": "send-it-up-76", "name": "Send It Up [V3]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "Gesaffelstein", "<PERSON><PERSON><PERSON><PERSON>", "Arca", "MIKE DEAN"], "notes": "OG Filename: KW - Send It Up Ref (4.22.13)\nHas same structure as released, but a different mix. Has a minor difference in the sample chop at the end of the song.", "length": "185.81", "fileDate": 16850592, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/738b5844b76f0adce223c797ebbaed41", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/738b5844b76f0adce223c797ebbaed41\", \"key\": \"Send It Up\", \"title\": \"Send It Up [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Punk, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Arca & MIKE DEAN)\", \"description\": \"OG Filename: KW - Send It Up Ref (4.22.13)\\nHas same structure as released, but a different mix. Has a minor difference in the sample chop at the end of the song.\", \"date\": 16850592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9be6c6b3d4119f8f16aa68bb18673313\", \"url\": \"https://api.pillowcase.su/api/download/9be6c6b3d4119f8f16aa68bb18673313\", \"size\": \"3.26 MB\", \"duration\": 185.81}", "aliases": [], "size": "3.26 MB"}, {"id": "send-it-up-77", "name": "Send It Up [V4]", "artists": ["<PERSON>"], "producers": ["Daft Punk", "Gesaffelstein", "<PERSON><PERSON><PERSON><PERSON>", "Arca", "MIKE DEAN"], "notes": "Seemingly identical to the released version, but with a minor difference in the sample chop at the end of the song. Played at the private Yeezus listening party on May 15th, 2013.", "length": "177.29", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/8843b679853714c67d043a7893cdfafe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8843b679853714c67d043a7893cdfafe\", \"key\": \"Send It Up\", \"title\": \"Send It Up [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Arca & MIKE <PERSON>AN)\", \"description\": \"Seemingly identical to the released version, but with a minor difference in the sample chop at the end of the song. Played at the private Yeezus listening party on May 15th, 2013.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4657e0aa8d9fbfdba0156a71e5ee681e\", \"url\": \"https://api.pillowcase.su/api/download/4657e0aa8d9fbfdba0156a71e5ee681e\", \"size\": \"3.12 MB\", \"duration\": 177.29}", "aliases": [], "size": "3.12 MB"}, {"id": "talk-to-me", "name": "Talk To Me [V3]", "artists": ["Assassin", "<PERSON>"], "producers": ["MIKE DEAN", "<PERSON>", "Sak Pase", "Evian Christ", "<PERSON>"], "notes": "OG Filename: KW - Talk To <PERSON> (3.31.13)\n<PERSON><PERSON> the \"dream\" sample.", "length": "445.65", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/90a737dbea18fd6bc44ebfca2f85f595", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/90a737dbea18fd6bc44ebfca2f85f595\", \"key\": \"Talk To Me\", \"title\": \"Talk To Me [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"I'm In It\"], \"description\": \"OG Filename: KW - Talk To Me Ref (3.31.13)\\nLacks the \\\"dream\\\" sample.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f156cfb8e47ddc9372296dd9a5f13a9d\", \"url\": \"https://api.pillowcase.su/api/download/f156cfb8e47ddc9372296dd9a5f13a9d\", \"size\": \"7.42 MB\", \"duration\": 445.65}", "aliases": ["I'm In It"], "size": "7.42 MB"}, {"id": "talk-to-me-79", "name": "Talk To Me [V4]", "artists": ["Assassin", "<PERSON>"], "producers": ["MIKE DEAN", "<PERSON>", "Sak Pase", "Evian Christ", "<PERSON>"], "notes": "OG Filename: KW - Talk To Me Ref (4.5.13)\nVersion with an extended first verse, no second verse and very different production. Was rumored to feature <PERSON>, but there is no evidence that any \"Talk To Me\" versions feature him. Original snippet of <PERSON><PERSON><PERSON>'s rape line previewed on Snapchat by @colinags. VC recording leaked February 2022.", "length": "440.51", "fileDate": 16850592, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/4fa2b53ec3e4d1618b3c445eb92a3ccf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4fa2b53ec3e4d1618b3c445eb92a3ccf\", \"key\": \"Talk To Me\", \"title\": \"Talk To Me [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"I'm In It\"], \"description\": \"OG Filename: KW - Talk To Me Ref (4.5.13)\\nVersion with an extended first verse, no second verse and very different production. Was rumored to feature <PERSON>, but there is no evidence that any \\\"Talk To Me\\\" versions feature him. Original snippet of <PERSON><PERSON><PERSON>'s rape line previewed on Snapchat by @colinags. VC recording leaked February 2022.\", \"date\": 16850592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"788c8084e5cc147d029ebbcb69d06956\", \"url\": \"https://api.pillowcase.su/api/download/788c8084e5cc147d029ebbcb69d06956\", \"size\": \"7.33 MB\", \"duration\": 440.51}", "aliases": ["I'm In It"], "size": "7.33 MB"}, {"id": "talk-to-me-80", "name": "⭐ Talk To Me [V5]", "artists": ["Assassin", "<PERSON>"], "producers": ["MIKE DEAN", "<PERSON>", "Sak Pase", "Evian Christ", "<PERSON>"], "notes": "OG Filename: KW - Talk To Me Ref (5.1.3)\nVersion with more developed production and an alternate mix on the sample. Compared to release, this is a way more lower energy version with only the first verse and a guitar solo near the end of the song not heard in release.", "length": "398.18", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/fe75068684995c18d123b90b718577b1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe75068684995c18d123b90b718577b1\", \"key\": \"Talk To Me\", \"title\": \"\\u2b50 Talk To Me [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"I'm In It\"], \"description\": \"OG Filename: KW - Talk To Me Ref (5.1.3)\\nVersion with more developed production and an alternate mix on the sample. Compared to release, this is a way more lower energy version with only the first verse and a guitar solo near the end of the song not heard in release.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"00ddc60df01d9f2c84abfff33f4c21a5\", \"url\": \"https://api.pillowcase.su/api/download/00ddc60df01d9f2c84abfff33f4c21a5\", \"size\": \"6.66 MB\", \"duration\": 398.18}", "aliases": ["I'm In It"], "size": "6.66 MB"}, {"id": "wow", "name": "WOW [V1]", "artists": [], "producers": ["Sak Pase"], "notes": "OG Filename: RAP KW WOW 081512\n Justxn Paul reference track, Thank God For Drugs era. Was thought to be <PERSON><PERSON><PERSON> before it leaked. Leaked after the \"Tulsa\" groupbuy.", "length": "211.98", "fileDate": 16336512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/c5d589746c073294a9071269652951ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c5d589746c073294a9071269652951ff\", \"key\": \"WOW\", \"title\": \"WOW [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: RAP KW WOW 081512\\n Justxn Paul reference track, Thank God For Drugs era. Was thought to be CyH<PERSON> before it leaked. Leaked after the \\\"Tulsa\\\" groupbuy.\", \"date\": 16336512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"de09ea6487f32fa15e89d3a1ae5529d6\", \"url\": \"https://api.pillowcase.su/api/download/de09ea6487f32fa15e89d3a1ae5529d6\", \"size\": \"3.68 MB\", \"duration\": 211.98}", "aliases": [], "size": "3.68 MB"}, {"id": "gay-girl-in-a-gay-bar", "name": "Gay Girl In A Gay Bar [V1]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: KW - G<PERSON><PERSON><PERSON> (1.9.13)\nHas mumble vocals from The Weeknd, lacks any <PERSON><PERSON><PERSON>. Sam<PERSON> \"Gay Girl In A Gay Bar\" by <PERSON>.", "length": "239.38", "fileDate": 17017344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/7c7f238df830d0b09b16885a24c6ef67", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c7f238df830d0b09b16885a24c6ef67\", \"key\": \"Gay Girl In A Gay Bar\", \"title\": \"Gay Girl In A Gay Bar [V1]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Why Feel Bad\"], \"description\": \"OG Filename: KW - GGIAG<PERSON> <PERSON> (1.9.13)\\nHas mumble vocals from The Weeknd, lacks any <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> \\\"Gay Girl In A Gay Bar\\\" by <PERSON>.\", \"date\": 17017344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"562cd95a2cab305583b3c69e278b5a51\", \"url\": \"https://api.pillowcase.su/api/download/562cd95a2cab305583b3c69e278b5a51\", \"size\": \"4.12 MB\", \"duration\": 239.38}", "aliases": ["Why Feel Bad"], "size": "4.12 MB"}, {"id": "gay-girl-in-a-gay-bar-83", "name": "Gay Girl In A Gay Bar [V2]", "artists": [], "producers": ["<PERSON><PERSON> <PERSON>", "DJ Camper"], "notes": "OG Filename: GG - Camp<PERSON> <PERSON>dea\nHas added production from DJ Camp<PERSON>.", "length": "239.7", "fileDate": 17017344, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/c07109d9c71a24729972738253b13af5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c07109d9c71a24729972738253b13af5\", \"key\": \"Gay Girl In A Gay Bar\", \"title\": \"Gay Girl In A Gay Bar [V2]\", \"artists\": \"(prod. <PERSON><PERSON> & DJ Camper)\", \"aliases\": [\"Why Feel Bad\"], \"description\": \"OG Filename: GG - Camper Idea\\nHas added production from <PERSON> Camper.\", \"date\": 17017344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6cc70fa808e95cf04ce854fba5ed1d0e\", \"url\": \"https://api.pillowcase.su/api/download/6cc70fa808e95cf04ce854fba5ed1d0e\", \"size\": \"4.12 MB\", \"duration\": 239.7}", "aliases": ["Why Feel Bad"], "size": "4.12 MB"}, {"id": "gay-girl-in-a-gay-bar-84", "name": "✨ Gay Girl In A Gay Bar [V3]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: KW - GGI<PERSON><PERSON> (1.11.13)\nVersion with finished The Weeknd vocals not seen in any other version. For whatever reason, after January 11th, 2013, engineers continued to bounce The Weeknd's mumble vocals and even send them to other producers to work on the song.", "length": "251.1", "fileDate": 17017344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/b215bd1f7d75a03f6ccc140490295114", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b215bd1f7d75a03f6ccc140490295114\", \"key\": \"Gay Girl In A Gay Bar\", \"title\": \"\\u2728 Gay Girl In A Gay Bar [V3]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Why Feel Bad\"], \"description\": \"OG Filename: KW - GGIAGB <PERSON> (1.11.13)\\nVersion with finished The Weeknd vocals not seen in any other version. For whatever reason, after January 11th, 2013, engineers continued to bounce The Weeknd's mumble vocals and even send them to other producers to work on the song.\", \"date\": 17017344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"844fe77943619acd8a44988a7fca340f\", \"url\": \"https://api.pillowcase.su/api/download/844fe77943619acd8a44988a7fca340f\", \"size\": \"4.31 MB\", \"duration\": 251.1}", "aliases": ["Why Feel Bad"], "size": "4.31 MB"}, {"id": "gay-girl-in-a-gay-bar-85", "name": "Gay Girl In A Gay Bar [V4]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "Initial pitched down versio. Keeps some singing removed for the later pitched version.", "length": "262.8", "fileDate": 17019072, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/9447924b1177411467b6f16dff3d1be8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9447924b1177411467b6f16dff3d1be8\", \"key\": \"Gay Girl In A Gay Bar\", \"title\": \"Gay Girl In A Gay Bar [V4]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Why Feel Bad\"], \"description\": \"Initial pitched down versio. Keeps some singing removed for the later pitched version.\", \"date\": 17019072, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0ac807d00975567cb7f5d269bd73e3fb\", \"url\": \"https://api.pillowcase.su/api/download/0ac807d00975567cb7f5d269bd73e3fb\", \"size\": \"4.49 MB\", \"duration\": 262.8}", "aliases": ["Why Feel Bad"], "size": "4.49 MB"}, {"id": "why-feel-bad", "name": "Why Feel Bad [V5]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: KW - Why Feel Bad Ptchd Ref (1.14.13)\nVersion owned by <PERSON><PERSON>, features <PERSON> Weeknd and no <PERSON><PERSON><PERSON> vocals. The entire track is pitched down from its original pitch.", "length": "232.55", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/6092e134d759d1f590149b16139e5dd3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6092e134d759d1f590149b16139e5dd3\", \"key\": \"Why Feel Bad\", \"title\": \"Why Feel Bad [V5]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Why Feel Bad Ptchd Ref (1.14.13)\\nVersion owned by <PERSON><PERSON>, features The Weeknd and no <PERSON><PERSON><PERSON> vocals. The entire track is pitched down from its original pitch.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8fc53ffcb782cd6dbb78672db9a73551\", \"url\": \"https://api.pillowcase.su/api/download/8fc53ffcb782cd6dbb78672db9a73551\", \"size\": \"4.01 MB\", \"duration\": 232.55}", "aliases": [], "size": "4.01 MB"}, {"id": "why-feel-bad-87", "name": "Why Feel Bad [V6]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: KW - Why Feel Bad…Ref (1.14.13)\nVersion owned by <PERSON><PERSON>, features <PERSON> Weeknd and no <PERSON><PERSON><PERSON> vocals. Pitched back up.", "length": "216.87", "fileDate": 17017344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/910a8fd13487897457a3c014f4002219", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/910a8fd13487897457a3c014f4002219\", \"key\": \"Why Feel Bad\", \"title\": \"Why Feel Bad [V6]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Why Feel Bad\\u2026Ref (1.14.13)\\nVersion owned by <PERSON><PERSON> Pa<PERSON>, features The Weeknd and no <PERSON><PERSON><PERSON> vocals. Pitched back up.\", \"date\": 17017344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a25f7741e34da51f62d86bd7e71eb518\", \"url\": \"https://api.pillowcase.su/api/download/a25f7741e34da51f62d86bd7e71eb518\", \"size\": \"3.76 MB\", \"duration\": 216.87}", "aliases": [], "size": "3.76 MB"}, {"id": "gay-girl-in-a-gay-bar-88", "name": "🗑️ Gay Girl In A Gay Bar [V7]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "Has \"vocals\" from <PERSON><PERSON><PERSON>, harmonizing and mimicking instruments.", "length": "228.53", "fileDate": 17019072, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/8daa9e8f8f7af88f69f8a820c732f485", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8daa9e8f8f7af88f69f8a820c732f485\", \"key\": \"Gay Girl In A Gay Bar\", \"title\": \"\\ud83d\\uddd1\\ufe0f Gay Girl In A Gay Bar [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Why Feel Bad\"], \"description\": \"Has \\\"vocals\\\" from <PERSON><PERSON><PERSON>, harmonizing and mimicking instruments.\", \"date\": 17019072, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cb755b38ba07e88336742f9efcb472b8\", \"url\": \"https://api.pillowcase.su/api/download/cb755b38ba07e88336742f9efcb472b8\", \"size\": \"3.94 MB\", \"duration\": 228.53}", "aliases": ["Why Feel Bad"], "size": "3.94 MB"}, {"id": "why-feel-bad-89", "name": "Why Feel Bad [V8]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: KW - Why Feel Bad Ref (1.25.13)\nHas better mixing and the sample has been EQ-ed.", "length": "216.86", "fileDate": 17017344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/ad04fac8a4eb029eebc67b45e9b5b313", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad04fac8a4eb029eebc67b45e9b5b313\", \"key\": \"Why Feel Bad\", \"title\": \"Why Feel Bad [V8]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Why Feel Bad Ref (1.25.13)\\nHas better mixing and the sample has been EQ-ed.\", \"date\": 17017344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"546a31a5fc08713e922ed1eb21ce2ad9\", \"url\": \"https://api.pillowcase.su/api/download/546a31a5fc08713e922ed1eb21ce2ad9\", \"size\": \"3.76 MB\", \"duration\": 216.86}", "aliases": [], "size": "3.76 MB"}, {"id": "why-feel-bad-90", "name": "Why Feel Bad [V9]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>", "Hudson Mohawke"], "notes": "OG Filename: KW - Why Feel Bad HM\nHas added Hudson Mohawke production. From late January 2013.", "length": "193.8", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/53cc994d605c57f53715ab99e3b454e5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53cc994d605c57f53715ab99e3b454e5\", \"key\": \"Why Feel Bad\", \"title\": \"Why Feel Bad [V9]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON>e <PERSON> & Hudson Mohawke)\", \"description\": \"OG Filename: KW - Why Feel Bad HM\\nHas added Hudson Mohawke production. From late January 2013.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ca576da2b7f5f72ce398b1b0d3f8d329\", \"url\": \"https://api.pillowcase.su/api/download/ca576da2b7f5f72ce398b1b0d3f8d329\", \"size\": \"3.39 MB\", \"duration\": 193.8}", "aliases": [], "size": "3.39 MB"}, {"id": "why-feel-bad-91", "name": "Why Feel Bad [V10]", "artists": ["The Weeknd"], "producers": ["<PERSON><PERSON> <PERSON>", "Hudson Mohawke"], "notes": "Slightly slower than the previous version, but retains the EQ-ed sample.", "length": "229.94", "fileDate": 17019072, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/3c8a7e6c9cf6aea2ffee292738a8c3e9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3c8a7e6c9cf6aea2ffee292738a8c3e9\", \"key\": \"Why Feel Bad\", \"title\": \"Why Feel Bad [V10]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON>e <PERSON> & Hudson Mohawke)\", \"description\": \"Slightly slower than the previous version, but retains the EQ-ed sample.\", \"date\": 17019072, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ff007eefd9d26b5d8b2a6bfe37268318\", \"url\": \"https://api.pillowcase.su/api/download/ff007eefd9d26b5d8b2a6bfe37268318\", \"size\": \"3.96 MB\", \"duration\": 229.94}", "aliases": [], "size": "3.96 MB"}, {"id": "through-the-roof", "name": "Beyoncé - Through The Roof [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Through The Roof_Stu Blend 1_3-31-13\nLikely a reference track made for <PERSON>, due to BOOTS writing for her around this time. Unknown when this song was given to <PERSON><PERSON><PERSON><PERSON> Was originally leaked in 2020 as a song from 808s & Heartbreak era however this is clearly false as <PERSON> didn't start making music until 2011, and the file is encoded with the original name and date.", "length": "211.04", "fileDate": 16011648, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/ae09598899659ceefa9eca944aad5eab", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae09598899659ceefa9eca944aad5eab\", \"key\": \"Through The Roof\", \"title\": \"Beyonc\\u00e9 - Through The Roof [V2]\", \"artists\": \"(ref. BOOTS) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"To The Roof\"], \"description\": \"OG Filename: Through The Roof_Stu Blend 1_3-31-13\\nLikely a reference track made for Beyonc\\u00e9, due to <PERSON>O<PERSON><PERSON> writing for her around this time. Unknown when this song was given to <PERSON><PERSON><PERSON><PERSON> Was originally leaked in 2020 as a song from 808s & Heartbreak era however this is clearly false as <PERSON> didn't start making music until 2011, and the file is encoded with the original name and date.\", \"date\": 16011648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b832b28635fcbeeb0491a7b9429c8de8\", \"url\": \"https://api.pillowcase.su/api/download/b832b28635fcbeeb0491a7b9429c8de8\", \"size\": \"3.66 MB\", \"duration\": 211.04}", "aliases": ["To The Roof"], "size": "3.66 MB"}, {"id": "made-to-love", "name": "<PERSON> - Made to Love [V5]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Made To Love Open Rap Verse Mix 2\nHas some open verses and differences in production, compared to the version released on Love In The Future.", "length": "263.55", "fileDate": 16822080, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/80a4fb32bb8bb6711ff504add9cf4b2e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80a4fb32bb8bb6711ff504add9cf4b2e\", \"key\": \"Made to Love\", \"title\": \"<PERSON> Legend - Made to Love [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Made To Love Open Rap Verse Mix 2\\nHas some open verses and differences in production, compared to the version released on Love In The Future.\", \"date\": 16822080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9717085ed0ba38c675ee5f6dab7f2fce\", \"url\": \"https://api.pillowcase.su/api/download/9717085ed0ba38c675ee5f6dab7f2fce\", \"size\": \"4.5 MB\", \"duration\": 263.55}", "aliases": [], "size": "4.5 MB"}, {"id": "shy-can-t-look", "name": "<PERSON> - <PERSON><PERSON> Can't <PERSON>", "artists": ["???"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> (8.4.12)\nRough Kanye reference track, meant for <PERSON>'s 2013 album Love In The Future.", "length": "197.81", "fileDate": 16778016, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/16d987d255454ee1d6c4f0a850aeef62", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16d987d255454ee1d6c4f0a850aeef62\", \"key\": \"<PERSON>hy Can't Look\", \"title\": \"<PERSON> - <PERSON>hy Can't Look\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. ???)\", \"description\": \"OG Filename: <PERSON><PERSON> (8.4.12)\\nRough Kanye reference track, meant for <PERSON>'s 2013 album Love In The Future.\", \"date\": 16778016, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d8f6d1d169dbd0c685ec5a058b7a01a9\", \"url\": \"https://api.pillowcase.su/api/download/d8f6d1d169dbd0c685ec5a058b7a01a9\", \"size\": \"3.45 MB\", \"duration\": 197.81}", "aliases": [], "size": "3.45 MB"}, {"id": "pain", "name": "<PERSON><PERSON><PERSON> <PERSON> [V5]", "artists": ["Future"], "producers": ["Kanye West", "No I.D."], "notes": "OG Filename: Pain M 3.2\nHas different drums to release.", "length": "250.23", "fileDate": 16717536, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/cf11e4f7e9c93537029f48cb38f4c2c9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf11e4f7e9c93537029f48cb38f4c2c9\", \"key\": \"Pain\", \"title\": \"<PERSON><PERSON><PERSON> T <PERSON> Pain [V5]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON><PERSON> & No I.D.)\", \"description\": \"OG Filename: Pain M 3.2\\nHas different drums to release.\", \"date\": 16717536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"70b13d612fdd2503c5fa66b844dd1f3a\", \"url\": \"https://api.pillowcase.su/api/download/70b13d612fdd2503c5fa66b844dd1f3a\", \"size\": \"4.29 MB\", \"duration\": 250.23}", "aliases": [], "size": "4.29 MB"}, {"id": "upper-echelon", "name": "<PERSON> - Upper Echelon [V1]", "artists": [], "producers": ["JGramm Beats", "<PERSON>", "<PERSON>", "MIKE DEAN", "Kanye West"], "notes": "Solo demo, has an unfinished second verse.", "length": "225.15", "fileDate": 13741920, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/b0a23914569853c0daf8acada31efc74", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b0a23914569853c0daf8acada31efc74\", \"key\": \"Upper Echelon\", \"title\": \"<PERSON> - Upper Echelon [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, MIKE <PERSON> & Ka<PERSON><PERSON>)\", \"description\": \"Solo demo, has an unfinished second verse.\", \"date\": 13741920, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"01fd8084bda7090965502f1b170e17aa\", \"url\": \"https://api.pillowcase.su/api/download/01fd8084bda7090965502f1b170e17aa\", \"size\": \"3.89 MB\", \"duration\": 225.15}", "aliases": [], "size": "3.89 MB"}, {"id": "upper-echelon-97", "name": "<PERSON> - Upper Echelon (Remix)", "artists": ["Kanye West"], "producers": ["TNGHT"], "notes": "Remix teased by <PERSON> on Vine. Was speculated to also feature <PERSON>, but there is no evidence supporting this.", "length": "6.3", "fileDate": 13671936, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/28da822685305d736c5a9e4784c253e5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/28da822685305d736c5a9e4784c253e5\", \"key\": \"Upper Echelon (Remix)\", \"title\": \"<PERSON> - Upper Echelon (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. TNGHT)\", \"description\": \"Remix teased by <PERSON> on Vine. <PERSON> speculated to also feature <PERSON>, but there is no evidence supporting this.\", \"date\": 13671936, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"16f6bc48657fc09aff19d233920d2dc3\", \"url\": \"https://api.pillowcase.su/api/download/16f6bc48657fc09aff19d233920d2dc3\", \"size\": \"335 kB\", \"duration\": 6.3}", "aliases": [], "size": "335 kB"}, {"id": "awkward", "name": "<PERSON>, The Creator - Awkward [V1]", "artists": ["<PERSON>"], "producers": [], "notes": "Has new <PERSON> vocals, and an open verse section meant for <PERSON><PERSON><PERSON> to record his vocals over. Called \"fake\" by <PERSON> on Instagram.", "length": "244.4", "fileDate": 16127424, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "thank-god-for-drugs", "originalUrl": "https://pillowcase.su/f/53085bb0a04bcddd23f989f4920fa73e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53085bb0a04bcddd23f989f4920fa73e\", \"key\": \"Awkward\", \"title\": \"<PERSON>, The Creator - Awkward [V1]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"Has <PERSON> vocals, and an open verse section meant for <PERSON><PERSON><PERSON> to record his vocals over. Called \\\"fake\\\" by <PERSON> on Instagram.\", \"date\": 16127424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d91c8f3004d23dd1fa5acb7841ba6eb7\", \"url\": \"https://api.pillowcase.su/api/download/d91c8f3004d23dd1fa5acb7841ba6eb7\", \"size\": \"4.2 MB\", \"duration\": 244.4}", "aliases": [], "size": "4.2 MB"}]}