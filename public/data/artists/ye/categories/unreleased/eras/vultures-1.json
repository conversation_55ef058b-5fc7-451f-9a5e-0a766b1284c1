{"id": "vultures-1", "name": "VULTURES 1", "description": "After the album's name change from Bad Bitch Playbook Vol. 1, to VULTURES 1, four listening parties were scheduled between December 12, 2023, and February 9, 2024. In 2024, <PERSON> announced that \"VULTURES\" would be a trilogy, with the first installment dropping on February 9, followed by the second and third albums in March and April. VULTURES 1 would end up being released on February 10, the day after the final listening party.", "backgroundColor": "rgb(46, 40, 30)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17HSU58gBw-_6DnhNfbfs9gyOXzEqB7zL5qzQll-PHTMQHgkjSIsgPgJomcf2rFgbKeEz1AwwgOKwFqM8WiXb1hTh3xZz3zSj2-Q-iVVUOS7HjPXeIQFfW9fE_FJ6DyucTcRNQec9Mnt7vELFj1S?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "back-to-me", "name": "BACK TO ME [V12]", "artists": ["<PERSON>", "Quavo"], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "Low quality leak of an early version. Contains extra Freddie ad-libs and a different song structure. Also a not fully tuned Ye freestyle on the outro, which eventually materialised as the long bridge we know as the 'Beautiful Big Titty Butt Naked Women Just Don't Fall Out The Sky' bridge.", "length": "254.81", "fileDate": 17037216, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/958fa3aab406a8f5144145a366f77ced", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/958fa3aab406a8f5144145a366f77ced\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V12]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Low quality leak of an early version. Contains extra <PERSON> ad-libs and a different song structure. Also a not fully tuned Ye freestyle on the outro, which eventually materialised as the long bridge we know as the 'Beautiful Big Titty Butt Naked Women Just Don't Fall Out The Sky' bridge.\", \"date\": 17037216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"eb219bcc7aae4d22230b4d4b1da5983c\", \"url\": \"https://api.pillowcase.su/api/download/eb219bcc7aae4d22230b4d4b1da5983c\", \"size\": \"7.05 MB\", \"duration\": 254.81}", "aliases": ["BACK 2 ME"], "size": "7.05 MB"}, {"id": "back-to-me-2", "name": "BACK TO ME [V13]", "artists": ["<PERSON>", "Quavo"], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "Version played at the VULTURES Miami rave. <PERSON><PERSON><PERSON>'s verse is removed and instead has adlibs on both <PERSON><PERSON><PERSON> and <PERSON>'s line \"where they at?\" The end of the track is distorted by microphone audio coming back in. Heavily structured around a clip from the movie \"Dogma\". Partial lossless iTunes file leaked July 31st, 2024.", "length": "345.63", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d013d7f1bef724f5ba3be247a3a2b450", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d013d7f1bef724f5ba3be247a3a2b450\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V13]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Version played at the VULTURES Miami rave. <PERSON><PERSON><PERSON>'s verse is removed and instead has adlibs on both <PERSON>'s and <PERSON>'s line \\\"where they at?\\\" The end of the track is distorted by microphone audio coming back in. Heavily structured around a clip from the movie \\\"Dogma\\\". Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"68cdf919c966501cacbd4e2680b75f28\", \"url\": \"https://api.pillowcase.su/api/download/68cdf919c966501cacbd4e2680b75f28\", \"size\": \"10.5 MB\", \"duration\": 345.63}", "aliases": ["BACK 2 ME"], "size": "10.5 MB"}, {"id": "back-to-me-3", "name": "BACK TO ME [V13]", "artists": ["<PERSON>", "Quavo"], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "Version played at the VULTURES Miami rave. <PERSON><PERSON><PERSON>'s verse is removed and instead has adlibs on both <PERSON><PERSON><PERSON> and <PERSON>'s line \"where they at?\" The end of the track is distorted by microphone audio coming back in. Heavily structured around a clip from the movie \"Dogma\". Partial lossless iTunes file leaked July 31st, 2024.", "length": "160", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/eacdce3d650be6eea868d8fcdb2cb134", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eacdce3d650be6eea868d8fcdb2cb134\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V13]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Version played at the VULTURES Miami rave. <PERSON><PERSON><PERSON>'s verse is removed and instead has adlibs on both <PERSON>'s and <PERSON>'s line \\\"where they at?\\\" The end of the track is distorted by microphone audio coming back in. Heavily structured around a clip from the movie \\\"Dogma\\\". Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ecd2fa649b97a03b9933a6d18a8d91b5\", \"url\": \"https://api.pillowcase.su/api/download/ecd2fa649b97a03b9933a6d18a8d91b5\", \"size\": \"7.57 MB\", \"duration\": 160}", "aliases": ["BACK 2 ME"], "size": "7.57 MB"}, {"id": "beg-forgiveness", "name": "BEG FORGIVENESS [V9]", "artists": ["<PERSON>"], "producers": ["Digital Nas"], "notes": "Version played at the VULTURES Miami rave. Less developed than the brunch snippet, containing no Ty$ vocals, so it's rumored to be an earlier version. Partial lossless iTunes file leaked July 31st, 2024.", "length": "196.31", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3ce09a61d37cee515dc562e81910d6f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ce09a61d37cee515dc562e81910d6f4\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V9]\", \"artists\": \"(feat. <PERSON>) (prod. Digital Nas)\", \"description\": \"Version played at the VULTURES Miami rave. Less developed than the brunch snippet, containing no Ty$ vocals, so it's rumored to be an earlier version. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"64e8b94613b35950bac68c36db0fe27d\", \"url\": \"https://api.pillowcase.su/api/download/64e8b94613b35950bac68c36db0fe27d\", \"size\": \"8.15 MB\", \"duration\": 196.31}", "aliases": [], "size": "8.15 MB"}, {"id": "beg-forgiveness-5", "name": "BEG FORGIVENESS [V9]", "artists": ["<PERSON>"], "producers": ["Digital Nas"], "notes": "Version played at the VULTURES Miami rave. Less developed than the brunch snippet, containing no Ty$ vocals, so it's rumored to be an earlier version. Partial lossless iTunes file leaked July 31st, 2024.", "length": "128", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/b5bfaa86259024f9bc8e5e132a43c1aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b5bfaa86259024f9bc8e5e132a43c1aa\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V9]\", \"artists\": \"(feat. <PERSON>) (prod. Digital Nas)\", \"description\": \"Version played at the VULTURES Miami rave. Less developed than the brunch snippet, containing no Ty$ vocals, so it's rumored to be an earlier version. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"aa8999fb45fc75855ded31ddb5e9362d\", \"url\": \"https://api.pillowcase.su/api/download/aa8999fb45fc75855ded31ddb5e9362d\", \"size\": \"7.06 MB\", \"duration\": 128}", "aliases": [], "size": "7.06 MB"}, {"id": "beg-forgiveness-6", "name": "BEG FORGIVENESS [V10]", "artists": ["<PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: 1-110\nTrack 5 on tracklist posted by Ty Dolla $ign. Contains vocals from <PERSON><PERSON> and <PERSON>. Was exported on or before December 13th 2023.", "length": "194.18", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c0897e91f5c2060356ed73e9c9cea06d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0897e91f5c2060356ed73e9c9cea06d\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V10]\", \"artists\": \"(feat. <PERSON>) (prod. Digital Nas)\", \"description\": \"OG Filename: 1-110\\nTrack 5 on tracklist posted by Ty Dolla $ign. Contains vocals from <PERSON> and <PERSON>. Was exported on or before December 13th 2023.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4093e9000012d976775a0f97b506a730\", \"url\": \"https://api.pillowcase.su/api/download/4093e9000012d976775a0f97b506a730\", \"size\": \"8.12 MB\", \"duration\": 194.18}", "aliases": [], "size": "8.12 MB"}, {"id": "beg-forgiveness-7", "name": "BEG FORGIVENESS [V11]", "artists": ["<PERSON>"], "producers": ["Digital Nas", "???"], "notes": "OG Filename: YE CHRIS TY 110 CHORDS\nVersion with extra production. This version is a stem bounce. Has a ton of production not seen in release at all.", "length": "194.18", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/290ba7f0e86180ef89dd16dea957015a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/290ba7f0e86180ef89dd16dea957015a\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V11]\", \"artists\": \"(feat. <PERSON>) (prod. Digital Nas & ???)\", \"description\": \"OG Filename: YE CHRIS TY 110 CHORDS\\nVersion with extra production. This version is a stem bounce. Has a ton of production not seen in release at all.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"12a59a7a1abb5ad824b1ac4b50d62031\", \"url\": \"https://api.pillowcase.su/api/download/12a59a7a1abb5ad824b1ac4b50d62031\", \"size\": \"8.12 MB\", \"duration\": 194.18}", "aliases": [], "size": "8.12 MB"}, {"id": "block-the-pain-away", "name": "BLOCK THE PAIN AWAY", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: BLOCK THE PAIN AWAY-YX-BAERO... \nThe beat in this snippet is not official, only the acapella. This is because <PERSON><PERSON><PERSON> heard a song that made him add a Ye chorus he had onto it.", "length": "37.77", "fileDate": 17057088, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8dce233dfb548412198ea8252d28e27b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8dce233dfb548412198ea8252d28e27b\", \"key\": \"BLOCK THE PAIN AWAY\", \"title\": \"BLOCK THE PAIN AWAY\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: BLOCK THE PAIN AWAY-YX-BAERO... \\nThe beat in this snippet is not official, only the acapella. This is because <PERSON><PERSON><PERSON> heard a song that made him add a Ye chorus he had onto it.\", \"date\": 17057088, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"588a92d7049174c4b4161114c366efaa\", \"url\": \"https://api.pillowcase.su/api/download/588a92d7049174c4b4161114c366efaa\", \"size\": \"5.61 MB\", \"duration\": 37.77}", "aliases": [], "size": "5.61 MB"}, {"id": "dead", "name": "DEAD [V2]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "OG Filename: dead <PERSON>AMI BABY 20 - YE\nVersion with vocals from <PERSON>. Most likely meant as a ref due to the filename. Has an alternate Ty Dolla $ign verse. Original snippets leaked August 8th, 2024 & October 14th, 2024.", "length": "207.86", "fileDate": 17290368, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/20db0dcc512f1339e5dad00ce0631aec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20db0dcc512f1339e5dad00ce0631aec\", \"key\": \"DEAD\", \"title\": \"DEAD [V2]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON> <PERSON> <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: dead MIAMI BABY 20 - YE\\nVersion with vocals from <PERSON>. Most likely meant as a ref due to the filename. Has an alternate Ty Dolla $ign verse. Original snippets leaked August 8th, 2024 & October 14th, 2024.\", \"date\": 17290368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3a0fee97384724a430827d05dd8cc6aa\", \"url\": \"https://api.pillowcase.su/api/download/3a0fee97384724a430827d05dd8cc6aa\", \"size\": \"8.34 MB\", \"duration\": 207.86}", "aliases": [], "size": "8.34 MB"}, {"id": "dead-10", "name": "DEAD [V3]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "OG Filename: DEAD - ty ref\nTy & Fre$h reference that would later be used for <PERSON>'s release verse. Original snippet leaked October 19th, 2024.", "length": "297.32", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ef35faa4b36e18e84cb20429f133c7a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef35faa4b36e18e84cb20429f133c7a1\", \"key\": \"DEAD\", \"title\": \"DEAD [V3]\", \"artists\": \"(ref. <PERSON>ign & <PERSON>e$h) (feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: DEAD - ty ref\\nTy & Fre$h reference that would later be used for <PERSON>'s release verse. Original snippet leaked October 19th, 2024.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3ad6b24f6961753f60fb265afe3f2b95\", \"url\": \"https://api.pillowcase.su/api/download/3ad6b24f6961753f60fb265afe3f2b95\", \"size\": \"9.77 MB\", \"duration\": 297.32}", "aliases": [], "size": "9.77 MB"}, {"id": "dead-11", "name": "DEAD [V5]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "Fat Money ref. <PERSON><PERSON><PERSON><PERSON> leaked August 6th, 2024.", "length": "", "fileDate": 17229024, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/36da07e78f82c4a91192241d2c9d3f2f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/36da07e78f82c4a91192241d2c9d3f2f\", \"key\": \"DEAD\", \"title\": \"DEAD [V5]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON> <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"Fat Money ref. Snippet leaked August 6th, 2024.\", \"date\": 17229024, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "dead-12", "name": "DEAD [V6]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "Previewed on Fat Money's instagram livestream. The song is slightly sped up and is possibly a reference track.", "length": "17.68", "fileDate": 17064864, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f673b5a7e6e529ef04707b8321ea7a9f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f673b5a7e6e529ef04707b8321ea7a9f\", \"key\": \"DEAD\", \"title\": \"DEAD [V6]\", \"artists\": \"(feat. <PERSON> <PERSON> <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"Previewed on <PERSON> Money's instagram livestream. The song is slightly sped up and is possibly a reference track.\", \"date\": 17064864, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f5f217066af12638ef73b4657171870a\", \"url\": \"https://api.pillowcase.su/api/download/f5f217066af12638ef73b4657171870a\", \"size\": \"5.15 MB\", \"duration\": 17.68}", "aliases": [], "size": "5.15 MB"}, {"id": "dead-13", "name": "DEAD [V7]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "OG Filename: dead w/ jew freestyle\nOriginal freestyle. Contains the same first 5 minutes as the Las Vegas version, with a three minute low mumble freestyle from <PERSON> following it. Was most likely made right before the Las Vegas version as it doesn't have the Khloe bar.", "length": "492.2", "fileDate": 17280864, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/91a55f529d0062a157a7002daa9644ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91a55f529d0062a157a7002daa9644ac\", \"key\": \"DEAD\", \"title\": \"DEAD [V7]\", \"artists\": \"(feat. <PERSON> <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: dead w/ jew freestyle\\nOriginal freestyle. Contains the same first 5 minutes as the Las Vegas version, with a three minute low mumble freestyle from <PERSON> following it. Was most likely made right before the Las Vegas version as it doesn't have the Khloe bar.\", \"date\": 17280864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7ddf6b0e70d25e659cdcdf6339de817e\", \"url\": \"https://api.pillowcase.su/api/download/7ddf6b0e70d25e659cdcdf6339de817e\", \"size\": \"12.9 MB\", \"duration\": 492.2}", "aliases": [], "size": "12.9 MB"}, {"id": "dead-14", "name": "DEAD [V8]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "OG Filename: dead - Track 1\nOriginal version with an alternate line \"<PERSON> bought you <PERSON><PERSON><PERSON> keep it true with me / All I ever wanted was you to be true with me\" and an alternate Ty line. Made on or before Dec 14th, 2023", "length": "304.54", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/dd8bae6d0509eb978b3cbc8ba442edc5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd8bae6d0509eb978b3cbc8ba442edc5\", \"key\": \"DEAD\", \"title\": \"DEAD [V8]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: dead - Track 1\\nOriginal version with an alternate line \\\"Tristan bought you <PERSON><PERSON>oe keep it true with me / All I ever wanted was you to be true with me\\\" and an alternate Ty line. Made on or before Dec 14th, 2023\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a2811187db4439e5301929a76272a888\", \"url\": \"https://api.pillowcase.su/api/download/a2811187db4439e5301929a76272a888\", \"size\": \"9.88 MB\", \"duration\": 304.54}", "aliases": [], "size": "9.88 MB"}, {"id": "dead-15", "name": "DEAD [V9]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob", "London on da Track", "Wheezy"], "notes": "OG Filename: Dead Intro 134\nVersion with added intro production and at the start of <PERSON>'s vocals. This version is a stem bounce.", "length": "304.54", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3460035d4635364b0506e2f7025d4c86", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3460035d4635364b0506e2f7025d4c86\", \"key\": \"DEAD\", \"title\": \"DEAD [V9]\", \"artists\": \"(feat. <PERSON> <PERSON>) (prod. <PERSON><PERSON>, London on da Track & Wheezy)\", \"description\": \"OG Filename: Dead Intro 134\\nVersion with added intro production and at the start of <PERSON>'s vocals. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1bc941149d8ff445f8edf57598acb56d\", \"url\": \"https://api.pillowcase.su/api/download/1bc941149d8ff445f8edf57598acb56d\", \"size\": \"9.88 MB\", \"duration\": 304.54}", "aliases": [], "size": "9.88 MB"}, {"id": "dead-16", "name": "DEAD [V10]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob"], "notes": "Version played at the Las Vegas rave. Reverts the alternate lines from the previous versions back to the original lines.", "length": "296.28", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e150db0ebd1e3e7936d3c3ca63a6956e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e150db0ebd1e3e7936d3c3ca63a6956e\", \"key\": \"DEAD\", \"title\": \"DEAD [V10]\", \"artists\": \"(feat. <PERSON> <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"Version played at the Las Vegas rave. Reverts the alternate lines from the previous versions back to the original lines.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"10c77bb4e1177a2804feb33609f6ca30\", \"url\": \"https://api.pillowcase.su/api/download/10c77bb4e1177a2804feb33609f6ca30\", \"size\": \"9.75 MB\", \"duration\": 296.28}", "aliases": [], "size": "9.75 MB"}, {"id": "dead-17", "name": "DEAD [V11]", "artists": ["Future", "<PERSON>"], "producers": ["ATL Jacob", "SHDØW"], "notes": "Version with added production from SHDØW. Vocals are AI extracted. Snippet leaked Dec 26th, 2024.", "length": "", "fileDate": 17351712, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1e8fb73c87fce4b419b1907f845e9520", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e8fb73c87fce4b419b1907f845e9520\", \"key\": \"DEAD\", \"title\": \"DEAD [V11]\", \"artists\": \"(feat. <PERSON> & Lil <PERSON>) (prod. ATL Jacob & SHD\\u00d8W)\", \"description\": \"Version with added production from SHD\\u00d8W. Vocals are AI extracted. Snippet leaked Dec 26th, 2024.\", \"date\": 17351712, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "do-it", "name": "DO IT [V5]", "artists": ["YG"], "producers": ["Wheezy"], "notes": "Version that was played several times during the Las Vegas rave before the event being abruptly stopped by local police.", "length": "181.11", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e57fc47c6187f2f79a7becb975299604", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e57fc47c6187f2f79a7becb975299604\", \"key\": \"DO IT\", \"title\": \"DO IT [V5]\", \"artists\": \"(feat. Y<PERSON>) (prod. Wheezy)\", \"description\": \"Version that was played several times during the Las Vegas rave before the event being abruptly stopped by local police.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e02dc149afbd5ccdd2b913da2f2718e2\", \"url\": \"https://api.pillowcase.su/api/download/e02dc149afbd5ccdd2b913da2f2718e2\", \"size\": \"7.91 MB\", \"duration\": 181.11}", "aliases": [], "size": "7.91 MB"}, {"id": "do-it-19", "name": "DO IT [V5]", "artists": ["YG"], "producers": ["Wheezy"], "notes": "Version that was played several times during the Las Vegas rave before the event being abruptly stopped by local police.", "length": "31.38", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ea5a2d3f264e2c0727ded0fa6a6b9ad1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ea5a2d3f264e2c0727ded0fa6a6b9ad1\", \"key\": \"DO IT\", \"title\": \"DO IT [V5]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. Wheezy)\", \"description\": \"Version that was played several times during the Las Vegas rave before the event being abruptly stopped by local police.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"81103009bbd0fbdfccfe3be5bbc7d12e\", \"url\": \"https://api.pillowcase.su/api/download/81103009bbd0fbdfccfe3be5bbc7d12e\", \"size\": \"5.51 MB\", \"duration\": 31.38}", "aliases": [], "size": "5.51 MB"}, {"id": "do-it-20", "name": "DO IT [V5]", "artists": ["YG"], "producers": ["Wheezy"], "notes": "Version that was played several times during the Las Vegas rave before the event being abruptly stopped by local police.", "length": "219.77", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4410139621e9115c7d1188b4361acbf6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4410139621e9115c7d1188b4361acbf6\", \"key\": \"DO IT\", \"title\": \"DO IT [V5]\", \"artists\": \"(feat. Y<PERSON>) (prod. Wheezy)\", \"description\": \"Version that was played several times during the Las Vegas rave before the event being abruptly stopped by local police.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"146bd9d25fbfe6f2fac321b8d19e6138\", \"url\": \"https://api.pillowcase.su/api/download/146bd9d25fbfe6f2fac321b8d19e6138\", \"size\": \"8.53 MB\", \"duration\": 219.77}", "aliases": [], "size": "8.53 MB"}, {"id": "do-it-21", "name": "DO IT [V6]", "artists": ["YG", "Tyga"], "producers": ["Wheezy"], "notes": "Has a Tyga verse that wasn't present in the Las Vegas Rave after <PERSON> Doll<PERSON> $ign's verse. <PERSON><PERSON>'s verse is also after <PERSON>'s verse. Unknown if this was made before or after the version played at the rave.", "length": "322.3", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e64f282c73c747d759f8e30db6682e0d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e64f282c73c747d759f8e30db6682e0d\", \"key\": \"DO IT\", \"title\": \"DO IT [V6]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>ga) (prod. Wheezy)\", \"description\": \"Has a Tyga verse that wasn't present in the Las Vegas Rave after <PERSON> Dolla $ign's verse. <PERSON><PERSON>'s verse is also after <PERSON>'s verse. Unknown if this was made before or after the version played at the rave.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"cf6d3705573ea9bfad6b0da5a4cd1b25\", \"url\": \"https://api.pillowcase.su/api/download/cf6d3705573ea9bfad6b0da5a4cd1b25\", \"size\": \"10.2 MB\", \"duration\": 322.3}", "aliases": [], "size": "10.2 MB"}, {"id": "don-t-kill-the-party", "name": "DON'T KILL THE PARTY [V3]", "artists": ["Tyga"], "producers": ["BNYX®", "Ojivolta"], "notes": "OG Filename: dont kill the party V1_OV\nHas a Tyga feature. Has BNYX production as the \"Working On Dying\" tag is used briefly and drums sounding closer to his production. Unknown which version played has this filename. Played at the Las Vegas Vultures City afterparty on December 15th, 2023.", "length": "180.56", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ba85fb58c23d76bb47f5523bdded8fde", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ba85fb58c23d76bb47f5523bdded8fde\", \"key\": \"DON'T KILL THE PARTY\", \"title\": \"DON'T KILL THE PARTY [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. BNYX\\u00ae & Ojivolta)\", \"description\": \"OG Filename: dont kill the party V1_OV\\nHas a Tyga feature. Has BNYX production as the \\\"Working On Dying\\\" tag is used briefly and drums sounding closer to his production. Unknown which version played has this filename. Played at the Las Vegas Vultures City afterparty on December 15th, 2023.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d05867eb222abbd66efefeb75949e40e\", \"url\": \"https://api.pillowcase.su/api/download/d05867eb222abbd66efefeb75949e40e\", \"size\": \"7.9 MB\", \"duration\": 180.56}", "aliases": [], "size": "7.9 MB"}, {"id": "don-t-kill-the-party-23", "name": "DON'T KILL THE PARTY [V3]", "artists": ["Tyga"], "producers": ["BNYX®", "Ojivolta"], "notes": "OG Filename: dont kill the party V1_OV\nHas a Tyga feature. Has BNYX production as the \"Working On Dying\" tag is used briefly and drums sounding closer to his production. Unknown which version played has this filename. Played at the Las Vegas Vultures City afterparty on December 15th, 2023.", "length": "137.61", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/bf350308ea76a73fde727f720b48b5ec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bf350308ea76a73fde727f720b48b5ec\", \"key\": \"DON'T KILL THE PARTY\", \"title\": \"DON'T KILL THE PARTY [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. BNYX\\u00ae & Ojivolta)\", \"description\": \"OG Filename: dont kill the party V1_OV\\nHas a Tyga feature. Has BNYX production as the \\\"Working On Dying\\\" tag is used briefly and drums sounding closer to his production. Unknown which version played has this filename. Played at the Las Vegas Vultures City afterparty on December 15th, 2023.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9ebc62a20813a6fc9a2672667ecc1a4d\", \"url\": \"https://api.pillowcase.su/api/download/9ebc62a20813a6fc9a2672667ecc1a4d\", \"size\": \"7.21 MB\", \"duration\": 137.61}", "aliases": [], "size": "7.21 MB"}, {"id": "don-t-kill-the-party-24", "name": "DON'T KILL THE PARTY [V4]", "artists": [], "producers": ["BNYX®", "Ojivolta"], "notes": "Version without <PERSON><PERSON> and has a fully finished Ye verse instead. Is slightly slower. Played at the Las Vegas Vultures City afterparty on December 15th, 2023.", "length": "211.15", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e349976a411d012e146a96fc9f6e3c75", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e349976a411d012e146a96fc9f6e3c75\", \"key\": \"DON'T KILL THE PARTY\", \"title\": \"DON'T KILL THE PARTY [V4]\", \"artists\": \"(prod. BNYX\\u00ae & Ojivolta)\", \"description\": \"Version without Ty<PERSON> and has a fully finished Ye verse instead. Is slightly slower. Played at the Las Vegas Vultures City afterparty on December 15th, 2023.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"248d2e281d5db9288247450b7d0cd8c5\", \"url\": \"https://api.pillowcase.su/api/download/248d2e281d5db9288247450b7d0cd8c5\", \"size\": \"8.39 MB\", \"duration\": 211.15}", "aliases": [], "size": "8.39 MB"}, {"id": "drunk", "name": "DRUNK [V6]", "artists": ["Kodak Black", "Bad Bunny"], "producers": ["<PERSON><PERSON>", "VEYIS", "SHDØW"], "notes": "OG Filename: drunk (shdow veyis vers ) 12...\nTrack 14 on tracklist posted by Ty Dolla $ign. Was played briefly at the VULTURES Miami rave, however it was not played on the live stream due to the stream having connection problems while the song was playing. <PERSON>'s vocals originate from <PERSON>'s unreleased song \"So Drunk\". <PERSON><PERSON> had confirmed a <PERSON> Bunny collaboration in a 2022 interview, which was most likely this song before it was taken for VULTURES. Another snippet was previewed by SHDØW May 29th, 2024.", "length": "66.41", "fileDate": 17023392, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a13588644d1b5dfc2f4bba295dc9a664", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a13588644d1b5dfc2f4bba295dc9a664\", \"key\": \"DRUNK\", \"title\": \"DRUNK [V6]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, VEYIS & SHD\\u00d8W)\", \"aliases\": [\"So Drunk\"], \"description\": \"OG Filename: drunk (shdow veyis vers ) 12...\\nTrack 14 on tracklist posted by Ty Dolla $ign. Was played briefly at the VULTURES Miami rave, however it was not played on the live stream due to the stream having connection problems while the song was playing. <PERSON>'s vocals originate from <PERSON>'s unreleased song \\\"So Drunk\\\". <PERSON><PERSON> had confirmed a Bad Bunny collaboration in a 2022 interview, which was most likely this song before it was taken for VULTURES. Another snippet was previewed by SH<PERSON>\\u00d8W May 29th, 2024.\", \"date\": 17023392, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b31fb69bcc13d2c99233cb8acf79076d\", \"url\": \"https://api.pillowcase.su/api/download/b31fb69bcc13d2c99233cb8acf79076d\", \"size\": \"6.07 MB\", \"duration\": 66.41}", "aliases": ["So Drunk"], "size": "6.07 MB"}, {"id": "drunk-26", "name": "DRUNK [V6]", "artists": ["Kodak Black", "Bad Bunny"], "producers": ["<PERSON><PERSON>", "VEYIS", "SHDØW"], "notes": "OG Filename: drunk (shdow veyis vers ) 12...\nTrack 14 on tracklist posted by Ty Dolla $ign. Was played briefly at the VULTURES Miami rave, however it was not played on the live stream due to the stream having connection problems while the song was playing. <PERSON>'s vocals originate from <PERSON>'s unreleased song \"So Drunk\". <PERSON><PERSON> had confirmed a <PERSON> Bunny collaboration in a 2022 interview, which was most likely this song before it was taken for VULTURES. Another snippet was previewed by SHDØW May 29th, 2024.", "length": "59.98", "fileDate": 17023392, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d96f746617a5278a675c56119820505d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d96f746617a5278a675c56119820505d\", \"key\": \"DRUNK\", \"title\": \"DRUNK [V6]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, VEYIS & SHD\\u00d8W)\", \"aliases\": [\"So Drunk\"], \"description\": \"OG Filename: drunk (shdow veyis vers ) 12...\\nTrack 14 on tracklist posted by Ty Dolla $ign. Was played briefly at the VULTURES Miami rave, however it was not played on the live stream due to the stream having connection problems while the song was playing. <PERSON>'s vocals originate from <PERSON>'s unreleased song \\\"So Drunk\\\". <PERSON><PERSON> had confirmed a Bad Bunny collaboration in a 2022 interview, which was most likely this song before it was taken for VULTURES. Another snippet was previewed by SH<PERSON>\\u00d8W May 29th, 2024.\", \"date\": 17023392, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e66f43dd07d1eecc46cb2f9d72fc3975\", \"url\": \"https://api.pillowcase.su/api/download/e66f43dd07d1eecc46cb2f9d72fc3975\", \"size\": \"5.97 MB\", \"duration\": 59.98}", "aliases": ["So Drunk"], "size": "5.97 MB"}, {"id": "everybody", "name": "EVERYBODY [V3]", "artists": ["<PERSON>"], "producers": [], "notes": "An early version that has the original Backstreet Boys vocals at the start that cut to <PERSON>'s vocals. Sam<PERSON> \"Puerto Rico\" by <PERSON>.", "length": "13.27", "fileDate": 17020800, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f5ede0d271c604c65fb0058d6cb3169f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f5ede0d271c604c65fb0058d6cb3169f\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V3]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"An early version that has the original Backstreet Boys vocals at the start that cut to <PERSON>'s vocals. <PERSON><PERSON> \\\"Puerto Rico\\\" by <PERSON>.\", \"date\": 17020800, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"89d7cbc2ec18438891239c37ccef3819\", \"url\": \"https://api.pillowcase.su/api/download/89d7cbc2ec18438891239c37ccef3819\", \"size\": \"5.22 MB\", \"duration\": 13.27}", "aliases": [], "size": "5.22 MB"}, {"id": "everybody-28", "name": "EVERYBODY [V4]", "artists": ["<PERSON>"], "producers": ["Timbaland"], "notes": "Early version with drums that would later be removed from the song & alternate <PERSON> vocals. Also has the intro done by <PERSON>ign instead of <PERSON>. Has an open verse for <PERSON>.", "length": "143.64", "fileDate": 17021664, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4f4ba8ec62448832b58ec22483dcfab2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4f4ba8ec62448832b58ec22483dcfab2\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Early version with drums that would later be removed from the song & alternate <PERSON> vocals. Also has the intro done by <PERSON>ign instead of <PERSON>. Has an open verse for <PERSON>.\", \"date\": 17021664, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6ef1bfee07b2b7e0b2114271b8efbfb9\", \"url\": \"https://api.pillowcase.su/api/download/6ef1bfee07b2b7e0b2114271b8efbfb9\", \"size\": \"7.31 MB\", \"duration\": 143.64}", "aliases": [], "size": "7.31 MB"}, {"id": "everybody-29", "name": "EVERYBODY [V5]", "artists": ["<PERSON>"], "producers": [], "notes": "Early version that <PERSON>ign played at a club on December 12th. The drums in this version has been replaced with the bassy type drums, later used in <PERSON>'s verse in the Listening Party version.", "length": "7.24", "fileDate": 17023392, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/29d7d854c9b460608cce747597b3c9f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/29d7d854c9b460608cce747597b3c9f1\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V5]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"Early version that Ty Dolla $ign played at a club on December 12th. The drums in this version has been replaced with the bassy type drums, later used in <PERSON>'s verse in the Listening Party version.\", \"date\": 17023392, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b831c8e953280c0f5e807a8ab0f1c184\", \"url\": \"https://api.pillowcase.su/api/download/b831c8e953280c0f5e807a8ab0f1c184\", \"size\": \"5.07 MB\", \"duration\": 7.24}", "aliases": [], "size": "5.07 MB"}, {"id": "everybody-30", "name": "EVERYBODY [V7]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Timbaland", "will.i.am", "Kanye West"], "notes": "Track 1 on tracklist posted by <PERSON> Dolla $ign, and played at the VULTURES Miami rave. Partial lossless iTunes file leaked July 31st, 2024.", "length": "328.23", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/300a9b8415fa29aeb45bb71072a6d459", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/300a9b8415fa29aeb45bb71072a6d459\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V7]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, will.i.am & <PERSON><PERSON><PERSON>)\", \"description\": \"Track 1 on tracklist posted by Ty Dolla $ign, and played at the VULTURES Miami rave. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4f0554934bd4b98a9fad26058d2b3b6c\", \"url\": \"https://api.pillowcase.su/api/download/4f0554934bd4b98a9fad26058d2b3b6c\", \"size\": \"10.3 MB\", \"duration\": 328.23}", "aliases": [], "size": "10.3 MB"}, {"id": "everybody-31", "name": "EVERYBODY [V7]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Timbaland", "will.i.am", "Kanye West"], "notes": "Track 1 on tracklist posted by <PERSON> Dolla $ign, and played at the VULTURES Miami rave. Partial lossless iTunes file leaked July 31st, 2024.", "length": "194.45", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/40455126012e3e2082c9d17b89b24627", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/40455126012e3e2082c9d17b89b24627\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V7]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, will.i.am & <PERSON><PERSON><PERSON>)\", \"description\": \"Track 1 on tracklist posted by Ty Dolla $ign, and played at the VULTURES Miami rave. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8ef4d34a5657b92fb2b106531ae16e8d\", \"url\": \"https://api.pillowcase.su/api/download/8ef4d34a5657b92fb2b106531ae16e8d\", \"size\": \"8.12 MB\", \"duration\": 194.45}", "aliases": [], "size": "8.12 MB"}, {"id": "everybody-32", "name": "EVERYBODY [V8]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: EVERYBODY V1A 108BPM\nLoop with extra production. This version is a stem bounce.", "length": "53.33", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/781394dfd0cbb4ec2b48756adc96dd30", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/781394dfd0cbb4ec2b48756adc96dd30\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V8]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: EVERYBODY V1A 108BPM\\nLoop with extra production. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6107340b231db5a7863c00b19b9bf916\", \"url\": \"https://api.pillowcase.su/api/download/6107340b231db5a7863c00b19b9bf916\", \"size\": \"5.86 MB\", \"duration\": 53.33}", "aliases": [], "size": "5.86 MB"}, {"id": "everything", "name": "EVERYTHING [V2]", "artists": [], "producers": [], "notes": "OG Filename: EVERYTHING 11.1.23\n<PERSON><PERSON> Clem<PERSON> reference track for the \"Once Again\" part of the song. First previewed on his Instagram. Has a completely different instrumental compared to later versions.", "length": "13.7", "fileDate": 17072640, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3a59031503068b296e4f9b70ddf17bce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a59031503068b296e4f9b70ddf17bce\", \"key\": \"EVERYTHING\", \"title\": \"EVERYTHING [V2]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Once Again\"], \"description\": \"OG Filename: EVERYTHING 11.1.23\\nAnt Clemons reference track for the \\\"Once Again\\\" part of the song. First previewed on his Instagram. Has a completely different instrumental compared to later versions.\", \"date\": 17072640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"77ae19c6c95593e6edaa5c1241ce0075\", \"url\": \"https://api.pillowcase.su/api/download/77ae19c6c95593e6edaa5c1241ce0075\", \"size\": \"5.23 MB\", \"duration\": 13.7}", "aliases": ["Once Again"], "size": "5.23 MB"}, {"id": "disconnected", "name": "<PERSON> - DISCONNECTED [V2]", "artists": [], "producers": ["<PERSON><PERSON>", "Wheezy"], "notes": "OG Filename: AUDIO_5340\nVersion from after <PERSON> gave the beat to <PERSON>.", "length": "236.14", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/dc44c4985b98c22d8ae37d1c20b65969", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc44c4985b98c22d8ae37d1c20b65969\", \"key\": \"DISCONNECTED\", \"title\": \"<PERSON> Toliver - DISCONNECTED [V2]\", \"artists\": \"(prod. <PERSON><PERSON> & Wheezy)\", \"aliases\": [\"FIELD TRIP\"], \"description\": \"OG Filename: AUDIO_5340\\nVersion from after <PERSON> gave the beat to <PERSON>.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ca87dd0ce0d2f7e626df43adad167a95\", \"url\": \"https://api.pillowcase.su/api/download/ca87dd0ce0d2f7e626df43adad167a95\", \"size\": \"8.79 MB\", \"duration\": 236.14}", "aliases": ["FIELD TRIP"], "size": "8.79 MB"}, {"id": "field-trip", "name": "<PERSON> - FIELD TRIP [V3]", "artists": ["Kanye West"], "producers": ["<PERSON><PERSON>", "Wheezy"], "notes": "OG Filename: field trip -ye ref\nVersion with a mumble freestyle from <PERSON>. This freestyle was also recorded in the same session \"as another VULTURES era song\". Unknown what the other song is. Original snippet leaked March 5th, 2024.", "length": "267.54", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/06607852f94789cc2d02388765ba0034", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/06607852f94789cc2d02388765ba0034\", \"key\": \"FIELD TRIP\", \"title\": \"<PERSON> Toliver - FIELD TRIP [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON> & Wheezy)\", \"description\": \"OG Filename: field trip -ye ref\\nVersion with a mumble freestyle from <PERSON>. This freestyle was also recorded in the same session \\\"as another VULTURES era song\\\". Unknown what the other song is. Original snippet leaked March 5th, 2024.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"95c2136dc3d39f0ac1b01353b54237c0\", \"url\": \"https://api.pillowcase.su/api/download/95c2136dc3d39f0ac1b01353b54237c0\", \"size\": \"9.29 MB\", \"duration\": 267.54}", "aliases": [], "size": "9.29 MB"}, {"id": "field-trip-36", "name": "<PERSON><PERSON><PERSON> - FIELD TRIP [V4]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "Wheezy"], "notes": "OG Filename: pbc 12-12-23\nVersion from when it was given to <PERSON><PERSON><PERSON>, and still had <PERSON> on the hook.", "length": "206.56", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4aed1df309e1c9e23f58230f1dcc4908", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4aed1df309e1c9e23f58230f1dcc4908\", \"key\": \"FIELD TRIP\", \"title\": \"<PERSON><PERSON><PERSON> - FIELD TRIP [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> & Wheezy)\", \"description\": \"OG Filename: pbc 12-12-23\\nVersion from when it was given to <PERSON><PERSON><PERSON>, and still had <PERSON> on the hook.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"43751e4fb6b8898aaf1fc821039d7d18\", \"url\": \"https://api.pillowcase.su/api/download/43751e4fb6b8898aaf1fc821039d7d18\", \"size\": \"8.31 MB\", \"duration\": 206.56}", "aliases": [], "size": "8.31 MB"}, {"id": "field-trip-37", "name": "FIELD TRIP [V6]", "artists": ["<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version of the Ye freestyle with a different instrumental arrangement.", "length": "6.01", "fileDate": 16860096, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/bdc0b2f907c74f7eaceed6ccc62500fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bdc0b2f907c74f7eaceed6ccc62500fb\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V6]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Version of the Ye freestyle with a different instrumental arrangement.\", \"date\": 16860096, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d1cf9bf9e3ce8f1227a1a10fb72c9dea\", \"url\": \"https://api.pillowcase.su/api/download/d1cf9bf9e3ce8f1227a1a10fb72c9dea\", \"size\": \"5.11 MB\", \"duration\": 6.01}", "aliases": [], "size": "5.11 MB"}, {"id": "field-trip-38", "name": "FIELD TRIP [V7]", "artists": ["<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Cutdown of the Ye freestyle. <PERSON>ly made not long after the previous version.", "length": "19.93", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/83ae8eaba3b3145ce09850f1fe8185ed", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/83ae8eaba3b3145ce09850f1fe8185ed\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V7]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Cutdown of the Ye freestyle. Likely made not long after the previous version.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"82af7bdd4103cef811422d1f0111b59a\", \"url\": \"https://api.pillowcase.su/api/download/82af7bdd4103cef811422d1f0111b59a\", \"size\": \"5.33 MB\", \"duration\": 19.93}", "aliases": [], "size": "5.33 MB"}, {"id": "field-trip-39", "name": "FIELD TRIP [V8]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "This particular version of \"FIELD TRIP\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.", "length": "21.19", "fileDate": 17104608, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/468cb0402cb771aa37ba8510e3ae24d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/468cb0402cb771aa37ba8510e3ae24d1\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"This particular version of \\\"FIELD TRIP\\\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.\", \"date\": 17104608, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ebaa31921a08d8d84d78a69f838ffa3e\", \"url\": \"https://api.pillowcase.su/api/download/ebaa31921a08d8d84d78a69f838ffa3e\", \"size\": \"5.35 MB\", \"duration\": 21.19}", "aliases": [], "size": "5.35 MB"}, {"id": "field-trip-40", "name": "FIELD TRIP [V8]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "This particular version of \"FIELD TRIP\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.", "length": "20.32", "fileDate": 17104608, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f6a1de0a7aa536181dfaeab2ed9728a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f6a1de0a7aa536181dfaeab2ed9728a0\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"This particular version of \\\"FIELD TRIP\\\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.\", \"date\": 17104608, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d294d6f688a28ff1dbfd278fdf0cdd2\", \"url\": \"https://api.pillowcase.su/api/download/8d294d6f688a28ff1dbfd278fdf0cdd2\", \"size\": \"5.33 MB\", \"duration\": 20.32}", "aliases": [], "size": "5.33 MB"}, {"id": "field-trip-41", "name": "FIELD TRIP [V8]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "This particular version of \"FIELD TRIP\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.", "length": "4.91", "fileDate": 17104608, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4bba3829301a3a4c1c5db16f7d6ceb0c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bba3829301a3a4c1c5db16f7d6ceb0c\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"This particular version of \\\"FIELD TRIP\\\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.\", \"date\": 17104608, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"538283c1f8f02c71b2b547d591bcebe4\", \"url\": \"https://api.pillowcase.su/api/download/538283c1f8f02c71b2b547d591bcebe4\", \"size\": \"5.09 MB\", \"duration\": 4.91}", "aliases": [], "size": "5.09 MB"}, {"id": "field-trip-42", "name": "FIELD TRIP [V8]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "This particular version of \"FIELD TRIP\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.", "length": "103.39", "fileDate": 17104608, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/da4daaf2bfcb71e49a218942d1195cb3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/da4daaf2bfcb71e49a218942d1195cb3\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"This particular version of \\\"FIELD TRIP\\\" is from December 2023. Does not have a Ye verse. Original snippets leaked February 13th, 2024, and another snippet with more of <PERSON>'s vocals was posted by Lidestyworld on X/Twitter later. Other snippet leaked March 5th, 2024, and were played by <PERSON><PERSON><PERSON> on Instagram Live March 15th, 2024.\", \"date\": 17104608, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"008c2dcfcc126844932149fd0073f176\", \"url\": \"https://api.pillowcase.su/api/download/008c2dcfcc126844932149fd0073f176\", \"size\": \"6.66 MB\", \"duration\": 103.39}", "aliases": [], "size": "6.66 MB"}, {"id": "forever-rollin", "name": "FOREVER ROLLIN' [V4]", "artists": ["<PERSON>", "EST Gee"], "producers": ["FOREVEROLLING", "Flex on the Beat", "Audiovista", "Kanye West", "Mattazik Muzik"], "notes": "Version with EST's verse and has mumble Ye vocals. Snippet posted on Instagram by EST Gee on January 7th, 2024.", "length": "44.09", "fileDate": 17045856, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/9e53c8b58719a593184960aeecdbf0f7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9e53c8b58719a593184960aeecdbf0f7\", \"key\": \"FOREVER ROLLIN'\", \"title\": \"FOREVER ROLLIN' [V4]\", \"artists\": \"(feat. <PERSON> & <PERSON><PERSON>) (prod. <PERSON>OR<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> on the Beat, Audiovista, Kanye West & Mattazik Muzik)\", \"aliases\": [\"Meant It\", \"FOREVER ROLLING\"], \"description\": \"Version with <PERSON><PERSON>'s verse and has mumble Ye vocals. Snippet posted on Instagram by E<PERSON> Gee on January 7th, 2024.\", \"date\": 17045856, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1c8a77d35f1112d657d4b2334f2ddddf\", \"url\": \"https://api.pillowcase.su/api/download/1c8a77d35f1112d657d4b2334f2ddddf\", \"size\": \"5.71 MB\", \"duration\": 44.09}", "aliases": ["Meant It", "FOREVER ROLLING"], "size": "5.71 MB"}, {"id": "forever-rollin-44", "name": "✨ FOREVER ROLLIN' [V7]", "artists": ["<PERSON>"], "producers": ["FOREVEROLLING", "Flex on the Beat", "Audiovista", "Kanye West", "Mattazik Muzik"], "notes": "Version played at the VULTURES Miami rave. Was originally <PERSON>'s song in 2021, however it was given to <PERSON> sometime in 2023. Was later dropped on VULTURES 2 with a new beat and arrangement.", "length": "193.94", "fileDate": 17023392, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4d4b1456dd048a1ee116e3cb9f8eb561", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d4b1456dd048a1ee116e3cb9f8eb561\", \"key\": \"FOREVER ROLLIN'\", \"title\": \"\\u2728 FOREVER ROLLIN' [V7]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> on the Beat, Audiovista, Kanye West & Mattazik Muzik)\", \"aliases\": [\"Meant It\", \"FOREVER ROLLING\"], \"description\": \"Version played at the VULTURES Miami rave. Was originally <PERSON>'s song in 2021, however it was given to <PERSON> sometime in 2023. Was later dropped on VULTURES 2 with a new beat and arrangement.\", \"date\": 17023392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"74483261c9c58f37f3c7c8c3340423ed\", \"url\": \"https://api.pillowcase.su/api/download/74483261c9c58f37f3c7c8c3340423ed\", \"size\": \"8.11 MB\", \"duration\": 193.94}", "aliases": ["Meant It", "FOREVER ROLLING"], "size": "8.11 MB"}, {"id": "forever-rollin-45", "name": "FOREVER ROLLIN' [V8]", "artists": ["<PERSON>"], "producers": ["FOREVEROLLING", "Flex on the Beat", "Audiovista", "Kanye West", "Mattazik Muzik"], "notes": "Version that was accidentally uploaded as \"Drunk\" on the December iTunes copy of VULTURES.", "length": "110.73", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/eb3e1d0e22a12b5728588beffab8be30", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eb3e1d0e22a12b5728588beffab8be30\", \"key\": \"FOREVER ROLLIN'\", \"title\": \"FOREVER ROLLIN' [V8]\", \"artists\": \"(feat. <PERSON>) (prod. FOREVE<PERSON>LL<PERSON>, <PERSON><PERSON> on the Beat, Audiovista, Kanye West & Mattazik Muzik)\", \"aliases\": [\"Meant It\", \"FOREVER ROLLING\"], \"description\": \"Version that was accidentally uploaded as \\\"Drunk\\\" on the December iTunes copy of VULTURES.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a0b42e45d6a864e0d66c14708dade1b7\", \"url\": \"https://api.pillowcase.su/api/download/a0b42e45d6a864e0d66c14708dade1b7\", \"size\": \"6.78 MB\", \"duration\": 110.73}", "aliases": ["Meant It", "FOREVER ROLLING"], "size": "6.78 MB"}, {"id": "fuk-sumn", "name": "FUK SUMN [V10]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>"], "notes": "Alternate and likely updated version played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.", "length": "157.41", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f482f915c7f831aa5430629524de719b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f482f915c7f831aa5430629524de719b\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, <PERSON><PERSON><PERSON>, <PERSON>D\\u00d8<PERSON> & <PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Alternate and likely updated version played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d26ea3d5b9c1b1c31af01f55b26c637c\", \"url\": \"https://api.pillowcase.su/api/download/d26ea3d5b9c1b1c31af01f55b26c637c\", \"size\": \"7.53 MB\", \"duration\": 157.41}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "7.53 MB"}, {"id": "fuk-sumn-47", "name": "FUK SUMN [V10]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>"], "notes": "Alternate and likely updated version played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.", "length": "72", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/08462a82b7d6b5cc0b41e859358220f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08462a82b7d6b5cc0b41e859358220f2\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, <PERSON><PERSON><PERSON>, SHD\\u00d8<PERSON> & Hu<PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Alternate and likely updated version played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cd7f19a363e8947d6049273828223547\", \"url\": \"https://api.pillowcase.su/api/download/cd7f19a363e8947d6049273828223547\", \"size\": \"6.16 MB\", \"duration\": 72}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "6.16 MB"}, {"id": "fuck-sumn", "name": "FUCK SUMN [V12]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "<PERSON>"], "notes": "OG Filename: FUCK SUMN V2 - Prod.ShaunEnzo...\nUpdated version with <PERSON> production. Unknown exactly when it was made. Posted on Instagram by <PERSON> himself.", "length": "11.08", "fileDate": 17094240, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a0dffea7881a979ebc9108bee2ab9089", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0dffea7881a979ebc9108bee2ab9089\", \"key\": \"FUCK SUMN\", \"title\": \"FUCK SUMN [V12]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>s, <PERSON>bal<PERSON>, SH<PERSON>\\u00d8W, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUK SUM\"], \"description\": \"OG Filename: FUCK SUMN V2 - Prod.ShaunEnzo...\\nUpdated version with <PERSON> production. Unknown exactly when it was made. Posted on Instagram by <PERSON> himself.\", \"date\": 17094240, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e3259127b24023b6942e59ee3d9b2b9d\", \"url\": \"https://api.pillowcase.su/api/download/e3259127b24023b6942e59ee3d9b2b9d\", \"size\": \"5.19 MB\", \"duration\": 11.08}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUK SUM"], "size": "5.19 MB"}, {"id": "fuk-sumn-49", "name": "FUK SUMN [V13]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>"], "notes": "Version of with the updated beat and has the Quavo reference verse.", "length": "9.04", "fileDate": 17025984, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/80f9020b8af8dd628b34a6ba9c0d67fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80f9020b8af8dd628b34a6ba9c0d67fc\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V13]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8W & Hubi)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Version of with the updated beat and has the Quavo reference verse.\", \"date\": 17025984, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3696abbc0e22bfea81cac70daaa4c5d1\", \"url\": \"https://api.pillowcase.su/api/download/3696abbc0e22bfea81cac70daaa4c5d1\", \"size\": \"5.15 MB\", \"duration\": 9.04}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.15 MB"}, {"id": "gun-to-my-head", "name": "GUN TO MY HEAD [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Earlier version with <PERSON> doing more vocals on the hook, without <PERSON><PERSON> vocals and also FRE$H & Ty doing lines that <PERSON> eventually does on later versions. This version contains no freestyle outro presumably. Played at Miami brunch back in December. Partial iTunes file leaked July 31st, 2024.", "length": "96", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/20f8e476fcc46afe28462e063e7ec13a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20f8e476fcc46afe28462e063e7ec13a\", \"key\": \"GUN TO MY HEAD\", \"title\": \"GUN TO MY HEAD [V2]\", \"artists\": \"(ref. <PERSON> $ign & FRE$H) (prod. <PERSON>)\", \"description\": \"Earlier version with <PERSON> doing more vocals on the hook, without <PERSON><PERSON> vocals and also F<PERSON>$H & Ty doing lines that <PERSON> eventually does on later versions. This version contains no freestyle outro presumably. Played at Miami brunch back in December. Partial iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"48a3292fa0b56e62bb0b3ae610d1e17c\", \"url\": \"https://api.pillowcase.su/api/download/48a3292fa0b56e62bb0b3ae610d1e17c\", \"size\": \"6.55 MB\", \"duration\": 96}", "aliases": [], "size": "6.55 MB"}, {"id": "gun-to-my-head-51", "name": "GUN TO MY HEAD [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Earlier version with <PERSON> doing more vocals on the hook, without <PERSON><PERSON> vocals and also FRE$H & Ty doing lines that <PERSON> eventually does on later versions. This version contains no freestyle outro presumably. Played at Miami brunch back in December. Partial iTunes file leaked July 31st, 2024.", "length": "22.52", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6940a438559917c73220fc8ad9001e9c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6940a438559917c73220fc8ad9001e9c\", \"key\": \"GUN TO MY HEAD\", \"title\": \"GUN TO MY HEAD [V2]\", \"artists\": \"(ref. <PERSON> $ign & FRE$H) (prod. <PERSON>)\", \"description\": \"Earlier version with <PERSON> doing more vocals on the hook, without <PERSON><PERSON> vocals and also <PERSON><PERSON>$H & Ty doing lines that <PERSON> eventually does on later versions. This version contains no freestyle outro presumably. Played at Miami brunch back in December. Partial iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5d702e8f6f3ca7cbb43c0e40531d93f5\", \"url\": \"https://api.pillowcase.su/api/download/5d702e8f6f3ca7cbb43c0e40531d93f5\", \"size\": \"5.37 MB\", \"duration\": 22.52}", "aliases": [], "size": "5.37 MB"}, {"id": "gun-to-my-head-52", "name": "GUN TO MY HEAD [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Earlier version with <PERSON> doing more vocals on the hook, without <PERSON><PERSON> vocals and also FRE$H & Ty doing lines that <PERSON> eventually does on later versions. This version contains no freestyle outro presumably. Played at Miami brunch back in December. Partial iTunes file leaked July 31st, 2024.", "length": "8.91", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/32adeae08cb06012a5dd99e34c4b5b98", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/32adeae08cb06012a5dd99e34c4b5b98\", \"key\": \"GUN TO MY HEAD\", \"title\": \"GUN TO MY HEAD [V2]\", \"artists\": \"(ref. <PERSON> $ign & FRE$H) (prod. <PERSON>)\", \"description\": \"Earlier version with <PERSON> doing more vocals on the hook, without <PERSON><PERSON> vocals and also FRE$H & Ty doing lines that <PERSON> eventually does on later versions. This version contains no freestyle outro presumably. Played at Miami brunch back in December. Partial iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6c74282107885afc27a91961d2aa5791\", \"url\": \"https://api.pillowcase.su/api/download/6c74282107885afc27a91961d2aa5791\", \"size\": \"5.08 MB\", \"duration\": 8.91}", "aliases": [], "size": "5.08 MB"}, {"id": "gun-to-my-head-53", "name": "✨ GUN TO MY HEAD [V3]", "artists": ["<PERSON>"], "producers": ["Ojivolta", "<PERSON>"], "notes": "Track 17 on tracklist posted by <PERSON> Dolla $ign. <PERSON> added <PERSON> vocals, and a freestyle outro not heard on the brunch version. Played in full at the Las Vegas stream. Would later release as a digital deluxe for VULTURES 2 however it misses the second part of the song entirely.", "length": "340.3", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/af7ff44ed8fa75e3fc676c7400d6ff95", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/af7ff44ed8fa75e3fc676c7400d6ff95\", \"key\": \"GUN TO MY HEAD\", \"title\": \"\\u2728 GUN TO MY HEAD [V3]\", \"artists\": \"(feat. <PERSON>) (prod. Ojivolta & Lester <PERSON>)\", \"description\": \"Track 17 on tracklist posted by Ty Dolla $ign. <PERSON> added <PERSON> vocals, and a freestyle outro not heard on the brunch version. Played in full at the Las Vegas stream. Would later release as a digital deluxe for VULTURES 2 however it misses the second part of the song entirely.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a723cf4cab7abe42fce224318c6bd4c6\", \"url\": \"https://api.pillowcase.su/api/download/a723cf4cab7abe42fce224318c6bd4c6\", \"size\": \"10.5 MB\", \"duration\": 340.3}", "aliases": [], "size": "10.5 MB"}, {"id": "honor-roll", "name": "HONOR ROLL [V2]", "artists": ["<PERSON>"], "producers": ["TheLabCook"], "notes": "Played in the Las Vegas Rave. Confirmed to be named \"Honor Roll\" at the time. Has a completely different Ye verse compared to the release version. Samples \"Iron Man\" by Black Sabbath.", "length": "117.6", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/76692bbba133badd2b61000a18949c95", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76692bbba133badd2b61000a18949c95\", \"key\": \"HONOR ROLL\", \"title\": \"HONOR ROLL [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"CARNIVAL\", \"H00LIGANS\"], \"description\": \"Played in the Las Vegas Rave. Confirmed to be named \\\"Honor Roll\\\" at the time. Has a completely different Ye verse compared to the release version. <PERSON><PERSON> \\\"Iron Man\\\" by Black Sabbath.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7671ae33e13d36e0fcfd2fda467b7f44\", \"url\": \"https://api.pillowcase.su/api/download/7671ae33e13d36e0fcfd2fda467b7f44\", \"size\": \"6.89 MB\", \"duration\": 117.6}", "aliases": ["CARNIVAL", "H00LIGANS"], "size": "6.89 MB"}, {"id": "honor-roll-55", "name": "HONOR ROLL [V3]", "artists": ["<PERSON>"], "producers": ["TheLabCook"], "notes": "Played on Instagram live after the Las Vegas Rave. <PERSON> says \"new Yeezy bag\" instead of \"old L(ouis bag)\", unknown which was recorded first, but it's more likely this was recorded after.", "length": "161", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a1c2889e47af753ebbf5f9226c02b1c6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a1c2889e47af753ebbf5f9226c02b1c6\", \"key\": \"HONOR ROLL\", \"title\": \"HONOR ROLL [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"CARNIVAL\", \"H00LIGANS\"], \"description\": \"Played on Instagram live after the Las Vegas Rave. Ye says \\\"new Yeezy bag\\\" instead of \\\"old L(ouis bag)\\\", unknown which was recorded first, but it's more likely this was recorded after.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"cdbd8956dfbc352484f7036116e1a4e9\", \"url\": \"https://api.pillowcase.su/api/download/cdbd8956dfbc352484f7036116e1a4e9\", \"size\": \"7.59 MB\", \"duration\": 161}", "aliases": ["CARNIVAL", "H00LIGANS"], "size": "7.59 MB"}, {"id": "honor-roll-56", "name": "HONOR ROLL [V4]", "artists": ["<PERSON>"], "producers": ["TheLabCook"], "notes": "Videos of the new verse being recorded on the 15th of December 2023. Contains the OG production.", "length": "36.78", "fileDate": 17025984, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d5a3073b91f3d631c8c792939c505547", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d5a3073b91f3d631c8c792939c505547\", \"key\": \"HONOR ROLL\", \"title\": \"HONOR ROLL [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"CARNIVAL\", \"H00LIGANS\"], \"description\": \"Videos of the new verse being recorded on the 15th of December 2023. Contains the OG production.\", \"date\": 17025984, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"56d103ce5f57a7aef4d76fee30b2a848\", \"url\": \"https://api.pillowcase.su/api/download/56d103ce5f57a7aef4d76fee30b2a848\", \"size\": \"5.3 MB\", \"duration\": 36.78}", "aliases": ["CARNIVAL", "H00LIGANS"], "size": "5.3 MB"}, {"id": "hood-rat", "name": "HOOD RAT [V6]", "artists": [], "producers": [], "notes": "OG Filename: 1-01 <PERSON> Rat\nEarly version. Made on or before Dec 15, 2023", "length": "126.17", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1ce42c1275997c217fdd6150a3e59b01", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ce42c1275997c217fdd6150a3e59b01\", \"key\": \"HOOD RAT\", \"title\": \"HOOD RAT [V6]\", \"aliases\": [\"HOODRAT\"], \"description\": \"OG Filename: 1-01 <PERSON> <PERSON>\\nEarly version. Made on or before Dec 15, 2023\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"64bd4cec9923e04ffa805e4f7a12085f\", \"url\": \"https://api.pillowcase.su/api/download/64bd4cec9923e04ffa805e4f7a12085f\", \"size\": \"7.03 MB\", \"duration\": 126.17}", "aliases": ["HOODRAT"], "size": "7.03 MB"}, {"id": "hood-rat-58", "name": "✨ HOOD RAT [V7]", "artists": [], "producers": [], "notes": "OG Filename: HOOD RAT V1A STMS\nVersion with the same first part as release but with a beatswitch not seen in any other versions of the song. This version is a stem bounce.", "length": "126.17", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7456bb0607a5b6aeddad1f2f11e63c3b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7456bb0607a5b6aeddad1f2f11e63c3b\", \"key\": \"HOOD RAT\", \"title\": \"\\u2728 HOOD RAT [V7]\", \"aliases\": [\"HOODRAT\"], \"description\": \"OG Filename: HOOD RAT V1A STMS\\nVersion with the same first part as release but with a beatswitch not seen in any other versions of the song. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e92f39360064ad4b8be48e279f0f4f01\", \"url\": \"https://api.pillowcase.su/api/download/e92f39360064ad4b8be48e279f0f4f01\", \"size\": \"7.03 MB\", \"duration\": 126.17}", "aliases": ["HOODRAT"], "size": "7.03 MB"}, {"id": "lifestyle", "name": "LIFESTYLE [V2]", "artists": [], "producers": ["<PERSON>", "FnZ", "London on da Track"], "notes": "iTunes tracklist version with no <PERSON> verse, way rougher mixing and a faster BPM not seen in later versions. Partial lossless iTunes file leaked July 31st, 2024.", "length": "136", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/41aa14e94a03444046eccc88071d224b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/41aa14e94a03444046eccc88071d224b\", \"key\": \"LIFESTYLE\", \"title\": \"LIFESTYLE [V2]\", \"artists\": \"(prod. <PERSON>, FnZ & London on da Track)\", \"description\": \"iTunes tracklist version with no <PERSON> verse, way rougher mixing and a faster BPM not seen in later versions. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6ddedb5a1152efc4e934365803eba69\", \"url\": \"https://api.pillowcase.su/api/download/d6ddedb5a1152efc4e934365803eba69\", \"size\": \"7.19 MB\", \"duration\": 136}", "aliases": [], "size": "7.19 MB"}, {"id": "lifestyle-60", "name": "LIFESTYLE [V3]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "FnZ", "London on da Track"], "notes": "Track 13 on tracklist posted by Ty Dolla $ign. Interpolates \"Runaway\". Samples \"LOVE IS GONE\" by <PERSON> (although this song uses vocals which are probably taken from a sample pack). Played in full at Vultures City.", "length": "341.66", "fileDate": 17038944, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8ed8d8dd738be41e86f38637edad57b4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ed8d8dd738be41e86f38637edad57b4\", \"key\": \"LIFESTYLE\", \"title\": \"LIFESTYLE [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, FnZ & London on da Track)\", \"description\": \"Track 13 on tracklist posted by Ty Dolla $ign. Interpolates \\\"Runaway\\\". <PERSON><PERSON> \\\"LOVE IS GONE\\\" by <PERSON> (although this song uses vocals which are probably taken from a sample pack). Played in full at Vultures City.\", \"date\": 17038944, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b9dc912a1f458fec2ec50ddb36bb51d9\", \"url\": \"https://api.pillowcase.su/api/download/b9dc912a1f458fec2ec50ddb36bb51d9\", \"size\": \"10.5 MB\", \"duration\": 341.66}", "aliases": [], "size": "10.5 MB"}, {"id": "lifestyle-61", "name": "LIFESTYLE [V3]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "FnZ", "London on da Track"], "notes": "Track 13 on tracklist posted by Ty Dolla $ign. Interpolates \"Runaway\". Samples \"LOVE IS GONE\" by <PERSON> (although this song uses vocals which are probably taken from a sample pack). Played in full at Vultures City.", "length": "13.45", "fileDate": 17038944, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/aeee229870264b206c6ba5a233356cbc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aeee229870264b206c6ba5a233356cbc\", \"key\": \"LIFESTYLE\", \"title\": \"LIFESTYLE [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, FnZ & London on da Track)\", \"description\": \"Track 13 on tracklist posted by Ty Dolla $ign. Interpolates \\\"Runaway\\\". <PERSON><PERSON> \\\"LOVE IS GONE\\\" by <PERSON> (although this song uses vocals which are probably taken from a sample pack). Played in full at Vultures City.\", \"date\": 17038944, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b3ea36bab9f32c21e217d1170d878ddc\", \"url\": \"https://api.pillowcase.su/api/download/b3ea36bab9f32c21e217d1170d878ddc\", \"size\": \"5.22 MB\", \"duration\": 13.45}", "aliases": [], "size": "5.22 MB"}, {"id": "make-it-feel-right", "name": "MAKE IT FEEL RIGHT [V1]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: make it feel right 90bpm lot x sm v1_Master\nEarly VULTURES-era song. Has some Ty vocals near the end of the song, as adlibs. Leaked alongside its stems.", "length": "226.85", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/73e13c40ac664562b891978cc9388b15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/73e13c40ac664562b891978cc9388b15\", \"key\": \"MAKE IT FEEL RIGHT\", \"title\": \"MAKE IT FEEL RIGHT [V1]\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"NEXT TIME\"], \"description\": \"OG Filename: make it feel right 90bpm lot x sm v1_Master\\nEarly VULTURES-era song. Has some Ty vocals near the end of the song, as adlibs. Leaked alongside its stems.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1aa5cf3ff2951371844b665ba0be9e52\", \"url\": \"https://api.pillowcase.su/api/download/1aa5cf3ff2951371844b665ba0be9e52\", \"size\": \"8.64 MB\", \"duration\": 226.85}", "aliases": ["NEXT TIME"], "size": "8.64 MB"}, {"id": "matthew", "name": "MATTHEW [V4]", "artists": ["North West", "<PERSON>"], "producers": ["88-<PERSON>"], "notes": "Version of \"<PERSON>\" played in the Las Vegas Rave. Samples a <PERSON><PERSON><PERSON><PERSON><PERSON> clip.", "length": "153.65", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6c29a87a71aac96ac4f95ceaff346582", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6c29a87a71aac96ac4f95ceaff346582\", \"key\": \"MATTHEW\", \"title\": \"MATTHEW [V4]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys)\", \"description\": \"Version of \\\"Matthew\\\" played in the Las Vegas Rave. Samples a <PERSON><PERSON><PERSON><PERSON><PERSON> clip.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7f75d9e9326f180465100e161b27a1fc\", \"url\": \"https://api.pillowcase.su/api/download/7f75d9e9326f180465100e161b27a1fc\", \"size\": \"7.47 MB\", \"duration\": 153.65}", "aliases": [], "size": "7.47 MB"}, {"id": "matthew-64", "name": "MATTHEW [V5]", "artists": ["North West", "<PERSON>", "Faouzia"], "producers": [], "notes": "On the December 15th Vultures City afterparty, <PERSON><PERSON><PERSON><PERSON> is shown doing multiple takes for \"<PERSON>\".", "length": "", "fileDate": 17025984, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/492e767df80750a02d1ee6af4ff8bdf6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/492e767df80750a02d1ee6af4ff8bdf6\", \"key\": \"MATTHEW\", \"title\": \"MATTHEW [V5]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON>uz<PERSON>)\", \"description\": \"On the December 15th Vultures City afterparty, Faouzia is shown doing multiple takes for \\\"Matthew\\\".\", \"date\": 17025984, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "matthew-65", "name": "MATTHEW [V5]", "artists": ["North West", "<PERSON>", "Faouzia"], "producers": [], "notes": "On the December 15th Vultures City afterparty, <PERSON><PERSON><PERSON><PERSON> is shown doing multiple takes for \"<PERSON>\".", "length": "", "fileDate": 17025984, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7e7b728dbeebe25f42e00a1ebb141592", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e7b728dbeebe25f42e00a1ebb141592\", \"key\": \"MATTHEW\", \"title\": \"MATTHEW [V5]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"On the December 15th Vultures City afterparty, Faouzia is shown doing multiple takes for \\\"Matthew\\\".\", \"date\": 17025984, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "new-body", "name": "NEW BODY [V35]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J"], "notes": "Version of \"New Body\" played at the VULTURES Miami rave. Very similar to the Yandhi versions of the track; however, a line from <PERSON>'s verse is repeated in the chorus, the RONNY J tag is faster and has a different echo, and an extra word from <PERSON>'s freestyle is added to his verse. Heavily distorted due to microphone audio.", "length": "222.64", "fileDate": 17023392, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e5c2064795bc5c682aea8578f6f282ba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e5c2064795bc5c682aea8578f6f282ba\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V35]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. RONNY J)\", \"description\": \"Version of \\\"New Body\\\" played at the VULTURES Miami rave. Very similar to the Yandhi versions of the track; however, a line from <PERSON>'s verse is repeated in the chorus, the RONNY J tag is faster and has a different echo, and an extra word from <PERSON>'s freestyle is added to his verse. Heavily distorted due to microphone audio.\", \"date\": 17023392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a06ac9772a83879c5cade03177edbc7b\", \"url\": \"https://api.pillowcase.su/api/download/a06ac9772a83879c5cade03177edbc7b\", \"size\": \"8.57 MB\", \"duration\": 222.64}", "aliases": [], "size": "8.57 MB"}, {"id": "new-body-67", "name": "NEW BODY [V36]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J"], "notes": "Version of \"New Body\" loaded onto iTunes for the planned December 15th release of VULTURES. Seemingly very similar to the version played in Miami, but with a new mix. Partial lossless iTunes file leaked July 31st, 2024.", "length": "124", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f4409cc73594de22d0e651baaea192d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f4409cc73594de22d0e651baaea192d8\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V36]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. RONNY J)\", \"description\": \"Version of \\\"New Body\\\" loaded onto iTunes for the planned December 15th release of VULTURES. Seemingly very similar to the version played in Miami, but with a new mix. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"512f7de9b7fd4565040f3e2760eb0e7d\", \"url\": \"https://api.pillowcase.su/api/download/512f7de9b7fd4565040f3e2760eb0e7d\", \"size\": \"6.99 MB\", \"duration\": 124}", "aliases": [], "size": "6.99 MB"}, {"id": "paid", "name": "PAID [V7]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "Version played during DJ <PERSON><PERSON><PERSON>' album premiere. <PERSON> added radio FX throughout the song.", "length": "185.86", "fileDate": 17025120, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4464731daf30ccc55fe3418e8e2d4bdc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4464731daf30ccc55fe3418e8e2d4bdc\", \"key\": \"PAID\", \"title\": \"PAID [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"Version played during <PERSON>' album premiere. Has added radio FX throughout the song.\", \"date\": 17025120, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ae3392e63731329733b49e41917ab963\", \"url\": \"https://api.pillowcase.su/api/download/ae3392e63731329733b49e41917ab963\", \"size\": \"7.98 MB\", \"duration\": 185.86}", "aliases": [], "size": "7.98 MB"}, {"id": "paid-69", "name": "PAID [V8]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "Song played at the Miami listening event. Features K-Ci. Partial lossless iTunes file leaked July 31st, 2024.", "length": "181.53", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4a664d0575a94a5759d1494a663becc5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a664d0575a94a5759d1494a663becc5\", \"key\": \"PAID\", \"title\": \"PAID [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"Song played at the Miami listening event. Features K-Ci. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"45a131a3c3a5db9ad664afce0bc44a37\", \"url\": \"https://api.pillowcase.su/api/download/45a131a3c3a5db9ad664afce0bc44a37\", \"size\": \"7.91 MB\", \"duration\": 181.53}", "aliases": [], "size": "7.91 MB"}, {"id": "paid-70", "name": "PAID [V8]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "Song played at the Miami listening event. Features K-Ci. Partial lossless iTunes file leaked July 31st, 2024.", "length": "104.43", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/5308ad790743a5c629c8e0b0e6973ca4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5308ad790743a5c629c8e0b0e6973ca4\", \"key\": \"PAID\", \"title\": \"PAID [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"Song played at the Miami listening event. Features K-Ci. Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6a444569436200a344f766964eabcd2d\", \"url\": \"https://api.pillowcase.su/api/download/6a444569436200a344f766964eabcd2d\", \"size\": \"6.68 MB\", \"duration\": 104.43}", "aliases": [], "size": "6.68 MB"}, {"id": "paid-71", "name": "PAID [V9]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "OG Filename: PAID 118STMS\nLoop version with more production. This version is a stem bounce.", "length": "28.47", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/0f29fdf2270388464c87e6101c4e446d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f29fdf2270388464c87e6101c4e446d\", \"key\": \"PAID\", \"title\": \"PAID [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"OG Filename: PAID 118STMS\\nLoop version with more production. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2f5ab734dcf6758de06c62e8bd8ac02d\", \"url\": \"https://api.pillowcase.su/api/download/2f5ab734dcf6758de06c62e8bd8ac02d\", \"size\": \"5.47 MB\", \"duration\": 28.47}", "aliases": [], "size": "5.47 MB"}, {"id": "paid-72", "name": "PAID [V10]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "OG Filename: PAID ADDS 12.14.23\nLoop version with more production. This version is a stem bounce.", "length": "81.36", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7aaf2b08e80d7127a139195d0d49215f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7aaf2b08e80d7127a139195d0d49215f\", \"key\": \"PAID\", \"title\": \"PAID [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"OG Filename: PAID ADDS 12.14.23\\nLoop version with more production. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9bd05ffb879b632d5c8cf8304ff88ed8\", \"url\": \"https://api.pillowcase.su/api/download/9bd05ffb879b632d5c8cf8304ff88ed8\", \"size\": \"6.31 MB\", \"duration\": 81.36}", "aliases": [], "size": "6.31 MB"}, {"id": "paid-73", "name": "PAID [V11]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "OG Filename: PAID - Track 1\nVersion of the track made before or on December 14th 2023.", "length": "183.09", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/26711a9be17a44570a4de5b16af6d36d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/26711a9be17a44570a4de5b16af6d36d\", \"key\": \"PAID\", \"title\": \"PAID [V11]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"OG Filename: PAID - Track 1\\nVersion of the track made before or on December 14th 2023.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f5a1177497e1a645881b0aac5a74dabb\", \"url\": \"https://api.pillowcase.su/api/download/f5a1177497e1a645881b0aac5a74dabb\", \"size\": \"7.94 MB\", \"duration\": 183.09}", "aliases": [], "size": "7.94 MB"}, {"id": "paid-74", "name": "PAID [V12]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "OG Filename: PAID\nVersion with added production. This version is a stem bounce.", "length": "183.09", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1309ce965625ee7a01eedca0b11b9333", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1309ce965625ee7a01eedca0b11b9333\", \"key\": \"PAID\", \"title\": \"PAID [V12]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"OG Filename: PAID\\nVersion with added production. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2b2a3d3cd8e49ebecac24c890bfed2f8\", \"url\": \"https://api.pillowcase.su/api/download/2b2a3d3cd8e49ebecac24c890bfed2f8\", \"size\": \"7.94 MB\", \"duration\": 183.09}", "aliases": [], "size": "7.94 MB"}, {"id": "paperwork", "name": "PAPERWORK [V7]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Version of \"Paperwork\" with production from 88-<PERSON>. Played on Instagram live December 14th, 2024. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.", "length": "30.82", "fileDate": 17341344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/726dcd91fe6f452abe0df87de163cec1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/726dcd91fe6f452abe0df87de163cec1\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V7]\", \"artists\": \"(ref. <PERSON>ua<PERSON>) (prod. 88-Keys)\", \"description\": \"Version of \\\"Paperwork\\\" with production from 88-Keys. Played on Instagram live December 14th, 2024. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.\", \"date\": 17341344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f7ed9627cc2d6d9daea532608ec83f4d\", \"url\": \"https://api.pillowcase.su/api/download/f7ed9627cc2d6d9daea532608ec83f4d\", \"size\": \"5.5 MB\", \"duration\": 30.82}", "aliases": [], "size": "5.5 MB"}, {"id": "paperwork-76", "name": "PAPERWORK [V8]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Another version of \"Paperwork\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.", "length": "20.06", "fileDate": 17341344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3dd03f2d6fb0a7e8b1486ece6eb2f993", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3dd03f2d6fb0a7e8b1486ece6eb2f993\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V8]\", \"artists\": \"(ref. <PERSON>ua<PERSON>) (prod. 88-Keys)\", \"description\": \"Another version of \\\"Paperwork\\\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.\", \"date\": 17341344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5f684492f5273470618ffa8adc93b613\", \"url\": \"https://api.pillowcase.su/api/download/5f684492f5273470618ffa8adc93b613\", \"size\": \"5.33 MB\", \"duration\": 20.06}", "aliases": [], "size": "5.33 MB"}, {"id": "paperwork-77", "name": "PAPERWORK [V9]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Another version of \"Paperwork\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.", "length": "26.38", "fileDate": 17341344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f6113d1abd4956364fa506da86d11c0c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f6113d1abd4956364fa506da86d11c0c\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V9]\", \"artists\": \"(ref. <PERSON>ua<PERSON>) (prod. 88-Keys)\", \"description\": \"Another version of \\\"Paperwork\\\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.\", \"date\": 17341344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"451893bad04fe2829a55d99b6b713f72\", \"url\": \"https://api.pillowcase.su/api/download/451893bad04fe2829a55d99b6b713f72\", \"size\": \"5.43 MB\", \"duration\": 26.38}", "aliases": [], "size": "5.43 MB"}, {"id": "paperwork-78", "name": "PAPERWORK [V10]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Another version of \"Paperwork\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.", "length": "26.5", "fileDate": 17341344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/5dda589989c4b6a0887fb39513ecdc9b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5dda589989c4b6a0887fb39513ecdc9b\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V10]\", \"artists\": \"(ref. <PERSON>uavo) (prod. 88-Keys)\", \"description\": \"Another version of \\\"Paperwork\\\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.\", \"date\": 17341344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f6419cc7b0b52f811c9e78f4eca230b8\", \"url\": \"https://api.pillowcase.su/api/download/f6419cc7b0b52f811c9e78f4eca230b8\", \"size\": \"5.43 MB\", \"duration\": 26.5}", "aliases": [], "size": "5.43 MB"}, {"id": "paperwork-79", "name": "PAPERWORK [V11]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Another version of \"Paperwork\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.", "length": "13.68", "fileDate": 17341344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/95b5ef3ade835683b43e7f79ea7d004d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/95b5ef3ade835683b43e7f79ea7d004d\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V11]\", \"artists\": \"(ref. <PERSON>ua<PERSON>) (prod. 88-Keys)\", \"description\": \"Another version of \\\"Paperwork\\\" with production from 88-Keys. All these versions use Quavos ref that <PERSON><PERSON><PERSON> would later record over but with release production.\", \"date\": 17341344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"850be24b7630aa6b097ca1e08da14ddd\", \"url\": \"https://api.pillowcase.su/api/download/850be24b7630aa6b097ca1e08da14ddd\", \"size\": \"5.23 MB\", \"duration\": 13.68}", "aliases": [], "size": "5.23 MB"}, {"id": "promotion", "name": "PROMOTION [V7]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Track 11 on tracklist posted by <PERSON> Dolla $ign. Snippet played at Dukunoo Jamaican Kitchen and Vultures City. Has a Ye mumble verse, a Future feature and a totally different instrumental. Snippets leaked December 10th, 2023 & October 28th, 2024.", "length": "30.04", "fileDate": 17300736, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/5cd8482082d01e8f0b4447226bf2fce6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5cd8482082d01e8f0b4447226bf2fce6\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V7]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Track 11 on tracklist posted by Ty Dolla $ign. Snippet played at Dukunoo Jamaican Kitchen and Vultures City. Has a Ye mumble verse, a Future feature and a totally different instrumental. Snippets leaked December 10th, 2023 & October 28th, 2024.\", \"date\": 17300736, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"91966e6d919e6dc76927f05b5f7b6fe0\", \"url\": \"https://api.pillowcase.su/api/download/91966e6d919e6dc76927f05b5f7b6fe0\", \"size\": \"5.25 MB\", \"duration\": 30.04}", "aliases": ["GORGEOUS"], "size": "5.25 MB"}, {"id": "promotion-81", "name": "PROMOTION [V7]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Track 11 on tracklist posted by <PERSON> Dolla $ign. Snippet played at Dukunoo Jamaican Kitchen and Vultures City. Has a Ye mumble verse, a Future feature and a totally different instrumental. Snippets leaked December 10th, 2023 & October 28th, 2024.", "length": "30.67", "fileDate": 17300736, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c2e9ad2d6b159f4ee78db2d9d9301754", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c2e9ad2d6b159f4ee78db2d9d9301754\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V7]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Track 11 on tracklist posted by Ty Dolla $ign. Snippet played at Dukunoo Jamaican Kitchen and Vultures City. Has a Ye mumble verse, a Future feature and a totally different instrumental. Snippets leaked December 10th, 2023 & October 28th, 2024.\", \"date\": 17300736, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"04f7961b77c947936a8371e33d2ac1d3\", \"url\": \"https://api.pillowcase.su/api/download/04f7961b77c947936a8371e33d2ac1d3\", \"size\": \"5.5 MB\", \"duration\": 30.67}", "aliases": ["GORGEOUS"], "size": "5.5 MB"}, {"id": "promotion-82", "name": "PROMOTION [V7]", "artists": ["Future"], "producers": ["AyoAA", "Wheezy", "London on da Track", "The Legendary Traxster"], "notes": "Track 11 on tracklist posted by <PERSON> Dolla $ign. Snippet played at Dukunoo Jamaican Kitchen and Vultures City. Has a Ye mumble verse, a Future feature and a totally different instrumental. Snippets leaked December 10th, 2023 & October 28th, 2024.", "length": "28.79", "fileDate": 17300736, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/93ab04d79a609dc6455d25ae5c099686", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/93ab04d79a609dc6455d25ae5c099686\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V7]\", \"artists\": \"(feat. Future) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, London on da Track & The Legendary Traxster)\", \"aliases\": [\"GORGEOUS\"], \"description\": \"Track 11 on tracklist posted by Ty Dolla $ign. Snippet played at Dukunoo Jamaican Kitchen and Vultures City. Has a Ye mumble verse, a Future feature and a totally different instrumental. Snippets leaked December 10th, 2023 & October 28th, 2024.\", \"date\": 17300736, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"a4658c0e3f27c33fdda4a0296ce0f794\", \"url\": \"https://api.pillowcase.su/api/download/a4658c0e3f27c33fdda4a0296ce0f794\", \"size\": \"5.47 MB\", \"duration\": 28.79}", "aliases": ["GORGEOUS"], "size": "5.47 MB"}, {"id": "gorgeous", "name": "GORGEOUS [V8]", "artists": [], "producers": [], "notes": "OG Filename: Gorgeous\nInstrumental for an early version of \"Gorgeous\". This version is a stem bounce.", "length": "162.04", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4cbac8ac5b218cf2a9fa1d87821d9ab1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4cbac8ac5b218cf2a9fa1d87821d9ab1\", \"key\": \"GORGEOUS\", \"title\": \"GORGEOUS [V8]\", \"aliases\": [\"PROMOTION\"], \"description\": \"OG Filename: Gorgeous\\nInstrumental for an early version of \\\"Gorgeous\\\". This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f08d816f9f4781a85c4187eb721cdc67\", \"url\": \"https://api.pillowcase.su/api/download/f08d816f9f4781a85c4187eb721cdc67\", \"size\": \"7.6 MB\", \"duration\": 162.04}", "aliases": ["PROMOTION"], "size": "7.6 MB"}, {"id": "gorgeous-84", "name": "GORGEOUS [V9]", "artists": [], "producers": [], "notes": "OG Filename: GORGEIUS STEMS V1A 157BPM\nInstrumental for an early version of \"Gorgeous\". This version is a stem bounce.", "length": "152.87", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/372696da7c95d6da40b4609b34427bd1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/372696da7c95d6da40b4609b34427bd1\", \"key\": \"GORGEOUS\", \"title\": \"GORGEOUS [V9]\", \"aliases\": [\"PROMOTION\"], \"description\": \"OG Filename: GORGEIUS STEMS V1A 157BPM\\nInstrumental for an early version of \\\"Gorgeous\\\". This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5c2c72a4a936843c95b5c615d890ca5c\", \"url\": \"https://api.pillowcase.su/api/download/5c2c72a4a936843c95b5c615d890ca5c\", \"size\": \"7.46 MB\", \"duration\": 152.87}", "aliases": ["PROMOTION"], "size": "7.46 MB"}, {"id": "gorgeous-85", "name": "GORGEOUS [V10]", "artists": [], "producers": [], "notes": "OG Filename: GORGEOUS V1B 157BPM\nInstrumental for an early version of \"Gorgeous\". This version is a stem bounce.", "length": "152.87", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/16589966581a9c8c07125dc953e3c9a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16589966581a9c8c07125dc953e3c9a6\", \"key\": \"GORGEOUS\", \"title\": \"GORGEOUS [V10]\", \"aliases\": [\"PROMOTION\"], \"description\": \"OG Filename: GORGEOUS V1B 157BPM\\nInstrumental for an early version of \\\"Gorgeous\\\". This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"05bd98a31c03eefc886d6dc39b793f35\", \"url\": \"https://api.pillowcase.su/api/download/05bd98a31c03eefc886d6dc39b793f35\", \"size\": \"7.46 MB\", \"duration\": 152.87}", "aliases": ["PROMOTION"], "size": "7.46 MB"}, {"id": "promotion-86", "name": "PROMOTION [V11]", "artists": [], "producers": [], "notes": "OG Filename: promotion piano intro\nInstrumental for an early version of \"Promotion\". This version is a stem bounce.", "length": "151.34", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ad27d74427c81acdc9f3aae9503d4b76", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad27d74427c81acdc9f3aae9503d4b76\", \"key\": \"PROMOTION\", \"title\": \"PROMOTION [V11]\", \"aliases\": [\"GORGEOUS\"], \"description\": \"OG Filename: promotion piano intro\\nInstrumental for an early version of \\\"Promotion\\\". This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d74d6fb317c3059a1c29f701fc7293e3\", \"url\": \"https://api.pillowcase.su/api/download/d74d6fb317c3059a1c29f701fc7293e3\", \"size\": \"7.43 MB\", \"duration\": 151.34}", "aliases": ["GORGEOUS"], "size": "7.43 MB"}, {"id": "river", "name": "RIVER [V14]", "artists": ["<PERSON> Thug"], "producers": ["Digital Nas", "London on da Track"], "notes": "OG Filename: River MIX TEST.09_09\nTrack seen on a tracklist from the Italy sessions and track 16 on tracklist posted by Ty <PERSON> $ign. Unknown if there's any other mix tests after this.", "length": "214.39", "fileDate": 17021664, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3f5980c4090a36347030a8cbc6ce6fbf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f5980c4090a36347030a8cbc6ce6fbf\", \"key\": \"RIVER\", \"title\": \"RIVER [V14]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. Digital Nas & London on da Track)\", \"description\": \"OG Filename: River MIX TEST.09_09\\nTrack seen on a tracklist from the Italy sessions and track 16 on tracklist posted by Ty Dolla $ign. Unknown if there's any other mix tests after this.\", \"date\": 17021664, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9086bba43c7fe870455854095051c55d\", \"url\": \"https://api.pillowcase.su/api/download/9086bba43c7fe870455854095051c55d\", \"size\": \"8.44 MB\", \"duration\": 214.39}", "aliases": [], "size": "8.44 MB"}, {"id": "river-88", "name": "RIVER [V15]", "artists": ["<PERSON> Thug"], "producers": ["Digital Nas", "London on da Track"], "notes": "Later version of \"River\" with different mixing and effects on some <PERSON> vocals, and less Ty vocals during the outro. Originally leaked due to a groupbuy on July 7th, 2024, before a more accurate stem bounce leaked later. Made on or before Dec 15, 2023.", "length": "215.23", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/0b0e53eb1b416df4904dd02c4a202da5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b0e53eb1b416df4904dd02c4a202da5\", \"key\": \"RIVER\", \"title\": \"RIVER [V15]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. Digital Nas & London on da Track)\", \"description\": \"Later version of \\\"River\\\" with different mixing and effects on some <PERSON> vocals, and less Ty vocals during the outro. Originally leaked due to a groupbuy on July 7th, 2024, before a more accurate stem bounce leaked later. Made on or before Dec 15, 2023.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5745a3c43bbcbb5cb081b31ef30080cd\", \"url\": \"https://api.pillowcase.su/api/download/5745a3c43bbcbb5cb081b31ef30080cd\", \"size\": \"8.45 MB\", \"duration\": 215.23}", "aliases": [], "size": "8.45 MB"}, {"id": "river-89", "name": "✨ RIVER [V16]", "artists": ["<PERSON> Thug"], "producers": ["Digital Nas", "London on da Track"], "notes": "OG Filename: River �\nVersion with alternate production from London on da Track. Original snippet leaked March 9th 2024. This version is a stem bounce.", "length": "226.09", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/321a5b6de50ca4a7125f9e6fe86cab40", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/321a5b6de50ca4a7125f9e6fe86cab40\", \"key\": \"RIVER\", \"title\": \"\\u2728 RIVER [V16]\", \"artists\": \"(feat. <PERSON>hug) (prod. Digital Nas & London on da Track)\", \"description\": \"OG Filename: River \\ufffd\\nVersion with alternate production from London on da Track. Original snippet leaked March 9th 2024. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"27bef95023d69da8c9b1c657fd7f66a7\", \"url\": \"https://api.pillowcase.su/api/download/27bef95023d69da8c9b1c657fd7f66a7\", \"size\": \"8.63 MB\", \"duration\": 226.09}", "aliases": [], "size": "8.63 MB"}, {"id": "river-90", "name": "RIVER [V17]", "artists": ["<PERSON> Thug"], "producers": ["Digital Nas", "London on da Track"], "notes": "OG Filename: RIVER V1A 132BPM\nLondon on da Track made three other versions of \"River\". Instrumental with additional production.", "length": "214.6", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4c711c4dbc1496477d091aac884eb45d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4c711c4dbc1496477d091aac884eb45d\", \"key\": \"RIVER\", \"title\": \"RIVER [V17]\", \"artists\": \"(feat. <PERSON>hug) (prod. Digital Nas & London on da Track)\", \"description\": \"OG Filename: RIVER V1A 132BPM\\nLondon on da Track made three other versions of \\\"River\\\". Instrumental with additional production.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"59943d2a45aea1310dfd9f9f07b216ff\", \"url\": \"https://api.pillowcase.su/api/download/59943d2a45aea1310dfd9f9f07b216ff\", \"size\": \"8.44 MB\", \"duration\": 214.6}", "aliases": [], "size": "8.44 MB"}, {"id": "so-good", "name": "SO GOOD [V6]", "artists": [], "producers": [], "notes": "Partial lossless iTunes file leaked July 31st, 2024.", "length": "95.4", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/b77df5d52287df30e66dbed0b6688ba6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b77df5d52287df30e66dbed0b6688ba6\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V6]\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"Partial lossless iTunes file leaked July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eb6c7bfc5d49357130a4529adbf5effe\", \"url\": \"https://api.pillowcase.su/api/download/eb6c7bfc5d49357130a4529adbf5effe\", \"size\": \"6.54 MB\", \"duration\": 95.4}", "aliases": ["GOOD (DON'T DIE)"], "size": "6.54 MB"}, {"id": "so-good-92", "name": "SO GOOD [V7]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: 12-130\n<PERSON> for \"So Good\". Originally leaked in full February 24th, 2024. Made on or before Dec 14, 2023", "length": "29.54", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/becf280a7e5e9d0a9013ddfa058e7a79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/becf280a7e5e9d0a9013ddfa058e7a79\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V7]\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: 12-130\\nLoop for \\\"So Good\\\". Originally leaked in full February 24th, 2024. Made on or before Dec 14, 2023\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"64f300f5668ea9e2be8b278ea76d5b19\", \"url\": \"https://api.pillowcase.su/api/download/64f300f5668ea9e2be8b278ea76d5b19\", \"size\": \"5.48 MB\", \"duration\": 29.54}", "aliases": ["GOOD (DON'T DIE)"], "size": "5.48 MB"}, {"id": "so-good-93", "name": "SO GOOD [V8]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: 130 YE LODT\nLoop for \"So Good\" with production from London on da Track. This version is a stem bounce.", "length": "29.54", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/06fadea57f7d13936e98be9f59d07252", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/06fadea57f7d13936e98be9f59d07252\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V8]\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: 130 YE LODT\\nLoop for \\\"So Good\\\" with production from London on da Track. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"142652d890f45a5c71814e5685c9b0d3\", \"url\": \"https://api.pillowcase.su/api/download/142652d890f45a5c71814e5685c9b0d3\", \"size\": \"5.48 MB\", \"duration\": 29.54}", "aliases": ["GOOD (DON'T DIE)"], "size": "5.48 MB"}, {"id": "so-good-94", "name": "SO GOOD [V9]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: YELODT 130 V1B BETTER CHORDS\nLoop for \"So Good\" with production from London on da Track. This version is a stem bounce.", "length": "44.31", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8feb3d5d22abca2952f0d07e7a5dd64c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8feb3d5d22abca2952f0d07e7a5dd64c\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V9]\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: YELODT 130 V1B BETTER CHORDS\\nLoop for \\\"So Good\\\" with production from London on da Track. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"525249b637e90f633f13fdc918529fb7\", \"url\": \"https://api.pillowcase.su/api/download/525249b637e90f633f13fdc918529fb7\", \"size\": \"5.72 MB\", \"duration\": 44.31}", "aliases": ["GOOD (DON'T DIE)"], "size": "5.72 MB"}, {"id": "so-good-95", "name": "SO GOOD [V10]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: INTRO IDEA 1 130 V1A\nLoop for \"So Good\" with production from London on da Track. This version is a stem bounce.", "length": "35.08", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/91d113ee63be4e1a135793f2d7824e46", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91d113ee63be4e1a135793f2d7824e46\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V10]\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: INTRO IDEA 1 130 V1A\\nLoop for \\\"So Good\\\" with production from London on da Track. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4ac578c7f44e4a5f7687d22da9de39bf\", \"url\": \"https://api.pillowcase.su/api/download/4ac578c7f44e4a5f7687d22da9de39bf\", \"size\": \"5.57 MB\", \"duration\": 35.08}", "aliases": ["GOOD (DON'T DIE)"], "size": "5.57 MB"}, {"id": "so-good-96", "name": "SO GOOD [V11]", "artists": [], "producers": ["London on da Track"], "notes": "OG Filename: So Good LONDONADDS V1B\nFull version of \"So Good\" with alternate production from London on da Track.", "length": "147.71", "fileDate": 17087328, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d001ce787a12cc1b4bc6962f1bdc06cb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d001ce787a12cc1b4bc6962f1bdc06cb\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V11]\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: So Good LONDONADDS V1B\\nFull version of \\\"So Good\\\" with alternate production from London on da Track.\", \"date\": 17087328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f060847494aa57ae6484fbad341a5557\", \"url\": \"https://api.pillowcase.su/api/download/f060847494aa57ae6484fbad341a5557\", \"size\": \"7.37 MB\", \"duration\": 147.71}", "aliases": ["GOOD (DON'T DIE)"], "size": "7.37 MB"}, {"id": "slidin", "name": "SLIDIN [V7]", "artists": [], "producers": ["<PERSON> again..", "AyoAA", "Wheezy"], "notes": "OG Filename: <PERSON><PERSON>in - Track 12\nLater version of \"Slide\" with different mixing. Has the extended outro shown on the DJ <PERSON><PERSON><PERSON> premiere and may be that exact version, but unknown. Originally leaked after a groupbuy July 7th, 2024, with the OG file later leaking. Made on or before Dec 14, 2023.", "length": "315.61", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/525ab2353c78863fe9a612c231206989", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/525ab2353c78863fe9a612c231206989\", \"key\": \"SLIDIN\", \"title\": \"SLIDIN [V7]\", \"artists\": \"(prod. <PERSON> again.., A<PERSON><PERSON> & Wheezy)\", \"aliases\": [\"Slide In\", \"SLIDE\"], \"description\": \"OG Filename: Slidin - Track 12\\nLater version of \\\"Slide\\\" with different mixing. Has the extended outro shown on the DJ Pharris premiere and may be that exact version, but unknown. Originally leaked after a groupbuy July 7th, 2024, with the OG file later leaking. Made on or before Dec 14, 2023.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2582d338de3d49beca4a8b412ef313f2\", \"url\": \"https://api.pillowcase.su/api/download/2582d338de3d49beca4a8b412ef313f2\", \"size\": \"10.1 MB\", \"duration\": 315.61}", "aliases": ["Slide In", "SLIDE"], "size": "10.1 MB"}, {"id": "slidin-98", "name": "✨ SLIDIN [V8]", "artists": [], "producers": ["<PERSON> again..", "London on da Track", "AyoAA", "Wheezy"], "notes": "Version with London on da Track production added. Original snippet leaked March 9th, 2024. This version is a stem bounce.", "length": "315.62", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a630ebbe5fa496f74b3c7d8d70b83b91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a630ebbe5fa496f74b3c7d8d70b83b91\", \"key\": \"SLIDIN\", \"title\": \"\\u2728 SLIDIN [V8]\", \"artists\": \"(prod. <PERSON> again.., London on da Track, AyoAA & Wheezy)\", \"aliases\": [\"Slide In\", \"SLIDE\"], \"description\": \"Version with London on da Track production added. Original snippet leaked March 9th, 2024. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f52d15bdaaf3e108123783595057f1e4\", \"url\": \"https://api.pillowcase.su/api/download/f52d15bdaaf3e108123783595057f1e4\", \"size\": \"10.1 MB\", \"duration\": 315.62}", "aliases": ["Slide In", "SLIDE"], "size": "10.1 MB"}, {"id": "slide", "name": "SLIDE [V9]", "artists": [], "producers": ["<PERSON> again..", "AyoAA", "Wheezy"], "notes": "Version played during <PERSON> <PERSON><PERSON><PERSON>' album premiere. Has a longer and new outro. <PERSON> added radio FX throughout the song.", "length": "300.04", "fileDate": 17025120, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c1087642c66eb94ed7030f1384cf5029", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1087642c66eb94ed7030f1384cf5029\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V9]\", \"artists\": \"(prod. <PERSON> again.., <PERSON><PERSON><PERSON> & Wheezy)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"Version played during <PERSON>' album premiere. Has a longer and new outro. Has added radio FX throughout the song.\", \"date\": 17025120, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d7d3ce56b54e14521d80015203040564\", \"url\": \"https://api.pillowcase.su/api/download/d7d3ce56b54e14521d80015203040564\", \"size\": \"9.81 MB\", \"duration\": 300.04}", "aliases": ["Slide In", "SLIDIN"], "size": "9.81 MB"}, {"id": "king", "name": "KING [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON>\nDrumless bounce.", "length": "169.34", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/b5bed51d320e182f0ea33c7f0d95dc4a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b5bed51d320e182f0ea33c7f0d95dc4a\", \"key\": \"KING\", \"title\": \"KING [V3]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"OG Filename: King\\nDrumless bounce.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cf22cd16478bf5cce48b505252afd87b\", \"url\": \"https://api.pillowcase.su/api/download/cf22cd16478bf5cce48b505252afd87b\", \"size\": \"7.72 MB\", \"duration\": 169.34}", "aliases": ["STILL THE KING"], "size": "7.72 MB"}, {"id": "still-the-king", "name": "STILL THE KING [V4]", "artists": [], "producers": ["Wheezy", "<PERSON>"], "notes": "OG Filename: STILL THE KING WHEEZY x ye\nDemo song that was played shortly in the Vultures City afterparty before <PERSON><PERSON><PERSON> joined the livestream. Better quality snippet was posted by <PERSON> who also produced the song with <PERSON><PERSON><PERSON>, and implied the real name of the song is \"<PERSON>\".", "length": "194.56", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f305e3ec5679bbffd0eb118673916f5d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f305e3ec5679bbffd0eb118673916f5d\", \"key\": \"STILL THE KING\", \"title\": \"STILL THE KING [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"KING\"], \"description\": \"OG Filename: STILL THE KING WHEEZY x ye\\nDemo song that was played shortly in the Vultures City afterparty before <PERSON><PERSON><PERSON> joined the livestream. Better quality snippet was posted by <PERSON> who also produced the song with <PERSON><PERSON><PERSON>, and implied the real name of the song is \\\"King\\\".\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"61f26581eb58f2d801e691e8b8bff474\", \"url\": \"https://api.pillowcase.su/api/download/61f26581eb58f2d801e691e8b8bff474\", \"size\": \"8.12 MB\", \"duration\": 194.56}", "aliases": ["KING"], "size": "8.12 MB"}, {"id": "king-102", "name": "KING [V5-V?]", "artists": [], "producers": ["Digital Nas"], "notes": "Version with Digital Nas production. In the video DN wrote \"Demo 1\", so there are likely more versions. Unknown when made.", "length": "3.66", "fileDate": 17095104, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1de0d41ff317095494195bdc130599f0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1de0d41ff317095494195bdc130599f0\", \"key\": \"KING\", \"title\": \"KING [V5-V?]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"Version with Digital Nas production. In the video DN wrote \\\"Demo 1\\\", so there are likely more versions. Unknown when made.\", \"date\": 17095104, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"04c92fe96c58807137ac08f209e28de7\", \"url\": \"https://api.pillowcase.su/api/download/04c92fe96c58807137ac08f209e28de7\", \"size\": \"5.04 MB\", \"duration\": 3.66}", "aliases": ["STILL THE KING"], "size": "5.04 MB"}, {"id": "king-103", "name": "✨ KING [V6]", "artists": [], "producers": ["Wheezy", "<PERSON>", "London on da Track"], "notes": "Version with London on da Track production. One of the background sounds is a sample of <PERSON> from \"The Minions\" saying \"KING BOB!\". Original snippet leaked February 13th, 2024, with the beat leaking July 31st, 2024.", "length": "150.59", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ccea8812fb39e5c25a528eceb1e4332a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ccea8812fb39e5c25a528eceb1e4332a\", \"key\": \"KING\", \"title\": \"\\u2728 KING [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON> on da Track)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"Version with London on da Track production. One of the background sounds is a sample of <PERSON> from \\\"The Minions\\\" saying \\\"KING BOB!\\\". Original snippet leaked February 13th, 2024, with the beat leaking July 31st, 2024.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ae02dbc2215997878ea6dae003d4b339\", \"url\": \"https://api.pillowcase.su/api/download/ae02dbc2215997878ea6dae003d4b339\", \"size\": \"7.42 MB\", \"duration\": 150.59}", "aliases": ["STILL THE KING"], "size": "7.42 MB"}, {"id": "king-104", "name": "KING [V7]", "artists": [], "producers": ["???"], "notes": "Version with drums and alternate production by a currently unknown producer. Accurate bounce provided by erickhatesbmc.", "length": "169.41", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4398714326a2218941ef4347e8c2a971", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4398714326a2218941ef4347e8c2a971\", \"key\": \"KING\", \"title\": \"KING [V7]\", \"artists\": \"(prod. ???)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"Version with drums and alternate production by a currently unknown producer. Accurate bounce provided by erickhatesbmc.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0fb29cbc671de7a73f9c25cecd4fc3f9\", \"url\": \"https://api.pillowcase.su/api/download/0fb29cbc671de7a73f9c25cecd4fc3f9\", \"size\": \"7.72 MB\", \"duration\": 169.41}", "aliases": ["STILL THE KING"], "size": "7.72 MB"}, {"id": "king-105", "name": "KING [V10]", "artists": [], "producers": [], "notes": "Leaked August 3rd 2024 by <PERSON> Money himself as a response to the release of VULTURES 2. Has some Ye vocals.", "length": "194.84", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/872bd58cacd11f4d8cd817bd5fe2a63f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/872bd58cacd11f4d8cd817bd5fe2a63f\", \"key\": \"KING\", \"title\": \"KING [V10]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"Leaked August 3rd 2024 by <PERSON> Money himself as a response to the release of VULTURES 2. Has some Ye vocals.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"50d8daf55da46b02528a04fd338c40f8\", \"url\": \"https://api.pillowcase.su/api/download/50d8daf55da46b02528a04fd338c40f8\", \"size\": \"8.13 MB\", \"duration\": 194.84}", "aliases": ["STILL THE KING"], "size": "8.13 MB"}, {"id": "papa-wanna-see", "name": "PAPA WANNA SEE [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: PAPA WANNA SEE V2 - 2023-11-03\nVersion with different Ye freestyle vocals. Snippet leaked October 19th, 2024. Some parts of this would later be used to make the hook/chorus of the release version.", "length": "137.22", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/fe6547d8610b5dd46c4fdd6248a77335", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe6547d8610b5dd46c4fdd6248a77335\", \"key\": \"PAPA WANNA SEE\", \"title\": \"PAPA WANNA SEE [V2]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"TAKE OFF YOUR DRESS\"], \"description\": \"OG Filename: PAPA WANNA SEE V2 - 2023-11-03\\nVersion with different Ye freestyle vocals. Snippet leaked October 19th, 2024. Some parts of this would later be used to make the hook/chorus of the release version.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"498ff1e1a192bb458a08c45adc5cf79c\", \"url\": \"https://api.pillowcase.su/api/download/498ff1e1a192bb458a08c45adc5cf79c\", \"size\": \"7.21 MB\", \"duration\": 137.22}", "aliases": ["TAKE OFF YOUR DRESS"], "size": "7.21 MB"}, {"id": "take-off-your-dress", "name": "TAKE OFF YOUR DRESS [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: TAKE OFF YOUR DRESS 12.5.23 YE_2\nFilename shown by SH<PERSON><PERSON><PERSON>. Nobody gave SHDØ<PERSON> stems for \"TOYD\", so he had to stem split the original version using AI. Snippet played from an interview.", "length": "8.21", "fileDate": 17250624, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/37496c56a886c22c3e0446c960d1771a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37496c56a886c22c3e0446c960d1771a\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"TAKE OFF YOUR DRESS [V3]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"OG Filename: TAKE OFF YOUR DRESS 12.5.23 YE_2\\nFilename shown by SHD\\u00d8W. Nobody gave SHD\\u00d8W stems for \\\"TOYD\\\", so he had to stem split the original version using AI. Snippet played from an interview.\", \"date\": 17250624, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d3c3565c46475cf1dcd5c0e79dc65f8a\", \"url\": \"https://api.pillowcase.su/api/download/d3c3565c46475cf1dcd5c0e79dc65f8a\", \"size\": \"5.14 MB\", \"duration\": 8.21}", "aliases": ["PAPA WANNA SEE"], "size": "5.14 MB"}, {"id": "take-off-your-dress-108", "name": "TAKE OFF YOUR DRESS [V9]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: TAKE OFF YOUR DRESS - sample only\nContains only the sample chop with no additional production. Played in full during the Vultures City livestream on Dec 16th 2023.", "length": "162.93", "fileDate": 17289504, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/56195d8634f5542120c2ae5b88aa466c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/56195d8634f5542120c2ae5b88aa466c\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"TAKE OFF YOUR DRESS [V9]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"OG Filename: TAKE OFF YOUR DRESS - sample only\\nContains only the sample chop with no additional production. Played in full during the Vultures City livestream on Dec 16th 2023.\", \"date\": 17289504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"411862c70735e81dde5663cc13093d28\", \"url\": \"https://api.pillowcase.su/api/download/411862c70735e81dde5663cc13093d28\", \"size\": \"7.62 MB\", \"duration\": 162.93}", "aliases": ["PAPA WANNA SEE"], "size": "7.62 MB"}, {"id": "take-off-your-dress-109", "name": "TAKE OFF YOUR DRESS [V10]", "artists": [], "producers": ["<PERSON>", "SHDØW"], "notes": "Played by <PERSON><PERSON><PERSON><PERSON> March 29th, 2024. Contains new production and finished vocals, solo <PERSON>.", "length": "", "fileDate": 17169408, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/9d6f455f75e7753b743e05bac3bbbb7d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d6f455f75e7753b743e05bac3bbbb7d\", \"key\": \"TAKE OFF YOUR DRESS\", \"title\": \"TAKE OFF YOUR DRESS [V10]\", \"artists\": \"(prod. Scott Bridgeway & SHD\\u00d8W)\", \"aliases\": [\"PAPA WANNA SEE\"], \"description\": \"Played by SHD\\u00d8W March 29th, 2024. Contains new production and finished vocals, solo Ye.\", \"date\": 17169408, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["PAPA WANNA SEE"], "size": ""}, {"id": "talking", "name": "TALKING [V7]", "artists": ["North West", "<PERSON>"], "producers": ["<PERSON>", "No I.D."], "notes": "Version played at the VULTURES Miami rave. Updated from the WAR version with Jersey drums, as well as North West rapping.", "length": "82.89", "fileDate": 17023392, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1eda5e0d2c83073a7b0b6fab69b4c381", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1eda5e0d2c83073a7b0b6fab69b4c381\", \"key\": \"TALKING\", \"title\": \"TALKING [V7]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING / ONCE AGAIN\"], \"description\": \"Version played at the VULTURES Miami rave. Updated from the WAR version with Jersey drums, as well as North West rapping.\", \"date\": 17023392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"866392e3c60b0594402f732072857e3e\", \"url\": \"https://api.pillowcase.su/api/download/866392e3c60b0594402f732072857e3e\", \"size\": \"6.34 MB\", \"duration\": 82.89}", "aliases": ["TALKING / ONCE AGAIN"], "size": "6.34 MB"}, {"id": "talking-once-again", "name": "TALKING / ONCE AGAIN [V8]", "artists": ["North West", "<PERSON>"], "producers": ["<PERSON>", "No I.D."], "notes": "Version that was accidentally uploaded as \"Unlock\" on the December iTunes copy of VULTURES.", "length": "145.54", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4bba39ee5ca6e216b032872193114c07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bba39ee5ca6e216b032872193114c07\", \"key\": \"TALKING / ONCE AGAIN\", \"title\": \"TALKING / ONCE AGAIN [V8]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING\"], \"description\": \"Version that was accidentally uploaded as \\\"Unlock\\\" on the December iTunes copy of VULTURES.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"74d2171710ec6c704e2ff373cbe9a498\", \"url\": \"https://api.pillowcase.su/api/download/74d2171710ec6c704e2ff373cbe9a498\", \"size\": \"7.34 MB\", \"duration\": 145.54}", "aliases": ["TALKING"], "size": "7.34 MB"}, {"id": "talking-once-again-112", "name": "TALKING / ONCE AGAIN [V9]", "artists": ["North West", "<PERSON>"], "producers": ["<PERSON>", "No I.D."], "notes": "OG Filename: untitled - Track 8\nVersion of the track made on December 14th. <PERSON>'s part on the outro goes on for longer than on released.", "length": "273.53", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/cda95725e13cda91161d6b9e455da25c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cda95725e13cda91161d6b9e455da25c\", \"key\": \"TALKING / ONCE AGAIN\", \"title\": \"TALKING / ONCE AGAIN [V9]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING\"], \"description\": \"OG Filename: untitled - Track 8\\nVersion of the track made on December 14th. Ye's part on the outro goes on for longer than on released.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"476f5d0ed4a5d25aa1f35620b61e7e41\", \"url\": \"https://api.pillowcase.su/api/download/476f5d0ed4a5d25aa1f35620b61e7e41\", \"size\": \"9.39 MB\", \"duration\": 273.53}", "aliases": ["TALKING"], "size": "9.39 MB"}, {"id": "talking-once-again-113", "name": "TALKING / ONCE AGAIN [V10]", "artists": ["North West", "<PERSON>"], "producers": ["<PERSON>", "No I.D."], "notes": "Has additional production at 3:28 that is unmixed. This version is a stem bounce.", "length": "273.53", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1e198f3eb1a4d051840ee121301fdd9d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e198f3eb1a4d051840ee121301fdd9d\", \"key\": \"TALKING / ONCE AGAIN\", \"title\": \"TALKING / ONCE AGAIN [V10]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING\"], \"description\": \"Has additional production at 3:28 that is unmixed. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e2174dbc88798d1593fcf7da4111002b\", \"url\": \"https://api.pillowcase.su/api/download/e2174dbc88798d1593fcf7da4111002b\", \"size\": \"9.39 MB\", \"duration\": 273.53}", "aliases": ["TALKING"], "size": "9.39 MB"}, {"id": "talking-once-again-114", "name": "TALKING / ONCE AGAIN [V11]", "artists": ["North West", "<PERSON>"], "producers": ["<PERSON>", "No I.D."], "notes": "Short snippet played in the Las Vegas Rave. Has new <PERSON> intro vocals. Presumably stitched together as well.", "length": "128.94", "fileDate": 17025984, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3a9929b9ff90c752c1ff5bfbcff04277", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a9929b9ff90c752c1ff5bfbcff04277\", \"key\": \"TALKING / ONCE AGAIN\", \"title\": \"TALKING / ONCE AGAIN [V11]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING\"], \"description\": \"Short snippet played in the Las Vegas Rave. Has new Ye intro vocals. Presumably stitched together as well.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b40a77a89fb3acfc384e71fb1db86379\", \"url\": \"https://api.pillowcase.su/api/download/b40a77a89fb3acfc384e71fb1db86379\", \"size\": \"7.07 MB\", \"duration\": 128.94}", "aliases": ["TALKING"], "size": "7.07 MB"}, {"id": "timbo-freestyle", "name": "TIMBO FREESTYLE [V3]", "artists": [], "producers": ["Timbaland", "<PERSON><PERSON>", "SHDØW", "VEYIS", "Vinnyforgood"], "notes": "OG Filename: TIMBO FREESTYLE 12.2.23_3\nVersion from December 2nd, 2023. Played on December 18th, 2023. Differences from later versions are unknown, but it's likely it's just an alt mix.", "length": "", "fileDate": 17028576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/627e6f09a5d2f0a6d3baade06ec70513", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/627e6f09a5d2f0a6d3baade06ec70513\", \"key\": \"TIMBO FREESTYLE\", \"title\": \"TIMBO FREESTYLE [V3]\", \"artists\": \"(prod. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\\u00d8W, VEYIS & Vinnyforgood)\", \"aliases\": [\"KEYS TO MY LIFE\"], \"description\": \"OG Filename: TIMBO FREESTYLE 12.2.23_3\\nVersion from December 2nd, 2023. Played on December 18th, 2023. Differences from later versions are unknown, but it's likely it's just an alt mix.\", \"date\": 17028576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["KEYS TO MY LIFE"], "size": ""}, {"id": "timbo-freestyle-116", "name": "TIMBO FREESTYLE [V4]", "artists": [], "producers": ["Timbaland", "<PERSON><PERSON>", "SHDØW", "VEYIS", "Vinnyforgood"], "notes": "OG Filename: <PERSON><PERSON> - Track 12\nTrack 8 on tracklist posted by Ty Dolla $ign. Previewed in the Vultures City livestream. Original snippet and OG filename leaked December 18th, 2023.", "length": "171.89", "fileDate": 17088192, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d64d66d12e1fd6fbc2d8b0b9f052dcae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d64d66d12e1fd6fbc2d8b0b9f052dcae\", \"key\": \"TIMBO FREESTYLE\", \"title\": \"TIMBO FREESTYLE [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\\u00d8W, VEYIS & Vinnyforgood)\", \"aliases\": [\"KEYS TO MY LIFE\"], \"description\": \"OG Filename: Timbo - Track 12\\nTrack 8 on tracklist posted by Ty Dolla $ign. Previewed in the Vultures City livestream. Original snippet and OG filename leaked December 18th, 2023.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d2f4248cc52db5503816dc34784b5a71\", \"url\": \"https://api.pillowcase.su/api/download/d2f4248cc52db5503816dc34784b5a71\", \"size\": \"7.76 MB\", \"duration\": 171.89}", "aliases": ["KEYS TO MY LIFE"], "size": "7.76 MB"}, {"id": "timbo-freestyle-117", "name": "✨ TIMBO FREESTYLE [V5]", "artists": [], "producers": ["Timbaland", "London on da Track", "<PERSON><PERSON>", "SHDØW", "VEYIS", "Vinnyforgood"], "notes": "OG Filename: Timbo LONDON ADDS\nVersion with added production from London on da Track. No India Love vocals.", "length": "171.89", "fileDate": 17087328, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/454d6e9f4a1db76547a684e8339396a8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/454d6e9f4a1db76547a684e8339396a8\", \"key\": \"TIMBO FREESTYLE\", \"title\": \"\\u2728 TIMBO FREESTYLE [V5]\", \"artists\": \"(prod. <PERSON>bal<PERSON>, London on da Track, Hubi, SHD\\u00d8W, VEYIS & Vinnyforgood)\", \"aliases\": [\"KEYS TO MY LIFE\"], \"description\": \"OG Filename: Timbo LONDON ADDS\\nVersion with added production from London on da Track. No India Love vocals.\", \"date\": 17087328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bca86a2db0c2521f3aaee686e0678547\", \"url\": \"https://api.pillowcase.su/api/download/bca86a2db0c2521f3aaee686e0678547\", \"size\": \"7.76 MB\", \"duration\": 171.89}", "aliases": ["KEYS TO MY LIFE"], "size": "7.76 MB"}, {"id": "time-moving-slow", "name": "TIME MOVING SLOW [V16]", "artists": [], "producers": [], "notes": "According to <PERSON><PERSON><PERSON>, a reference track exists with <PERSON><PERSON><PERSON> in which they were laying out ideas. A mumble rendition of the Time Moving Slow hook was then finished by <PERSON>.", "length": "14.86", "fileDate": 17350848, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7fd264a8a072f5481cb8b2bcbd251692", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7fd264a8a072f5481cb8b2bcbd251692\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V16]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON> & <PERSON>ban)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"According to <PERSON><PERSON><PERSON>, a reference track exists with <PERSON><PERSON><PERSON> in which they were laying out ideas. A mumble rendition of the Time Moving Slow hook was then finished by <PERSON>.\", \"date\": 17350848, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a49bff877f4d08059f91006bc83e6d23\", \"url\": \"https://api.pillowcase.su/api/download/a49bff877f4d08059f91006bc83e6d23\", \"size\": \"5.25 MB\", \"duration\": 14.86}", "aliases": ["TIME MOVES SLOW"], "size": "5.25 MB"}, {"id": "time-moving-slow-119", "name": "✨ TIME MOVING SLOW [V17]", "artists": [], "producers": ["AyoAA"], "notes": "Partial iTunes file leaked July 31st, 2024. Features different production that has more production compared to earlier versions.", "length": "124", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7d465f1909f69b8af09ce270c734602c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7d465f1909f69b8af09ce270c734602c\", \"key\": \"TIME MOVING SLOW\", \"title\": \"\\u2728 TIME MOVING SLOW [V17]\", \"artists\": \"(prod. AyoAA)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Partial iTunes file leaked July 31st, 2024. Features different production that has more production compared to earlier versions.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8bb5c77b572eae0e3606fb1762fc5a2a\", \"url\": \"https://api.pillowcase.su/api/download/8bb5c77b572eae0e3606fb1762fc5a2a\", \"size\": \"6.99 MB\", \"duration\": 124}", "aliases": ["TIME MOVES SLOW"], "size": "6.99 MB"}, {"id": "time-moving-slow-120", "name": "TIME MOVING SLOW [V18]", "artists": [], "producers": ["AyoAA"], "notes": "Track seen on a tracklist from the Italy sessions and track 4 on tracklist posted by <PERSON> $ign. Played in full on Power 92. Has radio sound effects throughout the premiere, which aren't on the song.", "length": "173.04", "fileDate": 17025120, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/9a5bfbb179fc68f82d73927a3cd58a8e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9a5bfbb179fc68f82d73927a3cd58a8e\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V18]\", \"artists\": \"(prod. AyoAA)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Track seen on a tracklist from the Italy sessions and track 4 on tracklist posted by Ty Dolla $ign. Played in full on Power 92. Has radio sound effects throughout the premiere, which aren't on the song.\", \"date\": 17025120, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"707be444d85b0f3bcdf98ec07092455e\", \"url\": \"https://api.pillowcase.su/api/download/707be444d85b0f3bcdf98ec07092455e\", \"size\": \"7.78 MB\", \"duration\": 173.04}", "aliases": ["TIME MOVES SLOW"], "size": "7.78 MB"}, {"id": "vultures", "name": "VULTURES [V3]", "artists": [], "producers": [], "notes": "Initial mumble freestyle for \"Vultures\". Snippet played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "5.42", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/2ae0e5367a374e859735056e577c949e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ae0e5367a374e859735056e577c949e\", \"key\": \"VULTURES\", \"title\": \"VULTURES [V3]\", \"description\": \"Initial mumble freestyle for \\\"Vultures\\\". Snippet played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"355eb541584528394c8ae25795010402\", \"url\": \"https://api.pillowcase.su/api/download/355eb541584528394c8ae25795010402\", \"size\": \"5.1 MB\", \"duration\": 5.42}", "aliases": [], "size": "5.1 MB"}, {"id": "vultures-122", "name": "VULTURES [V4]", "artists": [], "producers": [], "notes": "Previewed in a teaser for the album. It has a slower tempo and a much different arrangement. <PERSON> <PERSON> mumbling over the beat with heavy autotune. Snippet posted November 19th, 2024.", "length": "12.36", "fileDate": 17319744, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d9c5a5c854a65842d09df624a1c53a43", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9c5a5c854a65842d09df624a1c53a43\", \"key\": \"VULTURES\", \"title\": \"VULTURES [V4]\", \"description\": \"Previewed in a teaser for the album. It has a slower tempo and a much different arrangement. Has <PERSON> mumbling over the beat with heavy autotune. Snippet posted November 19th, 2024.\", \"date\": 17319744, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"94e451a15166344f472abbfe2a69b10b\", \"url\": \"https://api.pillowcase.su/api/download/94e451a15166344f472abbfe2a69b10b\", \"size\": \"5.21 MB\", \"duration\": 12.36}", "aliases": [], "size": "5.21 MB"}, {"id": "vultures-123", "name": "VULTURES [V7]", "artists": ["Bump J"], "producers": [], "notes": "Hi hats that were removed from the final version of the song can be heard in the bleed during <PERSON><PERSON> J's verse. Unknown if this Bump J vocal take is different from the original one.", "length": "13.21", "fileDate": 17001792, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f9c5cf0f41e20bbf7b71e350453d7df7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f9c5cf0f41e20bbf7b71e350453d7df7\", \"key\": \"VULTURES\", \"title\": \"VULTURES [V7]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON>)\", \"description\": \"Hi hats that were removed from the final version of the song can be heard in the bleed during <PERSON><PERSON> <PERSON>'s verse. Unknown if this Bump J vocal take is different from the original one.\", \"date\": 17001792, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"b5811e48efac998e1b7e9d75878c9ad4\", \"url\": \"https://api.pillowcase.su/api/download/b5811e48efac998e1b7e9d75878c9ad4\", \"size\": \"5.22 MB\", \"duration\": 13.21}", "aliases": [], "size": "5.22 MB"}, {"id": "worship", "name": "WORSHIP [V6]", "artists": [], "producers": ["Trybishop 88-<PERSON>", "<PERSON>"], "notes": "Song seen on various tracklists post-Italy. According to <PERSON><PERSON> the song has not been worked on at all during the VULTURES 2 sessions, meaning it is scrapped. Snippet previewed by SHDØW March 29th, 2024.", "length": "", "fileDate": 17169408, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/46d77230bd2d40b52fcbc30a0277423a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/46d77230bd2d40b52fcbc30a0277423a\", \"key\": \"WORSHIP\", \"title\": \"WORSHIP [V6]\", \"artists\": \"(ref. <PERSON>l\\u00f6e) (prod. <PERSON><PERSON><PERSON> 88-<PERSON> & <PERSON>)\", \"description\": \"Song seen on various tracklists post-Italy. According to <PERSON><PERSON> the song has not been worked on at all during the VULTURES 2 sessions, meaning it is scrapped. Snippet previewed by SHD\\u00d8W March 29th, 2024.\", \"date\": 17169408, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "Unknown potential VULTURES era song. Snippet recorded November 15th 2023.", "length": "30.62", "fileDate": 17000064, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/00319b5cbd6e568e5e55db21eb462140", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00319b5cbd6e568e5e55db21eb462140\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown potential VULTURES era song. Snippet recorded November 15th 2023.\", \"date\": 17000064, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1ab39ab321dc8d6b81f5d2f940cc1ad4\", \"url\": \"https://api.pillowcase.su/api/download/1ab39ab321dc8d6b81f5d2f940cc1ad4\", \"size\": \"5.5 MB\", \"duration\": 30.62}", "aliases": [], "size": "5.5 MB"}, {"id": "back-to-me-126", "name": "BACK TO ME [V15]", "artists": ["<PERSON>", "Quavo"], "producers": ["Kanye West", "Ty Dolla $ign", "88-<PERSON>", "Wax Motif", "AyoAA", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "MIKE DEAN", "Digital Nas"], "notes": "Played at the Chicago listening party for Vultures Vol. 1. Has a different and shorter structure, <PERSON> and <PERSON>'s vocals are slightly off time and the Urk<PERSON> soundbite was removed.", "length": "297.27", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8e09d688cd764c382c069bab72b03fd2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8e09d688cd764c382c069bab72b03fd2\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V15]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>gn, 88-<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, MIKE <PERSON>AN & Digital Nas) \", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Played at the Chicago listening party for Vultures Vol. 1. Has a different and shorter structure, <PERSON> and <PERSON>'s vocals are slightly off time and the Urkel soundbite was removed.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2b5e6e01489e24eaaad63d8afb002903\", \"url\": \"https://api.pillowcase.su/api/download/2b5e6e01489e24eaaad63d8afb002903\", \"size\": \"9.77 MB\", \"duration\": 297.27}", "aliases": ["BACK 2 ME"], "size": "9.77 MB"}, {"id": "back-to-me-127", "name": "BACK TO ME [V34]", "artists": ["<PERSON>", "Quavo"], "producers": ["Kanye West", "Ty Dolla $ign", "88-<PERSON>", "Wax Motif", "AyoAA", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "MIKE DEAN", "Digital Nas"], "notes": "OG Filename: BACK TO ME Mix 19 VoX shift MT001-T<PERSON>CI MASTER-24BIT-48kHz\nPost-release mix, leaked after a groupbuy. Has better mixing.", "length": "295.47", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8d32b4fd188fea39c74689569a4d320d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8d32b4fd188fea39c74689569a4d320d\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V34]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>gn, 88-<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, MIKE DEAN & Digital Nas) \", \"aliases\": [\"BACK 2 ME\"], \"description\": \"OG Filename: BACK TO ME Mix 19 VoX shift MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release mix, leaked after a groupbuy. Has better mixing.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0d46643ae36ac1912a330c4450d6b72a\", \"url\": \"https://api.pillowcase.su/api/download/0d46643ae36ac1912a330c4450d6b72a\", \"size\": \"9.74 MB\", \"duration\": 295.47}", "aliases": ["BACK 2 ME"], "size": "9.74 MB"}, {"id": "beg-forgiveness-128", "name": "✨ BEG FORGIVENESS [V13]", "artists": ["<PERSON>"], "producers": ["Digital Nas", "???"], "notes": "OG Filename: B<PERSON> FORGIVENESS V1B WITH DRUMS\nVersion that has a ton of production not seen in release at all, also has <PERSON> doing vocals. This version is a stem bounce.", "length": "196.36", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ea93af859db873cf1a5d9e4192052f15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ea93af859db873cf1a5d9e4192052f15\", \"key\": \"BEG FORGIVENESS\", \"title\": \"\\u2728 BEG FORGIVENESS [V13]\", \"artists\": \"(feat. <PERSON>) (prod. Digital Nas & ???)\", \"description\": \"OG Filename: BEG FORGIVENESS V1B WITH DRUMS\\nVersion that has a ton of production not seen in release at all, also has <PERSON> doing vocals. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d9bf730ecd257adbab76f9fc6ce84225\", \"url\": \"https://api.pillowcase.su/api/download/d9bf730ecd257adbab76f9fc6ce84225\", \"size\": \"8.15 MB\", \"duration\": 196.36}", "aliases": [], "size": "8.15 MB"}, {"id": "beg-forgiveness-129", "name": "BEG FORGIVENESS [V14]", "artists": ["<PERSON>"], "producers": ["Digital Nas"], "notes": "Version played during <PERSON> <PERSON><PERSON><PERSON>' album premiere. Has vocal effects added to <PERSON> to sound like the sample used on the pre-Chris <PERSON> version. Also more developed production. Has <PERSON> $ign background vocals, but still no Ye vocals. Has added radio FX throughout the song.", "length": "305.03", "fileDate": 17025120, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6d303265dfb16dab985608f13d652289", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d303265dfb16dab985608f13d652289\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V14]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> Nas)\", \"description\": \"Version played during <PERSON><PERSON>' album premiere. Has vocal effects added to <PERSON> to sound like the sample used on the pre-<PERSON> version. Also more developed production. Has <PERSON> $ign background vocals, but still no Ye vocals. Has added radio FX throughout the song.\", \"date\": 17025120, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"65dfa60fe760660038a1407884bfc2ea\", \"url\": \"https://api.pillowcase.su/api/download/65dfa60fe760660038a1407884bfc2ea\", \"size\": \"9.89 MB\", \"duration\": 305.03}", "aliases": [], "size": "9.89 MB"}, {"id": "beg-forgiveness-130", "name": "BEG FORGIVENESS [V15]", "artists": ["<PERSON>"], "producers": ["Digital Nas"], "notes": "Fat Money reference track for \"Beg Forgiveness\". It's unknown if Fat Money is doing a reference for <PERSON> or <PERSON>, but most likely for Ye. Played March 9th 2024. Leaked August 3rd 2024 by <PERSON> Money himself as a response to the release of VULTURES 2.", "length": "196.61", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/007e3f0e3bd69fe6f08f90f07ac6f472", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/007e3f0e3bd69fe6f08f90f07ac6f472\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V15]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. Digital Nas)\", \"description\": \"Fat Money reference track for \\\"Beg Forgiveness\\\". It's unknown if <PERSON> Money is doing a reference for <PERSON> or <PERSON>, but most likely for <PERSON>. Played March 9th 2024. Leaked August 3rd 2024 by <PERSON> himself as a response to the release of VULTURES 2.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5f95cc9455236e7b4a2f96aed19c1960\", \"url\": \"https://api.pillowcase.su/api/download/5f95cc9455236e7b4a2f96aed19c1960\", \"size\": \"8.16 MB\", \"duration\": 196.61}", "aliases": [], "size": "8.16 MB"}, {"id": "beg-forgiveness-131", "name": "BEG FORGIVENESS [V16]", "artists": [], "producers": ["Digital Nas"], "notes": "Another reference done by Fat Money. Leaked August 3rd 2024 by Fat Money himself as a response to the release of VULTURES 2.", "length": "188.59", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/5f32e6edf4e10683c02d3f42807e8369", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f32e6edf4e10683c02d3f42807e8369\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V16]\", \"artists\": \"(ref. <PERSON>) (prod. Digital Nas)\", \"description\": \"Another reference done by Fat Money. Leaked August 3rd 2024 by <PERSON> Money himself as a response to the release of VULTURES 2.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"34df1323cc942496dd660379f4e6a2c9\", \"url\": \"https://api.pillowcase.su/api/download/34df1323cc942496dd660379f4e6a2c9\", \"size\": \"8.03 MB\", \"duration\": 188.59}", "aliases": [], "size": "8.03 MB"}, {"id": "beg-forgiveness-132", "name": "BEG FORGIVENESS [V18]", "artists": ["<PERSON>"], "producers": ["Kanye West", "London on da Track", "VITALS", "Digital Nas", "JPEGMAFIA", "SHDØW", "<PERSON>", "<PERSON><PERSON>", "SHDØW"], "notes": "Played on an instagram live on March 29th, 2024. Has alternate production from SHDØW. Unknown when made.", "length": "11.66", "fileDate": 17116704, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ae296f5dbcb74b10eebf324149e75029", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae296f5dbcb74b10eebf324149e75029\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V18]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, London on da Track, VITALS, Digital Nas, JPEGMAFIA, SHD\\u00d8W, <PERSON>, Aver Ray & SHD\\u00d8W)\", \"description\": \"Played on an instagram live on March 29th, 2024. Has alternate production from SHD\\u00d8W. Unknown when made.\", \"date\": 17116704, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9728cccfed55c6a7ec0a36cab0bcfbcf\", \"url\": \"https://api.pillowcase.su/api/download/9728cccfed55c6a7ec0a36cab0bcfbcf\", \"size\": \"5.2 MB\", \"duration\": 11.66}", "aliases": [], "size": "5.2 MB"}, {"id": "beg-forgiveness-133", "name": "BEG FORGIVENESS [V19]", "artists": ["<PERSON>"], "producers": ["Kanye West", "London on da Track", "VITALS", "Digital Nas", "JPEGMAFIA", "SHDØW", "<PERSON>", "<PERSON><PERSON>"], "notes": "Version played at the Chicago listening party. Has an added Ye verse, and a beatswitch with more Ty <PERSON> $ign vocals.", "length": "370.1", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a8a30b65ee1d6a4efb7424ec6f6dfbf7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a8a30b65ee1d6a4efb7424ec6f6dfbf7\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V19]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, London on da Track, VITALS, Digital Nas, JPEGMAFIA, <PERSON><PERSON>\\u00d8W, <PERSON> & <PERSON>)\", \"description\": \"Version played at the Chicago listening party. Has an added Ye verse, and a beatswitch with more Ty Dolla $ign vocals.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"dcd70492cb3e3fdfbda5bfe325ed3550\", \"url\": \"https://api.pillowcase.su/api/download/dcd70492cb3e3fdfbda5bfe325ed3550\", \"size\": \"10.9 MB\", \"duration\": 370.1}", "aliases": [], "size": "10.9 MB"}, {"id": "beg-forgiveness-134", "name": "BEG FORGIVENESS [V27]", "artists": ["<PERSON>"], "producers": ["Kanye West", "London on da Track", "VITALS", "Digital Nas", "JPEGMAFIA", "SHDØW", "<PERSON>", "<PERSON><PERSON>"], "notes": "OG Filename: BEG FORGIVENESS M7 Ye Vers V2 MT001 SPLICED-TUCCI MASTER-24BIT-48kHz\nPost-release mix, leaked after a groupbuy. Has an actual outro that does not cut out prematurely, unlike release. This version was also played at the Italy and France Listening Party events.", "length": "388.41", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/0d8b11cba118cacab3e6d914e0407fe5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d8b11cba118cacab3e6d914e0407fe5\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V27]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, London on da Track, VITALS, Digital Nas, JPEGMAFIA, <PERSON>D\\u00d8W, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: BEG FORGIVENESS M7 Ye Vers V2 MT001 SPLICED-TUCCI MASTER-24BIT-48kHz\\nPost-release mix, leaked after a groupbuy. Has an actual outro that does not cut out prematurely, unlike release. This version was also played at the Italy and France Listening Party events.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7b63c439ec0b0ede04c140488cd4a8b0\", \"url\": \"https://api.pillowcase.su/api/download/7b63c439ec0b0ede04c140488cd4a8b0\", \"size\": \"11.2 MB\", \"duration\": 388.41}", "aliases": [], "size": "11.2 MB"}, {"id": "beg-forgiveness-135", "name": "BEG FORGIVENESS [V28]", "artists": ["<PERSON>", "The Hooligans"], "producers": ["Digital Nas", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event. Has added vocals from The Ultras, also known as The Hooligans.", "length": "54.54", "fileDate": 17100288, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/92c60198fbda53d7875b3aecd2e805ae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/92c60198fbda53d7875b3aecd2e805ae\", \"key\": \"BEG FORGIVENESS\", \"title\": \"BEG FORGIVENESS [V28]\", \"artists\": \"(feat. <PERSON> & The Hooligans) (prod. Digital Nas & JPEGMAFIA)\", \"description\": \"Played at the Footprint center listening event. Has added vocals from The Ultras, also known as The Hooligans.\", \"date\": 17100288, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"01d9b5a44c9b3982eb335aa3c640e03b\", \"url\": \"https://api.pillowcase.su/api/download/01d9b5a44c9b3982eb335aa3c640e03b\", \"size\": \"5.88 MB\", \"duration\": 54.54}", "aliases": [], "size": "5.88 MB"}, {"id": "burn", "name": "BURN [V8]", "artists": [], "producers": ["Kanye West", "Azul", "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "The Legendary Traxster", "<PERSON>"], "notes": "Version played at the Chicago listening party. Has no Ty$ verse, different drums and extra Ye vocals.", "length": "117.47", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/bc1031315cb93bacb0fa9cdb3d4bdffe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bc1031315cb93bacb0fa9cdb3d4bdffe\", \"key\": \"BURN\", \"title\": \"BURN [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> \\\"<PERSON><PERSON><PERSON>\\\" <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, The Legendary Traxster & Leon <PERSON>)\", \"aliases\": [\"Dangerous\"], \"description\": \"Version played at the Chicago listening party. Has no Ty$ verse, different drums and extra Ye vocals.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3485f0ae3e015e934bb2311d85b7d198\", \"url\": \"https://api.pillowcase.su/api/download/3485f0ae3e015e934bb2311d85b7d198\", \"size\": \"6.89 MB\", \"duration\": 117.47}", "aliases": ["Dangerous"], "size": "6.89 MB"}, {"id": "burn-137", "name": "BURN [V9]", "artists": [], "producers": ["Kanye West", "Azul", "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "The Legendary Traxster", "<PERSON>"], "notes": "OG Filename: BURN M7 MT001-TUCCI MASTER-24BIT-48khz\nAlternate mix that leaked after a groupbuy.", "length": "111.46", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4d3d2faadf2ebc3d71244864d3334253", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d3d2faadf2ebc3d71244864d3334253\", \"key\": \"BURN\", \"title\": \"BURN [V9]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> \\\"<PERSON><PERSON><PERSON>\\\" <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, The Legendary Traxster & Leon <PERSON>)\", \"aliases\": [\"Dangerous\"], \"description\": \"OG Filename: BURN M7 MT001-TUCCI MASTER-24BIT-48khz\\nAlternate mix that leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8bc8946423f06daf22aad966a0cf2b6b\", \"url\": \"https://api.pillowcase.su/api/download/8bc8946423f06daf22aad966a0cf2b6b\", \"size\": \"6.79 MB\", \"duration\": 111.46}", "aliases": ["Dangerous"], "size": "6.79 MB"}, {"id": "honor-roll-138", "name": "HONOR ROLL [V6]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["TheLabCook", "Ojivolta"], "notes": "Version heard in an unreleased <PERSON><PERSON> acapella bleed. Has new production and features The Hooligans. Ye's vocal take is different from release with some different lines. Also has different <PERSON><PERSON> adlibs. Any other differences are unknown. Original snippet leaked July 22nd, 2024.", "length": "264.41", "fileDate": 17222112, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/540fef1be5f24fe762eae5529ccb9f84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/540fef1be5f24fe762eae5529ccb9f84\", \"key\": \"HONOR ROLL\", \"title\": \"HONOR ROLL [V6]\", \"artists\": \"(feat. <PERSON> Hooligans, <PERSON> & <PERSON><PERSON>) (prod. TheLabCook & Ojivolta)\", \"aliases\": [\"CARNIVAL\", \"H00LIGANS\"], \"description\": \"Version heard in an unreleased Carti acapella bleed. Has new production and features The Hooligans. <PERSON>'s vocal take is different from release with some different lines. Also has different Carti adlibs. Any other differences are unknown. Original snippet leaked July 22nd, 2024.\", \"date\": 17222112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a7f39e2214ff6fe7a0a2e1a67ab8709a\", \"url\": \"https://api.pillowcase.su/api/download/a7f39e2214ff6fe7a0a2e1a67ab8709a\", \"size\": \"9.24 MB\", \"duration\": 264.41}", "aliases": ["CARNIVAL", "H00LIGANS"], "size": "9.24 MB"}, {"id": "carnival", "name": "CARNIVAL [V7]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "Version played at the Chicago listening party with a choir replacing the synth melody, new drums, a new take of <PERSON> the Kid's verse, a re-recorded Ye verse and a new <PERSON><PERSON> verse, but still has the \"Iron Man\" sample. Ojivolta posted a story with the caption \"CARNIVAL / H00LIGANS\" confirming they did production for it. Version without the crowd found March 26th, 2024.", "length": "264.26", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ebfe897ed605eafe1eecb04c0eb69dfa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ebfe897ed605eafe1eecb04c0eb69dfa\", \"key\": \"CARNIVAL\", \"title\": \"CARNIVAL [V7]\", \"artists\": \"(feat. <PERSON> Ho<PERSON>gans, <PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"H00LIGANS\"], \"description\": \"Version played at the Chicago listening party with a choir replacing the synth melody, new drums, a new take of <PERSON> the <PERSON>'s verse, a re-recorded <PERSON> verse and a new <PERSON><PERSON> verse, but still has the \\\"Iron Man\\\" sample. Ojivolta posted a story with the caption \\\"CARNIVAL / H00LIGANS\\\" confirming they did production for it. Version without the crowd found March 26th, 2024.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ce4d19ce541f93b2b994b5c1ad0c19c4\", \"url\": \"https://api.pillowcase.su/api/download/ce4d19ce541f93b2b994b5c1ad0c19c4\", \"size\": \"9.24 MB\", \"duration\": 264.26}", "aliases": ["H00LIGANS"], "size": "9.24 MB"}, {"id": "carnival-140", "name": "CARNIVAL [V7]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "Version played at the Chicago listening party with a choir replacing the synth melody, new drums, a new take of <PERSON> the Kid's verse, a re-recorded Ye verse and a new <PERSON><PERSON> verse, but still has the \"Iron Man\" sample. Ojivolta posted a story with the caption \"CARNIVAL / H00LIGANS\" confirming they did production for it. Version without the crowd found March 26th, 2024.", "length": "266.06", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/2e515323f4be8a2a92427f127819cd82", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2e515323f4be8a2a92427f127819cd82\", \"key\": \"CARNIVAL\", \"title\": \"CARNIVAL [V7]\", \"artists\": \"(feat. <PERSON> Ho<PERSON>gans, <PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"H00LIGANS\"], \"description\": \"Version played at the Chicago listening party with a choir replacing the synth melody, new drums, a new take of <PERSON> the <PERSON>'s verse, a re-recorded Ye verse and a new <PERSON><PERSON> verse, but still has the \\\"Iron Man\\\" sample. Ojivolta posted a story with the caption \\\"CARNIVAL / H00LIGANS\\\" confirming they did production for it. Version without the crowd found March 26th, 2024.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1757135a289ec1b14bd4b67d684b50c7\", \"url\": \"https://api.pillowcase.su/api/download/1757135a289ec1b14bd4b67d684b50c7\", \"size\": \"9.27 MB\", \"duration\": 266.06}", "aliases": ["H00LIGANS"], "size": "9.27 MB"}, {"id": "carnival-141", "name": "CARNIVAL [V8]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign", "London on da Track"], "notes": "Version with production from London on da Track. Has different drums, but apart from that is very similar to release.", "length": "264.39", "fileDate": 17374176, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/b3e155d8b17d8cd6e73803b82268baa0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b3e155d8b17d8cd6e73803b82268baa0\", \"key\": \"CARNIVAL\", \"title\": \"CARNIVAL [V8]\", \"artists\": \"(feat. <PERSON>gans, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas, Ty Dolla $ign & London on da Track)\", \"aliases\": [\"H00LIGANS\"], \"description\": \"Version with production from London on da Track. Has different drums, but apart from that is very similar to release.\", \"date\": 17374176, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c03203392b8f7db8012270535ac3bde5\", \"url\": \"https://api.pillowcase.su/api/download/c03203392b8f7db8012270535ac3bde5\", \"size\": \"9.24 MB\", \"duration\": 264.39}", "aliases": ["H00LIGANS"], "size": "9.24 MB"}, {"id": "carnival-142", "name": "CARNIVAL [V18]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "OG Filename: CARNIVAL M10 DECLICK MT001-<PERSON><PERSON><PERSON> MASTER-24BIT-48kHz\nPost-release mix, leaked after a groupbuy. Has better mixing.", "length": "264.32", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d14092c884990dd6b934aaffa1519cd0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d14092c884990dd6b934aaffa1519cd0\", \"key\": \"CARNIVAL\", \"title\": \"CARNIVAL [V18]\", \"artists\": \"(feat. <PERSON>gans, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"H00LIGANS\"], \"description\": \"OG Filename: CARNIVAL M10 DECLICK MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release mix, leaked after a groupbuy. Has better mixing.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2603d8716a1fc6fd18b7ee90e7e9c836\", \"url\": \"https://api.pillowcase.su/api/download/2603d8716a1fc6fd18b7ee90e7e9c836\", \"size\": \"9.24 MB\", \"duration\": 264.32}", "aliases": ["H00LIGANS"], "size": "9.24 MB"}, {"id": "carnival-143", "name": "CARNIVAL [V19]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "Updated version adding a sample of a Spanish guy screaming \"Puro pinche carnival y no mamadas\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earliert the same day it fully leaked. Different to music video version.", "length": "260.33", "fileDate": 17095968, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/22b692c40a610071070452fe1e355847", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/22b692c40a610071070452fe1e355847\", \"key\": \"CARNIVAL\", \"title\": \"CARNIVAL [V19]\", \"artists\": \"(feat. <PERSON> Ho<PERSON>gans, <PERSON> & <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"H00LIGANS\"], \"description\": \"Updated version adding a sample of a Spanish guy screaming \\\"Puro pinche carnival y no mamadas\\\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earliert the same day it fully leaked. Different to music video version.\", \"date\": 17095968, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"bbfccbe53aec094af6e066a2a8769763\", \"url\": \"https://api.pillowcase.su/api/download/bbfccbe53aec094af6e066a2a8769763\", \"size\": \"9.17 MB\", \"duration\": 260.33}", "aliases": ["H00LIGANS"], "size": "9.17 MB"}, {"id": "carnival-144", "name": "CARNIVAL [V19]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "Updated version adding a sample of a Spanish guy screaming \"Puro pinche carnival y no mamadas\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earliert the same day it fully leaked. Different to music video version.", "length": "12.98", "fileDate": 17095968, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3391dc63a712bff57aab25deeee964c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3391dc63a712bff57aab25deeee964c2\", \"key\": \"CARNIVAL\", \"title\": \"CARNIVAL [V19]\", \"artists\": \"(feat. <PERSON>gans, <PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"H00LIGANS\"], \"description\": \"Updated version adding a sample of a Spanish guy screaming \\\"Puro pinche carnival y no mamadas\\\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earliert the same day it fully leaked. Different to music video version.\", \"date\": 17095968, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"15bf36e9127ca6f029afcb2fc56f6397\", \"url\": \"https://api.pillowcase.su/api/download/15bf36e9127ca6f029afcb2fc56f6397\", \"size\": \"5.22 MB\", \"duration\": 12.98}", "aliases": ["H00LIGANS"], "size": "5.22 MB"}, {"id": "do-it-145", "name": "DO IT [V7]", "artists": ["YG", "Tyga"], "producers": ["Wheezy"], "notes": "Version of \"DO IT\" with a Tyga feature. Has slightly different production to the afterparty version, as well as different Tyga vocals. CDQ snippet leaked February 11th, 2024. Played in full on a Tyga YouTube stream February 1st, 2025.", "length": "10.5", "fileDate": 17383680, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/893a3c3e4f14af7b13a28cc49d4aecff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/893a3c3e4f14af7b13a28cc49d4aecff\", \"key\": \"DO IT\", \"title\": \"DO IT [V7]\", \"artists\": \"(feat. YG & Tyga) (prod. Wheezy)\", \"description\": \"Version of \\\"DO IT\\\" with a Tyga feature. Has slightly different production to the afterparty version, as well as different Tyga vocals. CDQ snippet leaked February 11th, 2024. Played in full on a Tyga YouTube stream February 1st, 2025.\", \"date\": 17383680, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f00fb91a3e8abb609c943947f09f89e1\", \"url\": \"https://api.pillowcase.su/api/download/f00fb91a3e8abb609c943947f09f89e1\", \"size\": \"5.18 MB\", \"duration\": 10.5}", "aliases": [], "size": "5.18 MB"}, {"id": "do-it-146", "name": "DO IT [V7]", "artists": ["YG", "Tyga"], "producers": ["Wheezy"], "notes": "Version of \"DO IT\" with a Tyga feature. Has slightly different production to the afterparty version, as well as different Tyga vocals. CDQ snippet leaked February 11th, 2024. Played in full on a Tyga YouTube stream February 1st, 2025.", "length": "266.09", "fileDate": 17383680, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/27c25c2692dec82e3b50ceaff632a8e8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/27c25c2692dec82e3b50ceaff632a8e8\", \"key\": \"DO IT\", \"title\": \"DO IT [V7]\", \"artists\": \"(feat. YG & Tyga) (prod. Wheezy)\", \"description\": \"Version of \\\"DO IT\\\" with a Tyga feature. Has slightly different production to the afterparty version, as well as different Tyga vocals. CDQ snippet leaked February 11th, 2024. Played in full on a Tyga YouTube stream February 1st, 2025.\", \"date\": 17383680, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"53a671cf804e50337f25a4d232e663da\", \"url\": \"https://api.pillowcase.su/api/download/53a671cf804e50337f25a4d232e663da\", \"size\": \"9.27 MB\", \"duration\": 266.09}", "aliases": [], "size": "9.27 MB"}, {"id": "do-it-147", "name": "DO IT [V8]", "artists": ["YG", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "Mustard", "Wheezy", "<PERSON><PERSON>", "CuB<PERSON>z", "LukasBL", "DTP", "The Legendary Traxster", "JPEGMAFIA"], "notes": "OG Filename: doit8barintro\nEarlier clean version. Basically same as release but there is an 8-bar intro before the choir intro, different mixing and the song being clean. Instrumental originally leaked June 9th, 2024.", "length": "242.24", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/5c8fe3e69e2b1dc4adf75e91e8ef90e7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5c8fe3e69e2b1dc4adf75e91e8ef90e7\", \"key\": \"DO IT\", \"title\": \"DO IT [V8]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, DTP, The Legendary Traxster & JPEGMAFIA)\", \"description\": \"OG Filename: doit8barintro\\nEarlier clean version. Basically same as release but there is an 8-bar intro before the choir intro, different mixing and the song being clean. Instrumental originally leaked June 9th, 2024.\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cf9bc132831cabaf3329751d13e1af54\", \"url\": \"https://api.pillowcase.su/api/download/cf9bc132831cabaf3329751d13e1af54\", \"size\": \"8.89 MB\", \"duration\": 242.24}", "aliases": [], "size": "8.89 MB"}, {"id": "do-it-148", "name": "DO IT [V9]", "artists": ["YG", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "Mustard", "Wheezy", "<PERSON><PERSON>", "CuB<PERSON>z", "LukasBL", "DTP", "The Legendary Traxster", "JPEGMAFIA"], "notes": "Version played at the Chicago listening party. Contains an evolved instrumental. <PERSON><PERSON> has a short <PERSON><PERSON><PERSON> verse stemming from unreleased song \"I Just Wanna Know\". New Ty$ line in <PERSON>'s verse, which was also re-recorded. Beat switch samples \"Back That Azz Up\" - Juvenile.", "length": "242.29", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7e92d73696638a5cb14b895974980be6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e92d73696638a5cb14b895974980be6\", \"key\": \"DO IT\", \"title\": \"DO IT [V9]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, D<PERSON>, The Legendary Traxster & JPEGMAFIA)\", \"description\": \"Version played at the Chicago listening party. Contains an evolved instrumental. <PERSON><PERSON> has a short <PERSON><PERSON><PERSON> verse stemming from unreleased song \\\"I Just Wanna Know\\\". New Ty$ line in <PERSON>'s verse, which was also re-recorded. Beat switch samples \\\"Back That Azz Up\\\" - Juvenile.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"01ea4553d3a37d2dd234ab2a962d926d\", \"url\": \"https://api.pillowcase.su/api/download/01ea4553d3a37d2dd234ab2a962d926d\", \"size\": \"8.89 MB\", \"duration\": 242.29}", "aliases": [], "size": "8.89 MB"}, {"id": "do-it-149", "name": "DO IT [V10]", "artists": ["YG", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "Mustard", "Wheezy", "<PERSON><PERSON>", "CuB<PERSON>z", "LukasBL", "DTP", "The Legendary Traxster", "JPEGMAFIA"], "notes": "OG Filename: yetydollasignfeatyg-doit\nSame as release but clean (clean version was never released).", "length": "224.68", "fileDate": 17253216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ca6a1c8e7a23a323c47f6cfbad696e5d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ca6a1c8e7a23a323c47f6cfbad696e5d\", \"key\": \"DO IT\", \"title\": \"DO IT [V10]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, DTP, The Legendary Traxster & JPEGMAFIA)\", \"description\": \"OG Filename: yetydollasignfeatyg-doit\\nSame as release but clean (clean version was never released).\", \"date\": 17253216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"279e8a3f1d2ef47e3a3209dff4130cf9\", \"url\": \"https://api.pillowcase.su/api/download/279e8a3f1d2ef47e3a3209dff4130cf9\", \"size\": \"8.6 MB\", \"duration\": 224.68}", "aliases": [], "size": "8.6 MB"}, {"id": "do-it-150", "name": "DO IT [V18]", "artists": ["YG", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "Mustard", "Wheezy", "<PERSON><PERSON>", "CuB<PERSON>z", "LukasBL", "DTP", "The Legendary Traxster", "JPEGMAFIA"], "notes": "OG Filename: Do It M7 24 bit\nAlternative mix, leaked after a groupbuy.", "length": "225", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/823886c12d74474fea99abe79beed43d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/823886c12d74474fea99abe79beed43d\", \"key\": \"DO IT\", \"title\": \"DO IT [V18]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, DTP, The Legendary Traxster & JPEGMAFIA)\", \"description\": \"OG Filename: Do It M7 24 bit\\nAlternative mix, leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e4c8104aded5a1bd73fa55ef5fe29664\", \"url\": \"https://api.pillowcase.su/api/download/e4c8104aded5a1bd73fa55ef5fe29664\", \"size\": \"8.61 MB\", \"duration\": 225}", "aliases": [], "size": "8.61 MB"}, {"id": "do-it-151", "name": "DO IT [V19]", "artists": ["YG", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "Mustard", "Wheezy", "<PERSON><PERSON>", "CuB<PERSON>z", "LukasBL", "DTP", "The Legendary Traxster", "JPEGMAFIA"], "notes": "OG Filename: DO IT CLEAN M8 FLAT MT001-TUCCI MASTER-24BIT-48kHz\nPost-release clean mix, leaked after a groupbuy. Has worse mixing compared to the previous version.", "length": "225", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d0f4def12f410cc790be7d7e836087a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d0f4def12f410cc790be7d7e836087a9\", \"key\": \"DO IT\", \"title\": \"DO IT [V19]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, LukasBL, DTP, The Legendary Traxster & JPEGMAFIA)\", \"description\": \"OG Filename: DO IT CLEAN M8 FLAT MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release clean mix, leaked after a groupbuy. Has worse mixing compared to the previous version.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"75d311da147f8e3f335a91db6124f1c1\", \"url\": \"https://api.pillowcase.su/api/download/75d311da147f8e3f335a91db6124f1c1\", \"size\": \"8.61 MB\", \"duration\": 225}", "aliases": [], "size": "8.61 MB"}, {"id": "everybody-152", "name": "🗑️ EVERYBODY [V9]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: V2 1-EB 2\nVersion that has vocals mainly from <PERSON> and only a few from <PERSON> that start at 1:29 and are low pitch likely meant for a \"I'm In It\" effect. Has 2 open verses (first one most likely for <PERSON> and second for <PERSON>) and has a gap on the outro. Made on or before Dec 16, 2023.", "length": "156.16", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c42394afa704e1489eb63ae581ec1104", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c42394afa704e1489eb63ae581ec1104\", \"key\": \"EVERYBODY\", \"title\": \"\\ud83d\\uddd1\\ufe0f EVERYBODY [V9]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: V2 1-EB 2\\nVersion that has vocals mainly from <PERSON> and only a few from <PERSON> that start at 1:29 and are low pitch likely meant for a \\\"I'm In It\\\" effect. Has 2 open verses (first one most likely for <PERSON> and second for <PERSON>) and has a gap on the outro. Made on or before Dec 16, 2023.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fc7f313f9882c5f9be254965290eec5f\", \"url\": \"https://api.pillowcase.su/api/download/fc7f313f9882c5f9be254965290eec5f\", \"size\": \"7.51 MB\", \"duration\": 156.16}", "aliases": [], "size": "7.51 MB"}, {"id": "everybody-153", "name": "EVERYBODY [V10]", "artists": ["<PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: EVERYBODY V2A\nVersion with alternate production that is not seen on any other version. This version is a stem bounce.", "length": "156.17", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1eca2567f9cdbb2083980e807aaad07f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1eca2567f9cdbb2083980e807aaad07f\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V10]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: EVERYBODY V2A\\nVersion with alternate production that is not seen on any other version. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"41084a983130a63d7584c92159cee52f\", \"url\": \"https://api.pillowcase.su/api/download/41084a983130a63d7584c92159cee52f\", \"size\": \"7.51 MB\", \"duration\": 156.17}", "aliases": [], "size": "7.51 MB"}, {"id": "everybody-154", "name": "EVERYBODY [V11]", "artists": ["<PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: everybody - Track 1\nThis version has rearranged order of the verses that would carry onto later versions. Made on or before December 19th, 2023.", "length": "208.96", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4dddd4f778b756f5635c89975a5425f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4dddd4f778b756f5635c89975a5425f3\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V11]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: everybody - Track 1\\nThis version has rearranged order of the verses that would carry onto later versions. Made on or before December 19th, 2023.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"70629a87f30a70387d99d4cfe61a80b5\", \"url\": \"https://api.pillowcase.su/api/download/70629a87f30a70387d99d4cfe61a80b5\", \"size\": \"8.35 MB\", \"duration\": 208.96}", "aliases": [], "size": "8.35 MB"}, {"id": "everybody-155", "name": "⭐ EVERYBODY [V12]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track"], "notes": "OG Filename: everybody 108 london adds\nVersion with added London on da Track production. This version is a stem bounce. Is missing the \"murder scene\" bar from Ye.", "length": "227.23", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/76887878a0b4d43272ea75fd7a97f5a4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76887878a0b4d43272ea75fd7a97f5a4\", \"key\": \"EVERYBODY\", \"title\": \"\\u2b50 EVERYBODY [V12]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track)\", \"description\": \"OG Filename: everybody 108 london adds\\nVersion with added London on da Track production. This version is a stem bounce. Is missing the \\\"murder scene\\\" bar from Ye.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ef50a79bbf8af20edc7d3632f3995852\", \"url\": \"https://api.pillowcase.su/api/download/ef50a79bbf8af20edc7d3632f3995852\", \"size\": \"8.65 MB\", \"duration\": 227.23}", "aliases": [], "size": "8.65 MB"}, {"id": "everybody-156", "name": "EVERYBODY [V13]", "artists": ["<PERSON>"], "producers": ["London on da Track"], "notes": "Has alternate production, and the same sample that would go onto be used in the Chicago & New York listening party versions of the song. Unknown when it was made. Snippet leaked December 30th, 2023.", "length": "16.22", "fileDate": 17038944, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/77872015530a74d10455b86cc78813f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/77872015530a74d10455b86cc78813f5\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V13]\", \"artists\": \"(feat. <PERSON>) (prod. London on da Track)\", \"description\": \"Has alternate production, and the same sample that would go onto be used in the Chicago & New York listening party versions of the song. Unknown when it was made. Snippet leaked December 30th, 2023.\", \"date\": 17038944, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bbc1fc7d8c66f33dfa43761dcf47d0c2\", \"url\": \"https://api.pillowcase.su/api/download/bbc1fc7d8c66f33dfa43761dcf47d0c2\", \"size\": \"5.27 MB\", \"duration\": 16.22}", "aliases": [], "size": "5.27 MB"}, {"id": "everybody-157", "name": "EVERYBODY [V14]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track", "Timbaland", "will.i.am", "Kanye West"], "notes": "OG Filename: EVERYBODY 12.23.F1.2\nPremiered during a DJ set. <PERSON> has no drums. <PERSON> has shorter structure and an uncut <PERSON> Baby verse.", "length": "19.2", "fileDate": 17044992, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d792be2d9d024987f4d22e8c53c78f1e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d792be2d9d024987f4d22e8c53c78f1e\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V14]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track, Timbaland, will.i.am & <PERSON>ny<PERSON> West)\", \"description\": \"OG Filename: EVERYBODY 12.23.F1.2\\nPremiered during a DJ set. Still has no drums. <PERSON> has shorter structure and an uncut Lil Baby verse.\", \"date\": 17044992, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7b0c8ad3d7a0f6c1d7d443344e49d613\", \"url\": \"https://api.pillowcase.su/api/download/7b0c8ad3d7a0f6c1d7d443344e49d613\", \"size\": \"5.16 MB\", \"duration\": 19.2}", "aliases": [], "size": "5.16 MB"}, {"id": "everybody-158", "name": "EVERYBODY [V14]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track", "Timbaland", "will.i.am", "Kanye West"], "notes": "OG Filename: EVERYBODY 12.23.F1.2\nPremiered during a DJ set. <PERSON> has no drums. <PERSON> has shorter structure and an uncut <PERSON> Baby verse.", "length": "7.18", "fileDate": 17044992, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6c40c54594285a128cb234673b93a298", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6c40c54594285a128cb234673b93a298\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V14]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track, Timbaland, will.i.am & <PERSON>ny<PERSON> West)\", \"description\": \"OG Filename: EVERYBODY 12.23.F1.2\\nPremiered during a DJ set. Still has no drums. <PERSON> has shorter structure and an uncut Lil Baby verse.\", \"date\": 17044992, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b9bb0baae93a0098f4c1ba16bf123928\", \"url\": \"https://api.pillowcase.su/api/download/b9bb0baae93a0098f4c1ba16bf123928\", \"size\": \"5.07 MB\", \"duration\": 7.18}", "aliases": [], "size": "5.07 MB"}, {"id": "everybody-159", "name": "EVERYBODY [V14]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track", "Timbaland", "will.i.am", "Kanye West"], "notes": "OG Filename: EVERYBODY 12.23.F1.2\nPremiered during a DJ set. <PERSON> has no drums. <PERSON> has shorter structure and an uncut <PERSON> Baby verse.", "length": "11.76", "fileDate": 17044992, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e5508e8cdb4202af6986f5cb7e60b0fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e5508e8cdb4202af6986f5cb7e60b0fc\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V14]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track, Timbaland, will.i.am & Kanye West)\", \"description\": \"OG Filename: EVERYBODY 12.23.F1.2\\nPremiered during a DJ set. Still has no drums. <PERSON> has shorter structure and an uncut Lil Baby verse.\", \"date\": 17044992, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a57de9994aa0f47e0b8a7ef26f033f14\", \"url\": \"https://api.pillowcase.su/api/download/a57de9994aa0f47e0b8a7ef26f033f14\", \"size\": \"5.2 MB\", \"duration\": 11.76}", "aliases": [], "size": "5.2 MB"}, {"id": "everybody-160", "name": "EVERYBODY [V14]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track", "Timbaland", "will.i.am", "Kanye West"], "notes": "OG Filename: EVERYBODY 12.23.F1.2\nPremiered during a DJ set. <PERSON> has no drums. <PERSON> has shorter structure and an uncut <PERSON> Baby verse.", "length": "3.27", "fileDate": 17044992, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/bb3ecfdb352ff0b38999f131fa95e287", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb3ecfdb352ff0b38999f131fa95e287\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V14]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track, Timbaland, will.i.am & <PERSON>nye West)\", \"description\": \"OG Filename: EVERYBODY 12.23.F1.2\\nPremiered during a DJ set. Still has no drums. <PERSON> has shorter structure and an uncut Lil Baby verse.\", \"date\": 17044992, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0d57dbffe8af9b720c374a87455dc7e6\", \"url\": \"https://api.pillowcase.su/api/download/0d57dbffe8af9b720c374a87455dc7e6\", \"size\": \"5.04 MB\", \"duration\": 3.27}", "aliases": [], "size": "5.04 MB"}, {"id": "everybody-161", "name": "EVERYBODY [V15]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track", "Timbaland", "will.i.am", "Kanye West"], "notes": "Version played at the Chicago listening party. Has a different structure. Contains a Shabba Ranks sample in the outro (\"Shine Eye Gal\").", "length": "236.36", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f82ac8cc7a352ceeb1d281eb17e0806a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f82ac8cc7a352ceeb1d281eb17e0806a\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V15]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track, Timbaland, will.i.am & <PERSON>nye West)\", \"description\": \"Version played at the Chicago listening party. Has a different structure. Contains a Shabba Ranks sample in the outro (\\\"Shine Eye Gal\\\").\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c1dd0f239c651f7ae220c13b7dfa00b3\", \"url\": \"https://api.pillowcase.su/api/download/c1dd0f239c651f7ae220c13b7dfa00b3\", \"size\": \"8.79 MB\", \"duration\": 236.36}", "aliases": [], "size": "8.79 MB"}, {"id": "everybody-162", "name": "EVERYBODY [V16]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["London on da Track", "Timbaland", "will.i.am", "Kanye West"], "notes": "Version played at the New York listening party. Has a different mix and some added production. Was notably missing from the album on release, likely due to sample clearance issues.", "length": "234.07", "fileDate": 17074368, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c77da61b050ed583d06a01209701d9b4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c77da61b050ed583d06a01209701d9b4\", \"key\": \"EVERYBODY\", \"title\": \"EVERYBODY [V16]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. London on da Track, Timbaland, will.i.am & Kanye West)\", \"description\": \"Version played at the New York listening party. Has a different mix and some added production. Was notably missing from the album on release, likely due to sample clearance issues.\", \"date\": 17074368, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c48c65dc107f43ce45764e7c4178645d\", \"url\": \"https://api.pillowcase.su/api/download/c48c65dc107f43ce45764e7c4178645d\", \"size\": \"8.75 MB\", \"duration\": 234.07}", "aliases": [], "size": "8.75 MB"}, {"id": "field-trip-163", "name": "✨ FIELD TRIP [V9]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "OG Filename: field trip 12.21.\nVersion from December 21st, 2023. Leaked during the Can U Be Groupbuy on May 26th, 2024. Said to have the worst mixing out of any version by insiders. <PERSON> vocals.", "length": "318.03", "fileDate": 17166816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c8b5899b69970ea9d8841b94c0bc6a9d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8b5899b69970ea9d8841b94c0bc6a9d\", \"key\": \"FIELD TRIP\", \"title\": \"\\u2728 FIELD TRIP [V9]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: field trip 12.21.\\nVersion from December 21st, 2023. Leaked during the Can U Be Groupbuy on May 26th, 2024. Said to have the worst mixing out of any version by insiders. No Ye vocals.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a938be6211091cae1995d54f41aaa15\", \"url\": \"https://api.pillowcase.su/api/download/4a938be6211091cae1995d54f41aaa15\", \"size\": \"10.1 MB\", \"duration\": 318.03}", "aliases": [], "size": "10.1 MB"}, {"id": "field-trip-164", "name": "FIELD TRIP [V11]", "artists": ["<PERSON>"], "producers": ["The Legendary Traxster"], "notes": "OG Filename: Field Trip Traxster 2.2\nTraxster-produced version. Likely the original concept for the beatswitch. This is the full bounce. This section would later be used for the <PERSON>dak part on release, but with other drums.", "length": "47.21", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/03a1a9a141c33b3e598d105271de6d07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03a1a9a141c33b3e598d105271de6d07\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V11]\", \"artists\": \"(feat. <PERSON>) (prod. The Legendary Traxster)\", \"description\": \"OG Filename: Field Trip Traxster 2.2\\nTraxster-produced version. Likely the original concept for the beatswitch. This is the full bounce. This section would later be used for the Kodak part on release, but with other drums.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"47119be910084cb15714c02b0e23066d\", \"url\": \"https://api.pillowcase.su/api/download/47119be910084cb15714c02b0e23066d\", \"size\": \"5.77 MB\", \"duration\": 47.21}", "aliases": [], "size": "5.77 MB"}, {"id": "fuk-sumn-165", "name": "FUK SUMN [V16]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Version with Quavo's reference verse added back with the new JPEGMAFIA production. Possibly made before the music video shoot. Snippet was posted on JPEGMAFIA's story on February 23rd, 2024, and previewed at Pitchfork Music Festival March 7th, 2024. Has no <PERSON><PERSON><PERSON> vocals.", "length": "95.66", "fileDate": 17097696, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d44dd3eddeb56001c3d75d3249a6519b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d44dd3eddeb56001c3d75d3249a6519b\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V16]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Version with <PERSON><PERSON><PERSON>'s reference verse added back with the new JPEGMAFIA production. Possibly made before the music video shoot. Snippet was posted on JPEGMAFIA's story on February 23rd, 2024, and previewed at Pitchfork Music Festival March 7th, 2024. Has no <PERSON><PERSON><PERSON> vocals.\", \"date\": 17097696, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c1cbe21b59631bcb09a9bd7b96060579\", \"url\": \"https://api.pillowcase.su/api/download/c1cbe21b59631bcb09a9bd7b96060579\", \"size\": \"5.77 MB\", \"duration\": 95.66}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.77 MB"}, {"id": "fuk-sumn-166", "name": "FUK SUMN [V17]", "artists": [], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "<PERSON> did a reference track for the Ye verse on \"Fuk Sumn\" which can be heard in the background on the pitched up section of the song. Snippet of the actual version of the song leaked October 14th, 2024.", "length": "26.5", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c693136bd7e20e80a927d2a7fdb9f402", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c693136bd7e20e80a927d2a7fdb9f402\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V17]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> Nas, Timbal<PERSON>, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"<PERSON> did a reference track for the Ye verse on \\\"Fuk Sumn\\\" which can be heard in the background on the pitched up section of the song. Snippet of the actual version of the song leaked October 14th, 2024.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"23d8a8baf47fd9a4e6aebaa1998dbbe5\", \"url\": \"https://api.pillowcase.su/api/download/23d8a8baf47fd9a4e6aebaa1998dbbe5\", \"size\": \"5.43 MB\", \"duration\": 26.5}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.43 MB"}, {"id": "fuk-sumn-167", "name": "FUK SUMN [V17]", "artists": [], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "<PERSON> did a reference track for the Ye verse on \"Fuk Sumn\" which can be heard in the background on the pitched up section of the song. Snippet of the actual version of the song leaked October 14th, 2024.", "length": "32.34", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a6f0290d4feafc99bfcdd94b70024551", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6f0290d4feafc99bfcdd94b70024551\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V17]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> Nas, Timbal<PERSON>, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"<PERSON> did a reference track for the Ye verse on \\\"Fuk Sumn\\\" which can be heard in the background on the pitched up section of the song. Snippet of the actual version of the song leaked October 14th, 2024.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"d3ee39ed02f53b29d3d2bbbb84c90d06\", \"url\": \"https://api.pillowcase.su/api/download/d3ee39ed02f53b29d3d2bbbb84c90d06\", \"size\": \"5.53 MB\", \"duration\": 32.34}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.53 MB"}, {"id": "fuk-sumn-168", "name": "FUK SUMN [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "An updated version without the Q<PERSON>vo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.", "length": "34.13", "fileDate": 17051904, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/b83f938b9fd365aa937deedb406276c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b83f938b9fd365aa937deedb406276c2\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbal<PERSON>, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"An updated version without the Quavo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.\", \"date\": 17051904, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b68a045ef53276e6773660add46e38f0\", \"url\": \"https://api.pillowcase.su/api/download/b68a045ef53276e6773660add46e38f0\", \"size\": \"5.56 MB\", \"duration\": 34.13}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.56 MB"}, {"id": "fuk-sumn-169", "name": "FUK SUMN [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "An updated version without the Q<PERSON>vo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.", "length": "22.73", "fileDate": 17051904, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/bad81214f2aabb2ce3cfe3bc98404d13", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bad81214f2aabb2ce3cfe3bc98404d13\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas, <PERSON>bal<PERSON>, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"An updated version without the Quavo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.\", \"date\": 17051904, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b069eedf15fc7aeb7eb0d0bd6e5774a8\", \"url\": \"https://api.pillowcase.su/api/download/b069eedf15fc7aeb7eb0d0bd6e5774a8\", \"size\": \"5.37 MB\", \"duration\": 22.73}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.37 MB"}, {"id": "fuk-sumn-170", "name": "FUK SUMN [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "An updated version without the Q<PERSON>vo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.", "length": "18.41", "fileDate": 17051904, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c4904651021f5aac5c270009485cc22f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4904651021f5aac5c270009485cc22f\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas, Timbal<PERSON>, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"An updated version without the Quavo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.\", \"date\": 17051904, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ae18c0fcb305c01897699201ed8f9281\", \"url\": \"https://api.pillowcase.su/api/download/ae18c0fcb305c01897699201ed8f9281\", \"size\": \"5.3 MB\", \"duration\": 18.41}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "5.3 MB"}, {"id": "fuk-sumn-171", "name": "FUK SUMN [V20]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Version played at the Chicago listening party. Has alternate production, with most of the main melody removed. Contains a beatswitch with pitched up Ye vocals and a new <PERSON> feature.", "length": "208.12", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/98e3594c6e15fa0df56e98b6e5d74b8e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/98e3594c6e15fa0df56e98b6e5d74b8e\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V20]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, <PERSON><PERSON>, <PERSON> & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Version played at the Chicago listening party. Has alternate production, with most of the main melody removed. Contains a beatswitch with pitched up Ye vocals and a new Travis feature.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"715014f97dd3570ec6af1c20d8e9f5ef\", \"url\": \"https://api.pillowcase.su/api/download/715014f97dd3570ec6af1c20d8e9f5ef\", \"size\": \"8.34 MB\", \"duration\": 208.12}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "8.34 MB"}, {"id": "fuk-sumn-172", "name": "FUK SUMN [V31]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "OG Filename: Fuk Sumn M11\nAlternate post-release mix, leaked after a groupbuy.", "length": "209.58", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6f163d8f301b8b668121c2426458d951", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f163d8f301b8b668121c2426458d951\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V31]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8W, <PERSON><PERSON>, Chrishan & JPEGMAFIA)\", \"description\": \"OG Filename: Fuk Sumn M11\\nAlternate post-release mix, leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c776596368feb7126ae8596094363a02\", \"url\": \"https://api.pillowcase.su/api/download/c776596368feb7126ae8596094363a02\", \"size\": \"8.36 MB\", \"duration\": 209.58}", "aliases": [], "size": "8.36 MB"}, {"id": "fuk-sumn-173", "name": "FUK SUMN (Remix) [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. According to <PERSON><PERSON><PERSON>, they still had intentions to drop the remix, but it's unknown if those plans are still going to happen, due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFI<PERSON> on his Outbreak set on June 28th, 2024.", "length": "82.08", "fileDate": 17252352, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/b4cd9c9e873ebdc443bbd27fb4d0f188", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b4cd9c9e873ebdc443bbd27fb4d0f188\", \"key\": \"FUK SUMN (Remix)\", \"title\": \"FUK SUMN (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. According to JoyBoy, they still had intentions to drop the remix, but it's unknown if those plans are still going to happen, due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by <PERSON><PERSON><PERSON><PERSON><PERSON> on his Outbreak set on June 28th, 2024.\", \"date\": 17252352, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4e771e57d6a87e33ef63f824c0918e94\", \"url\": \"https://api.pillowcase.su/api/download/4e771e57d6a87e33ef63f824c0918e94\", \"size\": \"6.32 MB\", \"duration\": 82.08}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "6.32 MB"}, {"id": "fuk-sumn-174", "name": "FUK SUMN (Remix) [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. According to <PERSON><PERSON><PERSON>, they still had intentions to drop the remix, but it's unknown if those plans are still going to happen, due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFI<PERSON> on his Outbreak set on June 28th, 2024.", "length": "245.86", "fileDate": 17252352, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/9d9ce30dcda550ddf6e9662cdaf8903a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d9ce30dcda550ddf6e9662cdaf8903a\", \"key\": \"FUK SUMN (Remix)\", \"title\": \"FUK SUMN (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>ua<PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. According to JoyBoy, they still had intentions to drop the remix, but it's unknown if those plans are still going to happen, due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by <PERSON><PERSON><PERSON><PERSON><PERSON> on his Outbreak set on June 28th, 2024.\", \"date\": 17252352, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ef98c2847ce1b633e668e864cf135824\", \"url\": \"https://api.pillowcase.su/api/download/ef98c2847ce1b633e668e864cf135824\", \"size\": \"8.94 MB\", \"duration\": 245.86}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "8.94 MB"}, {"id": "fuk-sumn-175", "name": "FUK SUMN (Remix) [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. According to <PERSON><PERSON><PERSON>, they still had intentions to drop the remix, but it's unknown if those plans are still going to happen, due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFI<PERSON> on his Outbreak set on June 28th, 2024.", "length": "245.4", "fileDate": 17252352, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e9e7700ea1d7d1638c422c88384ec045", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9e7700ea1d7d1638c422c88384ec045\", \"key\": \"FUK SUMN (Remix)\", \"title\": \"FUK SUMN (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>ua<PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. According to JoyBoy, they still had intentions to drop the remix, but it's unknown if those plans are still going to happen, due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by <PERSON><PERSON><PERSON><PERSON><PERSON> on his Outbreak set on June 28th, 2024.\", \"date\": 17252352, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c98b2c7e90b26b9c2259942753f6aa22\", \"url\": \"https://api.pillowcase.su/api/download/c98b2c7e90b26b9c2259942753f6aa22\", \"size\": \"8.94 MB\", \"duration\": 245.4}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "8.94 MB"}, {"id": "good", "name": "GOOD (DON'T DIE) [V11]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "​will.i.am", "<PERSON>", "No I.D."], "notes": "Played at the Chicago listening party. Similar to the release version, but without the sample replay.", "length": "214.05", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/afba6ccecaed363a71eece1ae29d91fe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afba6ccecaed363a71eece1ae29d91fe\", \"key\": \"GOOD (DON'T DIE)\", \"title\": \"GOOD (DON'T DIE) [V11]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, \\u200bwill.i.am, <PERSON> & No I.D.)\", \"aliases\": [\"SO GOOD\"], \"description\": \"Played at the Chicago listening party. Similar to the release version, but without the sample replay.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9c55ea89e5f9955e935102ab7672ba79\", \"url\": \"https://api.pillowcase.su/api/download/9c55ea89e5f9955e935102ab7672ba79\", \"size\": \"8.43 MB\", \"duration\": 214.05}", "aliases": ["SO GOOD"], "size": "8.43 MB"}, {"id": "good-177", "name": "GOOD (DON'T DIE) [V12]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "​will.i.am", "<PERSON>", "No I.D."], "notes": "OG Filename: Good (Don't Die) <PERSON>ey Feb8th_MST\nOG file for the released version, leaked after a groupbuy. Removed on all streaming services, due to sample clearance issues.", "length": "199.44", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/28938d29b4065e58626b4bd5799c8cd4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/28938d29b4065e58626b4bd5799c8cd4\", \"key\": \"GOOD (DON'T DIE)\", \"title\": \"GOOD (DON'T DIE) [V12]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, \\u200bwill.i.am, <PERSON> & No I.D.)\", \"aliases\": [\"SO GOOD\"], \"description\": \"OG Filename: Good (Don't Die) <PERSON>ey Feb8th_MST\\nOG file for the released version, leaked after a groupbuy. Removed on all streaming services, due to sample clearance issues.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4e6975bce102d18c6bcc194bb36e8d65\", \"url\": \"https://api.pillowcase.su/api/download/4e6975bce102d18c6bcc194bb36e8d65\", \"size\": \"8.2 MB\", \"duration\": 199.44}", "aliases": ["SO GOOD"], "size": "8.2 MB"}, {"id": "gun-to-my-head-178", "name": "GUN TO MY HEAD [V5]", "artists": ["<PERSON>"], "producers": ["London on da Track", "Wheezy", "<PERSON>"], "notes": "Version with <PERSON><PERSON><PERSON> and <PERSON> on da <PERSON> production. This version is a stem bounce. Has more vocals from <PERSON><PERSON>. Stems are missing \"Track 5\", It's unknown if this layer went unused, but it most likely was.", "length": "368.01", "fileDate": 17223840, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f0092e0e759a7e1bd29826c7bf47dfb3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0092e0e759a7e1bd29826c7bf47dfb3\", \"key\": \"GUN TO MY HEAD\", \"title\": \"GUN TO MY HEAD [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> on da <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Version with <PERSON><PERSON><PERSON> and <PERSON> on da Track production. This version is a stem bounce. Has more vocals from <PERSON><PERSON>. Stems are missing \\\"Track 5\\\", It's unknown if this layer went unused, but it most likely was.\", \"date\": 17223840, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f7aac0364dff7bf5ebc444eb5c29eb5f\", \"url\": \"https://api.pillowcase.su/api/download/f7aac0364dff7bf5ebc444eb5c29eb5f\", \"size\": \"10.9 MB\", \"duration\": 368.01}", "aliases": [], "size": "10.9 MB"}, {"id": "hoodrat", "name": "✨ HOODRAT [V10]", "artists": [], "producers": ["Kanye West", "88-<PERSON>", "DJ Camper"], "notes": "<PERSON><PERSON> finished solo version with <PERSON> singing instead of <PERSON>. Has release Ye verses and the OG pitch. Original snippet leaked July 22nd, 2024.", "length": "222.91", "fileDate": 17222112, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/213c0ddc246411f428757186256501ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/213c0ddc246411f428757186256501ff\", \"key\": \"HOODRAT\", \"title\": \"\\u2728 HOODRAT [V10]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, 88-<PERSON> & <PERSON>er)\", \"aliases\": [\"HOOD RAT\"], \"description\": \"<PERSON><PERSON> finished solo version with <PERSON> singing instead of <PERSON>. Has release Ye verses and the OG pitch. Original snippet leaked July 22nd, 2024.\", \"date\": 17222112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2763514bc23784b7a64a1d912de814c8\", \"url\": \"https://api.pillowcase.su/api/download/2763514bc23784b7a64a1d912de814c8\", \"size\": \"8.58 MB\", \"duration\": 222.91}", "aliases": ["HOOD RAT"], "size": "8.58 MB"}, {"id": "hoodrat-180", "name": "HOODRAT [V12]", "artists": [], "producers": ["Kanye West", "88-<PERSON>", "DJ Camper"], "notes": "Version played in full at the Chicago VULTURES listening party. Contains more Ye vocals on the first verse. Instrumental snippet originally leaked February 4th, 2024.", "length": "225.93", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3bd75893d1e17f84ee65a619c6d7c599", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3bd75893d1e17f84ee65a619c6d7c599\", \"key\": \"HOODRAT\", \"title\": \"HOODRAT [V12]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, 88-<PERSON> & <PERSON>)\", \"aliases\": [\"HOOD RAT\"], \"description\": \"Version played in full at the Chicago VULTURES listening party. Contains more Ye vocals on the first verse. Instrumental snippet originally leaked February 4th, 2024.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"682b991c7c7b570b093bc781fbfa8bef\", \"url\": \"https://api.pillowcase.su/api/download/682b991c7c7b570b093bc781fbfa8bef\", \"size\": \"8.62 MB\", \"duration\": 225.93}", "aliases": ["HOOD RAT"], "size": "8.62 MB"}, {"id": "hoodrat-181", "name": "HOODRAT [V20]", "artists": [], "producers": ["Kanye West", "88-<PERSON>", "DJ Camper"], "notes": "OG Filename: HOODRAT M8 Flatten DECLICK  MT001-T<PERSON><PERSON> MASTER-24BIT-48kHz &\nHOODRAT M8 MT001\nPost-release mix, leaked after a groupbuy. Has better mixing.", "length": "222.98", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/ae9e84eff20311aab18de84b6b4df7c9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae9e84eff20311aab18de84b6b4df7c9\", \"key\": \"HOODRAT\", \"title\": \"HOODRAT [V20]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, 88-<PERSON> & <PERSON>)\", \"aliases\": [\"HOOD RAT\"], \"description\": \"OG Filename: HOODRAT M8 Flatten DECLICK  MT001-TUCCI MASTER-24BIT-48kHz &\\nHOODRAT M8 MT001\\nPost-release mix, leaked after a groupbuy. Has better mixing.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0fc64d29d70d273e19993b2f10729d58\", \"url\": \"https://api.pillowcase.su/api/download/0fc64d29d70d273e19993b2f10729d58\", \"size\": \"8.58 MB\", \"duration\": 222.98}", "aliases": ["HOOD RAT"], "size": "8.58 MB"}, {"id": "hoodrat-182", "name": "HOODRAT [V21]", "artists": [], "producers": ["Kanye West", "88-<PERSON>", "DJ Camper", "London On Da Track"], "notes": "OG Filename: HOODRAT M9 FLATTEN LOTT\nDuring an instagram live, London On Da Track was showing off the new mix. Described by Digital Nas to be \"not good\".", "length": "57", "fileDate": 17077824, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/32f5c03ba681327918167c6b7b865df3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/32f5c03ba681327918167c6b7b865df3\", \"key\": \"HOODRAT\", \"title\": \"HOODRAT [V21]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, 88-<PERSON>, DJ Camper & London On Da Track)\", \"aliases\": [\"HOOD RAT\"], \"description\": \"OG Filename: HOODRAT M9 FLATTEN LOTT\\nDuring an instagram live, London On Da Track was showing off the new mix. Described by Digital Nas to be \\\"not good\\\".\", \"date\": 17077824, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"407f97ec29d92b7c1e8551e6cb519fd0\", \"url\": \"https://api.pillowcase.su/api/download/407f97ec29d92b7c1e8551e6cb519fd0\", \"size\": \"5.92 MB\", \"duration\": 57}", "aliases": ["HOOD RAT"], "size": "5.92 MB"}, {"id": "keys-to-life", "name": "KEYS TO LIFE [V13]", "artists": ["India Love"], "producers": ["Timbaland", "<PERSON><PERSON>", "SHDØW", "VEYIS", "Vinnyforgood", "The Legendary Traxster"], "notes": "OG Filename: Keys To Life Traxster v6 YE Long \nVersion similar to what was played during the Chicago listening party. Has alternate production and an alternate sample chop, along with an outro done by India Love.", "length": "184.62", "fileDate": 17073504, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/aec873dcc636903150d150c4c3016acc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aec873dcc636903150d150c4c3016acc\", \"key\": \"KEYS TO LIFE\", \"title\": \"KEYS TO LIFE [V13]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\\u00d8W, VEYIS, Vinnyforgood & The Legendary Traxster)\", \"aliases\": [\"KEYS TO MY LIFE\", \"TIMBO FREESTYLE\"], \"description\": \"OG Filename: Keys To Life Traxster v6 YE Long \\nVersion similar to what was played during the Chicago listening party. Has alternate production and an alternate sample chop, along with an outro done by India Love.\", \"date\": 17073504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"980b5b88e3e7637c7fb31c5b4f22d975\", \"url\": \"https://api.pillowcase.su/api/download/980b5b88e3e7637c7fb31c5b4f22d975\", \"size\": \"7.96 MB\", \"duration\": 184.62}", "aliases": ["KEYS TO MY LIFE", "TIMBO FREESTYLE"], "size": "7.96 MB"}, {"id": "keys-to-life-184", "name": "KEYS TO LIFE [V14]", "artists": ["India Love"], "producers": ["Timbaland", "<PERSON><PERSON>", "SHDØW", "VEYIS", "Vinnyforgood", "The Legendary Traxster"], "notes": "May be one of the 5 previous versions / mixes done by Traxster. Has different production at the second half, apart from that similar to release but more stripped down. Original snippet leaked July 22, 2024.", "length": "187.56", "fileDate": 17222112, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4c4e5977487d33426aed672a73ceb988", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4c4e5977487d33426aed672a73ceb988\", \"key\": \"KEYS TO LIFE\", \"title\": \"KEYS TO LIFE [V14]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\\u00d8W, VEYIS, Vinnyforgood & The Legendary Traxster)\", \"aliases\": [\"KEYS TO MY LIFE\", \"TIMBO FREESTYLE\"], \"description\": \"May be one of the 5 previous versions / mixes done by Traxster. Has different production at the second half, apart from that similar to release but more stripped down. Original snippet leaked July 22, 2024.\", \"date\": 17222112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9c38cc90cbad5b63f606fbae1bd3c12d\", \"url\": \"https://api.pillowcase.su/api/download/9c38cc90cbad5b63f606fbae1bd3c12d\", \"size\": \"8.01 MB\", \"duration\": 187.56}", "aliases": ["KEYS TO MY LIFE", "TIMBO FREESTYLE"], "size": "8.01 MB"}, {"id": "keys-to-my-life", "name": "KEYS TO MY LIFE [V15]", "artists": ["India Love"], "producers": ["Kanye West", "Timbaland", "<PERSON><PERSON>", "SHDØW", "Vinnyforgood", "VEYIS", "The Legendary Traxster"], "notes": "Version played at the Chicago listening party. Seemingly the same as release, but with an alternate mix.", "length": "178.39", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/16f64668bcceeda4c97123eb193c31c4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16f64668bcceeda4c97123eb193c31c4\", \"key\": \"KEYS TO MY LIFE\", \"title\": \"KEYS TO MY LIFE [V15]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>bal<PERSON>, <PERSON><PERSON>, <PERSON>D\\u00d8W, Vinnyforgood, VEYIS & The Legendary Traxster)\", \"aliases\": [\"TIMBO FREESTYLE\"], \"description\": \"Version played at the Chicago listening party. Seemingly the same as release, but with an alternate mix.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b3ccb9b45f08be321cad015cb1a5d960\", \"url\": \"https://api.pillowcase.su/api/download/b3ccb9b45f08be321cad015cb1a5d960\", \"size\": \"7.86 MB\", \"duration\": 178.39}", "aliases": ["TIMBO FREESTYLE"], "size": "7.86 MB"}, {"id": "keys-to-my-life-186", "name": "KEYS TO MY LIFE [V16]", "artists": ["India Love"], "producers": ["Kanye West", "Timbaland", "<PERSON><PERSON>", "SHDØW", "Vinnyforgood", "VEYIS", "The Legendary Traxster"], "notes": "OG Filename: KEYS TO MY LIFE\nAlternate mix, leaked after a groupbuy.", "length": "174.08", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/45c70c9340f3987ed18e63134d2aa548", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/45c70c9340f3987ed18e63134d2aa548\", \"key\": \"KEYS TO MY LIFE\", \"title\": \"KEYS TO MY LIFE [V16]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\\u00d8W, Vinnyforgood, VEYIS & The Legendary Traxster)\", \"aliases\": [\"TIMBO FREESTYLE\"], \"description\": \"OG Filename: KEYS TO MY LIFE\\nAlternate mix, leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3722b4fc8068e17b7ec8f5f2c9733115\", \"url\": \"https://api.pillowcase.su/api/download/3722b4fc8068e17b7ec8f5f2c9733115\", \"size\": \"7.79 MB\", \"duration\": 174.08}", "aliases": ["TIMBO FREESTYLE"], "size": "7.79 MB"}, {"id": "king-187", "name": "KING [V13]", "artists": [], "producers": ["Kanye West", "<PERSON>", "Wheezy", "<PERSON><PERSON>", "88-<PERSON>", "JPEGMAFIA"], "notes": "Played at the Chicago listening party. <PERSON><PERSON> finished song about <PERSON> overcoming his controversies. <PERSON> was cut short due to non-sporting curfew rules in Chicago, so we don't have a full HQ rip, but there is a full recording linked alongside the partial version.", "length": "92.19", "fileDate": 17073504, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6bf7737d4ea8fb06ba2f55e4c6507384", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6bf7737d4ea8fb06ba2f55e4c6507384\", \"key\": \"KING\", \"title\": \"KING [V13]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 88-<PERSON> & <PERSON>EGMAFIA)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"Played at the Chicago listening party. <PERSON><PERSON> finished song about <PERSON> overcoming his controversies. Stream was cut short due to non-sporting curfew rules in Chicago, so we don't have a full HQ rip, but there is a full recording linked alongside the partial version.\", \"date\": 17073504, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a853232e29df7c3a6ecbfe4a1298fe69\", \"url\": \"https://api.pillowcase.su/api/download/a853232e29df7c3a6ecbfe4a1298fe69\", \"size\": \"6.48 MB\", \"duration\": 92.19}", "aliases": ["STILL THE KING"], "size": "6.48 MB"}, {"id": "king-188", "name": "KING [V13]", "artists": [], "producers": ["Kanye West", "<PERSON>", "Wheezy", "<PERSON><PERSON>", "88-<PERSON>", "JPEGMAFIA"], "notes": "Played at the Chicago listening party. <PERSON><PERSON> finished song about <PERSON> overcoming his controversies. <PERSON> was cut short due to non-sporting curfew rules in Chicago, so we don't have a full HQ rip, but there is a full recording linked alongside the partial version.", "length": "157.99", "fileDate": 17073504, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a16b26a93f261c28c0edffdacacc520d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a16b26a93f261c28c0edffdacacc520d\", \"key\": \"KING\", \"title\": \"KING [V13]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 88-<PERSON> & JPEGMAFIA)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"Played at the Chicago listening party. <PERSON><PERSON> finished song about <PERSON> overcoming his controversies. Stream was cut short due to non-sporting curfew rules in Chicago, so we don't have a full HQ rip, but there is a full recording linked alongside the partial version.\", \"date\": 17073504, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6da4694e9285436f460c8470f350817d\", \"url\": \"https://api.pillowcase.su/api/download/6da4694e9285436f460c8470f350817d\", \"size\": \"7.54 MB\", \"duration\": 157.99}", "aliases": ["STILL THE KING"], "size": "7.54 MB"}, {"id": "king-189", "name": "KING [V16]", "artists": [], "producers": ["Kanye West", "<PERSON>", "Wheezy", "<PERSON><PERSON>", "88-<PERSON>", "JPEGMAFIA"], "notes": "OG Filename: KING M3 Flatten  MT001-TUCCI MASTER-24BIT-48kHz\nPost-release mix, leaked after a groupbuy. Has better mixing.", "length": "156.86", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/681c13e71dfa4e0e13dfdd94e342b91e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/681c13e71dfa4e0e13dfdd94e342b91e\", \"key\": \"KING\", \"title\": \"KING [V16]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 88-<PERSON> & <PERSON>EGMAFIA)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"OG Filename: KING M3 Flatten  MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release mix, leaked after a groupbuy. Has better mixing.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1b028f9f02c4ee9d730dc9da80cf6642\", \"url\": \"https://api.pillowcase.su/api/download/1b028f9f02c4ee9d730dc9da80cf6642\", \"size\": \"7.52 MB\", \"duration\": 156.86}", "aliases": ["STILL THE KING"], "size": "7.52 MB"}, {"id": "king-190", "name": "KING [V17]", "artists": [], "producers": ["Kanye West", "<PERSON>", "Wheezy", "<PERSON><PERSON>", "88-<PERSON>", "JPEGMAFIA"], "notes": "OG Filename: KING M3 Flatten NO OZZY MT001-T<PERSON>CI MASTER-24BIT-48kHz\nPost-release mix, leaked after a groupbuy. Similar to above but has the <PERSON><PERSON> sample removed and has some slight difference with vocal volume/delay compared to the above version.", "length": "156.86", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/113a40d75e6e9d41ab1a3e0a72de6757", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/113a40d75e6e9d41ab1a3e0a72de6757\", \"key\": \"KING\", \"title\": \"KING [V17]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 88-<PERSON> & JPEGMAFIA)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"OG Filename: KING M3 Flatten NO OZZY MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release mix, leaked after a groupbuy. Similar to above but has the <PERSON><PERSON> sample removed and has some slight difference with vocal volume/delay compared to the above version.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5f49c6c101a219f2e219ef16bb6f8745\", \"url\": \"https://api.pillowcase.su/api/download/5f49c6c101a219f2e219ef16bb6f8745\", \"size\": \"7.52 MB\", \"duration\": 156.86}", "aliases": ["STILL THE KING"], "size": "7.52 MB"}, {"id": "king-191", "name": "KING [V19]", "artists": [], "producers": ["Kanye West", "<PERSON>", "Wheezy", "<PERSON><PERSON>", "88-<PERSON>", "JPEGMAFIA"], "notes": "OG Filename: King M5 SAMPLE REPLAY\nPost-release mix, leaked after a groupbuy. Has better mixing.", "length": "157.25", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/aa32c8bab2081f7ac696ec44e12cda79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aa32c8bab2081f7ac696ec44e12cda79\", \"key\": \"KING\", \"title\": \"KING [V19]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 88-<PERSON> & JPEGMAFIA)\", \"aliases\": [\"STILL THE KING\"], \"description\": \"OG Filename: King M5 SAMPLE REPLAY\\nPost-release mix, leaked after a groupbuy. Has better mixing.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"271c4b9e0fdcc04e5f2cea3e5a474dc6\", \"url\": \"https://api.pillowcase.su/api/download/271c4b9e0fdcc04e5f2cea3e5a474dc6\", \"size\": \"7.53 MB\", \"duration\": 157.25}", "aliases": ["STILL THE KING"], "size": "7.53 MB"}, {"id": "lifestyle-192", "name": "LIFESTYLE [V4]", "artists": [], "producers": ["<PERSON>", "FnZ", "London on da Track", "SHDØW"], "notes": "Has production from SHDØW and no <PERSON>. This version is a stem bounce.", "length": "316.96", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/725bd5685b887f5f550807ea5ccaa5c9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/725bd5685b887f5f550807ea5ccaa5c9\", \"key\": \"LIFESTYLE\", \"title\": \"LIFESTYLE [V4]\", \"artists\": \"(prod. <PERSON>, FnZ, London on da Track & SHD\\u00d8W)\", \"description\": \"Has production from SHD\\u00d8W and no <PERSON> Wayne. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0f0b598da741fa27c16e36c7b5115bd1\", \"url\": \"https://api.pillowcase.su/api/download/0f0b598da741fa27c16e36c7b5115bd1\", \"size\": \"10.1 MB\", \"duration\": 316.96}", "aliases": [], "size": "10.1 MB"}, {"id": "lifestyle-193", "name": "⭐ LIFESTYLE [V5]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "FnZ", "London on da Track", "Wheezy"], "notes": "OG version before it was changed for VULTURES 2. Has added production from Wheezy & London On Da Track. Leaked during a mass VULTURES 1 song leak. This version is a stem bounce. Has an open verse, that is most likely meant for <PERSON>.", "length": "329.19", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1100ee5b88c36260c0c3716d414e55e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1100ee5b88c36260c0c3716d414e55e3\", \"key\": \"LIFESTYLE\", \"title\": \"\\u2b50 LIFESTYLE [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, FnZ, London on da Track & Wheezy)\", \"description\": \"OG version before it was changed for VULTURES 2. Has added production from Wheezy & London On Da Track. Leaked during a mass VULTURES 1 song leak. This version is a stem bounce. Has an open verse, that is most likely meant for <PERSON>.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"22b505eb29cfb2e2e050a2aa0b13d149\", \"url\": \"https://api.pillowcase.su/api/download/22b505eb29cfb2e2e050a2aa0b13d149\", \"size\": \"10.3 MB\", \"duration\": 329.19}", "aliases": [], "size": "10.3 MB"}, {"id": "make-it-feel-right-194", "name": "MAKE IT FEEL RIGHT [V2]", "artists": [], "producers": [], "notes": "Early VULTURES-era song. The version linked is a stem bounce. Has vocals from <PERSON> near the end but none from <PERSON>.", "length": "224", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/bbaf85977e860815e449b2c31a5eb80b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbaf85977e860815e449b2c31a5eb80b\", \"key\": \"MAKE IT FEEL RIGHT\", \"title\": \"MAKE IT FEEL RIGHT [V2]\", \"aliases\": [\"NEXT TIME\"], \"description\": \"Early VULTURES-era song. The version linked is a stem bounce. Has vocals from <PERSON> near the end but none from <PERSON><PERSON>\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"32ec5221fbfabb4683ae6186ca449fb5\", \"url\": \"https://api.pillowcase.su/api/download/32ec5221fbfabb4683ae6186ca449fb5\", \"size\": \"8.59 MB\", \"duration\": 224}", "aliases": ["NEXT TIME"], "size": "8.59 MB"}, {"id": "matthew-195", "name": "✨ MATTHEW [V6]", "artists": ["North West", "<PERSON>"], "producers": ["88-<PERSON>", "London on da Track"], "notes": "OG Filename: Matthew_v1.0\nVersion with updated production done by London on da Track. Features new piano production. London was not given the stems, so AI stem extraction was used to create this version. Original snippet leaked March 9th, 2024.", "length": "164.52", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/c4633e44e68eba392d146d0487cee633", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4633e44e68eba392d146d0487cee633\", \"key\": \"MATTHEW\", \"title\": \"\\u2728 MATTHEW [V6]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 88-Keys & London on da Track)\", \"description\": \"OG Filename: Matthew_v1.0\\nVersion with updated production done by London on da Track. Features new piano production. London was not given the stems, so AI stem extraction was used to create this version. Original snippet leaked March 9th, 2024.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c520aef45d519ec3f35c75ba020af21\", \"url\": \"https://api.pillowcase.su/api/download/5c520aef45d519ec3f35c75ba020af21\", \"size\": \"7.64 MB\", \"duration\": 164.52}", "aliases": [], "size": "7.64 MB"}, {"id": "paid-196", "name": "PAID [V13]", "artists": ["K-Ci"], "producers": ["Kanye West", "<PERSON><PERSON>v", "Wax Motif", "<PERSON><PERSON>", "KAYTRANADA", "The Legendary Traxster", "<PERSON>", "JPEGMAFIA"], "notes": "Version played by JPEGMAFIA at the Pitchfork Music Festival in Mexico. Has alternate production.", "length": "215.45", "fileDate": 17097696, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/73dd437b5907e3bb31e7238cf3a5cec6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/73dd437b5907e3bb31e7238cf3a5cec6\", \"key\": \"PAID\", \"title\": \"PAID [V13]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>AN<PERSON><PERSON>, <PERSON> Legendary <PERSON>, <PERSON> & JPEGMAFIA)\", \"description\": \"Version played by JPEGMAFIA at the Pitchfork Music Festival in Mexico. Has alternate production.\", \"date\": 17097696, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f62f676b16bc1afc57e31515ea1a3da5\", \"url\": \"https://api.pillowcase.su/api/download/f62f676b16bc1afc57e31515ea1a3da5\", \"size\": \"8.46 MB\", \"duration\": 215.45}", "aliases": [], "size": "8.46 MB"}, {"id": "paid-197", "name": "PAID [V14]", "artists": ["K-Ci"], "producers": ["Kanye West", "<PERSON><PERSON>v", "Wax Motif", "<PERSON><PERSON>", "KAYTRANADA", "The Legendary Traxster", "<PERSON>"], "notes": "Played by <PERSON> at an event he was at. Has a different chop of <PERSON>'s verse.", "length": "26.09", "fileDate": 17057088, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/9fdb8b92c875eeba78c654f9a6daade9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9fdb8b92c875eeba78c654f9a6daade9\", \"key\": \"PAID\", \"title\": \"PAID [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, The Legendary Traxster & Anthony <PERSON>)\", \"description\": \"Played by <PERSON> at an event he was at. Has a different chop of <PERSON>'s verse.\", \"date\": 17057088, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"60e9b0c94c40c940642280d7b90c3aa4\", \"url\": \"https://api.pillowcase.su/api/download/60e9b0c94c40c940642280d7b90c3aa4\", \"size\": \"5.43 MB\", \"duration\": 26.09}", "aliases": [], "size": "5.43 MB"}, {"id": "paid-198", "name": "PAID [V15]", "artists": ["K-Ci"], "producers": ["Kanye West", "<PERSON><PERSON>v", "Wax Motif", "<PERSON><PERSON>", "KAYTRANADA", "The Legendary Traxster", "<PERSON>"], "notes": "Version played at the Chicago listening party. Has a different structure, re-recorded K-Ci vocals and a more finished Ye verse.", "length": "194.74", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8a2c09e3c3f816fc878a34472650045a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8a2c09e3c3f816fc878a34472650045a\", \"key\": \"PAID\", \"title\": \"PAID [V15]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, The Legendary Traxster & Anthony <PERSON>)\", \"description\": \"Version played at the Chicago listening party. Has a different structure, re-recorded K-Ci vocals and a more finished Ye verse.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e421805cd94a4cdb019f47a6a17a9871\", \"url\": \"https://api.pillowcase.su/api/download/e421805cd94a4cdb019f47a6a17a9871\", \"size\": \"8.13 MB\", \"duration\": 194.74}", "aliases": [], "size": "8.13 MB"}, {"id": "paid-199", "name": "PAID [V33]", "artists": ["K-Ci"], "producers": ["Kanye West", "<PERSON><PERSON>v", "Wax Motif", "<PERSON><PERSON>", "KAYTRANADA", "The Legendary Traxster", "<PERSON>"], "notes": "OG Filename: Paid M17 K 120MT &\nOG Filename: PAID M17 K Master USE Flatten 120MT  MT001-TUCCI MASTER-24BIT-48kHz\nPost-release mix, leaked after a groupbuy. Features different drums.", "length": "195.12", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/e1ff39ff692da1df699fcfed9d4d2beb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e1ff39ff692da1df699fcfed9d4d2beb\", \"key\": \"PAID\", \"title\": \"PAID [V33]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ANADA, The Legendary Traxster & Anthony <PERSON>)\", \"description\": \"OG Filename: Paid M17 K 120MT &\\nOG Filename: PAID M17 K Master USE Flatten 120MT  MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release mix, leaked after a groupbuy. Features different drums.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3283cdc9d2df8b4f5488fc59c6d4cc05\", \"url\": \"https://api.pillowcase.su/api/download/3283cdc9d2df8b4f5488fc59c6d4cc05\", \"size\": \"8.13 MB\", \"duration\": 195.12}", "aliases": [], "size": "8.13 MB"}, {"id": "paperwork-200", "name": "PAPERWORK [V12]", "artists": ["Quavo"], "producers": ["Kanye West", "Digital Nas", "The Legendary Traxster", "88-<PERSON>", "Swizz Beatz"], "notes": "Played at the Chicago listening party for VULTURES 1. Has a different beat, a Ye verse and samples \"FAZ O MACETE 3.0\" - DJ <PERSON><PERSON><PERSON><PERSON> e DJ <PERSON>.", "length": "156.11", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/d100b3d42496aec59435e7cdb4effedf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d100b3d42496aec59435e7cdb4effedf\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V12]\", \"artists\": \"(feat. <PERSON>ua<PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> Nas, The Legendary Traxster, 88-Keys & Swizz Beatz)\", \"description\": \"Played at the Chicago listening party for VULTURES 1. Has a different beat, a Ye verse and samples \\\"FAZ O MACETE 3.0\\\" - <PERSON> <PERSON><PERSON><PERSON><PERSON> e DJ Roca.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8640d1244f935092e673f42fa1ccf9d1\", \"url\": \"https://api.pillowcase.su/api/download/8640d1244f935092e673f42fa1ccf9d1\", \"size\": \"7.51 MB\", \"duration\": 156.11}", "aliases": [], "size": "7.51 MB"}, {"id": "paperwork-201", "name": "PAPERWORK [V20]", "artists": ["Quavo"], "producers": ["Kanye West", "Digital Nas", "The Legendary Traxster", "88-<PERSON>", "Swizz Beatz"], "notes": "OG Filename: Paperwork M8 24 bit\nAlternate mix, leaked after a groupbuy.", "length": "145.79", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/252344da8d9efdc0315968dd34407974", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/252344da8d9efdc0315968dd34407974\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V20]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> Nas, The Legendary Traxster, 88-Keys & Swizz Beatz)\", \"description\": \"OG Filename: Paperwork M8 24 bit\\nAlternate mix, leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8a30c88f7ef8be7c2685bcefcf0d25d2\", \"url\": \"https://api.pillowcase.su/api/download/8a30c88f7ef8be7c2685bcefcf0d25d2\", \"size\": \"7.34 MB\", \"duration\": 145.79}", "aliases": [], "size": "7.34 MB"}, {"id": "pray", "name": "PRAY [V1]", "artists": [], "producers": ["Hitma<PERSON>", "Camper"], "notes": "Song that was later released by Cordae, this version has a Ty verse not used in the release version and a Hitmaka tag that was removed. Played in a Wiz Khalifa vlog. Unrelated to the other \"PRAY\" of the same name produced by TryBishop.", "length": "48.12", "fileDate": 17051040, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/6c8a9ca88d65702c9e67b192f8d6eb7b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6c8a9ca88d65702c9e67b192f8d6eb7b\", \"key\": \"PRAY\", \"title\": \"PRAY [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & Camper)\", \"description\": \"Song that was later released by Cordae, this version has a Ty verse not used in the release version and a Hitmaka tag that was removed. Played in a Wiz Khalifa vlog. Unrelated to the other \\\"PRAY\\\" of the same name produced by TryBishop.\", \"date\": 17051040, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d469984ce24b4e860c7987dc9548dd04\", \"url\": \"https://api.pillowcase.su/api/download/d469984ce24b4e860c7987dc9548dd04\", \"size\": \"5.78 MB\", \"duration\": 48.12}", "aliases": [], "size": "5.78 MB"}, {"id": "problematic", "name": "PROBLEMATIC [V8]", "artists": [], "producers": ["Kanye West", "88-<PERSON>", "<PERSON>", "Camper", "Slonka", "Ty Dolla $ign"], "notes": "Played at the Chicago listening party. Track nicknamed as \"Leader Of The Vatican\", until shown to be named \"Problematic\". Doesn't have the low quality release line 'Throw your motherfucking hands' and instead <PERSON> says 'I do it for my fam'.", "length": "194.74", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a7dca006f827c30285408fe1731fd7eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7dca006f827c30285408fe1731fd7eb\", \"key\": \"PROBLEMATIC\", \"title\": \"PROBLEMATIC [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, 88-<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & Ty <PERSON> $ign)\", \"aliases\": [\"Ceremony Freestyle\"], \"description\": \"Played at the Chicago listening party. Track nicknamed as \\\"Leader Of The Vatican\\\", until shown to be named \\\"Problematic\\\". Doesn't have the low quality release line 'Throw your motherfucking hands' and instead <PERSON> says 'I do it for my fam'.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d7ecf1abdc6b36eef46e4f4c80ab8ea0\", \"url\": \"https://api.pillowcase.su/api/download/d7ecf1abdc6b36eef46e4f4c80ab8ea0\", \"size\": \"8.13 MB\", \"duration\": 194.74}", "aliases": ["Ceremony Freestyle"], "size": "8.13 MB"}, {"id": "problematic-204", "name": "PROBLEMATIC [V14]", "artists": [], "producers": ["Kanye West", "88-<PERSON>", "<PERSON>", "Camper", "Slonka", "Ty Dolla $ign"], "notes": "OG Filename: Problematic M6\nAlternate mix, leaked after a groupbuy.", "length": "194.71", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/3f2626787dd425ba5eb2cb9c91e7dc4c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f2626787dd425ba5eb2cb9c91e7dc4c\", \"key\": \"PROBLEMATIC\", \"title\": \"PROBLEMATIC [V14]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, 88-<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & Ty <PERSON> $ign)\", \"aliases\": [\"Ceremony Freestyle\"], \"description\": \"OG Filename: Problematic M6\\nAlternate mix, leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c0d3b9dd69605aadeec76f6139bfaf6d\", \"url\": \"https://api.pillowcase.su/api/download/c0d3b9dd69605aadeec76f6139bfaf6d\", \"size\": \"8.13 MB\", \"duration\": 194.71}", "aliases": ["Ceremony Freestyle"], "size": "8.13 MB"}, {"id": "river-205", "name": "RIVER [V18]", "artists": ["<PERSON> Thug"], "producers": ["Digital Nas", "London on da Track"], "notes": "OG Filename: RIVER V2A\nLondon on da Track made three other versions of \"River\". Instrumental with additional production. This version is a stem bounce.", "length": "215.21", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/69f252cd2fa9bec19bc6de5a898950eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/69f252cd2fa9bec19bc6de5a898950eb\", \"key\": \"RIVER\", \"title\": \"RIVER [V18]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. Digital Nas & London on da Track)\", \"description\": \"OG Filename: RIVER V2A\\nLondon on da Track made three other versions of \\\"River\\\". Instrumental with additional production. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"298324628e50297cd5fbeb5016d827a5\", \"url\": \"https://api.pillowcase.su/api/download/298324628e50297cd5fbeb5016d827a5\", \"size\": \"8.45 MB\", \"duration\": 215.21}", "aliases": [], "size": "8.45 MB"}, {"id": "river-206", "name": "RIVER [V19]", "artists": ["<PERSON> Thug"], "producers": ["Digital Nas", "London on da Track"], "notes": "OG Filename: RIVER V3A 132BBPM\nLondon on da Track made three other versions of \"River\". Instrumental with additional production. This version is a stem bounce.", "length": "215.21", "fileDate": 17223840, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/4edbf3e26e41f9ddeae8597ecd5d08c6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4edbf3e26e41f9ddeae8597ecd5d08c6\", \"key\": \"RIVER\", \"title\": \"RIVER [V19]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. Digital Nas & London on da Track)\", \"description\": \"OG Filename: RIVER V3A 132BBPM\\nLondon on da Track made three other versions of \\\"River\\\". Instrumental with additional production. This version is a stem bounce.\", \"date\": 17223840, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4b205c0f2033f0207b4f11ffbe3f147e\", \"url\": \"https://api.pillowcase.su/api/download/4b205c0f2033f0207b4f11ffbe3f147e\", \"size\": \"8.45 MB\", \"duration\": 215.21}", "aliases": [], "size": "8.45 MB"}, {"id": "river-207", "name": "RIVER [V20]", "artists": ["<PERSON> Thug"], "producers": ["AyoAA", "Digital Nas", "London on da Track"], "notes": "Version with alternate production. Snippet leaked November 2nd, 2024.", "length": "36.68", "fileDate": 17305056, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/8a1c281e4bd3b0e490ec62e75d58a939", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8a1c281e4bd3b0e490ec62e75d58a939\", \"key\": \"RIVER\", \"title\": \"RIVER [V20]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. <PERSON><PERSON>, Digital Nas & London on da Track)\", \"description\": \"Version with alternate production. Snippet leaked November 2nd, 2024.\", \"date\": 17305056, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e62c44d4ae033f626c631038e8e8dfcf\", \"url\": \"https://api.pillowcase.su/api/download/e62c44d4ae033f626c631038e8e8dfcf\", \"size\": \"1.04 MB\", \"duration\": 36.68}", "aliases": [], "size": "1.04 MB"}, {"id": "stars", "name": "STARS [V9]", "artists": [], "producers": ["FNZ", "Ojivolta", "SHDØW"], "notes": "OG Filename: 026 - STAR<PERSON> 124 bpm (ye)\nEarly version of \"Stars\" with the same sample from \"Use This Gospel\" throughout the song. Has different drums and structure differences compared to Italy leak, also unused production compared to release. Original snippet leaked July 22, 2024.", "length": "135.58", "fileDate": 17222112, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/17239cd8a32dd7e5cb93ea0c682ff937", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/17239cd8a32dd7e5cb93ea0c682ff937\", \"key\": \"STARS\", \"title\": \"STARS [V9]\", \"artists\": \"(prod. FNZ, Ojivolta & SHD\\u00d8W)\", \"aliases\": [\"Up In The Stars\"], \"description\": \"OG Filename: 026 - STARS 124 bpm (ye)\\nEarly version of \\\"Stars\\\" with the same sample from \\\"Use This Gospel\\\" throughout the song. Has different drums and structure differences compared to Italy leak, also unused production compared to release. Original snippet leaked July 22, 2024.\", \"date\": 17222112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e72cb025cb1b09cc2511582af9fdd273\", \"url\": \"https://api.pillowcase.su/api/download/e72cb025cb1b09cc2511582af9fdd273\", \"size\": \"7.18 MB\", \"duration\": 135.58}", "aliases": ["Up In The Stars"], "size": "7.18 MB"}, {"id": "stars-209", "name": "STARS [V10]", "artists": [], "producers": ["FNZ", "Ojivolta", "JPEGMAFIA"], "notes": "Version played at the Chicago listening party.", "length": "114.49", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/a439996710fa5d51aecbd56d62bbbbf1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a439996710fa5d51aecbd56d62bbbbf1\", \"key\": \"STARS\", \"title\": \"STARS [V10]\", \"artists\": \"(prod. FNZ, Ojivolta & JPEGMAFIA)\", \"aliases\": [\"Up In The Stars\"], \"description\": \"Version played at the Chicago listening party.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"189e55ac9728a13cfe1c98ed03cfa407\", \"url\": \"https://api.pillowcase.su/api/download/189e55ac9728a13cfe1c98ed03cfa407\", \"size\": \"6.84 MB\", \"duration\": 114.49}", "aliases": ["Up In The Stars"], "size": "6.84 MB"}, {"id": "stars-210", "name": "STARS [V11]", "artists": ["The Hooligans"], "producers": ["Kanye West", "FNZ", "SHDØW", "Digital Nas", "JPEGMAFIA", "DJ Camper", "The Legendary Traxster", "Ojivolta"], "notes": "OG Filename: Stars\nAlternate mix. Leaked after a groupbuy.", "length": "115.24", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/224a90907fe46ee67263b6853b658ebd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/224a90907fe46ee67263b6853b658ebd\", \"key\": \"STARS\", \"title\": \"STARS [V11]\", \"artists\": \"(feat. The Hooligans) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>\\u00d8W, Digital Nas, JPEGMAFIA, <PERSON>, The Legendary Traxster & Ojivolta)\", \"description\": \"OG Filename: Stars\\nAlternate mix. Leaked after a groupbuy.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2c04c82ae64f616c43dbcead04d216e6\", \"url\": \"https://api.pillowcase.su/api/download/2c04c82ae64f616c43dbcead04d216e6\", \"size\": \"6.85 MB\", \"duration\": 115.24}", "aliases": [], "size": "6.85 MB"}, {"id": "talking-once-again-211", "name": "TALKING / ONCE AGAIN [V12]", "artists": ["North West", "<PERSON>"], "producers": ["<PERSON>", "No I.D."], "notes": "Version played on <PERSON> <PERSON><PERSON><PERSON>' radio show prior to the supposed intended release. Has slight differences to the release version such as a longer version of the \"Once Again\" part of the track (no different vocals though, just the same parts looped).", "length": "233.38", "fileDate": 17037216, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/9c4db1b99eb3d6f9fb206316a872b237", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c4db1b99eb3d6f9fb206316a872b237\", \"key\": \"TALKING / ONCE AGAIN\", \"title\": \"TALKING / ONCE AGAIN [V12]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING\"], \"description\": \"Version played on <PERSON>' radio show prior to the supposed intended release. Has slight differences to the release version such as a longer version of the \\\"Once Again\\\" part of the track (no different vocals though, just the same parts looped).\", \"date\": 17037216, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f954c495ea1d28c807841f8c42caf62c\", \"url\": \"https://api.pillowcase.su/api/download/f954c495ea1d28c807841f8c42caf62c\", \"size\": \"8.74 MB\", \"duration\": 233.38}", "aliases": ["TALKING"], "size": "8.74 MB"}, {"id": "talking-212", "name": "TALKING [V21]", "artists": ["North West", "<PERSON>"], "producers": ["Kanye West", "DJ Camper", "<PERSON>", "Edsclusive", "Swizz Beatz", "No I.D."], "notes": "OG Filename: TALK<PERSON> EXPLICIT M9 TY ADJUST MT001 declick &\nTALKING\nAlternate mix.", "length": "185.11", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/7e6d7bbc1ee3445f9183142368b4b151", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e6d7bbc1ee3445f9183142368b4b151\", \"key\": \"TALKING\", \"title\": \"TALKING [V21]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Swizz Beatz & No I.D.)\", \"aliases\": [\"TALKING / ONCE AGAIN\"], \"description\": \"OG Filename: TALKING EXPLICIT M9 TY ADJUST MT001 declick &\\nTALKING\\nAlternate mix.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5268783034833e685b9c6def0114c89d\", \"url\": \"https://api.pillowcase.su/api/download/5268783034833e685b9c6def0114c89d\", \"size\": \"7.97 MB\", \"duration\": 185.11}", "aliases": ["TALKING / ONCE AGAIN"], "size": "7.97 MB"}, {"id": "vultures-213", "name": "VULTURES [V9]", "artists": [], "producers": ["???"], "notes": "Version with different drums. These may be the KAYTRANADA drums, but has not been confirmed yet.", "length": "15.15", "fileDate": 17216064, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/f5948b39768398bc515696000066807c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f5948b39768398bc515696000066807c\", \"key\": \"VULTURES\", \"title\": \"VULTURES [V9]\", \"artists\": \"(prod. ???)\", \"description\": \"Version with different drums. These may be the KAYTRANADA drums, but has not been confirmed yet.\", \"date\": 17216064, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8378ca3d3ff59b1c349278c42f31b96a\", \"url\": \"https://api.pillowcase.su/api/download/8378ca3d3ff59b1c349278c42f31b96a\", \"size\": \"5.25 MB\", \"duration\": 15.15}", "aliases": [], "size": "5.25 MB"}, {"id": "vultures-214", "name": "VULTURES [V17]", "artists": ["Bump J", "<PERSON>"], "producers": ["Kanye West", "Ty Dolla $ign", "Ambezza", "<PERSON><PERSON>", "Ojivolta", "Chordz"], "notes": "OG Filename: VULTURES_FM6_MAIN1.1_MT001 &\nVULTURES FM6 MAIN1.1 MT001-TUCCI MASTER-24BIT-441kHz\nAlternate mix.", "length": "276.99", "fileDate": 17291232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/25ccdf5e8c61dae3ad3ad513a8c4ba55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/25ccdf5e8c61dae3ad3ad513a8c4ba55\", \"key\": \"VULTURES\", \"title\": \"VULTURES [V17]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>gn, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ojivolta & Chordz)\", \"description\": \"OG Filename: VULTURES_FM6_MAIN1.1_MT001 &\\nVULTURES FM6 MAIN1.1 MT001-TUCCI MASTER-24BIT-441kHz\\nAlternate mix.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cbef470938167e8b4ba7979bed669eb8\", \"url\": \"https://api.pillowcase.su/api/download/cbef470938167e8b4ba7979bed669eb8\", \"size\": \"9.44 MB\", \"duration\": 276.99}", "aliases": [], "size": "9.44 MB"}, {"id": "havoc-version", "name": "VULTURES - Havoc Version [V1]", "artists": ["<PERSON>", "Bump J"], "producers": ["Havoc", "The Legendary Traxster", "London on da Track", "<PERSON><PERSON> Beats"], "notes": "OG Filename: vulture bass - 73 BPM london x dson 1\nAlternate version with different production, including heavier bass and different drums.", "length": "276.17", "fileDate": 17073504, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/1defc56497cb56f70e6006ec41523067", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1defc56497cb56f70e6006ec41523067\", \"key\": \"Havoc Version\", \"title\": \"VULTURES - Havoc Version [V1]\", \"artists\": \"(feat. <PERSON> & Bump J) (prod. Havoc, The Legendary Traxster, London on da Track & Dson Beats)\", \"description\": \"OG Filename: vulture bass - 73 BPM london x dson 1\\nAlternate version with different production, including heavier bass and different drums.\", \"date\": 17073504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4c237b3ebdf355b9334ed44d57927e1a\", \"url\": \"https://api.pillowcase.su/api/download/4c237b3ebdf355b9334ed44d57927e1a\", \"size\": \"9.43 MB\", \"duration\": 276.17}", "aliases": [], "size": "9.43 MB"}, {"id": "havoc-version-216", "name": "VULTURES - Havoc Version [V2]", "artists": ["<PERSON>", "Bump J"], "producers": ["Havoc", "The Legendary Traxster"], "notes": "Posted by <PERSON> on his Instagram. The drums in the Bump J section are different compared to release and there is a 'perfect!' tag at the end.", "length": "153.55", "fileDate": 17059680, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://pillowcase.su/f/66813cb1045e77209690b7577426d7a3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/66813cb1045e77209690b7577426d7a3\", \"key\": \"Havoc Version\", \"title\": \"VULTURES - Havoc Version [V2]\", \"artists\": \"(feat. <PERSON> & Bump <PERSON>) (prod. Havoc & The Legendary Traxster)\", \"description\": \"Posted by <PERSON> on his Instagram. The drums in the Bump J section are different compared to release and there is a 'perfect!' tag at the end.\", \"date\": 17059680, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ffb0a518a5336d001847e5d70ef2a74a\", \"url\": \"https://api.pillowcase.su/api/download/ffb0a518a5336d001847e5d70ef2a74a\", \"size\": \"7.47 MB\", \"duration\": 153.55}", "aliases": [], "size": "7.47 MB"}, {"id": "havoc-version-217", "name": "VULTURES - Havoc Version [V2]", "artists": ["<PERSON>", "Bump J"], "producers": ["Havoc", "The Legendary Traxster"], "notes": "Posted by <PERSON> on his Instagram. The drums in the Bump J section are different compared to release and there is a 'perfect!' tag at the end.", "length": "", "fileDate": 17059680, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://www.reddit.com/r/GoodAssSub/comments/19e05fu/alt_havoc_vultures_snippet/", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.reddit.com/r/GoodAssSub/comments/19e05fu/alt_havoc_vultures_snippet/\", \"key\": \"Havoc Version\", \"title\": \"VULTURES - Havoc Version [V2]\", \"artists\": \"(feat. <PERSON> Du<PERSON> & Bump J) (prod. Havoc & The Legendary Traxster)\", \"description\": \"Posted by <PERSON> on his Instagram. The drums in the Bump J section are different compared to release and there is a 'perfect!' tag at the end.\", \"date\": 17059680, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "havoc-version-218", "name": "VULTURES - Havoc Version [V2]", "artists": ["<PERSON>", "Bump J"], "producers": ["Havoc", "The Legendary Traxster"], "notes": "Posted by <PERSON> on his Instagram. The drums in the Bump J section are different compared to release and there is a 'perfect!' tag at the end.", "length": "", "fileDate": 17059680, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "vultures-1", "originalUrl": "https://www.reddit.com/r/GoodAssSub/comments/19d4ior/new_vultures_the_song_snippet_with_updated_prod/", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.reddit.com/r/GoodAssSub/comments/19d4ior/new_vultures_the_song_snippet_with_updated_prod/\", \"key\": \"Havoc Version\", \"title\": \"VULTURES - Havoc Version [V2]\", \"artists\": \"(feat. <PERSON> Durk & Bump <PERSON>) (prod. Havoc & The Legendary Traxster)\", \"description\": \"Posted by <PERSON> on his Instagram. The drums in the Bump J section are different compared to release and there is a 'perfect!' tag at the end.\", \"date\": 17059680, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}]}