{"id": "hitler", "name": "<PERSON>", "description": "After <PERSON><PERSON><PERSON> was released from UCLA Medical Hospital and diagnosed as bipolar, he bought a ranch in Wyoming where he would produce his next album and multiple albums for his collaborators. The concept of the album came together in 2018. The album's subject matter varied wildly, with some songs being about introspection and change and others discussing his political views. The public name given for this album is \"LOVE EVERYONE\", but it is known that <PERSON><PERSON><PERSON> likely considered the \"Hitler\" title longer.", "backgroundColor": "rgb(185, 205, 213)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17HAoxg4tVQeCO3Q9E3jLP23M2UmqlStCk6UM7pZ8TFVBcryEOiKbXjdrwt2GQ9Dnbw1YJs4B88z39IEXsNLXeNJmq7GbWxDSLuaUiYrMJq8X-aeEgK8IGcHemTZnl8z8UFcei8i5i_aVuhpxuQ?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "bad-people", "name": "Bad People [V1]", "artists": ["<PERSON>"], "producers": ["Kanye West"], "notes": "Kanye mumble version, shown in 500 Days In UCLA.", "length": "108.17", "fileDate": 16955136, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a8e5e17811ab4a3bbe67b5a137d637d9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a8e5e17811ab4a3bbe67b5a137d637d9\", \"key\": \"Bad People\", \"title\": \"Bad People [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"Kanye mumble version, shown in 500 Days In UCLA.\", \"date\": 16955136, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"200765177750708461aa7c3740aad6e5\", \"url\": \"https://api.pillowcase.su/api/download/200765177750708461aa7c3740aad6e5\", \"size\": \"4.93 MB\", \"duration\": 108.17}", "aliases": ["White Label"], "size": "4.93 MB"}, {"id": "bed", "name": "Bed [V2]", "artists": ["The-Dream"], "producers": ["DJDS"], "notes": "OG Filename: Bed Fashion Show DJDS 2.10.17\nEarlier version of \"Bed\" with alternate production & shorter than release. Made for Yeezy Season 5.", "length": "900.62", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/6d4569536c8109aefd702df35d0e31c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d4569536c8109aefd702df35d0e31c0\", \"key\": \"Bed\", \"title\": \"Bed [V2]\", \"artists\": \"(feat. The-<PERSON>) (prod. DJDS)\", \"aliases\": [\"BED YEEZY SEASON 5\"], \"description\": \"OG Filename: Bed Fashion Show DJDS 2.10.17\\nEarlier version of \\\"Bed\\\" with alternate production & shorter than release. Made for Yeezy Season 5.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4e7dd01e8e0b890655ef24af4f86d233\", \"url\": \"https://api.pillowcase.su/api/download/4e7dd01e8e0b890655ef24af4f86d233\", \"size\": \"17.6 MB\", \"duration\": 900.62}", "aliases": ["BED YEEZY SEASON 5"], "size": "17.6 MB"}, {"id": "brothers", "name": "Brothers [V3]", "artists": [], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "Solo \"Brothers\" ref by <PERSON>, recorded in early 2018. Has no actual verses. Posted on his Instagram story in July 2019.", "length": "60.19", "fileDate": 15620256, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/5c967ff6abd300a64a315cd1deae3499", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5c967ff6abd300a64a315cd1deae3499\", \"key\": \"Brothers\", \"title\": \"Brothers [V3]\", \"artists\": \"(ref. <PERSON>) (prod. 7 <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Solo \\\"Brothers\\\" ref by <PERSON>, recorded in early 2018. Has no actual verses. Posted on his Instagram story in July 2019.\", \"date\": 15620256, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"28785bc3f321a4dd6bb0c69c3dc63946\", \"url\": \"https://api.pillowcase.su/api/download/28785bc3f321a4dd6bb0c69c3dc63946\", \"size\": \"4.17 MB\", \"duration\": 60.19}", "aliases": [], "size": "4.17 MB"}, {"id": "brothers-4", "name": "Brothers [V4]", "artists": ["<PERSON>"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>", "Bink!"], "notes": "Version made in February 2018. Only has the <PERSON> feature chopped up on the instrumental. \"Brothers\" was altered to create \"Violent Crimes\".", "length": "262.72", "fileDate": 15619392, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/3bb2b3cc450b9b575c842cce30582b5d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3bb2b3cc450b9b575c842cce30582b5d\", \"key\": \"Brothers\", \"title\": \"Brothers [V4]\", \"artists\": \"(feat. <PERSON>) (prod. 7 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>!)\", \"description\": \"Version made in February 2018. Only has the <PERSON> feature chopped up on the instrumental. \\\"Brothers\\\" was altered to create \\\"Violent Crimes\\\".\", \"date\": 15619392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0577d96635299170d1f6f7a78147b21f\", \"url\": \"https://api.pillowcase.su/api/download/0577d96635299170d1f6f7a78147b21f\", \"size\": \"7.41 MB\", \"duration\": 262.72}", "aliases": [], "size": "7.41 MB"}, {"id": "brothers-5", "name": "Brothers [V5]", "artists": [], "producers": ["7 Aurel<PERSON>"], "notes": "OG Filename: Brothers Ref (2.8.18)\nEarlier version of the 7 Aurelius ref.", "length": "98.67", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/cf5ffd80e998db9191acd3fc810077c5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf5ffd80e998db9191acd3fc810077c5\", \"key\": \"Brothers\", \"title\": \"Brothers [V5]\", \"artists\": \"(ref. 7 Aurelius) (prod. 7 Aurelius)\", \"description\": \"OG Filename: Brothers Ref (2.8.18)\\nEarlier version of the 7 Aurelius ref.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b7745dea578f3dfd163cdc02657f15e1\", \"url\": \"https://api.pillowcase.su/api/download/b7745dea578f3dfd163cdc02657f15e1\", \"size\": \"4.78 MB\", \"duration\": 98.67}", "aliases": [], "size": "4.78 MB"}, {"id": "brothers-6", "name": "Brothers [V6]", "artists": [], "producers": ["7 Aurel<PERSON>"], "notes": "OG Filename: Brothers Ref (4.12.18) @ 110 BPM\nA chopped version of 7 Aurelius playing \"Brothers\" to <PERSON><PERSON><PERSON> in November 2017. Ye vocals can be heard near the end of the song in the bleed.", "length": "78.6", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/7ca694a2c65db8ac25148613a2e495db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7ca694a2c65db8ac25148613a2e495db\", \"key\": \"Brothers\", \"title\": \"Brothers [V6]\", \"artists\": \"(ref. 7 Aurelius) (prod. 7 Aurelius)\", \"description\": \"OG Filename: Brothers Ref (4.12.18) @ 110 BPM\\nA chopped version of 7 Aurelius playing \\\"Brothers\\\" to <PERSON><PERSON><PERSON> in November 2017. Ye vocals can be heard near the end of the song in the bleed.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0bd14674b71e52eb80580102a2aec3d1\", \"url\": \"https://api.pillowcase.su/api/download/0bd14674b71e52eb80580102a2aec3d1\", \"size\": \"4.46 MB\", \"duration\": 78.6}", "aliases": [], "size": "4.46 MB"}, {"id": "cops-shot-the-kid", "name": "Cops Shot The Kid [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Charlamagne confirmed that a solo Ye version of the track existed, along with another song off Na<PERSON>' Nasir album. Small snippet can be heard in the 500 Days In UCLA documentary.", "length": "43.3", "fileDate": 16955136, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/799cf1a332c039b476aa8cf5d323d09e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/799cf1a332c039b476aa8cf5d323d09e\", \"key\": \"<PERSON><PERSON> Shot The Kid\", \"title\": \"<PERSON><PERSON> Shot The Kid [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"<PERSON><PERSON><PERSON><PERSON> confirmed that a solo Ye version of the track existed, along with another song off <PERSON><PERSON>' Na<PERSON> album. Small snippet can be heard in the 500 Days In UCLA documentary.\", \"date\": 16955136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5d0435295ceef57d38cc9aff69f1ea3e\", \"url\": \"https://api.pillowcase.su/api/download/5d0435295ceef57d38cc9aff69f1ea3e\", \"size\": \"3.9 MB\", \"duration\": 43.3}", "aliases": [], "size": "3.9 MB"}, {"id": "dj-k<PERSON>d-s-son", "name": "🗑️ <PERSON>'s Son", "artists": [], "producers": ["MIKE DEAN", "???"], "notes": "One of the most infamous unreleased <PERSON><PERSON><PERSON> songs ever. <PERSON><PERSON><PERSON> says several slurs, and is interpreted to be ranting about date rape and other controversial topics, though these portions are ambiguous since the song is heavily mumbled. MIKE DEAN has said that he did the keys. A partial VC recording leaked on December 13th, 2022, with another short snippet leaking March 27th, 2024. Randomly leaked in full two days later.", "length": "228.86", "fileDate": 17116704, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/fbd770f1a32a0ea277372dc75d072a97", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fbd770f1a32a0ea277372dc75d072a97\", \"key\": \"<PERSON><PERSON>'s Son\", \"title\": \"\\ud83d\\uddd1\\ufe0f <PERSON>'s Son\", \"artists\": \"(prod. <PERSON><PERSON> & ???)\", \"description\": \"One of the most infamous unreleased Kanye songs ever. <PERSON><PERSON><PERSON> says several slurs, and is interpreted to be ranting about date rape and other controversial topics, though these portions are ambiguous since the song is heavily mumbled. <PERSON><PERSON> has said that he did the keys. A partial VC recording leaked on December 13th, 2022, with another short snippet leaking March 27th, 2024. Randomly leaked in full two days later.\", \"date\": 17116704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6931250b46ab72679d5015bf34445e32\", \"url\": \"https://api.pillowcase.su/api/download/6931250b46ab72679d5015bf34445e32\", \"size\": \"6.87 MB\", \"duration\": 228.86}", "aliases": [], "size": "6.87 MB"}, {"id": "don-t-play-with-that-boy", "name": "Don't Play With That Boy [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Solo LOVE EVERYONE scrap, shares the beat reused for \"Ye Vs. The People\" but otherwise an entirely different song. Snippet was played in the behind the scenes \"kanye west / t.i.\" video posted on <PERSON>'s YouTube channel.", "length": "19.3", "fileDate": 15251328, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e21df375995ee16920ba75cf2d9930f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e21df375995ee16920ba75cf2d9930f3\", \"key\": \"Don't Play With That Boy\", \"title\": \"Don't Play With That Boy [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Ye vs. the People\"], \"description\": \"Solo LOVE EVERYONE scrap, shares the beat reused for \\\"Ye Vs. The People\\\" but otherwise an entirely different song. Snippet was played in the behind the scenes \\\"kanye west / t.i.\\\" video posted on <PERSON>'s YouTube channel.\", \"date\": 15251328, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d56a9b35a1730384c8b785b6ef552f5c\", \"url\": \"https://api.pillowcase.su/api/download/d56a9b35a1730384c8b785b6ef552f5c\", \"size\": \"3.51 MB\", \"duration\": 19.3}", "aliases": ["<PERSON> vs. the People"], "size": "3.51 MB"}, {"id": "freddy", "name": "<PERSON>", "artists": [], "producers": [], "notes": "OG Filename: malik freddy ref\n<PERSON> \"<PERSON>\" reference track, unknown if a version with <PERSON><PERSON><PERSON> vocals exists. Original snippet leaked November 18th, 2022.", "length": "95.4", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/6da6b5f10ad4c400be9e4ef34cbd91a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6da6b5f10ad4c400be9e4ef34cbd91a9\", \"key\": \"<PERSON>\", \"title\": \"<PERSON>\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"OG Filename: malik freddy ref\\n<PERSON><PERSON><PERSON> \\\"<PERSON>\\\" reference track, unknown if a version with <PERSON><PERSON><PERSON> vocals exists. Original snippet leaked November 18th, 2022.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a9ed2ab7f4e2eb97d3f997957e605fa0\", \"url\": \"https://api.pillowcase.su/api/download/a9ed2ab7f4e2eb97d3f997957e605fa0\", \"size\": \"1.96 MB\", \"duration\": 95.4}", "aliases": [], "size": "1.96 MB"}, {"id": "fine-line", "name": "Fine Line [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "notes": "OG Filename: Fine Line JV KW DAW BHASK\nEarlier version of \"Sh'diah\" which has a similar instrumental to \"Take Me To The Light\". Recorded March 10th, 2018. Samples \"Waves\" by <PERSON>. Leaked after a failed groupbuy.", "length": "193.3", "fileDate": 15890688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/bdb66c58605436d133a26920bd41a055", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bdb66c58605436d133a26920bd41a055\", \"key\": \"Fine Line\", \"title\": \"Fine Line [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Take Me To The Light\", \"You Still Take Me To The Light\", \"Metta World Peace\", \"Sh'Diah\"], \"description\": \"OG Filename: Fine Line JV KW DAW BHASK\\nEarlier version of \\\"Sh'diah\\\" which has a similar instrumental to \\\"Take Me To The Light\\\". Recorded March 10th, 2018. Samples \\\"Waves\\\" by <PERSON>. Leaked after a failed groupbuy.\", \"date\": 15890688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cfbf22df4031c3768951d621cd20b7af\", \"url\": \"https://api.pillowcase.su/api/download/cfbf22df4031c3768951d621cd20b7af\", \"size\": \"6.3 MB\", \"duration\": 193.3}", "aliases": ["Take Me To The Light", "You Still Take Me To The Light", "Metta World Peace", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "size": "6.3 MB"}, {"id": "ghost-town", "name": "Ghost Town [V2]", "artists": ["PARTYNEXTDOOR"], "producers": [], "notes": "OG Filename: Ghost Town Ye Party Ref (10.31.17)\nVersion of \"Ghost Town\", originally and falsely named as \"Someday\" while leaked. Includes three mostly mumble Kanye verses and the same PARTYNEXTDOOR verse as the final version. Also features different production. Has original sample (that being <PERSON> - \"Take Me For a Little While\"), later replaced by <PERSON> singing. Leaked after a groupbuy.", "length": "312.06", "fileDate": 16265664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/521fe33ac6a5ff29dd59df14cabf2074", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/521fe33ac6a5ff29dd59df14cabf2074\", \"key\": \"Ghost Town\", \"title\": \"Ghost Town [V2]\", \"artists\": \"(feat. PARTYNEXTDOOR)\", \"description\": \"OG Filename: Ghost Town Ye Party Ref (10.31.17)\\nVersion of \\\"Ghost Town\\\", originally and falsely named as \\\"Someday\\\" while leaked. Includes three mostly mumble Kanye verses and the same PARTYNEXTDOOR verse as the final version. Also features different production. Has original sample (that being <PERSON> - \\\"Take Me For a Little While\\\"), later replaced by <PERSON> singing. Leaked after a groupbuy.\", \"date\": 16265664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"779768ff9623dad1542fb62fb4d9d7d7\", \"url\": \"https://api.pillowcase.su/api/download/779768ff9623dad1542fb62fb4d9d7d7\", \"size\": \"8.2 MB\", \"duration\": 312.06}", "aliases": [], "size": "8.2 MB"}, {"id": "i-feel-like-that", "name": "🏆 I Feel Like That [V14]", "artists": ["Ty Dolla $ign", "The WRLDFMS <PERSON>"], "producers": ["MIKE DEAN", "DJDS", "<PERSON>"], "notes": "A version from 2017, featured in the third episode of jeen-yuhs. <PERSON><PERSON><PERSON> is seen recording new vocals for the song in the documentary, which contains mumble. <PERSON> and <PERSON>ign are both still featured. The production is similar to the DJDS version, but features different progression and new elements from <PERSON>.", "length": "86.54", "fileDate": 16436736, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a363190ca8b652a66a9120b81e4b831b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a363190ca8b652a66a9120b81e4b831b\", \"key\": \"I Feel Like That\", \"title\": \"\\ud83c\\udfc6 I Feel Like That [V14]\", \"artists\": \"(feat. <PERSON>ign & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"description\": \"A version from 2017, featured in the third episode of jeen-yuhs. <PERSON><PERSON><PERSON> is seen recording new vocals for the song in the documentary, which contains mumble. <PERSON> and <PERSON>ign are both still featured. The production is similar to the DJDS version, but features different progression and new elements from <PERSON>.\", \"date\": 16436736, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"afd9dd9a5ba45cbb8f57c9142581599d\", \"url\": \"https://api.pillowcase.su/api/download/afd9dd9a5ba45cbb8f57c9142581599d\", \"size\": \"4.59 MB\", \"duration\": 86.54}", "aliases": [], "size": "4.59 MB"}, {"id": "i-need-you-now", "name": "I Need You Now", "artists": [], "producers": [], "notes": "Mumble demo that was being sold on TheSource. Assumed to be LOVE EVERYONE era because it is \"2016-2018\" according to the seller.", "length": "8.65", "fileDate": 16187904, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/05fe26734c6b2d2116038de94efadf31", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/05fe26734c6b2d2116038de94efadf31\", \"key\": \"I Need You Now\", \"title\": \"I Need You Now\", \"description\": \"Mumble demo that was being sold on TheSource. Assumed to be LOVE EVERYONE era because it is \\\"2016-2018\\\" according to the seller.\", \"date\": 16187904, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"48588a8a492675489ab13dcc82ea43b0\", \"url\": \"https://api.pillowcase.su/api/download/48588a8a492675489ab13dcc82ea43b0\", \"size\": \"3.34 MB\", \"duration\": 8.65}", "aliases": [], "size": "3.34 MB"}, {"id": "mac-n-cheese", "name": "<PERSON> [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON><PERSON> Cheese\nSamples \"Chamber of Reflection\" by <PERSON>.", "length": "198.87", "fileDate": 16997472, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/667ddea8cca0320f326957bdcfc45161", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/667ddea8cca0320f326957bdcfc45161\", \"key\": \"Mac N Cheese\", \"title\": \"<PERSON> [V1]\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON>\\nSamples \\\"Chamber of Reflection\\\" by <PERSON>.\", \"date\": 16997472, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"19d272fc3c820c687302495eb6c76da6\", \"url\": \"https://api.pillowcase.su/api/download/19d272fc3c820c687302495eb6c76da6\", \"size\": \"6.39 MB\", \"duration\": 198.87}", "aliases": [], "size": "6.39 MB"}, {"id": "maria", "name": "<PERSON>", "artists": [], "producers": ["Kanye West", "Hudson Mohawke", "<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: PND - MARIA v1\nPARTY<PERSON><PERSON><PERSON><PERSON><PERSON> \"<PERSON>\" reference track. Unknown exactly when this version was made, but <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> first announced he was working with <PERSON><PERSON><PERSON> in July 2017, and the track released in June 2018. A lossless snippet leaked on Jan 23rd 2023, with it leaking in full October 5th, 2024 - but in CDQ.", "length": "270.39", "fileDate": 17280864, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/d5f87aca40f15aa02acec0b4950afbb5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d5f87aca40f15aa02acec0b4950afbb5\", \"key\": \"<PERSON>\", \"title\": \"<PERSON>\", \"artists\": \"(ref. PARTYNEXTDOOR) (prod. <PERSON><PERSON><PERSON>, Hudson Mohawke & Che Pope)\", \"description\": \"OG Filename: PND - MARIA v1\\nPARTYNEXTDOOR \\\"Maria\\\" reference track. Unknown exactly when this version was made, but PA<PERSON>YNEXTD<PERSON><PERSON> first announced he was working with <PERSON><PERSON><PERSON> in July 2017, and the track released in June 2018. A lossless snippet leaked on Jan 23rd 2023, with it leaking in full October 5th, 2024 - but in CDQ.\", \"date\": 17280864, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6213daabbe174bdbfb897c302bc3b468\", \"url\": \"https://api.pillowcase.su/api/download/6213daabbe174bdbfb897c302bc3b468\", \"size\": \"7.53 MB\", \"duration\": 270.39}", "aliases": [], "size": "7.53 MB"}, {"id": "let-you-go", "name": "✨ Let You Go", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filenames: KW-DREAM_16_Let You Go 11.17 &\nLet You Go 11.17\nLOVE EVERYONE era demo with <PERSON><PERSON><PERSON> doing ref vocals for <PERSON><PERSON><PERSON>. The instrumental, hook and <PERSON> $ign vocals were reworked into the <PERSON><PERSON> K.T.S.E. song \"Never Would Have Made It\". Unknown if a Kanye version exists.", "length": "182.31", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/25ed222be539a8be5f7fdcb07755f349", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/25ed222be539a8be5f7fdcb07755f349\", \"key\": \"Let You Go\", \"title\": \"\\u2728 Let You Go\", \"artists\": \"(ref. <PERSON>-<PERSON>) (feat. <PERSON>ign)\", \"aliases\": [\"Never Would Have Made It\"], \"description\": \"OG Filenames: KW-DREAM_16_Let You Go 11.17 &\\nLet You Go 11.17\\nLOVE EVERYONE era demo with <PERSON><PERSON><PERSON> doing ref vocals for <PERSON><PERSON><PERSON>. The instrumental, hook and <PERSON> <PERSON> $ign vocals were reworked into the <PERSON><PERSON> K.T.S.E. song \\\"Never Would Have Made It\\\". Unknown if a Kanye version exists.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"da7dd16c1aa37d44b583af486f0ed349\", \"url\": \"https://api.pillowcase.su/api/download/da7dd16c1aa37d44b583af486f0ed349\", \"size\": \"6.12 MB\", \"duration\": 182.31}", "aliases": ["Never Would Have Made It"], "size": "6.12 MB"}, {"id": "not-us", "name": "Not Us", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: Not <PERSON> Noah <PERSON>ple JV Work\nContains open verses. Instrumental is owned by REDACTED, and was found alongside other LE and ye instrumentals, but no vocals for it have been found. Unconfirmed whether it was intended for <PERSON><PERSON><PERSON>, but it likely was. Samples \"Black Power\" by <PERSON>.", "length": "131.44", "fileDate": 16999200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/0d1fe97688223b07fc06e1f4859519fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d1fe97688223b07fc06e1f4859519fc\", \"key\": \"Not Us\", \"title\": \"Not Us\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: Not <PERSON> <PERSON> JV Work\\nContains open verses. Instrumental is owned by REDACTED, and was found alongside other LE and ye instrumentals, but no vocals for it have been found. Unconfirmed whether it was intended for <PERSON><PERSON><PERSON>, but it likely was. <PERSON><PERSON> \\\"Black Power\\\" by <PERSON>.\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"655402a272967c5955cf5ec3899120a5\", \"url\": \"https://api.pillowcase.su/api/download/655402a272967c5955cf5ec3899120a5\", \"size\": \"5.31 MB\", \"duration\": 131.44}", "aliases": [], "size": "5.31 MB"}, {"id": "bastard-child", "name": "✨ Bon Iver - Bastard Child [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON> and the Lights"], "notes": "OG Filename: <PERSON><PERSON><PERSON> (Made this week from jams and sampled)\nUnreleased Bon Iver song that is sampled on \"Simple Things\".", "length": "104.32", "fileDate": 17065728, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1ec72123cd6c5dfaa6294c773ad9cb03", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ec72123cd6c5dfaa6294c773ad9cb03\", \"key\": \"Bastard Child\", \"title\": \"\\u2728 Bon Iver - Bastard Child [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON> and the Lights)\", \"aliases\": [\"Simple Things\", \"Finding Forever\"], \"description\": \"OG Filename: Bastard Child (Made this week from jams and sampled)\\nUnreleased Bon Iver song that is sampled on \\\"Simple Things\\\".\", \"date\": 17065728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"97376e37b0fcfc3cffcaef6eb71a0982\", \"url\": \"https://api.pillowcase.su/api/download/97376e37b0fcfc3cffcaef6eb71a0982\", \"size\": \"4.88 MB\", \"duration\": 104.32}", "aliases": ["Simple Things", "Finding Forever"], "size": "4.88 MB"}, {"id": "simple-things", "name": "🏆 Simple Things [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "Solo Ye version of \"Simple Things\" from <PERSON><PERSON><PERSON> <PERSON><PERSON> album with some mumble, is featured in the unreleased documentary 500 Days In UCLA.", "length": "47.18", "fileDate": 16955136, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/3c8a00985c8674670b7681721daa3e41", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3c8a00985c8674670b7681721daa3e41\", \"key\": \"Simple Things\", \"title\": \"\\ud83c\\udfc6 Simple Things [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Finding Forever\"], \"description\": \"Solo Ye version of \\\"Simple Things\\\" from Nas' Nasir album with some mumble, is featured in the unreleased documentary 500 Days In UCLA.\", \"date\": 16955136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b9b7295901ad0741af80814b847afae3\", \"url\": \"https://api.pillowcase.su/api/download/b9b7295901ad0741af80814b847afae3\", \"size\": \"3.96 MB\", \"duration\": 47.18}", "aliases": ["Finding Forever"], "size": "3.96 MB"}, {"id": "skeletons", "name": "⭐ Skeletons [V2]", "artists": ["<PERSON>", "The Weeknd", "<PERSON><PERSON><PERSON>"], "producers": ["Tame Impala", "MIKE DEAN", "<PERSON>"], "notes": "Version with <PERSON><PERSON><PERSON> doing an original finished verse about peace, ego, greed, copycats, and his reference writers. He also does <PERSON>'s verse from the released version. Has an <PERSON><PERSON> interview sample as well as additional background vocals and ad-libs from <PERSON> Weeknd and <PERSON><PERSON><PERSON>. <PERSON> is only in the intro and background vocals. Four lines were eventually used for <PERSON><PERSON><PERSON>'s verse in \"Cudi Montage\". Lyrics were seen in the 2018 notepad. Leaked after a groupbuy for $8,000.", "length": "285.5", "fileDate": 16562880, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ed4fe836970558e0ac3bc23c8a61e996", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ed4fe836970558e0ac3bc23c8a61e996\", \"key\": \"Skeletons\", \"title\": \"\\u2b50 Skeletons [V2]\", \"artists\": \"(feat. <PERSON>, The Weeknd & Phar<PERSON>) (prod. <PERSON><PERSON>, MIKE <PERSON>AN & <PERSON>)\", \"description\": \"Version with <PERSON><PERSON><PERSON> doing an original finished verse about peace, ego, greed, copycats, and his reference writers. He also does <PERSON>'s verse from the released version. Has an <PERSON><PERSON> interview sample as well as additional background vocals and ad-libs from <PERSON> Weeknd and <PERSON><PERSON><PERSON>. <PERSON> is only in the intro and background vocals. Four lines were eventually used for <PERSON><PERSON><PERSON>'s verse in \\\"Cudi <PERSON>age\\\". Lyrics were seen in the 2018 notepad. Leaked after a groupbuy for $8,000.\", \"date\": 16562880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"873f6e89559a95e82a1dc18b657c1d1d\", \"url\": \"https://api.pillowcase.su/api/download/873f6e89559a95e82a1dc18b657c1d1d\", \"size\": \"7.77 MB\", \"duration\": 285.5}", "aliases": [], "size": "7.77 MB"}, {"id": "sam-zach", "name": "<PERSON> - <PERSON> + <PERSON> [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: JUSTIN PHIL Sam + <PERSON>\nAccording to <PERSON>, \"Wouldn't Leave\" originated as \"a jam of me and <PERSON> on piano and <PERSON> synths and me on some op-1 gospel samples\".", "length": "211.12", "fileDate": 16999200, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e8b578123bf4f5a5b955664f6c3196e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8b578123bf4f5a5b955664f6c3196e1\", \"key\": \"<PERSON> + <PERSON>\", \"title\": \"<PERSON> Iver - <PERSON> + <PERSON> [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Wouldn't Leave\"], \"description\": \"OG Filename: JUSTIN PHIL Sam + <PERSON>\\nAccording to <PERSON>, \\\"Wouldn't Leave\\\" originated as \\\"a jam of me and <PERSON> on piano and <PERSON> synths and me on some op-1 gospel samples\\\".\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5204301a725e5d7110600c8b81da28e4\", \"url\": \"https://api.pillowcase.su/api/download/5204301a725e5d7110600c8b81da28e4\", \"size\": \"6.58 MB\", \"duration\": 211.12}", "aliases": ["Wouldn't Leave"], "size": "6.58 MB"}, {"id": "wouldn-t-leave", "name": "Wouldn't Leave [V2]", "artists": [], "producers": [], "notes": "Completely finished LOVE EVERYONE version of the song from March 2018. The only thing it has in common with released version is the instrumental and the phrase \"Wouldn't Leave\" in the lyrics. A LQ snippet taken from documentary footage originally leaked March 21st, 2022.", "length": "73.92", "fileDate": 16777152, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/cf181e0628ad143a3f83ca9ce3b4138e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf181e0628ad143a3f83ca9ce3b4138e\", \"key\": \"Wouldn't Leave\", \"title\": \"Wouldn't Leave [V2]\", \"description\": \"Completely finished LOVE EVERYONE version of the song from March 2018. The only thing it has in common with released version is the instrumental and the phrase \\\"Wouldn't Leave\\\" in the lyrics. A LQ snippet taken from documentary footage originally leaked March 21st, 2022.\", \"date\": 16777152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ce26438216637f2d30b8e90ffd7dd4f5\", \"url\": \"https://api.pillowcase.su/api/download/ce26438216637f2d30b8e90ffd7dd4f5\", \"size\": \"4.39 MB\", \"duration\": 73.92}", "aliases": [], "size": "4.39 MB"}, {"id": "", "name": "Drake - ??? [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Studio session recording posted by <PERSON> himself on 100gigs.org. Has an entirely different beat. The bleed from \"Yikes REF 05.26.18 3am\" proves that these vocals were used for \"Yikes\". Apparently recorded some time in March 2018 as the \"Yikes\" chop wasn't made until May. This beat was re-used for \"Astrology\" by Preme.", "length": "", "fileDate": 17229024, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/caa904a8110df55e1007d5f4eb7e3209", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/caa904a8110df55e1007d5f4eb7e3209\", \"key\": \"???\", \"title\": \"<PERSON> - ??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Yikes\"], \"description\": \"Studio session recording posted by <PERSON> himself on 100gigs.org. Has an entirely different beat. The bleed from \\\"Yikes REF 05.26.18 3am\\\" proves that these vocals were used for \\\"Yikes\\\". Apparently recorded some time in March 2018 as the \\\"Yikes\\\" chop wasn't made until May. This beat was re-used for \\\"Astrology\\\" by Preme.\", \"date\": 17229024, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["<PERSON><PERSON>"], "size": ""}, {"id": "yikes", "name": "🏆 Yikes [V3]", "artists": [], "producers": ["???"], "notes": "Version played by <PERSON><PERSON><PERSON> on live November 29th, 2024. Features new production from an unknown producer. Unknown whether <PERSON> is on this version, since <PERSON> is only heard interpolating the pre-chorus. Very different from the \"Yikes\" that released.", "length": "24.14", "fileDate": 17328384, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/6736c658343c6c99ad5af76e8cca2d27", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6736c658343c6c99ad5af76e8cca2d27\", \"key\": \"Yikes\", \"title\": \"\\ud83c\\udfc6 Yikes [V3]\", \"artists\": \"(prod. ???)\", \"description\": \"Version played by Conse<PERSON> on live November 29th, 2024. Features new production from an unknown producer. Unknown whether <PERSON> is on this version, since <PERSON> is only heard interpolating the pre-chorus. Very different from the \\\"Yikes\\\" that released.\", \"date\": 17328384, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6390286ca73ce1a224720aa8a3ab71ac\", \"url\": \"https://api.pillowcase.su/api/download/6390286ca73ce1a224720aa8a3ab71ac\", \"size\": \"3.59 MB\", \"duration\": 24.14}", "aliases": [], "size": "3.59 MB"}, {"id": "-26", "name": "??? [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Lyrics were found in the LOVE EVERYONE lyrics notepad, and it was performed for TMZ on April 26th 2018. Samples \"Idea #36\" by <PERSON>. The chop was eventually reused and developed further for the Young Thug song \"Rich Nigga Shit\" from his 2021 album Punk. Lyrics:\n\n\"Parents are the strippers\nStrip kids of they confidence\nTeach white dominance\nQuestion your common sense\nI been washed in tradition that ima rence\nI hopped off the amistad and made I'm a god. Oh it's gone be a litigation any time I'm involved\nOh it's gone be some litigation if it's not resolved\"", "length": "37.49", "fileDate": 15247008, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/b1fc570ec5247c41688847e12ac6a41b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b1fc570ec5247c41688847e12ac6a41b\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Amistad\", \"Rich Nigga Shit\"], \"description\": \"Lyrics were found in the LOVE EVERYONE lyrics notepad, and it was performed for TMZ on April 26th 2018. Samples \\\"Idea #36\\\" by <PERSON>. The chop was eventually reused and developed further for the Young Thug song \\\"Rich Nigga Shit\\\" from his 2021 album Punk. Lyrics:\\n\\n\\\"Parents are the strippers\\nStrip kids of they confidence\\nTeach white dominance\\nQuestion your common sense\\nI been washed in tradition that ima rence\\nI hopped off the amistad and made I'm a god. Oh it's gone be a litigation any time I'm involved\\nOh it's gone be some litigation if it's not resolved\\\"\", \"date\": 15247008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"975e7baaf899d1b16bbe970842e79e87\", \"url\": \"https://api.pillowcase.su/api/download/975e7baaf899d1b16bbe970842e79e87\", \"size\": \"3.8 MB\", \"duration\": 37.49}", "aliases": ["Amistad", "<PERSON>"], "size": "3.8 MB"}, {"id": "-27", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Snippet posted by <PERSON>G<PERSON><PERSON> on TheSource. Year listed as 2017. <PERSON><PERSON> notes it's a mumble demo. Privately sold.", "length": "11", "fileDate": 15952896, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/afb6a01fe2cea6d3f979748e89be70ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afb6a01fe2cea6d3f979748e89be70ce\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Away From Home\"], \"description\": \"Snippet posted by LilGoblin on TheSource. Year listed as 2017. <PERSON><PERSON> notes it's a mumble demo. Privately sold.\", \"date\": 15952896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d06f16e64e3190c3dad067b1ad04b064\", \"url\": \"https://api.pillowcase.su/api/download/d06f16e64e3190c3dad067b1ad04b064\", \"size\": \"3.38 MB\", \"duration\": 11}", "aliases": ["Away From Home"], "size": "3.38 MB"}, {"id": "-28", "name": "???", "artists": [], "producers": ["Kanye West"], "notes": "Very short and rough LOVE EVERYONE freestyle over a live instrumental. Has a significant amount of background noise.", "length": "39.78", "fileDate": 16283808, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/65363cf2af6115e116cc9e1d14d62850", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65363cf2af6115e116cc9e1d14d62850\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Take Me Now\"], \"description\": \"Very short and rough LOVE EVERYONE freestyle over a live instrumental. Has a significant amount of background noise.\", \"date\": 16283808, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a89ffe3b23fecd233149bb7adaa760ad\", \"url\": \"https://api.pillowcase.su/api/download/a89ffe3b23fecd233149bb7adaa760ad\", \"size\": \"3.52 MB\", \"duration\": 39.78}", "aliases": ["Take Me Now"], "size": "3.52 MB"}, {"id": "-29", "name": "???", "artists": [], "producers": ["Kanye West"], "notes": "Sample chop made during LOVE EVERYONE era. Leaked 18th November 2022 by Alek.", "length": "33.84", "fileDate": 16687296, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8ec8e18d50c99e3b8329f4ae4235bccb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ec8e18d50c99e3b8329f4ae4235bccb\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Yeah\"], \"description\": \"Sample chop made during LOVE EVERYONE era. Leaked 18th November 2022 by Alek.\", \"date\": 16687296, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2ec6135bb7f6ed8eddf975d574f425e0\", \"url\": \"https://api.pillowcase.su/api/download/2ec6135bb7f6ed8eddf975d574f425e0\", \"size\": \"3.75 MB\", \"duration\": 33.84}", "aliases": ["Yeah"], "size": "3.75 MB"}, {"id": "music-is-above-you", "name": "A$AP Rocky - Music Is Above You", "artists": [], "producers": ["Kanye West"], "notes": "After the viral freestyle session, A$AP Rocky was given the beat <PERSON><PERSON><PERSON> made and recorded over it.", "length": "6.9", "fileDate": 16869600, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/874b8a171a559aeba9db88b0c601924f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/874b8a171a559aeba9db88b0c601924f\", \"key\": \"Music Is Above You\", \"title\": \"A$AP Rocky - Music Is Above You\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"After the viral freestyle session, A$AP Rocky was given the beat <PERSON><PERSON><PERSON> made and recorded over it.\", \"date\": 16869600, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b37d5e8835f35fcd9139f1764566d1fd\", \"url\": \"https://api.pillowcase.su/api/download/b37d5e8835f35fcd9139f1764566d1fd\", \"size\": \"3.31 MB\", \"duration\": 6.9}", "aliases": [], "size": "3.31 MB"}, {"id": "music-is-above-you-31", "name": "A$AP Rocky - Music Is Above You", "artists": [], "producers": ["Kanye West"], "notes": "After the viral freestyle session, A$AP Rocky was given the beat <PERSON><PERSON><PERSON> made and recorded over it.", "length": "", "fileDate": 16869600, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://www.youtube.com/watch?v=q9ZGn3tOaDQ", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=q9ZGn3tOaDQ\", \"key\": \"Music Is Above You\", \"title\": \"A$AP Rocky - Music Is Above You\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"After the viral freestyle session, A$AP Rocky was given the beat <PERSON><PERSON><PERSON> made and recorded over it.\", \"date\": 16869600, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "yeezy-my", "name": "A$AP Rocky - Yeezy My", "artists": ["A$AP Ferg", "Kanye West"], "producers": [], "notes": "<PERSON>ny<PERSON> feature recorded during the sessions for A$AP Rocky's album TESTING 2. Has adlibs from A$AP Ferg. According to the seller there are \"2+ mins [of] vocals\" and A$AP Rocky does the hook. Track could be produced by <PERSON><PERSON><PERSON> as the instrumental shares similarities to <PERSON><PERSON><PERSON> and <PERSON>'s song \"New Choppa\". Snippet was previewed in AWGE DVD Vol. 3 and a second leaked on December 4th, 2023. A high quality snippet would be shared on December 15th 2023. A VC recording from ATTAM would be shared on July 18th 2024. Partial LQ snippet leaked August 9th, 2024.", "length": "104.68", "fileDate": 17231616, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1e1d61dafa56901fcce23434c592ada5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e1d61dafa56901fcce23434c592ada5\", \"key\": \"Yeezy My\", \"title\": \"A$AP Rocky - Yeezy My\", \"artists\": \"(feat. A$AP Ferg & Kanye West)\", \"description\": \"Kanye feature recorded during the sessions for A$AP Rocky's album TESTING 2. Has adlibs from A$AP Ferg. According to the seller there are \\\"2+ mins [of] vocals\\\" and A$AP Rocky does the hook. Track could be produced by <PERSON><PERSON><PERSON> as the instrumental shares similarities to <PERSON><PERSON><PERSON> and <PERSON>'s song \\\"New Choppa\\\". Snippet was previewed in AWGE DVD Vol. 3 and a second leaked on December 4th, 2023. A high quality snippet would be shared on December 15th 2023. A VC recording from ATTAM would be shared on July 18th 2024. Partial LQ snippet leaked August 9th, 2024.\", \"date\": 17231616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7d1814ae38de218854af725aec1694de\", \"url\": \"https://api.pillowcase.su/api/download/7d1814ae38de218854af725aec1694de\", \"size\": \"4.88 MB\", \"duration\": 104.68}", "aliases": [], "size": "4.88 MB"}, {"id": "yeezy-my-33", "name": "A$AP Rocky - Yeezy My", "artists": ["A$AP Ferg", "Kanye West"], "producers": [], "notes": "<PERSON>ny<PERSON> feature recorded during the sessions for A$AP Rocky's album TESTING 2. Has adlibs from A$AP Ferg. According to the seller there are \"2+ mins [of] vocals\" and A$AP Rocky does the hook. Track could be produced by <PERSON><PERSON><PERSON> as the instrumental shares similarities to <PERSON><PERSON><PERSON> and <PERSON>'s song \"New Choppa\". Snippet was previewed in AWGE DVD Vol. 3 and a second leaked on December 4th, 2023. A high quality snippet would be shared on December 15th 2023. A VC recording from ATTAM would be shared on July 18th 2024. Partial LQ snippet leaked August 9th, 2024.", "length": "12.75", "fileDate": 17231616, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2af627dc9cafba7dc97547c934e45b99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2af627dc9cafba7dc97547c934e45b99\", \"key\": \"Yeezy My\", \"title\": \"A$AP Rocky - Yeezy My\", \"artists\": \"(feat. A$AP Ferg & Kanye West)\", \"description\": \"Kanye feature recorded during the sessions for A$AP Rocky's album TESTING 2. Has adlibs from A$AP Ferg. According to the seller there are \\\"2+ mins [of] vocals\\\" and A$AP Rocky does the hook. Track could be produced by <PERSON><PERSON><PERSON> as the instrumental shares similarities to <PERSON><PERSON><PERSON> and <PERSON>'s song \\\"New Choppa\\\". Snippet was previewed in AWGE DVD Vol. 3 and a second leaked on December 4th, 2023. A high quality snippet would be shared on December 15th 2023. A VC recording from ATTAM would be shared on July 18th 2024. Partial LQ snippet leaked August 9th, 2024.\", \"date\": 17231616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3feace90d0f74b56b899b48d654ccbfb\", \"url\": \"https://api.pillowcase.su/api/download/3feace90d0f74b56b899b48d654ccbfb\", \"size\": \"3.41 MB\", \"duration\": 12.75}", "aliases": [], "size": "3.41 MB"}, {"id": "yeezy-my-34", "name": "A$AP Rocky - Yeezy My", "artists": ["A$AP Ferg", "Kanye West"], "producers": [], "notes": "<PERSON>ny<PERSON> feature recorded during the sessions for A$AP Rocky's album TESTING 2. Has adlibs from A$AP Ferg. According to the seller there are \"2+ mins [of] vocals\" and A$AP Rocky does the hook. Track could be produced by <PERSON><PERSON><PERSON> as the instrumental shares similarities to <PERSON><PERSON><PERSON> and <PERSON>'s song \"New Choppa\". Snippet was previewed in AWGE DVD Vol. 3 and a second leaked on December 4th, 2023. A high quality snippet would be shared on December 15th 2023. A VC recording from ATTAM would be shared on July 18th 2024. Partial LQ snippet leaked August 9th, 2024.", "length": "29.78", "fileDate": 17231616, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1d5c88efbf45c5b66c74232cb34c8a42", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1d5c88efbf45c5b66c74232cb34c8a42\", \"key\": \"Yeezy My\", \"title\": \"A$AP Rocky - Yeezy My\", \"artists\": \"(feat. A$AP Ferg & Kanye West)\", \"description\": \"Kanye feature recorded during the sessions for A$AP Rocky's album TESTING 2. Has adlibs from A$AP Ferg. According to the seller there are \\\"2+ mins [of] vocals\\\" and A$AP Rocky does the hook. Track could be produced by <PERSON><PERSON><PERSON> as the instrumental shares similarities to <PERSON><PERSON><PERSON> and <PERSON>'s song \\\"New Choppa\\\". Snippet was previewed in AWGE DVD Vol. 3 and a second leaked on December 4th, 2023. A high quality snippet would be shared on December 15th 2023. A VC recording from ATTAM would be shared on July 18th 2024. Partial LQ snippet leaked August 9th, 2024.\", \"date\": 17231616, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"cc084c98d9a59e831cd921729e6d93f5\", \"url\": \"https://api.pillowcase.su/api/download/cc084c98d9a59e831cd921729e6d93f5\", \"size\": \"3.68 MB\", \"duration\": 29.78}", "aliases": [], "size": "3.68 MB"}, {"id": "-35", "name": "Consequence - ???", "artists": [], "producers": ["Kanye West"], "notes": "Consequence song that uses a part of the beat that was seen in the Kanye, A$AP Rocky and FERG video. Played on Consequence's Instagram Live stream.", "length": "8.88", "fileDate": 17233344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/3d0b051c6c13b531586fa4eef1202804", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3d0b051c6c13b531586fa4eef1202804\", \"key\": \"???\", \"title\": \"Consequence - ???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Music Is Above You\"], \"description\": \"Consequence song that uses a part of the beat that was seen in the Kanye, A$AP Rocky and FERG video. Played on Consequence's Instagram Live stream.\", \"date\": 17233344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"685718e6aad03970e0e512da119fb997\", \"url\": \"https://api.pillowcase.su/api/download/685718e6aad03970e0e512da119fb997\", \"size\": \"3.35 MB\", \"duration\": 8.88}", "aliases": ["Music Is Above You"], "size": "3.35 MB"}, {"id": "pootie-tang", "name": "<PERSON><PERSON> - <PERSON><PERSON><PERSON> [V2]", "artists": [], "producers": ["BONGO ByTheWay", "ROBOTSCOTT"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON> TANG TP ANT BONGO SCOTT\nAnt Clemons song which the hook for \"All Mine\" was taken from. It was made well before the release of ye, and is not a reference track. According to xtcy, the file was in a folder of Jeremih x Ant Clemons tracks. On February 10th, 2018, Legion on Instagram posted a snippet along with what looks to be a scrapped music video.", "length": "147.6", "fileDate": 16264800, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/21961c6be53341a350e69e98017a5ac6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/21961c6be53341a350e69e98017a5ac6\", \"key\": \"<PERSON><PERSON><PERSON> Tang\", \"title\": \"<PERSON><PERSON> - <PERSON><PERSON><PERSON> [V2]\", \"artists\": \"(prod. BONGO ByTheWay & ROBOTSCOTT)\", \"aliases\": [\"All Mine\"], \"description\": \"OG Filename: POOTIE TANG TP ANT BONGO SCOTT\\nAnt Clemons song which the hook for \\\"All Mine\\\" was taken from. It was made well before the release of ye, and is not a reference track. According to xtcy, the file was in a folder of Jeremih x Ant Clemons tracks. On February 10th, 2018, Legion on Instagram posted a snippet along with what looks to be a scrapped music video.\", \"date\": 16264800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"18c5b1d558d006d6f8601b613dcb261f\", \"url\": \"https://api.pillowcase.su/api/download/18c5b1d558d006d6f8601b613dcb261f\", \"size\": \"5.57 MB\", \"duration\": 147.6}", "aliases": ["All Mine"], "size": "5.57 MB"}, {"id": "gun-or-god", "name": "<PERSON> The Rapper - Gun Or God [V1]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: GunOrGod_061818\nPosted to Chance The Rapper's Instagram on April 25th, 2020. The full song was later leaked and info was surfaced that it was a part of Chance x Gambino collab album. <PERSON> later worked on during GAJ 2018 sessions.", "length": "104.4", "fileDate": 17200512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ddb89918a4cfd80443262adb8ba791ea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ddb89918a4cfd80443262adb8ba791ea\", \"key\": \"Gun Or God\", \"title\": \"Chance The Rapper - Gun Or God [V1]\", \"artists\": \"(feat. <PERSON><PERSON> Gambino)\", \"description\": \"OG Filename: GunOrGod_061818\\nPosted to Chance The Rapper's Instagram on April 25th, 2020. The full song was later leaked and info was surfaced that it was a part of Chance x Gambino collab album. <PERSON> later worked on during GAJ 2018 sessions.\", \"date\": 17200512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9eab2fe3ca94e2e6b495a1961d5cab8a\", \"url\": \"https://api.pillowcase.su/api/download/9eab2fe3ca94e2e6b495a1961d5cab8a\", \"size\": \"4.87 MB\", \"duration\": 104.4}", "aliases": [], "size": "4.87 MB"}, {"id": "hold-it-down", "name": "<PERSON> The Rapper - Hold It Down [V1]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Hold it down JL ruff1 Chn vrs 9-20\nChance song possibly considered for his scrapped Gambino collab project due to him having vocals on the track and many other songs from that era also being played during GAJ sessions. Probably made in 2017 as the GAJ session where this was played was on September 15. Leaked in the 62 gigabyte mass leak.", "length": "162.43", "fileDate": 17200512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/29db7ed2306fbf335e8c1b3869ea9994", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/29db7ed2306fbf335e8c1b3869ea9994\", \"key\": \"Hold It Down\", \"title\": \"<PERSON> The Rapper - Hold It Down [V1]\", \"artists\": \"(feat. <PERSON><PERSON> Gambino)\", \"description\": \"OG Filename: Hold it down JL ruff1 Chn vrs 9-20\\nChance song possibly considered for his scrapped Gambino collab project due to him having vocals on the track and many other songs from that era also being played during GAJ sessions. Probably made in 2017 as the GAJ session where this was played was on September 15. Leaked in the 62 gigabyte mass leak.\", \"date\": 17200512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3d6b9942dc4c39046a632162d301681a\", \"url\": \"https://api.pillowcase.su/api/download/3d6b9942dc4c39046a632162d301681a\", \"size\": \"5.8 MB\", \"duration\": 162.43}", "aliases": [], "size": "5.8 MB"}, {"id": "j-j-what", "name": "<PERSON> The Rapper - <PERSON><PERSON><PERSON><PERSON> [V1]", "artists": ["Via Rosa", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Spanish Diego"], "notes": "OG Filename: <PERSON><PERSON> what 1A jl 10-3\nTrack originating in the sessions for <PERSON> The Rapper's scrapped album Owbum. Released as \"Drowsiness\" by <PERSON>, however this version lacks his verses, only containing his bridge and background vocals. Worked on during GAJ 2018 sessions. Leaked in the 62 gigabyte mass leak.", "length": "264.37", "fileDate": 17200512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8ddff11a83a52b7d40ea547fcf581ab0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ddff11a83a52b7d40ea547fcf581ab0\", \"key\": \"J.J. What\", \"title\": \"<PERSON> The Rapper - J<PERSON><PERSON>. What [V1]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spanish <PERSON>)\", \"aliases\": [\"Drowsiness\"], \"description\": \"OG Filename: JJ what 1A jl 10-3\\nTrack originating in the sessions for <PERSON> The Rapper's scrapped album Owbum. Released as \\\"Drowsiness\\\" by <PERSON>, however this version lacks his verses, only containing his bridge and background vocals. Worked on during GAJ 2018 sessions. Leaked in the 62 gigabyte mass leak.\", \"date\": 17200512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"50c792e86487aa97d9907ff9963cdc7a\", \"url\": \"https://api.pillowcase.su/api/download/50c792e86487aa97d9907ff9963cdc7a\", \"size\": \"7.44 MB\", \"duration\": 264.37}", "aliases": ["Drowsiness"], "size": "7.44 MB"}, {"id": "like-the-internet", "name": "<PERSON> The Rapper - Like The Internet [V1]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: LikeTheInternet\nSolo <PERSON><PERSON>, with around 30 seconds of vocals and an open verses meant for <PERSON>. Later worked on in the GAJ 2018 sessions. Original snippet leaked October 16th, 2021. Leaked after a groupbuy.", "length": "127.13", "fileDate": 17117568, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c8043716741305ad59e7112066b75e82", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8043716741305ad59e7112066b75e82\", \"key\": \"Like The Internet\", \"title\": \"<PERSON> The Rapper - Like The Internet [V1]\", \"artists\": \"(feat. <PERSON><PERSON>ambino)\", \"description\": \"OG Filename: LikeTheInternet\\nSolo Childish Gambino, with around 30 seconds of vocals and an open verses meant for <PERSON>. Later worked on in the GAJ 2018 sessions. Original snippet leaked October 16th, 2021. Leaked after a groupbuy.\", \"date\": 17117568, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2a2228cbef287716400c417e80eb2e6f\", \"url\": \"https://api.pillowcase.su/api/download/2a2228cbef287716400c417e80eb2e6f\", \"size\": \"1.59 MB\", \"duration\": 127.13}", "aliases": [], "size": "1.59 MB"}, {"id": "pets", "name": "<PERSON> the Rapper - Pets [V1]", "artists": ["<PERSON>"], "producers": [], "notes": "Jam session with <PERSON> & Daniel freestyling over the same instrumental that <PERSON><PERSON><PERSON> would later work on for \"The Garden\" from <PERSON><PERSON>.", "length": "1492.85", "fileDate": 16365888, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/91a002a3b3ae8ace908d8c4e71285823", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91a002a3b3ae8ace908d8c4e71285823\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> the Rapper - Pets [V1]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"The Garden\"], \"description\": \"Jam session with <PERSON> <PERSON> Daniel freestyling over the same instrumental that <PERSON><PERSON><PERSON> would later work on for \\\"The Garden\\\" from Yandhi.\", \"date\": 16365888, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f0d3d4766c6150a273089b15c9d36733\", \"url\": \"https://api.pillowcase.su/api/download/f0d3d4766c6150a273089b15c9d36733\", \"size\": \"27.1 MB\", \"duration\": 1492.85}", "aliases": ["The Garden"], "size": "27.1 MB"}, {"id": "die-for", "name": "<PERSON> the Rapper - Die For [V2]", "artists": ["<PERSON>"], "producers": [], "notes": "Cutdown of the \"<PERSON><PERSON>\" jam session. Has extra <PERSON> vocals throughout that aren't heard in the previous version.", "length": "378.55", "fileDate": 17337024, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/741826bad1d28171b2e0a2b8c24f33a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/741826bad1d28171b2e0a2b8c24f33a1\", \"key\": \"Die For\", \"title\": \"<PERSON> the Rapper - Die For [V2]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"The Garden\", \"Pets\"], \"description\": \"Cutdown of the \\\"Pets\\\" jam session. Has extra <PERSON> vocals throughout that aren't heard in the previous version.\", \"date\": 17337024, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"23e87896023819af5c3f669cf59996c7\", \"url\": \"https://api.pillowcase.su/api/download/23e87896023819af5c3f669cf59996c7\", \"size\": \"9.26 MB\", \"duration\": 378.55}", "aliases": ["The Garden", "Pets"], "size": "9.26 MB"}, {"id": "pets-43", "name": "<PERSON> the Rapper - Pets [V3]", "artists": [], "producers": [], "notes": "OG Filename: Jam sess idea1 PETS jl 9-5\nHas completely different Chance vocals. Was thought to be made during the Good Ass Job sessions with <PERSON><PERSON><PERSON>, but it turned out it was made on the day of the full 24 minute jam session with <PERSON>, also despite it saying \"9-5\" in the filename (could just be an error). Leaked as part of a 62 gigabyte mass leak.", "length": "418.06", "fileDate": 17200512, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1b27c153e9524087669406c8c4e77a27", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1b27c153e9524087669406c8c4e77a27\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> the Rapper - Pets [V3]\", \"aliases\": [\"The Garden\"], \"description\": \"OG Filename: Jam sess idea1 PETS jl 9-5\\nHas completely different Chance vocals. Was thought to be made during the Good Ass Job sessions with <PERSON><PERSON><PERSON>, but it turned out it was made on the day of the full 24 minute jam session with <PERSON>, also despite it saying \\\"9-5\\\" in the filename (could just be an error). Leaked as part of a 62 gigabyte mass leak.\", \"date\": 17200512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"843ba1c01b64bca9b323a366f67e62a8\", \"url\": \"https://api.pillowcase.su/api/download/843ba1c01b64bca9b323a366f67e62a8\", \"size\": \"6.55 MB\", \"duration\": 418.06}", "aliases": ["The Garden"], "size": "6.55 MB"}, {"id": "accelerate", "name": "<PERSON> - Accelerate [V23]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG FIlename: Accelerate_Final Rough.02_08\nOfficial bounce found in the \"Accelerate\" sessions. Closely matches the previous entry, but there is no fully accurate bounce of that version, so we can't be for sure if it's the same.", "length": "214.63", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4318b9135980d1d426890e4e13a89fca", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4318b9135980d1d426890e4e13a89fca\", \"key\": \"Accelerate\", \"title\": \"<PERSON> - Accelerate [V23]\", \"artists\": \"(feat. <PERSON> $ign)\", \"aliases\": [\"Pick Up Your Speed\", \"Auxillary\"], \"description\": \"OG FIlename: Accelerate_Final Rough.02_08\\nOfficial bounce found in the \\\"Accelerate\\\" sessions. Closely matches the previous entry, but there is no fully accurate bounce of that version, so we can't be for sure if it's the same.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"97c1756a726e93221291dbb5e111e2df\", \"url\": \"https://api.pillowcase.su/api/download/97c1756a726e93221291dbb5e111e2df\", \"size\": \"6.64 MB\", \"duration\": 214.63}", "aliases": ["Pick Up Your Speed", "Auxillary"], "size": "6.64 MB"}, {"id": "accelerate-45", "name": "<PERSON> - Accelerate [V27]", "artists": ["Ty Dolla $ign", "2 Chainz"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: accell che drums 042417 v3\nOfficial bounce made by combining left and right tracks found in the \"Accelerate\" sessions.", "length": "267.57", "fileDate": 17038080, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e9c2a30af5e3c2893b922ec328ef5a62", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9c2a30af5e3c2893b922ec328ef5a62\", \"key\": \"Accelerate\", \"title\": \"<PERSON> - Accelerate [V27]\", \"artists\": \"(feat. <PERSON> $ign & 2 <PERSON><PERSON>) (prod. <PERSON>e <PERSON>)\", \"aliases\": [\"Pick Up Your Speed\", \"Auxillary\"], \"description\": \"OG Filename: accell che drums 042417 v3\\nOfficial bounce made by combining left and right tracks found in the \\\"Accelerate\\\" sessions.\", \"date\": 17038080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a8bfc0cd04bb3bb033cadbc7df6521de\", \"url\": \"https://api.pillowcase.su/api/download/a8bfc0cd04bb3bb033cadbc7df6521de\", \"size\": \"7.49 MB\", \"duration\": 267.57}", "aliases": ["Pick Up Your Speed", "Auxillary"], "size": "7.49 MB"}, {"id": "accelerate-46", "name": "<PERSON> - Accelerate [V29]", "artists": ["Ty Dolla $ign", "2 Chainz"], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG FIlename: Accelerate_Final Rough.03_10\nOfficial bounce found in the \"Accelerate\" sessions.", "length": "242.81", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/76bd9b0e2797f802c027c32738efaf64", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76bd9b0e2797f802c027c32738efaf64\", \"key\": \"Accelerate\", \"title\": \"<PERSON> - Accelerate [V29]\", \"artists\": \"(feat. <PERSON> $ign & 2 <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Pick Up Your Speed\", \"Auxillary\"], \"description\": \"OG FIlename: Accelerate_Final Rough.03_10\\nOfficial bounce found in the \\\"Accelerate\\\" sessions.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fd583dd57e0bb82096571e808fdf8a7f\", \"url\": \"https://api.pillowcase.su/api/download/fd583dd57e0bb82096571e808fdf8a7f\", \"size\": \"7.09 MB\", \"duration\": 242.81}", "aliases": ["Pick Up Your Speed", "Auxillary"], "size": "7.09 MB"}, {"id": "you-ain-t-no-model", "name": "Cy<PERSON><PERSON> - You Ain't No Model [V6]", "artists": ["Kanye West", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: YOU AINT NO MODEL-KY-3-2-17\n\"You Ain't No Model\" was given to <PERSON><PERSON><PERSON> after being scrapped by <PERSON><PERSON><PERSON>. Samples \"A Lifetime\" by The Brothers Of Soul and \"Takin' It Back\" by To<PERSON>.", "length": "215.07", "fileDate": 16983648, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1459f1484c3bec2fb4a05a63c492beec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1459f1484c3bec2fb4a05a63c492beec\", \"key\": \"You Ain't No Model\", \"title\": \"<PERSON><PERSON><PERSON> - You Ain't No Model [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Ty Dolla $ign)\", \"aliases\": [\"Model\", \"Model Type\"], \"description\": \"OG Filename: YOU AINT NO MODEL-KY-3-2-17\\n\\\"You Ain't No Model\\\" was given to <PERSON><PERSON><PERSON> after being scrapped by <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> \\\"A Lifetime\\\" by The Brothers Of Soul and \\\"Takin' It Back\\\" by <PERSON><PERSON>.\", \"date\": 16983648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"012b81627b12e25413e5c616d30132e5\", \"url\": \"https://api.pillowcase.su/api/download/012b81627b12e25413e5c616d30132e5\", \"size\": \"6.65 MB\", \"duration\": 215.07}", "aliases": ["Model", "Model Type"], "size": "6.65 MB"}, {"id": "model", "name": "CyHi - Model [V7]", "artists": ["Kanye West", "Ty Dolla $ign"], "producers": ["<PERSON>"], "notes": "OG Filename: Model- REF 03.06.18\nA version of \"You Ain't No Model\" from 2018. Intended for a CyHi project, possibly the 7-track Kanye-produced album he was initially planned to have release alongside <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>'s. Samples \"A Lifetime\" by Brothers Of Soul. Leaked as a bonus for the \"Welcome To My Life\" groupbuy but was purposely bounced in low quality in the sessions because the buy did not finish in time for this version to leak in CDQ. CDQ snippet was leaked March 4th, 2024.", "length": "307.49", "fileDate": 17095104, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8025f4503fc8eaea2d9b34a2144dbf01", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8025f4503fc8eaea2d9b34a2144dbf01\", \"key\": \"Model\", \"title\": \"CyHi - Model [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> $ign) (prod. <PERSON>)\", \"aliases\": [\"Model Type\", \"You Ain't No Model\"], \"description\": \"OG Filename: Model- REF 03.06.18\\nA version of \\\"You Ain't No Model\\\" from 2018. Intended for a CyHi project, possibly the 7-track Ka<PERSON>e-produced album he was initially planned to have release alongside <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>'s. Samples \\\"A Lifetime\\\" by Brothers Of Soul. Leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy but was purposely bounced in low quality in the sessions because the buy did not finish in time for this version to leak in CDQ. CDQ snippet was leaked March 4th, 2024.\", \"date\": 17095104, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"5b89fd3ddf1382ffd695e550d84136bf\", \"url\": \"https://api.pillowcase.su/api/download/5b89fd3ddf1382ffd695e550d84136bf\", \"size\": \"8.12 MB\", \"duration\": 307.49}", "aliases": ["Model Type", "You Ain't No Model"], "size": "8.12 MB"}, {"id": "model-49", "name": "CyHi - Model [V7]", "artists": ["Kanye West", "Ty Dolla $ign"], "producers": ["<PERSON>"], "notes": "OG Filename: Model- REF 03.06.18\nA version of \"You Ain't No Model\" from 2018. Intended for a CyHi project, possibly the 7-track Kanye-produced album he was initially planned to have release alongside <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>'s. Samples \"A Lifetime\" by Brothers Of Soul. Leaked as a bonus for the \"Welcome To My Life\" groupbuy but was purposely bounced in low quality in the sessions because the buy did not finish in time for this version to leak in CDQ. CDQ snippet was leaked March 4th, 2024.", "length": "42.5", "fileDate": 17095104, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/05d6aaeb1579cfcc1a27e532e841e21a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/05d6aaeb1579cfcc1a27e532e841e21a\", \"key\": \"Model\", \"title\": \"CyHi - Model [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> $ign) (prod. <PERSON>)\", \"aliases\": [\"Model Type\", \"You Ain't No Model\"], \"description\": \"OG Filename: Model- REF 03.06.18\\nA version of \\\"You Ain't No Model\\\" from 2018. Intended for a CyHi project, possibly the 7-track Ka<PERSON>e-produced album he was initially planned to have release alongside <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>'s. Samples \\\"A Lifetime\\\" by Brothers Of Soul. Leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy but was purposely bounced in low quality in the sessions because the buy did not finish in time for this version to leak in CDQ. CDQ snippet was leaked March 4th, 2024.\", \"date\": 17095104, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"c78f27aac0cfaec32d8fabac51e814b2\", \"url\": \"https://api.pillowcase.su/api/download/c78f27aac0cfaec32d8fabac51e814b2\", \"size\": \"3.88 MB\", \"duration\": 42.5}", "aliases": ["Model Type", "You Ain't No Model"], "size": "3.88 MB"}, {"id": "model-50", "name": "CyHi - Model [V7]", "artists": ["Kanye West", "Ty Dolla $ign"], "producers": ["<PERSON>"], "notes": "OG Filename: Model- REF 03.06.18\nA version of \"You Ain't No Model\" from 2018. Intended for a CyHi project, possibly the 7-track Kanye-produced album he was initially planned to have release alongside <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>'s. Samples \"A Lifetime\" by Brothers Of Soul. Leaked as a bonus for the \"Welcome To My Life\" groupbuy but was purposely bounced in low quality in the sessions because the buy did not finish in time for this version to leak in CDQ. CDQ snippet was leaked March 4th, 2024.", "length": "10.79", "fileDate": 17095104, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/6fecb87a1107c2515c05aaa75e4373b0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6fecb87a1107c2515c05aaa75e4373b0\", \"key\": \"Model\", \"title\": \"CyHi - Model [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> $ign) (prod. <PERSON>)\", \"aliases\": [\"Model Type\", \"You Ain't No Model\"], \"description\": \"OG Filename: Model- REF 03.06.18\\nA version of \\\"You Ain't No Model\\\" from 2018. Intended for a CyHi project, possibly the 7-track Ka<PERSON>e-produced album he was initially planned to have release alongside <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>'s. <PERSON>ples \\\"A Lifetime\\\" by Brothers Of Soul. Leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy but was purposely bounced in low quality in the sessions because the buy did not finish in time for this version to leak in CDQ. CDQ snippet was leaked March 4th, 2024.\", \"date\": 17095104, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7b9ccc6e4c3566d0ab0e7feddb9a5247\", \"url\": \"https://api.pillowcase.su/api/download/7b9ccc6e4c3566d0ab0e7feddb9a5247\", \"size\": \"3.38 MB\", \"duration\": 10.79}", "aliases": ["Model Type", "You Ain't No Model"], "size": "3.38 MB"}, {"id": "model-51", "name": "CyHi - Model [V8]", "artists": ["Kanye West"], "producers": ["<PERSON>"], "notes": "An open verse version of \"Model\", found in <PERSON><PERSON><PERSON>'s video \"BARCODE EP 1\". Unknown when this version is from, but it was used in a video from 2020.", "length": "90.46", "fileDate": 16045344, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/eb029d08d9fb2413687574c9d02b3984", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eb029d08d9fb2413687574c9d02b3984\", \"key\": \"Model\", \"title\": \"CyHi - Model [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Model Type\", \"You Ain't No Model\"], \"description\": \"An open verse version of \\\"Model\\\", found in CyH<PERSON>'s video \\\"BARCODE EP 1\\\". Unknown when this version is from, but it was used in a video from 2020.\", \"date\": 16045344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"66bf5683ec582292979a34d1e635e08b\", \"url\": \"https://api.pillowcase.su/api/download/66bf5683ec582292979a34d1e635e08b\", \"size\": \"4.65 MB\", \"duration\": 90.46}", "aliases": ["Model Type", "You Ain't No Model"], "size": "4.65 MB"}, {"id": "i-hope", "name": "PARTYNEXTDOOR - <PERSON> Hope [V1]", "artists": [], "producers": ["Kanye West", "40"], "notes": "The original version of <PERSON><PERSON>. There are no <PERSON> or <PERSON><PERSON><PERSON> verses on this song, only <PERSON><PERSON>.", "length": "171.48", "fileDate": 16740000, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ce5e91e687d96346da615e756b05c902", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ce5e91e687d96346da615e756b05c902\", \"key\": \"<PERSON> Hope\", \"title\": \"PARTYNEXTDOOR - <PERSON> [V1]\", \"artists\": \"(prod. <PERSON>ny<PERSON> & 40)\", \"aliases\": [\"Glow\"], \"description\": \"The original version of Glow. There are no <PERSON> or <PERSON><PERSON><PERSON> verses on this song, only PND.\", \"date\": 16740000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8294befeced77c788ae588d9a5c03152\", \"url\": \"https://api.pillowcase.su/api/download/8294befeced77c788ae588d9a5c03152\", \"size\": \"5.95 MB\", \"duration\": 171.48}", "aliases": ["Glow"], "size": "5.95 MB"}, {"id": "-53", "name": "<PERSON> and the Lights - ??? [V1]", "artists": [], "producers": [], "notes": "Original song recorded by <PERSON> in one take on prismizer. 4 bars of this song was given to <PERSON><PERSON><PERSON> as a sample for \"I Thought About Killing You\". Recorded in 2017. Played by <PERSON> on his Twitch.", "length": "157.2", "fileDate": 16256160, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/cc5064526d3f0bbcc601c2c45539d788", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc5064526d3f0bbcc601c2c45539d788\", \"key\": \"???\", \"title\": \"<PERSON> and the Lights - ??? [V1]\", \"aliases\": [\"I Thought About Killing You\"], \"description\": \"Original song recorded by <PERSON> in one take on prismizer. 4 bars of this song was given to <PERSON><PERSON><PERSON> as a sample for \\\"I Thought About Killing You\\\". Recorded in 2017. Played by <PERSON> on his Twitch.\", \"date\": 16256160, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d3a92c141ce297629caa7f7f1025a90a\", \"url\": \"https://api.pillowcase.su/api/download/d3a92c141ce297629caa7f7f1025a90a\", \"size\": \"5.72 MB\", \"duration\": 157.2}", "aliases": ["I Thought About Killing You"], "size": "5.72 MB"}, {"id": "ass-shot", "name": "French Montana - <PERSON><PERSON> Shot [V4]", "artists": ["Kanye West", "<PERSON>'ron"], "producers": [], "notes": "OG Filename: 13.<PERSON><PERSON> SHOT feat. <PERSON><PERSON><PERSON> and <PERSON> 2\nMade for French Montanan's album Jungle Rules, in January 2017. Features updated production and better mixing.", "length": "232.94", "fileDate": 16765056, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/879489a46ca65f53dbc82bc043971de8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/879489a46ca65f53dbc82bc043971de8\", \"key\": \"Ass Shot\", \"title\": \"French Montana - Ass Shot [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>ron)\", \"aliases\": [\"Ass Shots\", \"Bang\"], \"description\": \"OG Filename: 13.ASS SHOT feat. <PERSON><PERSON><PERSON> and <PERSON> 2\\nMade for French Montanan's album Jungle Rules, in January 2017. Features updated production and better mixing.\", \"date\": 16765056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"acb656537e261614a0cc385340875cf2\", \"url\": \"https://api.pillowcase.su/api/download/acb656537e261614a0cc385340875cf2\", \"size\": \"6.93 MB\", \"duration\": 232.94}", "aliases": ["Ass Shots", "<PERSON>"], "size": "6.93 MB"}, {"id": "cudi-montage", "name": "<PERSON> - <PERSON><PERSON> [V3]", "artists": ["070 Shake"], "producers": ["<PERSON>", "<PERSON> da <PERSON>", "<PERSON>"], "notes": "Has vocals from 070 <PERSON> and mumble vocals from <PERSON>. Snippet leaked September 20th, 2024.", "length": "15.07", "fileDate": 17267904, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e0e6671ab5c1a4541d0d0b40d318e1df", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e0e6671ab5c1a4541d0d0b40d318e1df\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> - <PERSON><PERSON> [V3]\", \"artists\": \"(feat. 070 Shake) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Has vocals from 070 <PERSON> and mumble vocals from <PERSON>. Snippet leaked September 20th, 2024.\", \"date\": 17267904, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4431a650404f1a110570fd33e7e676de\", \"url\": \"https://api.pillowcase.su/api/download/4431a650404f1a110570fd33e7e676de\", \"size\": \"3.45 MB\", \"duration\": 15.07}", "aliases": [], "size": "3.45 MB"}, {"id": "reborn", "name": "<PERSON> - <PERSON> [V2]", "artists": ["<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "Version with vocals from <PERSON><PERSON><PERSON><PERSON>, alongside alternate production. Snippet leaked September 4th, 2024.", "length": "14.97", "fileDate": 17254080, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c921d12ff8d3d43b0eb1cd4248972495", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c921d12ff8d3d43b0eb1cd4248972495\", \"key\": \"Reborn\", \"title\": \"<PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Version with vocals from <PERSON><PERSON><PERSON><PERSON>, alongside alternate production. Snippet leaked September 4th, 2024.\", \"date\": 17254080, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"05c1a29d7225c17342a92271bfde0b69\", \"url\": \"https://api.pillowcase.su/api/download/05c1a29d7225c17342a92271bfde0b69\", \"size\": \"3.44 MB\", \"duration\": 14.97}", "aliases": [], "size": "3.44 MB"}, {"id": "ooooppps", "name": "<PERSON> - <PERSON><PERSON><PERSON> [V1]", "artists": ["<PERSON> the Rapper"], "producers": ["Polo Boy <PERSON>"], "notes": "OG Filename: oooop<PERSON> ref1a jl 9-19\n<PERSON><PERSON> <PERSON> vocals and an open verse. Would be later be recorded by <PERSON><PERSON><PERSON> during the Good Ass Job sessions. Leaked apart of a 62 gigabyte mass leak.", "length": "140.86", "fileDate": 17200512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/f0984f17a3730d816d49aac223fb7eac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0984f17a3730d816d49aac223fb7eac\", \"key\": \"Oooop<PERSON>\", \"title\": \"<PERSON> <PERSON><PERSON><PERSON> [V1]\", \"artists\": \"(feat. <PERSON> the Rapper) (prod. <PERSON>)\", \"aliases\": [\"OOPS\"], \"description\": \"OG Filename: ooooppps ref1a jl 9-19\\nHs Chance vocals and an open verse. Would be later be recorded by <PERSON><PERSON><PERSON> during the Good Ass Job sessions. Leaked apart of a 62 gigabyte mass leak.\", \"date\": 17200512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"46052ac04e378922fcb5064cb210b70e\", \"url\": \"https://api.pillowcase.su/api/download/46052ac04e378922fcb5064cb210b70e\", \"size\": \"5.46 MB\", \"duration\": 140.86}", "aliases": ["OOPS"], "size": "5.46 MB"}, {"id": "bbo", "name": "Migos - BBO (Bad Bitches Only) [V1]", "artists": ["21 Savage"], "producers": ["Quavo", "Bud<PERSON> Bless", "<PERSON> <PERSON><PERSON>", "Kanye West"], "notes": "Has alternate 21 Savage and Quavo lines. Leaked in 2019.", "length": "284.92", "fileDate": 15463008, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/78e960a1c67d82b5a6150a17ef9f45c4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/78e960a1c67d82b5a6150a17ef9f45c4\", \"key\": \"B<PERSON> (Bad Bitches Only)\", \"title\": \"<PERSON><PERSON> - B<PERSON> (Bad Bitches Only) [V1]\", \"artists\": \"(feat. 21 <PERSON>) (prod<PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"Has alternate 21 Savage and Quavo lines. Leaked in 2019.\", \"date\": 15463008, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"013e1843018b3f21aa8ae642813e9f96\", \"url\": \"https://api.pillowcase.su/api/download/013e1843018b3f21aa8ae642813e9f96\", \"size\": \"7.76 MB\", \"duration\": 284.92}", "aliases": [], "size": "7.76 MB"}, {"id": "-59", "name": "<PERSON><PERSON><PERSON> - ???", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> talib kweli chop 5\nTrack from a scrapped 4 song Kanye-produced Talib Kweli EP. Samples \"Try Something New\" by <PERSON><PERSON>. Snippet leaked September 19th, 2021.", "length": "8.62", "fileDate": 16320096, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c33146e2a1dc5852f82fa7f1d047a4c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c33146e2a1dc5852f82fa7f1d047a4c2\", \"key\": \"???\", \"title\": \"<PERSON><PERSON><PERSON> ???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Chop 5\"], \"description\": \"OG Filename: KW talib kweli chop 5\\nTrack from a scrapped 4 song Kanye-produced Talib <PERSON>weli EP. Samples \\\"Try Something New\\\" by <PERSON><PERSON>. Snippet leaked September 19th, 2021.\", \"date\": 16320096, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"476a873dbef92f2817d3d3f292562405\", \"url\": \"https://api.pillowcase.su/api/download/476a873dbef92f2817d3d3f292562405\", \"size\": \"3.34 MB\", \"duration\": 8.62}", "aliases": ["Chop 5"], "size": "3.34 MB"}, {"id": "-60", "name": "<PERSON><PERSON><PERSON> - ???", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> talib kweli chop 7\nTrack from a scrapped 4 song Kanye-produced Talib Kweli EP. Samples \"Liar\" by Three Dog Night. Snippet leaked September 19th, 2021. Instrumental leaked in June 2022.", "length": "97.13", "fileDate": 16320096, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/893a2dcc81a8a101f88fb0517dc3a90d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/893a2dcc81a8a101f88fb0517dc3a90d\", \"key\": \"???\", \"title\": \"<PERSON><PERSON><PERSON> ???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Chop 7\"], \"description\": \"OG Filename: KW talib kweli chop 7\\nTrack from a scrapped 4 song Kanye-produced Talib Kweli EP. Samples \\\"Liar\\\" by Three Dog Night. Snippet leaked September 19th, 2021. Instrumental leaked in June 2022.\", \"date\": 16320096, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"75af7cb09b7839a71e5d89b2263d95f8\", \"url\": \"https://api.pillowcase.su/api/download/75af7cb09b7839a71e5d89b2263d95f8\", \"size\": \"4.76 MB\", \"duration\": 97.13}", "aliases": ["Chop 7"], "size": "4.76 MB"}, {"id": "-61", "name": "<PERSON><PERSON><PERSON> - ???", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> talib kweli chop 7\nTrack from a scrapped 4 song Kanye-produced Talib Kweli EP. Samples \"Liar\" by Three Dog Night. Snippet leaked September 19th, 2021. Instrumental leaked in June 2022.", "length": "8.62", "fileDate": 16320096, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/9cca431a7645271448d2013c55171050", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9cca431a7645271448d2013c55171050\", \"key\": \"???\", \"title\": \"<PERSON><PERSON><PERSON> ???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Chop 7\"], \"description\": \"OG Filename: KW talib kweli chop 7\\nTrack from a scrapped 4 song Kanye-produced Talib Kweli EP. Samples \\\"Liar\\\" by Three Dog Night. Snippet leaked September 19th, 2021. Instrumental leaked in June 2022.\", \"date\": 16320096, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c537b4ba2a69570325ab8cecc56e2cde\", \"url\": \"https://api.pillowcase.su/api/download/c537b4ba2a69570325ab8cecc56e2cde\", \"size\": \"3.34 MB\", \"duration\": 8.62}", "aliases": ["Chop 7"], "size": "3.34 MB"}, {"id": "-62", "name": "<PERSON><PERSON><PERSON> - ???", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> talib kweli chop 8\nTrack from a scrapped 4 song Kanye-produced Talib <PERSON>weli EP. Samples \"Black And White\" by Three Dog Night. Previewed by <PERSON><PERSON><PERSON> on his IG, with another snippet leaking on September 19th 2021.", "length": "89.42", "fileDate": 17170272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/b30ea163815ce0b1444beaf9cc835cb7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b30ea163815ce0b1444beaf9cc835cb7\", \"key\": \"???\", \"title\": \"<PERSON><PERSON><PERSON> ???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Chop 8\"], \"description\": \"OG Filename: KW talib kweli chop 8\\nTrack from a scrapped 4 song Kanye-produced Talib <PERSON>weli EP. Samples \\\"Black And White\\\" by Three Dog Night. Previewed by <PERSON><PERSON><PERSON> on his IG, with another snippet leaking on September 19th 2021.\", \"date\": 17170272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0819517159fd6c429e4f4581bf5e609a\", \"url\": \"https://api.pillowcase.su/api/download/0819517159fd6c429e4f4581bf5e609a\", \"size\": \"4.64 MB\", \"duration\": 89.42}", "aliases": ["Chop 8"], "size": "4.64 MB"}, {"id": "-63", "name": "<PERSON><PERSON><PERSON> - ???", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> talib kweli chop 11\nTrack from a scrapped 4 song <PERSON><PERSON><PERSON>-produced Talib <PERSON>li EP. Sample chop reused on Abstract Mindstate's song \"Salutations (Intro)\" and <PERSON><PERSON>'s song \"Made It\". Samples \"He'll Never Love You Like I Do\" by The Spinners. Snippet leaked on September 19th, 2021.", "length": "8.62", "fileDate": 16320096, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/0c06f5c62f6f0c0dd7885889d16c8fee", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0c06f5c62f6f0c0dd7885889d16c8fee\", \"key\": \"???\", \"title\": \"<PERSON><PERSON><PERSON> ???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Chop 11\"], \"description\": \"OG Filename: <PERSON>W talib kweli chop 11\\nTrack from a scrapped 4 song Kanye-produced Talib <PERSON>li EP. Sample chop reused on Abstract Mindstate's song \\\"Salutations (Intro)\\\" and <PERSON><PERSON>'s song \\\"Made It\\\". Samples \\\"He'll Never Love You Like I Do\\\" by The Spinners. Snippet leaked on September 19th, 2021.\", \"date\": 16320096, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bdbc795d7a4cf49cb42afc7a8143c86d\", \"url\": \"https://api.pillowcase.su/api/download/bdbc795d7a4cf49cb42afc7a8143c86d\", \"size\": \"3.34 MB\", \"duration\": 8.62}", "aliases": ["Chop 11"], "size": "3.34 MB"}, {"id": "chop-26", "name": "<PERSON><PERSON> - <PERSON><PERSON> 26 [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON> 26 ref 1\nWRLDFMS <PERSON> reference track. Metadata dates it as being from between January 20th and 29th of 2018. Uses \"Chop 26\".", "length": "246.24", "fileDate": 16755552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/33a9164ebfcac3d3709c460528a4bfc1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/33a9164ebfcac3d3709c460528a4bfc1\", \"key\": \"Chop 26\", \"title\": \"<PERSON><PERSON> 26 [V1]\", \"artists\": \"(ref. The WRLDFM<PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"3Way\"], \"description\": \"OG Filename: <PERSON> 26 ref 1\\nWRLDFMS <PERSON> reference track. Metadata dates it as being from between January 20th and 29th of 2018. Uses \\\"Chop 26\\\".\", \"date\": 16755552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cb4f01326b1fbef374d412654293b5e3\", \"url\": \"https://api.pillowcase.su/api/download/cb4f01326b1fbef374d412654293b5e3\", \"size\": \"7.14 MB\", \"duration\": 246.24}", "aliases": ["3Way"], "size": "7.14 MB"}, {"id": "sisqosong", "name": "<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: NOVA SisqoSong V1 KW\nNOVA WAV reference track.", "length": "243.65", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/59dc49c686c7d276f884f131fd9b974e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/59dc49c686c7d276f884f131fd9b974e\", \"key\": \"<PERSON>s<PERSON><PERSON><PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V2]\", \"artists\": \"(ref. NOVA WAV) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"3Way\"], \"description\": \"OG Filename: NOVA SisqoSong V1 KW\\nNOVA WAV reference track.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"75f52ff49ec1bba7be3b4854b75d157b\", \"url\": \"https://api.pillowcase.su/api/download/75f52ff49ec1bba7be3b4854b75d157b\", \"size\": \"7.1 MB\", \"duration\": 243.65}", "aliases": ["3Way"], "size": "7.1 MB"}, {"id": "body", "name": "<PERSON><PERSON> - <PERSON> [V1]", "artists": [], "producers": [], "notes": "OG Filename: NOVA Go BodyRedo 1\nEarly NOVA WAV reference track.", "length": "161.39", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/50e85767412188b63a5a6ceda277366a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/50e85767412188b63a5a6ceda277366a\", \"key\": \"Body\", \"title\": \"<PERSON><PERSON> [V1]\", \"artists\": \"(ref. NOVA WAV)\", \"description\": \"OG Filename: NOVA Go BodyRedo 1\\nEarly NOVA WAV reference track.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"980b77bdd9c38c71e0056ea9a81adeb7\", \"url\": \"https://api.pillowcase.su/api/download/980b77bdd9c38c71e0056ea9a81adeb7\", \"size\": \"5.79 MB\", \"duration\": 161.39}", "aliases": [], "size": "5.79 MB"}, {"id": "body-67", "name": "<PERSON><PERSON> - <PERSON> [V2]", "artists": [], "producers": [], "notes": "OG Filename: NOVA Go BodyRedo SadeNote \nLater NOVA WAV reference track.", "length": "135.17", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4a6b10ac243200421a24b0e45cc33050", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a6b10ac243200421a24b0e45cc33050\", \"key\": \"Body\", \"title\": \"<PERSON><PERSON> [V2]\", \"artists\": \"(ref. NOVA WAV)\", \"description\": \"OG Filename: NOVA Go BodyRedo SadeNote \\nLater NOVA WAV reference track.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dbb9576ef9c20033c43b87f06cfcf693\", \"url\": \"https://api.pillowcase.su/api/download/dbb9576ef9c20033c43b87f06cfcf693\", \"size\": \"5.37 MB\", \"duration\": 135.17}", "aliases": [], "size": "5.37 MB"}, {"id": "body-68", "name": "<PERSON><PERSON> - <PERSON> [V3]", "artists": [], "producers": [], "notes": "OG Filename: NOVA Go BodyRedo SadeNote LowerYou\nFile data shows it was bounced with Protools on January 2nd 2018.", "length": "136.62", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/7038cecaa25565a3eb177bd90a3afde4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7038cecaa25565a3eb177bd90a3afde4\", \"key\": \"Body\", \"title\": \"<PERSON><PERSON> [V3]\", \"artists\": \"(ref. NOVA WAV)\", \"description\": \"OG Filename: NOVA Go BodyRedo SadeNote LowerYou\\nFile data shows it was bounced with Protools on January 2nd 2018.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f4284ab52298d5a077ab1c161ce64c84\", \"url\": \"https://api.pillowcase.su/api/download/f4284ab52298d5a077ab1c161ce64c84\", \"size\": \"5.39 MB\", \"duration\": 136.62}", "aliases": [], "size": "5.39 MB"}, {"id": "burn", "name": "<PERSON><PERSON> - <PERSON>", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Burn Hook Change ref 1 \nNOVA WAV reference track. Uses \"Chop 3\". Made sometime in 2018.", "length": "93.07", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/9315d42aa361a4a1bbd94cb0cbec1aac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9315d42aa361a4a1bbd94cb0cbec1aac\", \"key\": \"Burn\", \"title\": \"<PERSON><PERSON>\", \"artists\": \"(ref. NOVA WAV) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Burn Hook Change ref 1 \\nNOVA WAV reference track. Uses \\\"Chop 3\\\". Made sometime in 2018.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0b252c998c8333974d47c6ee4542c1ca\", \"url\": \"https://api.pillowcase.su/api/download/0b252c998c8333974d47c6ee4542c1ca\", \"size\": \"4.69 MB\", \"duration\": 93.07}", "aliases": [], "size": "4.69 MB"}, {"id": "cold-blooded", "name": "<PERSON><PERSON> - Cold Blooded [V5]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Cold Blooded KW V1 (8.1.17)\n2015 <PERSON><PERSON> song revisited for K.T.S.E., now with new Kanye production made during Wyoming sessions. Was seen being made in an unreleased clip of jeen-yuhs, posted by TIME. Leaked in 2021 as a reference track for <PERSON><PERSON><PERSON> but as seen in the video, it's a <PERSON><PERSON> song.", "length": "125.52", "fileDate": 16296768, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/5688adbd8c156770d51cd468179ac442", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5688adbd8c156770d51cd468179ac442\", \"key\": \"Cold Blooded\", \"title\": \"<PERSON><PERSON> - Cold Blooded [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Cold Blooded KW V1 (8.1.17)\\n2015 <PERSON><PERSON> song revisited for K.T.S.E., now with new Kanye production made during Wyoming sessions. Was seen being made in an unreleased clip of jeen-yuhs, posted by TIME. Leaked in 2021 as a reference track for <PERSON><PERSON><PERSON> but as seen in the video, it's a <PERSON><PERSON> song.\", \"date\": 16296768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a03f5f4b50d8c35ba4e926a8537a022\", \"url\": \"https://api.pillowcase.su/api/download/4a03f5f4b50d8c35ba4e926a8537a022\", \"size\": \"5.21 MB\", \"duration\": 125.52}", "aliases": [], "size": "5.21 MB"}, {"id": "hurry", "name": "<PERSON><PERSON> - <PERSON> [V1]", "artists": [], "producers": ["BongoByTheWay", "Kanye West", "ROBOTSCOTT", "Triangle Park"], "notes": "OG Filename: HURRY ANT GOGO SCOTT BONGO TRIANGLE KW Chop 23\nCan be heard in the later version of <PERSON><PERSON><PERSON> mumbling, likely a completely solo <PERSON><PERSON> Clem<PERSON> reference track. Was unbounced in the session for quite sometime, but was finally bounced by <PERSON><PERSON> on December 3rd, 2022.", "length": "109.97", "fileDate": 16700256, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ade37288172bd32a77911b5582a17bcc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ade37288172bd32a77911b5582a17bcc\", \"key\": \"Hurry\", \"title\": \"<PERSON><PERSON> [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Ka<PERSON><PERSON>, ROBOTSCOTT & Triangle Park)\", \"description\": \"OG Filename: HURRY ANT GOGO SCOTT BONGO TRIANGLE KW Chop 23\\nCan be heard in the later version of <PERSON><PERSON><PERSON> mumbling, likely a completely solo <PERSON><PERSON> reference track. Was unbounced in the session for quite sometime, but was finally bounced by <PERSON><PERSON> on December 3rd, 2022.\", \"date\": 16700256, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5a9038bbf234b9993ba08b9ce593aa92\", \"url\": \"https://api.pillowcase.su/api/download/5a9038bbf234b9993ba08b9ce593aa92\", \"size\": \"2.19 MB\", \"duration\": 109.97}", "aliases": [], "size": "2.19 MB"}, {"id": "hurry-72", "name": "<PERSON><PERSON> [V2]", "artists": [], "producers": ["BongoByTheWay", "Kanye West", "ROBOTSCOTT", "Triangle Park"], "notes": "OG Filename: HURRY ANT GOGO SCOTT BONGO TRIANGLE PIANO ORGAN BASS WHIRLI KW Chop 23\nSimilar to the earlier version however it has extra production and the fade out is different.", "length": "109.97", "fileDate": 16756416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2cfc8ac84cb2427798fe56ee0e115d7c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2cfc8ac84cb2427798fe56ee0e115d7c\", \"key\": \"Hurry\", \"title\": \"<PERSON><PERSON> [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BongoBy<PERSON>he<PERSON>, Kanye West, ROBOTSCOTT & Triangle Park)\", \"description\": \"OG Filename: HURRY ANT GOGO SCOTT BONGO TRIANGLE PIANO ORGAN BASS WHIRLI KW Chop 23\\nSimilar to the earlier version however it has extra production and the fade out is different.\", \"date\": 16756416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fcfcb90eab4b448e3c08eda0899b4800\", \"url\": \"https://api.pillowcase.su/api/download/fcfcb90eab4b448e3c08eda0899b4800\", \"size\": \"2.19 MB\", \"duration\": 109.97}", "aliases": [], "size": "2.19 MB"}, {"id": "hurry-73", "name": "<PERSON><PERSON> [V3]", "artists": ["Kanye West"], "producers": ["Kanye West", "BoogzDaBeast", "MIKE DEAN"], "notes": "3 minute longer early demo with mumbling. <PERSON><PERSON>' reference track can be heard in the background, <PERSON><PERSON><PERSON> is mumbling to find a flow. Totally <PERSON><PERSON><PERSON> solo besides <PERSON><PERSON> background vocals.", "length": "386.64", "fileDate": 15609024, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/05becaa98ad3bd0043b25fed1386e1d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/05becaa98ad3bd0043b25fed1386e1d8\", \"key\": \"Hurry\", \"title\": \"<PERSON><PERSON> [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & MIKE DEAN)\", \"description\": \"3 minute longer early demo with mumbling. <PERSON><PERSON>' reference track can be heard in the background, <PERSON><PERSON><PERSON> is mumbling to find a flow. Totally <PERSON><PERSON><PERSON> solo besides <PERSON><PERSON> background vocals.\", \"date\": 15609024, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"18bdc7d9c4367813a4fa74107ce72499\", \"url\": \"https://api.pillowcase.su/api/download/18bdc7d9c4367813a4fa74107ce72499\", \"size\": \"6.62 MB\", \"duration\": 386.64}", "aliases": [], "size": "6.62 MB"}, {"id": "hurry-74", "name": "<PERSON><PERSON> - <PERSON> [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "MIKE DEAN"], "notes": "Consequence reference track. Has the same lyrics used by <PERSON><PERSON><PERSON><PERSON> <PERSON> played several time over the years on Instagram Live. Longer snippet played July 18th, 2024.", "length": "", "fileDate": 17212608, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4ee968e344cc36851b8c43082f378241", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4ee968e344cc36851b8c43082f378241\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V4]\", \"artists\": \"(ref. Consequence) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & MIKE DEAN)\", \"description\": \"Consequence reference track. Has the same lyrics used by <PERSON><PERSON><PERSON><PERSON> <PERSON> played several time over the years on Instagram Live. Longer snippet played July 18th, 2024.\", \"date\": 17212608, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "hurry-75", "name": "<PERSON><PERSON> - <PERSON> [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "MIKE DEAN"], "notes": "Consequence reference track. Has the same lyrics used by <PERSON><PERSON><PERSON><PERSON> <PERSON> played several time over the years on Instagram Live. Longer snippet played July 18th, 2024.", "length": "110.74", "fileDate": 17212608, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/700d096170c51a1fcd2bd0a57b8d77b1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/700d096170c51a1fcd2bd0a57b8d77b1\", \"key\": \"Hurry\", \"title\": \"<PERSON><PERSON> [V4]\", \"artists\": \"(ref. Consequence) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & MIKE DEAN)\", \"description\": \"Consequence reference track. Has the same lyrics used by <PERSON><PERSON><PERSON><PERSON> <PERSON> played several time over the years on Instagram Live. Longer snippet played July 18th, 2024.\", \"date\": 17212608, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"bdfbb280c87f52f03389b4646cefa08e\", \"url\": \"https://api.pillowcase.su/api/download/bdfbb280c87f52f03389b4646cefa08e\", \"size\": \"2.2 MB\", \"duration\": 110.74}", "aliases": [], "size": "2.2 MB"}, {"id": "hurry-76", "name": "<PERSON><PERSON> - <PERSON> [V5]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "MIKE DEAN"], "notes": "Version with <PERSON><PERSON> doing reference vocals for <PERSON><PERSON> and <PERSON><PERSON><PERSON> doing reference vocals for <PERSON><PERSON><PERSON>'s verse.", "length": "188.89", "fileDate": 15609024, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c6962f924a18fcb6d3d4851126745572", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c6962f924a18fcb6d3d4851126745572\", \"key\": \"Hurry\", \"title\": \"<PERSON><PERSON> [V5]\", \"artists\": \"(ref. <PERSON><PERSON> & Myk<PERSON>) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & MIKE DEAN)\", \"description\": \"Version with <PERSON><PERSON> doing reference vocals for <PERSON><PERSON> and <PERSON><PERSON><PERSON> doing reference vocals for <PERSON><PERSON><PERSON>'s verse.\", \"date\": 15609024, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"30684d276856fd127bb4b8793d357ac1\", \"url\": \"https://api.pillowcase.su/api/download/30684d276856fd127bb4b8793d357ac1\", \"size\": \"3.45 MB\", \"duration\": 188.89}", "aliases": [], "size": "3.45 MB"}, {"id": "hurry-77", "name": "<PERSON><PERSON> - <PERSON> [V6]", "artists": ["Kanye West"], "producers": ["Kanye West", "BoogzDaBeast", "MIKE DEAN"], "notes": "Version with <PERSON><PERSON> doing reference vocals for <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s finished verse with some extra lines not present on the final version. Leaked alongside its stems.", "length": "193.59", "fileDate": 15609024, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/b354390f579a1be482ac2de47b57d753", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b354390f579a1be482ac2de47b57d753\", \"key\": \"Hurry\", \"title\": \"<PERSON><PERSON> [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & MIKE DEAN)\", \"description\": \"Version with <PERSON><PERSON> doing reference vocals for <PERSON><PERSON> and <PERSON><PERSON><PERSON>'s finished verse with some extra lines not present on the final version. Leaked alongside its stems.\", \"date\": 15609024, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9b0bb2511f8030ca58f00e4df122f706\", \"url\": \"https://api.pillowcase.su/api/download/9b0bb2511f8030ca58f00e4df122f706\", \"size\": \"3.53 MB\", \"duration\": 193.59}", "aliases": [], "size": "3.53 MB"}, {"id": "issues-hold-on", "name": "<PERSON><PERSON> - Issues / Hold On", "artists": [], "producers": ["Kanye West"], "notes": "Earlier version using a different vocal take and a different outro.", "length": "8.62", "fileDate": 16295904, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/12de94d50dcc6f76a2636889357ad4fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/12de94d50dcc6f76a2636889357ad4fd\", \"key\": \"Issues / Hold On\", \"title\": \"<PERSON><PERSON> - Issues / Hold On\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Earlier version using a different vocal take and a different outro.\", \"date\": 16295904, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a5621efcb57f5f265fd1a2ea35f0f152\", \"url\": \"https://api.pillowcase.su/api/download/a5621efcb57f5f265fd1a2ea35f0f152\", \"size\": \"569 kB\", \"duration\": 8.62}", "aliases": [], "size": "569 kB"}, {"id": "if-you-know-you-know", "name": "<PERSON><PERSON><PERSON> T - If You Know You Know [V2]", "artists": [], "producers": ["<PERSON>", "BNYX"], "notes": "Early version of the song, pre-Kany<PERSON>. Originally recorded in November 2016, and production was added in January 2017.", "length": "207.31", "fileDate": 15770592, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/31a2d52058c910ddff1d224437ce12ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/31a2d52058c910ddff1d224437ce12ac\", \"key\": \"If You Know You Know\", \"title\": \"<PERSON>usha T - If You Know You Know [V2]\", \"artists\": \"(prod. <PERSON> & BNYX)\", \"description\": \"Early version of the song, pre-<PERSON>ny<PERSON>. Originally recorded in November 2016, and production was added in January 2017.\", \"date\": 15770592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9dbe7ae5160c839568683ea84c0ca192\", \"url\": \"https://api.pillowcase.su/api/download/9dbe7ae5160c839568683ea84c0ca192\", \"size\": \"4.2 MB\", \"duration\": 207.31}", "aliases": [], "size": "4.2 MB"}, {"id": "never-woulda-made-it", "name": "<PERSON><PERSON> - Never Woulda Made It [V1]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: NOVA NeverWouldaMadeIt 2 1 18 KW\nEarlier NOVA WAV \"Never Would Have Made It\" reference track.", "length": "101.95", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2e9d4e60fc128c880a3dfc3b63469338", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2e9d4e60fc128c880a3dfc3b63469338\", \"key\": \"Never Woulda Made It\", \"title\": \"<PERSON><PERSON> - <PERSON> Woulda Made It [V1]\", \"artists\": \"(ref. NOVA WAV) (feat. <PERSON> $ign)\", \"aliases\": [\"Never Would Have Made It\"], \"description\": \"OG Filename: NOVA NeverWouldaMadeIt 2 1 18 KW\\nEarlier NOVA WAV \\\"Never Would Have Made It\\\" reference track.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"17a42e1a47007d465e6f307575b243a9\", \"url\": \"https://api.pillowcase.su/api/download/17a42e1a47007d465e6f307575b243a9\", \"size\": \"4.84 MB\", \"duration\": 101.95}", "aliases": ["Never Would Have Made It"], "size": "4.84 MB"}, {"id": "never-woulda-made-it-81", "name": "<PERSON><PERSON> - Never Woulda Made It [V2]", "artists": [], "producers": [], "notes": "OG Filename: NOVA NeverWouldaMadeIt 4 29 18 KW\nNOVA WAV \"Never Would Have Made It\" reference track. Contains a verse that was cut from the final version of the song. Original snippet leaked June 6th, 2024.", "length": "424.8", "fileDate": 16637184, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/5ea1bd0770e2938f84fed56c0cf39181", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ea1bd0770e2938f84fed56c0cf39181\", \"key\": \"Never Woulda Made It\", \"title\": \"<PERSON><PERSON> - <PERSON> Woulda Made It [V2]\", \"artists\": \"(ref. NOVA WAV)\", \"aliases\": [\"Never Would Have Made It\"], \"description\": \"OG Filename: NOVA NeverWouldaMadeIt 4 29 18 KW\\nNOVA WAV \\\"Never Would Have Made It\\\" reference track. Contains a verse that was cut from the final version of the song. Original snippet leaked June 6th, 2024.\", \"date\": 16637184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"68fdedfc6ed4cd4b65d028ac67e2c8a2\", \"url\": \"https://api.pillowcase.su/api/download/68fdedfc6ed4cd4b65d028ac67e2c8a2\", \"size\": \"10 MB\", \"duration\": 424.8}", "aliases": ["Never Would Have Made It"], "size": "10 MB"}, {"id": "where-are-you-hiding", "name": "<PERSON><PERSON> - Where Are You Hiding [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: NOVA WhereAreYouHiding LowerNote\nNOVA WAV reference track for \"Where Are You Hiding\". Uses \"Chop 17\". Original snippet leaked June 6th, 2024.", "length": "149.68", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/62312090c791824b3795ed6dd135c4a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/62312090c791824b3795ed6dd135c4a5\", \"key\": \"Where Are You Hiding\", \"title\": \"<PERSON><PERSON> - Where Are You Hiding [V2]\", \"artists\": \"(ref. NOVA WAV) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"No Manners\"], \"description\": \"OG Filename: NOVA WhereAreYouHiding LowerNote\\nNOVA WAV reference track for \\\"Where Are You Hiding\\\". Uses \\\"Chop 17\\\". Original snippet leaked June 6th, 2024.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"993b0900dbd8dd3e647e60ee887f18ed\", \"url\": \"https://api.pillowcase.su/api/download/993b0900dbd8dd3e647e60ee887f18ed\", \"size\": \"5.6 MB\", \"duration\": 149.68}", "aliases": ["No Manners"], "size": "5.6 MB"}, {"id": "no-manners", "name": "<PERSON><PERSON> - <PERSON> [V3]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West"], "notes": "A version of \"No Manners\" with a bridge or verse from <PERSON><PERSON>. His vocals seem to be a cut feature rather than a reference for <PERSON><PERSON>", "length": "12.3", "fileDate": 16041888, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e8462eec8a8a8cc727a5ab981fa59ef9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8462eec8a8a8cc727a5ab981fa59ef9\", \"key\": \"<PERSON> Manners\", \"title\": \"<PERSON><PERSON> - <PERSON> [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Where Are You Hiding\"], \"description\": \"A version of \\\"No Manners\\\" with a bridge or verse from <PERSON><PERSON>. His vocals seem to be a cut feature rather than a reference for <PERSON><PERSON>\", \"date\": 16041888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bbdfe90558eddb70ccaeaeb02fa26653\", \"url\": \"https://api.pillowcase.su/api/download/bbdfe90558eddb70ccaeaeb02fa26653\", \"size\": \"628 kB\", \"duration\": 12.3}", "aliases": ["Where Are You Hiding"], "size": "628 kB"}, {"id": "rose-in-harlem", "name": "<PERSON><PERSON> - Rose In Harlem [V2]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "johan lenox"], "notes": "Version previewed on <PERSON><PERSON>'s Instagram. Has a different outro to release, some lyrics differ, and the instrumental has more bass.", "length": "", "fileDate": 15285024, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a554e681617b6f03f5f77dd869440e04", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a554e681617b6f03f5f77dd869440e04\", \"key\": \"Rose In Harlem\", \"title\": \"<PERSON><PERSON> - <PERSON> In Harlem [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, E<PERSON> & joh<PERSON>)\", \"description\": \"Version previewed on <PERSON><PERSON>'s Instagram. Has a different outro to release, some lyrics differ, and the instrumental has more bass.\", \"date\": 15285024, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "would-you", "name": "<PERSON><PERSON> - Would You", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Would You Cheatin Redo ref 1\nNOVA WAV reference track. Sample chop was later reused on Abstract Mindstate's song \"Move Yo Body\". Made sometime in 2018.", "length": "56.28", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4d043b2376dcf1bf598cf09811d7e7ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d043b2376dcf1bf598cf09811d7e7ff\", \"key\": \"Would You\", \"title\": \"<PERSON><PERSON> - Would <PERSON>\", \"artists\": \"(ref. NOVA WAV) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Would You Cheatin Redo ref 1\\nNOVA WAV reference track. Sample chop was later reused on Abstract Mindstate's song \\\"Move Yo Body\\\". Made sometime in 2018.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d3549dbd19987880433238516085d610\", \"url\": \"https://api.pillowcase.su/api/download/d3549dbd19987880433238516085d610\", \"size\": \"4.1 MB\", \"duration\": 56.28}", "aliases": [], "size": "4.1 MB"}, {"id": "so-drunk", "name": "<PERSON> - So Drunk [V2]", "artists": ["Bad Bunny", "<PERSON>"], "producers": [], "notes": "OG Filename: <PERSON> Bunny <PERSON> - So Drunk New Reff\nOriginal version, dated 2017. Has a verse from <PERSON>.", "length": "200.69", "fileDate": 17230752, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e26a7fbb03598159e12f4052db1bc3db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e26a7fbb03598159e12f4052db1bc3db\", \"key\": \"So Drunk\", \"title\": \"<PERSON> Drunk [V2]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"aliases\": [\"DRUNK\"], \"description\": \"OG Filename: Bad <PERSON> - So Drunk New Reff\\nOriginal version, dated 2017. Has a verse from <PERSON>\", \"date\": 17230752, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"75f37fad656ca7d3d30e435396d451b2\", \"url\": \"https://api.pillowcase.su/api/download/75f37fad656ca7d3d30e435396d451b2\", \"size\": \"6.42 MB\", \"duration\": 200.69}", "aliases": ["DRUNK"], "size": "6.42 MB"}, {"id": "look-at-my-rollie", "name": "<PERSON> - Look At My Rollie [V1]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Version of the song that contains an open verse meant for <PERSON><PERSON><PERSON>. Snippet comes from <PERSON> playing the song for <PERSON><PERSON><PERSON> in the 500 Days in UCLA documentary.", "length": "26.88", "fileDate": 16955136, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1f359cc15d473edda16b2869b6e7e4b0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f359cc15d473edda16b2869b6e7e4b0\", \"key\": \"Look At My Rollie\", \"title\": \"<PERSON> - Look At My Rollie [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Watch\"], \"description\": \"Version of the song that contains an open verse meant for <PERSON><PERSON><PERSON>. Snippet comes from <PERSON> playing the song for <PERSON><PERSON><PERSON> in the 500 Days in UCLA documentary.\", \"date\": 16955136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"5a6482563a364bacbf12061d5a9c9c64\", \"url\": \"https://api.pillowcase.su/api/download/5a6482563a364bacbf12061d5a9c9c64\", \"size\": \"3.63 MB\", \"duration\": 26.88}", "aliases": ["Watch"], "size": "3.63 MB"}, {"id": "selfish", "name": "XXXTENTACION - Selfish [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "Original version of \"Selfish\" was made during 2017, before <PERSON><PERSON><PERSON> reused it for Donda 2. Features an open verse. Snippet of this version was leaked in 2018. Later, in 2022, a snippet of the sessions for this song was posted on an XXXTENTACION Discord server.", "length": "236.2", "fileDate": 16678656, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c1e29c9ef15c3bb7a17f69a45c4ee197", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1e29c9ef15c3bb7a17f69a45c4ee197\", \"key\": \"Selfish\", \"title\": \"XXXTENTACION - Selfish [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"Original version of \\\"Selfish\\\" was made during 2017, before <PERSON><PERSON><PERSON> reused it for Donda 2. Features an open verse. Snippet of this version was leaked in 2018. Later, in 2022, a snippet of the sessions for this song was posted on an XXXTENTACION Discord server.\", \"date\": 16678656, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"da2c8a31149e859e8c9a4e19cb95bfe9\", \"url\": \"https://api.pillowcase.su/api/download/da2c8a31149e859e8c9a4e19cb95bfe9\", \"size\": \"6.98 MB\", \"duration\": 236.2}", "aliases": [], "size": "6.98 MB"}, {"id": "brothers-89", "name": "Brothers [V7]", "artists": [], "producers": ["7 Aurel<PERSON>"], "notes": "OG Filename: Brothers Ref (4.19.18) \nSimilar to the 4.12.18 version, but the entire song is time stretched and edited, along with adding a new outro that would be further developed on in later versions.", "length": "113.63", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/19511d3b194d2d2c34b34f957c77963a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/19511d3b194d2d2c34b34f957c77963a\", \"key\": \"Brothers\", \"title\": \"Brothers [V7]\", \"artists\": \"(ref. 7 Aurelius) (prod. 7 Aurelius)\", \"description\": \"OG Filename: Brothers Ref (4.19.18) \\nSimilar to the 4.12.18 version, but the entire song is time stretched and edited, along with adding a new outro that would be further developed on in later versions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eb7736eedc9141cbc0f95896bc9b73bf\", \"url\": \"https://api.pillowcase.su/api/download/eb7736eedc9141cbc0f95896bc9b73bf\", \"size\": \"5.02 MB\", \"duration\": 113.63}", "aliases": [], "size": "5.02 MB"}, {"id": "brothers-90", "name": "Brothers [V8]", "artists": [], "producers": [], "notes": "OG Filename: Brothers REF (5.12.18)\nCut-down version of \"Brothers\".", "length": "69.82", "fileDate": 16714944, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/01e5e26117edb0cb1bf7f8122638fa65", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/01e5e26117edb0cb1bf7f8122638fa65\", \"key\": \"Brothers\", \"title\": \"Brothers [V8]\", \"description\": \"OG Filename: <PERSON> REF (5.12.18)\\nCut-down version of \\\"Brothers\\\".\", \"date\": 16714944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ab881954db92eff26dbc7e112ea26330\", \"url\": \"https://api.pillowcase.su/api/download/ab881954db92eff26dbc7e112ea26330\", \"size\": \"4.32 MB\", \"duration\": 69.82}", "aliases": [], "size": "4.32 MB"}, {"id": "everything", "name": "✨ Everything [V1]", "artists": ["The-Dream"], "producers": ["Kanye West"], "notes": "OG Filename: Everything REF (5.12.18)\nFirst version, when it was originally <PERSON><PERSON><PERSON>'s song. The beat is just the sample looped. Original snippet leaked November 18th, 2022.", "length": "203.53", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/bad9a70910c60bae0a9d491471e01315", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bad9a70910c60bae0a9d491471e01315\", \"key\": \"Everything\", \"title\": \"\\u2728 Everything [V1]\", \"artists\": \"(feat. The-<PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Everything REF (5.12.18)\\nFirst version, when it was originally <PERSON><PERSON><PERSON>'s song. The beat is just the sample looped. Original snippet leaked November 18th, 2022.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"edd1377715d4b37395231817b4552e89\", \"url\": \"https://api.pillowcase.su/api/download/edd1377715d4b37395231817b4552e89\", \"size\": \"6.46 MB\", \"duration\": 203.53}", "aliases": [], "size": "6.46 MB"}, {"id": "fanfare-bowl", "name": "Fanfare Bowl", "artists": [], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "notes": "OG Filename: for YE (Fanfare Bowl)\nBon Iver reference sent to <PERSON><PERSON><PERSON>. It's unlikely he ever recorded. Eventually became \"U (Man Like)\" from <PERSON> Iver's album i,i.", "length": "132.98", "fileDate": 16029792, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1a3fa0d17164589c6d746fa55fd17d19", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1a3fa0d17164589c6d746fa55fd17d19\", \"key\": \"Fanfare Bowl\", \"title\": \"Fanfare Bowl\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"U (Man Like)\"], \"description\": \"OG Filename: for Y<PERSON> (Fanfare Bowl)\\nBon Iver reference sent to <PERSON><PERSON><PERSON>. It's unlikely he ever recorded. Eventually became \\\"U (Man Like)\\\" from <PERSON> Iver's album i,i.\", \"date\": 16029792, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"11097bc2343df1a7773be189b3f0af10\", \"url\": \"https://api.pillowcase.su/api/download/11097bc2343df1a7773be189b3f0af10\", \"size\": \"5.33 MB\", \"duration\": 132.98}", "aliases": ["U (<PERSON> Like)"], "size": "5.33 MB"}, {"id": "i-feel-free", "name": "I Feel Free [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> (<PERSON>)\n<PERSON> reference track. Unknown when this is from.", "length": "194.88", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/cca070e0f487952011d0913b8d102a36", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cca070e0f487952011d0913b8d102a36\", \"key\": \"I Feel Free\", \"title\": \"I Feel Free [V1]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"<PERSON>\", \"I Feel Free\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"OG Filename: Free (<PERSON>)\\nM<PERSON><PERSON> reference track. Unknown when this is from.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1eabb663f35f4d1d8f489e975aad2ccf\", \"url\": \"https://api.pillowcase.su/api/download/1eabb663f35f4d1d8f489e975aad2ccf\", \"size\": \"6.32 MB\", \"duration\": 194.88}", "aliases": ["Free", "I Feel Free", "Freeee (Ghost Town Pt. 2)"], "size": "6.32 MB"}, {"id": "i-feel-free-94", "name": "I Feel Free [V3]", "artists": [], "producers": [], "notes": "Has no <PERSON>, or extra sample at the beginning, but has entirely different, alternate, yet unfinished lyrics/flow. Two other vocal references by <PERSON><PERSON><PERSON> were recorded on this day, \"KW VOX REF2\" and \"KW VOX REF4\" as well as the first vox ref.", "length": "206.81", "fileDate": 16036704, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/946a1aa0b3fac7c56c97601a5520dd55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/946a1aa0b3fac7c56c97601a5520dd55\", \"key\": \"I Feel Free\", \"title\": \"I Feel Free [V3]\", \"aliases\": [\"<PERSON>\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"Has no <PERSON> Cudi, or extra sample at the beginning, but has entirely different, alternate, yet unfinished lyrics/flow. Two other vocal references by <PERSON><PERSON><PERSON> were recorded on this day, \\\"KW VOX REF2\\\" and \\\"KW VOX REF4\\\" as well as the first vox ref.\", \"date\": 16036704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5f82e8ade0ae40a769b88acc5cf291a6\", \"url\": \"https://api.pillowcase.su/api/download/5f82e8ade0ae40a769b88acc5cf291a6\", \"size\": \"6.51 MB\", \"duration\": 206.81}", "aliases": ["Free", "Freeee (Ghost Town Pt. 2)"], "size": "6.51 MB"}, {"id": "i-feel-free-95", "name": "I Feel Free [V4]", "artists": [], "producers": [], "notes": "Early Chance reference for \"I Feel Free\".", "length": "144.34", "fileDate": 16749504, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/b77bcc4466c7df2f7c7dfc1dc49b39c5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b77bcc4466c7df2f7c7dfc1dc49b39c5\", \"key\": \"I Feel Free\", \"title\": \"I Feel Free [V4]\", \"artists\": \"(ref. <PERSON> the <PERSON>per)\", \"aliases\": [\"<PERSON>\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"Early Chance reference for \\\"I Feel Free\\\".\", \"date\": 16749504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d0e9dc684e0eb35a7a6989d2c7fbd81c\", \"url\": \"https://api.pillowcase.su/api/download/d0e9dc684e0eb35a7a6989d2c7fbd81c\", \"size\": \"5.51 MB\", \"duration\": 144.34}", "aliases": ["Free", "Freeee (Ghost Town Pt. 2)"], "size": "5.51 MB"}, {"id": "i-feel-free-96", "name": "I Feel Free [V5]", "artists": [], "producers": [], "notes": "Later Chance version. Has an open verse near the end.", "length": "208.3", "fileDate": 15905376, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/603600ec380b2897dd115b49bc9f417c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/603600ec380b2897dd115b49bc9f417c\", \"key\": \"I Feel Free\", \"title\": \"I Feel Free [V5]\", \"artists\": \"(ref. <PERSON> the <PERSON>per)\", \"aliases\": [\"<PERSON>\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"Later Chance version. Has an open verse near the end.\", \"date\": 15905376, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8c33105af1dd541f95a055bfc273e837\", \"url\": \"https://api.pillowcase.su/api/download/8c33105af1dd541f95a055bfc273e837\", \"size\": \"6.54 MB\", \"duration\": 208.3}", "aliases": ["Free", "Freeee (Ghost Town Pt. 2)"], "size": "6.54 MB"}, {"id": "i-know-i-know", "name": "I Know I Know [V2]", "artists": ["<PERSON> and the Lights"], "producers": [], "notes": "The original version of what became \"I Thought About Killing You.\" Said to be an unfinished mumble demo. Snippet leaked May 3rd, 2020.", "length": "7.13", "fileDate": 15884640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c10123c21920f1f18e6eb810eedfdbee", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c10123c21920f1f18e6eb810eedfdbee\", \"key\": \"I Know I Know\", \"title\": \"I Know I Know [V2]\", \"artists\": \"(feat. <PERSON> and the Lights)\", \"aliases\": [\"I Thought About Killing You\"], \"description\": \"The original version of what became \\\"I Thought About Killing You.\\\" Said to be an unfinished mumble demo. Snippet leaked May 3rd, 2020.\", \"date\": 15884640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"51b9b8cb66827f45fc012c8ba728efac\", \"url\": \"https://api.pillowcase.su/api/download/51b9b8cb66827f45fc012c8ba728efac\", \"size\": \"3.26 MB\", \"duration\": 7.13}", "aliases": ["I Thought About Killing You"], "size": "3.26 MB"}, {"id": "i-know-i-know-98", "name": "I Know I Know [V2]", "artists": ["<PERSON> and the Lights"], "producers": [], "notes": "The original version of what became \"I Thought About Killing You.\" Said to be an unfinished mumble demo. Snippet leaked May 3rd, 2020.", "length": "109.09", "fileDate": 15884640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/806cb89f2848771a9a9d297e93c80253", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/806cb89f2848771a9a9d297e93c80253\", \"key\": \"I Know I Know\", \"title\": \"I Know I Know [V2]\", \"artists\": \"(feat. <PERSON> and the Lights)\", \"aliases\": [\"I Thought About Killing You\"], \"description\": \"The original version of what became \\\"I Thought About Killing You.\\\" Said to be an unfinished mumble demo. Snippet leaked May 3rd, 2020.\", \"date\": 15884640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ec8a38e33a5bec11e1a585ac4cf911c1\", \"url\": \"https://api.pillowcase.su/api/download/ec8a38e33a5bec11e1a585ac4cf911c1\", \"size\": \"4.95 MB\", \"duration\": 109.09}", "aliases": ["I Thought About Killing You"], "size": "4.95 MB"}, {"id": "let-it-go", "name": "Let It Go [V2]", "artists": [], "producers": [], "notes": "Consequence \"Let It Go\" reference track. Samples \"Make a Joyful Noise\" by <PERSON><PERSON>. Played on a July 2023 Instagram Live.", "length": "18.21", "fileDate": 16881696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/52846650a1fc672ce0e56259243d90e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/52846650a1fc672ce0e56259243d90e3\", \"key\": \"Let It Go\", \"title\": \"Let It Go [V2]\", \"artists\": \"(ref. Consequence)\", \"description\": \"Consequence \\\"Let It Go\\\" reference track. Samples \\\"Make a Joyful Noise\\\" by <PERSON><PERSON>. Played on a July 2023 Instagram Live.\", \"date\": 16881696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"593b76309fb07f389f0f29ec43925dc0\", \"url\": \"https://api.pillowcase.su/api/download/593b76309fb07f389f0f29ec43925dc0\", \"size\": \"3.5 MB\", \"duration\": 18.21}", "aliases": [], "size": "3.5 MB"}, {"id": "lift-yourself", "name": "Lift Yourself [V4]", "artists": [], "producers": ["Kanye West", "MIKE DEAN", "Benji B", "<PERSON>"], "notes": "OG Filename: Lift Yourself Ref 4.24.18-00.00.09.851-00.02.46.613\nAn alternate version of the \"Lift Yourself\" instrumental with different mixing. Most likely the first bounce when the \"real\" vocals were taken off.", "length": "156.79", "fileDate": 16999200, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e211527c6a3b513983efaba40f51f1cd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e211527c6a3b513983efaba40f51f1cd\", \"key\": \"Lift Yourself\", \"title\": \"Lift Yourself [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>)\", \"description\": \"OG Filename: Lift Yourself Ref 4.24.18-00.00.09.851-00.02.46.613\\nAn alternate version of the \\\"Lift Yourself\\\" instrumental with different mixing. Most likely the first bounce when the \\\"real\\\" vocals were taken off.\", \"date\": 16999200, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c9f7ec4d7c842d6e748af6e0ccdbeb9f\", \"url\": \"https://api.pillowcase.su/api/download/c9f7ec4d7c842d6e748af6e0ccdbeb9f\", \"size\": \"5.71 MB\", \"duration\": 156.79}", "aliases": [], "size": "5.71 MB"}, {"id": "lift-yourself-101", "name": "Lift Yourself [V5]", "artists": [], "producers": ["Kanye West", "MIKE DEAN", "Benji B", "<PERSON>"], "notes": "OG Filename: Lift Yourself Mike Mix 2_01\nFound in the ProTools session. Alternate mix on <PERSON><PERSON><PERSON>'s vocals. Has some silence at the end.", "length": "187.19", "fileDate": 16999200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/b1f77a8e942598cb4b734c3c5a84f409", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b1f77a8e942598cb4b734c3c5a84f409\", \"key\": \"Lift Yourself\", \"title\": \"Lift Yourself [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Lift Yourself Mike Mix 2_01\\nFound in the ProTools session. Alternate mix on <PERSON><PERSON><PERSON>'s vocals. Has some silence at the end.\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a66918ca19fa17e0d57908cbb1de8b03\", \"url\": \"https://api.pillowcase.su/api/download/a66918ca19fa17e0d57908cbb1de8b03\", \"size\": \"6.2 MB\", \"duration\": 187.19}", "aliases": [], "size": "6.2 MB"}, {"id": "lift-yourself-102", "name": "Lift Yourself [V6]", "artists": [], "producers": ["Kanye West", "MIKE DEAN", "Benji B", "<PERSON>"], "notes": "OG Filename: 2018-04-27-LIFT YOURSELF\nNearly identical to the released version but with a totally different mix.", "length": "147.93", "fileDate": 16166304, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/63c99ef147fb9c08b3647bc5df555ea9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63c99ef147fb9c08b3647bc5df555ea9\", \"key\": \"Lift Yourself\", \"title\": \"Lift Yourself [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: 2018-04-27-LIFT YOURSELF\\nNearly identical to the released version but with a totally different mix.\", \"date\": 16166304, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1280a532c454e68b4b739a15b168ce1b\", \"url\": \"https://api.pillowcase.su/api/download/1280a532c454e68b4b739a15b168ce1b\", \"size\": \"5.57 MB\", \"duration\": 147.93}", "aliases": [], "size": "5.57 MB"}, {"id": "lift-yourself-103", "name": "Lift Yourself [V7]", "artists": [], "producers": ["Kanye West", "MIKE DEAN", "Benji B", "<PERSON>"], "notes": "OG Filename: Lift Yourself NG Mix 1 (4.27.18)\nFound in the Prootools session. Alternate Mix.", "length": "150", "fileDate": 16999200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/fa7a2d00529db982612b48f054ce3911", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa7a2d00529db982612b48f054ce3911\", \"key\": \"Lift Yourself\", \"title\": \"Lift Yourself [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Lift Yourself NG Mix 1 (4.27.18)\\nFound in the Prootools session. Alternate Mix.\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e8107d5efcc5f448d5db46a6b8d78e46\", \"url\": \"https://api.pillowcase.su/api/download/e8107d5efcc5f448d5db46a6b8d78e46\", \"size\": \"5.6 MB\", \"duration\": 150}", "aliases": [], "size": "5.6 MB"}, {"id": "lift-yourself-104", "name": "Lift Yourself [V8]", "artists": [], "producers": ["Kanye West", "MIKE DEAN", "Benji B", "<PERSON>"], "notes": "OG Filename: Lift Yourself Mike Mix 3_02\nFound in the ProTools session. Alternate Mix.", "length": "159.21", "fileDate": 16999200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/bbff89394103bf420a660f0316bb1c8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbff89394103bf420a660f0316bb1c8d\", \"key\": \"Lift Yourself\", \"title\": \"Lift Yourself [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Lift Yourself Mike Mix 3_02\\nFound in the ProTools session. Alternate Mix.\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ac17e1e25999ab4b060f5cd1e0d95323\", \"url\": \"https://api.pillowcase.su/api/download/ac17e1e25999ab4b060f5cd1e0d95323\", \"size\": \"5.75 MB\", \"duration\": 159.21}", "aliases": [], "size": "5.75 MB"}, {"id": "moonlight", "name": "🗑️ Moonlight [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Moon<PERSON> malik ref\n<PERSON> reference track. Unknown when this is from. <PERSON> mumble. Original snippet leaked November 18th, 2022.", "length": "173.4", "fileDate": 16950816, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8797a35d933a76fedd59593b56343b25", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8797a35d933a76fedd59593b56343b25\", \"key\": \"Moonlight\", \"title\": \"\\ud83d\\uddd1\\ufe0f Moonlight [V1]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Chop 7\"], \"description\": \"OG Filename: Moonlight malik ref\\n<PERSON><PERSON><PERSON> reference track. Unknown when this is from. <PERSON> mumble. Original snippet leaked November 18th, 2022.\", \"date\": 16950816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ad28baea4ac9172b3c03a6e5cd56002d\", \"url\": \"https://api.pillowcase.su/api/download/ad28baea4ac9172b3c03a6e5cd56002d\", \"size\": \"5.98 MB\", \"duration\": 173.4}", "aliases": ["Chop 7"], "size": "5.98 MB"}, {"id": "moonlight-106", "name": "Moonlight [V3]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "Track seen on both a leaked ye and leaked Hitler tracklist. From a folder called \"Moonlight_ForWY\". Originally incorrectly referred to as \"Moonlight Interlude from LOVE EVERYONE\". Samples \"Rope Ladder to the Moon\" by <PERSON> and \"Moon Light\" by Apple & 3 Oranges. Only the instrumental is available, but it is said to have mumble.", "length": "84.96", "fileDate": 16139520, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1dc7e7c8ae7800a7e64716a06cd99ce7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1dc7e7c8ae7800a7e64716a06cd99ce7\", \"key\": \"Moonlight\", \"title\": \"Moonlight [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Chop 7\"], \"description\": \"Track seen on both a leaked ye and leaked Hitler tracklist. From a folder called \\\"Moonlight_ForWY\\\". Originally incorrectly referred to as \\\"Moonlight Interlude from LOVE EVERYONE\\\". Sam<PERSON> \\\"Rope Ladder to the Moon\\\" by <PERSON> and \\\"Moon Light\\\" by Apple & 3 Oranges. Only the instrumental is available, but it is said to have mumble.\", \"date\": 16139520, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"477a5611231328e73a473ed858333f07\", \"url\": \"https://api.pillowcase.su/api/download/477a5611231328e73a473ed858333f07\", \"size\": \"4.56 MB\", \"duration\": 84.96}", "aliases": ["Chop 7"], "size": "4.56 MB"}, {"id": "mrs-<PERSON><PERSON>", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Consequence \"Mrs<PERSON> <PERSON>\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.", "length": "42.81", "fileDate": 17244576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/70a2335080352aeb23a1687dd1da9453", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/70a2335080352aeb23a1687dd1da9453\", \"key\": \"Mrs. <PERSON>\", \"title\": \"Mrs. <PERSON> [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence \\\"Mrs<PERSON> <PERSON>\\\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.\", \"date\": 17244576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"273fe513654962d9ba25b1ed16591ee2\", \"url\": \"https://api.pillowcase.su/api/download/273fe513654962d9ba25b1ed16591ee2\", \"size\": \"3.89 MB\", \"duration\": 42.81}", "aliases": [], "size": "3.89 MB"}, {"id": "mrs-<PERSON><PERSON>-108", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Consequence \"Mrs<PERSON> <PERSON>\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.", "length": "7.66", "fileDate": 17244576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2ccb651940b6d28f03de072ede30ceb2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ccb651940b6d28f03de072ede30ceb2\", \"key\": \"Mrs. <PERSON>\", \"title\": \"Mrs. <PERSON> [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence \\\"Mrs<PERSON> <PERSON>\\\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.\", \"date\": 17244576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d18f8076103983ad2985c0586c69b51c\", \"url\": \"https://api.pillowcase.su/api/download/d18f8076103983ad2985c0586c69b51c\", \"size\": \"3.33 MB\", \"duration\": 7.66}", "aliases": [], "size": "3.33 MB"}, {"id": "mrs-<PERSON><PERSON>-109", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Consequence \"Mrs<PERSON> <PERSON>\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.", "length": "16.56", "fileDate": 17244576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/7c672b1961e200438a96f2a1e1eb7623", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c672b1961e200438a96f2a1e1eb7623\", \"key\": \"Mrs. <PERSON>\", \"title\": \"Mrs. <PERSON> [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence \\\"Mrs. <PERSON>\\\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.\", \"date\": 17244576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"bab220e81ed0f3acf1400e38a2642c88\", \"url\": \"https://api.pillowcase.su/api/download/bab220e81ed0f3acf1400e38a2642c88\", \"size\": \"3.47 MB\", \"duration\": 16.56}", "aliases": [], "size": "3.47 MB"}, {"id": "mrs-<PERSON><PERSON>-110", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Consequence \"Mrs<PERSON> <PERSON>\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.", "length": "", "fileDate": 17244576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/533ec785ed20bc1eb132280a90cde06c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/533ec785ed20bc1eb132280a90cde06c\", \"key\": \"Mrs. <PERSON>\", \"title\": \"Mrs. <PERSON> [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence \\\"Mrs<PERSON> <PERSON>\\\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.\", \"date\": 17244576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "mrs-<PERSON><PERSON>-111", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Consequence \"Mrs<PERSON> <PERSON>\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.", "length": "19.19", "fileDate": 17244576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2a7e142046cfb37051b976fc1eb4d3a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2a7e142046cfb37051b976fc1eb4d3a0\", \"key\": \"Mrs. <PERSON>\", \"title\": \"Mrs. <PERSON> [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence \\\"Mrs<PERSON> <PERSON>\\\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.\", \"date\": 17244576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8c90833a0bb06155a43bde2122462b9d\", \"url\": \"https://api.pillowcase.su/api/download/8c90833a0bb06155a43bde2122462b9d\", \"size\": \"3.51 MB\", \"duration\": 19.19}", "aliases": [], "size": "3.51 MB"}, {"id": "mrs-<PERSON><PERSON>-112", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Consequence \"Mrs<PERSON> <PERSON>\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.", "length": "51.16", "fileDate": 17244576, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/f2f156d4ec5797b7b50467266e93f499", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f2f156d4ec5797b7b50467266e93f499\", \"key\": \"Mrs. <PERSON>\", \"title\": \"Mrs. <PERSON> [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence \\\"Mrs. <PERSON>\\\" reference track. Played on his Instagram Live. Consequence promised to play more of the song if his Instagram Live hit 300 viewers, but the community was unable to reach this goal in time. Played by Consequence multiple times on Instagram Live.\", \"date\": 17244576, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"40944e1fd2bafe739508678b4f3f90d3\", \"url\": \"https://api.pillowcase.su/api/download/40944e1fd2bafe739508678b4f3f90d3\", \"size\": \"4.02 MB\", \"duration\": 51.16}", "aliases": [], "size": "4.02 MB"}, {"id": "thirty-mile-zone", "name": "Thirty Mile Zone [V2]", "artists": ["070 Shake"], "producers": [], "notes": "OG Filename: Thirty Mile Zone REF 5.17.18\nVersion of \"Thirty Mile Zone\" with vocals from <PERSON><PERSON> leaked November 30th, 2024.", "length": "11.57", "fileDate": 17329248, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/20543fa1edf6243b8f0d60c64c8ecb1b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20543fa1edf6243b8f0d60c64c8ecb1b\", \"key\": \"Thirty Mile Zone\", \"title\": \"Thirty Mile Zone [V2]\", \"artists\": \"(feat. 070 Shake)\", \"aliases\": [\"TMZ\"], \"description\": \"OG Filename: Thirty Mile Zone REF 5.17.18\\nVersion of \\\"Thirty Mile Zone\\\" with vocals from <PERSON><PERSON> leaked November 30th, 2024.\", \"date\": 17329248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c08a69f1c8c20ed66d340cacda950659\", \"url\": \"https://api.pillowcase.su/api/download/c08a69f1c8c20ed66d340cacda950659\", \"size\": \"3.39 MB\", \"duration\": 11.57}", "aliases": ["TMZ"], "size": "3.39 MB"}, {"id": "tmz", "name": "TMZ [V3]", "artists": [], "producers": [], "notes": "OG Filename: TMZ HASS\n<PERSON> and 070 Shake \"TMZ\" reference track. Unknown when it was recorded. Samples \"<PERSON>tun Lama\" by <PERSON><PERSON>. Dated between March 29th and May 29th, 2018.", "length": "165.3", "fileDate": 16777152, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/cb3185d04fd4f42a310798f5186c2295", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cb3185d04fd4f42a310798f5186c2295\", \"key\": \"TMZ\", \"title\": \"TMZ [V3]\", \"artists\": \"(ref. <PERSON> & 070 <PERSON>)\", \"aliases\": [\"Thirty Mile Zone\"], \"description\": \"OG Filename: TMZ HASS\\nHassan Khaffaf and 070 Shake \\\"TMZ\\\" reference track. Unknown when it was recorded. <PERSON><PERSON> \\\"Pantun Lama\\\" by <PERSON><PERSON>. Dated between March 29th and May 29th, 2018.\", \"date\": 16777152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"51ee17e7dfcff9b268e6eacccda32ada\", \"url\": \"https://api.pillowcase.su/api/download/51ee17e7dfcff9b268e6eacccda32ada\", \"size\": \"5.85 MB\", \"duration\": 165.3}", "aliases": ["Thirty Mile Zone"], "size": "5.85 MB"}, {"id": "violent-nights", "name": "Violent Nights [V2]", "artists": ["070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Violent Nights REF No Drums (5.17.18)\nA rough Kanye take originally thought to have been a <PERSON> reference track. Contains mumble and no drums. Said to contain lyrics about <PERSON><PERSON> being sexually assaulted, when he was fourteen. Original snippet leaked November 18, 2022.", "length": "151.75", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/085e1aee1f38d8b6b78d936d9712e408", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/085e1aee1f38d8b6b78d936d9712e408\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V2]\", \"artists\": \"(feat. 070 Shake) (prod. <PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Violent Nights REF No Drums (5.17.18)\\nA rough Kanye take originally thought to have been a <PERSON> reference track. Contains mumble and no drums. Said to contain lyrics about <PERSON><PERSON> being sexually assaulted, when he was fourteen. Original snippet leaked November 18, 2022.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e720bc74d823f6bcac088820225d253f\", \"url\": \"https://api.pillowcase.su/api/download/e720bc74d823f6bcac088820225d253f\", \"size\": \"5.63 MB\", \"duration\": 151.75}", "aliases": [], "size": "5.63 MB"}, {"id": "violent-nights-116", "name": "⭐ Violent Nights [V3]", "artists": ["070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "Version with an additional verse from <PERSON> the <PERSON>per at the start of the song about <PERSON><PERSON> being sexually assaulted as a young teen. This is a writing reference for the mumble verse in a previous listing, with <PERSON> talking about <PERSON><PERSON><PERSON>'s mother and not his own. <PERSON> covers the last hook.", "length": "168.59", "fileDate": 16087680, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/482d3a8ea0e85a2a78ee40134a474512", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/482d3a8ea0e85a2a78ee40134a474512\", \"key\": \"Violent Nights\", \"title\": \"\\u2b50 Violent Nights [V3]\", \"artists\": \"(ref. <PERSON> the Rapper) (feat. 070 <PERSON>) (prod. 7 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\", \"description\": \"Version with an additional verse from <PERSON> the Rapper at the start of the song about <PERSON><PERSON> being sexually assaulted as a young teen. This is a writing reference for the mumble verse in a previous listing, with <PERSON> talking about <PERSON><PERSON><PERSON>'s mother and not his own. <PERSON> covers the last hook.\", \"date\": 16087680, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ef87f1198742670b0614de981e37777a\", \"url\": \"https://api.pillowcase.su/api/download/ef87f1198742670b0614de981e37777a\", \"size\": \"5.9 MB\", \"duration\": 168.59}", "aliases": [], "size": "5.9 MB"}, {"id": "wouldn-t-leave-117", "name": "✨ Wouldn't Leave [V4]", "artists": ["<PERSON><PERSON><PERSON>", "PARTYNEXTDOOR"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Wouldn't Leave REF (5.17.18)\nVersion that features different lines from release, and the first verse is partially finished. Second verse is the same as release but a little more mumbly. Original snippet leaked November 18th, 2022.", "length": "166.74", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/807a6e36fc27a8031a49cac94872738d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/807a6e36fc27a8031a49cac94872738d\", \"key\": \"Wouldn't Leave\", \"title\": \"\\u2728 Wouldn't Leave [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & PARTYNEXTDOOR) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Wouldn't Leave REF (5.17.18)\\nVersion that features different lines from release, and the first verse is partially finished. Second verse is the same as release but a little more mumbly. Original snippet leaked November 18th, 2022.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e554f25b51b5174701a02349abb86d7d\", \"url\": \"https://api.pillowcase.su/api/download/e554f25b51b5174701a02349abb86d7d\", \"size\": \"5.87 MB\", \"duration\": 166.74}", "aliases": [], "size": "5.87 MB"}, {"id": "ye-vs-the-people", "name": "<PERSON> vs. the People [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Version of \"Ye vs. the People\" previewed at the end of the Kanye and T.I. video. Has a single line change in the snippet.", "length": "17.09", "fileDate": 15251328, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/47aa19502f1ad60ea587784282c8a224", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/47aa19502f1ad60ea587784282c8a224\", \"key\": \"<PERSON> vs. the People\", \"title\": \"<PERSON> vs. the People [V2]\", \"artists\": \"(starring <PERSON><PERSON><PERSON><PERSON> as the People) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Don't Play With That Boy\"], \"description\": \"Version of \\\"Ye vs. the People\\\" previewed at the end of the Kanye and T.I. video. Has a single line change in the snippet.\", \"date\": 15251328, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7059a64c9eef70ebde60ad2213f46c44\", \"url\": \"https://api.pillowcase.su/api/download/7059a64c9eef70ebde60ad2213f46c44\", \"size\": \"3.48 MB\", \"duration\": 17.09}", "aliases": ["Don't Play With That Boy"], "size": "3.48 MB"}, {"id": "ye-vs-the-people-119", "name": "<PERSON> vs. the People [V3]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: 2018-04-28-YE VS THE PEOPLE\nNearly identical to the released version but with a totally different mix.", "length": "202.68", "fileDate": 16166304, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ad6b4212558fd466b577580f01320ae8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad6b4212558fd466b577580f01320ae8\", \"key\": \"<PERSON> vs. the People\", \"title\": \"<PERSON> vs. the People [V3]\", \"artists\": \"(starring <PERSON><PERSON><PERSON><PERSON> as the People) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Don't Play With That Boy\"], \"description\": \"OG Filename: 2018-04-28-YE VS THE PEOPLE\\nNearly identical to the released version but with a totally different mix.\", \"date\": 16166304, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"65d3d9de5e7fffb993a0322207693fd5\", \"url\": \"https://api.pillowcase.su/api/download/65d3d9de5e7fffb993a0322207693fd5\", \"size\": \"6.45 MB\", \"duration\": 202.68}", "aliases": ["Don't Play With That Boy"], "size": "6.45 MB"}, {"id": "-120", "name": "⭐ ??? [V2] ", "artists": ["A$AP Rocky", "<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Finished, but rough throwaway recorded sometime before ye's release. According to <PERSON>falls, was recorded the same day as the \"Amistad\" freestyle. Leakers have claimed several different names for it, such as \"Simulation Baptize\", \"My Choice\", and \"Foreskin Back\", but according to <PERSON>falls, the song was always untitled and all titles used for it were fake. Some of A$AP Rocky's lyrics were reused on \"Potato Salad\" by <PERSON> the Creator, with <PERSON><PERSON><PERSON> using some of his own lyrics in his White House meeting with <PERSON>.", "length": "192.05", "fileDate": 15710976, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2273be9faa89b653be40e5131f12391b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2273be9faa89b653be40e5131f12391b\", \"key\": \"???\", \"title\": \"\\u2b50 ??? [V2] \", \"artists\": \"(feat. A$AP Rocky & Pardison Fontaine)\", \"aliases\": [\"Foreskin Back\", \"My Choice\", \"Simulation Baptize\"], \"description\": \"Finished, but rough throwaway recorded sometime before ye's release. According to Waterfalls, was recorded the same day as the \\\"Amistad\\\" freestyle. Leakers have claimed several different names for it, such as \\\"Simulation Baptize\\\", \\\"My Choice\\\", and \\\"Foreskin Back\\\", but according to <PERSON>falls, the song was always untitled and all titles used for it were fake. Some of A$AP Rocky's lyrics were reused on \\\"Potato Salad\\\" by <PERSON> the Creator, with <PERSON><PERSON><PERSON> using some of his own lyrics in his White House meeting with <PERSON>.\", \"date\": 15710976, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"432d8bc6db2ba83fcccb41fd758a338a\", \"url\": \"https://api.pillowcase.su/api/download/432d8bc6db2ba83fcccb41fd758a338a\", \"size\": \"6.28 MB\", \"duration\": 192.05}", "aliases": ["<PERSON><PERSON><PERSON>", "My Choice", "Simulation Baptize"], "size": "6.28 MB"}, {"id": "-121", "name": "???", "artists": [], "producers": ["Kanye West"], "notes": "CDQ snippet of the beat <PERSON><PERSON><PERSON> was playing on a Twitter tracklist post.", "length": "11.09", "fileDate": 16691616, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/98c1e08bbea6baba239c7a71f22f0e15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/98c1e08bbea6baba239c7a71f22f0e15\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"CDQ snippet of the beat <PERSON><PERSON><PERSON> was playing on a Twitter tracklist post.\", \"date\": 16691616, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b35d177ccedaf36198a4877ef1ce3f1d\", \"url\": \"https://api.pillowcase.su/api/download/b35d177ccedaf36198a4877ef1ce3f1d\", \"size\": \"3.38 MB\", \"duration\": 11.09}", "aliases": [], "size": "3.38 MB"}, {"id": "look-at-my-rollie-122", "name": "<PERSON> - Look At My Rollie [V2]", "artists": ["<PERSON>", "Kanye West", "<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>", "MIKE DEAN"], "notes": "OG Filename: Look At My Rollie Explicit Master mfit\nOG file for the <PERSON> Scott song \"Watch\". Judging by the filename the song was called \"Look At My Rollie\" at this point. <PERSON><PERSON><PERSON>'s verse does not pan towards the left like it does in the released version of the song (it also does not pan in the released clean version). There are possibly other minor mixing differences.", "length": "217.5", "fileDate": 16618176, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/66f6520d241c781cddbb882e7917ebfa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/66f6520d241c781cddbb882e7917ebfa\", \"key\": \"Look At My Rollie\", \"title\": \"<PERSON> - Look At My Rollie [V2]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON>KE DEAN)\", \"aliases\": [\"Watch\"], \"description\": \"OG Filename: Look At My Rollie Explicit Master mfit\\nOG file for the <PERSON> Scott song \\\"Watch\\\". Judging by the filename the song was called \\\"Look At My Rollie\\\" at this point. <PERSON><PERSON><PERSON>'s verse does not pan towards the left like it does in the released version of the song (it also does not pan in the released clean version). There are possibly other minor mixing differences.\", \"date\": 16618176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a91bb5614078b8f162da1d1ccaa59cb4\", \"url\": \"https://api.pillowcase.su/api/download/a91bb5614078b8f162da1d1ccaa59cb4\", \"size\": \"6.68 MB\", \"duration\": 217.5}", "aliases": ["Watch"], "size": "6.68 MB"}, {"id": "queen-bitch", "name": "✨ Queen Bitch [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Fully solo version featuring almost 5 minutes of <PERSON><PERSON><PERSON> vocals without an open. Has lines that were later reused for \"I Love It\". Recorded the same day as the \"K Flows\" version. Unclear whose song it was, at this time. Original snippet leaked on November 18th, 2022. Leaked along with the Protools sessions.", "length": "283.37", "fileDate": 16999200, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c0e89ce8cba14082d23524bbbf5929d6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0e89ce8cba14082d23524bbbf5929d6\", \"key\": \"Queen Bitch\", \"title\": \"\\u2728 Queen Bitch [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"How Do You Respond?\", \"What Would Meek Do?\"], \"description\": \"Fully solo version featuring almost 5 minutes of <PERSON><PERSON><PERSON> vocals without an open. Has lines that were later reused for \\\"I Love It\\\". Recorded the same day as the \\\"K Flows\\\" version. Uncle<PERSON> whose song it was, at this time. Original snippet leaked on November 18th, 2022. Leaked along with the Protools sessions.\", \"date\": 16999200, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1316f588f95b8808b19ea539c541a37c\", \"url\": \"https://api.pillowcase.su/api/download/1316f588f95b8808b19ea539c541a37c\", \"size\": \"5.41 MB\", \"duration\": 283.37}", "aliases": ["How Do <PERSON> Re<PERSON>ond?", "What Would <PERSON><PERSON> Do?"], "size": "5.41 MB"}, {"id": "queen-bitch-124", "name": "Queen Bitch [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Queen Bitch <PERSON> Flows (11.1.17)\nCut-down of the previous version.", "length": "109.78", "fileDate": 16153344, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/406f53e9d845ef9bc20b75facc1a2432", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/406f53e9d845ef9bc20b75facc1a2432\", \"key\": \"Queen Bitch\", \"title\": \"Queen Bitch [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"How Do You Respond?\", \"What Would Meek Do?\"], \"description\": \"OG Filename: Queen <PERSON>ch <PERSON> (11.1.17)\\nCut-down of the previous version.\", \"date\": 16153344, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"87a906bfcf062ebc0b3b2c4b1a2c9f45\", \"url\": \"https://api.pillowcase.su/api/download/87a906bfcf062ebc0b3b2c4b1a2c9f45\", \"size\": \"2.64 MB\", \"duration\": 109.78}", "aliases": ["How Do <PERSON> Re<PERSON>ond?", "What Would <PERSON><PERSON> Do?"], "size": "2.64 MB"}, {"id": "cold-blooded-125", "name": "<PERSON><PERSON><PERSON> T - Cold Blooded [V2]", "artists": ["Swizz Beatz"], "producers": ["Kanye West", "Swizz Beatz"], "notes": "OG Filename: Cold Blooded_kw bb ref\nRecorded sometime in 2017, per the metadata. <PERSON><PERSON> beatboxing a reference drum pattern over the track, and a cut outro, but otherwise seems to be the exact same as the track released by Swizz Beatz on November 2nd, 2018.", "length": "225.77", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/f8f771278ce967da7c8c089e23f1d41c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f8f771278ce967da7c8c089e23f1d41c\", \"key\": \"Cold Blooded\", \"title\": \"<PERSON>usha T - Cold Blooded [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Swizz Beatz)\", \"description\": \"OG Filename: Cold Blooded_kw bb ref\\nRecorded sometime in 2017, per the metadata. Has Kanye beatboxing a reference drum pattern over the track, and a cut outro, but otherwise seems to be the exact same as the track released by Swizz Beatz on November 2nd, 2018.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c3d4d2b151b12411a4e4252f7b574dda\", \"url\": \"https://api.pillowcase.su/api/download/c3d4d2b151b12411a4e4252f7b574dda\", \"size\": \"4.5 MB\", \"duration\": 225.77}", "aliases": [], "size": "4.5 MB"}, {"id": "cold-blooded-126", "name": "<PERSON><PERSON><PERSON> T - Cold Blooded [V3]", "artists": ["Swizz Beatz"], "producers": ["Hudson Mohawke"], "notes": "OG Filename: HM Pusha Cold Blooded V1\nHas production from Hudson Mohawke.", "length": "173.22", "fileDate": 17157312, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8ac218f06bf3633a59fb2d648c318952", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ac218f06bf3633a59fb2d648c318952\", \"key\": \"Cold Blooded\", \"title\": \"<PERSON>usha T - Cold Blooded [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: HM Pusha Cold Blooded V1\\nHas production from Hudson Mohawke.\", \"date\": 17157312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b633f2f36e36b336c15dc97fe4578b16\", \"url\": \"https://api.pillowcase.su/api/download/b633f2f36e36b336c15dc97fe4578b16\", \"size\": \"3.65 MB\", \"duration\": 173.22}", "aliases": [], "size": "3.65 MB"}, {"id": "come-back-baby", "name": "<PERSON><PERSON><PERSON> T - Come Back Baby [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Come Back Baby Chop (10.1.17)\nOriginal chop done by <PERSON><PERSON><PERSON>.", "length": "276.35", "fileDate": 16959456, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a115b8cf9bbe87b051403982576f3b06", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a115b8cf9bbe87b051403982576f3b06\", \"key\": \"Come Back Baby\", \"title\": \"<PERSON>usha T - Come Back Baby [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Come Back Baby Chop (10.1.17)\\nOriginal chop done by <PERSON><PERSON><PERSON>.\", \"date\": 16959456, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fc9a9bef71204c15b2816d1c55d8fabb\", \"url\": \"https://api.pillowcase.su/api/download/fc9a9bef71204c15b2816d1c55d8fabb\", \"size\": \"5.31 MB\", \"duration\": 276.35}", "aliases": [], "size": "5.31 MB"}, {"id": "come-back-baby-128", "name": "<PERSON><PERSON><PERSON> T - Come Back Baby [V3]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Come Back Baby ref 1 1.18.18\nFirst version with <PERSON><PERSON><PERSON> vocals.", "length": "194.67", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/75a1b38f521568b671632cf32b8453fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/75a1b38f521568b671632cf32b8453fd\", \"key\": \"Come Back Baby\", \"title\": \"<PERSON>usha T - Come Back Baby [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Come Back Baby ref 1 1.18.18\\nFirst version with <PERSON><PERSON><PERSON> vocals.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6aa5e2f8818ea5de2708a0a09ea70b52\", \"url\": \"https://api.pillowcase.su/api/download/6aa5e2f8818ea5de2708a0a09ea70b52\", \"size\": \"3.99 MB\", \"duration\": 194.67}", "aliases": [], "size": "3.99 MB"}, {"id": "come-back-baby-129", "name": "<PERSON><PERSON><PERSON> T - Come Back Baby [V4]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Come Back Baby 2 ref 1 1.23.18\nFeatures alternate verses and production. VC snippet originally leaked January 31st, 2021", "length": "201.56", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a2b24fd4f812170c1b037583cf3c3c9a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2b24fd4f812170c1b037583cf3c3c9a\", \"key\": \"Come Back Baby\", \"title\": \"<PERSON>usha T - Come Back Baby [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Come Back Baby 2 ref 1 1.23.18\\nFeatures alternate verses and production. VC snippet originally leaked January 31st, 2021\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ce66cdb03828566189d2ba7b2037d700\", \"url\": \"https://api.pillowcase.su/api/download/ce66cdb03828566189d2ba7b2037d700\", \"size\": \"4.11 MB\", \"duration\": 201.56}", "aliases": [], "size": "4.11 MB"}, {"id": "ghosts", "name": "✨ Pusha T - Ghosts", "artists": [], "producers": ["Kanye West"], "notes": "<PERSON><PERSON><PERSON><PERSON> made during the Blobama sessions, and featured on a tracklist for it.", "length": "189.7", "fileDate": 17157312, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/610b8533c96540268ef9a5137b6587f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/610b8533c96540268ef9a5137b6587f8\", \"key\": \"Ghosts\", \"title\": \"\\u2728 Pusha T - Ghosts\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Afraid Of Ghosts\"], \"description\": \"Throwaway made during the Blobama sessions, and featured on a tracklist for it.\", \"date\": 17157312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b30cb709c600ff3da3c1681e4991cfa3\", \"url\": \"https://api.pillowcase.su/api/download/b30cb709c600ff3da3c1681e4991cfa3\", \"size\": \"3.92 MB\", \"duration\": 189.7}", "aliases": ["A<PERSON>id Of Ghosts"], "size": "3.92 MB"}, {"id": "gold", "name": "<PERSON>usha T - Gold [V1]", "artists": ["The-Dream"], "producers": ["Plain Pat"], "notes": "OG Filename: Gold_ref 03.18.17\nBlobama-era <PERSON><PERSON><PERSON> T throwaway. <PERSON><PERSON><PERSON>'s second verse would be reused on \"Feel The Love\".", "length": "177.19", "fileDate": 16714944, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a56b69ab64ceb3908bc074b062c40956", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a56b69ab64ceb3908bc074b062c40956\", \"key\": \"Gold\", \"title\": \"<PERSON><PERSON><PERSON> <PERSON> Gold [V1]\", \"artists\": \"(feat. The-<PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: Gold_ref 03.18.17\\nBlobama-era Pusha T throwaway. <PERSON><PERSON><PERSON>'s second verse would be reused on \\\"Feel The Love\\\".\", \"date\": 16714944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"693a95dd73a339cd3dee1f9ed0d1537b\", \"url\": \"https://api.pillowcase.su/api/download/693a95dd73a339cd3dee1f9ed0d1537b\", \"size\": \"3.72 MB\", \"duration\": 177.19}", "aliases": [], "size": "3.72 MB"}, {"id": "gold-132", "name": "<PERSON>usha T - Gold [V2]", "artists": [], "producers": ["Plain Pat"], "notes": "OG Filename: Gold_open hook bridge ref\nDemo of \"Gold\" with an open hook and bridge. Unknown when this was made.", "length": "192.77", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/9f6c0c07f6916f7f7a0c2f483498aea0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9f6c0c07f6916f7f7a0c2f483498aea0\", \"key\": \"Gold\", \"title\": \"<PERSON><PERSON><PERSON> T - Gold [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: Gold_open hook bridge ref\\nDemo of \\\"Gold\\\" with an open hook and bridge. Unknown when this was made.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"441bdc9a288685e7f53750aedbe3ac60\", \"url\": \"https://api.pillowcase.su/api/download/441bdc9a288685e7f53750aedbe3ac60\", \"size\": \"3.97 MB\", \"duration\": 192.77}", "aliases": [], "size": "3.97 MB"}, {"id": "gold-133", "name": "<PERSON>usha T - Gold [V3]", "artists": ["The-Dream"], "producers": ["Plain Pat"], "notes": "OG Filename: Gold_open hook bridge ref\nReplaces <PERSON><PERSON>'s second verse with open. Potentially made since <PERSON><PERSON>'s second verse would be reused for \"Feel The Love.\"", "length": "177.2", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8499d61cc51d4a9f054cad8109743a06", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8499d61cc51d4a9f054cad8109743a06\", \"key\": \"Gold\", \"title\": \"<PERSON><PERSON><PERSON> T - Gold [V3]\", \"artists\": \"(feat. The-<PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: Gold_open hook bridge ref\\nReplaces <PERSON><PERSON>'s second verse with open. Potentially made since <PERSON><PERSON>'s second verse would be reused for \\\"Feel The Love.\\\"\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a57c7854ff70c813b868b91970d79fd0\", \"url\": \"https://api.pillowcase.su/api/download/a57c7854ff70c813b868b91970d79fd0\", \"size\": \"3.72 MB\", \"duration\": 177.2}", "aliases": [], "size": "3.72 MB"}, {"id": "gold-134", "name": "<PERSON>usha T - Gold [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["Plain Pat"], "notes": "Version which features <PERSON><PERSON> instead of <PERSON><PERSON><PERSON>, but still contains the vocals reused for \"Feel The Love\". Unsure if this came before or after <PERSON>-<PERSON>'s version. Snippet leaked on December 19th, 2022.", "length": "20.87", "fileDate": 16714080, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/fc817121b2ed5ecf8ec1d468f6121eb1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fc817121b2ed5ecf8ec1d468f6121eb1\", \"key\": \"Gold\", \"title\": \"<PERSON><PERSON><PERSON> <PERSON> Gold [V4]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"Version which features <PERSON><PERSON> instead of <PERSON><PERSON><PERSON>, but still contains the vocals reused for \\\"Feel The Love\\\". Unsure if this came before or after The-<PERSON>'s version. Snippet leaked on December 19th, 2022.\", \"date\": 16714080, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8de639438db50877e63d08e9c5cd8900\", \"url\": \"https://api.pillowcase.su/api/download/8de639438db50877e63d08e9c5cd8900\", \"size\": \"1.21 MB\", \"duration\": 20.87}", "aliases": [], "size": "1.21 MB"}, {"id": "hard-piano", "name": "Pusha T - Hard Piano [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Hard Piano ref 1 (1.18.18)\nSolo <PERSON><PERSON><PERSON> T. Contains alternate lyrics and less effects on the beat. Only has 45 seconds of vocals followed by an open verse.", "length": "140.24", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/1c04ea82e441da56934fa88d99e791f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c04ea82e441da56934fa88d99e791f1\", \"key\": \"Hard Piano\", \"title\": \"Pusha T - Hard Piano [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Hard Piano ref 1 (1.18.18)\\nSolo Pusha T. Contains alternate lyrics and less effects on the beat. Only has 45 seconds of vocals followed by an open verse.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ace927e805683137042128bb21b85b79\", \"url\": \"https://api.pillowcase.su/api/download/ace927e805683137042128bb21b85b79\", \"size\": \"3.12 MB\", \"duration\": 140.24}", "aliases": [], "size": "3.12 MB"}, {"id": "hard-piano-136", "name": "Pusha T - Hard Piano [V2]", "artists": ["<PERSON>", "Kanye West"], "producers": ["Kanye West", "<PERSON>", "<PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON> Piano Hook Ref (5.10.18)\nHas <PERSON><PERSON><PERSON> mumble the hook. Has minor beat and lyric differences. Leaked as a bonus for the \"Skeletons\" groupbuy.", "length": "242.56", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8a5a38709f20d59cc0fa8bf4ce4591ad", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8a5a38709f20d59cc0fa8bf4ce4591ad\", \"key\": \"Hard Piano\", \"title\": \"Pusha T - Hard Piano [V2]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON>_<PERSON> Piano Hook Ref (5.10.18)\\nHas <PERSON><PERSON><PERSON> mumble the hook. Has minor beat and lyric differences. Leaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c3ac8011dc6a7b2dad164c9237f22d69\", \"url\": \"https://api.pillowcase.su/api/download/c3ac8011dc6a7b2dad164c9237f22d69\", \"size\": \"4.76 MB\", \"duration\": 242.56}", "aliases": [], "size": "4.76 MB"}, {"id": "hard-piano-137", "name": "Pusha T - Hard Piano [V3]", "artists": ["<PERSON>", "The-Dream"], "producers": ["Kanye West", "<PERSON>", "<PERSON>"], "notes": "More finished demo, featuring <PERSON><PERSON><PERSON> on the hook.", "length": "374.14", "fileDate": 15739488, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/706d26f19ad3a61d3c0e0e8aa6869dcb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/706d26f19ad3a61d3c0e0e8aa6869dcb\", \"key\": \"Hard Piano\", \"title\": \"Pusha T - Hard Piano [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"More finished demo, featuring <PERSON><PERSON><PERSON> on the hook.\", \"date\": 15739488, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"66761283a006df0c1d3d02a9a2b4136a\", \"url\": \"https://api.pillowcase.su/api/download/66761283a006df0c1d3d02a9a2b4136a\", \"size\": \"6.87 MB\", \"duration\": 374.14}", "aliases": [], "size": "6.87 MB"}, {"id": "hard-piano-138", "name": "Pusha T - Hard Piano [V4]", "artists": ["The WRLDFMS <PERSON>"], "producers": ["Kanye West"], "notes": "More finished version with a much longer <PERSON>ush<PERSON> T verse, as well as <PERSON> background vocals. Snipped leaked November 18th, 2022.", "length": "7.99", "fileDate": 16687296, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/f0ef9098dac43235a547e94cf3efd2a3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0ef9098dac43235a547e94cf3efd2a3\", \"key\": \"Hard Piano\", \"title\": \"Pusha T - Hard Piano [V4]\", \"artists\": \"(feat. The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"More finished version with a much longer Pusha T verse, as well as <PERSON> background vocals. Snipped leaked November 18th, 2022.\", \"date\": 16687296, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d98c87f6557e175fea14cd974bf75249\", \"url\": \"https://api.pillowcase.su/api/download/d98c87f6557e175fea14cd974bf75249\", \"size\": \"1.01 MB\", \"duration\": 7.99}", "aliases": [], "size": "1.01 MB"}, {"id": "if-you-know-you-know-139", "name": "If You Know You Know [V4]", "artists": ["The-Dream"], "producers": ["<PERSON>", "BNYX"], "notes": "Alternate version of the Charlie Heat version with a The-Dream feature. The-Dream recorded in March.", "length": "27.09", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/67a442aa96dab037855ea4e3f6087fb8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/67a442aa96dab037855ea4e3f6087fb8\", \"key\": \"If You Know You Know\", \"title\": \"If You Know You Know [V4]\", \"artists\": \"(feat. The-<PERSON>) (prod. <PERSON> Heat & BNYX)\", \"description\": \"Alternate version of the Charlie Heat version with a The-Dream feature. The-Dream recorded in March.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"63cef2985c2b8c0c9838d708f4ccf445\", \"url\": \"https://api.pillowcase.su/api/download/63cef2985c2b8c0c9838d708f4ccf445\", \"size\": \"1.31 MB\", \"duration\": 27.09}", "aliases": [], "size": "1.31 MB"}, {"id": "if-you-know", "name": "✨ <PERSON>usha T - If You Know [V5]", "artists": ["The-Dream"], "producers": ["<PERSON>", "BNYX"], "notes": "OG Filename: If You Know_ref 03.18.17\nLast known version with the original beat. Features The-Dream but is mixed quieter and is cut-down from the previous version.", "length": "198.19", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8384b9dc1e473519fe1eccc2085d393e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8384b9dc1e473519fe1eccc2085d393e\", \"key\": \"If You Know\", \"title\": \"\\u2728 <PERSON>usha T - If You Know [V5]\", \"artists\": \"(feat. The-<PERSON>) (prod. <PERSON> Heat & BNYX)\", \"aliases\": [\"If You Know You Know\"], \"description\": \"OG Filename: If You Know_ref 03.18.17\\nLast known version with the original beat. Features The-Dream but is mixed quieter and is cut-down from the previous version.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bb882663a63b890082b6827a8f9cdbee\", \"url\": \"https://api.pillowcase.su/api/download/bb882663a63b890082b6827a8f9cdbee\", \"size\": \"4.05 MB\", \"duration\": 198.19}", "aliases": ["If You Know You Know"], "size": "4.05 MB"}, {"id": "you-know", "name": "<PERSON><PERSON><PERSON> <PERSON> - You Know [V6]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON> Know Bass Line\nKanye does a reference for the bassline. Has him moaning the beat unlike beatboxing for \"Infrared\".", "length": "123.97", "fileDate": 17157312, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c9429e1fe218fdebbeff4aa41e936841", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9429e1fe218fdebbeff4aa41e936841\", \"key\": \"<PERSON> Know\", \"title\": \"<PERSON><PERSON><PERSON> <PERSON> <PERSON> You Know [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"If You Know You Know\"], \"description\": \"OG Filename: You Know Bass Line\\nKanye does a reference for the bassline. Has him moaning the beat unlike beatboxing for \\\"Infrared\\\".\", \"date\": 17157312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3af0b9aee250dde7571f35c235adccfc\", \"url\": \"https://api.pillowcase.su/api/download/3af0b9aee250dde7571f35c235adccfc\", \"size\": \"1.87 MB\", \"duration\": 123.97}", "aliases": ["If You Know You Know"], "size": "1.87 MB"}, {"id": "if-you-know-142", "name": "<PERSON><PERSON><PERSON> T - If You Know [V7]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: If You Know Ref (4.23.17)\nSimilar to release but with different mixing. Appears to be the version used to make V8 based on the mixing.", "length": "204.05", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/b3a38dd33cf161a69ff546a2ac2321eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b3a38dd33cf161a69ff546a2ac2321eb\", \"key\": \"If You Know\", \"title\": \"<PERSON>usha T - If You Know [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"If You Know You Know\"], \"description\": \"OG Filename: If You Know Ref (4.23.17)\\nSimilar to release but with different mixing. Appears to be the version used to make V8 based on the mixing.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b29d1278b4cb596754d3ec46ac091ded\", \"url\": \"https://api.pillowcase.su/api/download/b29d1278b4cb596754d3ec46ac091ded\", \"size\": \"4.15 MB\", \"duration\": 204.05}", "aliases": ["If You Know You Know"], "size": "4.15 MB"}, {"id": "if-you-know-143", "name": "<PERSON><PERSON><PERSON> T - If You Know [V8]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: If You Know String Intro Ref (4.23.17)\nHas an extended intro with alternate production. Was seen in a list of OG filenames leaked in October 2022.", "length": "215.9", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e17c75e649c52d3791774a0d2a446ca1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e17c75e649c52d3791774a0d2a446ca1\", \"key\": \"If You Know\", \"title\": \"<PERSON><PERSON><PERSON> T - If You Know [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"If You Know You Know\"], \"description\": \"OG Filename: If You Know String Intro Ref (4.23.17)\\nHas an extended intro with alternate production. Was seen in a list of OG filenames leaked in October 2022.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6a933f3541b39437d910b8ec115db074\", \"url\": \"https://api.pillowcase.su/api/download/6a933f3541b39437d910b8ec115db074\", \"size\": \"4.33 MB\", \"duration\": 215.9}", "aliases": ["If You Know You Know"], "size": "4.33 MB"}, {"id": "you-know-you-know", "name": "<PERSON><PERSON><PERSON> T - You Know You Know [V9]", "artists": [], "producers": ["Kanye West", "Plain Pat"], "notes": "OG Filename: You Know you Know M 7 REF 01\nSimilar to the following version, but with the outro cut. According to <PERSON><PERSON>, <PERSON> is labelled on \"most of\" the stems, including the drums. However, he is not credited on the released version. Leaked as a bonus for the \"Skeletons\" groupbuy.", "length": "199.48", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2ae26742993354710ab7c9a545344630", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ae26742993354710ab7c9a545344630\", \"key\": \"You Know You Know\", \"title\": \"<PERSON>usha T - You Know You Know [V9]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON> Pat)\", \"aliases\": [\"If You Know You Know\"], \"description\": \"OG Filename: You Know you Know M 7 REF 01\\nSimilar to the following version, but with the outro cut. According to <PERSON><PERSON>, <PERSON> is labelled on \\\"most of\\\" the stems, including the drums. However, he is not credited on the released version. Leaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"74e7fcb1e378fb43bc2732cc969ffbe1\", \"url\": \"https://api.pillowcase.su/api/download/74e7fcb1e378fb43bc2732cc969ffbe1\", \"size\": \"4.07 MB\", \"duration\": 199.48}", "aliases": ["If You Know You Know"], "size": "4.07 MB"}, {"id": "if-you-know-you-know-145", "name": "<PERSON><PERSON><PERSON> T - If You Know You Know [V10]", "artists": [], "producers": ["Kanye West", "Plain Pat", "<PERSON>"], "notes": "Nearly identical to the released version but with a rougher mix.", "length": "209.47", "fileDate": 16293312, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/03cb1297c0db0d392427eb2ad1fd05cc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03cb1297c0db0d392427eb2ad1fd05cc\", \"key\": \"If You Know You Know\", \"title\": \"<PERSON>usha T - If You Know You Know [V10]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Nearly identical to the released version but with a rougher mix.\", \"date\": 16293312, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3e44fb323b193a75e1e682219bc9f489\", \"url\": \"https://api.pillowcase.su/api/download/3e44fb323b193a75e1e682219bc9f489\", \"size\": \"4.23 MB\", \"duration\": 209.47}", "aliases": [], "size": "4.23 MB"}, {"id": "infrared", "name": "<PERSON><PERSON><PERSON> T - Infrared [V1]", "artists": [], "producers": [], "notes": "OG Filename: Kw Beat Box Infrared\nKanye beat box reference track.", "length": "40.01", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/2b9109a7187b9dde9c15ebde3beb4bd0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b9109a7187b9dde9c15ebde3beb4bd0\", \"key\": \"Infrared\", \"title\": \"<PERSON><PERSON><PERSON> T - Infrared [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Kw Beat Box Infrared\\nKanye beat box reference track.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"abc3ddf4dc5563d2ed1af8383ec5169f\", \"url\": \"https://api.pillowcase.su/api/download/abc3ddf4dc5563d2ed1af8383ec5169f\", \"size\": \"1.2 MB\", \"duration\": 40.01}", "aliases": [], "size": "1.2 MB"}, {"id": "infrared-147", "name": "<PERSON><PERSON><PERSON> T - Infrared [V2]", "artists": [], "producers": [], "notes": "OG Filename: Infrared Sub Bass <PERSON><PERSON> sub bass reference track.", "length": "17.39", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/81250bb8af05977d32fa19b6d81d96fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/81250bb8af05977d32fa19b6d81d96fa\", \"key\": \"Infrared\", \"title\": \"<PERSON><PERSON><PERSON> T - Infrared [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Infrared Sub Bass Ref\\nKanye sub bass reference track.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"df1ea0d457f7b9a3538cae2008417adc\", \"url\": \"https://api.pillowcase.su/api/download/df1ea0d457f7b9a3538cae2008417adc\", \"size\": \"1.02 MB\", \"duration\": 17.39}", "aliases": [], "size": "1.02 MB"}, {"id": "infrared-148", "name": "<PERSON><PERSON><PERSON> T - Infrared [V5]", "artists": ["???"], "producers": ["Kanye West"], "notes": "OG Filename: Infrared_07.17.17 ref new VA ad libs\nAn early veProduction is stripped back in comparision to release, only containing the sample chop. Contains ad-libs that weren't featured in the final release.", "length": "137.13", "fileDate": 17157312, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4e49df1bb94dc0f22f5b2055f4d76f62", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e49df1bb94dc0f22f5b2055f4d76f62\", \"key\": \"Infrared\", \"title\": \"<PERSON><PERSON><PERSON> T - Infrared [V5]\", \"artists\": \"(feat. ???) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Infrared_07.17.17 ref new VA ad libs\\nAn early veProduction is stripped back in comparision to release, only containing the sample chop. Contains ad-libs that weren't featured in the final release.\", \"date\": 17157312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d29d9fc4fef564dc7b01004e18cc3de0\", \"url\": \"https://api.pillowcase.su/api/download/d29d9fc4fef564dc7b01004e18cc3de0\", \"size\": \"3.08 MB\", \"duration\": 137.13}", "aliases": [], "size": "3.08 MB"}, {"id": "lord-forgive-em", "name": "<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Lord Forgive Em_ref 1\nSong from 2017 made during the King Push and Daytona sessions. Samples \"Lucifer\" by JAY-Z and \"If I've Done Anything Wrong\" by North Wind of Cleveland, Ohio.", "length": "115.35", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ad46c5b5244981ca65eaf6948d4e18c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad46c5b5244981ca65eaf6948d4e18c2\", \"key\": \"Lord Forgive 'Em\", \"title\": \"<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Lord Forgive Em_ref 1\\nSong from 2017 made during the King Push and Daytona sessions. Samples \\\"Lucifer\\\" by JAY-Z and \\\"If I've Done Anything Wrong\\\" by North Wind of Cleveland, Ohio.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ff2d5de8eac781a18521eac9b0534fe4\", \"url\": \"https://api.pillowcase.su/api/download/ff2d5de8eac781a18521eac9b0534fe4\", \"size\": \"2.73 MB\", \"duration\": 115.35}", "aliases": [], "size": "2.73 MB"}, {"id": "lord-forgive-em-150", "name": "<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Lord Forgive Em_ref 2\nHas alternate production.", "length": "160.1", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/968425c37117e9630cab1e5009f74adc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/968425c37117e9630cab1e5009f74adc\", \"key\": \"Lord Forgive 'Em\", \"title\": \"<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Lord Forgive Em_ref 2\\nHas alternate production.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"06c151b2da3efdea537fab0e655dd13b\", \"url\": \"https://api.pillowcase.su/api/download/06c151b2da3efdea537fab0e655dd13b\", \"size\": \"3.44 MB\", \"duration\": 160.1}", "aliases": [], "size": "3.44 MB"}, {"id": "lord-forgive-em-151", "name": "<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V3]", "artists": [], "producers": ["Kanye West"], "notes": "Alt mix of the previous version.", "length": "8.92", "fileDate": 16811712, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/bed83158b767e2626a3e49e283a1c315", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bed83158b767e2626a3e49e283a1c315\", \"key\": \"Lord Forgive 'Em\", \"title\": \"<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Alt mix of the previous version.\", \"date\": 16811712, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"66f43bc642d5664b9de3119df5173d16\", \"url\": \"https://api.pillowcase.su/api/download/66f43bc642d5664b9de3119df5173d16\", \"size\": \"1.02 MB\", \"duration\": 8.92}", "aliases": [], "size": "1.02 MB"}, {"id": "lord-forgive-em-152", "name": "<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V3]", "artists": [], "producers": ["Kanye West"], "notes": "Alt mix of the previous version.", "length": "12.51", "fileDate": 16811712, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e338eb96bc9aada908119f015ccef365", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e338eb96bc9aada908119f015ccef365\", \"key\": \"Lord Forgive 'Em\", \"title\": \"<PERSON><PERSON><PERSON> T - Lord Forgive 'Em [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Alt mix of the previous version.\", \"date\": 16811712, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9186ccb1d48b39e7dbef0ffae3a4be01\", \"url\": \"https://api.pillowcase.su/api/download/9186ccb1d48b39e7dbef0ffae3a4be01\", \"size\": \"1.08 MB\", \"duration\": 12.51}", "aliases": [], "size": "1.08 MB"}, {"id": "the-way-it-seems", "name": "<PERSON>usha T - The Way It Seems [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: The Way It Seems_ref 04.22.17\nEarlier version, with an open verse that was cut.", "length": "179.76", "fileDate": 17157312, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/0fb051ce6c09d604d519ad4aeab187f0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0fb051ce6c09d604d519ad4aeab187f0\", \"key\": \"The Way It Seems\", \"title\": \"<PERSON><PERSON>a T - The Way It Seems [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Objects In The Mirror\"], \"description\": \"OG Filename: The Way It Seems_ref 04.22.17\\nEarlier version, with an open verse that was cut.\", \"date\": 17157312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8344d70a2734cea680e44b8140ae8804\", \"url\": \"https://api.pillowcase.su/api/download/8344d70a2734cea680e44b8140ae8804\", \"size\": \"3.76 MB\", \"duration\": 179.76}", "aliases": ["Objects In The Mirror"], "size": "3.76 MB"}, {"id": "objects-in-the-mirror", "name": "⭐ Pusha T -  Objects In The Mirror [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Objects In The Mirror_ref 07.17.17\nBlowbama-era finished throwaway.", "length": "138.14", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/207b12e91583534c87c7dfcaedf208d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/207b12e91583534c87c7dfcaedf208d2\", \"key\": \"Objects In The Mirror\", \"title\": \"\\u2b50 Pusha T -  Objects In The Mirror [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"The Way It Seems\"], \"description\": \"OG Filename: Objects In The Mirror_ref 07.17.17\\nBlowbama-era finished throwaway.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c9309e3e97f1f30310aafdfa7e857ea7\", \"url\": \"https://api.pillowcase.su/api/download/c9309e3e97f1f30310aafdfa7e857ea7\", \"size\": \"3.09 MB\", \"duration\": 138.14}", "aliases": ["The Way It Seems"], "size": "3.09 MB"}, {"id": "real-gon-come-out", "name": "Pusha T - Real Gon' Come Out [V1]", "artists": [], "producers": [], "notes": "OG Filename: Real Gon Come Out_ref 02.20.17\nFirst known version, includes the original beat and reference vocals.", "length": "152.48", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/99b83b9ff132fad06dfa666ec5391ad0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/99b83b9ff132fad06dfa666ec5391ad0\", \"key\": \"Real Gon' Come Out\", \"title\": \"Pusha T - Real Gon' Come Out [V1]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Real Gon' Come\"], \"description\": \"OG Filename: Real Gon Come Out_ref 02.20.17\\nFirst known version, includes the original beat and reference vocals.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9c3071f4a187def6d761751680739314\", \"url\": \"https://api.pillowcase.su/api/download/9c3071f4a187def6d761751680739314\", \"size\": \"3.32 MB\", \"duration\": 152.48}", "aliases": ["Real Gon' Come"], "size": "3.32 MB"}, {"id": "real-gon-come", "name": "Pusha T - Real Gon' Come [V2]", "artists": ["Fabolous"], "producers": [], "notes": "OG Filename: Real Gon Come_07.17.17 OG beat\nBlobama-era finished throwaway. Uses the original beat prior to <PERSON><PERSON><PERSON> reworking it.", "length": "156.45", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ddf7597094702165ce1d88f051946a76", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ddf7597094702165ce1d88f051946a76\", \"key\": \"Real Gon' Come\", \"title\": \"Pusha T - Real Gon' Come [V2]\", \"artists\": \"(feat. Fabolous)\", \"aliases\": [\"Real Gon' Come Out\"], \"description\": \"OG Filename: Real Gon Come_07.17.17 OG beat\\nBlobama-era finished throwaway. Uses the original beat prior to <PERSON><PERSON><PERSON> reworking it.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6ab5e36aec9092d2ce8fa83b7056b393\", \"url\": \"https://api.pillowcase.su/api/download/6ab5e36aec9092d2ce8fa83b7056b393\", \"size\": \"3.39 MB\", \"duration\": 156.45}", "aliases": ["Real Gon' Come Out"], "size": "3.39 MB"}, {"id": "real-gon-come-157", "name": "⭐ Pusha T - Real Gon' Come [V3]", "artists": ["Fabolous"], "producers": ["Kanye West"], "notes": "OG Filename: Real Gon Come_07.17.17 KW utah beat\nBlobama-era finished throwaway. Original snippet leaked in July 2023.", "length": "158.53", "fileDate": 16952544, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/618fae43c18b92dbb13d1f9e394e72e2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/618fae43c18b92dbb13d1f9e394e72e2\", \"key\": \"Real Gon' Come\", \"title\": \"\\u2b50 Pusha T - Real Gon' Come [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Real Gon' Come Out\"], \"description\": \"OG Filename: Real Gon Come_07.17.17 KW utah beat\\nBlobama-era finished throwaway. Original snippet leaked in July 2023.\", \"date\": 16952544, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2fc69934a858a2d72bf3420f85c35c38\", \"url\": \"https://api.pillowcase.su/api/download/2fc69934a858a2d72bf3420f85c35c38\", \"size\": \"3.42 MB\", \"duration\": 158.53}", "aliases": ["Real Gon' Come Out"], "size": "3.42 MB"}, {"id": "santeria", "name": "⭐ Pusha T - Santeria [V3]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "Metro Boomin"], "notes": "Alternate version of the demo, with a massively reworked beat. <PERSON> <PERSON><PERSON> interpolating the vocals of the sample.", "length": "221.44", "fileDate": 15936480, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/dc07509df9ac582d09bec8523abf44de", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc07509df9ac582d09bec8523abf44de\", \"key\": \"Santeria\", \"title\": \"\\u2b50 Pusha T - Santeria [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON>e <PERSON> & Metro Boomin)\", \"description\": \"Alternate version of the demo, with a massively reworked beat. <PERSON> <PERSON><PERSON> interpolating the vocals of the sample.\", \"date\": 15936480, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"be50bc98d4371557383322ba0a1d4c4a\", \"url\": \"https://api.pillowcase.su/api/download/be50bc98d4371557383322ba0a1d4c4a\", \"size\": \"4.42 MB\", \"duration\": 221.44}", "aliases": [], "size": "4.42 MB"}, {"id": "santeria-159", "name": "<PERSON>usha T - Santeria [V6]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Santeria_ref 03.19.17\nVersion with the first sample beat that would later end up on the release version. Still contains the original sample chop from 2015. Drums heavily differ from release.", "length": "199.16", "fileDate": 17157312, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c1403c0af07a6cbb1e9022c4137c35ba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1403c0af07a6cbb1e9022c4137c35ba\", \"key\": \"Santeria\", \"title\": \"<PERSON><PERSON><PERSON> T - Sant<PERSON> [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Santeria_ref 03.19.17\\nVersion with the first sample beat that would later end up on the release version. Still contains the original sample chop from 2015. Drums heavily differ from release.\", \"date\": 17157312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8750b75bd0271edb1df936e14c9e2a12\", \"url\": \"https://api.pillowcase.su/api/download/8750b75bd0271edb1df936e14c9e2a12\", \"size\": \"4.07 MB\", \"duration\": 199.16}", "aliases": [], "size": "4.07 MB"}, {"id": "santeria-160", "name": "<PERSON>usha T - Santeria [V8]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> for <PERSON> 5.10.18\nHas the first verse, no vocals on the beatswitch, and later open verses, meant for <PERSON>.", "length": "241.42", "fileDate": 16755552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/c0da4acdacb89685206c93366afdfaae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0da4acdacb89685206c93366afdfaae\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON> <PERSON> Sant<PERSON> [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> Ref for Tony 5.10.18\\nHas the first verse, no vocals on the beatswitch, and later open verses, meant for <PERSON>.\", \"date\": 16755552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c48bcd278bf8c476358c63015582b2ae\", \"url\": \"https://api.pillowcase.su/api/download/c48bcd278bf8c476358c63015582b2ae\", \"size\": \"4.74 MB\", \"duration\": 241.42}", "aliases": [], "size": "4.74 MB"}, {"id": "santeria-161", "name": "<PERSON>usha T - Santeria [V9]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "OG Filename: Santeria Ref w KW Vocal 5.10.18\nHas <PERSON><PERSON><PERSON> mumbling the hook.", "length": "175.3", "fileDate": 15612480, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4d12121aaab5e2f4884a340b08cf15c7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d12121aaab5e2f4884a340b08cf15c7\", \"key\": \"Santeria\", \"title\": \"<PERSON><PERSON><PERSON> T - Sant<PERSON> [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Santeria Ref w KW Vocal 5.10.18\\nHas <PERSON>nye mumbling the hook.\", \"date\": 15612480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9cc27ad5ec77c3a155cc6b5aa3a9e04a\", \"url\": \"https://api.pillowcase.su/api/download/9cc27ad5ec77c3a155cc6b5aa3a9e04a\", \"size\": \"3.68 MB\", \"duration\": 175.3}", "aliases": [], "size": "3.68 MB"}, {"id": "santeria-162", "name": "Pusha T - Santeria [V10]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "<PERSON> finished <PERSON><PERSON><PERSON> vocals.", "length": "177.87", "fileDate": 15608160, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/87c61ee5c462cda990861673801823d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/87c61ee5c462cda990861673801823d2\", \"key\": \"Santeria\", \"title\": \"<PERSON><PERSON><PERSON> [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Has finished <PERSON><PERSON><PERSON> vocals.\", \"date\": 15608160, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"542f929a600772b7fe4cd6d560e486f8\", \"url\": \"https://api.pillowcase.su/api/download/542f929a600772b7fe4cd6d560e486f8\", \"size\": \"3.73 MB\", \"duration\": 177.87}", "aliases": [], "size": "3.73 MB"}, {"id": "santeria-163", "name": "Pusha T - Santeria [V11]", "artists": ["Kanye West", "<PERSON>"], "producers": ["Kanye West"], "notes": "<PERSON> poorly mixed <PERSON> vocals on the hook.", "length": "166.65", "fileDate": 15628896, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/e57cadefa4c33c78ff4077cd9188bc84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e57cadefa4c33c78ff4077cd9188bc84\", \"key\": \"Santeria\", \"title\": \"<PERSON><PERSON><PERSON> [V11]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Has poorly mixed <PERSON> vocals on the hook.\", \"date\": 15628896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"65334f566ab4580653e56e15efaa5361\", \"url\": \"https://api.pillowcase.su/api/download/65334f566ab4580653e56e15efaa5361\", \"size\": \"3.69 MB\", \"duration\": 166.65}", "aliases": [], "size": "3.69 MB"}, {"id": "sisters-and-brothers", "name": "✨ Pusha T - Sisters and Brothers", "artists": [], "producers": ["Kanye West"], "notes": "Blobama-era throwaway.", "length": "196.64", "fileDate": 17157312, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ed71c9dbb055a71ada0f83210eb6ea3f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ed71c9dbb055a71ada0f83210eb6ea3f\", \"key\": \"Sisters and Brothers\", \"title\": \"\\u2728 Pusha T - Sisters and Brothers\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Blobama-era throwaway.\", \"date\": 17157312, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"643f02a36eb30f39de7523bacea1422a\", \"url\": \"https://api.pillowcase.su/api/download/643f02a36eb30f39de7523bacea1422a\", \"size\": \"4.03 MB\", \"duration\": 196.64}", "aliases": [], "size": "4.03 MB"}, {"id": "sociopath", "name": "<PERSON><PERSON><PERSON> T - So<PERSON> [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Pusha - Sociopath 8.9.17\nEarliest known version.", "length": "166.9", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a232f787e1ef8b58854eb548b5d3a22c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a232f787e1ef8b58854eb548b5d3a22c\", \"key\": \"Sociopath\", \"title\": \"<PERSON><PERSON><PERSON> T - Sociopath [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Pusha - Sociopath 8.9.17\\nEarliest known version.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6a5fe8dc10feb9ed9d28d07f71a755a8\", \"url\": \"https://api.pillowcase.su/api/download/6a5fe8dc10feb9ed9d28d07f71a755a8\", \"size\": \"3.55 MB\", \"duration\": 166.9}", "aliases": [], "size": "3.55 MB"}, {"id": "sociopath-166", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> So<PERSON> [V3]", "artists": ["BIA"], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON><PERSON> T Sociopath 2.2 add <PERSON><PERSON>\nFeaturing rapper <PERSON><PERSON> rather than <PERSON><PERSON>. BIA raps the lines from <PERSON><PERSON><PERSON> T from 1:34 - 1:47.", "length": "166.76", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/aec1c89a2d7c4d55fea456b6cae215f9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aec1c89a2d7c4d55fea456b6cae215f9\", \"key\": \"Sociopath\", \"title\": \"<PERSON><PERSON><PERSON> T - Sociopath [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> Sociopath 2.2 add <PERSON><PERSON>\\nFeaturing rapper <PERSON><PERSON> rather than <PERSON><PERSON>. BIA raps the lines from <PERSON><PERSON><PERSON> T from 1:34 - 1:47.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d44a3f8c52551c638036b0d843a75683\", \"url\": \"https://api.pillowcase.su/api/download/d44a3f8c52551c638036b0d843a75683\", \"size\": \"3.55 MB\", \"duration\": 166.76}", "aliases": [], "size": "3.55 MB"}, {"id": "spaceship", "name": "<PERSON>usha T - Spaceship [V1]", "artists": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Spaceship pusha ref 11.15.16\nHas a mumble verse from <PERSON><PERSON><PERSON> and ad-libs from <PERSON><PERSON><PERSON><PERSON> taken directly from his track \"Panda\". Likely made during the same session as \"<PERSON>.\" Filename indicates an earlier version exists without <PERSON><PERSON><PERSON> \"You Brought The World To My Door\" by The Hues Corporation.", "length": "246.96", "fileDate": 16877376, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/236568ab67dcd3a52ff72426494927eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/236568ab67dcd3a52ff72426494927eb\", \"key\": \"Spaceship\", \"title\": \"Pusha T - Spaceship [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>)\", \"description\": \"OG Filename: Spaceship pusha ref 11.15.16\\nHas a mumble verse from <PERSON><PERSON><PERSON> and ad-libs from <PERSON><PERSON><PERSON><PERSON> taken directly from his track \\\"Panda\\\". Likely made during the same session as \\\"Castro.\\\" Filename indicates an earlier version exists without <PERSON><PERSON><PERSON> <PERSON><PERSON> \\\"You Brought The World To My Door\\\" by The Hues Corporation.\", \"date\": 16877376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e619b488b2b2a50fc308d476cac95eb6\", \"url\": \"https://api.pillowcase.su/api/download/e619b488b2b2a50fc308d476cac95eb6\", \"size\": \"4.83 MB\", \"duration\": 246.96}", "aliases": [], "size": "4.83 MB"}, {"id": "spaceship-168", "name": "Spaceship [V2]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Spaceship_ref 03.19.17\nHas an open verse, possibly for <PERSON><PERSON><PERSON> to re-record a new verse over. Original snippet leaked on June 11th, 2021.", "length": "274.37", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/4c59b6fff468eddf5e7e202fd97a6708", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4c59b6fff468eddf5e7e202fd97a6708\", \"key\": \"Spaceship\", \"title\": \"Spaceship [V2]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: Spaceship_ref 03.19.17\\nHas an open verse, possibly for <PERSON><PERSON><PERSON> to re-record a new verse over. Original snippet leaked on June 11th, 2021.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"90ecc4ed4ea6d20b4ea94d44c1192597\", \"url\": \"https://api.pillowcase.su/api/download/90ecc4ed4ea6d20b4ea94d44c1192597\", \"size\": \"5.27 MB\", \"duration\": 274.37}", "aliases": [], "size": "5.27 MB"}, {"id": "spaceship-169", "name": "Spaceship [V4]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Spaceship_new arr\nHas a different verse/chorus arrangement.", "length": "274.37", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/bf9fda1fa091ce66748fc4c7df9d5abe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bf9fda1fa091ce66748fc4c7df9d5abe\", \"key\": \"Spaceship\", \"title\": \"Spaceship [V4]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: Spaceship_new arr\\nHas a different verse/chorus arrangement.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6faec8d5a9badf4d6f0af6a2553a8b9d\", \"url\": \"https://api.pillowcase.su/api/download/6faec8d5a9badf4d6f0af6a2553a8b9d\", \"size\": \"5.27 MB\", \"duration\": 274.37}", "aliases": [], "size": "5.27 MB"}, {"id": "games-we-play", "name": "Pusha T - Games We Play [V3]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> 1_Pusha ref 1 (11.1.17)\nOriginally previewed in a post by <PERSON> on November 2nd, 2017.", "length": "166.08", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/79dc6c3513a5a0347e96dcc7ad996718", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/79dc6c3513a5a0347e96dcc7ad996718\", \"key\": \"Games We Play\", \"title\": \"Pusha T - Games We Play [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"The Games We Play\", \"Chop 1\"], \"description\": \"OG Filename: KW Chop 1_Pusha ref 1 (11.1.17)\\nOriginally previewed in a post by <PERSON> on November 2nd, 2017.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"30e52d06b7ca402a41de18bdb6e5ab83\", \"url\": \"https://api.pillowcase.su/api/download/30e52d06b7ca402a41de18bdb6e5ab83\", \"size\": \"3.54 MB\", \"duration\": 166.08}", "aliases": ["The Games We Play", "Chop 1"], "size": "3.54 MB"}, {"id": "the-games-we-play", "name": "<PERSON><PERSON><PERSON> T - The Games We Play [V5]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Games We Play AK Ref 1\nHas no sub bass throughout the track, and small lyric/take differences.", "length": "166.22", "fileDate": 16293312, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/5e063f82f9cc3a855844fe4755010fd5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e063f82f9cc3a855844fe4755010fd5\", \"key\": \"The Games We Play\", \"title\": \"Pusha T - The Games We Play [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Games We Play AK Ref 1\\nHas no sub bass throughout the track, and small lyric/take differences.\", \"date\": 16293312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"49c7c999a3936218d30ec91a4235f7e6\", \"url\": \"https://api.pillowcase.su/api/download/49c7c999a3936218d30ec91a4235f7e6\", \"size\": \"3.54 MB\", \"duration\": 166.22}", "aliases": [], "size": "3.54 MB"}, {"id": "treat-you-good", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON> You Good [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON><PERSON> (7.29.17)\nThe original sample chop, lacks vocals.", "length": "152.24", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/148cfaab9d47011a79601b1a8f3d5188", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/148cfaab9d47011a79601b1a8f3d5188\", \"key\": \"Treat <PERSON> Good\", \"title\": \"<PERSON><PERSON><PERSON> T - Treat You Good [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Treat <PERSON> (7.29.17)\\nThe original sample chop, lacks vocals.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2dbd76b00e2fc6a95bb69cac9155544d\", \"url\": \"https://api.pillowcase.su/api/download/2dbd76b00e2fc6a95bb69cac9155544d\", \"size\": \"3.32 MB\", \"duration\": 152.24}", "aliases": [], "size": "3.32 MB"}, {"id": "treat-you-good-173", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON> You Good [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON><PERSON> You Good _ref 1\nFirst known version with <PERSON><PERSON><PERSON> vocals. Was seen in a list of OG filenames leaked in October 2022.", "length": "143.78", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/11f62ee248a85a3bc5ffd7952ea2546e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/11f62ee248a85a3bc5ffd7952ea2546e\", \"key\": \"Treat You Good\", \"title\": \"<PERSON>ush<PERSON> T - Treat You Good [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Treat You Good _ref 1\\nFirst known version with <PERSON><PERSON><PERSON> vocals. Was seen in a list of OG filenames leaked in October 2022.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"26f4ea709128718aedbd926e1d7efc0a\", \"url\": \"https://api.pillowcase.su/api/download/26f4ea709128718aedbd926e1d7efc0a\", \"size\": \"3.18 MB\", \"duration\": 143.78}", "aliases": [], "size": "3.18 MB"}, {"id": "treat-you-good-174", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON> You Good [V3]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "OG Filename: Treat You Good.2\nOriginally claimed to be a Kanye song, but this was later proven false. Features no <PERSON><PERSON><PERSON> vocals and long pauses between lines. Samples \"I'm Gonna Treat You Good\" by The Donations. Beat later given to Abstract Mindstate.", "length": "182.05", "fileDate": 16951680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/f209c0120f4b93be1d5cbd4589014184", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f209c0120f4b93be1d5cbd4589014184\", \"key\": \"Treat You Good\", \"title\": \"<PERSON><PERSON><PERSON> T - Treat You Good [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Treat You Good.2\\nOriginally claimed to be a Kanye song, but this was later proven false. Features no <PERSON><PERSON><PERSON> vocals and long pauses between lines. Samples \\\"I'm Gonna Treat You Good\\\" by The Donations. Beat later given to Abstract Mindstate.\", \"date\": 16951680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"be23042dd56ade16c677189b9bbf3126\", \"url\": \"https://api.pillowcase.su/api/download/be23042dd56ade16c677189b9bbf3126\", \"size\": \"3.79 MB\", \"duration\": 182.05}", "aliases": [], "size": "3.79 MB"}, {"id": "treat-you-good-175", "name": "✨ <PERSON><PERSON><PERSON> T <PERSON> <PERSON><PERSON>t You Good [V4]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON>reat You Good _ref 3\nWas seen in a list of OG filenames leaked in October 2022. <PERSON><PERSON><PERSON> has some vocals but it's mostly <PERSON><PERSON><PERSON> with open parts.", "length": "182.07", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/a7a3289b5544493a3e3964dc6599de49", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7a3289b5544493a3e3964dc6599de49\", \"key\": \"Treat <PERSON> Good\", \"title\": \"\\u2728 <PERSON>usha T - Treat You <PERSON> [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Treat You Good _ref 3\\nWas seen in a list of OG filenames leaked in October 2022. <PERSON>usha T has some vocals but it's mostly Kanye with open parts.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6d4ae93a238405e046cddd3b35e980b7\", \"url\": \"https://api.pillowcase.su/api/download/6d4ae93a238405e046cddd3b35e980b7\", \"size\": \"3.79 MB\", \"duration\": 182.07}", "aliases": [], "size": "3.79 MB"}, {"id": "treat-you-good-176", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON> You Good [V5]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: 01 <PERSON>reat You <PERSON> and <PERSON><PERSON><PERSON>-<PERSON><PERSON> reference track.", "length": "233.15", "fileDate": 16730496, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/48e36c552634db24edad7df159ab9630", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48e36c552634db24edad7df159ab9630\", \"key\": \"Treat You Good\", \"title\": \"<PERSON><PERSON><PERSON> T - Treat <PERSON> [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 01 <PERSON>reat <PERSON> and <PERSON><PERSON><PERSON>\\nAb-Liva reference track.\", \"date\": 16730496, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9ad8566ce0bdbe54b85573e3715a8ec5\", \"url\": \"https://api.pillowcase.su/api/download/9ad8566ce0bdbe54b85573e3715a8ec5\", \"size\": \"4.61 MB\", \"duration\": 233.15}", "aliases": [], "size": "4.61 MB"}, {"id": "treat-you-good-177", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON> You Good [V6]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON><PERSON>_<PERSON> and <PERSON><PERSON><PERSON> 1.1\nAlternate mix.", "length": "233.15", "fileDate": 16959456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/5628de9c2262cf9a2fa81ed9bedfcbf3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5628de9c2262cf9a2fa81ed9bedfcbf3\", \"key\": \"Treat <PERSON> Good\", \"title\": \"<PERSON><PERSON><PERSON> T - Treat You Good [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>rea<PERSON>_<PERSON> and <PERSON><PERSON><PERSON>f 1.1\\nAlternate mix.\", \"date\": 16959456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"04e875541086dd770112f5c0a89a679c\", \"url\": \"https://api.pillowcase.su/api/download/04e875541086dd770112f5c0a89a679c\", \"size\": \"4.61 MB\", \"duration\": 233.15}", "aliases": [], "size": "4.61 MB"}, {"id": "queen-bitch-178", "name": "<PERSON><PERSON><PERSON> T - Queen Bitch [V3]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "Version where <PERSON><PERSON><PERSON> has some different and missing lines, more vocals at the start, and a different vocal take. <PERSON><PERSON><PERSON> has a different verse that was later reused for \"I Love It\", and uses lines that'd soon be used for \"Wouldn't Leave\". The version available is not the full song, but a cut-down version. Recorded in 2017.", "length": "169.2", "fileDate": 15859584, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/0f8f8f376a1c6d31361573503c42683b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f8f8f376a1c6d31361573503c42683b\", \"key\": \"Queen Bitch\", \"title\": \"<PERSON><PERSON>a T - Queen Bitch [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"How Do You Respond?\", \"What Would Meek Do?\"], \"description\": \"Version where Pusha T has some different and missing lines, more vocals at the start, and a different vocal take. <PERSON><PERSON><PERSON> has a different verse that was later reused for \\\"I Love It\\\", and uses lines that'd soon be used for \\\"Wouldn't Leave\\\". The version available is not the full song, but a cut-down version. Recorded in 2017.\", \"date\": 15859584, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"44affefd31844a1d85e6168b021c741e\", \"url\": \"https://api.pillowcase.su/api/download/44affefd31844a1d85e6168b021c741e\", \"size\": \"3.59 MB\", \"duration\": 169.2}", "aliases": ["How Do <PERSON> Re<PERSON>ond?", "What Would <PERSON><PERSON> Do?"], "size": "3.59 MB"}, {"id": "how-do-you-respond", "name": "<PERSON><PERSON><PERSON> T - How Do You Respond? [V4]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: How Do You Respond ref 1 (11.21.17)\nSolo Pusha T version with an open verse.", "length": "196.54", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/ec1888c6074353fd69eee7b9850f9c4f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ec1888c6074353fd69eee7b9850f9c4f\", \"key\": \"How Do You Respond?\", \"title\": \"Pusha T - How Do You Respond? [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Queen Bitch\", \"What Would Meek Do?\"], \"description\": \"OG Filename: How Do You Respond ref 1 (11.21.17)\\nSolo Pusha T version with an open verse.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"90e9f95fac62617aa8efa5de0a98433c\", \"url\": \"https://api.pillowcase.su/api/download/90e9f95fac62617aa8efa5de0a98433c\", \"size\": \"4.03 MB\", \"duration\": 196.54}", "aliases": ["Queen Bitch", "What Would <PERSON><PERSON> Do?"], "size": "4.03 MB"}, {"id": "how-do-you-respond-180", "name": "<PERSON><PERSON><PERSON> T - How Do You Respond? [V5]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "OG Filename: How Do You Respond Ref (2.24.18)\nLater version.", "length": "161.85", "fileDate": 16999200, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/5ee58c8a2d54dc612919ef03b6b37f6d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ee58c8a2d54dc612919ef03b6b37f6d\", \"key\": \"How Do You Respond?\", \"title\": \"Pusha T - How Do You Respond? [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Queen Bitch\", \"What Would Meek Do?\"], \"description\": \"OG Filename: How Do You Respond Ref (2.24.18)\\nLater version.\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c85b09faacbe544367bfd76761c9e5a7\", \"url\": \"https://api.pillowcase.su/api/download/c85b09faacbe544367bfd76761c9e5a7\", \"size\": \"3.47 MB\", \"duration\": 161.85}", "aliases": ["Queen Bitch", "What Would <PERSON><PERSON> Do?"], "size": "3.47 MB"}, {"id": "what-would-meek-do", "name": "<PERSON><PERSON><PERSON> T - What Would <PERSON><PERSON> Do? [V6]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "Has a beatswitch. Not an actual bounce, but bounced from the ProTools session.", "length": "7.06", "fileDate": 16687296, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/8eb9e5e286a74460ebab91497f6c3fe0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8eb9e5e286a74460ebab91497f6c3fe0\", \"key\": \"What Would Meek Do?\", \"title\": \"Pusha T - What Would Meek Do? [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Has a beatswitch. Not an actual bounce, but bounced from the ProTools session.\", \"date\": 16687296, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"de234eaa55e1faf15ff73f988513b25a\", \"url\": \"https://api.pillowcase.su/api/download/de234eaa55e1faf15ff73f988513b25a\", \"size\": \"993 kB\", \"duration\": 7.06}", "aliases": [], "size": "993 kB"}, {"id": "what-would-meek-do-182", "name": "<PERSON><PERSON><PERSON> T - What Would <PERSON><PERSON> Do? [V7]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "Has alternate Kanye lyrics, not present in the released version, where he name drops <PERSON><PERSON> and <PERSON>.", "length": "191.69", "fileDate": 15607296, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/bc693f2dcf5d7223eba539e9c68782cd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bc693f2dcf5d7223eba539e9c68782cd\", \"key\": \"What Would Meek Do?\", \"title\": \"Pusha T - What Would Meek Do? [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Has alternate Kanye lyrics, not present in the released version, where he name drops <PERSON><PERSON> and <PERSON>.\", \"date\": 15607296, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e9a6589cde4bc83c29a4de64067b3bdc\", \"url\": \"https://api.pillowcase.su/api/download/e9a6589cde4bc83c29a4de64067b3bdc\", \"size\": \"3.95 MB\", \"duration\": 191.69}", "aliases": [], "size": "3.95 MB"}, {"id": "what-would-meek-do-183", "name": "<PERSON><PERSON>a T - What Would <PERSON><PERSON> Do? [V8]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "Early version, with the only difference seemingly being <PERSON><PERSON><PERSON> doing his intro instead of <PERSON><PERSON><PERSON>.", "length": "154.17", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/0e5697a051187e8a6c21652375e18d80", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0e5697a051187e8a6c21652375e18d80\", \"key\": \"What Would Meek Do?\", \"title\": \"<PERSON>usha T - What Would Meek Do? [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Early version, with the only difference seemingly being <PERSON><PERSON><PERSON> doing his intro instead of <PERSON><PERSON><PERSON>.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4012daa6f1678042150682bf464bf9cf\", \"url\": \"https://api.pillowcase.su/api/download/4012daa6f1678042150682bf464bf9cf\", \"size\": \"3.35 MB\", \"duration\": 154.17}", "aliases": [], "size": "3.35 MB"}, {"id": "can-t-afford-it", "name": "<PERSON> Kid - Can't Afford It", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON><PERSON> Afford It Pusha ref_1\nSong from 2017 made during the Blobama sessions.", "length": "173.44", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "hitler", "originalUrl": "https://pillowcase.su/f/9e8cd0783687d9511dbb6a03b1f6fcae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9e8cd0783687d9511dbb6a03b1f6fcae\", \"key\": \"Can't Afford It\", \"title\": \"<PERSON> The Kid - Can't Afford It\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Can-t Afford It Pusha ref_1\\nSong from 2017 made during the Blobama sessions.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"435a31d863b0db3304b20a2fcebc828a\", \"url\": \"https://api.pillowcase.su/api/download/435a31d863b0db3304b20a2fcebc828a\", \"size\": \"3.66 MB\", \"duration\": 173.44}", "aliases": [], "size": "3.66 MB"}]}