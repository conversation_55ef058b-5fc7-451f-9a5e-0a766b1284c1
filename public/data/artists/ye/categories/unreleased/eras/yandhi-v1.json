{"id": "yandhi-v1", "name": "Yan<PERSON> [V1]", "description": "Upon hearing the beat to \"Hurricane\" during sessions for Good Ass Job, <PERSON><PERSON><PERSON> became inspired to create a whole new album titled Yan<PERSON>. With the album's concept in his mind, <PERSON><PERSON><PERSON> and his producers began frenzied work as they developed multiple new songs throughout September 2018, aiming for a September 29 release date. As they did not meet this deadline, <PERSON><PERSON><PERSON> went to Uganda to conduct further work on the album but delayed it indefinitely on November 13.", "backgroundColor": "rgb(241, 235, 255)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17G-3gMCXxRckawxkulgZHLyX_Y5OVk7ozDcgX55pFrxK1Z9chBk4cFvkl_dP9N1mypMuVNoWimzaQA60gzSHWUAIW3WTRHwDFpvSNPdgyl_Q5c2NHRDy2cgK_vTvdD3bjTo3VQxERLy5ks4h7E?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "brothers", "name": "Brothers [V9]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Brothers - 07.12.18 [Z<PERSON> Bounce]\nWorked on by <PERSON>.", "length": "69.82", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/374a6a160d0a734a8ba478c19f0056e5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/374a6a160d0a734a8ba478c19f0056e5\", \"key\": \"Brothers\", \"title\": \"Brothers [V9]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: Brothers - 07.12.18 [<PERSON><PERSON>]\\nWorked on by <PERSON>.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1a71a6611b2dda8365e2ec6c5f00a6a2\", \"url\": \"https://api.pillowcase.su/api/download/1a71a6611b2dda8365e2ec6c5f00a6a2\", \"size\": \"1.81 MB\", \"duration\": 69.82}", "aliases": [], "size": "1.81 MB"}, {"id": "brothers-2", "name": "✨ Brothers [V10]", "artists": [], "producers": [], "notes": "OG Filename: Brothers - <PERSON><PERSON> Freestyle 07.20.18\nFreestyle recorded after ye released. Includes some mumble and some clear words. <PERSON><PERSON><PERSON> gets emotional towards the end. The beginning of the song is from a previous version. Original CDQ file leaked on November 11th, 2021, in response to <PERSON><PERSON> scamming $3,000 in a groupbuy.", "length": "1217.84", "fileDate": 17065728, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/0850fa624c0ab94c6267227f1b6c0570", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0850fa624c0ab94c6267227f1b6c0570\", \"key\": \"Brothers\", \"title\": \"\\u2728 Brothers [V10]\", \"description\": \"OG Filename: Brothers - <PERSON><PERSON> Freestyle 07.20.18\\nFreestyle recorded after ye released. Includes some mumble and some clear words. <PERSON><PERSON><PERSON> gets emotional towards the end. The beginning of the song is from a previous version. Original CDQ file leaked on November 11th, 2021, in response to <PERSON><PERSON> scamming $3,000 in a groupbuy.\", \"date\": 17065728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f86563087c0c5dafd6499bd742eeb277\", \"url\": \"https://api.pillowcase.su/api/download/f86563087c0c5dafd6499bd742eeb277\", \"size\": \"20.2 MB\", \"duration\": 1217.84}", "aliases": [], "size": "20.2 MB"}, {"id": "brothers-3", "name": "Brothers [V11]", "artists": [], "producers": [], "notes": "OG Filename: Brothers - Ant Version KW ref\nVersion with cut-down freestyle vocals, with the <PERSON><PERSON> vocals found on other versions. Created from a bounce of stems.", "length": "405.84", "fileDate": 17065728, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/98199a956ba64650b3a36aff2adcba88", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/98199a956ba64650b3a36aff2adcba88\", \"key\": \"Brothers\", \"title\": \"Brothers [V11]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: Brothers - Ant Version KW ref\\nVersion with cut-down freestyle vocals, with the <PERSON><PERSON> vocals found on other versions. Created from a bounce of stems.\", \"date\": 17065728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"163a9488a6b3eeaa163f899a73717966\", \"url\": \"https://api.pillowcase.su/api/download/163a9488a6b3eeaa163f899a73717966\", \"size\": \"7.18 MB\", \"duration\": 405.84}", "aliases": [], "size": "7.18 MB"}, {"id": "exctacty", "name": "Exctacty [V12]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Exctacty 08.10.18 INSTRUMENTAL\nInstrumental bounced on August 10th, 2018.", "length": "179.94", "fileDate": 17159904, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/63e9cbb74fb2c5e65fa4a64b4c9ef960", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63e9cbb74fb2c5e65fa4a64b4c9ef960\", \"key\": \"Exctacty\", \"title\": \"Exctacty [V12]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"XTCY\", \"Extacy\"], \"description\": \"OG Filename: Exctacty 08.10.18 INSTRUMENTAL\\nInstrumental bounced on August 10th, 2018.\", \"date\": 17159904, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"80ee593abaadecfdcc8b7582e338d95b\", \"url\": \"https://api.pillowcase.su/api/download/80ee593abaadecfdcc8b7582e338d95b\", \"size\": \"3.57 MB\", \"duration\": 179.94}", "aliases": ["XTCY", "Extacy"], "size": "3.57 MB"}, {"id": "xtcy", "name": "XTCY [V13]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "Posted by <PERSON> to Twitter. Is the same as released with the OG metadata, but unmastered.", "length": "176.51", "fileDate": 15339456, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/738f860e5017eaeeca94f108aedeb718", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/738f860e5017eaeeca94f108aedeb718\", \"key\": \"XTCY\", \"title\": \"XTCY [V13]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Extacty\", \"Extacy\"], \"description\": \"Posted by Ye to Twitter. Is the same as released with the OG metadata, but unmastered.\", \"date\": 15339456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0e9269d412a33201c47d8ee3b1a503e8\", \"url\": \"https://api.pillowcase.su/api/download/0e9269d412a33201c47d8ee3b1a503e8\", \"size\": \"3.52 MB\", \"duration\": 176.51}", "aliases": ["Extacty", "Extacy"], "size": "3.52 MB"}, {"id": "faith", "name": "Faith [V1]", "artists": [], "producers": ["<PERSON>", "Edscusive"], "notes": "OG Filename: <PERSON> <PERSON> <PERSON><PERSON> Ref for CyHi 07.20.18\nEarliest known version, originally made in July 2018 and titled \"Faith\". Features rough Ye mumble vocals. Later reworked in 2019 and 2020.", "length": "122.96", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/4dcdad211be13babe205949673c4f2bf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4dcdad211be13babe205949673c4f2bf\", \"key\": \"Faith\", \"title\": \"Faith [V1]\", \"artists\": \"(prod. <PERSON> & Edscusive)\", \"aliases\": [\"Heaven Calls\", \"When Heaven Calls\", \"Freedom\"], \"description\": \"OG Filename: Faith - KW Ref for CyHi 07.20.18\\nEarliest known version, originally made in July 2018 and titled \\\"Faith\\\". Features rough Ye mumble vocals. Later reworked in 2019 and 2020.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"68a1d700a10106069e460615a2d7fdb9\", \"url\": \"https://api.pillowcase.su/api/download/68a1d700a10106069e460615a2d7fdb9\", \"size\": \"2.66 MB\", \"duration\": 122.96}", "aliases": ["Heaven Calls", "When Heaven Calls", "Freedom"], "size": "2.66 MB"}, {"id": "faith-7", "name": "Faith [V2]", "artists": [], "producers": ["<PERSON>", "Edscusive"], "notes": "OG Filename: Faith JDRough2\nCyHi reference track. Made before the Ant Clemons reference track as it has a lot simpler production matching the original Kanye ref. Reused in 2019.", "length": "171", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/9f8545eec225c5e1cded9c77c6158953", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9f8545eec225c5e1cded9c77c6158953\", \"key\": \"Faith\", \"title\": \"Faith [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON> & Edscusive)\", \"aliases\": [\"Heaven Calls\", \"When Heaven Calls\", \"Freedom\"], \"description\": \"OG Filename: Faith J<PERSON>ough2\\nCyHi reference track. Made before the Ant Clemons reference track as it has a lot simpler production matching the original Kanye ref. Reused in 2019.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e491457acc3efa58b3e51a41f5e26a29\", \"url\": \"https://api.pillowcase.su/api/download/e491457acc3efa58b3e51a41f5e26a29\", \"size\": \"3.43 MB\", \"duration\": 171}", "aliases": ["Heaven Calls", "When Heaven Calls", "Freedom"], "size": "3.43 MB"}, {"id": "when-heaven-calls", "name": "When Heaven Calls [V3]", "artists": [], "producers": ["<PERSON>", "Edsclusive", "<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> - When Heaven Calls - JackRo.DiGenius.AntClemons\nAnt Clemons reference track. Disregarded when it leaked November 2020 in a large pile of Ant Clemons leaks, but then rediscovered as a Kanye reference as it shared the same beat as the KayCyy reference track snippet (before that version leaked in full).", "length": "209.27", "fileDate": 16050528, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e9ff93b3c4455cbb617bf7adc18a2a82", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9ff93b3c4455cbb617bf7adc18a2a82\", \"key\": \"When Heaven Calls\", \"title\": \"When Heaven Calls [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>, Edsclusive & Di <PERSON>ius)\", \"aliases\": [\"Faith\", \"Freedom\", \"Heaven Calls\"], \"description\": \"OG Filename: Ant Clemons - When Heaven Calls - JackRo.DiGenius.AntClemons\\nAnt Clemons reference track. Disregarded when it leaked November 2020 in a large pile of Ant Clemons leaks, but then rediscovered as a Kanye reference as it shared the same beat as the KayCyy reference track snippet (before that version leaked in full).\", \"date\": 16050528, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1c50e23403bdea37317a88069b4e77f3\", \"url\": \"https://api.pillowcase.su/api/download/1c50e23403bdea37317a88069b4e77f3\", \"size\": \"4.04 MB\", \"duration\": 209.27}", "aliases": ["Faith", "Freedom", "Heaven Calls"], "size": "4.04 MB"}, {"id": "home", "name": "Home [V3]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> Ref for CyHi 07.21.18\nHas a looped <PERSON><PERSON> chorus and cutdown freestyle Ye vocals.", "length": "347.95", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/4253d3ce88c5fe42e0fdb3d788b0d7db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4253d3ce88c5fe42e0fdb3d788b0d7db\", \"key\": \"Home\", \"title\": \"Home [V3]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"Follow The Light\"], \"description\": \"OG Filename: KW Ref for CyHi 07.21.18\\nHas a looped <PERSON><PERSON>lem<PERSON> chorus and cutdown freestyle Ye vocals.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"72093bf7e89f4c6b722f22082523bf18\", \"url\": \"https://api.pillowcase.su/api/download/72093bf7e89f4c6b722f22082523bf18\", \"size\": \"6.26 MB\", \"duration\": 347.95}", "aliases": ["Follow The Light"], "size": "6.26 MB"}, {"id": "sluts-are-awesome", "name": "Sluts Are Awesome [V3]", "artists": ["<PERSON>", "<PERSON>", "Tyga"], "producers": ["RONNY J", "<PERSON>", "CBMIX"], "notes": "Later version of \"Sluts Are Awesome\". <PERSON> and <PERSON><PERSON><PERSON> have the same vocals as released, and <PERSON><PERSON> has a verse.", "length": "159.16", "fileDate": 16988832, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6d4b8be76004d34eb2fd2a466519f23d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d4b8be76004d34eb2fd2a466519f23d\", \"key\": \"Sluts Are Awesome\", \"title\": \"Sluts Are Awesome [V3]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Clark Kent & CBMIX)\", \"aliases\": [\"I Love It\"], \"description\": \"Later version of \\\"Sluts Are Awesome\\\". <PERSON> and <PERSON><PERSON><PERSON> have the same vocals as released, and <PERSON><PERSON> has a verse.\", \"date\": 16988832, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"419a5fb65cd04184cfe53b41d3b208bf\", \"url\": \"https://api.pillowcase.su/api/download/419a5fb65cd04184cfe53b41d3b208bf\", \"size\": \"3.24 MB\", \"duration\": 159.16}", "aliases": ["I Love It"], "size": "3.24 MB"}, {"id": "i-love-it", "name": "I Love It [V4]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["RONNY J", "<PERSON>", "CBMIX"], "notes": "OG Filename: I Love It (explicit master)\nOG file for release.", "length": "128.01", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/aa703a31651f0723dd6c1e8140426781", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aa703a31651f0723dd6c1e8140426781\", \"key\": \"I Love It\", \"title\": \"I Love It [V4]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Clark Kent & CBMIX)\", \"aliases\": [\"Sluts Are Awesome\"], \"description\": \"OG Filename: I Love It (explicit master)\\nOG file for release.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6247bda21615364bea732f4e9d546f7e\", \"url\": \"https://api.pillowcase.su/api/download/6247bda21615364bea732f4e9d546f7e\", \"size\": \"2.74 MB\", \"duration\": 128.01}", "aliases": ["Sluts Are Awesome"], "size": "2.74 MB"}, {"id": "i-love-it-12", "name": "I Love It [V4] [Clean]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["RONNY J", "<PERSON>", "CBMIX"], "notes": "OG Filenames: I Love It (Clean) &\nLOVE IT MIKE JESS MIX 10 CLEAN\nOG file for the clean release file. Leaked due to a 1.2 Terabyte leak.", "length": "127.95", "fileDate": 17196192, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/fe3495c578899a5d4f62f135ccf89b37", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe3495c578899a5d4f62f135ccf89b37\", \"key\": \"I Love It\", \"title\": \"I Love It [V4] [Clean]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Clark <PERSON> & CBMIX)\", \"aliases\": [\"Sluts Are Awesome\"], \"description\": \"OG Filenames: I Love It (Clean) &\\nLOVE IT MIKE JESS MIX 10 CLEAN\\nOG file for the clean release file. Leaked due to a 1.2 Terabyte leak.\", \"date\": 17196192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"30a868cee51ecb06d8d6a4d0051ebe34\", \"url\": \"https://api.pillowcase.su/api/download/30a868cee51ecb06d8d6a4d0051ebe34\", \"size\": \"2.74 MB\", \"duration\": 127.95}", "aliases": ["Sluts Are Awesome"], "size": "2.74 MB"}, {"id": "i-love-it-13", "name": "I Love It [V6]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["RONNY J", "<PERSON>", "CBMIX"], "notes": "Superclean ref done by Consequence. Played on his Instagram Live August 11th, 2024.", "length": "31.27", "fileDate": 17233344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/86b445da666203b2dc1c006e0cdd0e84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86b445da666203b2dc1c006e0cdd0e84\", \"key\": \"I Love It\", \"title\": \"I Love It [V6]\", \"artists\": \"(ref. Consequence) (feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Clark <PERSON> & CBMIX)\", \"aliases\": [\"Sluts Are Awesome\"], \"description\": \"Superclean ref done by Consequence. Played on his Instagram Live August 11th, 2024.\", \"date\": 17233344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2ff47420838d0034d6c4fdfac747f4b1\", \"url\": \"https://api.pillowcase.su/api/download/2ff47420838d0034d6c4fdfac747f4b1\", \"size\": \"1.19 MB\", \"duration\": 31.27}", "aliases": ["Sluts Are Awesome"], "size": "1.19 MB"}, {"id": "the-ballad-of-kanye-west", "name": "The Ballad of <PERSON><PERSON><PERSON> (The Chosen One) West", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: The Ballad of Kanye (The Chosen One) West\nReference track made for <PERSON><PERSON><PERSON> in \"either 2015 or 2016\" but was also bounced 2018, according to <PERSON><PERSON>. Unknown if a version with <PERSON><PERSON><PERSON> vocals exists. A version was rumored to include <PERSON><PERSON> and <PERSON>, but this was a hoax", "length": "171", "fileDate": 15922656, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/d6deb00e655c1465faec7a6aad98daba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d6deb00e655c1465faec7a6aad98daba\", \"key\": \"The Ballad of Kanye (The Chosen One) West\", \"title\": \"The Ballad of Kany<PERSON> (The Chosen One) West\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: The Ballad of Kanye (The Chosen One) West\\nReference track made for <PERSON><PERSON><PERSON> in \\\"either 2015 or 2016\\\" but was also bounced 2018, according to <PERSON><PERSON>. Unknown if a version with <PERSON><PERSON><PERSON> vocals exists. A version was rumored to include <PERSON><PERSON> and <PERSON>, but this was a hoax\", \"date\": 15922656, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"581b92825a71bd94025695a818930169\", \"url\": \"https://api.pillowcase.su/api/download/581b92825a71bd94025695a818930169\", \"size\": \"3.43 MB\", \"duration\": 171}", "aliases": [], "size": "3.43 MB"}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "Track said to be made in 2018. Nothing else is known. Snippet leaked on August 12th, 2024.", "length": "8.75", "fileDate": 17234208, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1f5d7dbc8ee4cd0938b4ac6dd970006d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f5d7dbc8ee4cd0938b4ac6dd970006d\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Curfew\"], \"description\": \"Track said to be made in 2018. Nothing else is known. Snippet leaked on August 12th, 2024.\", \"date\": 17234208, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0c45d108f22d465a9860a3d4598310e9\", \"url\": \"https://api.pillowcase.su/api/download/0c45d108f22d465a9860a3d4598310e9\", \"size\": \"832 kB\", \"duration\": 8.75}", "aliases": ["Curfew"], "size": "832 kB"}, {"id": "seven-chords", "name": "Seven Chords [V2]", "artists": [], "producers": ["7 Aurel<PERSON>"], "notes": "OG Filename: Seven Chords Shake 06.17.18\n070 Shake reference track reportedly intended for <PERSON><PERSON><PERSON>. \"Seven Chords\" likely isn't a song name, rather referring to <PERSON> Au<PERSON>ius producing the chords instrumental. Snippet surfaced in June 2021 and was said to be from ye sessions, but that was disproved when another snippet leaked alongside the OG filename. VC recorded snippet leaked December 20nd, 2022.", "length": "209.98", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/9bcb4b30994fa24e219258f3c3d7304c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9bcb4b30994fa24e219258f3c3d7304c\", \"key\": \"Seven Chords\", \"title\": \"Seven Chords [V2]\", \"artists\": \"(ref. 070 Shake) (prod. 7 Aurelius) \", \"aliases\": [\"Never Would Have Made It\"], \"description\": \"OG Filename: Seven Chords Shake 06.17.18\\n070 Shake reference track reportedly intended for <PERSON><PERSON><PERSON>. \\\"Seven Chords\\\" likely isn't a song name, rather referring to <PERSON> Aurelius producing the chords instrumental. Snippet surfaced in June 2021 and was said to be from ye sessions, but that was disproved when another snippet leaked alongside the OG filename. VC recorded snippet leaked December 20nd, 2022.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f1045b54bd54257af98a6f6d2d8c0ff7\", \"url\": \"https://api.pillowcase.su/api/download/f1045b54bd54257af98a6f6d2d8c0ff7\", \"size\": \"4.05 MB\", \"duration\": 209.98}", "aliases": ["Never Would Have Made It"], "size": "4.05 MB"}, {"id": "never-would-have-made-it", "name": "<PERSON><PERSON> - Never Would Have Made It [V3]", "artists": ["Kanye West"], "producers": ["7 Aurel<PERSON>", "BONGO ByTheWay", "Robot Scott"], "notes": "OG FIlename: NEVER WOULD HAVE MADE IT ANT JOZZY BONGO SCOTT 97\nAnt Clemons song that features short snippets of <PERSON><PERSON><PERSON> vocals. Has an open verse, most likely for <PERSON><PERSON>. Uses a further along version of the \"Seven Chords\" instrumental. Unknown where <PERSON><PERSON><PERSON>'s vocals originally come from, or when he recorded. Original snippet leaked December 30th, 2022, with two more leaking October 14th, 2023.", "length": "126.87", "fileDate": 17116704, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/5326974463e384d0d7f2c80747e84165", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5326974463e384d0d7f2c80747e84165\", \"key\": \"Never Would Have Made It\", \"title\": \"<PERSON><PERSON> - Never Would Have Made It [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 7 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Robot Scott)\", \"aliases\": [\"Seven Chords\"], \"description\": \"OG FIlename: NEVER WOULD HAVE MADE IT ANT JOZZY BONGO SCOTT 97\\nAnt Clemons song that features short snippets of <PERSON><PERSON><PERSON> vocals. Has an open verse, most likely for <PERSON><PERSON>. Uses a further along version of the \\\"Seven Chords\\\" instrumental. Unknown where <PERSON><PERSON><PERSON>'s vocals originally come from, or when he recorded. Original snippet leaked December 30th, 2022, with two more leaking October 14th, 2023.\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e6073bb953a44b39f88849511722cf91\", \"url\": \"https://api.pillowcase.su/api/download/e6073bb953a44b39f88849511722cf91\", \"size\": \"2.72 MB\", \"duration\": 126.87}", "aliases": ["Seven Chords"], "size": "2.72 MB"}, {"id": "tekken", "name": "6ix9ine - Tekken [V1]", "artists": ["Kanye West"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: Tekken Incomplete 9.8.18 2.2\nEarliest known version. Metadata in the file suggests this version was <PERSON><PERSON><PERSON><PERSON>'s song.", "length": "167.86", "fileDate": 16345152, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1bdc621e3f432b7a2bbf18ddfadef716", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1bdc621e3f432b7a2bbf18ddfadef716\", \"key\": \"Tekken\", \"title\": \"6ix9ine - Tekken [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KANGA\"], \"description\": \"OG Filename: Tekken Incomplete 9.8.18 2.2\\nEarliest known version. Metadata in the file suggests this version was 6<PERSON><PERSON><PERSON>'s song.\", \"date\": 16345152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"40ed2b3c54e7fd63b5aecdb6ac1eccf4\", \"url\": \"https://api.pillowcase.su/api/download/40ed2b3c54e7fd63b5aecdb6ac1eccf4\", \"size\": \"3.38 MB\", \"duration\": 167.86}", "aliases": ["KANGA"], "size": "3.38 MB"}, {"id": "teken", "name": "6ix9ine - <PERSON><PERSON> [V2]", "artists": ["Kanye West"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> - KW Long Freestyle 09.09.18\nLikely where the \"girlfriend look like a ogre\" line on released version came from, as this was not found on the later leaked versions.", "length": "652.4", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/dcf265c75f2e114bfb1f56d331a607e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dcf265c75f2e114bfb1f56d331a607e1\", \"key\": \"Teken\", \"title\": \"6ix9<PERSON> - <PERSON><PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KAN<PERSON>\", \"Tekken\"], \"description\": \"OG Filename: Teken Beat - KW Long Freestyle 09.09.18\\nLikely where the \\\"girlfriend look like a ogre\\\" line on released version came from, as this was not found on the later leaked versions.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c6c223693730570f812ed6af5d26fe33\", \"url\": \"https://api.pillowcase.su/api/download/c6c223693730570f812ed6af5d26fe33\", \"size\": \"11.1 MB\", \"duration\": 652.4}", "aliases": ["KANGA", "Tekken"], "size": "11.1 MB"}, {"id": "model", "name": "✨ CyHi - Model [V9]", "artists": ["Kanye West", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: Model- REF 09.06.18\nCyHi version from 2018. Has mumble Ty vocals.", "length": "307.28", "fileDate": 17207424, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7bc36403f2fb770ab781db485ae6d9e8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7bc36403f2fb770ab781db485ae6d9e8\", \"key\": \"Model\", \"title\": \"\\u2728 CyHi - Model [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> $ign)\", \"aliases\": [\"Model Type\", \"You Ain't No Model\"], \"description\": \"OG Filename: Model- REF 09.06.18\\nCyHi version from 2018. Has mumble Ty vocals.\", \"date\": 17207424, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dcf9020283dafd25d324e0cc1941a5f8\", \"url\": \"https://api.pillowcase.su/api/download/dcf9020283dafd25d324e0cc1941a5f8\", \"size\": \"5.61 MB\", \"duration\": 307.28}", "aliases": ["Model Type", "You Ain't No Model"], "size": "5.61 MB"}, {"id": "model-type", "name": "Ty Dolla $ign - Model Type [V10]", "artists": ["Kanye West", "Cy<PERSON><PERSON>"], "producers": [], "notes": "Has new production, and new vocals. <PERSON><PERSON> <PERSON>'s song as the snippet was found on IRKO's website, who worked with him before working with <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON>.", "length": "42.97", "fileDate": 16783200, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f60eb32c046e44a7bd97e623d48807ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f60eb32c046e44a7bd97e623d48807ce\", \"key\": \"Model Type\", \"title\": \"Ty Dolla $ign - Model Type [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & CyHi)\", \"aliases\": [\"You Ain't No Model\", \"Model\"], \"description\": \"Has new production, and new vocals. Like<PERSON> <PERSON>'s song as the snippet was found on IRKO's website, who worked with him before working with <PERSON><PERSON><PERSON> or Cy<PERSON>i.\", \"date\": 16783200, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c1de04623da3d8ac61b0763a4eebef3\", \"url\": \"https://api.pillowcase.su/api/download/2c1de04623da3d8ac61b0763a4eebef3\", \"size\": \"1.38 MB\", \"duration\": 42.97}", "aliases": ["You Ain't No Model", "Model"], "size": "1.38 MB"}, {"id": "i-m-mad-ok", "name": "Vory - I'm <PERSON> [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON>'m <PERSON>_DroRef 1\n\"Ok Ok\" was originally a Vory song recorded in 2018 that <PERSON><PERSON><PERSON> took for himself when the two linked up on July 25th, 2021. Is also in one of the Donda album copies. OG file leaked alongside the session.", "length": "139.88", "fileDate": 17169408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b038c5dbd976b77df9800696ebfdf582", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b038c5dbd976b77df9800696ebfdf582\", \"key\": \"I'm <PERSON>\", \"title\": \"<PERSON><PERSON> - I'm <PERSON> [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I'm Not <PERSON>\", \"Ok Ok\"], \"description\": \"OG Filename: I'm <PERSON>_<PERSON>ef 1\\n\\\"Ok Ok\\\" was originally a Vory song recorded in 2018 that <PERSON><PERSON><PERSON> took for himself when the two linked up on July 25th, 2021. Is also in one of the Donda album copies. OG file leaked alongside the session.\", \"date\": 17169408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e0332dbf117a850301d450cc09d06904\", \"url\": \"https://api.pillowcase.su/api/download/e0332dbf117a850301d450cc09d06904\", \"size\": \"2.93 MB\", \"duration\": 139.88}", "aliases": ["I'm Not Ok", "Ok Ok"], "size": "2.93 MB"}, {"id": "i-m-mad-ok-23", "name": "V<PERSON> - I'm <PERSON> [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I'm <PERSON>_DroRef 2\nHas better mixing. OG file leaked alongside the session.", "length": "139.55", "fileDate": 17169408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/efe7b091697aabbf3179be46f7e7114e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/efe7b091697aabbf3179be46f7e7114e\", \"key\": \"I'm <PERSON> Ok\", \"title\": \"<PERSON><PERSON> - <PERSON>'m <PERSON> [V2]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I'm Not <PERSON>\", \"Ok Ok\"], \"description\": \"OG Filename: I'm <PERSON>_DroRef 2\\nHas better mixing. OG file leaked alongside the session.\", \"date\": 17169408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"157ab52c1f4506ce575dab84352c3aed\", \"url\": \"https://api.pillowcase.su/api/download/157ab52c1f4506ce575dab84352c3aed\", \"size\": \"2.92 MB\", \"duration\": 139.55}", "aliases": ["I'm Not Ok", "Ok Ok"], "size": "2.92 MB"}, {"id": "one-minute", "name": "XXXTENTACION - One Minute [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "Original version of \"One Minute\" previewed four days before XXXTENTACION died. Did not feature <PERSON><PERSON><PERSON> at the time as the two never worked together while <PERSON> was alive. Any other differences are currently unknown.", "length": "30.25", "fileDate": 15289344, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f28dc77e05b2876c19a5d6237d2ae14e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f28dc77e05b2876c19a5d6237d2ae14e\", \"key\": \"One Minute\", \"title\": \"XXXTENTACION - One Minute [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"Original version of \\\"One Minute\\\" previewed four days before XXXTENTA<PERSON><PERSON> died. Did not feature <PERSON><PERSON><PERSON> at the time as the two never worked together while <PERSON> was alive. Any other differences are currently unknown.\", \"date\": 15289344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"155127fb6c082e9ee499e9034ff9e478\", \"url\": \"https://api.pillowcase.su/api/download/155127fb6c082e9ee499e9034ff9e478\", \"size\": \"1.17 MB\", \"duration\": 30.25}", "aliases": [], "size": "1.17 MB"}, {"id": "yes-indeed", "name": "XXXTENTACION - Yes Indeed (Freestyle)", "artists": [], "producers": ["Wheezy", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "notes": "The XXXTENTACION verse used on \"The Storm\" is from a freestyle over the \"Yes Indeed\" beat before he died. After <PERSON> was removed from \"The Storm\", the vocals were released on \"Voices\" by <PERSON>.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://www.youtube.com/shorts/FoOmkR1PSvk", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/shorts/FoOmkR1PSvk\", \"key\": \"Yes Indeed (Freestyle)\", \"title\": \"XXXTENTACION - Yes Indeed (Freestyle)\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>-Rackz)\", \"description\": \"The XXXTENTACION verse used on \\\"The Storm\\\" is from a freestyle over the \\\"Yes Indeed\\\" beat before he died. After <PERSON> was removed from \\\"The Storm\\\", the vocals were released on \\\"Voices\\\" by <PERSON>.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "20th", "name": "20th", "artists": [], "producers": ["Bizness Boi"], "notes": "OG Filename: 20th (bis) - <PERSON>t Ref\nRough Ant Clemons reference track, from the early sessions for <PERSON><PERSON>. In the bleed, <PERSON> can be heard talking about moving his offices to Chicago.", "length": "84.64", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c693797a38844ca323e4fa71e31a3a04", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c693797a38844ca323e4fa71e31a3a04\", \"key\": \"20th\", \"title\": \"20th\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 20th (bis) - Ant <PERSON>f\\nRough Ant Clemons reference track, from the early sessions for Yandhi. In the bleed, <PERSON> can be heard talking about moving his offices to Chicago.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d079500d7fbd6daa76c98fe137a3ea0\", \"url\": \"https://api.pillowcase.su/api/download/8d079500d7fbd6daa76c98fe137a3ea0\", \"size\": \"2.05 MB\", \"duration\": 84.64}", "aliases": [], "size": "2.05 MB"}, {"id": "120th", "name": "120th", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: 120th Brodi - K<PERSON> and <PERSON><PERSON>-produced track, with mum<PERSON> and <PERSON><PERSON> vocals.", "length": "123.01", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a40e4c9f72f5ab8781e8e701b7caae92", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a40e4c9f72f5ab8781e8e701b7caae92\", \"key\": \"120th\", \"title\": \"120th\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 120th <PERSON>rodi - KW and <PERSON><PERSON> \\nRough B<PERSON>ki-produced track, with mum<PERSON> <PERSON><PERSON> and <PERSON><PERSON> vocals.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"560d39abba68c9cb327708a1f3396809\", \"url\": \"https://api.pillowcase.su/api/download/560d39abba68c9cb327708a1f3396809\", \"size\": \"2.66 MB\", \"duration\": 123.01}", "aliases": [], "size": "2.66 MB"}, {"id": "124th", "name": "124th [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: 124th Brodi - KW and <PERSON><PERSON> reference track, with mumble <PERSON><PERSON> vocals and extremely quiet Ye vocals in the background.", "length": "93.49", "fileDate": 16294176, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a909ae42a4e899af3e84aefdf9185909", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a909ae42a4e899af3e84aefdf9185909\", \"key\": \"124th\", \"title\": \"124th [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 124th Brodi - KW and Ant <PERSON> \\nRough reference track, with mumble <PERSON><PERSON> vocals and extremely quiet Ye vocals in the background.\", \"date\": 16294176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c4556358c27488ba251fa8c0d66f5346\", \"url\": \"https://api.pillowcase.su/api/download/c4556358c27488ba251fa8c0d66f5346\", \"size\": \"2.19 MB\", \"duration\": 93.49}", "aliases": [], "size": "2.19 MB"}, {"id": "124th-29", "name": "124th [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: 124th - KW Ant Ref [LOUDER]\nSame as the initial version, but louder.", "length": "93.49", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6162d97adeb53d622738e1a0d7f707b4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6162d97adeb53d622738e1a0d7f707b4\", \"key\": \"124th\", \"title\": \"124th [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 124th - KW Ant Ref [LOUDER]\\nSame as the initial version, but louder.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"28202cfd458b511b05bd76f58d7e1c4f\", \"url\": \"https://api.pillowcase.su/api/download/28202cfd458b511b05bd76f58d7e1c4f\", \"size\": \"2.19 MB\", \"duration\": 93.49}", "aliases": [], "size": "2.19 MB"}, {"id": "124th-30", "name": "124th [V3]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: 124th Brodi - Ant Ref 2\nSecond Ant Clemons reference track.", "length": "94.56", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/cf808d7cf744e309056d321af325daba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf808d7cf744e309056d321af325daba\", \"key\": \"124th\", \"title\": \"124th [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 124th Brodi - Ant Ref 2\\nSecond Ant Clemons reference track.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b01fd1613aca11d299256ac1928228a5\", \"url\": \"https://api.pillowcase.su/api/download/b01fd1613aca11d299256ac1928228a5\", \"size\": \"2.21 MB\", \"duration\": 94.56}", "aliases": [], "size": "2.21 MB"}, {"id": "ac4", "name": "🗑️ AC4 [V1]", "artists": [], "producers": [], "notes": "OG Filename: AC4 - Ant Ref\nVersion with rough <PERSON><PERSON> vocals. Referred to as the \"worst Ant song ever\" by Waterfalls.", "length": "69.9", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/d7a2c3b53d81443bc0877a2202853eb6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d7a2c3b53d81443bc0877a2202853eb6\", \"key\": \"AC4\", \"title\": \"\\ud83d\\uddd1\\ufe0f AC4 [V1]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: AC4 - <PERSON><PERSON> Ref\\nVersion with rough <PERSON><PERSON> Clem<PERSON> vocals. Referred to as the \\\"worst Ant song ever\\\" by Waterfalls.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d40328e8e722b4688064ff0cddbe2ca\", \"url\": \"https://api.pillowcase.su/api/download/8d40328e8e722b4688064ff0cddbe2ca\", \"size\": \"1.81 MB\", \"duration\": 69.9}", "aliases": [], "size": "1.81 MB"}, {"id": "ac4-32", "name": "AC4 [V2]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> and <PERSON><PERSON>\nHas new, mumble <PERSON><PERSON><PERSON> vocals, with extremely quiet <PERSON><PERSON> vocals at the end of the song.", "length": "65.9", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f5ce9a878d756cd2e5526c597089f9f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f5ce9a878d756cd2e5526c597089f9f5\", \"key\": \"AC4\", \"title\": \"AC4 [V2]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>4 - <PERSON><PERSON> and <PERSON><PERSON>\\nHas new, mumble <PERSON><PERSON> vocals, with extremely quiet <PERSON><PERSON> vocals at the end of the song.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c15d1db49383ab5d05225a3be05bda17\", \"url\": \"https://api.pillowcase.su/api/download/c15d1db49383ab5d05225a3be05bda17\", \"size\": \"1.75 MB\", \"duration\": 65.9}", "aliases": [], "size": "1.75 MB"}, {"id": "ac4-33", "name": "AC4 [V3]", "artists": [], "producers": [], "notes": "OG Filename: AC4 9.18.18 HA crc_ROUGH_1\nSeen on a September 21, 2018 Yandhi tracklist. Has a repeated skit sample that <PERSON><PERSON> interpolates. Has a more finished Ant Clemons verse compared to the other solo version.", "length": "60.47", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/264a4d24e8cae595385086264e813809", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/264a4d24e8cae595385086264e813809\", \"key\": \"AC4\", \"title\": \"AC4 [V3]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: AC4 9.18.18 HA crc_ROUGH_1\\nSeen on a September 21, 2018 Yandhi tracklist. Has a repeated skit sample that <PERSON><PERSON> interpolates. Has a more finished Ant Clemons verse compared to the other solo version.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ba9395fcd9883ee0df1f98bc21d800cb\", \"url\": \"https://api.pillowcase.su/api/download/ba9395fcd9883ee0df1f98bc21d800cb\", \"size\": \"1.66 MB\", \"duration\": 60.47}", "aliases": [], "size": "1.66 MB"}, {"id": "again", "name": "Again", "artists": [], "producers": [], "notes": "OG Filename: Again - <PERSON><PERSON> Ref\n<PERSON>ble demo from 2018. Snippet posted by <PERSON><PERSON><PERSON><PERSON> on TheSource.", "length": "14.83", "fileDate": 15952896, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/36f6440e88a480e8a473e880fddb474e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/36f6440e88a480e8a473e880fddb474e\", \"key\": \"Again\", \"title\": \"Again\", \"description\": \"OG Filename: Again - K<PERSON> Ref\\nMumble demo from 2018. Snippet posted by LilGoblin on TheSource.\", \"date\": 15952896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ffd24314451c8dcd1fd4f1f098acf272\", \"url\": \"https://api.pillowcase.su/api/download/ffd24314451c8dcd1fd4f1f098acf272\", \"size\": \"928 kB\", \"duration\": 14.83}", "aliases": [], "size": "928 kB"}, {"id": "alien", "name": "Alien [V1]", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: RJ - Alien [OG kw freestyle]\nEarliest known version of \"Alien\". <PERSON>ks the \"Godzilla\" Ant <PERSON> intro. Has a RONNY J tag. Can be heard in the bleed of the freestyle vocals. Samples \"Flying Carpet Ride\" by Second Direction.", "length": "196.91", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/147369a3bbdb259b1dd35665264371fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/147369a3bbdb259b1dd35665264371fb\", \"key\": \"Alien\", \"title\": \"Alien [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"description\": \"OG Filename: RJ - Alien [OG kw freestyle]\\nEarliest known version of \\\"Alien\\\". <PERSON><PERSON> the \\\"Godzilla\\\" Ant Clemons intro. Has a RONNY J tag. Can be heard in the bleed of the freestyle vocals. <PERSON><PERSON> \\\"Flying Carpet Ride\\\" by Second Direction.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fbc12aea292721ae994609ff5c4528aa\", \"url\": \"https://api.pillowcase.su/api/download/fbc12aea292721ae994609ff5c4528aa\", \"size\": \"3.84 MB\", \"duration\": 196.91}", "aliases": [], "size": "3.84 MB"}, {"id": "space-x-alien", "name": "Space X / Alien [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J"], "notes": "OG Filename: Space X - Alien [KW Freestyle] 09.28.18\nFirst version of \"Alien\" that has the An<PERSON> \"Godzilla\" intro.", "length": "210.46", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/72115772c7c262b1d143595f4f917273", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/72115772c7c262b1d143595f4f917273\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. RONNY J)\", \"description\": \"OG Filename: Space X - Alien [KW Freestyle] 09.28.18\\nFirst version of \\\"Alien\\\" that has the <PERSON><PERSON> \\\"Godzilla\\\" intro.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6ce03de7866d0bc20aedbe4027b1731c\", \"url\": \"https://api.pillowcase.su/api/download/6ce03de7866d0bc20aedbe4027b1731c\", \"size\": \"4.06 MB\", \"duration\": 210.46}", "aliases": [], "size": "4.06 MB"}, {"id": "space-x-alien-37", "name": "Space X / Alien [V3]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Space X - Alien ANT HOOK REF\nVersion of \"Alien\" with <PERSON><PERSON> providing reference vocals to fill in some of the Kanye freestyle mumble. Bounced from session that did not include an instrumental on it.", "length": "210.4", "fileDate": 16360704, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2e67d4d86c84b06b41b0ed03d1928093", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2e67d4d86c84b06b41b0ed03d1928093\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Space X - Alien ANT HOOK REF\\nVersion of \\\"Alien\\\" with <PERSON><PERSON> providing reference vocals to fill in some of the Kanye freestyle mumble. Bounced from session that did not include an instrumental on it.\", \"date\": 16360704, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d284182a403e5b1a36ea745abc795c97\", \"url\": \"https://api.pillowcase.su/api/download/d284182a403e5b1a36ea745abc795c97\", \"size\": \"4.06 MB\", \"duration\": 210.4}", "aliases": [], "size": "4.06 MB"}, {"id": "space-x-alien-38", "name": "Space X / Alien [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay"], "notes": "OG Filename: Space X - Alien [KW Freestyle Ant Tuned] 09.28.18\nVersion of \"Alien\" containing the <PERSON><PERSON> \"Godzilla\" intro but the same instrumental as OG freesytle version. <PERSON><PERSON>'s \"Godzilla\" intro vocals are pitch corrected like the filename implies.", "length": "210.46", "fileDate": 16360704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f39f91d121a8643f3a50268d76862110", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f39f91d121a8643f3a50268d76862110\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V4]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FNZ & BONGO ByTheWay)\", \"description\": \"OG Filename: Space X - Alien [KW Freestyle Ant Tuned] 09.28.18\\nVersion of \\\"Alien\\\" containing the <PERSON><PERSON> \\\"Godzilla\\\" intro but the same instrumental as OG freesytle version. <PERSON><PERSON>'s \\\"Godzilla\\\" intro vocals are pitch corrected like the filename implies.\", \"date\": 16360704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c82c6a60cee273b34e173253dd498cbc\", \"url\": \"https://api.pillowcase.su/api/download/c82c6a60cee273b34e173253dd498cbc\", \"size\": \"4.06 MB\", \"duration\": 210.46}", "aliases": [], "size": "4.06 MB"}, {"id": "space-x-alien-39", "name": "Space X / Alien [V5]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay"], "notes": "<PERSON><PERSON><PERSON> \"Alien\" reference track. Leaked alongside the \"Alien\" sessions.", "length": "62.91", "fileDate": 16869600, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f36ec8c60ca8f93ddab046de20743acc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f36ec8c60ca8f93ddab046de20743acc\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FNZ & BONGO ByTheWay)\", \"description\": \"CyHi \\\"Alien\\\" reference track. Leaked alongside the \\\"Alien\\\" sessions.\", \"date\": 16869600, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ca4c1cd60d5e979c6bc788b3cc0fb273\", \"url\": \"https://api.pillowcase.su/api/download/ca4c1cd60d5e979c6bc788b3cc0fb273\", \"size\": \"1.7 MB\", \"duration\": 62.91}", "aliases": [], "size": "1.7 MB"}, {"id": "space-x-alien-40", "name": "Space X / Alien [V6]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Space Alien [<PERSON><PERSON><PERSON>] HA\nVersion of \"Alien\" with <PERSON><PERSON><PERSON> reference vocals, filling in some of the Kanye freestyle mumble.", "length": "52.87", "fileDate": 16519680, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/63c15dd7d4f2749ed03c6c551b7046d7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63c15dd7d4f2749ed03c6c551b7046d7\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Space Alien [<PERSON><PERSON><PERSON>] HA\\nVersion of \\\"Alien\\\" with <PERSON><PERSON><PERSON> reference vocals, filling in some of the Kanye freestyle mumble.\", \"date\": 16519680, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a18603c932c0d8c647bad02aa0a58fbf\", \"url\": \"https://api.pillowcase.su/api/download/a18603c932c0d8c647bad02aa0a58fbf\", \"size\": \"1.54 MB\", \"duration\": 52.87}", "aliases": [], "size": "1.54 MB"}, {"id": "space-x-alien-41", "name": "Space X / Alien [V6]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Space Alien [<PERSON><PERSON><PERSON>] HA\nVersion of \"Alien\" with <PERSON><PERSON><PERSON> reference vocals, filling in some of the Kanye freestyle mumble.", "length": "8.86", "fileDate": 16519680, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/463b7119ae646f79a2b68f0828bbc1ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/463b7119ae646f79a2b68f0828bbc1ac\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Space Alien [<PERSON><PERSON><PERSON>] HA\\nVersion of \\\"Alien\\\" with <PERSON><PERSON><PERSON> reference vocals, filling in some of the Kanye freestyle mumble.\", \"date\": 16519680, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9da0b0dfdad89f2d0ca4361e86ecc2bb\", \"url\": \"https://api.pillowcase.su/api/download/9da0b0dfdad89f2d0ca4361e86ecc2bb\", \"size\": \"833 kB\", \"duration\": 8.86}", "aliases": [], "size": "833 kB"}, {"id": "space-x-alien-42", "name": "Space X / Alien [V7]", "artists": ["<PERSON><PERSON>", "070 Shake"], "producers": [], "notes": "OG Filename: Space X - Alien 8.28.18 Shake REF\nVersion of \"Alien\" with 070 Shake vocals. <PERSON>'s intro, three long 070 Shake verses and the second Ka<PERSON><PERSON> verse. VC recording surfaced on dbree in July 2021. CDQ snippet leaked on September 27th, 2022. Leaked in full alongside the \"Alien\" sessions.", "length": "209.81", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/27fb314e2b7b646fa63d36952f5e5e80", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/27fb314e2b7b646fa63d36952f5e5e80\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V7]\", \"artists\": \"(feat. <PERSON><PERSON> & 070 Shake)\", \"description\": \"OG Filename: Space X - Alien 8.28.18 Shake REF\\nVersion of \\\"Alien\\\" with 070 Shake vocals. Has <PERSON><PERSON>'s intro, three long 070 Shake verses and the second Kanye verse. VC recording surfaced on dbree in July 2021. CDQ snippet leaked on September 27th, 2022. Leaked in full alongside the \\\"Alien\\\" sessions.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3a166dc99c67310bebde380e0d91ff7f\", \"url\": \"https://api.pillowcase.su/api/download/3a166dc99c67310bebde380e0d91ff7f\", \"size\": \"4.05 MB\", \"duration\": 209.81}", "aliases": [], "size": "4.05 MB"}, {"id": "alien-43", "name": "Alien [V8]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay", "<PERSON>"], "notes": "OG Filename: ALLIEN AD chop and chords (with ye ref)\nVersion with extra effects on the <PERSON><PERSON> vocals. Has the instrumental used in the Migos version. Filename misspells this version as \"Allien\".", "length": "210.46", "fileDate": 16360704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/67f001e7cfb5ecf7f48aa59a8735e75b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/67f001e7cfb5ecf7f48aa59a8735e75b\", \"key\": \"Alien\", \"title\": \"Alien [V8]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Andrew Dawson)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: ALLIEN AD chop and chords (with ye ref)\\nVersion with extra effects on the <PERSON><PERSON> vocals. Has the instrumental used in the Migos version. Filename misspells this version as \\\"Allien\\\".\", \"date\": 16360704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"edb6b38522d754fd2fa1d22a4e3a6eb8\", \"url\": \"https://api.pillowcase.su/api/download/edb6b38522d754fd2fa1d22a4e3a6eb8\", \"size\": \"4.06 MB\", \"duration\": 210.46}", "aliases": ["Space X"], "size": "4.06 MB"}, {"id": "space-x-alien-44", "name": "Space X / Alien [V9]", "artists": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay"], "notes": "Version of \"Alien\" with a <PERSON><PERSON> verse, is open otherwise. Includes <PERSON><PERSON>'s intro with some added ad-libs from <PERSON><PERSON>, and a line about <PERSON><PERSON><PERSON> putting back on the MAGA hat.", "length": "210.46", "fileDate": 16869600, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7159ff5d42f0ea30376de5aab6f26852", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7159ff5d42f0ea30376de5aab6f26852\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V9]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>a<PERSON>, FNZ & BONGO ByTheWay)\", \"description\": \"Version of \\\"Alien\\\" with a <PERSON><PERSON> verse, is open otherwise. Includes <PERSON><PERSON>'s intro with some added ad-libs from <PERSON><PERSON>, and a line about <PERSON><PERSON><PERSON> putting back on the MAGA hat.\", \"date\": 16869600, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"16acb2c1a182080b2685b969a3b969f5\", \"url\": \"https://api.pillowcase.su/api/download/16acb2c1a182080b2685b969a3b969f5\", \"size\": \"4.06 MB\", \"duration\": 210.46}", "aliases": [], "size": "4.06 MB"}, {"id": "space-x-alien-45", "name": "Space X / Alien [V10]", "artists": ["<PERSON>", "<PERSON><PERSON>"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay", "<PERSON>"], "notes": "OG Filename: Space X - Alien [<PERSON><PERSON>] 09.30.18\nVersion with <PERSON> but no <PERSON><PERSON><PERSON>. Portions of the track are empty, suggesting <PERSON><PERSON><PERSON> could have intended for his vocals to be laid.", "length": "225.22", "fileDate": 15710976, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/946bd86803ec1e50a70e25e05a69f38a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/946bd86803ec1e50a70e25e05a69f38a\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V10]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>G<PERSON> ByTheWay & <PERSON>)\", \"description\": \"OG Filename: Space X - Alien [<PERSON><PERSON> Verse] 09.30.18\\nVersion with <PERSON> but no <PERSON><PERSON><PERSON>. Portions of the track are empty, suggesting <PERSON><PERSON><PERSON> could have intended for his vocals to be laid.\", \"date\": 15710976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c6c5aa49206eb63e6ba8cd2cd65c4436\", \"url\": \"https://api.pillowcase.su/api/download/c6c5aa49206eb63e6ba8cd2cd65c4436\", \"size\": \"4.29 MB\", \"duration\": 225.22}", "aliases": [], "size": "4.29 MB"}, {"id": "space-x-alien-46", "name": "Space X / Alien [V11]", "artists": ["2 Chainz", "<PERSON><PERSON>"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay"], "notes": "Full version of the 2 Chainz \"Alien\" verse, cut-down for later versions. Clocks in at about 1:30. Has a lot of open at the end.", "length": "210.46", "fileDate": 16869600, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/35646560d4352880295848fc5f855c96", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/35646560d4352880295848fc5f855c96\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V11]\", \"artists\": \"(feat. 2 <PERSON>z & Ant Clemons) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON>ogzDa<PERSON>t, FNZ & BONGO ByTheWay)\", \"description\": \"Full version of the 2 Chainz \\\"Alien\\\" verse, cut-down for later versions. Clocks in at about 1:30. Has a lot of open at the end.\", \"date\": 16869600, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"60309cd35216bb9054dfc10461c081c3\", \"url\": \"https://api.pillowcase.su/api/download/60309cd35216bb9054dfc10461c081c3\", \"size\": \"4.06 MB\", \"duration\": 210.46}", "aliases": [], "size": "4.06 MB"}, {"id": "space-x-alien-47", "name": "Space X / Alien [V12]", "artists": ["2 Chainz", "<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON> From Chainz-KY-9-30-18\nAnother earlier version of \"Alien\" with 2 <PERSON><PERSON>' verse.", "length": "132.17", "fileDate": 17111520, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c7375cde2798c77d5d42128c07bcb158", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c7375cde2798c77d5d42128c07bcb158\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V12]\", \"artists\": \"(feat. 2 <PERSON><PERSON> & Ant Clemons)\", \"description\": \"OG Filename: Flames From Chainz-KY-9-30-18\\nAnother earlier version of \\\"Alien\\\" with 2 Chainz' verse.\", \"date\": 17111520, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1eeedaf88266286ad30e95c0861dd7da\", \"url\": \"https://api.pillowcase.su/api/download/1eeedaf88266286ad30e95c0861dd7da\", \"size\": \"2.81 MB\", \"duration\": 132.17}", "aliases": [], "size": "2.81 MB"}, {"id": "space-x-alien-48", "name": "Space X / Alien [V13]", "artists": ["2 Chainz", "<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Flames From Chains Edit 2-KY-9-30-18\nVersion of \"Alien\" with 2 <PERSON><PERSON>' verse. Seems to be cut-down and properly arranged, with extra effects. Original snippet leaked December 1st, 2019.", "length": "132.41", "fileDate": 17111520, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1ae77f0cd3c548ee6c67c32b8e4803fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ae77f0cd3c548ee6c67c32b8e4803fd\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V13]\", \"artists\": \"(feat. 2 <PERSON><PERSON> & Ant Clemons)\", \"description\": \"OG Filename: Flames From Chains Edit 2-KY-9-30-18\\nVersion of \\\"Alien\\\" with 2 <PERSON><PERSON>' verse. Seems to be cut-down and properly arranged, with extra effects. Original snippet leaked December 1st, 2019.\", \"date\": 17111520, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5dc2e8c682021ac36e5a0cbc326e270e\", \"url\": \"https://api.pillowcase.su/api/download/5dc2e8c682021ac36e5a0cbc326e270e\", \"size\": \"2.81 MB\", \"duration\": 132.41}", "aliases": [], "size": "2.81 MB"}, {"id": "space-x-alien-49", "name": "Space X / Alien [V14]", "artists": ["<PERSON><PERSON>", "Quavo", "Offset"], "producers": ["RONNY J", "BoogzDaBeast", "FNZ", "BONGO ByTheWay", "<PERSON>"], "notes": "Has a slightly different beat and more evolved \"Godzilla\" intro compared to the other \"Alien\" versions. Recorded in the first half of October 2018.", "length": "226.13", "fileDate": 15762816, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/aedcb83ee6163ebcd24663527de9f3d3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aedcb83ee6163ebcd24663527de9f3d3\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V14]\", \"artists\": \"(feat. <PERSON><PERSON>, Quavo & Offset) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>Z, B<PERSON>G<PERSON> ByTheWay & Andrew <PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"Has a slightly different beat and more evolved \\\"Godzilla\\\" intro compared to the other \\\"Alien\\\" versions. Recorded in the first half of October 2018.\", \"date\": 15762816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2b44636083282322e5498d7307c01839\", \"url\": \"https://api.pillowcase.su/api/download/2b44636083282322e5498d7307c01839\", \"size\": \"4.31 MB\", \"duration\": 226.13}", "aliases": ["Space X"], "size": "4.31 MB"}, {"id": "all-blue-don-c-s", "name": "All Blue Don C's [V1]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: ALL BLUE DON Cs Bump X Ye Ref 4 RicoMix\nBump J reference track. Snippet leaked due to a Discord VC recording.", "length": "87.46", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/4e477e91515c6b11b30e78b818e05bbc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e477e91515c6b11b30e78b818e05bbc\", \"key\": \"All Blue Don C's\", \"title\": \"All Blue Don C's [V1]\", \"artists\": \"(ref. <PERSON>ump J) (prod. Jeff & BoogzDaBeast)\", \"aliases\": [\"All New Don C's\", \"Strapped Steph Curry\"], \"description\": \"OG Filename: ALL BLUE DON Cs Bump X Ye Ref 4 RicoMix\\nBump J reference track. Snippet leaked due to a Discord VC recording.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8b1a04279fb95cd7a52542e6f0b32723\", \"url\": \"https://api.pillowcase.su/api/download/8b1a04279fb95cd7a52542e6f0b32723\", \"size\": \"2.09 MB\", \"duration\": 87.46}", "aliases": ["All New Don C's", "Strapped <PERSON><PERSON>"], "size": "2.09 MB"}, {"id": "all-blue-don-c-s-51", "name": "All Blue Don C's [V3]", "artists": ["<PERSON> Thug"], "producers": ["Beat By Jeff", "BoogzDaBeast"], "notes": "OG Filename: All Blue Don C's - BUMP AND THUG 09.22.18\nVersion with both <PERSON><PERSON>'s verses, and <PERSON> Thug vocals. <PERSON><PERSON> put this song up for sale as \"Strapped Steph Curry\" on December 14th, 2019, but later said that they made the name up because they didn't have an actual name. Later leaked for free.", "length": "158.28", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/48261e3b313d4434804f28184942402e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48261e3b313d4434804f28184942402e\", \"key\": \"All Blue Don C's\", \"title\": \"All Blue Don C's [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON>hu<PERSON>) (prod. Beat By Jeff & BoogzDaBeast)\", \"aliases\": [\"All New Don C's\", \"Strapped Steph Curry\"], \"description\": \"OG Filename: All Blue Don C's - BUMP AND THUG 09.22.18\\nVersion with both <PERSON><PERSON>'s verses, and <PERSON> Thug vocals. <PERSON>ek put this song up for sale as \\\"Strapped Steph Curry\\\" on December 14th, 2019, but later said that they made the name up because they didn't have an actual name. Later leaked for free.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"39a58d7c41f3b609101c0ea43f03dfe9\", \"url\": \"https://api.pillowcase.su/api/download/39a58d7c41f3b609101c0ea43f03dfe9\", \"size\": \"3.22 MB\", \"duration\": 158.28}", "aliases": ["All New Don C's", "Strapped <PERSON><PERSON>"], "size": "3.22 MB"}, {"id": "bitches-do-voodoo", "name": "Bitches Do Voodoo", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Bitches Do Voodoo - KW Ref\nMumble demo from 2018. Samples \"Bitches Do Voodoo\" by The Midnight Hour. Tagged snippets originally leaked June 27th, 2024.", "length": "79.49", "fileDate": 17255808, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/eba3594d2f9ef14ce8015614159d72bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eba3594d2f9ef14ce8015614159d72bc\", \"key\": \"Bitches Do Voodoo\", \"title\": \"Bitches Do Voodoo\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Hear Me\"], \"description\": \"OG Filename: Bitches Do Voodoo - KW Ref\\nMumble demo from 2018. Samples \\\"Bitches Do Voodoo\\\" by The Midnight Hour. Tagged snippets originally leaked June 27th, 2024.\", \"date\": 17255808, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e0d87889040ea7f8d2131f9b6a299f34\", \"url\": \"https://api.pillowcase.su/api/download/e0d87889040ea7f8d2131f9b6a299f34\", \"size\": \"1.96 MB\", \"duration\": 79.49}", "aliases": ["Hear Me"], "size": "1.96 MB"}, {"id": "brothers-53", "name": "Brothers [V12]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON> <PERSON> <PERSON><PERSON> and Ant version 09.19.18\nAlternate version of \"Brothers\" with the Ant Clemons feature, but no Ye freestyle vocals. Has a similar instrumental as \"Violent Crimes\".", "length": "152.69", "fileDate": 15825888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1a202ebb5b5a89eb73ea35f473016157", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1a202ebb5b5a89eb73ea35f473016157\", \"key\": \"Brothers\", \"title\": \"Brothers [V12]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Brothers - KW and Ant version 09.19.18\\nAlternate version of \\\"Brothers\\\" with the Ant Clemons feature, but no Ye freestyle vocals. Has a similar instrumental as \\\"Violent Crimes\\\".\", \"date\": 15825888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"119e816103043162eb43f29d3d94c70d\", \"url\": \"https://api.pillowcase.su/api/download/119e816103043162eb43f29d3d94c70d\", \"size\": \"3.13 MB\", \"duration\": 152.69}", "aliases": [], "size": "3.13 MB"}, {"id": "brothers-54", "name": "Brothers [V13]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Version of \"Brothers\" with alternate production and vocals from <PERSON><PERSON> that sounds like a feature. Snippet leaked August 12th, 2024.", "length": "9.64", "fileDate": 17234208, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/eea6306c8a14a3ca42bb32b00c9d7b30", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eea6306c8a14a3ca42bb32b00c9d7b30\", \"key\": \"Brothers\", \"title\": \"Brothers [V13]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"Version of \\\"Brothers\\\" with alternate production and vocals from <PERSON><PERSON> that sounds like a feature. Snippet leaked August 12th, 2024.\", \"date\": 17234208, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ade1086a7e0d490939248bac8cae3b32\", \"url\": \"https://api.pillowcase.su/api/download/ade1086a7e0d490939248bac8cae3b32\", \"size\": \"846 kB\", \"duration\": 9.64}", "aliases": [], "size": "846 kB"}, {"id": "bh2", "name": "BH2 [V1]", "artists": [], "producers": [], "notes": "OG Filename: BH2 [kw freestyle]\nRough Ye freestyle. Most likely made in around mid-September 2018.", "length": "64.21", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b01090d966203fd3d9ca0a2519869771", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b01090d966203fd3d9ca0a2519869771\", \"key\": \"BH2\", \"title\": \"BH2 [V1]\", \"description\": \"OG Filename: BH2 [kw freestyle]\\nRough Ye freestyle. Most likely made in around mid-September 2018.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1d99bdcdad44b2f6c7f6cfeb5510b601\", \"url\": \"https://api.pillowcase.su/api/download/1d99bdcdad44b2f6c7f6cfeb5510b601\", \"size\": \"1.72 MB\", \"duration\": 64.21}", "aliases": [], "size": "1.72 MB"}, {"id": "bottom-line", "name": "Bottom Line", "artists": [], "producers": [], "notes": "OG Filename: Bottom Line - <PERSON><PERSON> Ref\nMumble demo from 2018. Samples \"Bottom Line\" by Electric Wire Hustle.", "length": "15.94", "fileDate": 17210016, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://krakenfiles.com/view/yKwBFLnfYl/file.html", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://krakenfiles.com/view/yKwBFLnfYl/file.html\", \"key\": \"Bottom Line\", \"title\": \"Bottom Line\", \"description\": \"OG Filename: Bottom Line - KW Ref\\nMumble demo from 2018. Samples \\\"Bottom Line\\\" by Electric Wire Hustle.\", \"date\": 17210016, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2adc660b48e922ad4be72c98ab2eb441\", \"url\": \"https://api.pillowcase.su/api/download/2adc660b48e922ad4be72c98ab2eb441\", \"size\": \"946 kB\", \"duration\": 15.94}", "aliases": [], "size": "946 kB"}, {"id": "bye-bye-baby", "name": "Bye Bye Baby [V1] ", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J", "FNZ"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> <PERSON> [OG kw and <PERSON><PERSON> freestyle]\nOriginal freestyle with <PERSON><PERSON>. <PERSON><PERSON> mostly does ad-libs. Has many similar <PERSON><PERSON><PERSON> vocals to the leaked version, but there are some small changes and couple lines that are unheard. Snippets originally leaked November 19th, 2020 and October 14th, 2024.", "length": "134.49", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/228a9ef4d36ae22682f32125e3778141", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/228a9ef4d36ae22682f32125e3778141\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V1] \", \"artists\": \"(feat. <PERSON><PERSON>) (prod. RONNY J & FNZ)\", \"description\": \"OG Filename: RJ - <PERSON> Bye <PERSON> [OG kw and Ant freestyle]\\nOriginal freestyle with Ant Clemons. <PERSON>t mostly does ad-libs. Has many similar Kanye vocals to the leaked version, but there are some small changes and couple lines that are unheard. Snippets originally leaked November 19th, 2020 and October 14th, 2024.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"365b20ecdd2643b84cf846e00cc176f0\", \"url\": \"https://api.pillowcase.su/api/download/365b20ecdd2643b84cf846e00cc176f0\", \"size\": \"2.85 MB\", \"duration\": 134.49}", "aliases": [], "size": "2.85 MB"}, {"id": "bye-bye-baby-58", "name": "Bye Bye Baby [V2] ", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: BYE BYE BABY [KW Freestyle] 09.27.18\nHas no <PERSON><PERSON> vocals at all.", "length": "134.35", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/348303a1a63b9aa0a0fd89fe32489f77", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/348303a1a63b9aa0a0fd89fe32489f77\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V2] \", \"artists\": \"(prod. RONNY J)\", \"description\": \"OG Filename: BYE BYE BABY [KW Freestyle] 09.27.18\\nHas no <PERSON><PERSON> vocals at all.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0ef9d7244c4c693655c634216c69e391\", \"url\": \"https://api.pillowcase.su/api/download/0ef9d7244c4c693655c634216c69e391\", \"size\": \"2.84 MB\", \"duration\": 134.35}", "aliases": [], "size": "2.84 MB"}, {"id": "bye-bye-baby-59", "name": "Bye Bye Baby [V4] ", "artists": [], "producers": ["RONNY J", "FNZ"], "notes": "OG Filename: BYE BYE BABY [<PERSON><PERSON>] 09.27.18\nHas <PERSON><PERSON> vocals at the beginning, and then open. <PERSON><PERSON> rapped over this version for his reference track.", "length": "134.35", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/947f18ada769aee6cbe6b694469f37e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/947f18ada769aee6cbe6b694469f37e4\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V4] \", \"artists\": \"(prod. RONNY J & FNZ)\", \"description\": \"OG Filename: BYE BYE BABY [<PERSON><PERSON>] 09.27.18\\nHas <PERSON><PERSON> vocals at the beginning, and then open. <PERSON><PERSON> rapped over this version for his reference track.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ef6a96c8e438fd751c2bacfc1c56ade6\", \"url\": \"https://api.pillowcase.su/api/download/ef6a96c8e438fd751c2bacfc1c56ade6\", \"size\": \"2.84 MB\", \"duration\": 134.35}", "aliases": [], "size": "2.84 MB"}, {"id": "bye-bye-baby-60", "name": "Bye Bye Baby [V5]", "artists": [], "producers": ["RONNY J", "FNZ"], "notes": "OG Filename: BYE BYE BABY [Mykki Intro Loops Ref]\n<PERSON>k<PERSON> reference track. Has the spoken word intro from versions of \"Alien\".", "length": "69.38", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2454508916d4d846ac91f76389f6c0a8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2454508916d4d846ac91f76389f6c0a8\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. RONNY J & FNZ)\", \"description\": \"OG Filename: BYE BYE BABY [<PERSON><PERSON><PERSON> Intro Loops Ref]\\nMykki Blanoc reference track. Has the spoken word intro from versions of \\\"Alien\\\".\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"46f2b8a698705abc94e98953dad95948\", \"url\": \"https://api.pillowcase.su/api/download/46f2b8a698705abc94e98953dad95948\", \"size\": \"1.8 MB\", \"duration\": 69.38}", "aliases": [], "size": "1.8 MB"}, {"id": "bye-bye-baby-61", "name": "Bye Bye Baby [V6]", "artists": [], "producers": ["RONNY J", "FNZ"], "notes": "OG Filename: Bye Bye <PERSON>_<PERSON>no ref 1\nNino Blu reference track, recorded in The Mercer.", "length": "56.56", "fileDate": 16476480, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f637487d2a78804097b3812fff1bcd23", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f637487d2a78804097b3812fff1bcd23\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. RONNY J & FNZ)\", \"description\": \"OG Filename: Bye Bye Baby_Nino ref 1\\nNino Blu reference track, recorded in The Mercer.\", \"date\": 16476480, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7b534230ed6effc1e7117edc22771509\", \"url\": \"https://api.pillowcase.su/api/download/7b534230ed6effc1e7117edc22771509\", \"size\": \"1.6 MB\", \"duration\": 56.56}", "aliases": [], "size": "1.6 MB"}, {"id": "bye-bye-baby-62", "name": "Bye Bye Baby [V6]", "artists": [], "producers": ["RONNY J", "FNZ"], "notes": "OG Filename: Bye Bye <PERSON>_<PERSON>no ref 1\nNino Blu reference track, recorded in The Mercer.", "length": "21.79", "fileDate": 16476480, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c5a743cfbb40df7fd406708625ea099a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c5a743cfbb40df7fd406708625ea099a\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. RONNY J & FNZ)\", \"description\": \"OG Filename: Bye Bye Baby_Nino ref 1\\nNino Blu reference track, recorded in The Mercer.\", \"date\": 16476480, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"64f5777028c3e19407616ff1268bbaf5\", \"url\": \"https://api.pillowcase.su/api/download/64f5777028c3e19407616ff1268bbaf5\", \"size\": \"1.04 MB\", \"duration\": 21.79}", "aliases": [], "size": "1.04 MB"}, {"id": "dip", "name": "Dip", "artists": [], "producers": ["BoogzDaBeast", "d.a. got that dope"], "notes": "OG Filename: tyga boogz ac ref 09-16-18\nOriginal version of \"Dip\" by <PERSON><PERSON>, supposedly a reference track according to <PERSON><PERSON>. Unknown if <PERSON><PERSON><PERSON> ever did anything with the song, but either it was shown to him or intended for <PERSON><PERSON> to work on it judging from the \"AC\" present within the filename. The released version of the song has a Nicki Minaj feature, one that could've been recorded at the same time she recorded her \"New Body\" verse.", "length": "128.44", "fileDate": 16913664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a3ccad15c17a156dd647a6280b5fe580", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a3ccad15c17a156dd647a6280b5fe580\", \"key\": \"Dip\", \"title\": \"Dip\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast & d.a. got that dope)\", \"description\": \"OG Filename: tyga boogz ac ref 09-16-18\\nOriginal version of \\\"Dip\\\" by <PERSON><PERSON>, supposedly a reference track according to <PERSON><PERSON>. Unknown if <PERSON><PERSON><PERSON> ever did anything with the song, but either it was shown to him or intended for <PERSON><PERSON> to work on it judging from the \\\"AC\\\" present within the filename. The released version of the song has a Nicki <PERSON>j feature, one that could've been recorded at the same time she recorded her \\\"New Body\\\" verse.\", \"date\": 16913664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eb48aad35d0545e42b4665d9eaf0e328\", \"url\": \"https://api.pillowcase.su/api/download/eb48aad35d0545e42b4665d9eaf0e328\", \"size\": \"2.75 MB\", \"duration\": 128.44}", "aliases": [], "size": "2.75 MB"}, {"id": "drb-adb", "name": "DRB ADB", "artists": [], "producers": [], "notes": "OG Filename: DRB ADB - KW Ref\nVery rough and short freestyle likely made during the early Yandhi sessions, however it's unknown when it was actually made.", "length": "22.7", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f167a2de1d12ef89ea89e540024919ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f167a2de1d12ef89ea89e540024919ff\", \"key\": \"DRB ADB\", \"title\": \"DRB ADB\", \"description\": \"OG Filename: DRB ADB - KW Ref\\nVery rough and short freestyle likely made during the early Yandhi sessions, however it's unknown when it was actually made.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d72829872946cac1a7791e953626a058\", \"url\": \"https://api.pillowcase.su/api/download/d72829872946cac1a7791e953626a058\", \"size\": \"1.05 MB\", \"duration\": 22.7}", "aliases": [], "size": "1.05 MB"}, {"id": "ever-bryan", "name": "✨ <PERSON> Bryan", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J", "FNZ"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> [kw and <PERSON><PERSON> freestyle]\nYe freestyle over a RONNY J instrumental, with <PERSON><PERSON> adlibs. Deals with such topics such as the death of XXXTENTACI<PERSON>, trans rights, and getting molested by your uncle but still being a \"young goat\". Likely from the same sessions as \"Alien\" & \"Bye Bye Baby\".", "length": "172.39", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/0a59726182de58a39a99a34b553a011f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a59726182de58a39a99a34b553a011f\", \"key\": \"<PERSON> <PERSON>\", \"title\": \"\\u2728 <PERSON>\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. RONNY J & FNZ)\", \"description\": \"OG Filename: <PERSON><PERSON> <PERSON> <PERSON> [kw and <PERSON><PERSON> freestyle]\\nYe freestyle over a RONNY J instrumental, with <PERSON><PERSON> adlibs. Deals with such topics such as the death of XXXTEN<PERSON><PERSON><PERSON>, trans rights, and getting molested by your uncle but still being a \\\"young goat\\\". Likely from the same sessions as \\\"Alien\\\" & \\\"Bye Bye Baby\\\".\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e16a549478b9b34741d6616f58f35031\", \"url\": \"https://api.pillowcase.su/api/download/e16a549478b9b34741d6616f58f35031\", \"size\": \"3.45 MB\", \"duration\": 172.39}", "aliases": [], "size": "3.45 MB"}, {"id": "god-is-love", "name": "God Is Love", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> Is Love - <PERSON><PERSON> Ref\nFreestyle done over a chop of \"God Is Love (Single Version)\" by <PERSON>, from early September 2018 Yandhi sessions.", "length": "69.55", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c9ce6c8d232c2d1815952eb4e3609562", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9ce6c8d232c2d1815952eb4e3609562\", \"key\": \"God Is Love\", \"title\": \"God Is Love\", \"description\": \"OG Filename: <PERSON> Is Love - KW Ref\\nFreestyle done over a chop of \\\"God Is Love (Single Version)\\\" by <PERSON>, from early September 2018 Yandhi sessions.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"07d5786057db0e8b4163e703d37f57d6\", \"url\": \"https://api.pillowcase.su/api/download/07d5786057db0e8b4163e703d37f57d6\", \"size\": \"1.8 MB\", \"duration\": 69.55}", "aliases": [], "size": "1.8 MB"}, {"id": "godzilla", "name": "Godzilla [V1]", "artists": [], "producers": ["BONGO ByTheWay"], "notes": "OG Filename: 2252 GODZILLA ROUGH \nVersion featuring two full reference verses from <PERSON><PERSON>. Has instrumental differences and has a rougher sounding version of the hook. Samples \"HTF Brass 130 Dangerous Stab 01 E\" from the Splice sound bank.", "length": "158.93", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/441aac5379c322df674d37be0dfe2ac1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/441aac5379c322df674d37be0dfe2ac1\", \"key\": \"Godzilla\", \"title\": \"Godzilla [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"description\": \"OG Filename: 2252 GODZILLA ROUGH \\nVersion featuring two full reference verses from <PERSON><PERSON> Clemons. Has instrumental differences and has a rougher sounding version of the hook. <PERSON><PERSON> \\\"HTF Brass 130 Dangerous Stab 01 E\\\" from the Splice sound bank.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"06ffd2cb462da2c6b48236f1c134af1b\", \"url\": \"https://api.pillowcase.su/api/download/06ffd2cb462da2c6b48236f1c134af1b\", \"size\": \"3.23 MB\", \"duration\": 158.93}", "aliases": [], "size": "3.23 MB"}, {"id": "godzilla-68", "name": "Godzilla [V2]", "artists": [], "producers": ["BONGO ByTheWay"], "notes": "Same as the older solo <PERSON><PERSON> version, but with extra production. Original \"full\" version of Godzilla that leaked was an edit combining this version with the version with less production.", "length": "155.45", "fileDate": ********, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8c9b3b71d44645b4612f280a8f550ac8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8c9b3b71d44645b4612f280a8f550ac8\", \"key\": \"Godzilla\", \"title\": \"Godzilla [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"description\": \"Same as the older solo Ant version, but with extra production. Original \\\"full\\\" version of Godzilla that leaked was an edit combining this version with the version with less production.\", \"date\": ********, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a084b63512c8fae4d7c0bd4d79c32d4a\", \"url\": \"https://api.pillowcase.su/api/download/a084b63512c8fae4d7c0bd4d79c32d4a\", \"size\": \"3.18 MB\", \"duration\": 155.45}", "aliases": [], "size": "3.18 MB"}, {"id": "godzilla-69", "name": "Godzilla [V3]", "artists": ["<PERSON><PERSON>", "<PERSON> Thug"], "producers": ["BONGO ByTheWay"], "notes": "Version of \"Godzilla\" with a Young Thug verse and an open verse. Appears on a very early tracklist. Was later combined with \"Alien\" and appears as an intro to most versions, without the Thug verse.", "length": "152.66", "fileDate": 15698880, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/da65f42f3d988c13ff51ea303eb5c41b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/da65f42f3d988c13ff51ea303eb5c41b\", \"key\": \"Godzilla\", \"title\": \"Godzilla [V3]\", \"artists\": \"(feat. <PERSON><PERSON> Clemons & Young Thug) (prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"description\": \"Version of \\\"Godzilla\\\" with a Young Thug verse and an open verse. Appears on a very early tracklist. Was later combined with \\\"Alien\\\" and appears as an intro to most versions, without the Thug verse.\", \"date\": 15698880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1589797a4362a1ac53a7361f1fb89bf0\", \"url\": \"https://api.pillowcase.su/api/download/1589797a4362a1ac53a7361f1fb89bf0\", \"size\": \"3.13 MB\", \"duration\": 152.66}", "aliases": [], "size": "3.13 MB"}, {"id": "goodbye-piano", "name": "Goodbye Piano", "artists": [], "producers": [], "notes": "OG Filename: Goodbye Piano - <PERSON><PERSON> REF\nFreestyle done over a piano based beat, from early to mid-September 2018 Yandhi sessions.", "length": "73.42", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/dbb7a6ce051c8b257ce7cae309f669fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dbb7a6ce051c8b257ce7cae309f669fa\", \"key\": \"Goodbye Piano\", \"title\": \"Goodbye Piano\", \"description\": \"OG Filename: Goodbye Piano - KW REF\\nFreestyle done over a piano based beat, from early to mid-September 2018 Yandhi sessions.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f06111c58f6987c45e68cdbaa8cb2923\", \"url\": \"https://api.pillowcase.su/api/download/f06111c58f6987c45e68cdbaa8cb2923\", \"size\": \"1.87 MB\", \"duration\": 73.42}", "aliases": [], "size": "1.87 MB"}, {"id": "don-t-let-me-down", "name": "Don't Let Me Down [V2]", "artists": [], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "Version used in a video promoting the release of <PERSON><PERSON> posted by <PERSON><PERSON><PERSON> on his social media. The version in the video snippet is slightly sped up. The beat from \"Hurricane\" was originally for Good Ass Job, but when <PERSON> didn't want it, <PERSON><PERSON><PERSON> sang over it as the first track for his new album that would become <PERSON><PERSON>.", "length": "50.02", "fileDate": 15368832, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f04d082464543b9580aab6e638fbe724", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f04d082464543b9580aab6e638fbe724\", \"key\": \"Don't Let Me Down\", \"title\": \"Don't Let Me Down [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Hurricane\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Version used in a video promoting the release of <PERSON><PERSON> posted by <PERSON><PERSON><PERSON> on his social media. The version in the video snippet is slightly sped up. The beat from \\\"Hurricane\\\" was originally for Good Ass Job, but when <PERSON> didn't want it, <PERSON><PERSON><PERSON> sang over it as the first track for his new album that would become <PERSON><PERSON>.\", \"date\": 15368832, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ab729631a09c394d1cf6021a6559953d\", \"url\": \"https://api.pillowcase.su/api/download/ab729631a09c394d1cf6021a6559953d\", \"size\": \"1.49 MB\", \"duration\": 50.02}", "aliases": ["Hurricane", "Hurricanes", "80 Degrees"], "size": "1.49 MB"}, {"id": "don-t-let-me-down-72", "name": "Don't Let Me Down [V3]", "artists": [], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "Freestyle version of \"Hurricane\", from before the \"Wave Runner\" verse was recorded. From the 500 Days In UCLA documentary. Leaked as a bonus for the \"Welcome To My Life\" groupbuy.", "length": "101.47", "fileDate": 16729632, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1969ff00c5354539cb15d6b7ab9fcb50", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1969ff00c5354539cb15d6b7ab9fcb50\", \"key\": \"Don't Let Me Down\", \"title\": \"Don't Let Me Down [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeast, Nascent & RONNY J)\", \"aliases\": [\"Hurricane\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Freestyle version of \\\"Hurricane\\\", from before the \\\"Wave Runner\\\" verse was recorded. From the 500 Days In UCLA documentary. Leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy.\", \"date\": 16729632, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"f452cf09916e0022840378e389d20deb\", \"url\": \"https://api.pillowcase.su/api/download/f452cf09916e0022840378e389d20deb\", \"size\": \"2.31 MB\", \"duration\": 101.47}", "aliases": ["Hurricane", "Hurricanes", "80 Degrees"], "size": "2.31 MB"}, {"id": "don-t-let-me-down-73", "name": "Don't Let Me Down [V4] ", "artists": ["G Herbo"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "Version with a G Herbo feature. Snippet posted to <PERSON><PERSON><PERSON>'s Instagram in the studio with him. Confirmed by the \"Hurricane\" Pro Tools Session.", "length": "5.15", "fileDate": 15369696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c8bbea61c8279ac4c6d88d2bc7a38435", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8bbea61c8279ac4c6d88d2bc7a38435\", \"key\": \"Don't Let Me Down\", \"title\": \"Don't Let Me Down [V4] \", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Hurricane\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Version with a G Herbo feature. Snippet posted to <PERSON><PERSON><PERSON>'s Instagram in the studio with him. Confirmed by the \\\"Hurricane\\\" Pro Tools Session.\", \"date\": 15369696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3a19257369d2d8a68a59ae26302843e1\", \"url\": \"https://api.pillowcase.su/api/download/3a19257369d2d8a68a59ae26302843e1\", \"size\": \"773 kB\", \"duration\": 5.15}", "aliases": ["Hurricane", "Hurricanes", "80 Degrees"], "size": "773 kB"}, {"id": "hurricane", "name": "Hurricane [V5]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "MIKE DEAN", "Nascent", "RONNY J"], "notes": "Rough version of \"Hurricane\" with the original <PERSON><PERSON><PERSON> hook and rough <PERSON><PERSON> vocals. Possibly the version the original Twitter snippet was pulled from.", "length": "12.83", "fileDate": 16231104, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/cf351329f6854c058d8aa32a156ea383", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf351329f6854c058d8aa32a156ea383\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V5]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Rough version of \\\"Hurricane\\\" with the original <PERSON><PERSON><PERSON> hook and rough <PERSON><PERSON> vocals. Possibly the version the original Twitter snippet was pulled from.\", \"date\": 16231104, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1e6b1e453477a11f9028fc2f77630057\", \"url\": \"https://api.pillowcase.su/api/download/1e6b1e453477a11f9028fc2f77630057\", \"size\": \"897 kB\", \"duration\": 12.83}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "897 kB"}, {"id": "don-t-let-me-down-75", "name": "Don't Let Me Down [V6]", "artists": [], "producers": ["BoogzDaBeast", "MIKE DEAN", "Nascent", "RONNY J"], "notes": "OG Filename: Don't Let Me Done - MD version [ADD ANT VOCAL]\nVersion of \"Hurricane\" with a rough <PERSON><PERSON> hook as well as <PERSON><PERSON> punch-in vocals on an early version of the \"Wave Runner\" verse.", "length": "261.76", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/cbacf270f9bae0814dd838d7bb4ab9d6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cbacf270f9bae0814dd838d7bb4ab9d6\", \"key\": \"Don't Let Me Down\", \"title\": \"Don't Let Me Down [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, MIKE DEAN, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Done\", \"Hurricane\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Don't Let Me Done - MD version [ADD ANT VOCAL]\\nVersion of \\\"Hurricane\\\" with a rough <PERSON><PERSON> hook as well as <PERSON><PERSON> punch-in vocals on an early version of the \\\"Wave Runner\\\" verse.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d36cc1dacb03237ec31bf5bc37c7974d\", \"url\": \"https://api.pillowcase.su/api/download/d36cc1dacb03237ec31bf5bc37c7974d\", \"size\": \"4.88 MB\", \"duration\": 261.76}", "aliases": ["Don't Let Me Done", "Hurricane", "Hurricanes", "80 Degrees"], "size": "4.88 MB"}, {"id": "hurricane-76", "name": "Hurricane [V7]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "Version of \"Hurricane\" similar to the previous version but with slight alternate production.", "length": "11.63", "fileDate": 16650144, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f19cc6a24cd1113bbee1ba9b7fcc27f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f19cc6a24cd1113bbee1ba9b7fcc27f1\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Version of \\\"Hurricane\\\" similar to the previous version but with slight alternate production.\", \"date\": 16650144, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b19ba37cf717b369e2b8063847e9cbaa\", \"url\": \"https://api.pillowcase.su/api/download/b19ba37cf717b369e2b8063847e9cbaa\", \"size\": \"878 kB\", \"duration\": 11.63}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "878 kB"}, {"id": "hurricane-77", "name": "Hurricane [V10] ", "artists": ["<PERSON><PERSON>", "<PERSON> Thug", "Ty Dolla $ign"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "Version of 'Hurricane\" with verses from <PERSON>ign and <PERSON> Thug. Stems for this version leaked in November 2021.", "length": "261.76", "fileDate": 15762816, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/bbee401f985cc1af95dd6d0e62fba279", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbee401f985cc1af95dd6d0e62fba279\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V10] \", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON>hug & Ty <PERSON> $ign) (prod. BoogzDaBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Version of 'Hurricane\\\" with verses from <PERSON> $ign and <PERSON>hug. Stems for this version leaked in November 2021.\", \"date\": 15762816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d87defe76f80c2e89239730c2e05082e\", \"url\": \"https://api.pillowcase.su/api/download/d87defe76f80c2e89239730c2e05082e\", \"size\": \"4.88 MB\", \"duration\": 261.76}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.88 MB"}, {"id": "hurricane-78", "name": "Hurricane [V11] ", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane 09.23.18 [New KW Vrs]\nEarly <PERSON><PERSON> hook with one pretty much finished <PERSON><PERSON><PERSON> verse and an open verse.", "length": "262.01", "fileDate": 15695424, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8a18bc8d3019edf012f40b2e1dd75664", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8a18bc8d3019edf012f40b2e1dd75664\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V11] \", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane 09.23.18 [New KW Vrs]\\nEarly <PERSON> hook with one pretty much finished Kanye verse and an open verse.\", \"date\": 15695424, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6ee5fe3df7f59a610c0802026192d06\", \"url\": \"https://api.pillowcase.su/api/download/d6ee5fe3df7f59a610c0802026192d06\", \"size\": \"4.88 MB\", \"duration\": 262.01}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.88 MB"}, {"id": "hurricane-79", "name": "Hurricane [V12] ", "artists": ["<PERSON><PERSON>", "<PERSON> Thug"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane 09.23.18 [New KW Vrs 1 Thug Vrs 2]\nContains the same <PERSON><PERSON> Clem<PERSON> hook as V9 without <PERSON><PERSON><PERSON>'s hook layered under it. <PERSON><PERSON><PERSON>'s \"wave runners\" verse replaces <PERSON>gn's \"grandma couch sex\" verse (thank God).", "length": "258.73", "fileDate": 16962048, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/778388c54de7292733c2768b76f03ec1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/778388c54de7292733c2768b76f03ec1\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V12] \", \"artists\": \"(feat. <PERSON><PERSON>lemons & Young Thug) (prod. <PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane 09.23.18 [New KW Vrs 1 Thug Vrs 2]\\nContains the same <PERSON><PERSON> Clemons hook as V9 without <PERSON><PERSON><PERSON>'s hook layered under it. <PERSON><PERSON><PERSON>'s \\\"wave runners\\\" verse replaces <PERSON>a $ign's \\\"grandma couch sex\\\" verse (thank God).\", \"date\": 16962048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a713dcdfc11e837cd15d5b0935acdeee\", \"url\": \"https://api.pillowcase.su/api/download/a713dcdfc11e837cd15d5b0935acdeee\", \"size\": \"4.83 MB\", \"duration\": 258.73}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.83 MB"}, {"id": "hurricane-80", "name": "Hurricane [V15]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane 9.27.18 [<PERSON><PERSON><PERSON>] HA 2310_1\n<PERSON><PERSON><PERSON> reference for \"Hurricane\".", "length": "261.78", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e6963e067dc69c76b4f637cd62b9f688", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e6963e067dc69c76b4f637cd62b9f688\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V15]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON>z<PERSON>aBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane 9.27.18 [<PERSON><PERSON><PERSON>] HA 2310_1\\nMyk<PERSON> reference for \\\"Hurricane\\\".\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c4428a9ba30c774b148752c4dc19da86\", \"url\": \"https://api.pillowcase.su/api/download/c4428a9ba30c774b148752c4dc19da86\", \"size\": \"4.88 MB\", \"duration\": 261.78}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.88 MB"}, {"id": "hurricane-81", "name": "Hurricane [V16]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane 8.28.18 Shake Vocals\nFiledate says it was recorded 8.28.18, however this doesn't really make sense with \"Hurricane's\" evolution, so it's safe to assume this was a misspelling of 9.28.18. Snippet of <PERSON>'s vocal take leaked February 22nd, 2025. Longer snippet of the full track leaked February 23rd, 2025, showing it includes <PERSON>'s infamous \"grandma couch\" verse.", "length": "261.98", "fileDate": 17406144, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b03a4f73716777597525d6d45ab4b1c4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b03a4f73716777597525d6d45ab4b1c4\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V16]\", \"artists\": \"(ref. 070 Shake) (feat. <PERSON><PERSON> & Ty Dolla $ign) (prod. Boogz<PERSON>aBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane 8.28.18 <PERSON> Vocals\\nFiledate says it was recorded 8.28.18, however this doesn't really make sense with \\\"Hurricane's\\\" evolution, so it's safe to assume this was a misspelling of 9.28.18. Snippet of <PERSON>'s vocal take leaked February 22nd, 2025. Long<PERSON> snippet of the full track leaked February 23rd, 2025, showing it includes <PERSON>'s infamous \\\"grandma couch\\\" verse.\", \"date\": 17406144, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f607be4c3aed20d154179704a65c4e2e\", \"url\": \"https://api.pillowcase.su/api/download/f607be4c3aed20d154179704a65c4e2e\", \"size\": \"4.88 MB\", \"duration\": 261.98}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.88 MB"}, {"id": "hurricane-82", "name": "Hurricane [V19]", "artists": [], "producers": ["Cashmere Cat", "<PERSON> and the Lights", "BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: KW FS CC HURRICANE\nVersion of \"Hurricane\" with completely different, additonal production by Cash<PERSON> Cat and Francis and the Lights. <PERSON> autotuned <PERSON><PERSON><PERSON> vocals and extended melody loop in the intro.", "length": "110.11", "fileDate": 16246656, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/9944bd092b5ed0ac39e40b460bd37875", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9944bd092b5ed0ac39e40b460bd37875\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V19]\", \"artists\": \"(prod. Cash<PERSON> Cat, Francis and the Lights, BoogzDaBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: KW FS CC HURRICANE\\nVersion of \\\"Hurricane\\\" with completely different, additonal production by <PERSON><PERSON> Cat and Francis and the Lights. <PERSON> autotuned <PERSON><PERSON>e vocals and extended melody loop in the intro.\", \"date\": 16246656, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6e9f686f4acd7f83c2dc0ca443db0ad8\", \"url\": \"https://api.pillowcase.su/api/download/6e9f686f4acd7f83c2dc0ca443db0ad8\", \"size\": \"2.45 MB\", \"duration\": 110.11}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "2.45 MB"}, {"id": "hurricane-83", "name": "🗑️ Hurricane [V20]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: <PERSON>_Nino ref 1\nRecorded on September 29th, 2018 in The Mercer.", "length": "243.54", "fileDate": 16546464, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/d46fe1455b8a5f5506e7fbb749d72a3a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d46fe1455b8a5f5506e7fbb749d72a3a\", \"key\": \"Hurricane\", \"title\": \"\\ud83d\\uddd1\\ufe0f Hurricane [V20]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>aBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane_Nino ref 1\\nRecorded on September 29th, 2018 in The Mercer.\", \"date\": 16546464, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4f144d8823bbaf32510cf2ba0385d6bc\", \"url\": \"https://api.pillowcase.su/api/download/4f144d8823bbaf32510cf2ba0385d6bc\", \"size\": \"4.59 MB\", \"duration\": 243.54}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.59 MB"}, {"id": "hurricane-84", "name": "Hurricane [V21]", "artists": ["<PERSON><PERSON>", "Big Sean"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane 09.30.18 [<PERSON> Sean vrs and KW vrs 3 ref]\nVersion of \"Hurricane\" with an Ant hook, <PERSON><PERSON><PERSON> verse, <PERSON> <PERSON> verse and a more unfinished verse from <PERSON><PERSON><PERSON> at the end.", "length": "269.66", "fileDate": 16258752, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f6e2d391bced29f43fa85c760d2a7cc5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f6e2d391bced29f43fa85c760d2a7cc5\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V21]\", \"artists\": \"(feat. <PERSON><PERSON> Clemons & Big Sean) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane 09.30.18 [Big Sean vrs and KW vrs 3 ref]\\nVersion of \\\"Hurricane\\\" with an Ant hook, Kany<PERSON> verse, <PERSON> verse and a more unfinished verse from <PERSON><PERSON><PERSON> at the end.\", \"date\": 16258752, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9dfe611e2b4a8812a329d556c0b51e90\", \"url\": \"https://api.pillowcase.su/api/download/9dfe611e2b4a8812a329d556c0b51e90\", \"size\": \"5.01 MB\", \"duration\": 269.66}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "5.01 MB"}, {"id": "wally-wilder", "name": "<PERSON> [V1]", "artists": ["<PERSON><PERSON><PERSON><PERSON>"], "producers": ["BoogzDaBeast", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: <PERSON> Guitar Beat 09.22.18 [<PERSON><PERSON><PERSON><PERSON>]\nEarliest known \"Last Name\" version. Song is untitled at this point. About 23 seconds of <PERSON><PERSON><PERSON><PERSON> vocals, then it is just instrumental playing for 3 minutes.", "length": "212.16", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e7132d88c8477095b652d1a4de898837", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7132d88c8477095b652d1a4de898837\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> DEAN & Francis and the Lights)\", \"aliases\": [\"Last Name\", \"Slave Name\"], \"description\": \"OG Filename: <PERSON> Guitar Beat 09.22.18 [<PERSON><PERSON><PERSON><PERSON>]\\nEarliest known \\\"Last Name\\\" version. <PERSON> is untitled at this point. About 23 seconds of <PERSON><PERSON><PERSON><PERSON> vocals, then it is just instrumental playing for 3 minutes.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3b3abf930bf6379a010e9a75644b13ad\", \"url\": \"https://api.pillowcase.su/api/download/3b3abf930bf6379a010e9a75644b13ad\", \"size\": \"4.09 MB\", \"duration\": 212.16}", "aliases": ["Last Name", "Slave Name"], "size": "4.09 MB"}, {"id": "last-name", "name": "⭐ Last Name [V3] ", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: Last Name [<PERSON><PERSON><PERSON> and <PERSON>] KW and ANT Ref 09.22.18\nFirst revealed in 2018 after a video was posted to Instagram of <PERSON><PERSON><PERSON> rapping the song from <PERSON><PERSON>. Is Track 7 on the SNL Tracklist. Samples \"<PERSON>\" by <PERSON><PERSON><PERSON>.", "length": "232.51", "fileDate": 15710976, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b87584e3ff0fd58878a71da803633954", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b87584e3ff0fd58878a71da803633954\", \"key\": \"Last Name\", \"title\": \"\\u2b50 Last Name [V3] \", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>KE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: Last Name [<PERSON><PERSON><PERSON> and <PERSON>] K<PERSON> and ANT Ref 09.22.18\\nFirst revealed in 2018 after a video was posted to Instagram of <PERSON><PERSON><PERSON> rapping the song from <PERSON><PERSON>. Is Track 7 on the SNL Tracklist. Samples \\\"<PERSON>\\\" by <PERSON><PERSON><PERSON>\", \"date\": 15710976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"73cdcc35a38d8c0b6605aed26d101431\", \"url\": \"https://api.pillowcase.su/api/download/73cdcc35a38d8c0b6605aed26d101431\", \"size\": \"4.41 MB\", \"duration\": 232.51}", "aliases": ["Slave Name"], "size": "4.41 MB"}, {"id": "last-name-87", "name": "Last Name [V4] ", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: LAST NAME [KW edited and ANT harms] 09.23.18\nVery short Ant vocals in this version, includes longer uncut portions of <PERSON> mumble vocals. Said to be same version as the one played at FADER's office. Stems leaked in November 2021.", "length": "232.45", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/df6ffe7483c640943e96feb6a3fdb729", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/df6ffe7483c640943e96feb6a3fdb729\", \"key\": \"Last Name\", \"title\": \"Last Name [V4] \", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: LAST NAME [KW edited and ANT harms] 09.23.18\\nVery short Ant vocals in this version, includes longer uncut portions of <PERSON> mumble vocals. Said to be same version as the one played at FADER's office. Stems leaked in November 2021.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c6d3a07df6d4b50150bd66f33a7066dc\", \"url\": \"https://api.pillowcase.su/api/download/c6d3a07df6d4b50150bd66f33a7066dc\", \"size\": \"4.41 MB\", \"duration\": 232.45}", "aliases": ["Slave Name"], "size": "4.41 MB"}, {"id": "last-name-88", "name": "Last Name [V7]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: LAST NAME [Mute Synths] 09.25.18\nVersion that has extra <PERSON><PERSON><PERSON> mumble vocals at the beginning and end of the song. The structure of <PERSON><PERSON><PERSON>'s verses is also slightly different. There is instrumental where <PERSON><PERSON>'s chorus usually is and the only <PERSON><PERSON> vocals in this version of the song are background vocals.", "length": "232.51", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/5695dd5dc96ff77ad614a222abaeed48", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5695dd5dc96ff77ad614a222abaeed48\", \"key\": \"Last Name\", \"title\": \"Last Name [V7]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: LAST NAME [Mute Synths] 09.25.18\\nVersion that has extra Ka<PERSON><PERSON> mumble vocals at the beginning and end of the song. The structure of <PERSON><PERSON><PERSON>'s verses is also slightly different. There is instrumental where <PERSON><PERSON>'s chorus usually is and the only <PERSON><PERSON> vocals in this version of the song are background vocals.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea9c8b0a0cee9e76f3a7ed5a8b49b6fb\", \"url\": \"https://api.pillowcase.su/api/download/ea9c8b0a0cee9e76f3a7ed5a8b49b6fb\", \"size\": \"4.41 MB\", \"duration\": 232.51}", "aliases": ["Slave Name"], "size": "4.41 MB"}, {"id": "last-name-89", "name": "✨ Last Name [V9]", "artists": [], "producers": ["BoogzDaBeast", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: LAST NAME [Ant Ref] 09.27.18\nHas a new intro that samples \"Wake Up Mr. West\" and <PERSON><PERSON> punch-ins.", "length": "232.52", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/bbf9d860c1d024d2e39607f0ae2f9ed1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbf9d860c1d024d2e39607f0ae2f9ed1\", \"key\": \"Last Name\", \"title\": \"\\u2728 Last Name [V9]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: LAST NAME [Ant Ref] 09.27.18\\nHas a new intro that samples \\\"Wake Up Mr. West\\\" and <PERSON><PERSON> punch-ins.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a1ad55da2b059b2cbee9b06f9d5ee5f2\", \"url\": \"https://api.pillowcase.su/api/download/a1ad55da2b059b2cbee9b06f9d5ee5f2\", \"size\": \"4.41 MB\", \"duration\": 232.52}", "aliases": ["Slave Name"], "size": "4.41 MB"}, {"id": "last-name-90", "name": "Last Name [V11]", "artists": ["<PERSON><PERSON>", "E.VAX"], "producers": ["BoogzDaBeast", "E.VAX", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "Version intended for September 29th, 2018 \"SNL\" Yandhi. Bounced 28 September 2018. Contains the E.VAX instrumental and vocals. Contains open likely meant for Desiigner & 070 Shake, as well as weird mixing on the second verse.", "length": "235.35", "fileDate": 16724448, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2d19a840ed13f4a4b0a319f8ab3e69f9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d19a840ed13f4a4b0a319f8ab3e69f9\", \"key\": \"Last Name\", \"title\": \"Last Name [V11]\", \"artists\": \"(feat. <PERSON><PERSON> & E.<PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"Version intended for September 29th, 2018 \\\"SNL\\\" Yandhi. Bounced 28 September 2018. Contains the E.VAX instrumental and vocals. Contains open likely meant for <PERSON><PERSON>gner & 070 Shake, as well as weird mixing on the second verse.\", \"date\": 16724448, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"57baf8e1dc55169ae40981521a1cc643\", \"url\": \"https://api.pillowcase.su/api/download/57baf8e1dc55169ae40981521a1cc643\", \"size\": \"4.46 MB\", \"duration\": 235.35}", "aliases": ["Slave Name"], "size": "4.46 MB"}, {"id": "last-name-91", "name": "Last Name [V12]", "artists": [], "producers": [], "notes": "OG Filename: Last Name 9.29.18 [Ant Electric Lady]\nVersion of the song that seemingly has <PERSON><PERSON> provide a reference for <PERSON><PERSON><PERSON>'s second verse. Put up for sale by <PERSON> on TheSource.", "length": "13.58", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/350d8941e9e0bdc29834404ed9d5b701", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/350d8941e9e0bdc29834404ed9d5b701\", \"key\": \"Last Name\", \"title\": \"Last Name [V12]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: Last Name 9.29.18 [Ant Electric Lady]\\nVersion of the song that seemingly has <PERSON><PERSON> provide a reference for <PERSON><PERSON><PERSON>'s second verse. Put up for sale by <PERSON> on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"22ee2f91a6576cfcd2145533e1acfaf6\", \"url\": \"https://api.pillowcase.su/api/download/22ee2f91a6576cfcd2145533e1acfaf6\", \"size\": \"909 kB\", \"duration\": 13.58}", "aliases": ["Slave Name"], "size": "909 kB"}, {"id": "last-name-92", "name": "Last Name [V13]", "artists": [], "producers": ["BoogzDaBeast", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: Last Name_Nino ref 1\nRecorded on September 29th, 2018 in The Mercer. Leaked after a groupbuy.", "length": "235.79", "fileDate": 15977952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8439a2c565f3049122da6a0bcfd2f638", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8439a2c565f3049122da6a0bcfd2f638\", \"key\": \"Last Name\", \"title\": \"Last Name [V13]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: Last Name_Nino ref 1\\nRecorded on September 29th, 2018 in The Mercer. Leaked after a groupbuy.\", \"date\": 15977952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"921159fd685da35d7bd64905179b167f\", \"url\": \"https://api.pillowcase.su/api/download/921159fd685da35d7bd64905179b167f\", \"size\": \"4.47 MB\", \"duration\": 235.79}", "aliases": ["Slave Name"], "size": "4.47 MB"}, {"id": "last-name-93", "name": "Last Name [V14]", "artists": ["<PERSON><PERSON>", "E.VAX"], "producers": ["BoogzDaBeast", "E.VAX", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "Alternate version of \"Last Name\", originally said to be a response to <PERSON> dissing <PERSON><PERSON><PERSON> on SNL, but this is simply false as <PERSON> made those comments in October. Contains a good deal of mumble. <PERSON><PERSON> and E.<PERSON> background vocals. Originally leaked October 3rd, 20[??] as a stem edit, with the real file later leaking - as a fake WAV.", "length": "236.02", "fileDate": 17106336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c0fcb0452ce94d843d28a54abf138e98", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0fcb0452ce94d843d28a54abf138e98\", \"key\": \"Last Name\", \"title\": \"Last Name [V14]\", \"artists\": \"(feat. <PERSON><PERSON> & E.<PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"Alternate version of \\\"Last Name\\\", originally said to be a response to <PERSON> dissing Kanye on SNL, but this is simply false as <PERSON> made those comments in October. Contains a good deal of mumble. Has <PERSON><PERSON> and <PERSON><PERSON> background vocals. Originally leaked October 3rd, 20[??] as a stem edit, with the real file later leaking - as a fake WAV.\", \"date\": 17106336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9559d35b03e435f0688b4246d228308b\", \"url\": \"https://api.pillowcase.su/api/download/9559d35b03e435f0688b4246d228308b\", \"size\": \"4.47 MB\", \"duration\": 236.02}", "aliases": ["Slave Name"], "size": "4.47 MB"}, {"id": "last-name-94", "name": "Last Name [V15]", "artists": ["<PERSON><PERSON>", "E.VAX"], "producers": ["BoogzDaBeast", "E.VAX", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "OG Filename: LAST NAME [KW ref short version] 09.30.18\nSimilar to the version from the FADER snippet but with a slightly further along instrumental. Used on the 10.5.18 tracklist. Vocals are not autotuned and is a different vocal take. <PERSON> <PERSON><PERSON> and <PERSON><PERSON> background vocals. Leaked after a $1600 GB. Stems leaked in November 2021. 1:09 minutes long :sob: :cwl:", "length": "69.85", "fileDate": 16079904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3790dfa3b9d51b5242161db6fae89e5e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3790dfa3b9d51b5242161db6fae89e5e\", \"key\": \"Last Name\", \"title\": \"Last Name [V15]\", \"artists\": \"(feat. <PERSON><PERSON> & E.<PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"OG Filename: LAST NAME [KW ref short version] 09.30.18\\nSimilar to the version from the FADER snippet but with a slightly further along instrumental. Used on the 10.5.18 tracklist. Vocals are not autotuned and is a different vocal take. Has <PERSON><PERSON> and <PERSON><PERSON> background vocals. Leaked after a $1600 GB. Stems leaked in November 2021. 1:09 minutes long :sob: :cwl:\", \"date\": 16079904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e3cfc1b28ef4aed7ee4d70930d127a0e\", \"url\": \"https://api.pillowcase.su/api/download/e3cfc1b28ef4aed7ee4d70930d127a0e\", \"size\": \"1.81 MB\", \"duration\": 69.85}", "aliases": ["Slave Name"], "size": "1.81 MB"}, {"id": "lovey", "name": "<PERSON><PERSON> [V1]", "artists": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> Ref 09.18.18\nEarliest known version of \"<PERSON><PERSON>\". <PERSON> <PERSON><PERSON> vocals and a mumble Kanye verse. Instrumental loosely based on \"Can't Help Falling In Love\" by <PERSON>. <PERSON><PERSON><PERSON> has a brief backing vocal at 1:15. Original snippet leaked in 2019, later leaked after a GB.", "length": "167.88", "fileDate": 16069536, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/31e73060462cbed4ebae19827d672356", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/31e73060462cbed4ebae19827d672356\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON>ons & Jeremih)\", \"aliases\": [\"She's <PERSON><PERSON>\"], \"description\": \"OG Filename: Lovey - KW Ref 09.18.18\\nEarliest known version of \\\"Love<PERSON>\\\". Has <PERSON><PERSON> vocals and a mumble Kanye verse. Instrumental loosely based on \\\"Can't Help Falling In Love\\\" by <PERSON>. <PERSON><PERSON><PERSON> has a brief backing vocal at 1:15. Original snippet leaked in 2019, later leaked after a GB.\", \"date\": 16069536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a8703f8285bbf833583f12cb71f2fa0d\", \"url\": \"https://api.pillowcase.su/api/download/a8703f8285bbf833583f12cb71f2fa0d\", \"size\": \"3.38 MB\", \"duration\": 167.88}", "aliases": ["She's <PERSON><PERSON>"], "size": "3.38 MB"}, {"id": "she-s-lovely", "name": "<PERSON><PERSON> - She's <PERSON><PERSON> [V2]", "artists": ["Kanye West", "070 Shake", "<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON><PERSON> Shake x Ant Ref HA\nA further version of \"Lovely\" with 070 Shake vocals. This version seems to have the same <PERSON><PERSON><PERSON> vocals as V1, meaning it is still unfinished. Has a mumble verse from <PERSON><PERSON>, with 070 Shake adlibs. Considered for <PERSON><PERSON>'s album. Original snippets leaked September 2022 and May 15th, 2023.", "length": "267.32", "fileDate": 17116704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/bd2cd8a3fcbcc140e65142653a0add3e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bd2cd8a3fcbcc140e65142653a0add3e\", \"key\": \"She's <PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> - She's <PERSON><PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, 070 <PERSON> & Jeremih)\", \"aliases\": [\"Love<PERSON>\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x Ant Ref HA\\nA further version of \\\"Lovely\\\" with 070 Shake vocals. This version seems to have the same Kanye vocals as V1, meaning it is still unfinished. Has a mumble verse from <PERSON><PERSON>, with 070 Shake adlibs. Considered for <PERSON><PERSON>'s album. Original snippets leaked September 2022 and May 15th, 2023.\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4d3f5de44dcfac1cd086a4f2e40f7f74\", \"url\": \"https://api.pillowcase.su/api/download/4d3f5de44dcfac1cd086a4f2e40f7f74\", \"size\": \"4.97 MB\", \"duration\": 267.32}", "aliases": ["<PERSON><PERSON>"], "size": "4.97 MB"}, {"id": "make-cap", "name": "Make Cap", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> Cap [kw freestyle]\nFreestyle on RONNY J beat, originally being sold as \"Freestyle 9\" by <PERSON>.", "length": "110.56", "fileDate": 16240608, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6cace4f212294f7ae2f21adebd1fec73", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6cace4f212294f7ae2f21adebd1fec73\", \"key\": \"Make Cap\", \"title\": \"Make Cap\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"aliases\": [\"Freestyle 9\"], \"description\": \"OG Filename: RJ - Make Cap [kw freestyle]\\nFreestyle on RONNY J beat, originally being sold as \\\"Freestyle 9\\\" by <PERSON> Goblin.\", \"date\": 16240608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"18f6a4ba863299a61a41f4371a352f1d\", \"url\": \"https://api.pillowcase.su/api/download/18f6a4ba863299a61a41f4371a352f1d\", \"size\": \"2.46 MB\", \"duration\": 110.56}", "aliases": ["Freestyle 9"], "size": "2.46 MB"}, {"id": "new-body", "name": "New Body [V1]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J"], "notes": "A rough demo with <PERSON><PERSON><PERSON> mumble vocals and <PERSON><PERSON>. <PERSON><PERSON><PERSON>'s vocals were used in a fake leak made by <PERSON><PERSON><PERSON> named \"HARDD\".", "length": "15.1", "fileDate": 16231104, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e34b0f8a6783737a75b75122bae9ad45", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e34b0f8a6783737a75b75122bae9ad45\", \"key\": \"New Body\", \"title\": \"New Body [V1]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"A rough demo with <PERSON><PERSON><PERSON> mumble vocals and <PERSON><PERSON>. <PERSON><PERSON><PERSON>'s vocals were used in a fake leak made by <PERSON><PERSON><PERSON> named \\\"HARDD\\\".\", \"date\": 16231104, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0252ff7fb4380342926df66c2f72acfb\", \"url\": \"https://api.pillowcase.su/api/download/0252ff7fb4380342926df66c2f72acfb\", \"size\": \"933 kB\", \"duration\": 15.1}", "aliases": ["Can't Wait To See Your New Body"], "size": "933 kB"}, {"id": "new-body-99", "name": "New Body [V1]", "artists": ["<PERSON><PERSON>"], "producers": ["RONNY J"], "notes": "A rough demo with <PERSON><PERSON><PERSON> mumble vocals and <PERSON><PERSON>. <PERSON><PERSON><PERSON>'s vocals were used in a fake leak made by <PERSON><PERSON><PERSON> named \"HARDD\".", "length": "211.97", "fileDate": 16231104, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3e36a597fbdbcaa39e8994f366cebb2b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3e36a597fbdbcaa39e8994f366cebb2b\", \"key\": \"New Body\", \"title\": \"New Body [V1]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON>ON<PERSON><PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"A rough demo with <PERSON><PERSON><PERSON> mumble vocals and <PERSON><PERSON>. <PERSON><PERSON><PERSON>'s vocals were used in a fake leak made by <PERSON><PERSON><PERSON> named \\\"HARDD\\\".\", \"date\": 16231104, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1946e63a2d32e36cbe193758cd813735\", \"url\": \"https://api.pillowcase.su/api/download/1946e63a2d32e36cbe193758cd813735\", \"size\": \"4.08 MB\", \"duration\": 211.97}", "aliases": ["Can't Wait To See Your New Body"], "size": "4.08 MB"}, {"id": "new-body-100", "name": "New Body [V3]", "artists": ["6ix9ine"], "producers": ["RONNY J"], "notes": "A very rough demo with a 6ix9ine hook and <PERSON><PERSON><PERSON> and 6ix9ine freestyling on the beat. Groupbought along with 3 other demos of \"New Body\". Stems for this version leaked in November 2021.", "length": "181.94", "fileDate": 15792192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2963a7f9caacdb077c319efaf6906676", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2963a7f9caacdb077c319efaf6906676\", \"key\": \"New Body\", \"title\": \"New Body [V3]\", \"artists\": \"(feat. 6ix9ine) (prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"A very rough demo with a 6ix9ine hook and <PERSON><PERSON><PERSON> and 6ix9ine freestyling on the beat. Groupbought along with 3 other demos of \\\"New Body\\\". Stems for this version leaked in November 2021.\", \"date\": 15792192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cf1ff96056f8ca181f4f5fca2c5cbaaa\", \"url\": \"https://api.pillowcase.su/api/download/cf1ff96056f8ca181f4f5fca2c5cbaaa\", \"size\": \"3.6 MB\", \"duration\": 181.94}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.6 MB"}, {"id": "new-body-101", "name": "New Body [V4]", "artists": ["6ix9ine"], "producers": ["RONNY J"], "notes": "Version with <PERSON><PERSON> reference vocals and a 6ix9ine hook on the beat.", "length": "181.94", "fileDate": 15792192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1f880d370ee4a43b30cb8dd669dcec08", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f880d370ee4a43b30cb8dd669dcec08\", \"key\": \"New Body\", \"title\": \"New Body [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. 6ix9ine) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"Version with <PERSON><PERSON> reference vocals and a 6ix9ine hook on the beat.\", \"date\": 15792192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d989a289b6e7d6be0bd393f7e5efacac\", \"url\": \"https://api.pillowcase.su/api/download/d989a289b6e7d6be0bd393f7e5efacac\", \"size\": \"3.6 MB\", \"duration\": 181.94}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.6 MB"}, {"id": "new-body-102", "name": "New Body [V5]", "artists": ["6ix9ine"], "producers": ["RONNY J"], "notes": "OG Filename: New Body 9_28_18 ant bongo verse\nAlternate <PERSON><PERSON> reference track for \"New Body\" with different vocals.", "length": "5.87", "fileDate": 17220384, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b4c1b5a162edec4cb8f81a26ec3b612c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b4c1b5a162edec4cb8f81a26ec3b612c\", \"key\": \"New Body\", \"title\": \"New Body [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. 6ix9<PERSON>) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body 9_28_18 ant bongo verse\\nAlternate Ant Clemons reference track for \\\"New Body\\\" with different vocals.\", \"date\": 17220384, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"aef5049ecf40d33af32ead502149231a\", \"url\": \"https://api.pillowcase.su/api/download/aef5049ecf40d33af32ead502149231a\", \"size\": \"786 kB\", \"duration\": 5.87}", "aliases": ["Can't Wait To See Your New Body"], "size": "786 kB"}, {"id": "new-body-103", "name": "New Body [V5]", "artists": ["6ix9ine"], "producers": ["RONNY J"], "notes": "OG Filename: New Body 9_28_18 ant bongo verse\nAlternate <PERSON><PERSON> reference track for \"New Body\" with different vocals.", "length": "22.97", "fileDate": 17220384, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/5680fb939fefb7e65f847388f3aad9ae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5680fb939fefb7e65f847388f3aad9ae\", \"key\": \"New Body\", \"title\": \"New Body [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. 6ix9<PERSON>) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body 9_28_18 ant bongo verse\\nAlternate Ant Clemons reference track for \\\"New Body\\\" with different vocals.\", \"date\": 17220384, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"50f9999d42563b31bf458e0ad507c4f1\", \"url\": \"https://api.pillowcase.su/api/download/50f9999d42563b31bf458e0ad507c4f1\", \"size\": \"1.06 MB\", \"duration\": 22.97}", "aliases": ["Can't Wait To See Your New Body"], "size": "1.06 MB"}, {"id": "new-body-104", "name": "New Body [V6]", "artists": [], "producers": ["RONNY J"], "notes": "Solo Kanye version with very little mumble. Another version with the same vocals was bounced in 2024 when the song was being reworked during the VULTURES era.", "length": "12.28", "fileDate": 16071264, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6c10e10f2dedca6fa4ad1aaded25afaa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6c10e10f2dedca6fa4ad1aaded25afaa\", \"key\": \"New Body\", \"title\": \"New Body [V6]\", \"artists\": \"(prod. <PERSON>ON<PERSON><PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"Solo Kanye version with very little mumble. Another version with the same vocals was bounced in 2024 when the song was being reworked during the VULTURES era.\", \"date\": 16071264, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"74b9f2a1c9d36c31455666ef5a0fb2b4\", \"url\": \"https://api.pillowcase.su/api/download/74b9f2a1c9d36c31455666ef5a0fb2b4\", \"size\": \"888 kB\", \"duration\": 12.28}", "aliases": ["Can't Wait To See Your New Body"], "size": "888 kB"}, {"id": "new-body-105", "name": "New Body [V7]", "artists": ["Ty Dolla $ign", "6ix9ine"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [69 hook and TY$ verse] 09.29.18\n6ix9ine on the hook and a Ty Dolla $ign verse from the finished version.", "length": "171.84", "fileDate": 15792192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a6503873e370a9cc02e9d7e79216d3b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6503873e370a9cc02e9d7e79216d3b6\", \"key\": \"New Body\", \"title\": \"New Body [V7]\", \"artists\": \"(feat. <PERSON> $ign & 6ix9ine) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [69 hook and TY$ verse] 09.29.18\\n6ix9ine on the hook and a Ty Dolla $ign verse from the finished version.\", \"date\": 15792192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7742c6ca5399cf96f904656eb2ffa7ab\", \"url\": \"https://api.pillowcase.su/api/download/7742c6ca5399cf96f904656eb2ffa7ab\", \"size\": \"3.44 MB\", \"duration\": 171.84}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.44 MB"}, {"id": "new-body-106", "name": "New Body [V8]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON><PERSON><PERSON>", "6ix9ine"], "producers": ["RONNY J"], "notes": "<PERSON><PERSON> with <PERSON> $ign on the hook and a bridge from Desiigner with parts of the rough 6ix9ine and Kanye freestyle demo.", "length": "181.94", "fileDate": 15792192, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7c128145269c354cb64850efaf09bf14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c128145269c354cb64850efaf09bf14\", \"key\": \"New Body\", \"title\": \"New Body [V8]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON><PERSON><PERSON> & 6ix9<PERSON>) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"Demo with <PERSON> Dolla $ign on the hook and a bridge from <PERSON><PERSON><PERSON><PERSON> with parts of the rough 6ix9ine and Kanye freestyle demo.\", \"date\": 15792192, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d74adbd7e476d014af7efa2d34e0c29c\", \"url\": \"https://api.pillowcase.su/api/download/d74adbd7e476d014af7efa2d34e0c29c\", \"size\": \"3.6 MB\", \"duration\": 181.94}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.6 MB"}, {"id": "new-body-107", "name": "New Body [V9]", "artists": [], "producers": ["RONNY J"], "notes": "VC recording posted to d<PERSON>e July 17, 2021. <PERSON><PERSON><PERSON> appears to be doing reference vocals for <PERSON><PERSON><PERSON>.", "length": "5.98", "fileDate": 16264800, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/154029c4c54a6b775b043c2098941a0a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/154029c4c54a6b775b043c2098941a0a\", \"key\": \"New Body\", \"title\": \"New Body [V9]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"VC recording posted to dbree July 17, 2021. <PERSON><PERSON><PERSON> appears to be doing reference vocals for <PERSON><PERSON><PERSON>.\", \"date\": 16264800, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a0b4000d1572a2d1d855e2f82eebc637\", \"url\": \"https://api.pillowcase.su/api/download/a0b4000d1572a2d1d855e2f82eebc637\", \"size\": \"787 kB\", \"duration\": 5.98}", "aliases": ["Can't Wait To See Your New Body"], "size": "787 kB"}, {"id": "new-body-108", "name": "New Body [V9]", "artists": [], "producers": ["RONNY J"], "notes": "VC recording posted to d<PERSON>e July 17, 2021. <PERSON><PERSON><PERSON> appears to be doing reference vocals for <PERSON><PERSON><PERSON>.", "length": "31.27", "fileDate": 16264800, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2c7d08ca879da922d7395b41370518fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2c7d08ca879da922d7395b41370518fb\", \"key\": \"New Body\", \"title\": \"New Body [V9]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"VC recording posted to dbree July 17, 2021. <PERSON><PERSON><PERSON> appears to be doing reference vocals for <PERSON><PERSON><PERSON>.\", \"date\": 16264800, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cefc0f0d1bf73af5625b2febcf1c482e\", \"url\": \"https://api.pillowcase.su/api/download/cefc0f0d1bf73af5625b2febcf1c482e\", \"size\": \"1.19 MB\", \"duration\": 31.27}", "aliases": ["Can't Wait To See Your New Body"], "size": "1.19 MB"}, {"id": "new-body-109", "name": "New Body [V9]", "artists": [], "producers": ["RONNY J"], "notes": "VC recording posted to d<PERSON>e July 17, 2021. <PERSON><PERSON><PERSON> appears to be doing reference vocals for <PERSON><PERSON><PERSON>.", "length": "31.5", "fileDate": 16264800, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7ceb415628d1061ad7e0076f448ecfbd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7ceb415628d1061ad7e0076f448ecfbd\", \"key\": \"New Body\", \"title\": \"New Body [V9]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"VC recording posted to dbree July 17, 2021. <PERSON><PERSON><PERSON> appears to be doing reference vocals for <PERSON><PERSON><PERSON>.\", \"date\": 16264800, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"16e17694e871bcb87605d0bb57ab1330\", \"url\": \"https://api.pillowcase.su/api/download/16e17694e871bcb87605d0bb57ab1330\", \"size\": \"1.2 MB\", \"duration\": 31.5}", "aliases": ["Can't Wait To See Your New Body"], "size": "1.2 MB"}, {"id": "new-body-110", "name": "New Body [V10]", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: New Body [KW Hook] 09.29.18\nVersion of \"New Body\" with <PERSON> doing the hook in iPhone mic quality, and has an open verse.", "length": "171.84", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/67bbf92bbbbeb758bca0b0903c7aa828", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/67bbf92bbbbeb758bca0b0903c7aa828\", \"key\": \"New Body\", \"title\": \"New Body [V10]\", \"artists\": \"(prod. RONN<PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [KW Hook] 09.29.18\\nVersion of \\\"New Body\\\" with <PERSON> doing the hook in iPhone mic quality, and has an open verse.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0f4fd9c093365c5f14a0072287b1aeac\", \"url\": \"https://api.pillowcase.su/api/download/0f4fd9c093365c5f14a0072287b1aeac\", \"size\": \"3.44 MB\", \"duration\": 171.84}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.44 MB"}, {"id": "new-body-111", "name": "New Body [V11]", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: New Body_Nino ref 1\n<PERSON><PERSON> Blu \"New Body\" reference track, recorded alongside \"Hurricane\", \"Bye Bye Baby\", \"Tekken\", and \"Last Name\" in The Mercer before SNL. In a 2023 Reddit AMA, <PERSON><PERSON> said \"for [\"New Body\"] I showed [<PERSON><PERSON><PERSON>] a flow in front of him and we freestyled back and forth\".", "length": "252.76", "fileDate": 16476480, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/961e4c7d82a8b6a5270503c53e14683f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/961e4c7d82a8b6a5270503c53e14683f\", \"key\": \"New Body\", \"title\": \"New Body [V11]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body_Nino ref 1\\nNino Blu \\\"New Body\\\" reference track, recorded alongside \\\"Hurricane\\\", \\\"Bye Bye Baby\\\", \\\"Tekken\\\", and \\\"Last Name\\\" in The Mercer before SNL. In a 2023 Reddit AMA, <PERSON><PERSON> said \\\"for [\\\"New Body\\\"] I showed [<PERSON><PERSON><PERSON>] a flow in front of him and we freestyled back and forth\\\".\", \"date\": 16476480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d06fbeba7f00165ae61fa873462a69da\", \"url\": \"https://api.pillowcase.su/api/download/d06fbeba7f00165ae61fa873462a69da\", \"size\": \"4.73 MB\", \"duration\": 252.76}", "aliases": ["Can't Wait To See Your New Body"], "size": "4.73 MB"}, {"id": "new-body-112", "name": "New Body [V12]", "artists": ["Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [TY hook bass line KW ref] 09.30.18\nHas extended verse similar to the finished version but with alternate finished lines. <PERSON><PERSON><PERSON> uses some of <PERSON><PERSON> Clemons' reference lines. Dubbed \"R. Kelly Version\" as it features a <PERSON><PERSON> line.", "length": "192.05", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1bbe57f3bdcbb66b6e842e10258f7003", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1bbe57f3bdcbb66b6e842e10258f7003\", \"key\": \"New Body\", \"title\": \"New Body [V12]\", \"artists\": \"(feat. <PERSON>ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [TY hook bass line KW ref] 09.30.18\\nHas extended verse similar to the finished version but with alternate finished lines. <PERSON><PERSON><PERSON> uses some of <PERSON><PERSON> Clemons' reference lines. Dubbed \\\"R<PERSON> Kelly Version\\\" as it features a <PERSON><PERSON> line.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"caeae6c9ed31d1d6ff819e16434e7edd\", \"url\": \"https://api.pillowcase.su/api/download/caeae6c9ed31d1d6ff819e16434e7edd\", \"size\": \"3.76 MB\", \"duration\": 192.05}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.76 MB"}, {"id": "new-body-113", "name": "New Body [V13]", "artists": [], "producers": ["RONNY J"], "notes": "OG Filename: Ye New Body Tassho <PERSON><PERSON><PERSON> \"New Body\" reference track. From September 2018.", "length": "82.66", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/4fc222d333b6ae018668c770d3b9dc58", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4fc222d333b6ae018668c770d3b9dc58\", \"key\": \"New Body\", \"title\": \"New Body [V13]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON>ONN<PERSON> J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: Ye New Body Tassho Ref\\nT<PERSON><PERSON> \\\"New Body\\\" reference track. From September 2018.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c6747ba18b4f5cb0348fed4a1349b4c4\", \"url\": \"https://api.pillowcase.su/api/download/c6747ba18b4f5cb0348fed4a1349b4c4\", \"size\": \"2.02 MB\", \"duration\": 82.66}", "aliases": ["Can't Wait To See Your New Body"], "size": "2.02 MB"}, {"id": "robinhood", "name": "Robinhood", "artists": ["<PERSON>"], "producers": ["BoogzDaBeast", "Nascent"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> and K<PERSON> ref\n<PERSON><PERSON> and <PERSON> mumble collab put up for sale by TheSource. Likely the same session as \"Thank You Vol. 2\".", "length": "14", "fileDate": 15935616, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/97a29c26fdd48dad57bac09e4f1cc3ee", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/97a29c26fdd48dad57bac09e4f1cc3ee\", \"key\": \"Robinhood\", \"title\": \"Robinhood\", \"artists\": \"(feat. <PERSON>) (prod. BoogzDaBeast & Nascent)\", \"description\": \"OG Filename: <PERSON><PERSON> - CU<PERSON> and <PERSON><PERSON> ref\\nKanye and <PERSON> mumble collab put up for sale by TheSource. Likely the same session as \\\"Thank You Vol. 2\\\".\", \"date\": 15935616, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e006c02f601ac0e602b0e26ea8536a87\", \"url\": \"https://api.pillowcase.su/api/download/e006c02f601ac0e602b0e26ea8536a87\", \"size\": \"915 kB\", \"duration\": 14}", "aliases": [], "size": "915 kB"}, {"id": "sax-appeal", "name": "Sax Appeal", "artists": [], "producers": [], "notes": "OG Filename: Sax Appeal - KW Ref\nVery short demo that has barely any <PERSON> vocals, the song also finishes just as it sounds like it's about to properly start. Date is unknown, assumed to be early Yandhi era.", "length": "44.42", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/14f9cf24f4977328327bca9b5b584767", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/14f9cf24f4977328327bca9b5b584767\", \"key\": \"Sax Appeal\", \"title\": \"Sax Appeal\", \"description\": \"OG Filename: Sax Appeal - KW Ref\\nVery short demo that has barely any Ye vocals, the song also finishes just as it sounds like it's about to properly start. Date is unknown, assumed to be early Yandhi era.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"15f9090d03c5ecfc1c28bdbe6433f840\", \"url\": \"https://api.pillowcase.su/api/download/15f9090d03c5ecfc1c28bdbe6433f840\", \"size\": \"1.4 MB\", \"duration\": 44.42}", "aliases": [], "size": "1.4 MB"}, {"id": "city-in-the-sky", "name": "City In The Sky [V2]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": ["<PERSON>"], "notes": "OG Filename: city in the sky - ty.gtr.song.1.12._Master 1\nFeatures different Ty <PERSON> $ign vocals, unfinished <PERSON><PERSON><PERSON> vocals, extra <PERSON><PERSON><PERSON><PERSON> vocals and a longer 070 Shake verse. Originally leaked distorted and corrupted. Was wrongfully thought to be from ye era.", "length": "273.42", "fileDate": 16553376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7a7beaeab4b81d6e755a331268a5d79a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a7beaeab4b81d6e755a331268a5d79a\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V2]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON> <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Loyal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: city in the sky - ty.gtr.song.1.12._Master 1\\nFeatures different Ty <PERSON> $ign vocals, unfinished <PERSON><PERSON><PERSON> vocals, extra <PERSON><PERSON><PERSON><PERSON> vocals and a longer 070 Shake verse. Originally leaked distorted and corrupted. Was wrongfully thought to be from ye era.\", \"date\": 16553376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fea7464098a508bc672fafe055347b5d\", \"url\": \"https://api.pillowcase.su/api/download/fea7464098a508bc672fafe055347b5d\", \"size\": \"5.07 MB\", \"duration\": 273.42}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "5.07 MB"}, {"id": "city-in-the-sky-117", "name": "City In The Sky [V3]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": ["<PERSON>", "​MIKE DEAN"], "notes": "OG Filename: City In The Sky - [<PERSON> drums <PERSON> Ty Vocal] 09.23.18\nHas MIKE DEAN drums.", "length": "264.36", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/73aa0aa49e6ccc4e1118f2f83e617dda", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/73aa0aa49e6ccc4e1118f2f83e617dda\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V3]\", \"artists\": \"(feat. <PERSON>gn, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON> <PERSON>) (prod. <PERSON> & \\u200bMIKE DEAN)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky - [MD drums New Ty Vocal] 09.23.18\\nHas MIKE DEAN drums.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"af376767812b4de98a7209749d547898\", \"url\": \"https://api.pillowcase.su/api/download/af376767812b4de98a7209749d547898\", \"size\": \"4.92 MB\", \"duration\": 264.36}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.92 MB"}, {"id": "city-in-the-sky-118", "name": "City In The Sky [V4]", "artists": ["Ty Dolla $ign"], "producers": ["<PERSON>", "​MIKE DEAN"], "notes": "May be the version of the song above, but has not been confirmed yet. Has subtle <PERSON><PERSON><PERSON> mumble throughout the whole song, and a freestyle at the end. Also has no 070 Shake, <PERSON><PERSON><PERSON> or <PERSON><PERSON>. Snippet leaked October 9th, 2024.", "length": "12.96", "fileDate": 17284320, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/155e0c67ec8dcc74360e835857168a80", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/155e0c67ec8dcc74360e835857168a80\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V4]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON> & \\u200bMIKE DEAN)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"May be the version of the song above, but has not been confirmed yet. Has subtle <PERSON><PERSON><PERSON> mumble throughout the whole song, and a freestyle at the end. Also has no 070 Shake, <PERSON><PERSON><PERSON> or <PERSON><PERSON>. Snippet leaked October 9th, 2024.\", \"date\": 17284320, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25ffbe8e2a3dfb482eb33d4f71782b77\", \"url\": \"https://api.pillowcase.su/api/download/25ffbe8e2a3dfb482eb33d4f71782b77\", \"size\": \"899 kB\", \"duration\": 12.96}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "899 kB"}, {"id": "city-in-the-sky-119", "name": "City In The Sky [V5]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": ["<PERSON>", "​MIKE DEAN"], "notes": "OG Filename: city in the sky mike reff\nHas full Ty Dolla $ign intro, unfinished <PERSON><PERSON><PERSON> vocals, full 070 Shake verse and different drums and production. Leaked after groupbuy.", "length": "264.38", "fileDate": 16226784, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c4347d1fbfd736ad03cc8309b64163fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4347d1fbfd736ad03cc8309b64163fc\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V5]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON> <PERSON>) (prod. <PERSON> \\u200bMIKE DEAN)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: city in the sky mike reff\\nHas full Ty Dolla $ign intro, unfinished <PERSON><PERSON><PERSON> vocals, full 070 Shake verse and different drums and production. Leaked after groupbuy.\", \"date\": 16226784, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ff441d785e8d72c59fae9ce9aad07ae7\", \"url\": \"https://api.pillowcase.su/api/download/ff441d785e8d72c59fae9ce9aad07ae7\", \"size\": \"4.92 MB\", \"duration\": 264.38}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.92 MB"}, {"id": "city-in-the-sky-120", "name": "City In The Sky [V6]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "OG Filename: City In The Sky - Cut Intro 09.23.18 [3pm]\nHas less drums and a long silence at the end.", "length": "321.05", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6fc19d19e8fa8c037ce9f1c7c357d1af", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6fc19d19e8fa8c037ce9f1c7c357d1af\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V6]\", \"artists\": \"(feat. <PERSON>gn, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON>) (prod. <PERSON> & <PERSON>KE <PERSON>)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky - Cut Intro 09.23.18 [3pm]\\nHas less drums and a long silence at the end.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5164849e0e18d294cf798467256a8944\", \"url\": \"https://api.pillowcase.su/api/download/5164849e0e18d294cf798467256a8944\", \"size\": \"5.83 MB\", \"duration\": 321.05}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "5.83 MB"}, {"id": "city-in-the-sky-121", "name": "City In The Sky [V7]", "artists": ["Ty Dolla $ign", "070 Shake", "<PERSON><PERSON><PERSON><PERSON>"], "producers": ["​<PERSON>", "MIKE DEAN"], "notes": "OG Filename: City In The Sky - Cut Intro 09.23.18 [<PERSON> Drums]\nHas some better mixing on the intro.", "length": "264.36", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/62215b2e0299518edf39eae3abf33c49", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/62215b2e0299518edf39eae3abf33c49\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V7]\", \"artists\": \"(feat. <PERSON>ign, 070 <PERSON> & <PERSON>ii<PERSON>r) (prod. \\u200bJames Royo & MIKE DEAN)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky - Cut Intro 09.23.18 [MD Drums]\\nHas some better mixing on the intro.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8e169e8e3d4c4e570a76d053f93fa86b\", \"url\": \"https://api.pillowcase.su/api/download/8e169e8e3d4c4e570a76d053f93fa86b\", \"size\": \"4.92 MB\", \"duration\": 264.36}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.92 MB"}, {"id": "sky-city", "name": "Sky City [V8]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign", "070 Shake"], "producers": ["BONGO ByTheWay"], "notes": "OG Filename: Audio 2_01\nJam session for \"Sky City\" over BONG<PERSON> ByTheWay playing piano. Likely from around 9.23-9.26. Leaked October 3rd, 2022.", "length": "225.19", "fileDate": 16647552, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ad0543e09879e6ab70e6af54af968cc1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad0543e09879e6ab70e6af54af968cc1\", \"key\": \"Sky City\", \"title\": \"Sky City [V8]\", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON> $ign & 070 Shake) (prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"aliases\": [\"City In The Sky\", \"Loyalty\", \"Ooh Child Things Are Gonna Get Easier\", \"Ooh Child\"], \"description\": \"OG Filename: Audio 2_01\\nJam session for \\\"Sky City\\\" over BONGO ByTheWay playing piano. Likely from around 9.23-9.26. Leaked October 3rd, 2022.\", \"date\": 16647552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fdc273a637bf9ea3583ce16046466240\", \"url\": \"https://api.pillowcase.su/api/download/fdc273a637bf9ea3583ce16046466240\", \"size\": \"2.49 MB\", \"duration\": 225.19}", "aliases": ["City In The Sky", "Loyalty", "Ooh Child Things Are Gonna Get Easier", "Ooh <PERSON>"], "size": "2.49 MB"}, {"id": "city-in-the-sky-123", "name": "City In The Sky [V9]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign", "070 Shake", "<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: City In The Sky 09.24.18 [Add <PERSON>]\nHas <PERSON><PERSON> vocals included as background vocals for the intro.", "length": "264.36", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b6e3f4ee214be70a430fe75863c8fbd0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b6e3f4ee214be70a430fe75863c8fbd0\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V9]\", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON> $ign, 070 <PERSON> & Desii<PERSON>r)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.24.18 [Add <PERSON>t <PERSON>]\\nHas <PERSON> vocals included as background vocals for the intro.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3c0e19dbc3765cfedec5935557e88f37\", \"url\": \"https://api.pillowcase.su/api/download/3c0e19dbc3765cfedec5935557e88f37\", \"size\": \"4.92 MB\", \"duration\": 264.36}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.92 MB"}, {"id": "city-in-the-sky-124", "name": "City In The Sky [V10]", "artists": ["Ty Dolla $ign", "070 Shake", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: City In The Sky 09.24.18 [EON bounce]\nHas no drums.", "length": "264.36", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/98578f16631c55338117ca70b22a4a17", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/98578f16631c55338117ca70b22a4a17\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V10]\", \"artists\": \"(feat. <PERSON>ign, 070 Shake, Ant Clemons & Desiigner)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.24.18 [EON bounce]\\nHas no drums.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"360c9c68f2659595cf840dac0beadf9f\", \"url\": \"https://api.pillowcase.su/api/download/360c9c68f2659595cf840dac0beadf9f\", \"size\": \"4.92 MB\", \"duration\": 264.36}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.92 MB"}, {"id": "city-in-the-sky-125", "name": "City In The Sky [V11]", "artists": ["Ty Dolla $ign", "070 Shake", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: City In The Sky 09.24.18 [No Drums]\nHas no drums and is louder.", "length": "264.36", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/4adc36d99c47790e9e6815e69bee47ba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4adc36d99c47790e9e6815e69bee47ba\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V11]\", \"artists\": \"(feat. <PERSON>ign, 070 Shake, Ant Clemons & Desiigner)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.24.18 [No Drums]\\nHas no drums and is louder.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dfc8a518b7ad26226b9abe9a09c5b6c4\", \"url\": \"https://api.pillowcase.su/api/download/dfc8a518b7ad26226b9abe9a09c5b6c4\", \"size\": \"4.92 MB\", \"duration\": 264.36}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.92 MB"}, {"id": "city-in-the-sky-126", "name": "City in The Sky [V13]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": ["​<PERSON>", "MIKE DEAN"], "notes": "OG Filename: City in The Sky 09.25.18 [K<PERSON> edits <PERSON><PERSON> and <PERSON>d ch vocals under shake]\nHas more vocals at the end of the song.", "length": "227.9", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/9e5291f269cec8228cd99f74af48372d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9e5291f269cec8228cd99f74af48372d\", \"key\": \"City in The Sky\", \"title\": \"City in The Sky [V13]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON>) (prod. \\u200bJames Royo & MIKE DEAN)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City in The Sky 09.25.18 [K<PERSON> edits <PERSON><PERSON> and Add ch vocals under shake]\\nHas more vocals at the end of the song.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"69bc13939c1b0cccbfb44cb48a456a1d\", \"url\": \"https://api.pillowcase.su/api/download/69bc13939c1b0cccbfb44cb48a456a1d\", \"size\": \"4.34 MB\", \"duration\": 227.9}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.34 MB"}, {"id": "city-in-the-sky-127", "name": "City In The Sky [V14]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": [], "notes": "OG Filename: City In The Sky 09.25.18 [<PERSON><PERSON> edits CUT TIME]\nHas less Ty delay effects on his vocals.", "length": "224.88", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e1ef55515d536eeed16547c5fc59fdd8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e1ef55515d536eeed16547c5fc59fdd8\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V14]\", \"artists\": \"(feat. <PERSON>gn, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON> <PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.25.18 [K<PERSON> edits CUT TIME]\\nHas less Ty delay effects on his vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"52431c46aa164f0f45103207a2f49d39\", \"url\": \"https://api.pillowcase.su/api/download/52431c46aa164f0f45103207a2f49d39\", \"size\": \"4.29 MB\", \"duration\": 224.88}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.29 MB"}, {"id": "city-in-the-sky-128", "name": "City In The Sky [V15]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": [], "notes": "OG Filename: City In The Sky 09.25.18 [open verses]\nHas an open verse.", "length": "227.9", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e47408cc91e115286bfa2e470f8a0ec8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e47408cc91e115286bfa2e470f8a0ec8\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V15]\", \"artists\": \"(feat. <PERSON>gn, <PERSON><PERSON>, <PERSON><PERSON><PERSON>r & 07<PERSON> Shake)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easierl\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.25.18 [open verses]\\nHas an open verse.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"caf800a84643602f3f245cffbbf657c8\", \"url\": \"https://api.pillowcase.su/api/download/caf800a84643602f3f245cffbbf657c8\", \"size\": \"4.34 MB\", \"duration\": 227.9}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easierl", "Sky City"], "size": "4.34 MB"}, {"id": "city-in-the-sky-129", "name": "City In The Sky [V16]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake", "<PERSON>"], "producers": ["Ty Dolla $ign", "MIKE DEAN"], "notes": "OG Filename: City In The Sky 09.27.18 [Ant electric lady ref] \nAnt reference track for \"City In The Sky\".", "length": "227.9", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e406cf343fe43243f2a4425fad05ec51", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e406cf343fe43243f2a4425fad05ec51\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V16]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON>ign, <PERSON><PERSON><PERSON><PERSON>, 070 <PERSON> & <PERSON>) (prod. <PERSON> $ign & MIKE DEAN)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.27.18 [Ant electric lady ref] \\nAnt reference track for \\\"City In The Sky\\\".\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"14a23f30732fc9da4d633a3accafff19\", \"url\": \"https://api.pillowcase.su/api/download/14a23f30732fc9da4d633a3accafff19\", \"size\": \"4.34 MB\", \"duration\": 227.9}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.34 MB"}, {"id": "city-in-the-sky-130", "name": "City In The Sky [V17]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake", "<PERSON>"], "producers": ["​<PERSON>", "MIKE DEAN"], "notes": "OG Filename: City In The Sky 09.29.18 [Open verse]\nOpen verse version of \"City In The Sky\" from 9.29. The 9.29 \"Sky City\" ProTools are named \"City In The Sky 09.29.18 [ADD CUDI and MD DRUMS] KW SNL vocal.ptx\"", "length": "227.91", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b9341813771f71eeef1d4b5a9984e9a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b9341813771f71eeef1d4b5a9984e9a6\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V17]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 070 <PERSON> & <PERSON>) (prod. \\u200bJames Royo & MIKE DEAN)\", \"aliases\": [\"<PERSON>yal<PERSON>\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.29.18 [Open verse]\\nOpen verse version of \\\"City In The Sky\\\" from 9.29. The 9.29 \\\"Sky City\\\" ProTools are named \\\"City In The Sky 09.29.18 [ADD CUDI and MD DRUMS] KW SNL vocal.ptx\\\"\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fb3f8dc0e491e1318c5ac0140421b410\", \"url\": \"https://api.pillowcase.su/api/download/fb3f8dc0e491e1318c5ac0140421b410\", \"size\": \"4.34 MB\", \"duration\": 227.91}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.34 MB"}, {"id": "city-in-the-sky-131", "name": "City In The Sky [V18]", "artists": [], "producers": [], "notes": "OG Filename: City In The Sky 09.29.18 [Acapella Intro]\nHas the full uncut acapella intro basically no other version after this.", "length": "215.76", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e9b1c0dd912eeaba35cb3818b57e0480", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9b1c0dd912eeaba35cb3818b57e0480\", \"key\": \"City In The Sky\", \"title\": \"City In The Sky [V18]\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"<PERSON>oh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Sky City\"], \"description\": \"OG Filename: City In The Sky 09.29.18 [Acapella Intro]\\nHas the full uncut acapella intro basically no other version after this.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"024563b53e24f3bf793da046a6b7cc35\", \"url\": \"https://api.pillowcase.su/api/download/024563b53e24f3bf793da046a6b7cc35\", \"size\": \"4.14 MB\", \"duration\": 215.76}", "aliases": ["Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Sky City"], "size": "4.14 MB"}, {"id": "sky-city-132", "name": "⭐ Sky City [V20]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake", "<PERSON>"], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "OG Filename: Sky City 09.29.18 [<PERSON><PERSON> verse]\nSNL version with <PERSON><PERSON><PERSON> rapping over an alternate instrumental. Original snippet leaked June 3rd, 2021.", "length": "227.9", "fileDate": 16294176, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/86961f6f77e8aef561e06f1913e22985", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86961f6f77e8aef561e06f1913e22985\", \"key\": \"Sky City\", \"title\": \"\\u2b50 Sky City [V20]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 070 <PERSON> & <PERSON>) (prod. <PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"City In The Sky\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\", \"Loyalty\"], \"description\": \"OG Filename: Sky City 09.29.18 [KW verse]\\nSNL version with <PERSON><PERSON><PERSON> rapping over an alternate instrumental. Original snippet leaked June 3rd, 2021.\", \"date\": 16294176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"386134eaa6097c7f464fd3a3a188ac02\", \"url\": \"https://api.pillowcase.su/api/download/386134eaa6097c7f464fd3a3a188ac02\", \"size\": \"4.34 MB\", \"duration\": 227.9}", "aliases": ["City In The Sky", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier", "Loyalty"], "size": "4.34 MB"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> freestyle, from the back of beats that <PERSON><PERSON><PERSON><PERSON><PERSON> sent during the Chicago sessions.", "length": "152.58", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8be6f3cc79c75e1d66d9f0af4e633a85", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8be6f3cc79c75e1d66d9f0af4e633a85\", \"key\": \"<PERSON>gh<PERSON><PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Spaghettinski - Ant Ref\\nRough freestyle, from the back of beats that <PERSON><PERSON><PERSON><PERSON><PERSON> sent during the Chicago sessions.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ea856ba2dd2addd732c75e9111f6d2d\", \"url\": \"https://api.pillowcase.su/api/download/5ea856ba2dd2addd732c75e9111f6d2d\", \"size\": \"3.13 MB\", \"duration\": 152.58}", "aliases": [], "size": "3.13 MB"}, {"id": "take-me-to-the-light", "name": "Take Me To The Light [V6]", "artists": ["<PERSON> and the Lights", "<PERSON>", "<PERSON>", "<PERSON> the Rapper", "<PERSON><PERSON>"], "producers": ["Kanye West", "benny blanco", "<PERSON> and the Lights", "<PERSON>", "Cashmere Cat", "<PERSON><PERSON>", "<PERSON>"], "notes": "OG Filename: Take Me to the Light Remix PREMASTER\nIncludes <PERSON><PERSON> verse. The filename lists this version as a \"remix\". <PERSON><PERSON> verse is the same as his verse in an earlier version except it sounds off-beat. Unknown who's song this was at this point. Bounced 7 minutes before the non-remix premaster. This (or V7) was released officially by Francis and the Lights on streaming September 17th, 2020, except it fades out before <PERSON><PERSON>'s verse.", "length": "265.85", "fileDate": 16307136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a8ace322e1a4573bc6b22a5dd55a1806", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a8ace322e1a4573bc6b22a5dd55a1806\", \"key\": \"Take Me To The Light\", \"title\": \"Take Me To The Light [V6]\", \"artists\": \"(feat. <PERSON> and the Lights, <PERSON>, <PERSON>, <PERSON> the Rapper & Ant Clemons) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and the Lights, <PERSON>, Cash<PERSON> Cat, B<PERSON> & <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"OG Filename: Take Me to the Light Remix PREMASTER\\nIncludes <PERSON><PERSON> verse. The filename lists this version as a \\\"remix\\\". <PERSON><PERSON> verse is the same as his verse in an earlier version except it sounds off-beat. Unknown who's song this was at this point. Bounced 7 minutes before the non-remix premaster. This (or V7) was released officially by <PERSON> and the Lights on streaming September 17th, 2020, except it fades out before <PERSON><PERSON>'s verse.\", \"date\": 16307136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9700ae510fc119920a3c3197bf47dccb\", \"url\": \"https://api.pillowcase.su/api/download/9700ae510fc119920a3c3197bf47dccb\", \"size\": \"4.95 MB\", \"duration\": 265.85}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "4.95 MB"}, {"id": "take-me-to-the-light-135", "name": "Take Me To The Light [V7]", "artists": ["<PERSON> and the Lights", "<PERSON>", "<PERSON> the Rapper", "<PERSON>", "<PERSON>"], "producers": ["Kanye West", "benny blanco", "<PERSON> and the Lights", "<PERSON>", "Cashmere Cat", "<PERSON><PERSON>", "<PERSON>"], "notes": "OG Filename: Take Me to the Light PREMASTER\nIncludes the full uncensored Lil Dicky outro which was later reused in <PERSON>' song \"METTA WORLD PEACE\". Unknown who's song it was at this point. This (or V6) was released officially by Francis and the Lights on streaming September 17th, 2020, except it fades out before <PERSON>'s outro.", "length": "284.88", "fileDate": 16307136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/0280b48ea22ae9e294a5fdffe709d60c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0280b48ea22ae9e294a5fdffe709d60c\", \"key\": \"Take Me To The Light\", \"title\": \"Take Me To The Light [V7]\", \"artists\": \"(feat. <PERSON> and the Lights, <PERSON>, <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and the Lights, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"OG Filename: Take Me to the Light PREMASTER\\nIncludes the full uncensored <PERSON> Dick<PERSON> outro which was later reused in <PERSON>' song \\\"METTA WORLD PEACE\\\". Unknown who's song it was at this point. This (or V6) was released officially by Francis and the Lights on streaming September 17th, 2020, except it fades out before <PERSON>'s outro.\", \"date\": 16307136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"468a22f7675b55c8ebf51641689617e1\", \"url\": \"https://api.pillowcase.su/api/download/468a22f7675b55c8ebf51641689617e1\", \"size\": \"5.25 MB\", \"duration\": 284.88}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "5.25 MB"}, {"id": "tekken-136", "name": "<PERSON><PERSON><PERSON> [V4]", "artists": ["6ix9ine"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON>kken Beat - 9.25.18 [Better Mix]\nFeatures a further along beat compared to the earlier version.", "length": "166.7", "fileDate": 16345152, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ee495eed2e441ce3b0f0c72510d70eb6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ee495eed2e441ce3b0f0c72510d70eb6\", \"key\": \"Tekken\", \"title\": \"<PERSON>k<PERSON> [V4]\", \"artists\": \"(feat. 6ix9ine) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KANGA\"], \"description\": \"OG Filename: Tekken Beat - 9.25.18 [Better Mix]\\nFeatures a further along beat compared to the earlier version.\", \"date\": 16345152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4d967dfe1f8a72f19b0e5a31ee911813\", \"url\": \"https://api.pillowcase.su/api/download/4d967dfe1f8a72f19b0e5a31ee911813\", \"size\": \"3.36 MB\", \"duration\": 166.7}", "aliases": ["KANGA"], "size": "3.36 MB"}, {"id": "thank-you", "name": "Thank You (Vol. 2) [V1]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: Thank You Vol 2 - CU<PERSON> and KW REF\nUnfinished throwaway recorded after KIDS SEE GHOSTS, since the sample was not released until August 2018. Was eventually given to <PERSON> $ign. The <PERSON><PERSON><PERSON> vocal take is the same as the released version.", "length": "243.93", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8efe70d53a0fb3aa1e372fe79f34cf90", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8efe70d53a0fb3aa1e372fe79f34cf90\", \"key\": \"Thank You (Vol. 2)\", \"title\": \"Thank You (Vol. 2) [V1]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"Track 5\", \"Track 6\"], \"description\": \"OG Filename: Thank You Vol 2 - CUDI and KW REF\\nUnfinished throwaway recorded after KIDS SEE GHOSTS, since the sample was not released until August 2018. Was eventually given to Ty Dolla $ign. The Kanye vocal take is the same as the released version.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6fc651c0c3a67bf3e8d60688cbc259b6\", \"url\": \"https://api.pillowcase.su/api/download/6fc651c0c3a67bf3e8d60688cbc259b6\", \"size\": \"4.59 MB\", \"duration\": 243.93}", "aliases": ["Track 5", "Track 6"], "size": "4.59 MB"}, {"id": "good-morning", "name": "Good Morning [V1]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG FIlename: GOOD MORNING ANT JERIMIH KW\nWas previously thought to be a standalone Ant song later given to <PERSON><PERSON><PERSON>, but this is incorrect. The song was recorded to help <PERSON><PERSON><PERSON> get an idea for his own version.", "length": "115.77", "fileDate": 16053120, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/600f7d2a250277d7959487888bbfbc2d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/600f7d2a250277d7959487888bbfbc2d\", \"key\": \"Good Morning\", \"title\": \"Good Morning [V1]\", \"artists\": \"(ref. Ant Clemons & Jeremih) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Selah\", \"The Chakra\", \"I Love It (Remix)\"], \"description\": \"OG FIlename: GOOD MORNING ANT JERIMIH KW\\nWas previously thought to be a standalone Ant song later given to <PERSON><PERSON><PERSON>, but this is incorrect. The song was recorded to help <PERSON><PERSON><PERSON> get an idea for his own version.\", \"date\": 16053120, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9d7a27b0161c21295908450894553f0e\", \"url\": \"https://api.pillowcase.su/api/download/9d7a27b0161c21295908450894553f0e\", \"size\": \"2.54 MB\", \"duration\": 115.77}", "aliases": ["Chakras", "<PERSON><PERSON>", "The Chakra", "I Love It (Remix)"], "size": "2.54 MB"}, {"id": "i-love-it-139", "name": "I Love It (Remix) [V2]", "artists": ["<PERSON>", "The-Dream"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "Has more The-Dream vocals not heard in any other version. May be a reference track. Played in the 2018 GAJ sessions. Made sometime before or on September 16th, 2018.", "length": "66.05", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ef76bd389b0a33e5b794b8dbc3786b07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef76bd389b0a33e5b794b8dbc3786b07\", \"key\": \"I Love It (Remix)\", \"title\": \"I Love It (Remix) [V2]\", \"artists\": \"(feat. <PERSON> & The-Dream) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Chakra<PERSON>\", \"Selah\", \"The Chakra\", \"Good Morning\"], \"description\": \"Has more The-Dream vocals not heard in any other version. May be a reference track. Played in the 2018 GAJ sessions. Made sometime before or on September 16th, 2018.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f84be9269101a6126f3664f3f9d45a1c\", \"url\": \"https://api.pillowcase.su/api/download/f84be9269101a6126f3664f3f9d45a1c\", \"size\": \"1.22 MB\", \"duration\": 66.05}", "aliases": ["Chakras", "<PERSON><PERSON>", "The Chakra", "Good Morning"], "size": "1.22 MB"}, {"id": "i-love-it-140", "name": "I Love It (Remix) [V2]", "artists": ["<PERSON>", "The-Dream"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "Has more The-Dream vocals not heard in any other version. May be a reference track. Played in the 2018 GAJ sessions. Made sometime before or on September 16th, 2018.", "length": "66.05", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/17dddd3f91d6b71829a045b62a8478cb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/17dddd3f91d6b71829a045b62a8478cb\", \"key\": \"I Love It (Remix)\", \"title\": \"I Love It (Remix) [V2]\", \"artists\": \"(feat. <PERSON> & The-Dream) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Selah\", \"The Chakra\", \"Good Morning\"], \"description\": \"Has more The-Dream vocals not heard in any other version. May be a reference track. Played in the 2018 GAJ sessions. Made sometime before or on September 16th, 2018.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7d462aabbf4c761c65e4da40f04db2d3\", \"url\": \"https://api.pillowcase.su/api/download/7d462aabbf4c761c65e4da40f04db2d3\", \"size\": \"1.22 MB\", \"duration\": 66.05}", "aliases": ["Chakras", "<PERSON><PERSON>", "The Chakra", "Good Morning"], "size": "1.22 MB"}, {"id": "i-love-it-141", "name": "I Love It (Remix) [V3]", "artists": ["<PERSON>", "The-Dream"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: I Love It (Remix) 09.17.18 [<PERSON><PERSON><PERSON>]\n<PERSON><PERSON><PERSON>'s vocals start at 0:43s and end at 1:35s. Includes The-<PERSON> vocals. <PERSON> vocals. Put up for sale by <PERSON> on TheSource.", "length": "125.54", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/93900df8888d16b54d9964b36683117f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/93900df8888d16b54d9964b36683117f\", \"key\": \"I Love It (Remix)\", \"title\": \"I Love It (Remix) [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON> & The-Dream) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"Selah\", \"The Chakra\", \"Good Morning\"], \"description\": \"OG Filename: I Love It (Remix) 09.17.18 [<PERSON><PERSON><PERSON>]\\n<PERSON><PERSON><PERSON>'s vocals start at 0:43s and end at 1:35s. Includes The<PERSON><PERSON> vocals. No Kanye vocals. Put up for sale by <PERSON> on TheSource.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9223191ee6cd707728a585087cad70ce\", \"url\": \"https://api.pillowcase.su/api/download/9223191ee6cd707728a585087cad70ce\", \"size\": \"2.7 MB\", \"duration\": 125.54}", "aliases": ["Chakras", "<PERSON><PERSON>", "The Chakra", "Good Morning"], "size": "2.7 MB"}, {"id": "i-love-it-142", "name": "I Love It (Remix) [V4]", "artists": ["<PERSON>", "<PERSON><PERSON>", "The-Dream"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: I Love It (Remix) <PERSON><PERSON> and <PERSON><PERSON> Bounce [take .45] long acapella\n<PERSON><PERSON><PERSON>'s OG Freestyle with <PERSON><PERSON>, on a September 21, 2018 tracklist for Yandhi, made the same day. Some lines are similar to \"Chakras\" V3. The title \"I Love it (Remix)\" refers to <PERSON><PERSON><PERSON>'s vocals, not the <PERSON><PERSON><PERSON> song with <PERSON>. Leaked alongside a large amount of other Yandhi demos and instrumentals in October 2021, stems leaked in November 2021.", "length": "344.81", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/788e4fb2817f606e636035675e1659db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/788e4fb2817f606e636035675e1659db\", \"key\": \"I Love It (Remix)\", \"title\": \"I Love It (Remix) [V4]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON>lemons & The-Dream) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Chakra<PERSON>\", \"Selah\", \"The Chakra\", \"Good Morning\"], \"description\": \"OG Filename: I Love It (Remix) K<PERSON> and <PERSON><PERSON>ce [take .45] long acapella\\nKanye's OG Freestyle with <PERSON><PERSON>, on a September 21, 2018 tracklist for <PERSON><PERSON>, made the same day. Some lines are similar to \\\"Chakras\\\" V3. The title \\\"I Love it (Remix)\\\" refers to <PERSON><PERSON><PERSON>'s vocals, not the Kanye song with <PERSON> Pump. Leaked alongside a large amount of other Yandhi demos and instrumentals in October 2021, stems leaked in November 2021.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ce1c434641294a8e089d6b6a84769fa2\", \"url\": \"https://api.pillowcase.su/api/download/ce1c434641294a8e089d6b6a84769fa2\", \"size\": \"6.21 MB\", \"duration\": 344.81}", "aliases": ["Chakras", "<PERSON><PERSON>", "The Chakra", "Good Morning"], "size": "6.21 MB"}, {"id": "the-chakra", "name": "The Chakra [V5]", "artists": ["<PERSON>", "<PERSON><PERSON>", "The-Dream"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: The Chakra 09.22.18 [<PERSON><PERSON> and <PERSON><PERSON> extended freestyle]\nSimilar to the original \"I Love It (Remix)\" freestyle with different mixing.", "length": "344.86", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/5dbfea40c46242091f95d746c0def6fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5dbfea40c46242091f95d746c0def6fa\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V5]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON> & The-Dream) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Chakra<PERSON>\", \"Selah\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra 09.22.18 [K<PERSON> and <PERSON><PERSON> extended freestyle]\\nSimilar to the original \\\"I Love It (Remix)\\\" freestyle with different mixing.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"924dfa698a7d97024bed3a28f299f285\", \"url\": \"https://api.pillowcase.su/api/download/924dfa698a7d97024bed3a28f299f285\", \"size\": \"6.21 MB\", \"duration\": 344.86}", "aliases": ["Chakras", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "6.21 MB"}, {"id": "the-chakra-144", "name": "The Chakra [V6]", "artists": ["<PERSON>", "The-Dream"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: The Chakra [NEW VOCAL REFERENCE]\nHeard in the bleed on <PERSON><PERSON> Clem<PERSON> and BONG<PERSON> ByTheWay's vocal stem. Has a whole new vocal take, and new lyrics, with more mumble than later versions.", "length": "192.05", "fileDate": 16407360, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e306b9e9371f91708ee44d5f0ca54baa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e306b9e9371f91708ee44d5f0ca54baa\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V6]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON>) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra [NEW VOCAL REFERENCE]\\nHeard in the bleed on <PERSON><PERSON> Clemons and BONGO ByTheWay's vocal stem. Has a whole new vocal take, and new lyrics, with more mumble than later versions.\", \"date\": 16407360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6b5fc57f69add99e98b1be7ca741d280\", \"url\": \"https://api.pillowcase.su/api/download/6b5fc57f69add99e98b1be7ca741d280\", \"size\": \"3.76 MB\", \"duration\": 192.05}", "aliases": ["Chakras", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "chakras", "name": "Chakras [V7]", "artists": ["The-Dream", "<PERSON>", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: Chakras - 09.28.18 [Full 2nd Verse]\nSimilar to the version from 9.29 with slightly different mixing.", "length": "191.89", "fileDate": 16716672, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7b22ddfc99c4085ac81ff2066305109a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7b22ddfc99c4085ac81ff2066305109a\", \"key\": \"Chakras\", \"title\": \"Chakra<PERSON> [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Ant Clemons) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"The Chakra\", \"Selah\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: Chakras - 09.28.18 [Full 2nd Verse]\\nSimilar to the version from 9.29 with slightly different mixing.\", \"date\": 16716672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"abf9557eb5d7e9a07de09b5e3e179c4b\", \"url\": \"https://api.pillowcase.su/api/download/abf9557eb5d7e9a07de09b5e3e179c4b\", \"size\": \"3.76 MB\", \"duration\": 191.89}", "aliases": ["The Chakra", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "the-chakra-146", "name": "The Chakra [V8]", "artists": ["The-Dream", "<PERSON>", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: The Chakra [<PERSON><PERSON> For <PERSON>ing] 09.29.18\nOriginally assumed to be \"Law of Attraction\" but is confirmed to be an original version of \"Selah\". Same version as first heard in the 2018 Virgil promo. <PERSON><PERSON> and <PERSON><PERSON> are on the adlibs, not Desiigner. Track 10 on the SNL Tracklist. Later renamed \"Selah\" after Uganda but it's the same version of this track proven by <PERSON>'s preview.", "length": "192.08", "fileDate": 15724800, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/962d99a3e3fd5f09c7f99f775e967945", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/962d99a3e3fd5f09c7f99f775e967945\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>O ByTheWay & Ant Clemons) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra [Ref For Ending] 09.29.18\\nOriginally assumed to be \\\"Law of Attraction\\\" but is confirmed to be an original version of \\\"Selah\\\". Same version as first heard in the 2018 Virgil promo. Ant Clemons and <PERSON>go are on the adlibs, not Desiigner. Track 10 on the SNL Tracklist. Later renamed \\\"Selah\\\" after Uganda but it's the same version of this track proven by <PERSON>'s preview.\", \"date\": 15724800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a72cba037961054e6c566b30980e40a4\", \"url\": \"https://api.pillowcase.su/api/download/a72cba037961054e6c566b30980e40a4\", \"size\": \"3.76 MB\", \"duration\": 192.08}", "aliases": ["Chakras", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "the-chakra-147", "name": "⭐ The Chakra [V12]", "artists": ["The-Dream", "<PERSON>", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: The Chakra ADMIX.4 KW ARRANGEMENT\nLeaked October 24, 2021. Has better mix and slightly different outro. Another file of this version is also called \"HYPE AF\", but it's not clear if this was intended as an actual title.", "length": "191.65", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6558efec5a9a0d5205b8e3acdaac44fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6558efec5a9a0d5205b8e3acdaac44fa\", \"key\": \"The Chakra\", \"title\": \"\\u2b50 The Chakra [V12]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>O ByTheWay & Ant Clemons) (prod. <PERSON> Shaw & BoogzDaBeast)\", \"aliases\": [\"Chakra<PERSON>\", \"Selah\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra ADMIX.4 KW ARRANGEMENT\\nLeaked October 24, 2021. Has better mix and slightly different outro. Another file of this version is also called \\\"HYPE AF\\\", but it's not clear if this was intended as an actual title.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0941c5802d7cfadbd56debfa167b50e7\", \"url\": \"https://api.pillowcase.su/api/download/0941c5802d7cfadbd56debfa167b50e7\", \"size\": \"3.76 MB\", \"duration\": 191.65}", "aliases": ["Chakras", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "the-garden", "name": "The Garden [V7]", "artists": [], "producers": [], "notes": "OG Filename: IMG_1164\nA version of \"Garden\" that contains the voice note <PERSON><PERSON><PERSON> freestyle that is heard in the version of \"Garden\" with <PERSON>'s extended reference. Shares the same beat as the \"Pets\" demo. VC recording snippet leaked October 22, 2022.", "length": "141.49", "fileDate": 16888608, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c51c7d84c6b3e3ca9fea28d25ca91c00", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c51c7d84c6b3e3ca9fea28d25ca91c00\", \"key\": \"The Garden\", \"title\": \"The Garden [V7]\", \"aliases\": [\"Garden\"], \"description\": \"OG Filename: IMG_1164\\nA version of \\\"Garden\\\" that contains the voice note <PERSON><PERSON><PERSON> freestyle that is heard in the version of \\\"Garden\\\" with <PERSON>'s extended reference. Shares the same beat as the \\\"Pets\\\" demo. VC recording snippet leaked October 22, 2022.\", \"date\": 16888608, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3fde89112d7cac6586a2ff8e381e29e2\", \"url\": \"https://api.pillowcase.su/api/download/3fde89112d7cac6586a2ff8e381e29e2\", \"size\": \"1.82 MB\", \"duration\": 141.49}", "aliases": ["Garden"], "size": "1.82 MB"}, {"id": "the-garden-149", "name": "The Garden [V8]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: The Garden Ye Lyrics\nA very early version of \"Garden\" with <PERSON>'s extended voicemail where she's reciting her reference lyrics over Kany<PERSON> freestyle vocals. This is the same version put up for sale by Alek in early 2020. The leaked file is a stem edit with a later beat, however, the <PERSON> vocals are real.", "length": "181.39", "fileDate": 16087680, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7de7e6539d1d744f7174227ea7b97288", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7de7e6539d1d744f7174227ea7b97288\", \"key\": \"The Garden\", \"title\": \"The Garden [V8]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON><PERSON>)\", \"aliases\": [\"Garden\"], \"description\": \"OG Filename: The Garden Ye Lyrics\\nA very early version of \\\"Garden\\\" with <PERSON>'s extended voicemail where she's reciting her reference lyrics over Kanye freestyle vocals. This is the same version put up for sale by Alek in early 2020. The leaked file is a stem edit with a later beat, however, the <PERSON> vocals are real.\", \"date\": 16087680, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bbfa7b9a6a6eef801d88fbb4284b76e2\", \"url\": \"https://api.pillowcase.su/api/download/bbfa7b9a6a6eef801d88fbb4284b76e2\", \"size\": \"3.59 MB\", \"duration\": 181.39}", "aliases": ["Garden"], "size": "3.59 MB"}, {"id": "the-garden-150", "name": "The Garden [V9]", "artists": ["<PERSON>", "Ty Dolla $ign", "<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: The Garden 09.22.18 [TY and ANT]\nVersion of \"Garden\" featuring only verses from <PERSON>gn and <PERSON><PERSON>. Stems leaked in November 2021", "length": "180.86", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b35537adb3a0a9b676ff6317eaf2fb69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b35537adb3a0a9b676ff6317eaf2fb69\", \"key\": \"The Garden\", \"title\": \"The Garden [V9]\", \"artists\": \"(feat. <PERSON>, <PERSON>gn & <PERSON>)\", \"aliases\": [\"Garden\"], \"description\": \"OG Filename: The Garden 09.22.18 [TY and ANT]\\nVersion of \\\"Garden\\\" featuring only verses from <PERSON> $ign and <PERSON><PERSON>. Stems leaked in November 2021\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b662fa649ed4323d02d7df7430117cfa\", \"url\": \"https://api.pillowcase.su/api/download/b662fa649ed4323d02d7df7430117cfa\", \"size\": \"3.59 MB\", \"duration\": 180.86}", "aliases": ["Garden"], "size": "3.59 MB"}, {"id": "the-garden-151", "name": "⭐ The Garden [V10]", "artists": ["<PERSON>", "Ty Dolla $ign", "<PERSON><PERSON>", "The-Dream", "<PERSON><PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: The Garden 09.30.18 [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>] Add MD bass and pad\nIncludes new verses from <PERSON><PERSON><PERSON> and <PERSON><PERSON>, with no <PERSON><PERSON><PERSON> verse, alongside added production from MIKE DEAN.", "length": "230.82", "fileDate": 15725664, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/374985285300611365d58bfd1725f0c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/374985285300611365d58bfd1725f0c8\", \"key\": \"The Garden\", \"title\": \"\\u2b50 The Garden [V10]\", \"artists\": \"(feat. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>) (prod. <PERSON>KE DEAN)\", \"aliases\": [\"Garden\"], \"description\": \"OG Filename: The Garden 09.30.18 [T<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>] Add MD bass and pad\\nIncludes new verses from <PERSON><PERSON><PERSON> and <PERSON><PERSON>, with no <PERSON><PERSON><PERSON> verse, alongside added production from MIKE DEAN.\", \"date\": 15725664, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"947856fb684aef46772d83ee074f1052\", \"url\": \"https://api.pillowcase.su/api/download/947856fb684aef46772d83ee074f1052\", \"size\": \"4.38 MB\", \"duration\": 230.82}", "aliases": ["Garden"], "size": "4.38 MB"}, {"id": "jeet", "name": "Jeet [V2]", "artists": ["tizhimself"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> Iphone Ref solo\nEarly iPhone freestyle of \"Jeet\". \"Jeet\" means \"victory\" in Hindi, or can also be interpreted as slang for \"did you eat\".", "length": "101.84", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3eed8206d00f752ab57580cc1650ea91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3eed8206d00f752ab57580cc1650ea91\", \"key\": \"Jeet\", \"title\": \"Jeet [V2]\", \"artists\": \"(feat. tizhimself)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet - KW Iphone Ref solo\\nEarly iPhone freestyle of \\\"Jeet\\\". \\\"Jeet\\\" means \\\"victory\\\" in Hindi, or can also be interpreted as slang for \\\"did you eat\\\".\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8a0aeafe98b087b670658203483cb534\", \"url\": \"https://api.pillowcase.su/api/download/8a0aeafe98b087b670658203483cb534\", \"size\": \"2.32 MB\", \"duration\": 101.84}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "2.32 MB"}, {"id": "jeet-153", "name": "Jeet [V3]", "artists": ["tizhimself"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> Ref 1 - Singing\nLikely the first version of \"Jeet\" with <PERSON><PERSON> vocals.", "length": "177.4", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/65bf3b66aed03c2e7f00877202a1d051", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65bf3b66aed03c2e7f00877202a1d051\", \"key\": \"Jeet\", \"title\": \"Jeet [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. tizhimself)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet - Ant Ref 1 - Singing\\nLikely the first version of \\\"Jeet\\\" with <PERSON>t vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ed306e64d1d33a3f3e2740b588bd418\", \"url\": \"https://api.pillowcase.su/api/download/5ed306e64d1d33a3f3e2740b588bd418\", \"size\": \"3.53 MB\", \"duration\": 177.4}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.53 MB"}, {"id": "jeet-154", "name": "Jeet [V4]", "artists": ["tizhimself"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> ref - 117bpm\nAnother early version of \"Jeet\", lacking <PERSON><PERSON> vocals.", "length": "180.43", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/31c60d5321397c4d41f3cb845241df48", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/31c60d5321397c4d41f3cb845241df48\", \"key\": \"Jeet\", \"title\": \"Jeet [V4]\", \"artists\": \"(feat. tizhimself)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet - KW ref - 117bpm\\nAnother early version of \\\"Jeet\\\", lacking Ant vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eda4b4558c080eeed40798c898f4343f\", \"url\": \"https://api.pillowcase.su/api/download/eda4b4558c080eeed40798c898f4343f\", \"size\": \"3.58 MB\", \"duration\": 180.43}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.58 MB"}, {"id": "jeet-155", "name": "<PERSON>et [V5]", "artists": ["<PERSON><PERSON>", "tizhimself"], "producers": ["RONNY J", "FnZ"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON>\nAnother early version of \"Jeet\".", "length": "201.55", "fileDate": 16320096, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8460229bf97979ada7fac6a7c3ecff2e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8460229bf97979ada7fac6a7c3ecff2e\", \"key\": \"Jeet\", \"title\": \"Jeet [V5]\", \"artists\": \"(feat. <PERSON><PERSON> & tizhim<PERSON>) (prod. RONNY J & FnZ)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet - Ant Ref\\nAnother early version of \\\"Jeet\\\".\", \"date\": 16320096, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a86a9e1c21a7266f9e8d480b4a0c4d76\", \"url\": \"https://api.pillowcase.su/api/download/a86a9e1c21a7266f9e8d480b4a0c4d76\", \"size\": \"3.92 MB\", \"duration\": 201.55}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.92 MB"}, {"id": "jeet-156", "name": "Jeet [V6]", "artists": ["<PERSON><PERSON>", "tizhimself"], "producers": [], "notes": "OG Filename: Je<PERSON> - <PERSON><PERSON> ref KW ref ON TOP - 117bpm\nVersion of \"Jeet\" with <PERSON><PERSON> and <PERSON> vocals.", "length": "180.69", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f46b84c14dc7d65585aa9bad7bd9372b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f46b84c14dc7d65585aa9bad7bd9372b\", \"key\": \"Jeet\", \"title\": \"Jeet [V6]\", \"artists\": \"(feat. <PERSON><PERSON> & tizhim<PERSON>)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Je<PERSON> - <PERSON>t ref KW ref ON TOP - 117bpm\\nVersion of \\\"Jeet\\\" with <PERSON><PERSON> and <PERSON> vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4de494c1001afeeb05df16da2e710d61\", \"url\": \"https://api.pillowcase.su/api/download/4de494c1001afeeb05df16da2e710d61\", \"size\": \"3.59 MB\", \"duration\": 180.69}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.59 MB"}, {"id": "jeet-157", "name": "Jeet [V7]", "artists": ["tizhimself"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> (ant ref) <PERSON><PERSON> and <PERSON> <PERSON>e Ref 09.18.18\nHas <PERSON><PERSON> and <PERSON> reference vocals.", "length": "176.88", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/0c1430b66725e9627bf34726d6543bb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0c1430b66725e9627bf34726d6543bb0\", \"key\": \"Jeet\", \"title\": \"Jeet [V7]\", \"artists\": \"(ref. <PERSON><PERSON> & Really <PERSON>) (feat. tizhimself)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: <PERSON><PERSON> (ant ref) K<PERSON> and Really Doe Ref 09.18.18\\nHas <PERSON><PERSON> and <PERSON> reference vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"79363e6e050b6a9c509330d95e559b2d\", \"url\": \"https://api.pillowcase.su/api/download/79363e6e050b6a9c509330d95e559b2d\", \"size\": \"3.52 MB\", \"duration\": 176.88}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.52 MB"}, {"id": "leave-it-in", "name": "Leave It In (Jeet) [V8]", "artists": ["<PERSON><PERSON>", "XXXTENTACION", "tizhimself"], "producers": ["RONNY J"], "notes": "OG Filename: LEAVE IT IN (Jeet) 09.22.18 [add xxxtentacion]\nFirst known version of \"Jeet\" with XXXTENTACION's verse.", "length": "180.43", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2fe6a8658c558a7c0264a02528ef68b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2fe6a8658c558a7c0264a02528ef68b6\", \"key\": \"Leave It In (Jeet)\", \"title\": \"Leave It In (Jeet) [V8]\", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> & tizhimself) (prod. RONNY J)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: LEAVE IT IN (Jeet) 09.22.18 [add xxxtentacion]\\nFirst known version of \\\"Jeet\\\" with XXXTENTACION's verse.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4539bde00d0d115c2853aa423f7c77c3\", \"url\": \"https://api.pillowcase.su/api/download/4539bde00d0d115c2853aa423f7c77c3\", \"size\": \"3.58 MB\", \"duration\": 180.43}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.58 MB"}, {"id": "jeet-159", "name": "Jeet [V9]", "artists": ["tizhimself"], "producers": [], "notes": "OG Filename: Jeet 09.22.18 [mute 3rd verse and louder]\nHas no XXXTENTACION's verse.", "length": "180.43", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3ffef0409f9c7dd44a4ae388b243233e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ffef0409f9c7dd44a4ae388b243233e\", \"key\": \"Jeet\", \"title\": \"Jeet [V9]\", \"artists\": \"(feat. tizhimself)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet 09.22.18 [mute 3rd verse and louder]\\nHas no XXXTENTACION's verse.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ab05104fc0435fdb20367f883fd53f75\", \"url\": \"https://api.pillowcase.su/api/download/ab05104fc0435fdb20367f883fd53f75\", \"size\": \"3.58 MB\", \"duration\": 180.43}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.58 MB"}, {"id": "jeet-160", "name": "Jeet [V10]", "artists": ["Ty Dolla $ign", "XXXTENTACION", "<PERSON><PERSON>", "tizhimself"], "producers": [], "notes": "OG Filename: Jeet 09.22.18 [TY$]\nLikely one of the first versions with <PERSON>ign vocals.", "length": "180.43", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1c901c2705c0042915b290f35dbdda2e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c901c2705c0042915b290f35dbdda2e\", \"key\": \"Jeet\", \"title\": \"Jeet [V10]\", \"artists\": \"(feat. <PERSON> $ign, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> & tizhimself)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet 09.22.18 [TY$]\\nLikely one of the first versions with <PERSON> <PERSON><PERSON> $ign vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c40500af0c9c1a6ac7fceecc64172d18\", \"url\": \"https://api.pillowcase.su/api/download/c40500af0c9c1a6ac7fceecc64172d18\", \"size\": \"3.58 MB\", \"duration\": 180.43}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.58 MB"}, {"id": "jeet-161", "name": "Jeet [V11]", "artists": ["Ty Dolla $ign", "XXXTENTACION", "<PERSON><PERSON>", "tizhimself"], "producers": [], "notes": "OG Filename: Jeet 09.22.18 [TY$] UPDATED\nHas louder Ty vocals.", "length": "180.43", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8ccdecccd06d3a2d2daa8290136aebec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ccdecccd06d3a2d2daa8290136aebec\", \"key\": \"Jeet\", \"title\": \"Jeet [V11]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> & tizhim<PERSON>)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: Jeet 09.22.18 [TY$] UPDATED\\nHas louder Ty vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"faf40d356bbfbf1c8b139e59eec1864c\", \"url\": \"https://api.pillowcase.su/api/download/faf40d356bbfbf1c8b139e59eec1864c\", \"size\": \"3.58 MB\", \"duration\": 180.43}", "aliases": ["Everything We Need", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.58 MB"}, {"id": "we-begin", "name": "We Begin [V12]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "XXXTENTACION", "tizhimself"], "producers": ["RONNY J"], "notes": "OG Filename: We Begin 09.22.18 [TY$ Ant KW edits]\nVersion of \"Jeet\" that includes <PERSON>ign background vocals.", "length": "151.72", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ed70309e8e1ba64211560aa1abf88929", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ed70309e8e1ba64211560aa1abf88929\", \"key\": \"We Begin\", \"title\": \"We Begin [V12]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> & tizhimself) (prod. RONNY J)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"Jeet\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: We Begin 09.22.18 [TY$ Ant KW edits]\\nVersion of \\\"Jeet\\\" that includes <PERSON> $ign background vocals.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"42cec34122d661636dc4d6add0ed0790\", \"url\": \"https://api.pillowcase.su/api/download/42cec34122d661636dc4d6add0ed0790\", \"size\": \"3.12 MB\", \"duration\": 151.72}", "aliases": ["Everything We Need", "The Storm", "<PERSON>et", "XXX", "We Begin After The Storm Inside"], "size": "3.12 MB"}, {"id": "the-storm", "name": "The Storm [V13]", "artists": ["<PERSON>", "XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": ["RONNY J"], "notes": "OG Filename: We Begin 09.24.18 [X verse <PERSON><PERSON>]\nVery early unmastered version with <PERSON> that is lacking a lot of the effects and instruments from the other versions. Was intended for SNL <PERSON> as seen from a video of someone listening to the album being mixed on the night of SNL. \"The Storm\" was written as \"Triple X\" and was the first track on the SNL tracklist.", "length": "181.03", "fileDate": 15738624, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/774171bcf11cb2695afbbeec57572e57", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/774171bcf11cb2695afbbeec57572e57\", \"key\": \"The Storm\", \"title\": \"The Storm [V13]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> & tizhimself) (prod. RONNY J)\", \"aliases\": [\"Everything We Need\", \"Jeet\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: We Begin 09.24.18 [X verse <PERSON><PERSON> Verse]\\nVery early unmastered version with <PERSON> that is lacking a lot of the effects and instruments from the other versions. Was intended for SNL Yandhi as seen from a video of someone listening to the album being mixed on the night of SNL. \\\"The Storm\\\" was written as \\\"Triple X\\\" and was the first track on the SNL tracklist.\", \"date\": 15738624, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fc7b6d8ed6ba9b29bf0699f48478ad29\", \"url\": \"https://api.pillowcase.su/api/download/fc7b6d8ed6ba9b29bf0699f48478ad29\", \"size\": \"3.59 MB\", \"duration\": 181.03}", "aliases": ["Everything We Need", "<PERSON>et", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "3.59 MB"}, {"id": "xxx", "name": "XXX [V14]", "artists": ["<PERSON>", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": ["RONNY J"], "notes": "OG Filename: XXX 09.25.18 [Open Verses]\nOpen verse version of \"The Storm\" / \"XXX\". Lacks XXXTENTACTION's verse.", "length": "215.77", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6af82daea07ca3355c674f40208a3545", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6af82daea07ca3355c674f40208a3545\", \"key\": \"XXX\", \"title\": \"XXX [V14]\", \"artists\": \"(feat. <PERSON>, <PERSON>ign, <PERSON><PERSON> & tizhim<PERSON>) (prod. RONNY J)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"Jeet\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: XXX 09.25.18 [Open Verses]\\nOpen verse version of \\\"The Storm\\\" / \\\"XXX\\\". <PERSON><PERSON> XXXTENTACTION's verse.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f0968fa924a9e30a507d6c2a416d7313\", \"url\": \"https://api.pillowcase.su/api/download/f0968fa924a9e30a507d6c2a416d7313\", \"size\": \"4.15 MB\", \"duration\": 215.77}", "aliases": ["Everything We Need", "The Storm", "We Begin", "<PERSON>et", "We Begin After The Storm Inside"], "size": "4.15 MB"}, {"id": "xxx-165", "name": "⭐ XXX (The Storm) [V15]", "artists": ["<PERSON>", "XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": ["RONNY J", "MIKE DEAN"], "notes": "OG Filename: XXX (the storm) mike add\nSNL version that features more finished production and mixing than V3. There is slight autotune over <PERSON><PERSON><PERSON>'s vocals and there is a different song structure. Includes new production from MIKE DEAN.", "length": "199.04", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/22d5955135780cbdbfbb7692e9bf0cd4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/22d5955135780cbdbfbb7692e9bf0cd4\", \"key\": \"XXX (The Storm)\", \"title\": \"\\u2b50 XXX (The Storm) [V15]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gn, <PERSON><PERSON> & tizhimself) (prod. RONNY J & MIKE DEAN)\", \"aliases\": [\"Everything We Need\", \"We Begin\", \"Jeet\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: XXX (the storm) mike add\\nSNL version that features more finished production and mixing than V3. There is slight autotune over <PERSON><PERSON><PERSON>'s vocals and there is a different song structure. Includes new production from MIKE DEAN.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5cc4c1e31524a6a7bb3ba4ca593d511a\", \"url\": \"https://api.pillowcase.su/api/download/5cc4c1e31524a6a7bb3ba4ca593d511a\", \"size\": \"3.88 MB\", \"duration\": 199.04}", "aliases": ["Everything We Need", "We Begin", "<PERSON>et", "We Begin After The Storm Inside"], "size": "3.88 MB"}, {"id": "xxx-166", "name": "XXX [V16]", "artists": ["<PERSON>", "XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself", "<PERSON>"], "producers": ["RONNY J", "MIKE DEAN"], "notes": "OG Filename: <PERSON><PERSON><PERSON> Chords in final CH\nVersion of \"The Storm\" with added outro production from <PERSON>. Unknown exactly when made.", "length": "198.86", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b8b657a22c3f350b9781a72dbb8ba115", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b8b657a22c3f350b9781a72dbb8ba115\", \"key\": \"XXX\", \"title\": \"XXX [V16]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gn, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>N<PERSON> J & MIKE DEAN)\", \"aliases\": [\"Everything We Need\", \"We Begin\", \"Jeet\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: XXX Dawson Chords in final CH\\nVersion of \\\"The Storm\\\" with added outro production from <PERSON>. Unknown exactly when made.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"05ba5cf0cef4b6bcc39c335b10d343ed\", \"url\": \"https://api.pillowcase.su/api/download/05ba5cf0cef4b6bcc39c335b10d343ed\", \"size\": \"3.88 MB\", \"duration\": 198.86}", "aliases": ["Everything We Need", "We Begin", "<PERSON>et", "We Begin After The Storm Inside"], "size": "3.88 MB"}, {"id": "we-got-love", "name": "We Got Love [V4]", "artists": ["<PERSON><PERSON>", "Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "MIKE DEAN"], "notes": "OG Filename: 130918 We Got Love\nScrapped <PERSON><PERSON> K.T.S.E. song played live by <PERSON><PERSON><PERSON> and <PERSON><PERSON> on SNL, a whiteboard tracklist revealed it was intended for <PERSON><PERSON>. This version uses a slightly more complex instrumental, lacks the intro and includes a synth outro under the <PERSON><PERSON> speech. Later received 2 commercial releases, both without <PERSON><PERSON><PERSON>.", "length": "231.11", "fileDate": 16102368, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ec28393346705bac9e667b1341f16f12", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ec28393346705bac9e667b1341f16f12\", \"key\": \"We Got Love\", \"title\": \"We Got Love [V4]\", \"artists\": \"(feat. <PERSON><PERSON> & Ms. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, E.<PERSON> & <PERSON>)\", \"description\": \"OG Filename: 130918 We Got Love\\nScrapped <PERSON><PERSON> K.T.S.E. song played live by <PERSON><PERSON><PERSON> and <PERSON><PERSON> on SNL, a whiteboard tracklist revealed it was intended for <PERSON><PERSON>. This version uses a slightly more complex instrumental, lacks the intro and includes a synth outro under the <PERSON><PERSON> speech. Later received 2 commercial releases, both without <PERSON><PERSON><PERSON>.\", \"date\": 16102368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7393bef0b57390fb1e99bad2a854235e\", \"url\": \"https://api.pillowcase.su/api/download/7393bef0b57390fb1e99bad2a854235e\", \"size\": \"4.39 MB\", \"duration\": 231.11}", "aliases": [], "size": "4.39 MB"}, {"id": "we-got-love-168", "name": "We Got Love [V5]", "artists": ["<PERSON><PERSON>", "Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "MIKE DEAN"], "notes": "Version of \"We Got Love\" similar to the previous version, except the full intro is there and there are less vocal effects on <PERSON><PERSON><PERSON>'s verse.", "length": "240.73", "fileDate": 16330464, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/293ade7ff5d7dbe4895ffdf4c1fceac6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/293ade7ff5d7dbe4895ffdf4c1fceac6\", \"key\": \"We Got Love\", \"title\": \"We Got Love [V5]\", \"artists\": \"(feat. <PERSON><PERSON> & Ms. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, E.<PERSON> & <PERSON>)\", \"description\": \"Version of \\\"We Got Love\\\" similar to the previous version, except the full intro is there and there are less vocal effects on <PERSON><PERSON><PERSON>'s verse.\", \"date\": 16330464, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2991de1f4c99915f94e995c862f64580\", \"url\": \"https://api.pillowcase.su/api/download/2991de1f4c99915f94e995c862f64580\", \"size\": \"4.54 MB\", \"duration\": 240.73}", "aliases": [], "size": "4.54 MB"}, {"id": "we-got-love-169", "name": "We Got Love [V14]", "artists": ["<PERSON><PERSON>", "Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "MIKE DEAN"], "notes": "OG Filename: WE GOT LOVE MIKE DEAN JESS MIX 9\nVersion of the song that is similar to the SNL version except there is no synth on the outro, a simpler drum loop and additional strings and organ from E.VAX. Leaked alongside many other Yandhi demos and instrumentals.", "length": "226.32", "fileDate": 16350336, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3247a9e7e8347fcf8d3a699c274cc47a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3247a9e7e8347fcf8d3a699c274cc47a\", \"key\": \"We Got Love\", \"title\": \"We Got Love [V14]\", \"artists\": \"(feat. <PERSON><PERSON> & Ms. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, E.<PERSON> & <PERSON>KE DEAN)\", \"description\": \"OG Filename: WE GOT LOVE MIKE DEAN JESS MIX 9\\nVersion of the song that is similar to the SNL version except there is no synth on the outro, a simpler drum loop and additional strings and organ from E.VAX. Leaked alongside many other Yandhi demos and instrumentals.\", \"date\": 16350336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a9479c2429be459a522d3ae72a3d0786\", \"url\": \"https://api.pillowcase.su/api/download/a9479c2429be459a522d3ae72a3d0786\", \"size\": \"4.31 MB\", \"duration\": 226.32}", "aliases": [], "size": "4.31 MB"}, {"id": "we-got-love-170", "name": "We Got Love [V19]", "artists": ["<PERSON><PERSON>", "Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "MIKE DEAN"], "notes": "OG Filename: WE GOT LOVE MIKE DEAN JESS MIX 14\nAnother mix of the song. Similar to V8, but has no claps at 1:11\nand 3 extra seconds of silence at the end. Found within the \"Bye Bye Baby\" Protools session.", "length": "241.52", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8ddf787758cdd0f33420484c45466b27", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ddf787758cdd0f33420484c45466b27\", \"key\": \"We Got Love\", \"title\": \"We Got Love [V19]\", \"artists\": \"(feat. <PERSON><PERSON> & Ms. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, E.<PERSON> & <PERSON>)\", \"description\": \"OG Filename: WE GOT LOVE MIKE DEAN JESS MIX 14\\nAnother mix of the song. Similar to V8, but has no claps at 1:11\\nand 3 extra seconds of silence at the end. Found within the \\\"Bye Bye Baby\\\" Protools session.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f2ee2bc88e589e10a8926d1d2ca60ce7\", \"url\": \"https://api.pillowcase.su/api/download/f2ee2bc88e589e10a8926d1d2ca60ce7\", \"size\": \"4.56 MB\", \"duration\": 241.52}", "aliases": [], "size": "4.56 MB"}, {"id": "we-got-love-171", "name": "We Got Love [V22]", "artists": ["<PERSON><PERSON>", "Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "MIKE DEAN"], "notes": "OG Filename: WE GOT LOVE MIKE JESS MIX 16-1\nSNL version that was played in <PERSON>nye's Uganda live streams, using the simplified K.T.S.E. version of the instrumental. Still includes the synth outro but has no drum break intro.", "length": "238.45", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/dde8aacf505335ea3213bc06c46639c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dde8aacf505335ea3213bc06c46639c8\", \"key\": \"We Got Love\", \"title\": \"We Got Love [V22]\", \"artists\": \"(feat. <PERSON><PERSON> & Ms. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, E.<PERSON> & <PERSON>)\", \"description\": \"OG Filename: WE GOT LOVE MIKE JESS MIX 16-1\\nSNL version that was played in Kanye's Uganda live streams, using the simplified K.T.S.E. version of the instrumental. Still includes the synth outro but has no drum break intro.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d89253efb6733abf1e71cce8dc7ef2fd\", \"url\": \"https://api.pillowcase.su/api/download/d89253efb6733abf1e71cce8dc7ef2fd\", \"size\": \"4.51 MB\", \"duration\": 238.45}", "aliases": [], "size": "4.51 MB"}, {"id": "-172", "name": "Consequence - ??? [V2]", "artists": ["Kanye West"], "producers": [], "notes": "Later version of \"Chance Liked 9\" with <PERSON><PERSON><PERSON> vocals.The song is a part of LQ Good Ass Job/early Yandhi recordings accidentally imported into the \"New Body\" session. Played on Consequence's Instagram livestream celebrating JESUS IS KING reaching number 1 on the charts. Second snippet is from the 5+ hour VC recording.", "length": "25.73", "fileDate": 15723936, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6126b428a10fce93e748d916824604dd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6126b428a10fce93e748d916824604dd\", \"key\": \"???\", \"title\": \"Consequence - ??? [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Back In This Bitch Like\"], \"description\": \"Later version of \\\"Chance Liked 9\\\" with Consequence vocals.The song is a part of LQ Good Ass Job/early Yandhi recordings accidentally imported into the \\\"New Body\\\" session. Played on Consequence's Instagram livestream celebrating JESUS IS KING reaching number 1 on the charts. Second snippet is from the 5+ hour VC recording.\", \"date\": 15723936, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ae2745ee0010fe5249c106f375789c84\", \"url\": \"https://api.pillowcase.su/api/download/ae2745ee0010fe5249c106f375789c84\", \"size\": \"1.1 MB\", \"duration\": 25.73}", "aliases": ["Back In This Bitch Like"], "size": "1.1 MB"}, {"id": "fuck-the-internet", "name": "<PERSON> - Fuck the Internet [V5]", "artists": ["Kanye West"], "producers": ["Da<PERSON>", "Kanye West", "RONNY J"], "notes": "OG Filename: Fuck the internet_Post_YE_RJ\nVersion with additional RONNY J production.", "length": "253.05", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7c0f136c938949cf96352389d82143fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c0f136c938949cf96352389d82143fd\", \"key\": \"Fuck the Internet\", \"title\": \"<PERSON> Malone - Fuck the Internet [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON> & RONNY J)\", \"aliases\": [\"Instalove\", \"Internet\"], \"description\": \"OG Filename: Fuck the internet_Post_YE_RJ\\nVersion with additional RONNY J production.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6df0185a54b98ccd6c2d0cb84e7a891\", \"url\": \"https://api.pillowcase.su/api/download/d6df0185a54b98ccd6c2d0cb84e7a891\", \"size\": \"4.74 MB\", \"duration\": 253.05}", "aliases": ["Instalove", "Internet"], "size": "4.74 MB"}, {"id": "space-x-alien-174", "name": "Space X / Alien [V15]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Space X - Alien [KW ref] 10.04.18\nVersion of \"Alien\" with a new freestyle verse, features lyrics about <PERSON>, the Waco, Texas Branch Davidians incident, and the Challenger explosion being faked. Reused on the iTunes DONDA tracklist <PERSON><PERSON><PERSON> posted, and the DONDA visual album.", "length": "210.48", "fileDate": 15708384, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/887b2c4bdd1e930550fc86a0294b941f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/887b2c4bdd1e930550fc86a0294b941f\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V15]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Space X - Alien [KW ref] 10.04.18\\nVersion of \\\"Alien\\\" with a new freestyle verse, features lyrics about <PERSON>, the Waco, Texas Branch Davidians incident, and the Challenger explosion being faked. Reused on the iTunes DONDA tracklist <PERSON><PERSON><PERSON> posted, and the DONDA visual album.\", \"date\": 15708384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0b96d0bbd81379a7b5455cde0e0c1753\", \"url\": \"https://api.pillowcase.su/api/download/0b96d0bbd81379a7b5455cde0e0c1753\", \"size\": \"4.06 MB\", \"duration\": 210.48}", "aliases": [], "size": "4.06 MB"}, {"id": "space-x-alien-175", "name": "Space X / Alien [V16]", "artists": [], "producers": [], "notes": "OG Filename: Space X - Alien [open verse] 10.04.18\nSame as the previous version but lacking <PERSON><PERSON><PERSON> vocals.", "length": "210.46", "fileDate": 16345152, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/201c8dcb18f1e34fcbbf063bd226cd0e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/201c8dcb18f1e34fcbbf063bd226cd0e\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V16]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: Space X - Alien [open verse] 10.04.18\\nSame as the previous version but lacking <PERSON><PERSON><PERSON> vocals.\", \"date\": 16345152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1bea3a99a8d4fab7bb25f0b3d0d668e2\", \"url\": \"https://api.pillowcase.su/api/download/1bea3a99a8d4fab7bb25f0b3d0d668e2\", \"size\": \"4.06 MB\", \"duration\": 210.46}", "aliases": [], "size": "4.06 MB"}, {"id": "alien-176", "name": "Alien [V17]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: bump alien ye ref!\nBump J \"Alien\" reference track. Vocals are panned all the way to the left. The session with his vocals is dated October 6th, 2018. On 10.06.18, <PERSON><PERSON><PERSON> planned to finish \"Alien\" by recording over <PERSON><PERSON> <PERSON>'s ref, as there is a KW audio track in the sessions, with just audio of a engineer giving directions to another engineer about how <PERSON><PERSON><PERSON> likes to be recorded. However, he never recorded over it.", "length": "210.48", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f9b35b2784eeeb829f8adbfcd91997bc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f9b35b2784eeeb829f8adbfcd91997bc\", \"key\": \"Alien\", \"title\": \"Alien [V17]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: bump alien ye ref!\\nBump J \\\"Alien\\\" reference track. Vocals are panned all the way to the left. The session with his vocals is dated October 6th, 2018. On 10.06.18, <PERSON><PERSON><PERSON> planned to finish \\\"Alien\\\" by recording over <PERSON><PERSON> <PERSON>'s ref, as there is a KW audio track in the sessions, with just audio of a engineer giving directions to another engineer about how <PERSON><PERSON><PERSON> likes to be recorded. However, he never recorded over it.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0f3b1a3ec32aeca85d4948b8cc19407f\", \"url\": \"https://api.pillowcase.su/api/download/0f3b1a3ec32aeca85d4948b8cc19407f\", \"size\": \"4.06 MB\", \"duration\": 210.48}", "aliases": ["Space X"], "size": "4.06 MB"}, {"id": "space-x-alien-177", "name": "Space X / Alien [V18]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Space X - Alien [add intro] 10.07.18\nSimilar to earlier versions , with a new text-to-speech intro.", "length": "222.55", "fileDate": 16359840, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b45697e5c2b3846926f2395021c0656d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b45697e5c2b3846926f2395021c0656d\", \"key\": \"Space X / Alien\", \"title\": \"Space X / Alien [V18]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Space X - Alien [add intro] 10.07.18\\nSimilar to earlier versions , with a new text-to-speech intro.\", \"date\": 16359840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"947c0c89daeed2cac9f031b208cf0a20\", \"url\": \"https://api.pillowcase.su/api/download/947c0c89daeed2cac9f031b208cf0a20\", \"size\": \"4.25 MB\", \"duration\": 222.55}", "aliases": [], "size": "4.25 MB"}, {"id": "alien-178", "name": "Alien [V19]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Alien 10.08.18 [ends after second verse]\nVersion of \"Alien\" from October 8th, 2018. Cuts the song off at the end of the second verse. Was used on a December tracklist.", "length": "129.41", "fileDate": 16833312, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/eaf3602fc8b9d0081aaeaf8782e5dea4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eaf3602fc8b9d0081aaeaf8782e5dea4\", \"key\": \"Alien\", \"title\": \"Alien [V19]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Alien 10.08.18 [ends after second verse]\\nVersion of \\\"Alien\\\" from October 8th, 2018. Cuts the song off at the end of the second verse. Was used on a December tracklist.\", \"date\": 16833312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5df89d2589a9fe62dba10c5cc4ee930e\", \"url\": \"https://api.pillowcase.su/api/download/5df89d2589a9fe62dba10c5cc4ee930e\", \"size\": \"2.76 MB\", \"duration\": 129.41}", "aliases": ["Space X"], "size": "2.76 MB"}, {"id": "alien-179", "name": "Alien [V20]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Alien 10.08.18 [<PERSON><PERSON><PERSON> Ref] HA\nCyHi providing a reference verse for <PERSON><PERSON>e. Snippet posted by <PERSON><PERSON><PERSON> then later on TheSource. \"Includes the <PERSON><PERSON> Clem<PERSON> hook vocals. No Kanye vocals\".", "length": "10.42", "fileDate": 15751584, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8b56dcd187261b8e1b2af4580825a51d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b56dcd187261b8e1b2af4580825a51d\", \"key\": \"Alien\", \"title\": \"Alien [V20]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Alien 10.08.18 [CyHi Ref] HA\\nCyHi providing a reference verse for Kanye. Snippet posted by Pluto then later on TheSource. \\\"Includes the <PERSON><PERSON> hook vocals. No Ka<PERSON> vocals\\\".\", \"date\": 15751584, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d8710ab641ea56b630602ce6923dd82d\", \"url\": \"https://api.pillowcase.su/api/download/d8710ab641ea56b630602ce6923dd82d\", \"size\": \"858 kB\", \"duration\": 10.42}", "aliases": ["Space X"], "size": "858 kB"}, {"id": "alien-180", "name": "Alien [V20]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Alien 10.08.18 [<PERSON><PERSON><PERSON> Ref] HA\nCyHi providing a reference verse for <PERSON><PERSON>e. Snippet posted by <PERSON><PERSON><PERSON> then later on TheSource. \"Includes the <PERSON><PERSON> Clem<PERSON> hook vocals. No Kanye vocals\".", "length": "6.79", "fileDate": 15751584, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8aabfd9dd8e37eac64046fe9754ba54d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8aabfd9dd8e37eac64046fe9754ba54d\", \"key\": \"Alien\", \"title\": \"Alien [V20]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Alien 10.08.18 [CyHi Ref] HA\\nCyHi providing a reference verse for Kanye. Snippet posted by Plut<PERSON> then later on TheSource. \\\"Includes the <PERSON><PERSON> hook vocals. No <PERSON><PERSON><PERSON> vocals\\\".\", \"date\": 15751584, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"321fba33fbc3fd7423854bfaf24f84ff\", \"url\": \"https://api.pillowcase.su/api/download/321fba33fbc3fd7423854bfaf24f84ff\", \"size\": \"800 kB\", \"duration\": 6.79}", "aliases": ["Space X"], "size": "800 kB"}, {"id": "alien-181", "name": "Alien [V21]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Alien 10.08.18 [TONY WILLIAMS demo]\nHas <PERSON> mumble vocals.", "length": "222.56", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/d6a2144bb29cc754f1e9d2ded68b10cc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d6a2144bb29cc754f1e9d2ded68b10cc\", \"key\": \"Alien\", \"title\": \"Alien [V21]\", \"artists\": \"(ref. The WRLDFMS <PERSON>) (feat. <PERSON><PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Alien 10.08.18 [TONY WILLIAMS demo]\\nHas <PERSON> mumble vocals.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"16312b016bfa76d96cd65ea13b7b77b3\", \"url\": \"https://api.pillowcase.su/api/download/16312b016bfa76d96cd65ea13b7b77b3\", \"size\": \"4.25 MB\", \"duration\": 222.56}", "aliases": ["Space X"], "size": "4.25 MB"}, {"id": "alien-182", "name": "Alien [V22]", "artists": [], "producers": [], "notes": "OG Filename: Alien 10.15.18 [tony williams vox ref]\nHas finished <PERSON> vocals.", "length": "36.99", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/77a49f01c74f33f09685682de388b39c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/77a49f01c74f33f09685682de388b39c\", \"key\": \"Alien\", \"title\": \"Alien [V22]\", \"artists\": \"(ref. The WRLDFMS <PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Alien 10.15.18 [tony williams vox ref]\\nHas finished <PERSON> vocals.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"68a0c2da0266eb5d65f95de66e96e54a\", \"url\": \"https://api.pillowcase.su/api/download/68a0c2da0266eb5d65f95de66e96e54a\", \"size\": \"1.28 MB\", \"duration\": 36.99}", "aliases": ["Space X"], "size": "1.28 MB"}, {"id": "bye-bye-baby-183", "name": "Bye Bye Baby [V7]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["RONNY J", "E.VAX"], "notes": "OG Filename: BYE BYE BABY [<PERSON><PERSON><PERSON>] 10.01.18\n<PERSON><PERSON><PERSON> intro version of \"Bye Bye Baby\" with a rougher mix.", "length": "57.2", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/31fa5128bd76c475db09d39cbb912dbd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/31fa5128bd76c475db09d39cbb912dbd\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. RONNY J & E.VAX)\", \"description\": \"OG Filename: BYE BYE BABY [<PERSON><PERSON><PERSON>] 10.01.18\\n<PERSON><PERSON><PERSON> intro version of \\\"Bye Bye Baby\\\" with a rougher mix.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3f8c3de233b36d0cbcfbacbe4dccab08\", \"url\": \"https://api.pillowcase.su/api/download/3f8c3de233b36d0cbcfbacbe4dccab08\", \"size\": \"1.61 MB\", \"duration\": 57.2}", "aliases": [], "size": "1.61 MB"}, {"id": "bye-bye-baby-184", "name": "Bye Bye Baby [V8]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["RONNY J", "E.VAX"], "notes": "OG Filename: BYE BYE BABY [Evan Version KW short freestyle] 10.01.18\nVersion of \"Bye Bye Baby\" using the <PERSON> instrumental and <PERSON><PERSON><PERSON> intro but with cut-down <PERSON><PERSON><PERSON> vocals and a long open verse.", "length": "142.92", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ab096f05229c8b914b8e9ed96474fc6c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab096f05229c8b914b8e9ed96474fc6c\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. RONNY J & E.VAX)\", \"description\": \"OG Filename: BYE BYE BABY [Evan Version KW short freestyle] 10.01.18\\nVersion of \\\"Bye Bye Baby\\\" using the <PERSON> instrumental and <PERSON><PERSON><PERSON> intro but with cut-down <PERSON><PERSON><PERSON> vocals and a long open verse.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3ae14e43d78be3fbd998ad596df4b09f\", \"url\": \"https://api.pillowcase.su/api/download/3ae14e43d78be3fbd998ad596df4b09f\", \"size\": \"2.98 MB\", \"duration\": 142.92}", "aliases": [], "size": "2.98 MB"}, {"id": "bye-bye-baby-185", "name": "Bye Bye Baby [V9]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["RONNY J", "E.VAX"], "notes": "OG Filename: BYE BYE BABY [With In<PERSON> and Song - Full KW Ref] 10.04.18\nSong listed on Yandhi whiteboard. Leaked after a groupbuy. Track 3 on the SNL tracklist.", "length": "200.07", "fileDate": 15766272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/59cb1b1689e6b08462e1033a22409015", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/59cb1b1689e6b08462e1033a22409015\", \"key\": \"Bye Bye Baby\", \"title\": \"Bye Bye Baby [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. RONNY J & E.VAX)\", \"description\": \"OG Filename: BYE BYE BABY [With Intro and Song - Full KW Ref] 10.04.18\\nSong listed on Yandhi whiteboard. Leaked after a groupbuy. Track 3 on the SNL tracklist.\", \"date\": 15766272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f91f0340d703a2cd9fba6cb91b13448d\", \"url\": \"https://api.pillowcase.su/api/download/f91f0340d703a2cd9fba6cb91b13448d\", \"size\": \"3.89 MB\", \"duration\": 200.07}", "aliases": [], "size": "3.89 MB"}, {"id": "home-186", "name": "Home [V4]", "artists": [], "producers": ["RONNY J", "FnZ", "<PERSON>"], "notes": "OG Filenames: 10-23-18 Ant Clemons Home Ref 1 & \nHome - AntClemons.RonnyJ\nFirst known version of \"Home\", featuring production from RONNY J not found in later versions. Was used on a December tracklist. Leaked after being sold for $20.", "length": "153.18", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1ad0ccc790b671d4ad233f3793bd643a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ad0ccc790b671d4ad233f3793bd643a\", \"key\": \"Home\", \"title\": \"Home [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Follow The Light\"], \"description\": \"OG Filenames: 10-23-18 Ant Clemons Home Ref 1 & \\nHome - AntClemons.RonnyJ\\nFirst known version of \\\"Home\\\", featuring production from R<PERSON>N<PERSON> <PERSON> not found in later versions. Was used on a December tracklist. Leaked after being sold for $20.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0e8400036576cd145ea667b6a716fd28\", \"url\": \"https://api.pillowcase.su/api/download/0e8400036576cd145ea667b6a716fd28\", \"size\": \"3.14 MB\", \"duration\": 153.18}", "aliases": ["Follow The Light"], "size": "3.14 MB"}, {"id": "houston-we-got-a-problem", "name": "Houston We Got A Problem [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Houston We Got A Problem - AntClemons.JackRochon\nFirst version of \"Houston We Got A Problem\".", "length": "116.86", "fileDate": 17116704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/76542ca1396fbfc7d409652fed90d537", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76542ca1396fbfc7d409652fed90d537\", \"key\": \"Houston We Got A Problem\", \"title\": \"Houston We Got A Problem [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>)\", \"aliases\": [\"<PERSON>\", \"House Party\"], \"description\": \"OG Filename: Houston We Got A Problem - AntClemons.JackRochon\\nFirst version of \\\"Houston We Got A Problem\\\".\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9650481e8a199e38e02292cf6da22bc0\", \"url\": \"https://api.pillowcase.su/api/download/9650481e8a199e38e02292cf6da22bc0\", \"size\": \"2.56 MB\", \"duration\": 116.86}", "aliases": ["Houston", "House Party"], "size": "2.56 MB"}, {"id": "houston-we-got-a-problem-188", "name": "Houston We Got A Problem [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Houston We Got A Problem2 - AntClemons.JackRochon\nSecond version of \"Houston We Got A Problem\".", "length": "115.28", "fileDate": 17116704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3d8586b0eaf17e240d37d0ad7b24bfde", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3d8586b0eaf17e240d37d0ad7b24bfde\", \"key\": \"Houston We Got A Problem\", \"title\": \"Houston We Got A Problem [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>)\", \"aliases\": [\"<PERSON>\", \"House Party\"], \"description\": \"OG Filename: Houston We Got A Problem2 - AntClemons.JackRochon\\nSecond version of \\\"Houston We Got A Problem\\\".\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"da06ef36d5ba6849450ee7707b330c2b\", \"url\": \"https://api.pillowcase.su/api/download/da06ef36d5ba6849450ee7707b330c2b\", \"size\": \"2.54 MB\", \"duration\": 115.28}", "aliases": ["Houston", "House Party"], "size": "2.54 MB"}, {"id": "houston", "name": "Houston [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Houston V3 - <PERSON><PERSON><PERSON>\nHas different production and drums.", "length": "142.03", "fileDate": 17116704, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/acd9813a1baf9e093dd21444c96eea4f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/acd9813a1baf9e093dd21444c96eea4f\", \"key\": \"<PERSON>\", \"title\": \"Houston [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Houston We Got A Problem\", \"House Party\"], \"description\": \"OG Filename: Houston V3 - Ant.JackRo\\nHas different production and drums.\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0be6606c4463e2b203e7384d15c059b5\", \"url\": \"https://api.pillowcase.su/api/download/0be6606c4463e2b203e7384d15c059b5\", \"size\": \"2.96 MB\", \"duration\": 142.03}", "aliases": ["Houston We Got A Problem", "House Party"], "size": "2.96 MB"}, {"id": "house-party", "name": "House Party [V1]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>", "<PERSON>", "RONNY J"], "notes": "Original version of \"House Party\", found on dbree. Features a much shorter track length.", "length": "95.48", "fileDate": 16170624, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/24b7bc2a554dead83e2b9c1f99c1e7c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/24b7bc2a554dead83e2b9c1f99c1e7c8\", \"key\": \"House Party\", \"title\": \"House Party [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & R<PERSON><PERSON>)\", \"aliases\": [\"Houston We Have A Problem\", \"Me Too\"], \"description\": \"Original version of \\\"House Party\\\", found on dbree. Features a much shorter track length.\", \"date\": 16170624, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"94d3c65364fc48a533cd0325fb0afb95\", \"url\": \"https://api.pillowcase.su/api/download/94d3c65364fc48a533cd0325fb0afb95\", \"size\": \"2.22 MB\", \"duration\": 95.48}", "aliases": ["Houston We Have A Problem", "Me Too"], "size": "2.22 MB"}, {"id": "house-party-191", "name": "House Party [V2]", "artists": [], "producers": ["Kanye West", "CardoGotWings", "<PERSON>", "RONNY J"], "notes": "OG Filename: HOUSE PARTY v3 prod. kanye cardo jack RONNY J\nAnother version of \"House Party\" with a cut during the intro. Unknown when this is from.", "length": "149.05", "fileDate": 16633728, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/0555d183df75b02827c931da66f2ffdd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0555d183df75b02827c931da66f2ffdd\", \"key\": \"House Party\", \"title\": \"House Party [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> & R<PERSON><PERSON>)\", \"aliases\": [\"<PERSON> Too\"], \"description\": \"OG Filename: HOUSE PARTY v3 prod. kanye cardo jack RONNY J\\nAnother version of \\\"House Party\\\" with a cut during the intro. Unknown when this is from.\", \"date\": 16633728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ffa63654d71a277a4f76660b4865da3a\", \"url\": \"https://api.pillowcase.su/api/download/ffa63654d71a277a4f76660b4865da3a\", \"size\": \"3.08 MB\", \"duration\": 149.05}", "aliases": ["Me Too"], "size": "3.08 MB"}, {"id": "house-party-192", "name": "House Party [V3]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>", "<PERSON>", "RONNY J"], "notes": "Reference meant for <PERSON><PERSON><PERSON>. Basically 3 different songs stitched together. A version with <PERSON><PERSON><PERSON> vocals does not exist. First beat was given to <PERSON>bs<PERSON>state for \"Sound Off The Alarm\".", "length": "185.08", "fileDate": 16053120, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/fca986efda1a9f8ec6554b1c90710d2f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fca986efda1a9f8ec6554b1c90710d2f\", \"key\": \"House Party\", \"title\": \"House Party [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & R<PERSON><PERSON><PERSON>)\", \"aliases\": [\"Me Too\"], \"description\": \"Reference meant for <PERSON><PERSON><PERSON>. Basically 3 different songs stitched together. A version with <PERSON><PERSON><PERSON> vocals does not exist. First beat was given to <PERSON>bs<PERSON> Mindstate for \\\"Sound Off The Alarm\\\".\", \"date\": 16053120, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"482beb888683da579d6bf6b53fcf1de0\", \"url\": \"https://api.pillowcase.su/api/download/482beb888683da579d6bf6b53fcf1de0\", \"size\": \"3.65 MB\", \"duration\": 185.08}", "aliases": ["Me Too"], "size": "3.65 MB"}, {"id": "hurricane-193", "name": "⭐ Hurricane [V22]", "artists": ["<PERSON><PERSON>", "Big Sean"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "Alt mix from October 4th, bounced from the stems. Identical to the September 30th version other than some mixing and volume differences.", "length": "268.03", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b212b27b6e90b062c6e2b6452927b938", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b212b27b6e90b062c6e2b6452927b938\", \"key\": \"Hurricane\", \"title\": \"\\u2b50 Hurricane [V22]\", \"artists\": \"(feat. <PERSON><PERSON> & Big Sean) (prod. <PERSON><PERSON><PERSON>aBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"Alt mix from October 4th, bounced from the stems. Identical to the September 30th version other than some mixing and volume differences.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9a8932869fdc7212645f954c3b74af1b\", \"url\": \"https://api.pillowcase.su/api/download/9a8932869fdc7212645f954c3b74af1b\", \"size\": \"4.98 MB\", \"duration\": 268.03}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.98 MB"}, {"id": "hurricane-194", "name": "Hurricane [V23]", "artists": [], "producers": ["BoogzDaBeast", "Cashmere Cat", "<PERSON>", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane [<PERSON> and <PERSON><PERSON> KW vrs] 10.06.18\nCompletely different beat with <PERSON><PERSON><PERSON>'s verses vocoded. Sold and leaked as \"V2\". First previewed in the October 2018 <PERSON> promo <PERSON><PERSON><PERSON> put on his Twitter. Leaked in mono.", "length": "186.79", "fileDate": 15697152, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6602f3437c628d0c3745396d8cb37535", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6602f3437c628d0c3745396d8cb37535\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V23]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Cat, <PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane [<PERSON> and <PERSON><PERSON> KW vrs] 10.06.18\\nCompletely different beat with <PERSON><PERSON><PERSON>'s verses vocoded. Sold and leaked as \\\"V2\\\". First previewed in the October 2018 <PERSON> promo <PERSON> put on his Twitter. Leaked in mono.\", \"date\": 15697152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1427ed9b2aba8b6d4b75db48abde8a90\", \"url\": \"https://api.pillowcase.su/api/download/1427ed9b2aba8b6d4b75db48abde8a90\", \"size\": \"2.19 MB\", \"duration\": 186.79}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "2.19 MB"}, {"id": "hurricane-195", "name": "Hurricane [V24]", "artists": [], "producers": ["BoogzDaBeast", "Cashmere Cat", "<PERSON>", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane 10.08.18 [TONY WILLIAMS demo]\nVersion of \"Hurricane\" featuring <PERSON> on the Cashmere Cat beat.", "length": "124.84", "fileDate": 16524864, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/8f457505e2ce7b8b00d870b501d7f1d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8f457505e2ce7b8b00d870b501d7f1d2\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V24]\", \"artists\": \"(ref. The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Cashmere Cat, <PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane 10.08.18 [TONY WILLIAMS demo]\\nVersion of \\\"Hurricane\\\" featuring <PERSON> on the Cashmere Cat beat.\", \"date\": 16524864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a8c180f25756155424d45553af773e96\", \"url\": \"https://api.pillowcase.su/api/download/a8c180f25756155424d45553af773e96\", \"size\": \"2.69 MB\", \"duration\": 124.84}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "2.69 MB"}, {"id": "hurricane-196", "name": "Hurricane [V29]", "artists": ["Big Sean"], "producers": ["Cashmere Cat", "<PERSON> and the Lights", "BoogzDaBeast", "Nascent", "RONNY J"], "notes": "OG Filename: Hurricane [with big sean] 10.13.18\nSame as the solo Cashmere Cat and <PERSON> produced version, but with a Big Sean verse tacked on at the end. Leaked to promote the groupbuy for \"Hurricane\" with <PERSON> $ign and <PERSON> Thug.", "length": "253.51", "fileDate": 15759360, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/63b537cd9503936afeab2c047b26e876", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63b537cd9503936afeab2c047b26e876\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V29]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> Cat, <PERSON> and the Lights, BoogzDaBeast, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"OG Filename: Hurricane [with big sean] 10.13.18\\nSame as the solo Cashmere Cat and <PERSON> produced version, but with a Big Sean verse tacked on at the end. Leaked to promote the groupbuy for \\\"Hurricane\\\" with <PERSON>a $ign and <PERSON> Thug.\", \"date\": 15759360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e65d11983df1c19efd8d8a1171c897aa\", \"url\": \"https://api.pillowcase.su/api/download/e65d11983df1c19efd8d8a1171c897aa\", \"size\": \"4.75 MB\", \"duration\": 253.51}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.75 MB"}, {"id": "hurricane-197", "name": "Hurricane [V30]", "artists": ["Big Sean", "Ty Dolla $ign"], "producers": ["BoogzDaBeast", "Nascent", "RONNY J"], "notes": "This version has <PERSON><PERSON><PERSON> doing the first version of the hook. Definitely later than the prior Big Sean version and Ty Dolla $ign version due the the instrumental change.", "length": "260.24", "fileDate": 16606944, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/6a1920eb541d5981d1e53f15c4645dd5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6a1920eb541d5981d1e53f15c4645dd5\", \"key\": \"Hurricane\", \"title\": \"Hurricane [V30]\", \"artists\": \"(feat. <PERSON> & <PERSON> $ign) (prod. <PERSON><PERSON><PERSON><PERSON>, Nascent & RONNY J)\", \"aliases\": [\"Don't Let Me Down\", \"Hurricanes\", \"80 Degrees\"], \"description\": \"This version has <PERSON><PERSON><PERSON> doing the first version of the hook. Definitely later than the prior Big Sean version and Ty Dolla $ign version due the the instrumental change.\", \"date\": 16606944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6021fb027b83f632c7d916e305db4f40\", \"url\": \"https://api.pillowcase.su/api/download/6021fb027b83f632c7d916e305db4f40\", \"size\": \"4.86 MB\", \"duration\": 260.24}", "aliases": ["Don't Let Me Down", "Hurricanes", "80 Degrees"], "size": "4.86 MB"}, {"id": "last-name-198", "name": "Last Name [V16]", "artists": [], "producers": ["BoogzDaBeast", "E.VAX", "MIKE DEAN", "<PERSON> and the Lights"], "notes": "St<PERSON> bounce of \"Last Name,\" basically identical to the September 30th version other than slight mixing differences.", "length": "69.73", "fileDate": 16357248, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a4ba54033a01a5c6fb0a0d1e0ff6d857", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a4ba54033a01a5c6fb0a0d1e0ff6d857\", \"key\": \"Last Name\", \"title\": \"Last Name [V16]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, MIKE DEAN & Francis and the Lights)\", \"aliases\": [\"Slave Name\"], \"description\": \"Stem bounce of \\\"Last Name,\\\" basically identical to the September 30th version other than slight mixing differences.\", \"date\": 16357248, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"86704bea5b3f8fa34ed23813392b01e1\", \"url\": \"https://api.pillowcase.su/api/download/86704bea5b3f8fa34ed23813392b01e1\", \"size\": \"1.81 MB\", \"duration\": 69.73}", "aliases": ["Slave Name"], "size": "1.81 MB"}, {"id": "me-too", "name": "Me Too", "artists": [], "producers": ["RONNY J"], "notes": "OG Filenames: 10-23-18 <PERSON><PERSON> - <PERSON> Too Ref 1 &\n<PERSON> Too - AntClemons.<PERSON><PERSON><PERSON> \"Me Too\" reference track. Confirmed to be the original bounce for the third beat on \"House Party\" and not for the December 2018 track.", "length": "75.38", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/d84a7fe5478f68a1045e815fde76e955", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d84a7fe5478f68a1045e815fde76e955\", \"key\": \"<PERSON> Too\", \"title\": \"<PERSON> Too\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"House Party\"], \"description\": \"OG Filenames: 10-23-18 <PERSON><PERSON> - Me Too Ref 1 &\\nMe Too - AntClemons.RonnyJ\\nAnt C<PERSON>ons \\\"Me Too\\\" reference track. Confirmed to be the original bounce for the third beat on \\\"House Party\\\" and not for the December 2018 track.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9a891ab50a22abf6f36424e795d576dc\", \"url\": \"https://api.pillowcase.su/api/download/9a891ab50a22abf6f36424e795d576dc\", \"size\": \"1.9 MB\", \"duration\": 75.38}", "aliases": ["House Party"], "size": "1.9 MB"}, {"id": "me-too-200", "name": "Me Too", "artists": [], "producers": ["RONNY J"], "notes": "OG Filenames: 10-23-18 <PERSON><PERSON> - <PERSON> Too Ref 1 &\n<PERSON> Too - AntClemons.<PERSON><PERSON><PERSON> \"Me Too\" reference track. Confirmed to be the original bounce for the third beat on \"House Party\" and not for the December 2018 track.", "length": "75.38", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/d5797e2dd3d386806544b930b7506cdd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d5797e2dd3d386806544b930b7506cdd\", \"key\": \"<PERSON> Too\", \"title\": \"<PERSON> Too\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> <PERSON>)\", \"aliases\": [\"House Party\"], \"description\": \"OG Filenames: 10-23-18 <PERSON><PERSON> - Me Too Ref 1 &\\nMe Too - AntClemons.RonnyJ\\nAnt Clemons \\\"Me Too\\\" reference track. Confirmed to be the original bounce for the third beat on \\\"House Party\\\" and not for the December 2018 track.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9e234c9a83cf354f4bdf682fe63acbb5\", \"url\": \"https://api.pillowcase.su/api/download/9e234c9a83cf354f4bdf682fe63acbb5\", \"size\": \"1.9 MB\", \"duration\": 75.38}", "aliases": ["House Party"], "size": "1.9 MB"}, {"id": "new-body-201", "name": "New Body [V14]", "artists": ["Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [TY hook bass line <PERSON><PERSON> chopped vrs 3 CH] 10.01.18\nVersion of \"New Body\" from October 1st, 2018. Does not have <PERSON><PERSON>'s verse. Similar to the 10.04 Kanye x Ty version, without <PERSON>'s extra vocals at the end.", "length": "171.84", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/68372fb6a4dcf5a8ba664f682f548f27", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68372fb6a4dcf5a8ba664f682f548f27\", \"key\": \"New Body\", \"title\": \"New Body [V14]\", \"artists\": \"(feat. <PERSON>ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [TY hook bass line KW chopped vrs 3 CH] 10.01.18\\nVersion of \\\"New Body\\\" from October 1st, 2018. Does not have <PERSON><PERSON>'s verse. Similar to the 10.04 Kanye x Ty version, without <PERSON>'s extra vocals at the end.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7b4603cbcc6f92a9319bff8c3742a9df\", \"url\": \"https://api.pillowcase.su/api/download/7b4603cbcc6f92a9319bff8c3742a9df\", \"size\": \"3.44 MB\", \"duration\": 171.84}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.44 MB"}, {"id": "new-body-202", "name": "New Body [V15]", "artists": ["Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [OPEN VERSES] 10.04.18\nOpen verse version of \"New Body\". Likely the version sent to <PERSON><PERSON> after <PERSON> reached out to her.", "length": "171.84", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/37c706bb1c244f748294ef46748a37a8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37c706bb1c244f748294ef46748a37a8\", \"key\": \"New Body\", \"title\": \"New Body [V15]\", \"artists\": \"(feat. <PERSON>ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [OPEN VERSES] 10.04.18\\nOpen verse version of \\\"New Body\\\". Likely the version sent to <PERSON><PERSON> after <PERSON> reached out to her.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e84b0681d455b4e2355337156e5da368\", \"url\": \"https://api.pillowcase.su/api/download/e84b0681d455b4e2355337156e5da368\", \"size\": \"3.44 MB\", \"duration\": 171.84}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.44 MB"}, {"id": "new-body-203", "name": "New Body [V16]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [KW REF NM VRS 2] 10.04.18\nFeatures a further along <PERSON><PERSON><PERSON> verse, and a <PERSON><PERSON> verse. <PERSON><PERSON> stated that she recorded her verse \"within an hour\" after being contacted by <PERSON>.", "length": "171.84", "fileDate": 16357248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2cdc5daba5bb623ac5f06b52bcdd333e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2cdc5daba5bb623ac5f06b52bcdd333e\", \"key\": \"New Body\", \"title\": \"New Body [V16]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> $ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [KW REF NM VRS 2] 10.04.18\\nFeatures a further along <PERSON><PERSON><PERSON> verse, and a <PERSON><PERSON> verse. <PERSON><PERSON> stated that she recorded her verse \\\"within an hour\\\" after being contacted by <PERSON>.\", \"date\": 16357248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd406c0bb42acca8c2a3e4632018f051\", \"url\": \"https://api.pillowcase.su/api/download/bd406c0bb42acca8c2a3e4632018f051\", \"size\": \"3.44 MB\", \"duration\": 171.84}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.44 MB"}, {"id": "new-body-204", "name": "New Body [V17]", "artists": ["Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [KW REF TY VRS 2] 10.04.18\nFeatures a further along <PERSON><PERSON><PERSON> verse, and a Ty Dolla $ign verse.", "length": "171.84", "fileDate": 16357248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/b5bd1c2c4e6b5473b03a25d670e0acd1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b5bd1c2c4e6b5473b03a25d670e0acd1\", \"key\": \"New Body\", \"title\": \"New Body [V17]\", \"artists\": \"(feat. <PERSON> $ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [KW REF TY VRS 2] 10.04.18\\nFeatures a further along Kanye verse, and a Ty Dolla $ign verse.\", \"date\": 16357248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c0d88617f138b2fc31f0a931196e100e\", \"url\": \"https://api.pillowcase.su/api/download/c0d88617f138b2fc31f0a931196e100e\", \"size\": \"3.44 MB\", \"duration\": 171.84}", "aliases": ["Can't Wait To See Your New Body"], "size": "3.44 MB"}, {"id": "new-body-205", "name": "New Body [V19]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "<PERSON><PERSON><PERSON>'s verse is unfinished. \"New Body\" is Track 2 on the SNL tracklist.", "length": "222.41", "fileDate": 15627168, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/fe3235680b174921c6b09ab47a594074", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe3235680b174921c6b09ab47a594074\", \"key\": \"New Body\", \"title\": \"New Body [V19]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> $ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"<PERSON><PERSON><PERSON>'s verse is unfinished. \\\"New Body\\\" is Track 2 on the SNL tracklist.\", \"date\": 15627168, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b222250a030b6b19a5051c036c21d17c\", \"url\": \"https://api.pillowcase.su/api/download/b222250a030b6b19a5051c036c21d17c\", \"size\": \"4.25 MB\", \"duration\": 222.41}", "aliases": ["Can't Wait To See Your New Body"], "size": "4.25 MB"}, {"id": "new-body-206", "name": "⭐ New Body [V20]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "OG Filename: New Body [Add KW tag line] 10.06.18\nVery similar to previous version except it has extra Kany<PERSON> vocals on the chorus.", "length": "224.9", "fileDate": 16337376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a14bab72af29f145f2efe8e4151024e5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a14bab72af29f145f2efe8e4151024e5\", \"key\": \"New Body\", \"title\": \"\\u2b50 New Body [V20]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> $ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body [Add KW tag line] 10.06.18\\nVery similar to previous version except it has extra Kanye vocals on the chorus.\", \"date\": 16337376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"56e875b33076744e779ab8f89225be44\", \"url\": \"https://api.pillowcase.su/api/download/56e875b33076744e779ab8f89225be44\", \"size\": \"4.29 MB\", \"duration\": 224.9}", "aliases": ["Can't Wait To See Your New Body"], "size": "4.29 MB"}, {"id": "oz", "name": "Oz", "artists": [], "producers": ["Symphony", "Triangle Park"], "notes": "OG Filename: Oz_Prod by Symphony x TP\nUganda-era reference track. Open verses are meant for <PERSON>ny<PERSON>. Leaked due to a groupbuy.", "length": "179.62", "fileDate": 17216064, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1c3855d43cf52f06a9cb3a1847a1ecd3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c3855d43cf52f06a9cb3a1847a1ecd3\", \"key\": \"Oz\", \"title\": \"<PERSON>\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Symphony & Triangle Park)\", \"description\": \"OG Filename: Oz_Prod by Symphony x TP\\nUganda-era reference track. Open verses are meant for Kanye. Leaked due to a groupbuy.\", \"date\": 17216064, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e6ff344684366de03fa3277c6780b4c2\", \"url\": \"https://api.pillowcase.su/api/download/e6ff344684366de03fa3277c6780b4c2\", \"size\": \"3.57 MB\", \"duration\": 179.62}", "aliases": [], "size": "3.57 MB"}, {"id": "sky-city-208", "name": "Sky City [V21]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake", "<PERSON>"], "producers": ["Ty Dolla $ign", "MIKE DEAN"], "notes": "OG Filename: SKY CITY [10.01.18] OPEN VERSE\nOpen verse version of \"Sky City\" from 10.01.", "length": "258.29", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c7c814f4d97e21c42449596cf293f73f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c7c814f4d97e21c42449596cf293f73f\", \"key\": \"Sky City\", \"title\": \"Sky City [V21]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 070 <PERSON> & <PERSON>) (prod. <PERSON> $ign & MIKE DEAN)\", \"aliases\": [\"City In The Sky\"], \"description\": \"OG Filename: SKY CITY [10.01.18] OPEN VERSE\\nOpen verse version of \\\"Sky City\\\" from 10.01.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bfe455b4eafac68c4162465eeedc4c75\", \"url\": \"https://api.pillowcase.su/api/download/bfe455b4eafac68c4162465eeedc4c75\", \"size\": \"4.82 MB\", \"duration\": 258.29}", "aliases": ["City In The Sky"], "size": "4.82 MB"}, {"id": "sky-city-209", "name": "Sky City [V22]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake", "<PERSON>"], "producers": ["Ty Dolla $ign", "MIKE DEAN"], "notes": "OG Filename: SKY CITY 10.01.18 [Move Shake]\nVersion of \"Sky City\" similar to the one above, with 070 Shake vocals moved to after <PERSON><PERSON><PERSON> verse. Extended guitar outro.", "length": "258.29", "fileDate": 16294176, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/00feef233b031eadb20c92d191564535", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00feef233b031eadb20c92d191564535\", \"key\": \"Sky City\", \"title\": \"Sky City [V22]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 070 <PERSON> & <PERSON>) (prod. <PERSON> $ign & MIKE DEAN)\", \"aliases\": [\"City In The Sky\", \"Loyalty\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\"], \"description\": \"OG Filename: SKY CITY 10.01.18 [Move Shake]\\nVersion of \\\"Sky City\\\" similar to the one above, with 070 Shake vocals moved to after <PERSON><PERSON><PERSON> verse. Extended guitar outro.\", \"date\": 16294176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"37d730c22c928c6d46a4de73722505b9\", \"url\": \"https://api.pillowcase.su/api/download/37d730c22c928c6d46a4de73722505b9\", \"size\": \"4.82 MB\", \"duration\": 258.29}", "aliases": ["City In The Sky", "Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier"], "size": "4.82 MB"}, {"id": "sky-city-210", "name": "Sky City [V23]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake", "<PERSON>"], "producers": ["Ty Dolla $ign", "MIKE DEAN"], "notes": "OG Filename: SKY CITY 10.04.18 [Extend Shake Verse]\nHas more Shake vocals than the one above.", "length": "252.22", "fileDate": 16294176, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2b43da5e9a98778f5afde8f136c3c28e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b43da5e9a98778f5afde8f136c3c28e\", \"key\": \"Sky City\", \"title\": \"Sky City [V23]\", \"artists\": \"(feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, 070 <PERSON> & <PERSON>) (prod. <PERSON> $ign & MIKE DEAN)\", \"aliases\": [\"City In The Sky\", \"Loyalty\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\"], \"description\": \"OG Filename: SKY CITY 10.04.18 [Extend Shake Verse]\\nHas more Shake vocals than the one above.\", \"date\": 16294176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fd3c9c9924b80111d6620bbb64e7fbc7\", \"url\": \"https://api.pillowcase.su/api/download/fd3c9c9924b80111d6620bbb64e7fbc7\", \"size\": \"4.73 MB\", \"duration\": 252.22}", "aliases": ["City In The Sky", "Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier"], "size": "4.73 MB"}, {"id": "sky-city-211", "name": "Sky City [V24]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "070 Shake"], "producers": ["Ty Dolla $ign", "MIKE DEAN"], "notes": "OG Filename: SKY CITY 10.08.18 [TONY WILLIAMS demo]\n<PERSON> \"Sky City\" reference track.", "length": "255.24", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/dd61e65cbd7d81a1a35e3423e606476a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd61e65cbd7d81a1a35e3423e606476a\", \"key\": \"Sky City\", \"title\": \"Sky City [V24]\", \"artists\": \"(ref. The WRLDFMS <PERSON>) (feat. <PERSON>ign, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & 07<PERSON> <PERSON>) (prod. <PERSON> $ign & MIKE DEAN)\", \"aliases\": [\"City In The Sky\", \"<PERSON>yalty\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\"], \"description\": \"OG Filename: SKY CITY 10.08.18 [TONY WILLIAMS demo]\\nTony Williams \\\"Sky City\\\" reference track.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"19280c13de06935271a5cd2d8ca95854\", \"url\": \"https://api.pillowcase.su/api/download/19280c13de06935271a5cd2d8ca95854\", \"size\": \"4.78 MB\", \"duration\": 255.24}", "aliases": ["City In The Sky", "Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier"], "size": "4.78 MB"}, {"id": "speak-up", "name": "Speak Up [V1]", "artists": [], "producers": ["FnZ", "RONNY J"], "notes": "OG Filename: Speak Up [10.15.18 Ant catch]\nAn<PERSON> Clemons reference track for \"Speak Up\". Unknown if a Ye version exists.", "length": "124.65", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1bc46e9a489207207994a04b0b88ebe8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1bc46e9a489207207994a04b0b88ebe8\", \"key\": \"Speak Up\", \"title\": \"Speak Up [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. FnZ & RONNY J)\", \"aliases\": [\"Detroit\"], \"description\": \"OG Filename: Speak Up [10.15.18 Ant catch]\\nAnt Clemons reference track for \\\"Speak Up\\\". Unknown if a Ye version exists.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ac0d6fd184fe6e281951d406563bf77e\", \"url\": \"https://api.pillowcase.su/api/download/ac0d6fd184fe6e281951d406563bf77e\", \"size\": \"2.69 MB\", \"duration\": 124.65}", "aliases": ["Detroit"], "size": "2.69 MB"}, {"id": "tekken-213", "name": "<PERSON><PERSON><PERSON> [V6]", "artists": ["6ix9ine"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: Teken - 10.04.18 Shorter\nUnfinished demo of \"KANGA\" originally titled \"Tekken\".", "length": "134.32", "fileDate": 16286400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c26469b1ba19dcd6021f56771483293d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c26469b1ba19dcd6021f56771483293d\", \"key\": \"Tekken\", \"title\": \"Tek<PERSON> [V6]\", \"artists\": \"(feat. 6ix9<PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KANGA\"], \"description\": \"OG Filename: Teken - 10.04.18 Shorter\\nUnfinished demo of \\\"KANGA\\\" originally titled \\\"Tekken\\\".\", \"date\": 16286400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"04be75011445409ec739573693fae04f\", \"url\": \"https://api.pillowcase.su/api/download/04be75011445409ec739573693fae04f\", \"size\": \"2.84 MB\", \"duration\": 134.32}", "aliases": ["KANGA"], "size": "2.84 MB"}, {"id": "tekken-214", "name": "<PERSON><PERSON><PERSON> [V8]", "artists": ["6ix9ine"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Beat - 10.06.18 [Even Shorter]\nShorter version of the already short October 4th version.", "length": "126.07", "fileDate": 16761600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e7174aa9177c08724d595b1e189508e0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7174aa9177c08724d595b1e189508e0\", \"key\": \"Tekken\", \"title\": \"<PERSON>k<PERSON> [V8]\", \"artists\": \"(feat. 6ix9<PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KANGA\"], \"description\": \"OG Filename: Teken Beat - 10.06.18 [<PERSON> Shorter]\\nShorter version of the already short October 4th version.\", \"date\": 16761600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"85ec3e6369bbc0f0c251bd4845def6e8\", \"url\": \"https://api.pillowcase.su/api/download/85ec3e6369bbc0f0c251bd4845def6e8\", \"size\": \"2.71 MB\", \"duration\": 126.07}", "aliases": ["KANGA"], "size": "2.71 MB"}, {"id": "tekken-215", "name": "Tekken [V10]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON>ken 10.08.18 [CyHi Ref] HA\nUnknown how different this is from the initial reference track.", "length": "124.11", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f0dd137ff46292ea4ff2b28a733e06b5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0dd137ff46292ea4ff2b28a733e06b5\", \"key\": \"Tekken\", \"title\": \"<PERSON>k<PERSON> [V10]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KANGA\"], \"description\": \"OG Filename: Teken 10.08.18 [CyHi Ref] HA\\nUnknown how different this is from the initial reference track.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d14e1ca530a24c97f667567c37432e0\", \"url\": \"https://api.pillowcase.su/api/download/8d14e1ca530a24c97f667567c37432e0\", \"size\": \"2.68 MB\", \"duration\": 124.11}", "aliases": ["KANGA"], "size": "2.68 MB"}, {"id": "tekken-216", "name": "Tek<PERSON> [V11]", "artists": ["6ix9ine", "Azealia Banks"], "producers": ["<PERSON><PERSON>"], "notes": "Version of \"KANGA\"/\"Tekken\" including a rough demo verse from <PERSON><PERSON><PERSON>, posted herself after the Dummy Boy album leaked. Unknown when this was recorded and if it was meant for the song when it was <PERSON><PERSON><PERSON>'s or 6<PERSON><PERSON><PERSON>'s.", "length": "52.11", "fileDate": ********, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/5e48bf0178dd415368a864c042245cd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e48bf0178dd415368a864c042245cd7\", \"key\": \"Tekken\", \"title\": \"Tek<PERSON> [V11]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"KANGA\"], \"description\": \"Version of \\\"KANGA\\\"/\\\"Tekken\\\" including a rough demo verse from <PERSON><PERSON><PERSON>, posted herself after the Dummy Boy album leaked. Unknown when this was recorded and if it was meant for the song when it was <PERSON><PERSON><PERSON>'s or 6ix9<PERSON>'s.\", \"date\": ********, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c5b19080ab2833d46c0f9d0d6d5eefee\", \"url\": \"https://api.pillowcase.su/api/download/c5b19080ab2833d46c0f9d0d6d5eefee\", \"size\": \"1.11 MB\", \"duration\": 52.11}", "aliases": ["KANGA"], "size": "1.11 MB"}, {"id": "the-chakra-217", "name": "The Chakra [V13]", "artists": ["The-Dream", "<PERSON>", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: The Chakra [OPEN VERSE] 10.04.18\nVersion of \"The Chakra\" with an open verse.", "length": "191.89", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/53d06e8e51c39eae257208e753508208", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53d06e8e51c39eae257208e753508208\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V13]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Ant Clemons) (prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Chakra<PERSON>\", \"<PERSON>lah\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra [OPEN VERSE] 10.04.18\\nVersion of \\\"The Chakra\\\" with an open verse.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"86f152c930870aea841a528e83adf620\", \"url\": \"https://api.pillowcase.su/api/download/86f152c930870aea841a528e83adf620\", \"size\": \"3.76 MB\", \"duration\": 191.89}", "aliases": ["Chakras", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "the-chakra-218", "name": "The Chakra [V14]", "artists": ["The-Dream", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: The Chakra [REF] 10.04.18\nVersion of \"The Chakra\" used on a December tracklist.", "length": "191.89", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/9ce4bc130906427bca26215537402323", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9ce4bc130906427bca26215537402323\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Ant Clemons) (prod. BoogzDaBeast & <PERSON>)\", \"aliases\": [\"Chakra<PERSON>\", \"<PERSON>lah\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra [REF] 10.04.18\\nVersion of \\\"The Chakra\\\" used on a December tracklist.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4b8ea9f5c2cd15c860f8682e87baed3d\", \"url\": \"https://api.pillowcase.su/api/download/4b8ea9f5c2cd15c860f8682e87baed3d\", \"size\": \"3.76 MB\", \"duration\": 191.89}", "aliases": ["Chakras", "<PERSON><PERSON>", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "the-chakra-219", "name": "The Chakra [V16]", "artists": ["The-Dream", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: The Chakra [NEW VOCAL REFERENCE]\nHas no more mumble vocals and rough finished vocals instead.", "length": "206.66", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/fa72d3bd16f0b18e2b16ec7107985ef1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa72d3bd16f0b18e2b16ec7107985ef1\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V16]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Ant Clemons) (prod. BoogzDaBeast & Caroline <PERSON>)\", \"aliases\": [\"Chakra<PERSON>\", \"<PERSON><PERSON>\", \"Chakra\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra [NEW VOCAL REFERENCE]\\nHas no more mumble vocals and rough finished vocals instead.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ec09dc94964fd28a5346dc03a85ebdbd\", \"url\": \"https://api.pillowcase.su/api/download/ec09dc94964fd28a5346dc03a85ebdbd\", \"size\": \"4 MB\", \"duration\": 206.66}", "aliases": ["Chakras", "<PERSON><PERSON>", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "4 MB"}, {"id": "the-chakra-220", "name": "The Chakra [V17]", "artists": ["The-Dream", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: The Chakra 10.08.18 [TONY WILLIAMS demo]\n<PERSON> \"The Chakra\" reference track. Has <PERSON> recording over an earlier version of the song.", "length": "191.89", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/4d8afcf53f0f151062c8ad935d0320ef", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d8afcf53f0f151062c8ad935d0320ef\", \"key\": \"The Chakra\", \"title\": \"The Chakra [V17]\", \"artists\": \"(ref. The WRLDFMS <PERSON>) (feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Ant Clemons) (prod. BoogzDaBeast & Caroline Shaw)\", \"aliases\": [\"Chakra<PERSON>\", \"Selah\", \"Chakra\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: The Chakra 10.08.18 [TONY WILLIAMS demo]\\nT<PERSON> \\\"The Chakra\\\" reference track. Has <PERSON> recording over an earlier version of the song.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bb5bfb0fa514d8c7c462faf29262423c\", \"url\": \"https://api.pillowcase.su/api/download/bb5bfb0fa514d8c7c462faf29262423c\", \"size\": \"3.76 MB\", \"duration\": 191.89}", "aliases": ["Chakras", "<PERSON><PERSON>", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "selah", "name": "<PERSON><PERSON> [V18]", "artists": ["The-Dream", "BONGO ByTheWay", "<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: SELAH 10.08.18 [<PERSON> demo]\nHas more <PERSON><PERSON> vocals with <PERSON> stacked over.", "length": "192.05", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/5805921d3ea2e472d272ed67593a5e8b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5805921d3ea2e472d272ed67593a5e8b\", \"key\": \"<PERSON>lah\", \"title\": \"<PERSON>lah [V18]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ByTheWay & Ant Clemons) (prod. BoogzDaBeast & Caroline Shaw)\", \"aliases\": [\"Chakra<PERSON>\", \"The Chakra\", \"Cha<PERSON>\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: SELAH 10.08.18 [<PERSON> demo]\\nHas more <PERSON><PERSON> vocals with <PERSON> stacked over.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"30f77033288ab1329efe36ede695a629\", \"url\": \"https://api.pillowcase.su/api/download/30f77033288ab1329efe36ede695a629\", \"size\": \"3.76 MB\", \"duration\": 192.05}", "aliases": ["Chakras", "The Chakra", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "3.76 MB"}, {"id": "selah-222", "name": "<PERSON><PERSON> [V19]", "artists": [], "producers": [], "notes": "OG Filename: SELAH 10.14.18 [<PERSON> demo]\nFilename shown by orb. Nothing else is known.", "length": "8.49", "fileDate": 16962912, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/0f46129eb79cf6588207b51e27f8b046", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f46129eb79cf6588207b51e27f8b046\", \"key\": \"<PERSON>lah\", \"title\": \"<PERSON><PERSON> [V19]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"The Chakra\", \"Cha<PERSON>\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: SELAH 10.14.18 [<PERSON> demo]\\nFilename shown by orb. Nothing else is known.\", \"date\": 16962912, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9d69991647f977a8d9da5e82a14c0a64\", \"url\": \"https://api.pillowcase.su/api/download/9d69991647f977a8d9da5e82a14c0a64\", \"size\": \"827 kB\", \"duration\": 8.49}", "aliases": ["Chakras", "The Chakra", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "827 kB"}, {"id": "selah-223", "name": "<PERSON><PERSON> [V20]", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: se<PERSON> (evan 10.27)\nOriginal version that was intended for Yandhi after the Uganda era. Reuses vocal takes from \"Chakras\", with additional mixing changes. Was also used on a December tracklist.", "length": "209.61", "fileDate": 15708384, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/20a223c194e8abfcc9f300f304bdb6f0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20a223c194e8abfcc9f300f304bdb6f0\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V20]\", \"artists\": \"(prod. E.VAX)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"The Chakra\", \"Chakra\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"OG Filename: selah (evan 10.27)\\nOriginal version that was intended for Yandhi after the Uganda era. Reuses vocal takes from \\\"Chakras\\\", with additional mixing changes. Was also used on a December tracklist.\", \"date\": 15708384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7fccc0a16df1816d0878a1f203f0f7e1\", \"url\": \"https://api.pillowcase.su/api/download/7fccc0a16df1816d0878a1f203f0f7e1\", \"size\": \"4.04 MB\", \"duration\": 209.61}", "aliases": ["Chakras", "The Chakra", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "4.04 MB"}, {"id": "the-garden-224", "name": "The Garden [V11]", "artists": ["<PERSON>", "Ty Dolla $ign", "<PERSON><PERSON>", "The-Dream"], "producers": [], "notes": "OG Filename: The Garden 10.04.18 [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>]\nVersion of \"The Garden\" with an open during <PERSON><PERSON>'s part. Was also used on a December tracklist.", "length": "228.65", "fileDate": 16357248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/c1dc8840ce2515eea1cf40ca1d7e23b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1dc8840ce2515eea1cf40ca1d7e23b7\", \"key\": \"The Garden\", \"title\": \"The Garden [V11]\", \"artists\": \"(feat. <PERSON>, <PERSON>gn, <PERSON><PERSON> & The-Dream)\", \"description\": \"OG Filename: The Garden 10.04.18 [T<PERSON>, <PERSON><PERSON>, <PERSON>]\\nVersion of \\\"The Garden\\\" with an open during <PERSON><PERSON>'s part. Was also used on a December tracklist.\", \"date\": 16357248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7366dafb444c563b3181d13b01a341f5\", \"url\": \"https://api.pillowcase.su/api/download/7366dafb444c563b3181d13b01a341f5\", \"size\": \"4.35 MB\", \"duration\": 228.65}", "aliases": [], "size": "4.35 MB"}, {"id": "the-garden-225", "name": "The Garden [V13]", "artists": [], "producers": [], "notes": "OG Filename: The Garden 10.08.18 [TONY WILLIAMS demo 2]\nSecond of two <PERSON> \"Garden\" reference tracks.", "length": "228.65", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f36aaaac718bdc4adf641c224383b13a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f36aaaac718bdc4adf641c224383b13a\", \"key\": \"The Garden\", \"title\": \"The Garden [V13]\", \"artists\": \"(ref. The WRLDFMS <PERSON>)\", \"description\": \"OG Filename: The Garden 10.08.18 [TONY WILLIAMS demo 2]\\nSecond of two <PERSON> \\\"Garden\\\" reference tracks.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f5eea0e32b2e83d90ef684b542596a6b\", \"url\": \"https://api.pillowcase.su/api/download/f5eea0e32b2e83d90ef684b542596a6b\", \"size\": \"4.35 MB\", \"duration\": 228.65}", "aliases": [], "size": "4.35 MB"}, {"id": "the-storm-226", "name": "The Storm [V17]", "artists": ["XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": [], "notes": "OG Filename: The Storm 10.04.18 [Shortened]\nVersion of \"The Storm\" with autotuned <PERSON><PERSON><PERSON> vocals, no <PERSON><PERSON> verse and XXX verse. Shorter outro.", "length": "133.29", "fileDate": 16294176, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f741f6834e2d2738845eee573beb4b64", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f741f6834e2d2738845eee573beb4b64\", \"key\": \"The Storm\", \"title\": \"The Storm [V17]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gn, <PERSON><PERSON> & tizhimself)\", \"aliases\": [\"Everything We Need\", \"Jeet\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: The Storm 10.04.18 [Shortened]\\nVersion of \\\"The Storm\\\" with autotuned <PERSON><PERSON><PERSON> vocals, no <PERSON>udi verse and XXX verse. Shorter outro.\", \"date\": 16294176, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3e86ef3fced2e50199cebaf6f78a7424\", \"url\": \"https://api.pillowcase.su/api/download/3e86ef3fced2e50199cebaf6f78a7424\", \"size\": \"2.83 MB\", \"duration\": 133.29}", "aliases": ["Everything We Need", "<PERSON>et", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "2.83 MB"}, {"id": "xxx-227", "name": "XXX [V19]", "artists": ["<PERSON>", "XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": ["RONNY J", "MIKE DEAN"], "notes": "OG Filename: XXX 10.04.18 [ADD MD PARTS]\nThird 10.04.18 version of \"The Storm\". Basically the same as the SNL version, but with a shorter outro.", "length": "182.47", "fileDate": 16357248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/e694824cfe9885da24bc33dd79953a35", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e694824cfe9885da24bc33dd79953a35\", \"key\": \"XXX\", \"title\": \"XXX [V19]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> & tizhim<PERSON>) (prod. RONNY J & MIKE DEAN)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"Jeet\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: XXX 10.04.18 [ADD MD PARTS]\\nThird 10.04.18 version of \\\"The Storm\\\". Basically the same as the SNL version, but with a shorter outro.\", \"date\": 16357248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7dd379d54cc247caf1464cee9bed0197\", \"url\": \"https://api.pillowcase.su/api/download/7dd379d54cc247caf1464cee9bed0197\", \"size\": \"3.61 MB\", \"duration\": 182.47}", "aliases": ["Everything We Need", "The Storm", "We Begin", "<PERSON>et", "We Begin After The Storm Inside"], "size": "3.61 MB"}, {"id": "xxx-228", "name": "XXX [V21]", "artists": ["<PERSON>", "XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": ["RONNY J", "MIKE DEAN"], "notes": "OG Filename: XXX 10.07.18 [Shorten]\nAnother shortened version of \"XXX\". Was used on a December tracklist.", "length": "133.3", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/80306ec246a3d2fc06c4ee7ab9576ce3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80306ec246a3d2fc06c4ee7ab9576ce3\", \"key\": \"XXX\", \"title\": \"XXX [V21]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ign, <PERSON><PERSON> & tizhim<PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> J & MIKE DEAN)\", \"aliases\": [\"Everything We Need\", \"The Storm\", \"We Begin\", \"Jeet\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: XXX 10.07.18 [Shorten]\\nAnother shortened version of \\\"XXX\\\". Was used on a December tracklist.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c810c9800a34f4b477f2a3ad5ea573cd\", \"url\": \"https://api.pillowcase.su/api/download/c810c9800a34f4b477f2a3ad5ea573cd\", \"size\": \"2.83 MB\", \"duration\": 133.3}", "aliases": ["Everything We Need", "The Storm", "We Begin", "<PERSON>et", "We Begin After The Storm Inside"], "size": "2.83 MB"}, {"id": "we-free", "name": "✨ We Free", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: WE FREE 10.12.18\nA completely finished interlude supposedly intended for Black Friday Yandhi where he just raps over a \"beat\" made entirely out of his mouth.", "length": "42.34", "fileDate": 16027200, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/ba47cdec7147b3ac438fa3527cb044d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ba47cdec7147b3ac438fa3527cb044d8\", \"key\": \"We Free\", \"title\": \"\\u2728 We Free\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: WE FREE 10.12.18\\nA completely finished interlude supposedly intended for Black Friday Yandhi where he just raps over a \\\"beat\\\" made entirely out of his mouth.\", \"date\": 16027200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"61eb5c2883319ebfcff0aead4225caf1\", \"url\": \"https://api.pillowcase.su/api/download/61eb5c2883319ebfcff0aead4225caf1\", \"size\": \"1.37 MB\", \"duration\": 42.34}", "aliases": [], "size": "1.37 MB"}, {"id": "we-got-love-230", "name": "⭐ We Got Love [V24]", "artists": ["<PERSON><PERSON>", "Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "E.VAX", "MIKE DEAN"], "notes": "OG Filename: WE GOT LOVE MIKE JESS MASTER 18\nMixed and mastered version, seemingly intended for release.", "length": "238", "fileDate": 16765920, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/81199b95b261d503a4fbe0136af22f94", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/81199b95b261d503a4fbe0136af22f94\", \"key\": \"We Got Love\", \"title\": \"\\u2b50 We Got Love [V24]\", \"artists\": \"(feat. <PERSON><PERSON> & Ms. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, E.VAX & MIKE DEAN)\", \"description\": \"OG Filename: WE GOT LOVE MIKE JESS MASTER 18\\nMixed and mastered version, seemingly intended for release.\", \"date\": 16765920, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a39c60a6d6e0576c7861efd8ea8c5fd\", \"url\": \"https://api.pillowcase.su/api/download/4a39c60a6d6e0576c7861efd8ea8c5fd\", \"size\": \"4.5 MB\", \"duration\": 238}", "aliases": [], "size": "4.5 MB"}, {"id": "-231", "name": "??? [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Early mumble demo <PERSON><PERSON><PERSON> did over the \"Ego Death\" loop. Likely not <PERSON>gn's song at this point or called \"Ego Death\", as the basis for the \"Ego Death\" beat was produced by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>gn doesn't have vocals on this version.", "length": "9.67", "fileDate": 16026336, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/7614598256c4833a5a83885206e1295e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7614598256c4833a5a83885206e1295e\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Ego Death\"], \"description\": \"Early mumble demo <PERSON><PERSON><PERSON> did over the \\\"Ego Death\\\" loop. Likely not <PERSON> $ign's song at this point or called \\\"Ego Death\\\", as the basis for the \\\"Ego Death\\\" beat was produced by <PERSON><PERSON><PERSON><PERSON><PERSON>B<PERSON>t and <PERSON>ign doesn't have vocals on this version.\", \"date\": 16026336, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b73d97c0f1271bf36a4f1194edce1e0c\", \"url\": \"https://api.pillowcase.su/api/download/b73d97c0f1271bf36a4f1194edce1e0c\", \"size\": \"846 kB\", \"duration\": 9.67}", "aliases": ["Ego Death"], "size": "846 kB"}, {"id": "-232", "name": "???", "artists": [], "producers": ["RONNY J"], "notes": "Short recording of <PERSON><PERSON><PERSON> rapping over RONNY J beats while in Uganda. Repeats a \"we free\" line from the 11 minute livestream session. Was posted on Instagram in June 2019.", "length": "13.3", "fileDate": 15593472, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/23b12388d6596db355eb2e102aa660e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23b12388d6596db355eb2e102aa660e4\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> J)\", \"aliases\": [\"We Free\"], \"description\": \"Short recording of <PERSON><PERSON><PERSON> rapping over RONNY J beats while in Uganda. Repeats a \\\"we free\\\" line from the 11 minute livestream session. Was posted on Instagram in June 2019.\", \"date\": 15593472, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e5e7648e11555ce499e8ace36951ffb2\", \"url\": \"https://api.pillowcase.su/api/download/e5e7648e11555ce499e8ace36951ffb2\", \"size\": \"904 kB\", \"duration\": 13.3}", "aliases": ["We Free"], "size": "904 kB"}, {"id": "mama", "name": "6ix9ine - MAMA [V1]", "artists": [], "producers": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "notes": "Includes the original line \"Splish, splash, Fashion Nova make that ass fat\" that 6ix9ine later cut. Fashion Nova paid six figures for the line, but <PERSON><PERSON> asked for it to be cut because of her beef with <PERSON><PERSON>. This version has no <PERSON><PERSON><PERSON> or <PERSON><PERSON> vocals.", "length": "39.5", "fileDate": 15383520, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/a6ac540a6625e0b590bea3bc4b09a01a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6ac540a6625e0b590bea3bc4b09a01a\", \"key\": \"MAMA\", \"title\": \"6ix9ine - MAMA [V1]\", \"artists\": \"(prod. <PERSON><PERSON> Hits & Murda Beatz)\", \"description\": \"Includes the original line \\\"Splish, splash, Fashion Nova make that ass fat\\\" that 6ix9ine later cut. Fashion Nova paid six figures for the line, but <PERSON><PERSON> asked for it to be cut because of her beef with <PERSON><PERSON>. This version has no <PERSON><PERSON><PERSON> or <PERSON><PERSON> vocals.\", \"date\": 15383520, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8d14294b2252d082700e99c7d06f5f3c\", \"url\": \"https://api.pillowcase.su/api/download/8d14294b2252d082700e99c7d06f5f3c\", \"size\": \"1.01 MB\", \"duration\": 39.5}", "aliases": [], "size": "1.01 MB"}, {"id": "mama-234", "name": "6ix9ine - MAMA [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "notes": "Leaked on an IG live. This version has <PERSON><PERSON> doing the first verse. It is unknown if <PERSON><PERSON><PERSON> is on this version.", "length": "42.81", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/35b9405f3b16280a85fe9fc5e88f31cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/35b9405f3b16280a85fe9fc5e88f31cf\", \"key\": \"MAMA\", \"title\": \"6ix9ine - MAMA [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON> Hits & Murda Beatz)\", \"description\": \"Leaked on an IG live. This version has <PERSON><PERSON> doing the first verse. It is unknown if <PERSON><PERSON><PERSON> is on this version.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1f9b78b351e605ec25de74ec8c1820d0\", \"url\": \"https://api.pillowcase.su/api/download/1f9b78b351e605ec25de74ec8c1820d0\", \"size\": \"1.03 MB\", \"duration\": 42.81}", "aliases": [], "size": "1.03 MB"}, {"id": "take-me-to-the-light-235", "name": "<PERSON> and the Lights - Take Me To The Light [V8]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: LIL DICKY OUTRO\nAn alternate version of the \"Take Me To The Light\" Lil <PERSON>y outro, most likely as a test or to stitch onto the rest of the song.", "length": "78.33", "fileDate": 16232832, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/f6f29a7182570ea8b36f3789e9e31546", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f6f29a7182570ea8b36f3789e9e31546\", \"key\": \"Take Me To The Light\", \"title\": \"<PERSON> and the Lights - Take Me To The Light [V8]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"OG Filename: LIL DICKY OUTRO\\nAn alternate version of the \\\"Take Me To The Light\\\" Lil Dicky outro, most likely as a test or to stitch onto the rest of the song.\", \"date\": 16232832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3fa61dc79788c05579d97d4c9f7690f3\", \"url\": \"https://api.pillowcase.su/api/download/3fa61dc79788c05579d97d4c9f7690f3\", \"size\": \"1.94 MB\", \"duration\": 78.33}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "1.94 MB"}, {"id": "no-problem", "name": "Smokepurpp - No Problem", "artists": ["Kanye West"], "producers": ["Major Seven"], "notes": "OG Filename: <PERSON> Problem ft. <PERSON><PERSON><PERSON> prod by Major <PERSON> finished song, cut off Deadstar 2 due to <PERSON><PERSON><PERSON>'s new strong religious ties. Was on the album as late as August 2019. Forceleaked after an attempted groupbuy. OG file later leaked May 13th, 2024.", "length": "142.32", "fileDate": 17155584, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/2d7b7c44ab219ec14fa1b6e3fa29e451", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d7b7c44ab219ec14fa1b6e3fa29e451\", \"key\": \"No Problem\", \"title\": \"Smokepurpp - No Problem\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: No Problem ft. <PERSON><PERSON><PERSON> prod by <PERSON> Seven\\nFully finished song, cut off Deadstar 2 due to <PERSON><PERSON><PERSON>'s new strong religious ties. Was on the album as late as August 2019. Forceleaked after an attempted groupbuy. OG file later leaked May 13th, 2024.\", \"date\": 17155584, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0d1539bdb6ed80abbcb97975a2f3883d\", \"url\": \"https://api.pillowcase.su/api/download/0d1539bdb6ed80abbcb97975a2f3883d\", \"size\": \"2.97 MB\", \"duration\": 142.32}", "aliases": [], "size": "2.97 MB"}, {"id": "one-minute-237", "name": "XXXTENTACION - One Minute [V2]", "artists": ["Kanye West"], "producers": ["<PERSON>"], "notes": "Has different mixing and song structure. Includes two minutes of new <PERSON><PERSON><PERSON> vocals.", "length": "183.62", "fileDate": 16025472, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/adaa695a84798a9a9cc7545ef93c0b99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/adaa695a84798a9a9cc7545ef93c0b99\", \"key\": \"One Minute\", \"title\": \"XXXTENTACION - One Minute [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"Has different mixing and song structure. Includes two minutes of new <PERSON><PERSON><PERSON> vocals.\", \"date\": 16025472, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c49ce17d0836146b82ed17828ef395d7\", \"url\": \"https://api.pillowcase.su/api/download/c49ce17d0836146b82ed17828ef395d7\", \"size\": \"3.63 MB\", \"duration\": 183.62}", "aliases": [], "size": "3.63 MB"}, {"id": "one-minute-238", "name": "XXXTENTACION - One Minute [V3]", "artists": ["Kanye West", "The WRLDFMS <PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> Minnute 10.19.18 [<PERSON>ing]\nHas mumble <PERSON><PERSON><PERSON>, alternate production, and <PERSON> vocals. Leaked as part of the \"Welcome To My Life\" groupbuy.", "length": "327.32", "fileDate": 16722720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/3de9f477efe2e93afca12be340433c94", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3de9f477efe2e93afca12be340433c94\", \"key\": \"One Minute\", \"title\": \"XXXTENTACION - One Minute [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & The WRLDFMS <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: One Minnute 10.19.18 [<PERSON>]\\nHas mumble Kanye, alternate production, and <PERSON> vocals. Leaked as part of the \\\"Welcome To My Life\\\" groupbuy.\", \"date\": 16722720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b7b018d7094497defa5792a67b66f78f\", \"url\": \"https://api.pillowcase.su/api/download/b7b018d7094497defa5792a67b66f78f\", \"size\": \"5.93 MB\", \"duration\": 327.32}", "aliases": [], "size": "5.93 MB"}, {"id": "one-minute-239", "name": "XXXTENTACION - One Minute [V4]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: LP V1 - One Minute (KH Final Mix v4) (MASTERED DK)\nHas <PERSON> but no <PERSON><PERSON><PERSON>, likely because his verse was not yet finished. Mastered file from <PERSON>.", "length": "93.38", "fileDate": 16633728, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/bf2a875e62e5cfdc4b25b032800637ca", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bf2a875e62e5cfdc4b25b032800637ca\", \"key\": \"One Minute\", \"title\": \"XXXTENTACION - One Minute [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: LP V1 - One Minute (KH Final Mix v4) (MASTERED DK)\\nHas <PERSON> but no <PERSON><PERSON><PERSON>, likely because his verse was not yet finished. Mastered file from <PERSON>.\", \"date\": 16633728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6f6af124a7be448278762d53f2904fd1\", \"url\": \"https://api.pillowcase.su/api/download/6f6af124a7be448278762d53f2904fd1\", \"size\": \"2.19 MB\", \"duration\": 93.38}", "aliases": [], "size": "2.19 MB"}, {"id": "one-minute-240", "name": "XXXTENTACION - One Minute [V5]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: one minute \nCyHi reference track. Likely made after the mumble version as the lyrics are more similar to the release than the original. Leaked along with the Protools sessions.", "length": "188.24", "fileDate": 16110144, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v1", "originalUrl": "https://pillowcase.su/f/1faa27aa616b635b1de342cc202ac8a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1faa27aa616b635b1de342cc202ac8a6\", \"key\": \"One Minute\", \"title\": \"XXXTENTACION - One Minute [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: one minute \\nCyHi reference track. Likely made after the mumble version as the lyrics are more similar to the release than the original. Leaked along with the Protools sessions.\", \"date\": 16110144, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fb5fe1db5cd8d0ca4b662d581936558a\", \"url\": \"https://api.pillowcase.su/api/download/fb5fe1db5cd8d0ca4b662d581936558a\", \"size\": \"3.7 MB\", \"duration\": 188.24}", "aliases": [], "size": "3.7 MB"}]}