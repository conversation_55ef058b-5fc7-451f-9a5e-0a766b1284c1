{"id": "war", "name": "WAR", "description": "A collaborative project between <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> I.D.. Sessions for the project began around April/May 2022. On his third Drink Champs interview, <PERSON> referred to these sessions as work for \"their album\". Three songs from the project were played at a party featured on <PERSON>'s Instagram, with the song \"Always\" being previewed in full at YZY Season 9 shortly thereafter. The project was presumably scrapped in the wake of <PERSON>'s numerous antisemitic comments, considering that <PERSON> has previously cancelled projects with <PERSON><PERSON><PERSON> due to <PERSON><PERSON><PERSON>'s radical political views. However, insiders have stated the project is still being worked on, and was worked on in 2023.", "backgroundColor": "rgb(0, 0, 0)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17GEZoagYbgllAuQCjwfGJ1aVPiZxzXP4ly3z51bHC1q6vofT_0x3uoukNVtoX1PTqxZUcwVGwy5pyvGhS6uRRF5Q6X2OgZ_q7vs037iCTQVJaOwNUTD_-xb2i4LJuYTr7v9shLzMJqLrCahHY8nuQ?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "bobby-digital", "name": "<PERSON> [V1]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Song made during the <PERSON> sessions. Has mumble. Snippet leaked February 17th, 2023.", "length": "28.68", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/1aff4bbdce37f265b7dba73f070efce2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1aff4bbdce37f265b7dba73f070efce2\", \"key\": \"<PERSON> Digital\", \"title\": \"<PERSON> [V1]\", \"artists\": \"(prod. <PERSON> & No I.D.)\", \"aliases\": [\"Close It All Down\"], \"description\": \"Song made during the <PERSON> sessions. Has mumble. Snippet leaked February 17th, 2023.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6041faee1171ee5ae2d3cd3202aebfe0\", \"url\": \"https://api.pillowcase.su/api/download/6041faee1171ee5ae2d3cd3202aebfe0\", \"size\": \"2.19 MB\", \"duration\": 28.68}", "aliases": ["Close It All Down"], "size": "2.19 MB"}, {"id": "fall-in-all-is-love", "name": "Fall In / All Is Love [V1]", "artists": [], "producers": [], "notes": "OG Filename: fall in/all is love\nHas <PERSON> singing over a piano and string instrumental. May be from the James Blake & No I.D. sessions due to how the metadata and titling is very similar to other songs made during that era, such as \"Let It In\".", "length": "138.82", "fileDate": 16954272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/1267d742c39d89a524db1af0acc0f347", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1267d742c39d89a524db1af0acc0f347\", \"key\": \"Fall In / All Is Love\", \"title\": \"Fall In / All Is Love [V1]\", \"aliases\": [\"Fall In Love\", \"All Is Love\"], \"description\": \"OG Filename: fall in/all is love\\nHas Ye singing over a piano and string instrumental. May be from the James Blake & No I.D. sessions due to how the metadata and titling is very similar to other songs made during that era, such as \\\"Let It In\\\".\", \"date\": 16954272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6443fcd90c6becd774249fc3e8cc532f\", \"url\": \"https://api.pillowcase.su/api/download/6443fcd90c6becd774249fc3e8cc532f\", \"size\": \"2.6 MB\", \"duration\": 138.82}", "aliases": ["Fall In Love", "All Is Love"], "size": "2.6 MB"}, {"id": "let-it-in", "name": "Let It In [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: let it in w/space 2\nSolo <PERSON> song made during the beginning of the sessions between <PERSON> and <PERSON>. Contains open verses, most likely for <PERSON> to record over. Allegedly on a tracklist according to the seller of the song. Interpolates \"A Whiter Shade of Pale\" by <PERSON><PERSON><PERSON>. Original snippet leaked February 22, 2023.", "length": "306.52", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/aaaac654c72408767e3e3c9459048e11", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aaaac654c72408767e3e3c9459048e11\", \"key\": \"Let It In\", \"title\": \"Let It In [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: let it in w/space 2\\n<PERSON>olo <PERSON> song made during the beginning of the sessions between <PERSON> and <PERSON>. Contains open verses, most likely for <PERSON> to record over. Allegedly on a tracklist according to the seller of the song. Interpolates \\\"A Whiter Shade of Pale\\\" by Procol Harum. Original snippet leaked February 22, 2023.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ed1ed854da3666bbc066460deeb60de1\", \"url\": \"https://api.pillowcase.su/api/download/ed1ed854da3666bbc066460deeb60de1\", \"size\": \"5.28 MB\", \"duration\": 306.52}", "aliases": [], "size": "5.28 MB"}, {"id": "let-it-in-4", "name": "Let It In [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "Version of \"Let It In\" with what seems to be freestyle vocals from <PERSON><PERSON><PERSON>. Snippet leaked January 17th, 2025.", "length": "9.93", "fileDate": 17370720, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/ebe96dfc51006829c9d5e48ff2b26fbf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ebe96dfc51006829c9d5e48ff2b26fbf\", \"key\": \"Let It In\", \"title\": \"Let It In [V3]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"Version of \\\"Let It In\\\" with what seems to be freestyle vocals from <PERSON><PERSON><PERSON>. Snippet leaked January 17th, 2025.\", \"date\": 17370720, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bf968e93e11175a0b0a7f60a8228f0ba\", \"url\": \"https://api.pillowcase.su/api/download/bf968e93e11175a0b0a7f60a8228f0ba\", \"size\": \"534 kB\", \"duration\": 9.93}", "aliases": [], "size": "534 kB"}, {"id": "spiky", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: spiky (demo)\nAccording to American, \"Spiky\" is from the WAR sessions. Released by <PERSON> himself on Vault.fm, his unreleased music service. However, he uploaded a version without any <PERSON> vocals on it.", "length": "106.79", "fileDate": 17310240, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/680da62e01cee64381655f7c4a52d762", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/680da62e01cee64381655f7c4a52d762\", \"key\": \"Spiky\", \"title\": \"Spiky\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: spiky (demo)\\nAccording to American, \\\"Spiky\\\" is from the WAR sessions. Released by <PERSON> himself on Vault.fm, his unreleased music service. However, he uploaded a version without any Ye vocals on it.\", \"date\": 17310240, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f9461caaa9c54d597f1506c18a2fe459\", \"url\": \"https://api.pillowcase.su/api/download/f9461caaa9c54d597f1506c18a2fe459\", \"size\": \"2.08 MB\", \"duration\": 106.79}", "aliases": [], "size": "2.08 MB"}, {"id": "talking", "name": "Talking [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "Original version with solo <PERSON> production. Has a slower BPM compared to other versions. Snippet posted November 5th, 2024.", "length": "4.49", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/63b3621b37886b99a96a8713ad2afed0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63b3621b37886b99a96a8713ad2afed0\", \"key\": \"Talking\", \"title\": \"Talking [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"TALKING / ONCE AGAIN\"], \"description\": \"Original version with solo <PERSON> production. Has a slower BPM compared to other versions. Snippet posted November 5th, 2024.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c65a5d27f2129e8b15a0d8c53f1c635\", \"url\": \"https://api.pillowcase.su/api/download/2c65a5d27f2129e8b15a0d8c53f1c635\", \"size\": \"447 kB\", \"duration\": 4.49}", "aliases": ["TALKING / ONCE AGAIN"], "size": "447 kB"}, {"id": "talking-7", "name": "Talking [V2]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Initial <PERSON><PERSON><PERSON> and <PERSON> freestyle for \"Talking\". Said by <PERSON><PERSON><PERSON><PERSON> to be 20 minutes long and \"consists of 10 min <PERSON><PERSON><PERSON> vocals and 10 min <PERSON> vocals\". Snippet leaked January 18th 2025.", "length": "20.65", "fileDate": 17371584, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/cccdb8dc928455b9749cfadb9948a2b0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cccdb8dc928455b9749cfadb9948a2b0\", \"key\": \"Talking\", \"title\": \"Talking [V2]\", \"artists\": \"(prod. <PERSON> & <PERSON> I.D.)\", \"aliases\": [\"TALKING / ONCE AGAIN\"], \"description\": \"<PERSON> <PERSON><PERSON><PERSON> and <PERSON> freestyle for \\\"Talking\\\". Said by <PERSON><PERSON><PERSON><PERSON> to be 20 minutes long and \\\"consists of 10 min <PERSON><PERSON><PERSON> vocals and 10 min <PERSON> vocals\\\". Snippet leaked January 18th 2025.\", \"date\": 17371584, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d736a0ec72156c38e34fa54d29cf0a09\", \"url\": \"https://api.pillowcase.su/api/download/d736a0ec72156c38e34fa54d29cf0a09\", \"size\": \"706 kB\", \"duration\": 20.65}", "aliases": ["TALKING / ONCE AGAIN"], "size": "706 kB"}, {"id": "talking-8", "name": "Talking [V3]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Song made during the <PERSON> sessions. Snippet leaked February 17th, 2023. Seemingly the first version of the song with a faster BPM.", "length": "6.74", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/463f8d907adb842f316cc0e1c5ed1a3f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/463f8d907adb842f316cc0e1c5ed1a3f\", \"key\": \"Talking\", \"title\": \"Talking [V3]\", \"artists\": \"(prod. <PERSON> & No I.D.)\", \"aliases\": [\"TALKING / ONCE AGAIN\"], \"description\": \"Song made during the <PERSON> sessions. Snippet leaked February 17th, 2023. Seemingly the first version of the song with a faster BPM.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b8c4ec4b21cab33a174bffe922508f74\", \"url\": \"https://api.pillowcase.su/api/download/b8c4ec4b21cab33a174bffe922508f74\", \"size\": \"429 kB\", \"duration\": 6.74}", "aliases": ["TALKING / ONCE AGAIN"], "size": "429 kB"}, {"id": "talking-9", "name": "✨ Talking [V4]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Fat Money reference track. Leaked by <PERSON> Money himself as a response to the release of VULTURES 2. Most likely the WAR version of the song as <PERSON> Money does the same flow as the faint <PERSON> mumble on release and has a different chop.", "length": "130.26", "fileDate": 17226432, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/03bb81066365b1bb2445aab489d79d51", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03bb81066365b1bb2445aab489d79d51\", \"key\": \"Talking\", \"title\": \"\\u2728 Talking [V4]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & <PERSON> I.D.)\", \"aliases\": [\"TALKING / ONCE AGAIN\"], \"description\": \"Fat Money reference track. Leaked by <PERSON> himself as a response to the release of VULTURES 2. Most likely the WAR version of the song as Fat Money does the same flow as the faint Ye mumble on release and has a different chop.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a2b85b54d32ad761191e78087cae6801\", \"url\": \"https://api.pillowcase.su/api/download/a2b85b54d32ad761191e78087cae6801\", \"size\": \"2.46 MB\", \"duration\": 130.26}", "aliases": ["TALKING / ONCE AGAIN"], "size": "2.46 MB"}, {"id": "", "name": "??? [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "Instrumental for an early version of \"Always\". Snippet posted November 5th, 2024, in a pack of 20 snippets (full pack being 57) of beats that were meant for WAR and that were solo produced by <PERSON>. Slower BPM than later versions of the song. Most likely did not have a name by now.", "length": "16.82", "fileDate": 17307648, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/6ad457e404851484ecda405c4b712115", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ad457e404851484ecda405c4b712115\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"<PERSON>'s Funeral\", \"What I Would Have Said At Virgil's Funeral\"], \"description\": \"Instrumental for an early version of \\\"Always\\\". Snippet posted November 5th, 2024, in a pack of 20 snippets (full pack being 57) of beats that were meant for WAR and that were solo produced by <PERSON>. Slower BPM than later versions of the song. Most likely did not have a name by now.\", \"date\": 17307648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9e20e9482c8f0ef71094d94855656962\", \"url\": \"https://api.pillowcase.su/api/download/9e20e9482c8f0ef71094d94855656962\", \"size\": \"644 kB\", \"duration\": 16.82}", "aliases": ["<PERSON>'s Funeral", "What I Would Have Said At Virgil's Funeral"], "size": "644 kB"}, {"id": "what-i-would-have-said-at-virgil-s-funeral", "name": "What I Would Have Said At Virgil's Funeral [V2]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Was played at a party featured on <PERSON>'s instagram, and at YZY Season 9, most of the song was played. Has extra vocals in <PERSON>'s third verse which were removed from later versions, also the intro is structured slightly differently. Probably earlier than the version from April 7th despite being played much later. Was played by <PERSON><PERSON><PERSON> on his Instagram Story May 19th, 2023.", "length": "272.71", "fileDate": 16844544, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/a5c7d5d00491b1938cd1c4911d3b4849", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a5c7d5d00491b1938cd1c4911d3b4849\", \"key\": \"What I Would Have Said At <PERSON>'s Funeral\", \"title\": \"What I Would Have Said At <PERSON>'s Funeral [V2]\", \"artists\": \"(prod. <PERSON> & <PERSON> I.D.)\", \"aliases\": [\"Virgil's Funeral\"], \"description\": \"Was played at a party featured on <PERSON>'s instagram, and at YZY Season 9, most of the song was played. Has extra vocals in <PERSON>'s third verse which were removed from later versions, also the intro is structured slightly differently. Probably earlier than the version from April 7th despite being played much later. Was played by <PERSON><PERSON><PERSON> on his Instagram Story May 19th, 2023.\", \"date\": 16844544, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"0a57f73d1d2275f6f129a8a983ce5460\", \"url\": \"https://api.pillowcase.su/api/download/0a57f73d1d2275f6f129a8a983ce5460\", \"size\": \"4.74 MB\", \"duration\": 272.71}", "aliases": ["<PERSON>'s Funeral"], "size": "4.74 MB"}, {"id": "what-i-would-have-said-at-virgil-s-funeral-12", "name": "What I Would Have Said At Virgil's Funeral [V2]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Was played at a party featured on <PERSON>'s instagram, and at YZY Season 9, most of the song was played. Has extra vocals in <PERSON>'s third verse which were removed from later versions, also the intro is structured slightly differently. Probably earlier than the version from April 7th despite being played much later. Was played by <PERSON><PERSON><PERSON> on his Instagram Story May 19th, 2023.", "length": "", "fileDate": 16844544, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/da9f6a1282a450a390db97b627603cc4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/da9f6a1282a450a390db97b627603cc4\", \"key\": \"What I Would Have Said At <PERSON>'s Funeral\", \"title\": \"What I Would Have Said At <PERSON>'s Funeral [V2]\", \"artists\": \"(prod. <PERSON> & <PERSON> I.D.)\", \"aliases\": [\"Virgil's Funeral\"], \"description\": \"Was played at a party featured on <PERSON>'s instagram, and at YZY Season 9, most of the song was played. Has extra vocals in <PERSON>'s third verse which were removed from later versions, also the intro is structured slightly differently. Probably earlier than the version from April 7th despite being played much later. Was played by <PERSON><PERSON><PERSON> on his Instagram Story May 19th, 2023.\", \"date\": 16844544, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["<PERSON>'s Funeral"], "size": ""}, {"id": "what-i-would-have-said-at-virgil-s-funeral-13", "name": "⭐ What I Would Have Said At Virgil's Funeral [V6]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "OG Filename: what I would have said at virgils funeral4\nHas mumble Ye vocals. Was thought to be called \"Always\". Samples \"Crazy Bout My Boifriend (<PERSON><PERSON>)\" by <PERSON><PERSON>. Original CDQ snippets leaked December 2022 & October 16th, 2023, with an HQ snippet also leaking February 17th, 2023.", "length": "272.06", "fileDate": 16983648, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/cd92f39b4b23afa022e36c0b64d90e3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cd92f39b4b23afa022e36c0b64d90e3c\", \"key\": \"What I Would Have Said At <PERSON>'s Funeral\", \"title\": \"\\u2b50 What I Would Have Said At <PERSON>'s Funeral [V6]\", \"artists\": \"(prod. <PERSON> & <PERSON> I.D.)\", \"aliases\": [\"<PERSON>'s Funeral\"], \"description\": \"OG Filename: what I would have said at virgils funeral4\\nHas mumble Ye vocals. Was thought to be called \\\"Always\\\". Sam<PERSON> \\\"Crazy Bout My Boifriend (Loopy)\\\" by <PERSON><PERSON> Nobby. Original CDQ snippets leaked December 2022 & October 16th, 2023, with an HQ snippet also leaking February 17th, 2023.\", \"date\": 16983648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b4d57d105102d2d540e984991ab2f1a4\", \"url\": \"https://api.pillowcase.su/api/download/b4d57d105102d2d540e984991ab2f1a4\", \"size\": \"6.08 MB\", \"duration\": 272.06}", "aliases": ["<PERSON>'s Funeral"], "size": "6.08 MB"}, {"id": "virgil-s-funeral", "name": "<PERSON>'s Funeral [V9]", "artists": ["Vory"], "producers": ["<PERSON>", "No I.D."], "notes": "Later version of \"What I Would Have Said At Virgils Funeral\" with a verse from <PERSON><PERSON>.", "length": "270.68", "fileDate": 16960320, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/9ff3c57df342643100b33bcdf94deb99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9ff3c57df342643100b33bcdf94deb99\", \"key\": \"<PERSON>'s Funeral\", \"title\": \"<PERSON>'s Funeral [V9]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON> & No I.D.)\", \"aliases\": [\"What I Would Have Said At Virgil's Funeral\"], \"description\": \"Later version of \\\"What I Would Have Said At Virgils Funeral\\\" with a verse from <PERSON><PERSON>.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"289b08dba75573d78f4279085d1bce64\", \"url\": \"https://api.pillowcase.su/api/download/289b08dba75573d78f4279085d1bce64\", \"size\": \"4.71 MB\", \"duration\": 270.68}", "aliases": ["What I Would Have Said At Virgil's Funeral"], "size": "4.71 MB"}, {"id": "-15", "name": "??? [V1]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Has mumble Ye vocals. Name is technically still unconfirmed, but it's very likely the song is titled \"Never Coming Back.\" Was played at a party featured on <PERSON>'s instagram.", "length": "36.28", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/c94e4d84343a7be6dcdda0706822f75d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c94e4d84343a7be6dcdda0706822f75d\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON> I.D.) \", \"aliases\": [\"Never Coming Back\"], \"description\": \"<PERSON> mumble Ye vocals. Name is technically still unconfirmed, but it's very likely the song is titled \\\"Never Coming Back.\\\" Was played at a party featured on <PERSON>'s instagram.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b5e2862df869a799d5b2dcd7c0f0d89f\", \"url\": \"https://api.pillowcase.su/api/download/b5e2862df869a799d5b2dcd7c0f0d89f\", \"size\": \"2.31 MB\", \"duration\": 36.28}", "aliases": ["Never Coming Back"], "size": "2.31 MB"}, {"id": "-16", "name": "🏆 ???", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Track said by <PERSON><PERSON> to have a \"chiptune organ\". Snippet leaked February 17th, 2023. The chorus and the first Ye verse is seemingly finished with no mumble.", "length": "58.47", "fileDate": 16765920, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/e6e1d7d0c7b1a4c2820033416685ad47", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e6e1d7d0c7b1a4c2820033416685ad47\", \"key\": \"???\", \"title\": \"\\ud83c\\udfc6 ???\", \"artists\": \"(prod. <PERSON> & No I.D.) \", \"aliases\": [\"Quiet\"], \"description\": \"Track said by <PERSON><PERSON> to have a \\\"chiptune organ\\\". Snippet leaked February 17th, 2023. The chorus and the first Ye verse is seemingly finished with no mumble.\", \"date\": 16765920, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1e63f8bd6e863d4ef3ffd06e878ebeaf\", \"url\": \"https://api.pillowcase.su/api/download/1e63f8bd6e863d4ef3ffd06e878ebeaf\", \"size\": \"843 kB\", \"duration\": 58.47}", "aliases": ["Quiet"], "size": "843 kB"}, {"id": "-17", "name": "🏆 ???", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Track said by <PERSON><PERSON> to have a \"chiptune organ\". Snippet leaked February 17th, 2023. The chorus and the first Ye verse is seemingly finished with no mumble.", "length": "28.71", "fileDate": 16765920, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/649b48f1c94a490765c0a03548379343", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/649b48f1c94a490765c0a03548379343\", \"key\": \"???\", \"title\": \"\\ud83c\\udfc6 ???\", \"artists\": \"(prod. <PERSON> & No I.D.) \", \"aliases\": [\"Quiet\"], \"description\": \"Track said by <PERSON><PERSON> to have a \\\"chiptune organ\\\". Snippet leaked February 17th, 2023. The chorus and the first Ye verse is seemingly finished with no mumble.\", \"date\": 16765920, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c4bcf31406c6719bc938b6ab769a4b4b\", \"url\": \"https://api.pillowcase.su/api/download/c4bcf31406c6719bc938b6ab769a4b4b\", \"size\": \"2.19 MB\", \"duration\": 28.71}", "aliases": ["Quiet"], "size": "2.19 MB"}, {"id": "-18", "name": "🏆 ??? [V2]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Name confirmed to not be \"<PERSON>\". Was played at a party featured on <PERSON>'s instagram. Second snippet leaked Jan 21st 2024. has two equally long Ye verses, second one being unheard. Also has a short unheard Ye bridge that is repeated many times throughout the song. The unheard second verse is more than half finished, and the bridge is also basically half finished.", "length": "82.52", "fileDate": 17374176, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/87ccce5bde8152451070483a4c1d7ebb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/87ccce5bde8152451070483a4c1d7ebb\", \"key\": \"???\", \"title\": \"\\ud83c\\udfc6 ??? [V2]\", \"artists\": \"(prod. <PERSON> & No I.D.) \", \"aliases\": [\"This One Here\", \"Time\", \"Showtime\"], \"description\": \"Name confirmed to not be \\\"Showtime\\\". Was played at a party featured on <PERSON>'s instagram. Second snippet leaked Jan 21st 2024. has two equally long Ye verses, second one being unheard. Also has a short unheard Ye bridge that is repeated many times throughout the song. The unheard second verse is more than half finished, and the bridge is also basically half finished.\", \"date\": 17374176, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ed39a56b6db99e3886171088f67fd196\", \"url\": \"https://api.pillowcase.su/api/download/ed39a56b6db99e3886171088f67fd196\", \"size\": \"3.05 MB\", \"duration\": 82.52}", "aliases": ["This One Here", "Time", "Showtime"], "size": "3.05 MB"}, {"id": "-19", "name": "🏆 ??? [V2]", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Name confirmed to not be \"<PERSON>\". Was played at a party featured on <PERSON>'s instagram. Second snippet leaked Jan 21st 2024. has two equally long Ye verses, second one being unheard. Also has a short unheard Ye bridge that is repeated many times throughout the song. The unheard second verse is more than half finished, and the bridge is also basically half finished.", "length": "9.29", "fileDate": 17374176, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/b045776a5b5695985fa5f2ca6ff5df93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b045776a5b5695985fa5f2ca6ff5df93\", \"key\": \"???\", \"title\": \"\\ud83c\\udfc6 ??? [V2]\", \"artists\": \"(prod. <PERSON> & No I.D.) \", \"aliases\": [\"This One Here\", \"Time\", \"Showtime\"], \"description\": \"Name confirmed to not be \\\"Showtime\\\". Was played at a party featured on <PERSON>'s instagram. Second snippet leaked Jan 21st 2024. has two equally long Ye verses, second one being unheard. Also has a short unheard Ye bridge that is repeated many times throughout the song. The unheard second verse is more than half finished, and the bridge is also basically half finished.\", \"date\": 17374176, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"58e4a42566aee28e820bab08ef1292c1\", \"url\": \"https://api.pillowcase.su/api/download/58e4a42566aee28e820bab08ef1292c1\", \"size\": \"523 kB\", \"duration\": 9.29}", "aliases": ["This One Here", "Time", "Showtime"], "size": "523 kB"}, {"id": "-20", "name": "???", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "<PERSON> reference track recorded in <PERSON> collab sessions. Was said to have <PERSON> vocals but this is false.", "length": "10.48", "fileDate": 16963776, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/eb8678d932a10404be27f970efcea135", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eb8678d932a10404be27f970efcea135\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & No I.D.)\", \"description\": \"<PERSON> reference track recorded in <PERSON> collab sessions. Was said to have <PERSON> vocals but this is false.\", \"date\": 16963776, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"722321d9cb8e89bd19605a44a3bbfbd5\", \"url\": \"https://api.pillowcase.su/api/download/722321d9cb8e89bd19605a44a3bbfbd5\", \"size\": \"1.9 MB\", \"duration\": 10.48}", "aliases": [], "size": "1.9 MB"}, {"id": "-21", "name": "???", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Unknown song made during the <PERSON> collab sessions. Samples \"Church Girl\" by <PERSON>. Snippet leaked February 17th, 2023.", "length": "6.74", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/f43e24aa6a263b61c4fc533b8bbf8ade", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f43e24aa6a263b61c4fc533b8bbf8ade\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON> & No I.D.)\", \"aliases\": [\"Church Girl\"], \"description\": \"Unknown song made during the <PERSON> collab sessions. Samples \\\"Church Girl\\\" by Beyonc\\u00e9. Snippet leaked February 17th, 2023.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"376dba4042ec1a9a1b794e436615b388\", \"url\": \"https://api.pillowcase.su/api/download/376dba4042ec1a9a1b794e436615b388\", \"size\": \"429 kB\", \"duration\": 6.74}", "aliases": ["Church Girl"], "size": "429 kB"}, {"id": "-22", "name": "???", "artists": [], "producers": ["<PERSON>", "No I.D."], "notes": "Snippet of a song made during the Donda 2 / James Blake sessions. Snippet leaked February 17th, 2023.", "length": "2.3", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "war", "originalUrl": "https://pillowcase.su/f/7d91f13ddc5b956b78b45641470b8a25", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7d91f13ddc5b956b78b45641470b8a25\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON> & No I.D.)\", \"description\": \"Snippet of a song made during the <PERSON><PERSON> <PERSON> / <PERSON> sessions. Snippet leaked February 17th, 2023.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c7ec6cf71d180e93ec0689c5768cadb1\", \"url\": \"https://api.pillowcase.su/api/download/c7ec6cf71d180e93ec0689c5768cadb1\", \"size\": \"412 kB\", \"duration\": 2.3}", "aliases": [], "size": "412 kB"}]}