{"id": "cruel-summer", "name": "Cruel Summer", "description": "A compilation of new songs from <PERSON><PERSON><PERSON>'s label, G.O.O.D. Music, 2012's Cruel Summer is one of the most collaborative <PERSON><PERSON><PERSON> projects he accomplished. Featuring various collaborations with <PERSON><PERSON><PERSON>, <PERSON>, 2 Chainz, <PERSON>, and many more, this album spawned many big hits, including \"Mercy\" and the remix of the <PERSON> song \"Don't Like.\" This album also marks the first time <PERSON><PERSON><PERSON> would work with <PERSON>, an at-the-time complete unknown with no mixtape to his name.", "backgroundColor": "rgb(133, 133, 133)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17G5om-bH5eoknNdHC7of2shqZoqm2unVvxRJ0nNyp4czAXbakJwupW-ri--PBjZ7lYzAwbHSQWYLDE6NpJotUOa6jkTlIktjvS9cjhvaXJzVuMn20uQdTXLG1pxV0eJm0OB9gLV3KfL5sQ0PrA?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "clique", "name": "✨ Clique (TNGHT Remix)", "artists": [], "producers": ["TNGHT"], "notes": "OG Fileaname: C<PERSON> TNGHT Remix\nTNGHT Remix of the song. Has no <PERSON> Sean verse (only the chorus), and does not have the release intro.", "length": "288.81", "fileDate": 16983648, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/4b6d73182be68e353e73b9baf6870c72", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4b6d73182be68e353e73b9baf6870c72\", \"key\": \"Clique (TNGHT Remix)\", \"title\": \"\\u2728 Clique (TNGHT Remix)\", \"artists\": \"(with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>-<PERSON> & <PERSON>) (prod. TNGHT)\", \"description\": \"OG Fileaname: Clique TNGHT Remix\\nTNGHT Remix of the song. Has no Big Sean verse (only the chorus), and does not have the release intro.\", \"date\": 16983648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5c5b3e0000ff3d56d46e89e00f8f9a52\", \"url\": \"https://api.pillowcase.su/api/download/5c5b3e0000ff3d56d46e89e00f8f9a52\", \"size\": \"6.11 MB\", \"duration\": 288.81}", "aliases": [], "size": "6.11 MB"}, {"id": "cold", "name": "Cold (TNGHT Remix)", "artists": ["<PERSON>", "DJ <PERSON><PERSON><PERSON>"], "producers": ["TNGHT"], "notes": "OG Filename: Cold TNGHT Remix\nTNGHT Remix of the song.", "length": "220.06", "fileDate": 16983648, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/609fab86d651d47af17e56ed8b5830d7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/609fab86d651d47af17e56ed8b5830d7\", \"key\": \"Cold (TNGHT Remix)\", \"title\": \"Cold (TNGHT Remix)\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (feat. DJ <PERSON> & <PERSON>) (prod. TNGHT)\", \"aliases\": [\"Theraflu\"], \"description\": \"OG Filename: Cold TNGHT Remix\\nTNGHT Remix of the song.\", \"date\": 16983648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0d25fb811a50c4005560f644f94db226\", \"url\": \"https://api.pillowcase.su/api/download/0d25fb811a50c4005560f644f94db226\", \"size\": \"5.01 MB\", \"duration\": 220.06}", "aliases": ["Theraflu"], "size": "5.01 MB"}, {"id": "don-t-like-1", "name": "Don't Like.1 [V2]", "artists": [], "producers": ["<PERSON>", "Kanye West", "The Twilite Tone", "<PERSON>"], "notes": "Has no verses from <PERSON><PERSON><PERSON><PERSON> or <PERSON> Sean. Snippet leaked December 22nd, 2022.", "length": "12.16", "fileDate": 16716672, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/9289738528fb2277c8c395e91d11a560", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9289738528fb2277c8c395e91d11a560\", \"key\": \"Don't Like.1\", \"title\": \"Don't Like.1 [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON>, Chief <PERSON><PERSON> & <PERSON><PERSON>a <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, The Twilite Tone & Noah <PERSON>)\", \"description\": \"Has no verses from Jadakiss or Big Sean. Snippet leaked December 22nd, 2022.\", \"date\": 16716672, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4558461fd758a0ab461ba6a8977bee23\", \"url\": \"https://api.pillowcase.su/api/download/4558461fd758a0ab461ba6a8977bee23\", \"size\": \"1.68 MB\", \"duration\": 12.16}", "aliases": [], "size": "1.68 MB"}, {"id": "don-t-like-1-4", "name": "Don't Like.1 [V4]", "artists": ["Chief <PERSON><PERSON>", "Jadakiss"], "producers": ["<PERSON>", "Kanye West", "The Twilite Tone", "<PERSON>"], "notes": "Extended demo with a longer Pusha T verse.", "length": "298.32", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/6d857e31ce3e66270ef35260abdab34a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d857e31ce3e66270ef35260abdab34a\", \"key\": \"Don't Like.1\", \"title\": \"Don't Like.1 [V4]\", \"artists\": \"(with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (feat. Chief <PERSON><PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, The Twilite Tone & Noah <PERSON>)\", \"description\": \"Extended demo with a longer Pusha T verse.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"edb7b60522e28e91a9c15121c80905a6\", \"url\": \"https://api.pillowcase.su/api/download/edb7b60522e28e91a9c15121c80905a6\", \"size\": \"6.26 MB\", \"duration\": 298.32}", "aliases": [], "size": "6.26 MB"}, {"id": "hear-me-now", "name": "Hear Me Now [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: JL - Hear Me Now Ruff 3.24.11\nEarliest known version, solo <PERSON>", "length": "179.11", "fileDate": 17239392, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/b1d8b217d30f5b4957c0ef4a954fda0c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b1d8b217d30f5b4957c0ef4a954fda0c\", \"key\": \"Hear Me Now\", \"title\": \"Hear Me Now [V1]\", \"artists\": \"(with <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Oh My God\", \"End Of It All\"], \"description\": \"OG Filename: JL - Hear Me Now Ruff 3.24.11\\nEarliest known version, solo John Legend\", \"date\": 17239392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0d917ff7f1051a593004f4cea2785299\", \"url\": \"https://api.pillowcase.su/api/download/0d917ff7f1051a593004f4cea2785299\", \"size\": \"4.36 MB\", \"duration\": 179.11}", "aliases": ["Oh My God", "End Of It All"], "size": "4.36 MB"}, {"id": "hear-me-now-6", "name": "Hear Me Now [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: HearMeNow_ja Ref Mix 1\nAnother version, still solo <PERSON>.", "length": "217.61", "fileDate": 17239392, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/b80e6e9cf718123a430efc96774f9a47", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b80e6e9cf718123a430efc96774f9a47\", \"key\": \"Hear Me Now\", \"title\": \"Hear Me Now [V2]\", \"artists\": \"(with <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Oh My God\", \"End Of It All\"], \"description\": \"OG Filename: HearMeNow_ja Ref Mix 1\\nAnother version, still solo <PERSON>.\", \"date\": 17239392, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgba(0, 0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"093bed67b049222bfe57d507c4052f4d\", \"url\": \"https://api.pillowcase.su/api/download/093bed67b049222bfe57d507c4052f4d\", \"size\": \"4.97 MB\", \"duration\": 217.61}", "aliases": ["Oh My God", "End Of It All"], "size": "4.97 MB"}, {"id": "oh-my-god", "name": "Oh My God [V3]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: JL - Oh My God Ruff 12.10.11 (So Appalled Drums)\nVersion with drums from \"So Appalled\".", "length": "223.26", "fileDate": 17239392, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/b446f903464cb153f7eb451852f08972", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b446f903464cb153f7eb451852f08972\", \"key\": \"Oh My God\", \"title\": \"Oh My God [V3]\", \"artists\": \"(with <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Hear Me Now\", \"End Of It All\"], \"description\": \"OG Filename: JL - Oh My God Ruff 12.10.11 (So Appalled Drums)\\nVersion with drums from \\\"So Appalled\\\".\", \"date\": 17239392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d46aa52caf3de6b8d92dd1be0c3e910e\", \"url\": \"https://api.pillowcase.su/api/download/d46aa52caf3de6b8d92dd1be0c3e910e\", \"size\": \"5.06 MB\", \"duration\": 223.26}", "aliases": ["Hear Me Now", "End Of It All"], "size": "5.06 MB"}, {"id": "hear-me-now-8", "name": "Hear Me Now [V4]", "artists": [], "producers": ["Kanye West", "DJ Camper"], "notes": "OG Filename: Hear Me Now Camper Ref (9.11.12)\nHas added production from <PERSON> <PERSON><PERSON>.", "length": "235.91", "fileDate": 17239392, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/fae7ffcfef473f3cfcf00fc505de6687", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fae7ffcfef473f3cfcf00fc505de6687\", \"key\": \"Hear Me Now\", \"title\": \"Hear Me Now [V4]\", \"artists\": \"(with <PERSON>) (prod. <PERSON><PERSON><PERSON> & DJ Camper)\", \"aliases\": [\"Oh My God\", \"End Of It All\"], \"description\": \"OG Filename: Hear Me Now Camper Ref (9.11.12)\\nHas added production from <PERSON>.\", \"date\": 17239392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"27a2927415e3ef1b37be692db375026c\", \"url\": \"https://api.pillowcase.su/api/download/27a2927415e3ef1b37be692db375026c\", \"size\": \"5.26 MB\", \"duration\": 235.91}", "aliases": ["Oh My God", "End Of It All"], "size": "5.26 MB"}, {"id": "oh-my-god-9", "name": "Oh My God [V5]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Oh My God_Rough 5\nUnknown exactly when made. Most likely a test mix for release.", "length": "233.55", "fileDate": 17239392, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/77bfd1545c9ee858ce9f4d74dd8ef4ab", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/77bfd1545c9ee858ce9f4d74dd8ef4ab\", \"key\": \"Oh My God\", \"title\": \"Oh My God [V5]\", \"artists\": \"(with <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Hear Me Now\", \"End Of It All\"], \"description\": \"OG Filename: Oh My God_Rough 5\\nUnknown exactly when made. Most likely a test mix for release.\", \"date\": 17239392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"329eabf5a246e8453c2d97cf2fad897d\", \"url\": \"https://api.pillowcase.su/api/download/329eabf5a246e8453c2d97cf2fad897d\", \"size\": \"5.22 MB\", \"duration\": 233.55}", "aliases": ["Hear Me Now", "End Of It All"], "size": "5.22 MB"}, {"id": "higher", "name": "Higher", "artists": [], "producers": ["Hit-Boy"], "notes": "Earlier version of \"Higher\", features beside The-<PERSON> are unknown.", "length": "11.68", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/4215918f756e7bd20b95612ccaf9013f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4215918f756e7bd20b95612ccaf9013f\", \"key\": \"Higher\", \"title\": \"Higher\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. Hit-Boy)\", \"description\": \"Earlier version of \\\"Higher\\\", features beside The-Dream are unknown.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"81af15d7833f7d4578f2107366a0996e\", \"url\": \"https://api.pillowcase.su/api/download/81af15d7833f7d4578f2107366a0996e\", \"size\": \"1.67 MB\", \"duration\": 11.68}", "aliases": [], "size": "1.67 MB"}, {"id": "new-god-flow", "name": "New God Flow [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Has a slightly different beat and is a Pusha T solo, rapping <PERSON><PERSON><PERSON>'s lines that take place during <PERSON><PERSON><PERSON> verses. Debuted in the Cruel Summer film", "length": "193.8", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/24929afde38adaba91505bedea835465", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/24929afde38adaba91505bedea835465\", \"key\": \"New God Flow\", \"title\": \"New God Flow [V1]\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Has a slightly different beat and is a Pusha T solo, rapping <PERSON><PERSON><PERSON>'s lines that take place during <PERSON><PERSON><PERSON> verses. Debuted in the Cruel Summer film\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5fdc65bf52be458d6ec5754c2233d213\", \"url\": \"https://api.pillowcase.su/api/download/5fdc65bf52be458d6ec5754c2233d213\", \"size\": \"3.04 MB\", \"duration\": 193.8}", "aliases": [], "size": "3.04 MB"}, {"id": "new-god-flow-12", "name": "New God Flow [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>", "Boogz", "Tapez"], "notes": "OG Filename: New God Flow 14.3\nDemo, has a slightly different mix.", "length": "294.06", "fileDate": 16717536, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/4bab33fe2035fb17a0049b86fc580d52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bab33fe2035fb17a0049b86fc580d52\", \"key\": \"New God Flow\", \"title\": \"New God Flow [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & Bo<PERSON>z & Ta<PERSON>z)\", \"description\": \"OG Filename: New God Flow 14.3\\nDemo, has a slightly different mix.\", \"date\": 16717536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1236abac68d4f1f84ec038bd693cd528\", \"url\": \"https://api.pillowcase.su/api/download/1236abac68d4f1f84ec038bd693cd528\", \"size\": \"6.19 MB\", \"duration\": 294.06}", "aliases": [], "size": "6.19 MB"}, {"id": "no-more", "name": "No More", "artists": [], "producers": [], "notes": "Song previewed in the Cruel Summer movie, but never released. <PERSON><PERSON><PERSON> said that <PERSON> was the vocalist, but he isn't.", "length": "159.89", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/a53c5b913e70be2d22818eb8e660779d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a53c5b913e70be2d22818eb8e660779d\", \"key\": \"No More\", \"title\": \"No More\", \"artists\": \"(with <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"<PERSON> previewed in the Cruel Summer movie, but never released. <PERSON><PERSON><PERSON> said that <PERSON> was the vocalist, but he isn't.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"fac88288d427df83a7826db465484fd4\", \"url\": \"https://api.pillowcase.su/api/download/fac88288d427df83a7826db465484fd4\", \"size\": \"4.05 MB\", \"duration\": 159.89}", "aliases": [], "size": "4.05 MB"}, {"id": "one-night-star", "name": "One Night Star", "artists": [], "producers": [], "notes": "OG Filename: Kanye1 reference\nLQ voice memo freestyle. An unknown voice can be heard at 1:06.", "length": "144.12", "fileDate": 15710976, "leakDate": "", "availableLength": "OG File", "quality": "Low Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/45bdb219d9c648bab5d4463d79a65795", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/45bdb219d9c648bab5d4463d79a65795\", \"key\": \"One Night Star\", \"title\": \"One Night Star\", \"artists\": \"(with <PERSON><PERSON><PERSON> & ???)\", \"description\": \"OG Filename: Kanye1 reference\\nLQ voice memo freestyle. An unknown voice can be heard at 1:06.\", \"date\": 15710976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"f9049f7d5f4b4df06c6aa69221dfbf2a\", \"url\": \"https://api.pillowcase.su/api/download/f9049f7d5f4b4df06c6aa69221dfbf2a\", \"size\": \"2.64 MB\", \"duration\": 144.12}", "aliases": [], "size": "2.64 MB"}, {"id": "sin-city", "name": "<PERSON> - Sin City [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "<PERSON> solo version featuring a different chorus from <PERSON> as well as an additional verse from <PERSON>. His first verse also features different lyrics from the final. The pacing and instrumental of the song are also different. Meant for <PERSON>'s Owl Pharaoh album according to <PERSON><PERSON>.", "length": "163.58", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/2baba9aa5d844b3d79a892dc50f22f3a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2baba9aa5d844b3d79a892dc50f22f3a\", \"key\": \"Sin City\", \"title\": \"<PERSON> - <PERSON> [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"<PERSON> solo version featuring a different chorus from <PERSON> as well as an additional verse from <PERSON>. His first verse also features different lyrics from the final. The pacing and instrumental of the song are also different. Meant for <PERSON>'s Owl Pharaoh album according to Alek.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"626b4d1bd605820ff573c48de8986dd5\", \"url\": \"https://api.pillowcase.su/api/download/626b4d1bd605820ff573c48de8986dd5\", \"size\": \"4.1 MB\", \"duration\": 163.58}", "aliases": [], "size": "4.1 MB"}, {"id": "sin-city-16", "name": "Sin City [V3]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "<PERSON> solo version and open verse section. <PERSON> verse lacks adlibs and effects in released version. Probably not still <PERSON>' song since <PERSON><PERSON> has been added. Leaked in 2013, however any version containing more than the first minute of the song was subsequently was lost. Thankfully, the full version resurfaced in 2021.", "length": "268.69", "fileDate": 16166304, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/88f8c7197a0cc493a93572d739b4cbb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/88f8c7197a0cc493a93572d739b4cbb0\", \"key\": \"Sin City\", \"title\": \"Sin City [V3]\", \"artists\": \"(with <PERSON><PERSON>) (feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"<PERSON> solo version and open verse section. <PERSON> verse lacks adlibs and effects in released version. Probably not still <PERSON>' song since <PERSON><PERSON> has been added. Leaked in 2013, however any version containing more than the first minute of the song was subsequently was lost. Thankfully, the full version resurfaced in 2021.\", \"date\": 16166304, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7220a10efe91654437009b4b0e70ecb4\", \"url\": \"https://api.pillowcase.su/api/download/7220a10efe91654437009b4b0e70ecb4\", \"size\": \"5.79 MB\", \"duration\": 268.69}", "aliases": [], "size": "5.79 MB"}, {"id": "sin-city-17", "name": "Sin City [V4]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "TBHits"], "notes": "Poetry remix that leaked a few weeks before Cruel Summer dropped, featuring a longer intro from <PERSON>.", "length": "178.81", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/d86944d771622e141513e6ddfe38a02f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d86944d771622e141513e6ddfe38a02f\", \"key\": \"Sin City\", \"title\": \"Sin City [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> & TBHits)\", \"description\": \"Poetry remix that leaked a few weeks before Cruel Summer dropped, featuring a longer intro from <PERSON><PERSON>\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9619f7d4e538add4d9ad0603c1335021\", \"url\": \"https://api.pillowcase.su/api/download/9619f7d4e538add4d9ad0603c1335021\", \"size\": \"4.35 MB\", \"duration\": 178.81}", "aliases": [], "size": "4.35 MB"}, {"id": "the-morning", "name": "The Morning", "artists": ["<PERSON><PERSON><PERSON>", "2 Chainz"], "producers": [], "notes": "Has a different beat and longer 2 Chainz verse. Snippet leaked March 30th, 2023.", "length": "14.94", "fileDate": 16801344, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/399cab2d3cdfe4d9f0ce79b992d63424", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/399cab2d3cdfe4d9f0ce79b992d63424\", \"key\": \"The Morning\", \"title\": \"The Morning\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>, 2 <PERSON><PERSON>)\", \"description\": \"Has a different beat and longer 2 Chainz verse. Snippet leaked March 30th, 2023.\", \"date\": 16801344, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"afd20f7cc00e0ce35ad3bd44d641a587\", \"url\": \"https://api.pillowcase.su/api/download/afd20f7cc00e0ce35ad3bd44d641a587\", \"size\": \"1.73 MB\", \"duration\": 14.94}", "aliases": [], "size": "1.73 MB"}, {"id": "the-morning-19", "name": "The Morning (<PERSON><PERSON><PERSON> Remix)", "artists": ["Kanye West"], "producers": ["Lunice"], "notes": "Official <PERSON><PERSON><PERSON> remix.", "length": "200.53", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/edd309299bf6f10ce5e397ffa3d1cc9d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edd309299bf6f10ce5e397ffa3d1cc9d\", \"key\": \"The Morning (Lunice Remix)\", \"title\": \"The Morning (Lunic<PERSON> Remix)\", \"artists\": \"(with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. Lu<PERSON><PERSON>)\", \"description\": \"Official Lunice remix.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a29cac3403a4e5b11bbf6b90d9b63fdd\", \"url\": \"https://api.pillowcase.su/api/download/a29cac3403a4e5b11bbf6b90d9b63fdd\", \"size\": \"4.7 MB\", \"duration\": 200.53}", "aliases": [], "size": "4.7 MB"}, {"id": "the-one", "name": "The One", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Has different lyrics from <PERSON><PERSON>. Any other features on this version are unknown.", "length": "83.12", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/d8c77ff15d1cfb3a5f6245627d5e0128", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d8c77ff15d1cfb3a5f6245627d5e0128\", \"key\": \"The One\", \"title\": \"The One\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"Has different lyrics from <PERSON><PERSON>. Any other features on this version are unknown.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3fb9db143e7b10fce3e61f5f5eadeea1\", \"url\": \"https://api.pillowcase.su/api/download/3fb9db143e7b10fce3e61f5f5eadeea1\", \"size\": \"2.82 MB\", \"duration\": 83.12}", "aliases": [], "size": "2.82 MB"}, {"id": "theraflu", "name": "Theraflu [V1]", "artists": ["<PERSON>", "DJ <PERSON><PERSON><PERSON>"], "producers": ["Hit-Boy"], "notes": "Version with a different instrumental. Was used by <PERSON><PERSON><PERSON> for several performances, so it is often considered to be the tour version of \"Cold\". Samples \"Cold As Ice\" by <PERSON><PERSON>.", "length": "12.25", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/c0e15d857bba2884281bee3a07d2c8a2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0e15d857bba2884281bee3a07d2c8a2\", \"key\": \"Theraflu\", \"title\": \"Theraflu [V1]\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (feat. DJ <PERSON> & <PERSON>) (prod. Hit-Boy)\", \"aliases\": [\"Cold\"], \"description\": \"Version with a different instrumental. Was used by <PERSON><PERSON><PERSON> for several performances, so it is often considered to be the tour version of \\\"Cold\\\". <PERSON><PERSON> \\\"Cold As Ice\\\" by Foreigner.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a1bb13e68a8a7d0ab14c2a317361818e\", \"url\": \"https://api.pillowcase.su/api/download/a1bb13e68a8a7d0ab14c2a317361818e\", \"size\": \"1.68 MB\", \"duration\": 12.25}", "aliases": ["Cold"], "size": "1.68 MB"}, {"id": "theraflu-22", "name": "Theraflu [V2]", "artists": ["<PERSON>", "DJ <PERSON><PERSON><PERSON>"], "producers": ["Hit-Boy"], "notes": "OG Filename: THERAFLU\nOG file to the single version.", "length": "219.21", "fileDate": 16716672, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/e03fab8464acd4714341cd1bfe550bfd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e03fab8464acd4714341cd1bfe550bfd\", \"key\": \"Theraflu\", \"title\": \"Theraflu [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (feat. DJ <PERSON> & <PERSON>) (prod. Hit-Boy)\", \"aliases\": [\"Cold\"], \"description\": \"OG Filename: THERAFLU\\nOG file to the single version.\", \"date\": 16716672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3f9ec00db55650a6e8ad8e7ad08023f8\", \"url\": \"https://api.pillowcase.su/api/download/3f9ec00db55650a6e8ad8e7ad08023f8\", \"size\": \"4.99 MB\", \"duration\": 219.21}", "aliases": ["Cold"], "size": "4.99 MB"}, {"id": "you-missed-it", "name": "You Missed It [V2]", "artists": [], "producers": ["Hit-Boy"], "notes": "OG Filename: <PERSON>L_KW 3 - FOR TONY (with Vox Ref)\n2012 version, very similar to the WTT version except the mix is different and the open verse is cut. Song was later released by <PERSON> on his album <PERSON> or the Fool: An Opera Volume I as \"I Know You Missed It\", still featuring some <PERSON> vocals.", "length": "71.99", "fileDate": 16731360, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/7828b8499c715ff2765ea683f7db6f0d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7828b8499c715ff2765ea683f7db6f0d\", \"key\": \"<PERSON> Missed It\", \"title\": \"You Missed It [V2]\", \"artists\": \"(ref. <PERSON>) (prod. Hit-Boy)\", \"aliases\": [\"I Know You Missed It\"], \"description\": \"OG Filename: JL_KW 3 - FOR TONY (with Vox Ref)\\n2012 version, very similar to the WTT version except the mix is different and the open verse is cut. <PERSON> was later released by <PERSON> on his album <PERSON> or the Fool: An Opera Volume I as \\\"I Know You Missed It\\\", still featuring some <PERSON> vocals.\", \"date\": 16731360, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0874d2779df51e77f812f91a433ac9a9\", \"url\": \"https://api.pillowcase.su/api/download/0874d2779df51e77f812f91a433ac9a9\", \"size\": \"2.64 MB\", \"duration\": 71.99}", "aliases": ["I Know You Missed It"], "size": "2.64 MB"}, {"id": "", "name": "???", "artists": [], "producers": ["Kanye West"], "notes": "A finished Cruel Summer song, with production from <PERSON><PERSON><PERSON>. Title is currently unknown. Likely recorded January 26, 2011 per a tweet from <PERSON>. Snippets leaked October 3rd, 2022 & January 26th, 2023.", "length": "13.82", "fileDate": 16746912, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/5f191d60a82ed1add59b124acf151774", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f191d60a82ed1add59b124acf151774\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(with <PERSON>, <PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Why\"], \"description\": \"A finished Cruel Summer song, with production from <PERSON><PERSON><PERSON>. Title is currently unknown. Likely recorded January 26, 2011 per a tweet from <PERSON>. Snippets leaked October 3rd, 2022 & January 26th, 2023.\", \"date\": 16746912, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"23ade9351c5f8faf3968f19c73e287aa\", \"url\": \"https://api.pillowcase.su/api/download/23ade9351c5f8faf3968f19c73e287aa\", \"size\": \"1.71 MB\", \"duration\": 13.82}", "aliases": ["Why"], "size": "1.71 MB"}, {"id": "-25", "name": "???", "artists": [], "producers": ["Kanye West"], "notes": "A finished Cruel Summer song, with production from <PERSON><PERSON><PERSON>. Title is currently unknown. Likely recorded January 26, 2011 per a tweet from <PERSON>. Snippets leaked October 3rd, 2022 & January 26th, 2023.", "length": "32.54", "fileDate": 16746912, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/93c5ca3101395f9f0ce5c605dcd9dc15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/93c5ca3101395f9f0ce5c605dcd9dc15\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(with <PERSON>, <PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Why\"], \"description\": \"A finished Cruel Summer song, with production from <PERSON><PERSON><PERSON>. Title is currently unknown. Likely recorded January 26, 2011 per a tweet from <PERSON>. Snippets leaked October 3rd, 2022 & January 26th, 2023.\", \"date\": 16746912, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"900fbcf4dfec7f7414e01caf69855784\", \"url\": \"https://api.pillowcase.su/api/download/900fbcf4dfec7f7414e01caf69855784\", \"size\": \"2.01 MB\", \"duration\": 32.54}", "aliases": ["Why"], "size": "2.01 MB"}, {"id": "-26", "name": "???", "artists": [], "producers": ["<PERSON>"], "notes": "Cruel Summer-era beat. Possibly never recorded on. Snippet uploaded to <PERSON>'s YouTube channel.", "length": "14.73", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/a5e14b7a89d0c26bd5b8107f05f2c63f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a5e14b7a89d0c26bd5b8107f05f2c63f\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"Cruel Summer-era beat. Possibly never recorded on. Snippet uploaded to <PERSON>'s YouTube channel.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"033366c31effbeb54d8c6f8973de6385\", \"url\": \"https://api.pillowcase.su/api/download/033366c31effbeb54d8c6f8973de6385\", \"size\": \"1.6 MB\", \"duration\": 14.73}", "aliases": [], "size": "1.6 MB"}, {"id": "-27", "name": "???", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: KanyeIdea.16bar\nShort instrumental idea made by <PERSON><PERSON><PERSON>. Unknown if he ever recorded for it. Was exported with Logic Pro 9.1.7 which came out around March 2012.", "length": "46.89", "fileDate": 16877376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/674a2b2babffe12b6f3fea96674e18a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/674a2b2babffe12b6f3fea96674e18a0\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Ka<PERSON>eIdea.16bar\\nShort instrumental idea made by <PERSON><PERSON><PERSON>. Unknown if he ever recorded for it. Was exported with Logic Pro 9.1.7 which came out around March 2012.\", \"date\": 16877376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"837c2722341d4ab4a88ae18b06d8ce2f\", \"url\": \"https://api.pillowcase.su/api/download/837c2722341d4ab4a88ae18b06d8ce2f\", \"size\": \"2.24 MB\", \"duration\": 46.89}", "aliases": [], "size": "2.24 MB"}, {"id": "enjoy-the-pain", "name": "⭐ John Legend - <PERSON><PERSON> [V2]", "artists": [], "producers": [], "notes": "OG Filename: Enjoy the Pain_Kanye Ref Vocal\nCruel Summer throwaway.", "length": "210.79", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/0ab1b931454bc556f74ec2b0529ead80", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0ab1b931454bc556f74ec2b0529ead80\", \"key\": \"Enjoy The Pain\", \"title\": \"\\u2b50 <PERSON> Legend - Enjoy The Pain [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Enjoy the Pain_Kanye Ref Vocal\\nCruel Summer throwaway.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fdc4038d543dbca7b902c98477c2e8c8\", \"url\": \"https://api.pillowcase.su/api/download/fdc4038d543dbca7b902c98477c2e8c8\", \"size\": \"4.86 MB\", \"duration\": 210.79}", "aliases": [], "size": "4.86 MB"}, {"id": "get-comfortable", "name": "<PERSON> - Get Comfortable [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> Track Ruff 12.11.11\nMumble Kanye reference track for John <PERSON>. Samples \"Love What Happened Here\" by <PERSON>.", "length": "334.37", "fileDate": 16772832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/90f4076bf3531d9daa8db5cc6760020f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/90f4076bf3531d9daa8db5cc6760020f\", \"key\": \"Get Comfortable\", \"title\": \"<PERSON> - Get Comfortable [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON> Track Ruff 12.11.11\\nMumble Kanye reference track for John <PERSON>. Samples \\\"Love What Happened Here\\\" by <PERSON>.\", \"date\": 16772832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3ae63a718952c5e8f12ac7a045badc59\", \"url\": \"https://api.pillowcase.su/api/download/3ae63a718952c5e8f12ac7a045badc59\", \"size\": \"6.84 MB\", \"duration\": 334.37}", "aliases": [], "size": "6.84 MB"}, {"id": "get-comfortable-30", "name": "<PERSON> - Get Comfortable [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Get Comfortable Ref (9.9.12)\nSolo John Legend, intended for his 2013 album Love In The Future.", "length": "239.38", "fileDate": 16776288, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/882677292700d15e73193fd524e50c12", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/882677292700d15e73193fd524e50c12\", \"key\": \"Get Comfortable\", \"title\": \"<PERSON> - Get Comfortable [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: Get Comfortable Ref (9.9.12)\\nSolo John Legend, intended for his 2013 album Love In The Future.\", \"date\": 16776288, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"65c41b25124f2c2869427b68f7a47502\", \"url\": \"https://api.pillowcase.su/api/download/65c41b25124f2c2869427b68f7a47502\", \"size\": \"5.32 MB\", \"duration\": 239.38}", "aliases": [], "size": "5.32 MB"}, {"id": "on-fire", "name": "<PERSON> - On Fire [V2]", "artists": [], "producers": [], "notes": "OG Filename: JL - On Fire Ruff (12.9.11)\nVersion with <PERSON><PERSON><PERSON> and <PERSON> freestyling. Mostly mumble. Unconfirmed, but most likely produced by <PERSON><PERSON><PERSON> too. Meant for <PERSON>'s 2013 album Love In The Future.", "length": "326.85", "fileDate": 16842816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/c6aa1bdf4ead545ce5ed178e5714d09a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c6aa1bdf4ead545ce5ed178e5714d09a\", \"key\": \"On Fire\", \"title\": \"<PERSON> - On Fire [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: JL - On Fire Ruff (12.9.11)\\nVersion with <PERSON><PERSON><PERSON> and <PERSON> freestyling. Mostly mumble. Unconfirmed, but most likely produced by <PERSON><PERSON><PERSON> too. Meant for <PERSON>'s 2013 album Love In The Future.\", \"date\": 16842816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c55c78559272aff03e434738c0610d65\", \"url\": \"https://api.pillowcase.su/api/download/c55c78559272aff03e434738c0610d65\", \"size\": \"6.72 MB\", \"duration\": 326.85}", "aliases": [], "size": "6.72 MB"}, {"id": "on-fire-32", "name": "<PERSON> - On Fire [V3]", "artists": [], "producers": [], "notes": "OG Filename: On Fire Rough 2\nVersion that is close to being finished with backing vocals from a \"random girl\", a melody switch and a guitar solo. Does not have any <PERSON><PERSON><PERSON> vocals.", "length": "334.95", "fileDate": 16842816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/e3914b9e5c34954fb678a7922c12b3cd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e3914b9e5c34954fb678a7922c12b3cd\", \"key\": \"On Fire\", \"title\": \"John <PERSON> - On Fire [V3]\", \"artists\": \"(with ???)\", \"description\": \"OG Filename: On Fire Rough 2\\nVersion that is close to being finished with backing vocals from a \\\"random girl\\\", a melody switch and a guitar solo. Does not have any Kanye vocals.\", \"date\": 16842816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"62ae9391aeae6edc7b3a22615e944c4f\", \"url\": \"https://api.pillowcase.su/api/download/62ae9391aeae6edc7b3a22615e944c4f\", \"size\": \"6.85 MB\", \"duration\": 334.95}", "aliases": [], "size": "6.85 MB"}, {"id": "on-fire-33", "name": "<PERSON> - On Fire [V4]", "artists": [], "producers": [], "notes": "OG Filename: On Fire KW Ref @ 88 BPM (9.9.12)\nKanye freestyle version with autotune. There are no <PERSON> vocals.", "length": "130.83", "fileDate": 16842816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/2752d41757688fdc27bd154f7e5e5045", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2752d41757688fdc27bd154f7e5e5045\", \"key\": \"On Fire\", \"title\": \"John <PERSON> - On Fire [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: On Fire KW Ref @ 88 BPM (9.9.12)\\nKanye freestyle version with autotune. There are no <PERSON> vocals.\", \"date\": 16842816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ecc4038ef118c3e5ed96456b1fd4b49\", \"url\": \"https://api.pillowcase.su/api/download/5ecc4038ef118c3e5ed96456b1fd4b49\", \"size\": \"3.58 MB\", \"duration\": 130.83}", "aliases": [], "size": "3.58 MB"}, {"id": "whoa-oh-oh", "name": "RZA - <PERSON><PERSON> Oh Oh [V1]", "artists": [], "producers": ["RZA", "Kanye West"], "notes": "OG Filename: JL - <PERSON><PERSON> Oh Oh Ruff 12.9.11\nJohn Legend reference track.", "length": "233.68", "fileDate": 16772832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/7b3b33a6ef7313aa52ba93f3854e6ff1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7b3b33a6ef7313aa52ba93f3854e6ff1\", \"key\": \"Whoa Oh Oh\", \"title\": \"<PERSON>ZA - Who<PERSON> Oh Oh [V1]\", \"artists\": \"(ref. <PERSON>) (prod. RZA & Kanye West)\", \"aliases\": [\"White Dress\"], \"description\": \"OG Filename: JL - Who<PERSON> Oh Oh Ruff 12.9.11\\nJohn Legend reference track.\", \"date\": 16772832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fa4380891c796427d9aab85253c523d8\", \"url\": \"https://api.pillowcase.su/api/download/fa4380891c796427d9aab85253c523d8\", \"size\": \"5.23 MB\", \"duration\": 233.68}", "aliases": ["White Dress"], "size": "5.23 MB"}, {"id": "white-dress", "name": "RZA - <PERSON> Dress [V2]", "artists": ["Kanye West"], "producers": ["RZA", "Kanye West"], "notes": "Demo version of \"White Dress\" with some mumble.", "length": "215.63", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/fdf799feec35dc559a3454ec6d456593", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fdf799feec35dc559a3454ec6d456593\", \"key\": \"White Dress\", \"title\": \"RZA - White Dress [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. RZA & Kany<PERSON> West)\", \"description\": \"Demo version of \\\"White Dress\\\" with some mumble.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a8d3d976e8d7a0a189c20db9ef4a7317\", \"url\": \"https://api.pillowcase.su/api/download/a8d3d976e8d7a0a189c20db9ef4a7317\", \"size\": \"4.94 MB\", \"duration\": 215.63}", "aliases": [], "size": "4.94 MB"}, {"id": "skyscrapers", "name": "Swizz Beatz - Skyscrapers [V3]", "artists": ["Kanye West", "<PERSON><PERSON>"], "producers": ["Swizz Beatz", "Musicman <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "notes": "Unreleased track from S<PERSON>zz Beatz's scrapped album Haute Living. Cuts down the <PERSON><PERSON><PERSON> vocals, with only one verse in the final song. Released as a DMX track in 2021, without <PERSON><PERSON><PERSON>.", "length": "213.37", "fileDate": 15695424, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-summer", "originalUrl": "https://pillowcase.su/f/0a0d03bf8fff7c1136e5735bec49122f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a0d03bf8fff7c1136e5735bec49122f\", \"key\": \"Skyscrapers\", \"title\": \"Swizz Beatz - Skyscrapers [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Unreleased track from <PERSON><PERSON><PERSON> Beatz's scrapped album Haute Living. Cuts down the <PERSON><PERSON><PERSON> vocals, with only one verse in the final song. Released as a DMX track in 2021, without <PERSON><PERSON><PERSON>.\", \"date\": 15695424, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"84dd7d4a9ec9ea70a3d08002e58b51a4\", \"url\": \"https://api.pillowcase.su/api/download/84dd7d4a9ec9ea70a3d08002e58b51a4\", \"size\": \"4.9 MB\", \"duration\": 213.37}", "aliases": [], "size": "4.9 MB"}, {"id": "skyscrapers-37", "name": "Swizz Beatz - Skyscrapers [V4]", "artists": ["Kanye West", "<PERSON><PERSON>"], "producers": ["Swizz Beatz", "Musicman <PERSON>", "<PERSON>", "<PERSON><PERSON>"], "notes": "Version previewed by Swizz Beatz. Has a different <PERSON><PERSON><PERSON> vocal take.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "cruel-summer", "originalUrl": "https://www.youtube.com/watch?v=pgIJDBKKrz8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=pgIJDBKKrz8\", \"key\": \"Skyscrapers\", \"title\": \"Swizz Beatz - Skyscrapers [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"Version previewed by Swizz Beatz. Has a different Kanye vocal take.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}]}