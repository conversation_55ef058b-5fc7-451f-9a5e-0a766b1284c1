{"id": "yebu", "name": "YEBU", "description": "Days before <PERSON> was set to hold YZY Season 9, he suddenly changed plans for the event and wore a \"White Lives Matter\" shirt. After that, <PERSON> tweeted about going \"death con 3\" on Jewish people. Following an interview with <PERSON> in which he proclaimed himself a Nazi and claimed he \"liked Hitler,\" many fans and artists gave up supporting <PERSON> and his antics. Following this and an \"apology\" posted on Instagram, <PERSON> went silent for months and began working on new material in Italy with his frequent collaborators. Reportedly being made at the same time as VULTURES. The cover for this era is from the \"Someday We'll All Be Free\" single.", "backgroundColor": "rgb(41, 51, 65)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17H0jJLv6dhDIXcdzhYF_6fN_-Lbf-ZK9FasJ_IrF047ZFboTxNz-2iHnwerpd-P56VVXN66-JW9wlxPbTI8pivCFSMf031Ha-0raz16H8-wZKNw1muf_0-KNc4MbgCo?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "benz-freestyle-1", "name": "🗑️ Benz Freestyle 1", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> Freestyle 1 92.34bpm - 5.4.23\nMumble freestyle with iPhone-ish vocals done during the solo album era, done of the instrumental to \"Much More\" by <PERSON> La Soul.", "length": "77.96", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/26370d5c4f4e647fc4586ee32008f188", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/26370d5c4f4e647fc4586ee32008f188\", \"key\": \"Benz Freestyle 1\", \"title\": \"\\ud83d\\uddd1\\ufe0f Benz Freestyle 1\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: Ye Benz Freestyle 1 92.34bpm - 5.4.23\\nMumble freestyle with iPhone-ish vocals done during the solo album era, done of the instrumental to \\\"Much More\\\" by <PERSON>.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d378d2c98dfe2539dc6e5d186f0a90ed\", \"url\": \"https://api.pillowcase.su/api/download/d378d2c98dfe2539dc6e5d186f0a90ed\", \"size\": \"2.18 MB\", \"duration\": 77.96}", "aliases": [], "size": "2.18 MB"}, {"id": "hood-rat", "name": "Hood Rat [V1]", "artists": [], "producers": [], "notes": "An earlier version of \"Hood Rat\" than the April version shown in <PERSON><PERSON> Foreign's documentary. Said by <PERSON><PERSON> to just be an instrumental.", "length": "11.1", "fileDate": 17072640, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/b5fa2982ec5694800aee89214f066502", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b5fa2982ec5694800aee89214f066502\", \"key\": \"Hood Rat\", \"title\": \"Hood Rat [V1]\", \"aliases\": [\"Hoodrat\"], \"description\": \"An earlier version of \\\"Hood Rat\\\" than the April version shown in Fivio Foreign's documentary. Said by <PERSON><PERSON> to just be an instrumental.\", \"date\": 17072640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cc6881d111047a480cc8e3c9c851f0ac\", \"url\": \"https://api.pillowcase.su/api/download/cc6881d111047a480cc8e3c9c851f0ac\", \"size\": \"1.02 MB\", \"duration\": 11.1}", "aliases": ["<PERSON><PERSON>"], "size": "1.02 MB"}, {"id": "hood-rat-3", "name": "Hood Rat [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: <PERSON> Rat Ye Ref - Ye.88-Keys - 4.20.23\nTrack shown briefly in Fivio Foreign's Without Warning documentary. Made around April 2023. Has mumble vocals. In the same clip in the documentary, <PERSON><PERSON> was seen making a voice memo, however it wasn't for \"Hood Rat\" as it was thought to be. Said by <PERSON><PERSON> to have been played at the private YEBU LP.", "length": "8.33", "fileDate": 17209152, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/76d6c162260cdab528fe62c4a00855f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76d6c162260cdab528fe62c4a00855f5\", \"key\": \"Hood Rat\", \"title\": \"Hood Rat [V2]\", \"artists\": \"(prod. 88-<PERSON>)\", \"aliases\": [\"Hoodrat\"], \"description\": \"OG Filename: <PERSON> Rat Ye Ref - Ye.88-Keys - 4.20.23\\nTrack shown briefly in Fivio Foreign's Without Warning documentary. Made around April 2023. <PERSON> mumble vocals. In the same clip in the documentary, <PERSON><PERSON> was seen making a voice memo, however it wasn't for \\\"Hood Rat\\\" as it was thought to be. Said by <PERSON><PERSON> to have been played at the private YEBU LP.\", \"date\": 17209152, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d704edeafb3ea6eaae6f62af8f127d06\", \"url\": \"https://api.pillowcase.su/api/download/d704edeafb3ea6eaae6f62af8f127d06\", \"size\": \"1.07 MB\", \"duration\": 8.33}", "aliases": ["<PERSON><PERSON>"], "size": "1.07 MB"}, {"id": "hood-rat-4", "name": "Hood Rat [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: <PERSON> Rat Ye Ref - Ye.88-Keys - 4.20.23\nTrack shown briefly in Fivio Foreign's Without Warning documentary. Made around April 2023. Has mumble vocals. In the same clip in the documentary, <PERSON><PERSON> was seen making a voice memo, however it wasn't for \"Hood Rat\" as it was thought to be. Said by <PERSON><PERSON> to have been played at the private YEBU LP.", "length": "10.84", "fileDate": 17209152, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/337acc8eaa3ea8d2351e9a0df6ac5a29", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/337acc8eaa3ea8d2351e9a0df6ac5a29\", \"key\": \"Hood Rat\", \"title\": \"Hood Rat [V2]\", \"artists\": \"(prod. 88-<PERSON>)\", \"aliases\": [\"Hoodrat\"], \"description\": \"OG Filename: <PERSON> Rat Ye Ref - Ye.88-Keys - 4.20.23\\nTrack shown briefly in Fivio Foreign's Without Warning documentary. Made around April 2023. <PERSON> mumble vocals. In the same clip in the documentary, <PERSON><PERSON> was seen making a voice memo, however it wasn't for \\\"Hood Rat\\\" as it was thought to be. Said by <PERSON><PERSON> to have been played at the private YEBU LP.\", \"date\": 17209152, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bf3e41ac0ef0299d849189102641e0e4\", \"url\": \"https://api.pillowcase.su/api/download/bf3e41ac0ef0299d849189102641e0e4\", \"size\": \"1.11 MB\", \"duration\": 10.84}", "aliases": ["<PERSON><PERSON>"], "size": "1.11 MB"}, {"id": "hood-rat-5", "name": "<PERSON> Rat [V3]", "artists": [], "producers": ["88-<PERSON>"], "notes": "\"Hood Rat\" was accidentally played briefly at the YZY FREE Casting Showcase on May 1st, 2023. Unknown if there are any differences compared to other versions.", "length": "2.98", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/df4004e419f65b59e9ea5cf7ff6e3a8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/df4004e419f65b59e9ea5cf7ff6e3a8d\", \"key\": \"Hood Rat\", \"title\": \"Hood Rat [V3]\", \"artists\": \"(prod. 88-Keys)\", \"aliases\": [\"Hoodrat\"], \"description\": \"\\\"Hood Rat\\\" was accidentally played briefly at the YZY FREE Casting Showcase on May 1st, 2023. Unknown if there are any differences compared to other versions.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"68bf65199769eeb596a492a20aa8cc9c\", \"url\": \"https://api.pillowcase.su/api/download/68bf65199769eeb596a492a20aa8cc9c\", \"size\": \"958 kB\", \"duration\": 2.98}", "aliases": ["<PERSON><PERSON>"], "size": "958 kB"}, {"id": "not-inclusive", "name": "Not Inclusive [V1]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: Ty Not Inclusive -ob oxv 10.11.22 idea a 143bpm\nMumble track recorded after YZYSZN 9. Only contains a hook and open verses, likely meant for <PERSON> Doll<PERSON> $ign to record on.", "length": "134.21", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/9a570258a8d157817b1c14a6e5fda03d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9a570258a8d157817b1c14a6e5fda03d\", \"key\": \"Not Inclusive\", \"title\": \"Not Inclusive [V1]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"OG Filename: Ty Not Inclusive -ob oxv 10.11.22 idea a 143bpm\\nMumble track recorded after YZYSZN 9. Only contains a hook and open verses, likely meant for Ty Dolla $ign to record on.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c6063f4e02c57f8580d252f3a5878058\", \"url\": \"https://api.pillowcase.su/api/download/c6063f4e02c57f8580d252f3a5878058\", \"size\": \"3.08 MB\", \"duration\": 134.21}", "aliases": [], "size": "3.08 MB"}, {"id": "love-love-love", "name": "Love Love Love [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "Samples \"Love, Love, Love\" by <PERSON><PERSON>. Said by flab to be from \"the same December [2022] session as \"Someday We'll All Be Free\". Original snippet leaked February 17th, 2023. Another brief snippet was posted by <PERSON><PERSON> on her instagram story during the YZY Casting Call, then other snippets leaked in February and March of 2024. Forceleaked after an attempted blind buy.", "length": "241.87", "fileDate": 17278272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/9018df22d797fe66d1531c478d3e0e0b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9018df22d797fe66d1531c478d3e0e0b\", \"key\": \"Love Love Love\", \"title\": \"Love Love Love [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"<PERSON><PERSON> \\\"Love, Love, Love\\\" by <PERSON><PERSON>. Said by flab to be from \\\"the same December [2022] session as \\\"Someday We'll All Be Free\\\". Original snippet leaked February 17th, 2023. Another brief snippet was posted by <PERSON><PERSON> on her instagram story during the YZY Casting Call, then other snippets leaked in February and March of 2024. Forceleaked after an attempted blind buy.\", \"date\": 17278272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cf6645219812755a7900644985452395\", \"url\": \"https://api.pillowcase.su/api/download/cf6645219812755a7900644985452395\", \"size\": \"4.8 MB\", \"duration\": 241.87}", "aliases": [], "size": "4.8 MB"}, {"id": "love-love-love-8", "name": "Love Love Love [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Consequence reference track for \"Love Love Love\". Likely made in the same session as his \"Someday We'll All Be Free\" ref. Played by Consequence on Instagram Live October 8th, 2024.", "length": "155.71", "fileDate": 17283456, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/267cba7bea9f751919c74a7062400e93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/267cba7bea9f751919c74a7062400e93\", \"key\": \"Love Love Love\", \"title\": \"Love Love Love [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON>)\", \"description\": \"Consequence reference track for \\\"Love Love Love\\\". Likely made in the same session as his \\\"Someday We'll All Be Free\\\" ref. Played by Consequence on Instagram Live October 8th, 2024.\", \"date\": 17283456, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"03dd1c190cc59f60262e76aaefaf55df\", \"url\": \"https://api.pillowcase.su/api/download/03dd1c190cc59f60262e76aaefaf55df\", \"size\": \"3.43 MB\", \"duration\": 155.71}", "aliases": [], "size": "3.43 MB"}, {"id": "someday-we-ll-all-be-free", "name": "Someday We'll All Be Free [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Confirmed to exist by Consequence, unknown when this was made. Has a slightly different instrumental. Played on a Consequence live.", "length": "47.99", "fileDate": 16705440, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/694ca736a111ea309344131fdde7cac2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/694ca736a111ea309344131fdde7cac2\", \"key\": \"Someday We'll All Be Free\", \"title\": \"Someday We'll All Be Free [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON>)\", \"aliases\": [\"Censori Overload\"], \"description\": \"Confirmed to exist by Consequence, unknown when this was made. Has a slightly different instrumental. Played on a Consequence live.\", \"date\": 16705440, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9a1bbf8ea34c5c70408ac21e5c9e68ab\", \"url\": \"https://api.pillowcase.su/api/download/9a1bbf8ea34c5c70408ac21e5c9e68ab\", \"size\": \"1.7 MB\", \"duration\": 47.99}", "aliases": ["Censori Overload"], "size": "1.7 MB"}, {"id": "someday-we-ll-all-be-free-10", "name": "Someday We'll All Be Free [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Confirmed to exist by Consequence, unknown when this was made. Has a slightly different instrumental. Played on a Consequence live.", "length": "", "fileDate": 16705440, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/1e7fba094a690bc9aff1f5d983524f57", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e7fba094a690bc9aff1f5d983524f57\", \"key\": \"Someday We'll All Be Free\", \"title\": \"Someday We'll All Be Free [V2]\", \"artists\": \"(ref. Consequence) (prod. <PERSON>)\", \"aliases\": [\"Censori Overload\"], \"description\": \"Confirmed to exist by Consequence, unknown when this was made. Has a slightly different instrumental. Played on a Consequence live.\", \"date\": 16705440, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Censori Overload"], "size": ""}, {"id": "someday-we-ll-all-be-free-11", "name": "Someday We'll All Be Free [V4]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Someday we'll all be free - Ye Vox.01\nFirst vocal take for \"Someday We'll All Be Free\", at least according to the file name. Has alternate lines.", "length": "123.53", "fileDate": 16954272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/c1696b4ede74bf2fc8b68711c657c10a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1696b4ede74bf2fc8b68711c657c10a\", \"key\": \"Someday We'll All Be Free\", \"title\": \"Someday We'll All Be Free [V4]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Censori Overload\"], \"description\": \"OG Filename: Someday we'll all be free - Ye Vox.01\\nFirst vocal take for \\\"Someday We'll All Be Free\\\", at least according to the file name. Has alternate lines.\", \"date\": 16954272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ce7777ceb548de8cafb0328643676d47\", \"url\": \"https://api.pillowcase.su/api/download/ce7777ceb548de8cafb0328643676d47\", \"size\": \"2.91 MB\", \"duration\": 123.53}", "aliases": ["Censori Overload"], "size": "2.91 MB"}, {"id": "someday-we-ll-all-be-free-12", "name": "Someday We'll All Be Free [V20]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Someday well all be free - Ye Vox.17\nVersion shared via <PERSON>'s Telegram channel. Has slight lyric differences, including \"why you want to leave\", rather than \"she want to leave\". Reposted on <PERSON>'s Instagram story.", "length": "129.77", "fileDate": 16703712, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/2d8a738e9d70bcc17b259f91458eff3d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d8a738e9d70bcc17b259f91458eff3d\", \"key\": \"Someday We'll All Be Free\", \"title\": \"Someday We'll All Be Free [V20]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Censori Overload\"], \"description\": \"OG Filename: Someday well all be free - Ye Vox.17\\nVersion shared via <PERSON>'s Telegram channel. Has slight lyric differences, including \\\"why you want to leave\\\", rather than \\\"she want to leave\\\". Reposted on <PERSON>'s Instagram story.\", \"date\": 16703712, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"61e14aba4647568a0e942cc3a2779111\", \"url\": \"https://api.pillowcase.su/api/download/61e14aba4647568a0e942cc3a2779111\", \"size\": \"3.01 MB\", \"duration\": 129.77}", "aliases": ["Censori Overload"], "size": "3.01 MB"}, {"id": "ticket-to-heaven", "name": "🗑️ Ticket To Heaven [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Ticket to heaven - Ye freestyle.01\nMore finished version. Samples the \"This is my eternal soul\" line from \"God Is\". alongside the beat (which is <PERSON> extracted). Was mentioned by <PERSON>, that he \"heard <PERSON><PERSON> remixing [\"God Is\"]\". Original snippet leaked February 17th, 2023.", "length": "169.2", "fileDate": 17079552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/616c96744510c01a05fed15b0405e63c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/616c96744510c01a05fed15b0405e63c\", \"key\": \"Ticket To Heaven\", \"title\": \"\\ud83d\\uddd1\\ufe0f Ticket To Heaven [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: Ticket to heaven - Ye freestyle.01\\nMore finished version. Samples the \\\"This is my eternal soul\\\" line from \\\"God Is\\\". alongside the beat (which is AI extracted). Was mentioned by <PERSON>, that he \\\"heard Y<PERSON> remixing [\\\"God Is\\\"]\\\". Original snippet leaked February 17th, 2023.\", \"date\": 17079552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fd21947028f821ff002e1b2931a8353f\", \"url\": \"https://api.pillowcase.su/api/download/fd21947028f821ff002e1b2931a8353f\", \"size\": \"2.88 MB\", \"duration\": 169.2}", "aliases": [], "size": "2.88 MB"}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "Unknown song made during sessions from mid-late 2022. Snippet leaked February 17th, 2023.", "length": "2.11", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/af88aa239ada98222eeb090f8b73886a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/af88aa239ada98222eeb090f8b73886a\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown song made during sessions from mid-late 2022. Snippet leaked February 17th, 2023.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e6ce14789843906bfa1829b1cf5e8db7\", \"url\": \"https://api.pillowcase.su/api/download/e6ce14789843906bfa1829b1cf5e8db7\", \"size\": \"969 kB\", \"duration\": 2.11}", "aliases": [], "size": "969 kB"}, {"id": "-15", "name": "???", "artists": [], "producers": [], "notes": "Unknown song made during sessions from mid-late 2022. Snippet leaked February 17th, 2023.", "length": "1.9100000000000001", "fileDate": 16765920, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/07fb0f1dbf5e8ad88d58f1b60d3eb540", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/07fb0f1dbf5e8ad88d58f1b60d3eb540\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown song made during sessions from mid-late 2022. Snippet leaked February 17th, 2023.\", \"date\": 16765920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3b4de91425bd73feceed18426cf03689\", \"url\": \"https://api.pillowcase.su/api/download/3b4de91425bd73feceed18426cf03689\", \"size\": \"950 kB\", \"duration\": 1.9100000000000001}", "aliases": [], "size": "950 kB"}, {"id": "dead", "name": "Future & Lil Durk - Dead [V1]", "artists": [], "producers": ["ATL Jacob"], "notes": "OG Filename: Dead w Durk\nAccording to <PERSON><PERSON>, \"Dead\" was initially made for the Lil Durk & Future collab album, and was given to <PERSON> later on. <PERSON> interpolates \"FOR A NUT\" by Future.", "length": "200.6", "fileDate": 17223840, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/7475d3a444078b2faac78ae92d6e018e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7475d3a444078b2faac78ae92d6e018e\", \"key\": \"Dead\", \"title\": \"<PERSON> & Lil Durk - Dead [V1]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Dead w Durk\\nAccording to <PERSON><PERSON>, \\\"Dead\\\" was initially made for the Lil Durk & Future collab album, and was given to <PERSON> later on. <PERSON> interpolates \\\"FOR A NUT\\\" by Future.\", \"date\": 17223840, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"976d181b19b3dbd1b5a85a1a797c60d0\", \"url\": \"https://api.pillowcase.su/api/download/976d181b19b3dbd1b5a85a1a797c60d0\", \"size\": \"4.15 MB\", \"duration\": 200.6}", "aliases": [], "size": "4.15 MB"}, {"id": "future-bounce", "name": "<PERSON> - FUTURE BOUNCE [V34]", "artists": ["Future", "SZA"], "producers": ["<PERSON>", "BoogzDaBeast", "<PERSON>", "Kanye West", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hudson Mohawke"], "notes": "OG Filename: FBOPENS-V - CD REF 1\nFeatures a different SZA verse than the released version. Filename suggests that the old \"Future Bounce\" name was still inplace until last minute. Original snippet leaked June 9th, 2024. Leaked as part of a groupbuy.", "length": "323.86", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/8ea9ed14c11f44a4b0641e0f4016b450", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ea9ed14c11f44a4b0641e0f4016b450\", \"key\": \"FUTURE BOUNCE\", \"title\": \"<PERSON> - FUTURE BOUNCE [V34]\", \"artists\": \"(feat. Future & SZA) (prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Future Of Sound\", \"Future Sounds\", \"Ultrasounds\", \"TELEKINESIS\"], \"description\": \"OG Filename: FBOPENS-V - CD REF 1\\nFeatures a different SZA verse than the released version. Filename suggests that the old \\\"Future Bounce\\\" name was still inplace until last minute. Original snippet leaked June 9th, 2024. Leaked as part of a groupbuy.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e213332d261df87aca2b4624c0e2b1cd\", \"url\": \"https://api.pillowcase.su/api/download/e213332d261df87aca2b4624c0e2b1cd\", \"size\": \"5.35 MB\", \"duration\": 323.86}", "aliases": ["Future Of Sound", "Future Sounds", "Ultrasounds", "TELEKINESIS"], "size": "5.35 MB"}, {"id": "future-bounce-18", "name": "<PERSON> - FUTURE BOUNCE [V35]", "artists": ["Future", "SZA"], "producers": ["<PERSON>", "BoogzDaBeast", "<PERSON>", "Kanye West", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hudson Mohawke"], "notes": "OG Filename: FBOPENS-V - CD REF 2\nFeatures a different SZA verse than the released version and different mixing. Leaked as part of a groupbuy.", "length": "325.44", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/ee4921bd4d11e8d651e037a358987751", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ee4921bd4d11e8d651e037a358987751\", \"key\": \"FUTURE BOUNCE\", \"title\": \"<PERSON> - FUTURE BOUNCE [V35]\", \"artists\": \"(feat. Future & SZA) (prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Na<PERSON>in & <PERSON>e)\", \"aliases\": [\"Future Of Sound\", \"Future Sounds\", \"Ultrasounds\", \"TELEKINESIS\"], \"description\": \"OG Filename: FBOPENS-V - CD REF 2\\nFeatures a different SZA verse than the released version and different mixing. Leaked as part of a groupbuy.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d7e8f80119443241249f06331c5aaded\", \"url\": \"https://api.pillowcase.su/api/download/d7e8f80119443241249f06331c5aaded\", \"size\": \"5.38 MB\", \"duration\": 325.44}", "aliases": ["Future Of Sound", "Future Sounds", "Ultrasounds", "TELEKINESIS"], "size": "5.38 MB"}, {"id": "dis-bitch-dat-hoe", "name": "Ty Dolla $ign - <PERSON>s Bitch Dat Hoe [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "Ojivolta"], "notes": "OG Filename: - dis bitch dat hoe - (OV HOOK ) - <PERSON><PERSON>\nAccording to <PERSON><PERSON>, <PERSON><PERSON><PERSON> helped <PERSON> rewrite the hook for \"Dis Bitch Dat Hoe\". <PERSON><PERSON><PERSON> likely recorded a reference track for the song, as <PERSON><PERSON><PERSON> was recording other things with <PERSON> around this time. Original snippet leaked November 15th, 2024, with a longer snippet leaking December 22nd, 2024. Leaked after a successful Soakbuy.", "length": "137.93", "fileDate": 17351712, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/00645f4a9c7da208752412fea1ba61d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00645f4a9c7da208752412fea1ba61d8\", \"key\": \"Dis Bitch Dat Hoe\", \"title\": \"Ty Dolla $ign - Dis Bitch Dat Hoe [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Juicy J & Ojivolta)\", \"aliases\": [\"PAY PER VIEW\"], \"description\": \"OG Filename: - dis bitch dat hoe - (OV HOOK ) - <PERSON><PERSON>\\nAccording to <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON> helped <PERSON> rewrite the hook for \\\"Dis Bitch Dat Hoe\\\". <PERSON><PERSON><PERSON> likely recorded a reference track for the song, as <PERSON><PERSON><PERSON> was recording other things with <PERSON> around this time. Original snippet leaked November 15th, 2024, with a longer snippet leaking December 22nd, 2024. Leaked after a successful Soakbuy.\", \"date\": 17351712, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4334b7bb1b27d050c21b48fa52777fe2\", \"url\": \"https://api.pillowcase.su/api/download/4334b7bb1b27d050c21b48fa52777fe2\", \"size\": \"3.14 MB\", \"duration\": 137.93}", "aliases": ["PAY PER VIEW"], "size": "3.14 MB"}, {"id": "beg-forgiveness", "name": "<PERSON>g <PERSON> [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: beg forgiveness [dn]\nThe first known version of \"Beg Forgiveness\". Leaked randomly on Dbree. Samples \"<PERSON>\" by <PERSON> & Valentina. <PERSON> interpolates the sample in later versions. Was originally thought to be from VULTURES 1 era, but acording to <PERSON><PERSON>, \"Beg Forgiveness\" was first made during the solo album sessions, and was played at a private listening event for the album.", "length": "222.9", "fileDate": 17025120, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/e7f5dc25e1a0a82373e1a5d59a207e35", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7f5dc25e1a0a82373e1a5d59a207e35\", \"key\": \"Beg Forgiveness\", \"title\": \"Beg Forgiveness [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: beg forgiveness [dn]\\nThe first known version of \\\"Beg Forgiveness\\\". Leaked randomly on Dbree. Samples \\\"Gabriel\\\" by <PERSON> & Valentina. <PERSON> interpolates the sample in later versions. Was originally thought to be from VULTURES 1 era, but acording to <PERSON><PERSON>, \\\"Beg Forgiveness\\\" was first made during the solo album sessions, and was played at a private listening event for the album.\", \"date\": 17025120, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e397442e42a3efcd887ea84e22bd8253\", \"url\": \"https://api.pillowcase.su/api/download/e397442e42a3efcd887ea84e22bd8253\", \"size\": \"8.58 MB\", \"duration\": 222.9}", "aliases": [], "size": "8.58 MB"}, {"id": "beg-forgiveness-21", "name": "<PERSON><PERSON> [V3]", "artists": [], "producers": ["Digital Nas"], "notes": "<PERSON><PERSON><PERSON> Gray reference track for \"Beg Forgiveness\".", "length": "95.43", "fileDate": 17292960, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/34926afa180b7222ed721a4ef8e04e44", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/34926afa180b7222ed721a4ef8e04e44\", \"key\": \"Beg Forgiveness\", \"title\": \"Beg Forgiveness [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"Bryson Gray reference track for \\\"Beg Forgiveness\\\".\", \"date\": 17292960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"83b280505000b11cc0c0c5056ef126f6\", \"url\": \"https://api.pillowcase.su/api/download/83b280505000b11cc0c0c5056ef126f6\", \"size\": \"1.7 MB\", \"duration\": 95.43}", "aliases": [], "size": "1.7 MB"}, {"id": "beg-forgiveness-22", "name": "<PERSON><PERSON> [V6]", "artists": [], "producers": ["Digital Nas"], "notes": "<PERSON><PERSON> reference track for \"Beg Forgiveness\". Likely recorded prior to <PERSON> recording for the song. Played by <PERSON><PERSON> himself on March 11th 2024. Another snippet was posted September 17th, 2024.", "length": "49.87", "fileDate": 17265312, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/0a6073df76f19db4fb659e11a32fb30f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a6073df76f19db4fb659e11a32fb30f\", \"key\": \"Beg Forgiveness\", \"title\": \"Beg Forgiveness [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"Vory reference track for \\\"Beg Forgiveness\\\". Likely recorded prior to <PERSON> recording for the song. Played by <PERSON><PERSON> himself on March 11th 2024. Another snippet was posted September 17th, 2024.\", \"date\": 17265312, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2013c32bfec884e8fd1e164a8359a39a\", \"url\": \"https://api.pillowcase.su/api/download/2013c32bfec884e8fd1e164a8359a39a\", \"size\": \"5.81 MB\", \"duration\": 49.87}", "aliases": [], "size": "5.81 MB"}, {"id": "beg-forgiveness-23", "name": "<PERSON><PERSON> [V6]", "artists": [], "producers": ["Digital Nas"], "notes": "<PERSON><PERSON> reference track for \"Beg Forgiveness\". Likely recorded prior to <PERSON> recording for the song. Played by <PERSON><PERSON> himself on March 11th 2024. Another snippet was posted September 17th, 2024.", "length": "", "fileDate": 17265312, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/e74a3a96cb76479998ebf43def998d84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e74a3a96cb76479998ebf43def998d84\", \"key\": \"Beg Forgiveness\", \"title\": \"Beg Forgiveness [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"Vory reference track for \\\"Beg Forgiveness\\\". Likely recorded prior to <PERSON> recording for the song. Played by <PERSON><PERSON> himself on March 11th 2024. Another snippet was posted September 17th, 2024.\", \"date\": 17265312, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "beg-forgiveness-24", "name": "<PERSON><PERSON> [V7]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: Beg Forgiveness - QM x Rooga x Bryson\nSecond Rooga reference track for \"Beg Forgiveness\". Rooga raps the the entire verse, but it was also written by <PERSON> and <PERSON><PERSON><PERSON>.", "length": "162.77", "fileDate": 17292960, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/420dac5f16178d06a9905810393f30e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/420dac5f16178d06a9905810393f30e1\", \"key\": \"Beg Forgiveness\", \"title\": \"Beg Forgiveness [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"OG Filename: Beg Forgiveness - QM x Rooga x Bryson\\nSecond Rooga reference track for \\\"Beg Forgiveness\\\". Rooga raps the the entire verse, but it was also written by <PERSON> and <PERSON><PERSON><PERSON>.\", \"date\": 17292960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"20c56de0f186da418b70e266e75ca64f\", \"url\": \"https://api.pillowcase.su/api/download/20c56de0f186da418b70e266e75ca64f\", \"size\": \"7.61 MB\", \"duration\": 162.77}", "aliases": [], "size": "7.61 MB"}, {"id": "dangerous", "name": "Ty Dolla $ign - Dangerous [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "\"Burn\" was originally a Ty Dolla $ign song, shown to <PERSON> when <PERSON> had originally wanted him to executive produce his next album. According to <PERSON><PERSON><PERSON>, <PERSON> was given the track by BEAM. Snippet leaked February 3rd, 2025.", "length": "7.31", "fileDate": 17385408, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/c150e2075ce72748a5836ac0b2163883", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c150e2075ce72748a5836ac0b2163883\", \"key\": \"Dangerous\", \"title\": \"Ty Dolla $ign - Dangerous [V2]\", \"artists\": \"(prod. Ojivo<PERSON>)\", \"aliases\": [\"Burn\"], \"description\": \"\\\"Burn\\\" was originally a Ty Dolla $ign song, shown to <PERSON> when <PERSON> had originally wanted him to executive produce his next album. According to <PERSON><PERSON><PERSON>, <PERSON> was given the track by BEAM. Snippet leaked February 3rd, 2025.\", \"date\": 17385408, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f1ecdf6123de96fb59e21b01ce142399\", \"url\": \"https://api.pillowcase.su/api/download/f1ecdf6123de96fb59e21b01ce142399\", \"size\": \"1.05 MB\", \"duration\": 7.31}", "aliases": ["Burn"], "size": "1.05 MB"}, {"id": "cheesecake", "name": "Ty Dolla $ign - Cheesecake [V1]", "artists": [], "producers": [], "notes": "According to <PERSON><PERSON>, \"Back To Me\" was originally a Ty Dolla $ign song. The instrumental was likely similar to the Bump J version. Unknown when this version is from, and later confirmed to be shown to <PERSON> and later rework during the Japan sessions. Snippet leaked February 3rd, 2025.", "length": "7.5", "fileDate": 17385408, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/86e26e2ce3571d78e6d2ce2ab0711569", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86e26e2ce3571d78e6d2ce2ab0711569\", \"key\": \"Cheesecake\", \"title\": \"Ty Dolla $ign - Cheesecake [V1]\", \"aliases\": [\"BACK 2 ME\", \"BACK TO ME\"], \"description\": \"According to <PERSON><PERSON>, \\\"Back To Me\\\" was originally a Ty Dolla $ign song. The instrumental was likely similar to the Bump J version. Unknown when this version is from, and later confirmed to be shown to <PERSON> and later rework during the Japan sessions. Snippet leaked February 3rd, 2025.\", \"date\": 17385408, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0527bc560dee47df7569bbd699485a7d\", \"url\": \"https://api.pillowcase.su/api/download/0527bc560dee47df7569bbd699485a7d\", \"size\": \"1.06 MB\", \"duration\": 7.5}", "aliases": ["BACK 2 ME", "BACK TO ME"], "size": "1.06 MB"}, {"id": "everybody", "name": "Everybody [V1]", "artists": [], "producers": [], "notes": "According to <PERSON><PERSON>, \"Everybody\" was initially meant for the solo Ye album being worked on prior to VULTURES. Snippets leaked October 20th + 21st, 2024.", "length": "72.88", "fileDate": 17295552, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/1c5e0336e70935d44eeb214d5aa81e7e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c5e0336e70935d44eeb214d5aa81e7e\", \"key\": \"Everybody\", \"title\": \"Everybody [V1]\", \"description\": \"According to <PERSON><PERSON>, \\\"Everybody\\\" was initially meant for the solo Ye album being worked on prior to VULTURES. Snippets leaked October 20th + 21st, 2024.\", \"date\": 17295552, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"90abd0e11da707391a55b2ba0423375f\", \"url\": \"https://api.pillowcase.su/api/download/90abd0e11da707391a55b2ba0423375f\", \"size\": \"2.1 MB\", \"duration\": 72.88}", "aliases": [], "size": "2.1 MB"}, {"id": "hoodrat", "name": "🗑️ Hoodrat [V4]", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> (<PERSON><PERSON><PERSON>)\n<PERSON><PERSON><PERSON> reference track. <PERSON><PERSON><PERSON> says the lines \"Why every female rapper want to be a hoodrat? Is rapping 'bout they vagina the only thing they good at?\" and \"The industry is ran by the LGBT & satanists\".", "length": "55.51", "fileDate": 17292960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/6b23e56e34936f9ecf550ca83d12e28e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6b23e56e34936f9ecf550ca83d12e28e\", \"key\": \"Hood<PERSON>\", \"title\": \"\\ud83d\\uddd1\\ufe0f Hoodrat [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. 88-<PERSON>)\", \"aliases\": [\"Hood Rat\"], \"description\": \"OG Filename: <PERSON><PERSON> (<PERSON><PERSON><PERSON> Rough)\\nBryson Gray reference track. <PERSON><PERSON><PERSON> says the lines \\\"Why every female rapper want to be a hoodrat? Is rapping 'bout they vagina the only thing they good at?\\\" and \\\"The industry is ran by the LGBT & satanists\\\".\", \"date\": 17292960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fdbf31b1a22c9840afc48ba8cac01c51\", \"url\": \"https://api.pillowcase.su/api/download/fdbf31b1a22c9840afc48ba8cac01c51\", \"size\": \"1.82 MB\", \"duration\": 55.51}", "aliases": ["<PERSON>"], "size": "1.82 MB"}, {"id": "paperwork", "name": "Paperwork [V1]", "artists": [], "producers": [], "notes": "Track seen on a tracklist from the Italy sessions. The song originates from Japan sessions according to an insider. The song samples \"Ay Si Ñ<PERSON>ño\" by Rochy RD. LQ snippet leaked January 21st, 2024, with the recording being from July 2023. Another snippet leaked October 19th, 2024.", "length": "8.72", "fileDate": 17292960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/d94ccaf088da1a0e36d2a4454ce965bf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d94ccaf088da1a0e36d2a4454ce965bf\", \"key\": \"Paperwork\", \"title\": \"Paperwork [V1]\", \"description\": \"Track seen on a tracklist from the Italy sessions. The song originates from Japan sessions according to an insider. The song samples \\\"Ay Si \\u00d1i\\u00f1o\\\" by Rochy RD. LQ snippet leaked January 21st, 2024, with the recording being from July 2023. Another snippet leaked October 19th, 2024.\", \"date\": 17292960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6d9f5e0cc35da52f3585119be9697abe\", \"url\": \"https://api.pillowcase.su/api/download/6d9f5e0cc35da52f3585119be9697abe\", \"size\": \"5.15 MB\", \"duration\": 8.72}", "aliases": [], "size": "5.15 MB"}, {"id": "paperwork-30", "name": "Paperwork [V1]", "artists": [], "producers": [], "notes": "Track seen on a tracklist from the Italy sessions. The song originates from Japan sessions according to an insider. The song samples \"Ay Si Ñ<PERSON>ño\" by Rochy RD. LQ snippet leaked January 21st, 2024, with the recording being from July 2023. Another snippet leaked October 19th, 2024.", "length": "3.79", "fileDate": 17292960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/d3e52911f7b1eeff7865aef342eb1015", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d3e52911f7b1eeff7865aef342eb1015\", \"key\": \"Paperwork\", \"title\": \"Paperwork [V1]\", \"description\": \"Track seen on a tracklist from the Italy sessions. The song originates from Japan sessions according to an insider. The song samples \\\"Ay Si \\u00d1i\\u00f1o\\\" by Rochy RD. LQ snippet leaked January 21st, 2024, with the recording being from July 2023. Another snippet leaked October 19th, 2024.\", \"date\": 17292960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d1006d1848f423ba85d401da04ed950d\", \"url\": \"https://api.pillowcase.su/api/download/d1006d1848f423ba85d401da04ed950d\", \"size\": \"995 kB\", \"duration\": 3.79}", "aliases": [], "size": "995 kB"}, {"id": "paperwork-31", "name": "Paperwork [V1]", "artists": [], "producers": [], "notes": "Track seen on a tracklist from the Italy sessions. The song originates from Japan sessions according to an insider. The song samples \"Ay Si Ñ<PERSON>ño\" by Rochy RD. LQ snippet leaked January 21st, 2024, with the recording being from July 2023. Another snippet leaked October 19th, 2024.", "length": "31.84", "fileDate": 17292960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/e220cf3b0a8ca9a60910bd74d4fc9d5c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e220cf3b0a8ca9a60910bd74d4fc9d5c\", \"key\": \"Paperwork\", \"title\": \"Paperwork [V1]\", \"description\": \"Track seen on a tracklist from the Italy sessions. The song originates from Japan sessions according to an insider. The song samples \\\"Ay Si \\u00d1i\\u00f1o\\\" by Rochy RD. LQ snippet leaked January 21st, 2024, with the recording being from July 2023. Another snippet leaked October 19th, 2024.\", \"date\": 17292960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"60b65c1f0e2a6dae3372bd954f7cc01a\", \"url\": \"https://api.pillowcase.su/api/download/60b65c1f0e2a6dae3372bd954f7cc01a\", \"size\": \"1.44 MB\", \"duration\": 31.84}", "aliases": [], "size": "1.44 MB"}, {"id": "paperwork-32", "name": "Paperwork [V2]", "artists": [], "producers": [], "notes": "According to <PERSON><PERSON>, \"Paperwork\" was initially meant for the solo Ye album being worked on prior to VULTURES. Seen on Japan tracklist. According to <PERSON><PERSON> the version on tracklists had \"Ye intro chipmunk scream is a bit longer and chipmunk sings lines <PERSON><PERSON> would later do\". Snippet leaked Octonber 23rd, 2024.", "length": "10.27", "fileDate": 17296416, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/ffa1f345736c00f535e7399e7f95554e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ffa1f345736c00f535e7399e7f95554e\", \"key\": \"Paperwork\", \"title\": \"Paperwork [V2]\", \"description\": \"According to <PERSON><PERSON>, \\\"Paperwork\\\" was initially meant for the solo Ye album being worked on prior to VULTURES. Seen on Japan tracklist. According to <PERSON><PERSON> the version on tracklists had \\\"Ye intro chipmunk scream is a bit longer and chipmunk sings lines Ty<PERSON> would later do\\\". Snippet leaked Octonber 23rd, 2024.\", \"date\": 17296416, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8297445af97897dc99c5e61f141d49ac\", \"url\": \"https://api.pillowcase.su/api/download/8297445af97897dc99c5e61f141d49ac\", \"size\": \"1.1 MB\", \"duration\": 10.27}", "aliases": [], "size": "1.1 MB"}, {"id": "ceremony-freestyle", "name": "Ceremony Freestyle [V2]", "artists": [], "producers": [], "notes": "<PERSON> played backstage after <PERSON>'s set at the Electric Picnic festival in Ireland. Snippet contains mumble. Unknown who produced it or if there are any features. Samples \"Jubilation\" by <PERSON><PERSON><PERSON><PERSON>. Nicknamed \"Leader Of The Vatican\" until the real name was revealed later. Seen on the Japan tracklist for YEBU. <PERSON><PERSON><PERSON> said on an Instagram Live that he has this version.", "length": "10.79", "fileDate": 16935264, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/227299290e9c030d6f4ca19e441a9d28", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/227299290e9c030d6f4ca19e441a9d28\", \"key\": \"Ceremony Freestyle\", \"title\": \"Ceremony Freestyle [V2]\", \"aliases\": [\"Problematic\"], \"description\": \"<PERSON> played backstage after <PERSON>'s set at the Electric Picnic festival in Ireland. Snippet contains mumble. Unknown who produced it or if there are any features. <PERSON><PERSON> \\\"Jubilation\\\" by <PERSON><PERSON><PERSON><PERSON>. Nicknamed \\\"Leader Of The Vatican\\\" until the real name was revealed later. Seen on the Japan tracklist for YEBU. <PERSON><PERSON><PERSON> said on an Instagram Live that he has this version.\", \"date\": 16935264, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"51ed5dbd91193b3c93a64e4ffc887978\", \"url\": \"https://api.pillowcase.su/api/download/51ed5dbd91193b3c93a64e4ffc887978\", \"size\": \"1.02 MB\", \"duration\": 10.79}", "aliases": ["Problematic"], "size": "1.02 MB"}, {"id": "problematic", "name": "Problematic [V3]", "artists": [], "producers": [], "notes": "OG Filename: Problematic - Ye Ref 1\nReference track with <PERSON> mumble. Made sometime in 2023, but file was rebounced in 2024. Has no beatswitch. According to <PERSON><PERSON> this was recorded during Japan sessions. Apparently was sent to Bryson Gray to make Christian. Leaked during the \"Can U Be\" groupbuy.", "length": "222.56", "fileDate": 17166816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/9515e35f907912b7c2ae2ba62623e10d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9515e35f907912b7c2ae2ba62623e10d\", \"key\": \"Problematic\", \"title\": \"Problematic [V3]\", \"artists\": \"(ref. <PERSON>ooga)\", \"aliases\": [\"Ceremony Freestyle\"], \"description\": \"OG Filename: Problematic - Ye Ref 1\\nReference track with <PERSON> mumble. Made sometime in 2023, but file was rebounced in 2024. Has no beatswitch. According to <PERSON><PERSON> this was recorded during Japan sessions. Apparently was sent to Bryson Gray to make Christian. Leaked during the \\\"Can U Be\\\" groupbuy.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"57d1f32dcd813df0f799eefa60eef3ae\", \"url\": \"https://api.pillowcase.su/api/download/57d1f32dcd813df0f799eefa60eef3ae\", \"size\": \"4.5 MB\", \"duration\": 222.56}", "aliases": ["Ceremony Freestyle"], "size": "4.5 MB"}, {"id": "problematic-35", "name": "Problematic [V4]", "artists": [], "producers": [], "notes": "OG Filename: Problematic - Ye.Rooga Re 2 - 20230715\nSecond Rooga ref. This version has slighty more Rooga vocals compared to the previous.", "length": "219.74", "fileDate": 17292960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/6802c430982cb94ae53652e38dc78994", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6802c430982cb94ae53652e38dc78994\", \"key\": \"Problematic\", \"title\": \"Problematic [V4]\", \"artists\": \"(ref. Rooga)\", \"aliases\": [\"Ceremony Freestyle\"], \"description\": \"OG Filename: Problematic - Ye.Rooga Re 2 - 20230715\\nSecond Rooga ref. This version has slighty more Rooga vocals compared to the previous.\", \"date\": 17292960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"47ce1cfe9027910cef92f1965aa154f7\", \"url\": \"https://api.pillowcase.su/api/download/47ce1cfe9027910cef92f1965aa154f7\", \"size\": \"4.45 MB\", \"duration\": 219.74}", "aliases": ["Ceremony Freestyle"], "size": "4.45 MB"}, {"id": "big-boody-bitch", "name": "Young Thug - <PERSON> [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: BIG BOODY BITCH(Open)\nVersion bounced in 2023. Likely a rebounce of an earlier version of the song, likely the file sent to <PERSON>'s team.", "length": "174.46", "fileDate": 17280000, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/97fd9881a20b3a9698d5967cdb4d8f8e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/97fd9881a20b3a9698d5967cdb4d8f8e\", \"key\": \"Big Boody Bitch\", \"title\": \"Young Thug - Big Boody Bitch [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"River\"], \"description\": \"OG Filename: BIG BOODY BITCH(Open)\\nVersion bounced in 2023. Likely a rebounce of an earlier version of the song, likely the file sent to <PERSON>'s team.\", \"date\": 17280000, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3d5d0d0f5ef9d03387c1af96e2d4ccd7\", \"url\": \"https://api.pillowcase.su/api/download/3d5d0d0f5ef9d03387c1af96e2d4ccd7\", \"size\": \"3.73 MB\", \"duration\": 174.46}", "aliases": ["River"], "size": "3.73 MB"}, {"id": "-37", "name": "<PERSON> - ??? [V5]", "artists": [], "producers": [], "notes": "First version of \"River\" with a reference by <PERSON>, later given to <PERSON><PERSON> leaked February 8th, 2025.", "length": "14.11", "fileDate": 17389728, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/5f14d3876a722792be7fd7ee4eff2dd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f14d3876a722792be7fd7ee4eff2dd7\", \"key\": \"???\", \"title\": \"<PERSON> - ??? [V5]\", \"aliases\": [\"River\"], \"description\": \"First version of \\\"River\\\" with a reference by <PERSON>, later given to <PERSON>. Snippet leaked February 8th, 2025.\", \"date\": 17389728, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"bbab748276966bda0e26533fa7ea603c\", \"url\": \"https://api.pillowcase.su/api/download/bbab748276966bda0e26533fa7ea603c\", \"size\": \"1.16 MB\", \"duration\": 14.11}", "aliases": ["River"], "size": "1.16 MB"}, {"id": "so-good", "name": "So Good [V1]", "artists": [], "producers": [], "notes": "OG Filename: so good - demo\n<PERSON> Ye demo for \"So Good\" made during the YEBU sessions in Japan. Leaked after a groupbuy for various Vultures-era tracks. Better microphone quality begins 15 seconds in.", "length": "77.54", "fileDate": 17305920, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/b6596c68969256c24f3a5ab8ea2a06a3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b6596c68969256c24f3a5ab8ea2a06a3\", \"key\": \"So Good\", \"title\": \"So Good [V1]\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: so good - demo\\nSolo Ye demo for \\\"So Good\\\" made during the YEBU sessions in Japan. Leaked after a groupbuy for various Vultures-era tracks. Better microphone quality begins 15 seconds in.\", \"date\": 17305920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"89e33cc47f6557f7b526b68cd274a8d5\", \"url\": \"https://api.pillowcase.su/api/download/89e33cc47f6557f7b526b68cd274a8d5\", \"size\": \"2.18 MB\", \"duration\": 77.54}", "aliases": ["GOOD (DON'T DIE)"], "size": "2.18 MB"}, {"id": "so-soon", "name": "So Soon [V1]", "artists": [], "producers": [], "notes": "It can be assumed that an initial freestyle exists for \"So Soon\", which <PERSON> would later use as the basis for his ref. Part of the freestyle can be heard on the version that leaked Oct 15th 2024. An inverted version is linked so you can hear the mumble better.", "length": "13.39", "fileDate": 17289504, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/194e5da07c3e1b8f9eb5429f9b74424c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/194e5da07c3e1b8f9eb5429f9b74424c\", \"key\": \"So Soon\", \"title\": \"So Soon [V1]\", \"description\": \"It can be assumed that an initial freestyle exists for \\\"So Soon\\\", which <PERSON> would later use as the basis for his ref. Part of the freestyle can be heard on the version that leaked Oct 15th 2024. An inverted version is linked so you can hear the mumble better.\", \"date\": 17289504, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"ebe0bcac6b86cf7cef7f7c3121cc632d\", \"url\": \"https://api.pillowcase.su/api/download/ebe0bcac6b86cf7cef7f7c3121cc632d\", \"size\": \"1.15 MB\", \"duration\": 13.39}", "aliases": [], "size": "1.15 MB"}, {"id": "so-soon-40", "name": "⭐ So Soon [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> <PERSON> <PERSON> <PERSON> Reference\nSeen on a tracklist for <PERSON>'s solo album being worked on in Japan. Has one finished verse that loops. Said to have been brought back for VULTURES 2 by trusted sources. Leaked after a groupbuy.", "length": "149.13", "fileDate": 17289504, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/193bb7003c18723063716767af0a319e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/193bb7003c18723063716767af0a319e\", \"key\": \"So Soon\", \"title\": \"\\u2b50 So Soon [V3]\", \"description\": \"OG Filename: <PERSON> <PERSON> <PERSON> <PERSON> Reference\\nSeen on a tracklist for <PERSON>'s solo album being worked on in Japan. Has one finished verse that loops. Said to have been brought back for VULTURES 2 by trusted sources. Leaked after a groupbuy.\", \"date\": 17289504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0d57d9fcb78177188159a5270558f198\", \"url\": \"https://api.pillowcase.su/api/download/0d57d9fcb78177188159a5270558f198\", \"size\": \"3.32 MB\", \"duration\": 149.13}", "aliases": [], "size": "3.32 MB"}, {"id": "stand-up", "name": "French Montana - Stand Up [V5]", "artists": ["Kanye West", "SAINt JHN", "<PERSON><PERSON><PERSON>"], "producers": ["Dem <PERSON>z", "<PERSON><PERSON><PERSON> <PERSON>", "BoogzDaBeast"], "notes": "OG Filename: 04 _STAND UP ye x french (1)\nVersion of \"Stand United\" with French Montana punch-in lines. Still contains <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> JHN vocals however not their full verses. Also includes Dem Jointz's \"INCOMING\" tag.", "length": "165.86", "fileDate": 17409600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/939c79469b3f56a27f9cc84d4c3e0a98", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/939c79469b3f56a27f9cc84d4c3e0a98\", \"key\": \"Stand Up\", \"title\": \"French Montana - Stand Up [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, SAINt JHN & Bu<PERSON>) (prod. <PERSON><PERSON>, Pyro da <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Stand United\"], \"description\": \"OG Filename: 04 _STAND UP ye x french (1)\\nVersion of \\\"Stand United\\\" with French Montana punch-in lines. Still contains <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> JHN vocals however not their full verses. Also includes <PERSON><PERSON> Jointz's \\\"INCOMING\\\" tag.\", \"date\": 17409600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cbbf561180588cd6f49dfb138bf09f72\", \"url\": \"https://api.pillowcase.su/api/download/cbbf561180588cd6f49dfb138bf09f72\", \"size\": \"3.59 MB\", \"duration\": 165.86}", "aliases": ["Stand United"], "size": "3.59 MB"}, {"id": "stand-united", "name": "French Montana - Stand United [V6]", "artists": ["Kanye West", "SAINt JHN", "<PERSON><PERSON><PERSON>"], "producers": ["Dem <PERSON>z", "<PERSON><PERSON><PERSON> <PERSON>", "BoogzDaBeast"], "notes": "OG Filename: Stand United (FRENCH x YE) \nVersion of \"Stand United\" with French Montana punch-in lines. Still contains <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>H<PERSON> vocals however not their full verses. Also includes <PERSON>m Jointz's \"INCOMING\" tag. Was released on French's album Mac N Cheese 5. Unknown what is different than release.", "length": "165.87", "fileDate": 17057952, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/56b9c091ef583bdc6798444e5c3c7c3a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/56b9c091ef583bdc6798444e5c3c7c3a\", \"key\": \"Stand United\", \"title\": \"French Montana - Stand United [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, SAINt JHN & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>, Pyro da <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Stand Up\"], \"description\": \"OG Filename: Stand United (FRENCH x YE) \\nVersion of \\\"Stand United\\\" with French Montana punch-in lines. Still contains <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> JHN vocals however not their full verses. Also includes <PERSON><PERSON>z's \\\"INCOMING\\\" tag. Was released on <PERSON>'s album Mac N Cheese 5. Unknown what is different than release.\", \"date\": 17057952, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a2aaf3add61b54687710e5943e457395\", \"url\": \"https://api.pillowcase.su/api/download/a2aaf3add61b54687710e5943e457395\", \"size\": \"3.59 MB\", \"duration\": 165.87}", "aliases": ["Stand Up"], "size": "3.59 MB"}, {"id": "unlock", "name": "✨ Unlock [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> - <PERSON> Reference\nMade sometime before mid-June 2023. The song has a minute long finished verse, two choruses and two minutes of open verse. Tagged file leaked after it was private bought for $5.5k. Detag can be found the in Fakes tab.", "length": "213.24", "fileDate": 17284320, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/7fdd7d196b87b05bf534c18010bbac76", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7fdd7d196b87b05bf534c18010bbac76\", \"key\": \"Unlock\", \"title\": \"\\u2728 Unlock [V3]\", \"description\": \"OG Filename: Unlock - Ye Reference\\nMade sometime before mid-June 2023. The song has a minute long finished verse, two choruses and two minutes of open verse. Tagged file leaked after it was private bought for $5.5k. Detag can be found the in Fakes tab.\", \"date\": 17284320, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"051ee45fc42d3b7e990d2825af690411\", \"url\": \"https://api.pillowcase.su/api/download/051ee45fc42d3b7e990d2825af690411\", \"size\": \"4.35 MB\", \"duration\": 213.24}", "aliases": [], "size": "4.35 MB"}, {"id": "unlock-44", "name": "Unlock [V4]", "artists": [], "producers": [], "notes": "Version of the song played at the Mowalola fashion show in London on September 15th, 2023. Has a different hook compared to other versions, which was most likely made post-June 2023.", "length": "", "fileDate": 16947360, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/022cd95cc4a74c5e12771dd84a672d25", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/022cd95cc4a74c5e12771dd84a672d25\", \"key\": \"Unlock\", \"title\": \"Unlock [V4]\", \"description\": \"Version of the song played at the Mowalola fashion show in London on September 15th, 2023. Has a different hook compared to other versions, which was most likely made post-June 2023.\", \"date\": 16947360, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "wolverine", "name": "Wolverine", "artists": [], "producers": ["454", "Pops", "", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON> -<PERSON> ruff-\nApparently a reference track made for <PERSON>. Released officially on <PERSON>' album You Only Die 1nce. Leaked as a bonus for the \"Pure\" groupbuy.", "length": "171.08", "fileDate": 17429472, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/f24902a6856aca417bd390141c8fd22a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f24902a6856aca417bd390141c8fd22a\", \"key\": \"<PERSON>\", \"title\": \"<PERSON>\", \"artists\": \"(ref. <PERSON>) (prod. 454, <PERSON><PERSON>, & <PERSON><PERSON>)\", \"description\": \"OG Filename: Wolverine -Ye ruff-\\nApparently a reference track made for Ye. Released officially on <PERSON>' album You Only Die 1nce. Leaked as a bonus for the \\\"Pure\\\" groupbuy.\", \"date\": 17429472, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"40f8a9a45ac1e7fb867a16939688e7b0\", \"url\": \"https://api.pillowcase.su/api/download/40f8a9a45ac1e7fb867a16939688e7b0\", \"size\": \"3.67 MB\", \"duration\": 171.08}", "aliases": [], "size": "3.67 MB"}, {"id": "worship", "name": "Chlöe - Worship [V1]", "artists": [], "producers": ["88-<PERSON>", "<PERSON>"], "notes": "According to <PERSON><PERSON>, \"Worship\" started out as a <PERSON><PERSON><PERSON><PERSON> song. It came from 10 song recording sessions she had, one of which resulted in \"Worship\", which <PERSON><PERSON><PERSON><PERSON> would later give to <PERSON><PERSON>ni<PERSON> leaked December 5th, 2024.", "length": "11", "fileDate": 17333568, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/4266d592d1ebf55c19547c991962c4b0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4266d592d1ebf55c19547c991962c4b0\", \"key\": \"Worship\", \"title\": \"Chl\\u00f6e - Worship [V1]\", \"artists\": \"(prod. 88-<PERSON> & <PERSON>)\", \"description\": \"According to <PERSON><PERSON>, \\\"Worship\\\" started out as a Chl\\u00f6e Bailey song. It came from 10 song recording sessions she had, one of which resulted in \\\"Worship\\\", which Chl\\u00f6e would later give to <PERSON><PERSON>nippe<PERSON> leaked December 5th, 2024.\", \"date\": 17333568, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6db231c367c9c4c011d888d2a3718946\", \"url\": \"https://api.pillowcase.su/api/download/6db231c367c9c4c011d888d2a3718946\", \"size\": \"1.02 MB\", \"duration\": 11}", "aliases": [], "size": "1.02 MB"}, {"id": "worship-47", "name": "Worship [V3]", "artists": [], "producers": ["88-<PERSON>", "<PERSON>"], "notes": "Initial Ye \"Worship\" freestyle, has the drums from later versions. Snippet leaked November 18th, 2024.", "length": "3.86", "fileDate": 17318880, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/aaf1e1b43a2a5ee1350010852a15091e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aaf1e1b43a2a5ee1350010852a15091e\", \"key\": \"Worship\", \"title\": \"Worship [V3]\", \"artists\": \"(prod. 88-<PERSON> & <PERSON>)\", \"description\": \"Initial Ye \\\"Worship\\\" freestyle, has the drums from later versions. Snippet leaked November 18th, 2024.\", \"date\": 17318880, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f0398404dc1d3b8432e2b466932c50ad\", \"url\": \"https://api.pillowcase.su/api/download/f0398404dc1d3b8432e2b466932c50ad\", \"size\": \"997 kB\", \"duration\": 3.86}", "aliases": [], "size": "997 kB"}, {"id": "-48", "name": "???", "artists": [], "producers": ["<PERSON>"], "notes": "YEBU era beat produced by <PERSON>. Has no vocals.", "length": "16.4", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/bcdbcca7242fe95087abb56ded2a94f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bcdbcca7242fe95087abb56ded2a94f5\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"YEBU era beat produced by <PERSON>. Has no vocals.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ad7953b7dfd87d591bcd6e8eeef136cd\", \"url\": \"https://api.pillowcase.su/api/download/ad7953b7dfd87d591bcd6e8eeef136cd\", \"size\": \"1.2 MB\", \"duration\": 16.4}", "aliases": [], "size": "1.2 MB"}, {"id": "-49", "name": "???", "artists": [], "producers": [], "notes": "Random VULTURES / YEBU era freestyle. Leaked as part of the \"So Soon\" joebuy, with its original snippet being posted only earlier that day.", "length": "222.98", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/b9620236341243f9a8e8070bbd0dea5b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b9620236341243f9a8e8070bbd0dea5b\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Tell Me That\"], \"description\": \"Random VULTURES / YEBU era freestyle. Leaked as part of the \\\"So Soon\\\" joebuy, with its original snippet being posted only earlier that day.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"cb6c99c80e02bcf2c167847265cc494d\", \"url\": \"https://api.pillowcase.su/api/download/cb6c99c80e02bcf2c167847265cc494d\", \"size\": \"4.5 MB\", \"duration\": 222.98}", "aliases": ["Tell Me That"], "size": "4.5 MB"}, {"id": "-50", "name": "???", "artists": [], "producers": [], "notes": "Random YEBU era freestyle. Original snippet leaked 2 days prior to it fully leaking as a bonus for the second VULTURES 2 charity buy.", "length": "117.47", "fileDate": 17290368, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/8ba81f6ccc1db28aa3d5aca77a899f6e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ba81f6ccc1db28aa3d5aca77a899f6e\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Random YEBU era freestyle. Original snippet leaked 2 days prior to it fully leaking as a bonus for the second VULTURES 2 charity buy.\", \"date\": 17290368, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c89840bf4e82739085691c6d4d19ec6a\", \"url\": \"https://api.pillowcase.su/api/download/c89840bf4e82739085691c6d4d19ec6a\", \"size\": \"2.81 MB\", \"duration\": 117.47}", "aliases": [], "size": "2.81 MB"}, {"id": "-51", "name": "??? [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "Beat to an unknown YEBU era freestyle. Sam<PERSON> \"chase\" by bat<PERSON> and the beat was later given to SMILEZ and released as \"Self Sabotage\". Leaked as a bonus for the third VULTURES 2 charity buy.", "length": "56.53", "fileDate": 17294688, "leakDate": "", "availableLength": "Beat Only", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/d28099ae96d98dfdde22bb34ebd5b9b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d28099ae96d98dfdde22bb34ebd5b9b7\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"Beat to an unknown YEBU era freestyle. Samples \\\"chase\\\" by bat<PERSON> and the beat was later given to SMILEZ and released as \\\"Self Sabotage\\\". Leaked as a bonus for the third VULTURES 2 charity buy.\", \"date\": 17294688, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f803fd53a11990bab9ec589f42a901b8\", \"url\": \"https://api.pillowcase.su/api/download/f803fd53a11990bab9ec589f42a901b8\", \"size\": \"1.84 MB\", \"duration\": 56.53}", "aliases": [], "size": "1.84 MB"}, {"id": "-52", "name": "??? [V2]", "artists": [], "producers": ["Digital Nas"], "notes": "Random YEBU era freestyle. Original snippet leaked 2 days prior to it fully leaking as a bonus for the second VULTURES 2 charity buy.", "length": "71.39", "fileDate": 17290368, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/2fbc8f45adaa3f38aca4bad3f1067b44", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2fbc8f45adaa3f38aca4bad3f1067b44\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"Random YEBU era freestyle. Original snippet leaked 2 days prior to it fully leaking as a bonus for the second VULTURES 2 charity buy.\", \"date\": 17290368, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5b822f731cd3b5389296a09f4c12bdaa\", \"url\": \"https://api.pillowcase.su/api/download/5b822f731cd3b5389296a09f4c12bdaa\", \"size\": \"2.08 MB\", \"duration\": 71.39}", "aliases": [], "size": "2.08 MB"}, {"id": "-53", "name": "???", "artists": [], "producers": [], "notes": "Unknown song played at the Melrose party on April 21st, 2024.", "length": "7.39", "fileDate": 17136576, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/4bcebf880f58d274883befd6d7e15ece", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bcebf880f58d274883befd6d7e15ece\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Stop\"], \"description\": \"Unknown song played at the Melrose party on April 21st, 2024.\", \"date\": 17136576, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"36a96825894a068728afa9e6ee4cdd37\", \"url\": \"https://api.pillowcase.su/api/download/36a96825894a068728afa9e6ee4cdd37\", \"size\": \"289 kB\", \"duration\": 7.39}", "aliases": ["Stop"], "size": "289 kB"}, {"id": "-54", "name": "???", "artists": [], "producers": [], "notes": "Snippet of an unknown song was posted by Joy<PERSON> before being swiftly deleted. Nothing is known about the song or when exactly is it from, but assumed to be VULTURES 2 era. Later found out to be a YEBU era freestyle.", "length": "11.55", "fileDate": 17225568, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/2ca082a319d8297cab5be473ad72c71f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ca082a319d8297cab5be473ad72c71f\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Snippet of an unknown song was posted by <PERSON><PERSON> before being swiftly deleted. Nothing is known about the song or when exactly is it from, but assumed to be VULTURES 2 era. Later found out to be a YEBU era freestyle.\", \"date\": 17225568, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"18e5a9e15075a808a8af3c0d2f24ddfa\", \"url\": \"https://api.pillowcase.su/api/download/18e5a9e15075a808a8af3c0d2f24ddfa\", \"size\": \"642 kB\", \"duration\": 11.55}", "aliases": [], "size": "642 kB"}, {"id": "-55", "name": "???", "artists": [], "producers": [], "notes": "Unknown beat leaked as a bonus for the second VULTURES 2 groupbuy. Samples \"Yes\" by She<PERSON>ah Glory Ministry.", "length": "20.87", "fileDate": 17294688, "leakDate": "", "availableLength": "Beat Only", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/960bb232fef5c5cf1f509ea2306b4e00", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/960bb232fef5c5cf1f509ea2306b4e00\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Yes\"], \"description\": \"Unknown beat leaked as a bonus for the second VULTURES 2 groupbuy. Samples \\\"Yes\\\" by Shekinah Glory Ministry.\", \"date\": 17294688, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2afc0e88bf6f774e2b6e7e8817cba48b\", \"url\": \"https://api.pillowcase.su/api/download/2afc0e88bf6f774e2b6e7e8817cba48b\", \"size\": \"1.27 MB\", \"duration\": 20.87}", "aliases": ["Yes"], "size": "1.27 MB"}, {"id": "blood-stain-2", "name": "Consequence - Blood Stain 2 [V3]", "artists": ["<PERSON>", "Ghostface <PERSON>", "Amerie"], "producers": ["Ojivolta", "Kanye West", "<PERSON><PERSON><PERSON>"], "notes": "Version with a scrapped Ghost<PERSON> feature. Was played on several Consequence Instagram Lives.", "length": "84.17", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/64ec83be17f4248ea9958f9f06864008", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/64ec83be17f4248ea9958f9f06864008\", \"key\": \"Blood Stain 2\", \"title\": \"Consequence - Blood Stain 2 [V3]\", \"artists\": \"(feat. <PERSON>, Ghost<PERSON>ah & Amerie) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Version with a scrapped Ghostface Killah feature. Was played on several Consequence Instagram Lives.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"900c3808bfffa84d77351616fa14ecdb\", \"url\": \"https://api.pillowcase.su/api/download/900c3808bfffa84d77351616fa14ecdb\", \"size\": \"2.28 MB\", \"duration\": 84.17}", "aliases": [], "size": "2.28 MB"}, {"id": "blood-stain-2-57", "name": "Consequence - Blood Stain 2 [V3]", "artists": ["<PERSON>", "Ghostface <PERSON>", "Amerie"], "producers": ["Ojivolta", "Kanye West", "<PERSON><PERSON><PERSON>"], "notes": "Version with a scrapped Ghost<PERSON> feature. Was played on several Consequence Instagram Lives.", "length": "11.23", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/c1ea7e52d5aac3e1c23b8ca23fb689f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1ea7e52d5aac3e1c23b8ca23fb689f8\", \"key\": \"Blood Stain 2\", \"title\": \"Consequence - Blood Stain 2 [V3]\", \"artists\": \"(feat. <PERSON>, Ghost<PERSON> Killah & Amerie) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Version with a scrapped Ghostface Killah feature. Was played on several Consequence Instagram Lives.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"30f85943b28710f45b9afd8cf1f68335\", \"url\": \"https://api.pillowcase.su/api/download/30f85943b28710f45b9afd8cf1f68335\", \"size\": \"1.11 MB\", \"duration\": 11.23}", "aliases": [], "size": "1.11 MB"}, {"id": "blood-stain-2-58", "name": "Consequence - Blood Stain 2 [V3]", "artists": ["<PERSON>", "Ghostface <PERSON>", "Amerie"], "producers": ["Ojivolta", "Kanye West", "<PERSON><PERSON><PERSON>"], "notes": "Version with a scrapped Ghost<PERSON> feature. Was played on several Consequence Instagram Lives.", "length": "163.5", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/5bd5a6ce93373bf7e78227c5dbafb540", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5bd5a6ce93373bf7e78227c5dbafb540\", \"key\": \"Blood Stain 2\", \"title\": \"Consequence - Blood Stain 2 [V3]\", \"artists\": \"(feat. <PERSON>, Ghost<PERSON> Killah & Amerie) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Version with a scrapped Ghostface Killah feature. Was played on several Consequence Instagram Lives.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"4d3c1fdbd7df3b791f1f27f01768ca13\", \"url\": \"https://api.pillowcase.su/api/download/4d3c1fdbd7df3b791f1f27f01768ca13\", \"size\": \"3.55 MB\", \"duration\": 163.5}", "aliases": [], "size": "3.55 MB"}, {"id": "bloodstains-3", "name": "Consequence - Bloodstains 3 [V1]", "artists": ["Ghostface <PERSON>", "<PERSON><PERSON><PERSON> The Crownholder"], "producers": ["Kanye West"], "notes": "Features the cut <PERSON><PERSON> and <PERSON><PERSON><PERSON> vocals from the initial remix. Said to be releasing soon. Snippet previewed in Consequence's VladTV interview.", "length": "18.6", "fileDate": 16809120, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/5b4039b3fff47b57aa1187022e0ae3f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5b4039b3fff47b57aa1187022e0ae3f3\", \"key\": \"Bloodstains 3\", \"title\": \"Consequence - Bloodstains 3 [V1]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON><PERSON><PERSON> The Crownholder) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Features the cut <PERSON><PERSON> and <PERSON><PERSON><PERSON> vocals from the initial remix. Said to be releasing soon. Snippet previewed in Consequence's <PERSON><PERSON> interview.\", \"date\": 16809120, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ab44e2892002b8bef5f40760d8be0c62\", \"url\": \"https://api.pillowcase.su/api/download/ab44e2892002b8bef5f40760d8be0c62\", \"size\": \"1.23 MB\", \"duration\": 18.6}", "aliases": [], "size": "1.23 MB"}, {"id": "what-has-america-done", "name": "Consequence - What Has America Done [V6]", "artists": ["The WRLDFMS <PERSON>"], "producers": [], "notes": "A further along version of \"America\", with an added <PERSON> feature, and alternate production. Played on Instagram Live by Consequence. A music video also exists.", "length": "8.62", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/c4c639469fcbef2b6a4defdc3638da78", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4c639469fcbef2b6a4defdc3638da78\", \"key\": \"What Has America Done\", \"title\": \"Consequence - What Has America Done [V6]\", \"artists\": \"(feat. The WRLDFMS <PERSON>)\", \"aliases\": [\"America\", \"What Has America Done 4 Me\", \"What Has America Done For Me\", \"What Have America Done For You\"], \"description\": \"A further along version of \\\"America\\\", with an added <PERSON> feature, and alternate production. Played on Instagram Live by Consequence. A music video also exists.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"821c508df4076ce47fbc6edc2103fb83\", \"url\": \"https://api.pillowcase.su/api/download/821c508df4076ce47fbc6edc2103fb83\", \"size\": \"1.07 MB\", \"duration\": 8.62}", "aliases": ["America", "What Has America Done 4 Me", "What Has America Done For Me", "What Have America Done For You"], "size": "1.07 MB"}, {"id": "what-has-america-done-61", "name": "Consequence - What Has America Done [V6]", "artists": ["The WRLDFMS <PERSON>"], "producers": [], "notes": "A further along version of \"America\", with an added <PERSON> feature, and alternate production. Played on Instagram Live by Consequence. A music video also exists.", "length": "109.19", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/69316fb130cb9c13b42a28633f8a9532", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/69316fb130cb9c13b42a28633f8a9532\", \"key\": \"What Has America Done\", \"title\": \"Consequence - What Has America Done [V6]\", \"artists\": \"(feat. The WRLDFMS <PERSON>)\", \"aliases\": [\"America\", \"What Has America Done 4 Me\", \"What Has America Done For Me\", \"What Have America Done For You\"], \"description\": \"A further along version of \\\"America\\\", with an added <PERSON> feature, and alternate production. Played on Instagram Live by Consequence. A music video also exists.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"329baf114ea3eb0e2ad65c6e5ba4f345\", \"url\": \"https://api.pillowcase.su/api/download/329baf114ea3eb0e2ad65c6e5ba4f345\", \"size\": \"2.68 MB\", \"duration\": 109.19}", "aliases": ["America", "What Has America Done 4 Me", "What Has America Done For Me", "What Have America Done For You"], "size": "2.68 MB"}, {"id": "concussion", "name": "Fivio Foreign - Concussion [V2]", "artists": ["Kanye West"], "producers": ["Albe Back", "AyoAA"], "notes": "Version of \"Concussion\", with slightly different production. Was previewed on Fivio's Instagram.", "length": "39.24", "fileDate": 16809120, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/167cfe42b674e01c8a88ee3403536999", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/167cfe42b674e01c8a88ee3403536999\", \"key\": \"Concussion\", \"title\": \"Fivio Foreign - Concussion [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Albe Back & AyoAA)\", \"description\": \"Version of \\\"Concussion\\\", with slightly different production. Was previewed on Fivio's Instagram.\", \"date\": 16809120, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"14763652116e5dd7043eaf744e180222\", \"url\": \"https://api.pillowcase.su/api/download/14763652116e5dd7043eaf744e180222\", \"size\": \"1.56 MB\", \"duration\": 39.24}", "aliases": [], "size": "1.56 MB"}, {"id": "a-thousand", "name": "<PERSON> - A Thousand", "artists": [], "producers": ["Kanye West"], "notes": "Low quality leak of a <PERSON> song possibly for his new album.", "length": "204.05", "fileDate": 17037216, "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/cc41863d802db5f626133e9a8c179b9c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc41863d802db5f626133e9a8c179b9c\", \"key\": \"A Thousand\", \"title\": \"<PERSON> - A Thousand\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Low quality leak of a <PERSON> song possibly for his new album.\", \"date\": 17037216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3c93e19aac525a4f04d1afaaf7d544df\", \"url\": \"https://api.pillowcase.su/api/download/3c93e19aac525a4f04d1afaaf7d544df\", \"size\": \"2.57 MB\", \"duration\": 204.05}", "aliases": [], "size": "2.57 MB"}, {"id": "where-they-at", "name": "French Montana - Where They At [V3]", "artists": ["Kanye West", "Westside Gunn"], "producers": ["Dem <PERSON>z", "BoogzDaBeast"], "notes": "OG Filename: Where they at ft Kanye West West Side Gunn\nHas different mixing and different echo effects on some French adlibs not seen on release, however has the same structure as release.", "length": "176.15", "fileDate": 17344800, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/e3dcdbe2cce29eb2ffbfe927bb314f93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e3dcdbe2cce29eb2ffbfe927bb314f93\", \"key\": \"Where They At\", \"title\": \"French Montana - Where They At [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Westside Gunn) (prod. Dem Jointz & BoogzDaBeast)\", \"description\": \"OG Filename: Where they at ft Kanye West West Side Gunn\\nHas different mixing and different echo effects on some French adlibs not seen on release, however has the same structure as release.\", \"date\": 17344800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e7744ec88c798fa90cc092ed9b723dce\", \"url\": \"https://api.pillowcase.su/api/download/e7744ec88c798fa90cc092ed9b723dce\", \"size\": \"3.75 MB\", \"duration\": 176.15}", "aliases": [], "size": "3.75 MB"}, {"id": "where-they-at-65", "name": "French Montana - Where They At [V4]", "artists": ["Kanye West"], "producers": ["Dem <PERSON>z", "BoogzDaBeast"], "notes": "OG Filename: French x YE AUDIO_0934\nOpen verse version of \"Where They At\" meant for French to record over. Was strangely bounced November 18th 2023, which was after French and <PERSON><PERSON> Gunn recorded.", "length": "118.69", "fileDate": 17344800, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/69245828a075e943dcd5acdc7bed80c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/69245828a075e943dcd5acdc7bed80c8\", \"key\": \"Where They At\", \"title\": \"French Montana - Where They At [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Dem Jointz & BoogzDaBeast)\", \"description\": \"OG Filename: French x YE AUDIO_0934\\nOpen verse version of \\\"Where They At\\\" meant for French to record over. Was strangely bounced November 18th 2023, which was after French and Westside Gunn recorded.\", \"date\": 17344800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2f2da85511361b3567795c66794f7a4a\", \"url\": \"https://api.pillowcase.su/api/download/2f2da85511361b3567795c66794f7a4a\", \"size\": \"2.83 MB\", \"duration\": 118.69}", "aliases": [], "size": "2.83 MB"}, {"id": "where-they-at-66", "name": "French Montana - Where They At [V5]", "artists": ["Kanye West", "Westside Gunn"], "producers": ["Dem <PERSON>z", "BoogzDaBeast"], "notes": "Original version of \"Where They At\" possibly made during the French Montana Donda 2 sessions. Released on French's album Mac & Cheese 5. Released version is said to be louder than this version. Original snippet leaked November 15th 2023.", "length": "173.9", "fileDate": 17032032, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/b34f483cebde10c9c40634517574bbce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b34f483cebde10c9c40634517574bbce\", \"key\": \"Where They At\", \"title\": \"French Montana - Where They At [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Westside Gunn) (prod. Dem Jointz & BoogzDaBeast)\", \"description\": \"Original version of \\\"Where They At\\\" possibly made during the French Montana Donda 2 sessions. Released on French's album Mac & Cheese 5. Released version is said to be louder than this version. Original snippet leaked November 15th 2023.\", \"date\": 17032032, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2019d860275c723dbe9309c86526106b\", \"url\": \"https://api.pillowcase.su/api/download/2019d860275c723dbe9309c86526106b\", \"size\": \"3.08 MB\", \"duration\": 173.9}", "aliases": [], "size": "3.08 MB"}, {"id": "pelle-coat", "name": "<PERSON> - <PERSON><PERSON> [V2]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "The original instrumental for \"Pelle Coat\" can be heard in the bleed on the release version of the song. The original instrumental is said to be produced by <PERSON> by <PERSON><PERSON>.", "length": "8.05", "fileDate": 17254944, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/eacfc290746047cc149f2bb2adbfa66b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eacfc290746047cc149f2bb2adbfa66b\", \"key\": \"Pelle Coat\", \"title\": \"<PERSON> Du<PERSON> - <PERSON>elle Coat [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"The original instrumental for \\\"Pelle Coat\\\" can be heard in the bleed on the release version of the song. The original instrumental is said to be produced by <PERSON> by Alek.\", \"date\": 17254944, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e19bd994294317adab2ad50e0a00e1c8\", \"url\": \"https://api.pillowcase.su/api/download/e19bd994294317adab2ad50e0a00e1c8\", \"size\": \"1.06 MB\", \"duration\": 8.05}", "aliases": [], "size": "1.06 MB"}, {"id": "cat-n-mouse", "name": "Nico Baran - CAT N MOUSE [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: NICO BARAN CAT N MOUSE 140\nOG loop for \"Pay Per View\", posted by <PERSON> himself. It's unknown when this was made, or what version it was used on. Full file around 24 seconds in length.", "length": "12.17", "fileDate": 17148672, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/fda480fb29453df6711f6236a9c27a91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fda480fb29453df6711f6236a9c27a91\", \"key\": \"CAT N MOUSE\", \"title\": \"<PERSON>an - CAT N MOUSE [V2]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"PAY PER VIEW\"], \"description\": \"OG Filename: NICO BARAN CAT N MOUSE 140\\nOG loop for \\\"Pay Per View\\\", posted by <PERSON> himself. It's unknown when this was made, or what version it was used on. Full file around 24 seconds in length.\", \"date\": 17148672, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5a9fc17562f7951c7b4e85ece8e9b0dc\", \"url\": \"https://api.pillowcase.su/api/download/5a9fc17562f7951c7b4e85ece8e9b0dc\", \"size\": \"5.2 MB\", \"duration\": 12.17}", "aliases": ["PAY PER VIEW"], "size": "5.2 MB"}, {"id": "paid", "name": "Ty Dolla $ign - Paid [V1]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v"], "notes": "\"Paid\" was originally a Ty Dolla $ign track. Snippet of an alternate Ty$ verse, presumably on an a pre-VULTURES version, leaked on February 10th, 2024. Unknown when this version is from, but likely shown to <PERSON> during the Japan sessions.", "length": "23.01", "fileDate": 17075232, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/4d302c6dbe975bd4c38e2b630e4c0601", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d302c6dbe975bd4c38e2b630e4c0601\", \"key\": \"Paid\", \"title\": \"Ty Dolla $ign - Paid [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. Stryv)\", \"description\": \"\\\"Paid\\\" was originally a Ty Dolla $ign track. Snippet of an alternate Ty$ verse, presumably on an a pre-VULTURES version, leaked on February 10th, 2024. Unknown when this version is from, but likely shown to <PERSON> during the Japan sessions.\", \"date\": 17075232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ea1c0157dd6ecaf92b553a2ffc380dd5\", \"url\": \"https://api.pillowcase.su/api/download/ea1c0157dd6ecaf92b553a2ffc380dd5\", \"size\": \"5.19 MB\", \"duration\": 23.01}", "aliases": [], "size": "5.19 MB"}, {"id": "promotion", "name": "Ty Dolla $ign - Promotion [V3]", "artists": ["Future"], "producers": ["DJ <PERSON><PERSON>"], "notes": "OG Filenames: PROMOTION ESCOXTY$XFUTURE &\nPROMOTION V1\nVersion of \"Promotion\" with <PERSON> $ign and <PERSON> vocals, likely pre-Ye involvement. Possibly seen on a blurry Japan tracklist as \"Gorgeous\". A recording originally leaked August 6th, 2024, with an HQ version leaking the next day. Apparently for some reason, the \"PROMOTION ESCOXTY$XFUTURE\" is just a minute of the snippet that was previewed fully but \"PROMOTION V1\" is the actual 3 minute file.", "length": "59.88", "fileDate": 17229888, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/f37482ff3beec5d864fe44fcecdbc5aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f37482ff3beec5d864fe44fcecdbc5aa\", \"key\": \"Promotion\", \"title\": \"Ty Dolla $ign - Promotion [V3]\", \"artists\": \"(feat. Future) (prod. <PERSON>)\", \"aliases\": [\"Gorgeous\"], \"description\": \"OG Filenames: PROMOTION ESCOXTY$XFUTURE &\\nPROMOTION V1\\nVersion of \\\"Promotion\\\" with Ty Dolla $ign and Future vocals, likely pre-Ye involvement. Possibly seen on a blurry Japan tracklist as \\\"Gorgeous\\\". A recording originally leaked August 6th, 2024, with an HQ version leaking the next day. Apparently for some reason, the \\\"PROMOTION ESCOXTY$XFUTURE\\\" is just a minute of the snippet that was previewed fully but \\\"PROMOTION V1\\\" is the actual 3 minute file.\", \"date\": 17229888, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"827f55ccca6fa7a9311b5899f6b7cac0\", \"url\": \"https://api.pillowcase.su/api/download/827f55ccca6fa7a9311b5899f6b7cac0\", \"size\": \"1.89 MB\", \"duration\": 59.88}", "aliases": ["Gorgeous"], "size": "1.89 MB"}, {"id": "promotion-71", "name": "Ty Dolla $ign - Promotion [V3]", "artists": ["Future"], "producers": ["DJ <PERSON><PERSON>"], "notes": "OG Filenames: PROMOTION ESCOXTY$XFUTURE &\nPROMOTION V1\nVersion of \"Promotion\" with <PERSON> $ign and <PERSON> vocals, likely pre-Ye involvement. Possibly seen on a blurry Japan tracklist as \"Gorgeous\". A recording originally leaked August 6th, 2024, with an HQ version leaking the next day. Apparently for some reason, the \"PROMOTION ESCOXTY$XFUTURE\" is just a minute of the snippet that was previewed fully but \"PROMOTION V1\" is the actual 3 minute file.", "length": "10.06", "fileDate": 17229888, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/95c7a86c1f6134e077a900cc5f8466b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/95c7a86c1f6134e077a900cc5f8466b6\", \"key\": \"Promotion\", \"title\": \"Ty Dolla $ign - Promotion [V3]\", \"artists\": \"(feat. Future) (prod. <PERSON>)\", \"aliases\": [\"Gorgeous\"], \"description\": \"OG Filenames: PROMOTION ESCOXTY$XFUTURE &\\nPROMOTION V1\\nVersion of \\\"Promotion\\\" with Ty Dolla $ign and Future vocals, likely pre-Ye involvement. Possibly seen on a blurry Japan tracklist as \\\"Gorgeous\\\". A recording originally leaked August 6th, 2024, with an HQ version leaking the next day. Apparently for some reason, the \\\"PROMOTION ESCOXTY$XFUTURE\\\" is just a minute of the snippet that was previewed fully but \\\"PROMOTION V1\\\" is the actual 3 minute file.\", \"date\": 17229888, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c3e8c2d412485969c5524e5c9e5bde9d\", \"url\": \"https://api.pillowcase.su/api/download/c3e8c2d412485969c5524e5c9e5bde9d\", \"size\": \"1.1 MB\", \"duration\": 10.06}", "aliases": ["Gorgeous"], "size": "1.1 MB"}, {"id": "promotion-72", "name": "Ty Dolla $ign - Promotion [V4]", "artists": ["Future"], "producers": ["London on da Track"], "notes": "Later version of \"Promotion\", with alternate production that would be used on later versions, still pre-Ye involvement. Has extra Ty vocals. Snippet leaked March 9th, 2024.", "length": "9.74", "fileDate": 17099424, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/23270a5168262276b4faffd90d2461c4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23270a5168262276b4faffd90d2461c4\", \"key\": \"Promotion\", \"title\": \"Ty Dolla $ign - Promotion [V4]\", \"artists\": \"(feat. Future) (prod. London on da Track)\", \"aliases\": [\"Gorgeous\"], \"description\": \"Later version of \\\"Promotion\\\", with alternate production that would be used on later versions, still pre-Ye involvement. Has extra Ty vocals. Snippet leaked March 9th, 2024.\", \"date\": 17099424, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6f530f57ceef519fc5484f22ef732d8c\", \"url\": \"https://api.pillowcase.su/api/download/6f530f57ceef519fc5484f22ef732d8c\", \"size\": \"5.16 MB\", \"duration\": 9.74}", "aliases": ["Gorgeous"], "size": "5.16 MB"}, {"id": "slide", "name": "✨ Ty Dolla $ign - Slide [V1]", "artists": [], "producers": ["Ojivolta", "<PERSON> again..", "Skrillex"], "notes": "OG Filename: FRED TY SLIDE\nOG Solo version with just <PERSON>. Has a very different beat compared to later versions. Unknown when this version is from, but likely shown to <PERSON> during the Japan sessions. Original snippets leaked January 14th & August 12th, 2024. Leaked in full as a Soakbuy bonus.", "length": "160", "fileDate": 17389728, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/5e52ff8c6e1fda5316d6ea4d7bbbf5ec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e52ff8c6e1fda5316d6ea4d7bbbf5ec\", \"key\": \"Slide\", \"title\": \"\\u2728 Ty Dolla $ign - Slide [V1]\", \"artists\": \"(prod. <PERSON>, <PERSON> again.. & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Sliding\", \"SLIDIN\", \"SLIDE IN\"], \"description\": \"OG Filename: FRED TY SLIDE\\nOG Solo version with just Ty. Has a very different beat compared to later versions. Unknown when this version is from, but likely shown to <PERSON> during the Japan sessions. Original snippets leaked January 14th & August 12th, 2024. Leaked in full as a Soakbuy bonus.\", \"date\": 17389728, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6a654554de1457a5e156827cff2acde1\", \"url\": \"https://api.pillowcase.su/api/download/6a654554de1457a5e156827cff2acde1\", \"size\": \"3.5 MB\", \"duration\": 160}", "aliases": ["Sliding", "SLIDIN", "SLIDE IN"], "size": "3.5 MB"}, {"id": "smoking-on-junt", "name": "Ty Dolla $ign - Smoking On Junt [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: smokin on junt - TY - JJ Vocals Rev 1.L\n\"Fuk Sumn\" was originally titled \"Smoking On Junt\" and featured <PERSON><PERSON><PERSON> when this version is from, but likely shown to <PERSON> during the Japan sessions. Original snippet leaked January 15th, 2024. Mono file leaked after a groupbuy.", "length": "123.56", "fileDate": 17160768, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yebu", "originalUrl": "https://pillowcase.su/f/5036715855223383ae64cc4635c9e38c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5036715855223383ae64cc4635c9e38c\", \"key\": \"Smoking On Junt\", \"title\": \"Ty Dolla $ign - Smoking On Junt [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fuk Sumn\", \"Fukk Sumn\", \"Fuck Sum\"], \"description\": \"OG Filename: smokin on junt - TY - JJ Vocals Rev 1.L\\n\\\"Fuk Sumn\\\" was originally titled \\\"Smoking On Junt\\\" and featured <PERSON><PERSON><PERSON> when this version is from, but likely shown to <PERSON> during the Japan sessions. Original snippet leaked January 15th, 2024. Mono file leaked after a groupbuy.\", \"date\": 17160768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"20e5e9b6393bf3e74fb91e0587e7b624\", \"url\": \"https://api.pillowcase.su/api/download/20e5e9b6393bf3e74fb91e0587e7b624\", \"size\": \"6.99 MB\", \"duration\": 123.56}", "aliases": ["Fuk Sumn", "Fukk Sumn", "Fuck Sum"], "size": "6.99 MB"}]}