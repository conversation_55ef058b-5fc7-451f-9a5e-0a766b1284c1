{"id": "yandhi-v2", "name": "<PERSON><PERSON> [V2]", "description": "After <PERSON><PERSON><PERSON> delayed <PERSON><PERSON> indefinitely, he began working with record producer <PERSON><PERSON><PERSON> to create \"more healing music\" for the album. Shortly after the announcement of the delay, <PERSON><PERSON><PERSON> underwent a sudden and dramatic conversion towards born-again evangelical Christianity, debuting the Sunday Service Choir at the start of 2019. The creation of the choir coincided with the songs on <PERSON><PERSON> taking a new Christian lyrical focus. Eventually, the album would morph into the thoroughly Christian JESUS IS KING by mid-2019.", "backgroundColor": "rgb(116, 27, 71)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17F7tPpSHIYVNiLppGHYNrmb_lDM2a1c0S0HJ3Bmw3EPwt7Zx9wCpvYdnR-FAUnomTegh8rgWG6gbKGZASIcilP4byCakuwOSag32XXcW9TEWmP731odcAWIH548LNJWI-7cYd3iW0Csfg5Xp34?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "3", "name": "+3 [V1]", "artists": ["???"], "producers": ["<PERSON>"], "notes": "OG Filename: ******.05BPM BOUNCE\nOriginally thought to feature <PERSON>, but then said to feature <PERSON>, however the featured vocals have never been identified.", "length": "39.71", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/43c1331b65b0707c6c59b485d409c644", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/43c1331b65b0707c6c59b485d409c644\", \"key\": \"+3\", \"title\": \"+3 [V1]\", \"artists\": \"(feat. ???) (prod. <PERSON>)\", \"aliases\": [\"Come Home\"], \"description\": \"OG Filename: ******.05BPM BOUNCE\\nOriginally thought to feature <PERSON>, but then said to feature <PERSON>, however the featured vocals have never been identified.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c2e496b0490935712fe7c92c6bf28e42\", \"url\": \"https://api.pillowcase.su/api/download/c2e496b0490935712fe7c92c6bf28e42\", \"size\": \"2.89 MB\", \"duration\": 39.71}", "aliases": ["Come Home"], "size": "2.89 MB"}, {"id": "3-2", "name": "+3 [V2]", "artists": ["???"], "producers": ["<PERSON>"], "notes": "OG Filename: +3 Fix 126.05BPM AL Fix REF\nLonger version of, first seen in a list of files of songs made during the late 2018 Timbaland sessions. Seems to fix a bounce issue present within the first version.", "length": "221.14", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4cc6cad67f9fb7e8719ba3fc21469329", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4cc6cad67f9fb7e8719ba3fc21469329\", \"key\": \"+3\", \"title\": \"+3 [V2]\", \"artists\": \"(feat. ???) (prod. <PERSON>)\", \"aliases\": [\"Come Home\"], \"description\": \"OG Filename: +3 Fix 126.05BPM AL Fix REF\\nLonger version of, first seen in a list of files of songs made during the late 2018 Timbaland sessions. Seems to fix a bounce issue present within the first version.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"12643830f7994a04c2babd32ebece488\", \"url\": \"https://api.pillowcase.su/api/download/12643830f7994a04c2babd32ebece488\", \"size\": \"5.8 MB\", \"duration\": 221.14}", "aliases": ["Come Home"], "size": "5.8 MB"}, {"id": "3-3", "name": "+3 [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: +3_CY Demo\nCyHi reference track, still contains some <PERSON><PERSON><PERSON> vocals in the intro.", "length": "202.08", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/bb9bad4a14d263424785dc057cec2fc6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb9bad4a14d263424785dc057cec2fc6\", \"key\": \"+3\", \"title\": \"+3 [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Come Home\"], \"description\": \"OG Filename: +3_CY Demo\\nCyHi reference track, still contains some Kanye vocals in the intro.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"87f4ef007f68f5b50275ff7d8fe0b3af\", \"url\": \"https://api.pillowcase.su/api/download/87f4ef007f68f5b50275ff7d8fe0b3af\", \"size\": \"5.49 MB\", \"duration\": 202.08}", "aliases": ["Come Home"], "size": "5.49 MB"}, {"id": "aliens", "name": "Aliens [V23]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Timbaland", "Team Timbo"], "notes": "OG Filename: ALIENS TeamTimbo V1 12.19.18\nFeatures different production during the \"Godzilla\" intro. Leaked as part of the \"Alien\" mega bundle.", "length": "146.76", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5fe42d0fc597c6878e1230ef6375cf1d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5fe42d0fc597c6878e1230ef6375cf1d\", \"key\": \"Aliens\", \"title\": \"Aliens [V23]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Timbaland & Team Timbo)\", \"aliases\": [\"Alien\", \"Space X\"], \"description\": \"OG Filename: ALIENS TeamTimbo V1 12.19.18\\nFeatures different production during the \\\"Godzilla\\\" intro. Leaked as part of the \\\"Alien\\\" mega bundle.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"abfdfdcb0ec872a7055d541864a26717\", \"url\": \"https://api.pillowcase.su/api/download/abfdfdcb0ec872a7055d541864a26717\", \"size\": \"4.61 MB\", \"duration\": 146.76}", "aliases": ["Alien", "Space X"], "size": "4.61 MB"}, {"id": "aliens-5", "name": "Aliens [V24]", "artists": ["<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Timbaland", "Team Timbo"], "notes": "OG Filename: ALIENS Timbo TeamTimbo Boogz v1.12.21.18\nFeatures new elements not found in other versions. Leaked as a bonus for the \"Skeletons\" groupbuy.", "length": "146.76", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cc010bb5485bf2bcfc2d814f8de6597b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc010bb5485bf2bcfc2d814f8de6597b\", \"key\": \"Aliens\", \"title\": \"Aliens [V24]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, Timbaland & Team Timbo)\", \"aliases\": [\"Alien\", \"Space X\"], \"description\": \"OG Filename: ALIENS Timbo TeamTimbo Boogz v1.12.21.18\\nFeatures new elements not found in other versions. Leaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"66f724f60d8acc3d5a27ba433d15009c\", \"url\": \"https://api.pillowcase.su/api/download/66f724f60d8acc3d5a27ba433d15009c\", \"size\": \"4.61 MB\", \"duration\": 146.76}", "aliases": ["Alien", "Space X"], "size": "4.61 MB"}, {"id": "aliens-6", "name": "Aliens [V25]", "artists": [], "producers": [], "notes": "OG Filename: ALIENS (NO DRUMS) 130bpm\nIs missing the Godzilla intro and has no drums. From December 2018. Leaked as part of the \"Alien\" mega bundle.", "length": "103.44", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/34f50f42f295b3a6919529e498d868b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/34f50f42f295b3a6919529e498d868b6\", \"key\": \"Aliens\", \"title\": \"Aliens [V25]\", \"aliases\": [\"Alien\", \"Space X\"], \"description\": \"OG Filename: <PERSON>IENS (NO DRUMS) 130bpm\\nIs missing the Godzilla intro and has no drums. From December 2018. Leaked as part of the \\\"Alien\\\" mega bundle.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5e06f28b546c792f447e7db24b0374b2\", \"url\": \"https://api.pillowcase.su/api/download/5e06f28b546c792f447e7db24b0374b2\", \"size\": \"3.91 MB\", \"duration\": 103.44}", "aliases": ["Alien", "Space X"], "size": "3.91 MB"}, {"id": "alien", "name": "Alien [V26]", "artists": [], "producers": [], "notes": "OG Filename: Alien (new version 130 bpm)\nSimilar to the previous version but has new drums. From December 2018. Leaked as part of the \"Alien\" mega bundle.", "length": "184.61", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/2ca12f4ad1fac491c02c3ecca79c44e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ca12f4ad1fac491c02c3ecca79c44e3\", \"key\": \"Alien\", \"title\": \"Alien [V26]\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Alien (new version 130 bpm)\\nSimilar to the previous version but has new drums. From December 2018. Leaked as part of the \\\"Alien\\\" mega bundle.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9b8d9efcbfac45468a9d128984de1364\", \"url\": \"https://api.pillowcase.su/api/download/9b8d9efcbfac45468a9d128984de1364\", \"size\": \"5.21 MB\", \"duration\": 184.61}", "aliases": ["Space X"], "size": "5.21 MB"}, {"id": "alien-8", "name": "Alien [V27]", "artists": [], "producers": [], "notes": "OG Filename: ALIEN - NEW EXTENDED V3\nSimilar to the previous version but is more developed. From December 2018. Leaked as part of the \"Alien\" mega bundle.", "length": "185.09", "fileDate": 16869600, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/35b1cc3ac39b8607c7f461003710ee0d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/35b1cc3ac39b8607c7f461003710ee0d\", \"key\": \"Alien\", \"title\": \"Alien [V27]\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: ALIEN - NEW EXTENDED V3\\nSimilar to the previous version but is more developed. From December 2018. Leaked as part of the \\\"Alien\\\" mega bundle.\", \"date\": 16869600, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ed69261253575f4899efa4ef2b037003\", \"url\": \"https://api.pillowcase.su/api/download/ed69261253575f4899efa4ef2b037003\", \"size\": \"5.22 MB\", \"duration\": 185.09}", "aliases": ["Space X"], "size": "5.22 MB"}, {"id": "all-dreams-real", "name": "All Dreams Real [V1]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "OG Filename: Ye - All Dreams Real 161bpm REF\n 7 minute freestyle. Cut-down for later versions.", "length": "465.11", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8ddb41696abf5a96a7ec1ea7ca597c82", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ddb41696abf5a96a7ec1ea7ca597c82\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V1]\", \"artists\": \"(prod. Calabasas & Team Timbo)\", \"description\": \"OG Filename: Ye - All Dreams Real 161bpm REF\\n 7 minute freestyle. Cut-down for later versions.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ae84d504f8c7d9951ff3a7fcda949b5e\", \"url\": \"https://api.pillowcase.su/api/download/ae84d504f8c7d9951ff3a7fcda949b5e\", \"size\": \"9.7 MB\", \"duration\": 465.11}", "aliases": [], "size": "9.7 MB"}, {"id": "all-dreams-real-10", "name": "All Dreams Real [V2]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "OG Filename: All Dreams Real (Edited) V1 161bpm\nFirst version with an arranged Ka<PERSON><PERSON> verse. Has more simple production compared to other versions, including not having \"real real real\" echo mixed in. Originally found on dbree in 2020, before the OG file leaked.", "length": "198.32", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/80669ec697a2f1e9b404daa8bcef47d5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80669ec697a2f1e9b404daa8bcef47d5\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V2]\", \"artists\": \"(prod. Calabasas & Team Timbo)\", \"description\": \"OG Filename: All Dreams Real (Edited) V1 161bpm\\nFirst version with an arranged Kanye verse. Has more simple production compared to other versions, including not having \\\"real real real\\\" echo mixed in. Originally found on dbree in 2020, before the OG file leaked.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"31633e6f291da2e479872189e1cd3ac7\", \"url\": \"https://api.pillowcase.su/api/download/31633e6f291da2e479872189e1cd3ac7\", \"size\": \"5.43 MB\", \"duration\": 198.32}", "aliases": [], "size": "5.43 MB"}, {"id": "all-dreams-real-11", "name": "All Dreams Real [V3]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "OG Filenames: All Dreams (Team Timbo Calabasas) V6 161bpm & \nALL DREAMS V6 161bpm\nMixed version with <PERSON><PERSON><PERSON> mumble verse, leaked along with stems.", "length": "198.95", "fileDate": 16040160, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5bc872ffc73163b45d9a04c29d5d8692", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5bc872ffc73163b45d9a04c29d5d8692\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V3]\", \"artists\": \"(prod. Calabasas & Team Timbo)\", \"description\": \"OG Filenames: All Dreams (Team Timbo Calabasas) V6 161bpm & \\nALL DREAMS V6 161bpm\\nMixed version with <PERSON><PERSON><PERSON> mumble verse, leaked along with stems.\", \"date\": 16040160, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"20cf8d9a4d1f78a547702493c75a415b\", \"url\": \"https://api.pillowcase.su/api/download/20cf8d9a4d1f78a547702493c75a415b\", \"size\": \"5.44 MB\", \"duration\": 198.95}", "aliases": [], "size": "5.44 MB"}, {"id": "all-dreams-real-12", "name": "All Dreams Real [V4]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "Only has the chorus with open verse.", "length": "199.05", "fileDate": 15708384, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/741b881748b99ed644ea8e457510b299", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/741b881748b99ed644ea8e457510b299\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V4]\", \"artists\": \"(prod. Calabasas & Team Timbo)\", \"description\": \"Only has the chorus with open verse.\", \"date\": 15708384, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fb3eb4205e7e2ddfe2813dbf02a9775b\", \"url\": \"https://api.pillowcase.su/api/download/fb3eb4205e7e2ddfe2813dbf02a9775b\", \"size\": \"5.44 MB\", \"duration\": 199.05}", "aliases": [], "size": "5.44 MB"}, {"id": "all-dreams-real-13", "name": "⭐ All Dreams Real [V5]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "Has an <PERSON><PERSON> Clem<PERSON> reference verse replacing <PERSON><PERSON><PERSON>'s first verse.", "length": "199.03", "fileDate": 15709248, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/12138e43d4f25da2bfa4ffe1fc24e3b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/12138e43d4f25da2bfa4ffe1fc24e3b7\", \"key\": \"All Dreams Real\", \"title\": \"\\u2b50 All Dreams Real [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Calabasas & Team Timbo)\", \"description\": \"Has an <PERSON><PERSON> Clem<PERSON> reference verse replacing <PERSON><PERSON><PERSON>'s first verse.\", \"date\": 15709248, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6aadbd1e02b9c1b3423c5c1b47e8f41\", \"url\": \"https://api.pillowcase.su/api/download/d6aadbd1e02b9c1b3423c5c1b47e8f41\", \"size\": \"5.44 MB\", \"duration\": 199.03}", "aliases": [], "size": "5.44 MB"}, {"id": "all-dream-real", "name": "All Dream Real [V6]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "OG Filename: All Dream Real_Cy Demo\nOne of two CyHi references, with him doing a similar albeit shorter reference to the Ant Clemons reference verse.", "length": "198.31", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/059317852e706dcc0f50236cc33b9a10", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/059317852e706dcc0f50236cc33b9a10\", \"key\": \"All Dream Real\", \"title\": \"All Dream Real [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Calabasas & Team Timbo)\", \"aliases\": [\"All Dreams Real\"], \"description\": \"OG Filename: All Dream Real_Cy Demo\\nOne of two CyHi references, with him doing a similar albeit shorter reference to the Ant Clemons reference verse.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4289933580b8adfbae84ac8f83be662d\", \"url\": \"https://api.pillowcase.su/api/download/4289933580b8adfbae84ac8f83be662d\", \"size\": \"5.43 MB\", \"duration\": 198.31}", "aliases": ["All Dreams Real"], "size": "5.43 MB"}, {"id": "all-dreams-real-15", "name": "All Dreams Real [V7]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "Second CyHi reference track. Includes <PERSON><PERSON><PERSON> hook vocals and alternate CyHi lyrics. Unknown which of the two references came first.", "length": "10.45", "fileDate": 16047072, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/67f86b85f5bcfa5a4d94706333efd2a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/67f86b85f5bcfa5a4d94706333efd2a0\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Calabasas & Team Timbo)\", \"description\": \"Second CyHi reference track. Includes <PERSON><PERSON><PERSON> hook vocals and alternate CyHi lyrics. Unknown which of the two references came first.\", \"date\": 16047072, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f5276f230dc412e99c36d04726557341\", \"url\": \"https://api.pillowcase.su/api/download/f5276f230dc412e99c36d04726557341\", \"size\": \"2.42 MB\", \"duration\": 10.45}", "aliases": [], "size": "2.42 MB"}, {"id": "all-dreams-real-16", "name": "All Dreams Real [V9]", "artists": [], "producers": ["Calabasas", "Team Timbo"], "notes": "Consequence reference track. Was played on a July 19th, 2023 Instagram Live.", "length": "", "fileDate": 16897248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/7285a7e9f66ac80f0d2f36ccd4340b2a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7285a7e9f66ac80f0d2f36ccd4340b2a\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V9]\", \"artists\": \"(ref. Consequence) (prod. Calabasas & Team Timbo)\", \"description\": \"Consequence reference track. Was played on a July 19th, 2023 Instagram Live.\", \"date\": 16897248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "all-dreams-real-17", "name": "All Dreams Real [V10]", "artists": ["<PERSON><PERSON>"], "producers": ["Calabasas", "Team Timbo", "<PERSON><PERSON><PERSON>"], "notes": "Was gifted to <PERSON><PERSON><PERSON> during his January 2019 sessions with <PERSON> and <PERSON><PERSON><PERSON>. Identical to V7, but with additional drums added to the track's production.", "length": "199.55", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5fab30d2a2cf882d2426c92e3c19c33f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5fab30d2a2cf882d2426c92e3c19c33f\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V10]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON>, Team Timbo & Cal<PERSON>li)\", \"description\": \"Was gifted to <PERSON><PERSON><PERSON> during his January 2019 sessions with <PERSON> and <PERSON><PERSON><PERSON>. Identical to V7, but with additional drums added to the track's production.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d62b9f7fe4e380da0bf765649e2d9742\", \"url\": \"https://api.pillowcase.su/api/download/d62b9f7fe4e380da0bf765649e2d9742\", \"size\": \"5.45 MB\", \"duration\": 199.55}", "aliases": [], "size": "5.45 MB"}, {"id": "baddddd-little-darling", "name": "Baddddd Little Darling", "artists": [], "producers": [], "notes": "OG Filename: badddd <PERSON> darling 139BPM BOUNCE\nRough December freestyle. Leaked after a groupbuy.", "length": "146.23", "fileDate": 15932160, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/06f41ed0233908ec2153649861d75408", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/06f41ed0233908ec2153649861d75408\", \"key\": \"Baddddd Little Darling\", \"title\": \"Baddddd Little Darling\", \"aliases\": [\"Bad Little Darling\"], \"description\": \"OG Filename: badddd Little darling 139BPM BOUNCE\\nRough December freestyle. Leaked after a groupbuy.\", \"date\": 15932160, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d14a3320fac5bb924924016d39f24221\", \"url\": \"https://api.pillowcase.su/api/download/d14a3320fac5bb924924016d39f24221\", \"size\": \"4.6 MB\", \"duration\": 146.23}", "aliases": ["<PERSON> Little <PERSON>"], "size": "4.6 MB"}, {"id": "bipolar", "name": "Bipolar [V2]", "artists": [], "producers": ["Wheezy"], "notes": "OG Filename: ye 'BIPOLARmp3 - D#m or Am - 147.0\nOriginal version of <PERSON><PERSON><PERSON>'s track, solo <PERSON><PERSON><PERSON>. Reuses the verse from \"In Abundance\". Was given to <PERSON><PERSON> after A$AP <PERSON> played the song for him. Contains the same Wheezy production as the final song. File is dated to be December 2018, most likely from a reimport. Leaked after a low quality snippet originally surfaced.", "length": "130.77", "fileDate": 16845408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/55012029befd226ff05e1a07f329f75b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/55012029befd226ff05e1a07f329f75b\", \"key\": \"Bipolar\", \"title\": \"Bipolar [V2]\", \"artists\": \"(prod. Wheezy)\", \"aliases\": [\"Go2DaMoon\"], \"description\": \"OG Filename: ye 'BIPOLARmp3 - D#m or Am - 147.0\\nOriginal version of <PERSON><PERSON><PERSON>'s track, solo <PERSON><PERSON><PERSON>. Reuses the verse from \\\"In Abundance\\\". Was given to <PERSON><PERSON> after A$AP <PERSON> played the song for him. Contains the same Wheezy production as the final song. File is dated to be December 2018, most likely from a reimport. Leaked after a low quality snippet originally surfaced.\", \"date\": 16845408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a693b42a4a44fa6f0152faf50b811ad8\", \"url\": \"https://api.pillowcase.su/api/download/a693b42a4a44fa6f0152faf50b811ad8\", \"size\": \"4.35 MB\", \"duration\": 130.77}", "aliases": ["Go2DaMoon"], "size": "4.35 MB"}, {"id": "biscayne", "name": "Biscayne [V1]", "artists": [], "producers": ["FnZ"], "notes": "OG Filename: (8485) Biscanye 6 132bpm\nRough iPhone voice memo freestyle from late-December Yandhi sessions.", "length": "116.36", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/b0b2b456a159f3ffdd3c84cd3a89c1de", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b0b2b456a159f3ffdd3c84cd3a89c1de\", \"key\": \"B<PERSON><PERSON>yne\", \"title\": \"<PERSON><PERSON><PERSON>yne [V1]\", \"artists\": \"(prod. FnZ)\", \"description\": \"OG Filename: (8485) Biscanye 6 132bpm\\nRough iPhone voice memo freestyle from late-December Yandhi sessions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"55d795516889594a70b222ff28eb3fbd\", \"url\": \"https://api.pillowcase.su/api/download/55d795516889594a70b222ff28eb3fbd\", \"size\": \"4.12 MB\", \"duration\": 116.36}", "aliases": [], "size": "4.12 MB"}, {"id": "biscayne-21", "name": "Biscayne [V2]", "artists": [], "producers": ["FnZ"], "notes": "OG Filename: biscayne 6 BOUNCE 132BPM\nLater version, featuring rough, albeit this time studio recorded, vocals.", "length": "213.11", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0a9feebe9d14498868d7271080f9e241", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a9feebe9d14498868d7271080f9e241\", \"key\": \"Biscayne\", \"title\": \"<PERSON>iscayne [V2]\", \"artists\": \"(prod. FnZ)\", \"description\": \"OG Filename: biscayne 6 BOUNCE 132BPM\\nLater version, featuring rough, albeit this time studio recorded, vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b0c1101d570f217725bf893c7bd48869\", \"url\": \"https://api.pillowcase.su/api/download/b0c1101d570f217725bf893c7bd48869\", \"size\": \"5.67 MB\", \"duration\": 213.11}", "aliases": [], "size": "5.67 MB"}, {"id": "cash-to-burn", "name": "Cash To Burn [V2]", "artists": [], "producers": ["AllDay", "<PERSON>", "BoogzDaBeast", "Team Timbo", "Timbaland"], "notes": "OG Filename: Jam 4 (Cash to burn) (85.1) V2 (SAMPLE REPLAY) 12.19.18\nEarliest available version after it was further developed version from \"Jam 4\". Features less vocals and production compared to later versions.", "length": "125.57", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/519a7d33c9b7ff618b41fbfb08c7974e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/519a7d33c9b7ff618b41fbfb08c7974e\", \"key\": \"Cash To Burn\", \"title\": \"Cash To Burn [V2]\", \"artists\": \"(prod. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Jam 4\"], \"description\": \"OG Filename: Jam 4 (Cash to burn) (85.1) V2 (SAMPLE REPLAY) 12.19.18\\nEarliest available version after it was further developed version from \\\"Jam 4\\\". Features less vocals and production compared to later versions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c069c5c3b9352a710d8d7fc58bf8855a\", \"url\": \"https://api.pillowcase.su/api/download/c069c5c3b9352a710d8d7fc58bf8855a\", \"size\": \"4.27 MB\", \"duration\": 125.57}", "aliases": ["Jam 4"], "size": "4.27 MB"}, {"id": "cash-to-burn-23", "name": "Cash To Burn [V3]", "artists": [], "producers": ["AllDay", "<PERSON>", "BoogzDaBeast", "Team Timbo", "Timbaland"], "notes": "Throwaway track recorded in December of 2018. Samples \"Red Light\" by Greenslade. Leaked with stems after a groupbuy.", "length": "141.01", "fileDate": 15671232, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/533a65ff5bdc0cd9f67eed730127c3e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/533a65ff5bdc0cd9f67eed730127c3e3\", \"key\": \"Cash To Burn\", \"title\": \"Cash To Burn [V3]\", \"artists\": \"(prod. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"description\": \"Throwaway track recorded in December of 2018. Samples \\\"Red Light\\\" by Greenslade. Leaked with stems after a groupbuy.\", \"date\": 15671232, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"235715b649071accc07e48478c1c4eb1\", \"url\": \"https://api.pillowcase.su/api/download/235715b649071accc07e48478c1c4eb1\", \"size\": \"4.51 MB\", \"duration\": 141.01}", "aliases": [], "size": "4.51 MB"}, {"id": "cash-to-burn-24", "name": "✨ Cash To Burn [V5]", "artists": [], "producers": ["AllDay", "<PERSON>", "BoogzDaBeast", "Team Timbo", "Timbaland"], "notes": "OG Filename: Cash To Burn V3 Ant Vox 12.20.18\nAnt Clemons reference track, originally leaked along with its original stems. OG file later leaked as a bonus for the \"Skeletons\" groupbuy. Recorded on December 12, 2018.", "length": "141.09", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/026d17ffea18ccd1ed92c73069701e84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/026d17ffea18ccd1ed92c73069701e84\", \"key\": \"Cash To Burn\", \"title\": \"\\u2728 Cash To Burn [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"description\": \"OG Filename: Cash To Burn V3 Ant Vox 12.20.18\\nAnt Clemons reference track, originally leaked along with its original stems. OG file later leaked as a bonus for the \\\"Skeletons\\\" groupbuy. Recorded on December 12, 2018.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"905dcdf747ef4ce0091b6f8adc51f014\", \"url\": \"https://api.pillowcase.su/api/download/905dcdf747ef4ce0091b6f8adc51f014\", \"size\": \"4.51 MB\", \"duration\": 141.09}", "aliases": [], "size": "4.51 MB"}, {"id": "cash-for-you-to-burn", "name": "Cash For You To Burn [V6]", "artists": [], "producers": ["AllDay", "<PERSON>", "BoogzDaBeast", "Team Timbo", "Timbaland"], "notes": "OG Filename: Cash for You to Burn_CY_<PERSON><PERSON>'s \"Cash To Burn\" reference. Features a hook that would later be covered by <PERSON> on the Kenny <PERSON> and Styles P version.", "length": "124.15", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a21e5afea84a5093aaa36410ef5f167a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a21e5afea84a5093aaa36410ef5f167a\", \"key\": \"Cash For You To Burn\", \"title\": \"Cash For You To Burn [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Cash To Burn\"], \"description\": \"OG Filename: Cash for You to Burn_CY_Demo\\nCyHi's \\\"Cash To Burn\\\" reference. Features a hook that would later be covered by <PERSON> on the Kenny G and Styles P version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a3ef8fa9f479447068beb6b8759a8ded\", \"url\": \"https://api.pillowcase.su/api/download/a3ef8fa9f479447068beb6b8759a8ded\", \"size\": \"4.24 MB\", \"duration\": 124.15}", "aliases": ["Cash To Burn"], "size": "4.24 MB"}, {"id": "christ", "name": "Christ [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: Christ 160bpm\nExtremely rough demo track. Recorded in Timbaland sessions.", "length": "149.14", "fileDate": 15808608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/084c8ea1f9d7aeb5cd5ca8a0bc42f95f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/084c8ea1f9d7aeb5cd5ca8a0bc42f95f\", \"key\": \"Christ\", \"title\": \"Christ [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Sunday\"], \"description\": \"OG Filename: Christ 160bpm\\nExtremely rough demo track. Recorded in Timbaland sessions.\", \"date\": 15808608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bb60cacfa94f952af54a43a9fc21556b\", \"url\": \"https://api.pillowcase.su/api/download/bb60cacfa94f952af54a43a9fc21556b\", \"size\": \"4.64 MB\", \"duration\": 149.14}", "aliases": ["Sunday"], "size": "4.64 MB"}, {"id": "christ-27", "name": "Christ [V2]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "E.VAX"], "notes": "OG Filename: <PERSON> (evan version) 160bpm\nVersion of \"Christ\" with <PERSON>.<PERSON> drums.", "length": "156.06", "fileDate": 16765056, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8c47304d7ab7a439743d9e84d684cdd9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8c47304d7ab7a439743d9e84d684cdd9\", \"key\": \"Christ\", \"title\": \"Christ [V2]\", \"artists\": \"(prod. <PERSON>, <PERSON> & E.VAX)\", \"aliases\": [\"Sunday\"], \"description\": \"OG Filename: Christ (evan version) 160bpm\\nVersion of \\\"Christ\\\" with E<PERSON> drums.\", \"date\": 16765056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b81bb853b231afe8749f2fb0eca3a54b\", \"url\": \"https://api.pillowcase.su/api/download/b81bb853b231afe8749f2fb0eca3a54b\", \"size\": \"4.75 MB\", \"duration\": 156.06}", "aliases": ["Sunday"], "size": "4.75 MB"}, {"id": "christ-28", "name": "Christ [V3]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "E.VAX"], "notes": "OG Filename: <PERSON> (Evan version 2)\nCut-down of the E.VAX version.", "length": "109.04", "fileDate": 16765056, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/845abf287d8e3d315427620405f657f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/845abf287d8e3d315427620405f657f3\", \"key\": \"Christ\", \"title\": \"Christ [V3]\", \"artists\": \"(prod. <PERSON>, <PERSON> & E.VAX)\", \"aliases\": [\"Sunday\"], \"description\": \"OG Filename: <PERSON> (<PERSON> version 2)\\nCut-down of the E.VAX version.\", \"date\": 16765056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7d6d327aec9b719692cc95cfb05ca67a\", \"url\": \"https://api.pillowcase.su/api/download/7d6d327aec9b719692cc95cfb05ca67a\", \"size\": \"4 MB\", \"duration\": 109.04}", "aliases": ["Sunday"], "size": "4 MB"}, {"id": "color-1", "name": "✨ Color 1", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "OG Filename: KW - color 1 62bpm - BOUNCE -- (melly, kanye)\nPreviewed officially in a video with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Chief <PERSON> and <PERSON><PERSON>. Was called \"I Kill For Fun\" by fans. <PERSON>y leaked along with \"Bye Bye Baby\" and \"Me Too\" after a groupbuy.", "length": "186.94", "fileDate": 15766272, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/1f805315c16d06d7a7ae81176e5a5a6b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f805315c16d06d7a7ae81176e5a5a6b\", \"key\": \"Color 1\", \"title\": \"\\u2728 Color 1\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: KW - color 1 62bpm - BOUNCE -- (melly, kanye)\\nPreviewed officially in a video with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Chief <PERSON><PERSON> and <PERSON><PERSON>. Was called \\\"I Kill For Fun\\\" by fans. Fully leaked along with \\\"Bye Bye Baby\\\" and \\\"Me Too\\\" after a groupbuy.\", \"date\": 15766272, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8e6c855f49e492c7f0942aa62407317b\", \"url\": \"https://api.pillowcase.su/api/download/8e6c855f49e492c7f0942aa62407317b\", \"size\": \"5.25 MB\", \"duration\": 186.94}", "aliases": [], "size": "5.25 MB"}, {"id": "piano-sample", "name": "Piano Sample [V1]", "artists": [], "producers": ["Calabasas"], "notes": "OG Filename: piano sample 103BPM b minor d major BOUNCE\nA 10 minute \"Cuban Link\" version. This version is where the vocals for the later version come from, as they were chopped up from this freestyle. Original snippet leaked January 2nd, 2023.", "length": "14.68", "fileDate": 16726176, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a57b1de0339ce4598adbc666999b7314", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a57b1de0339ce4598adbc666999b7314\", \"key\": \"Piano Sample\", \"title\": \"Piano Sample [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Cuban Link\"], \"description\": \"OG Filename: piano sample 103BPM b minor d major BOUNCE\\nA 10 minute \\\"Cuban Link\\\" version. This version is where the vocals for the later version come from, as they were chopped up from this freestyle. Original snippet leaked January 2nd, 2023.\", \"date\": 16726176, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cd3da8197ff3b609a6e87a49589d651b\", \"url\": \"https://api.pillowcase.su/api/download/cd3da8197ff3b609a6e87a49589d651b\", \"size\": \"2.49 MB\", \"duration\": 14.68}", "aliases": ["Cuban Link"], "size": "2.49 MB"}, {"id": "cuban-link", "name": "Cuban Link [V2]", "artists": [], "producers": ["Calabasas", "Stereosine"], "notes": "OG Filename: Calabassas 2 YE Cuban Link UPDATE (StereoSines)\nRough freestyle. A rumour was spread that <PERSON><PERSON><PERSON> does reference vocals for <PERSON>, but it is likely untrue.", "length": "207.44", "fileDate": 16143840, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/48f6e5bfa36c2e323ee6fdeaf5ac9194", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48f6e5bfa36c2e323ee6fdeaf5ac9194\", \"key\": \"Cuban Link\", \"title\": \"Cuban Link [V2]\", \"artists\": \"(prod. Calabasas & Stereosine)\", \"description\": \"OG Filename: Calabassas 2 YE Cuban Link UPDATE (StereoSines)\\nRough freestyle. A rumour was spread that <PERSON><PERSON><PERSON> does reference vocals for <PERSON>, but it is likely untrue.\", \"date\": 16143840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"af781a62522f2af563114d91e7a89bd0\", \"url\": \"https://api.pillowcase.su/api/download/af781a62522f2af563114d91e7a89bd0\", \"size\": \"5.58 MB\", \"duration\": 207.44}", "aliases": [], "size": "5.58 MB"}, {"id": "jam-10", "name": "Jam 10 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 10\n10th TImbaland jam from the late 2018 Yandhi sessions, later reworked by E.<PERSON> into \"Different Life\".", "length": "192.97", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/200cb8c224e0e2c1c918478d06e33593", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/200cb8c224e0e2c1c918478d06e33593\", \"key\": \"Jam 10\", \"title\": \"Jam 10 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Different Life\"], \"description\": \"OG Filename: Jam 10\\n10th TImbaland jam from the late 2018 Yandhi sessions, later reworked by E.VAX into \\\"Different Life\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"06052b7e3911ffe2734ec790f5a806ee\", \"url\": \"https://api.pillowcase.su/api/download/06052b7e3911ffe2734ec790f5a806ee\", \"size\": \"5.34 MB\", \"duration\": 192.97}", "aliases": ["Different Life"], "size": "5.34 MB"}, {"id": "different-life", "name": "✨ Different Life [V2]", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: different life (jam10 evan version) 92bpm\nLater version, with a new arrangement and updated production from E.VAX.", "length": "148.75", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0dc23ca7cb2d23878a7db9683aafb776", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0dc23ca7cb2d23878a7db9683aafb776\", \"key\": \"Different Life\", \"title\": \"\\u2728 Different Life [V2]\", \"artists\": \"(prod. E.VAX)\", \"aliases\": [\"Jam 10\"], \"description\": \"OG Filename: different life (jam10 evan version) 92bpm\\nLater version, with a new arrangement and updated production from E.VAX.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"17b7e10f1cd45286bf9381ff5caa5195\", \"url\": \"https://api.pillowcase.su/api/download/17b7e10f1cd45286bf9381ff5caa5195\", \"size\": \"4.64 MB\", \"duration\": 148.75}", "aliases": ["Jam 10"], "size": "4.64 MB"}, {"id": "different-life-34", "name": "Different Life [V3]", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: different life jam 10 (evan ver 2)\nSecond version of <PERSON>'s \"Jam 10\" take, featuring a slightly different mix.", "length": "148.75", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c702bee4a9b0b0f308bedda6abf1c32f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c702bee4a9b0b0f308bedda6abf1c32f\", \"key\": \"Different Life\", \"title\": \"Different Life [V3]\", \"artists\": \"(prod. E.VAX)\", \"aliases\": [\"Jam 10\"], \"description\": \"OG Filename: different life jam 10 (evan ver 2)\\nSecond version of <PERSON>'s \\\"Jam 10\\\" take, featuring a slightly different mix.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fda029ce4db514b16bcef6483c8b844b\", \"url\": \"https://api.pillowcase.su/api/download/fda029ce4db514b16bcef6483c8b844b\", \"size\": \"4.64 MB\", \"duration\": 148.75}", "aliases": ["Jam 10"], "size": "4.64 MB"}, {"id": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": [], "notes": "OG Filename: (9109) DEADALUS 101bpm\nRough December-Yandhi iPhone voice memo freestyle. The sample used for the beat is unknown. Non-lossless file originally leaked October 14th, 2023.", "length": "111.48", "fileDate": 17019072, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/f0c13d671a0394381ae4cc2bd6b3004c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0c13d671a0394381ae4cc2bd6b3004c\", \"key\": \"Deadal<PERSON>\", \"title\": \"Deadal<PERSON>\", \"description\": \"OG Filename: (9109) DEADALUS 101bpm\\nRough December-Yan<PERSON> iPhone voice memo freestyle. The sample used for the beat is unknown. Non-lossless file originally leaked October 14th, 2023.\", \"date\": 17019072, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"88f39a37acee8e328441dd7b6c6c5f9b\", \"url\": \"https://api.pillowcase.su/api/download/88f39a37acee8e328441dd7b6c6c5f9b\", \"size\": \"4.04 MB\", \"duration\": 111.48}", "aliases": [], "size": "4.04 MB"}, {"id": "detroit", "name": "Detroit [V2]", "artists": [], "producers": ["FnZ", "RONNY J"], "notes": "OG Filename: Detroit_znf - <PERSON><PERSON>y Ref\nLater version of \"Speak Up\", with reference vocals from <PERSON><PERSON>. \"Detroit\" is most likely just the name to the beat.", "length": "125.86", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e09ada471efe16ca9e40527fcb5ada55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e09ada471efe16ca9e40527fcb5ada55\", \"key\": \"Detroit\", \"title\": \"Detroit [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. FnZ & RONNY J)\", \"aliases\": [\"Speak Up\"], \"description\": \"OG Filename: Detroit_znf - Tee Grizzley Ref\\nLater version of \\\"Speak Up\\\", with reference vocals from <PERSON><PERSON>. \\\"Detroit\\\" is most likely just the name to the beat.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"eed3a64cdc5542ee05c81c4fa7524619\", \"url\": \"https://api.pillowcase.su/api/download/eed3a64cdc5542ee05c81c4fa7524619\", \"size\": \"4.27 MB\", \"duration\": 125.86}", "aliases": ["Speak Up"], "size": "4.27 MB"}, {"id": "downtown-love", "name": "Downtown Love [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: KW - 5 115.5bpm\nSolo and earlier version of \"Downtown Soul\" confirmed by <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>'s vocals are likely the same in both version of the song. Advertised as 50% mumble but is mostly mumble.", "length": "99.29", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d786219467dffdd795381dd7074e4c07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d786219467dffdd795381dd7074e4c07\", \"key\": \"Downtown Love\", \"title\": \"Downtown Love [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Downtown Soul\", \"Timbaland Freestyle 5\"], \"description\": \"OG Filename: KW - 5 115.5bpm\\nSolo and earlier version of \\\"Downtown Soul\\\" confirmed by <PERSON><PERSON><PERSON>'s vocals are likely the same in both version of the song. Advertised as 50% mumble but is mostly mumble.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b52c0923131e5961c5b89e033d74e861\", \"url\": \"https://api.pillowcase.su/api/download/b52c0923131e5961c5b89e033d74e861\", \"size\": \"3.85 MB\", \"duration\": 99.29}", "aliases": ["Downtown Soul", "Timbaland Freestyle 5"], "size": "3.85 MB"}, {"id": "downtown-soul", "name": "Downtown Soul [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Snippet of a Timbaland session track. Sold to <PERSON> and was being groupbought before it failed. Likely from the Timbaland sessions. Features 2 and a half minutes of finished <PERSON><PERSON><PERSON> vocals and around 2 minutes of half mumble, half finished <PERSON><PERSON><PERSON> vocals. Snippets leaked Feb 15th 2021. Latest snippet leaked Feb 1st 2025.", "length": "24.92", "fileDate": 17383680, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/39a2140a2369b6c562155d7d7a2b5faf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/39a2140a2369b6c562155d7d7a2b5faf\", \"key\": \"Downtown Soul\", \"title\": \"Downtown Soul [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Downtown Love\", \"Timbaland Freestyle 5\"], \"description\": \"Snippet of a Timbaland session track. Sold to <PERSON> and was being groupbought before it failed. Likely from the Timbaland sessions. Features 2 and a half minutes of finished <PERSON><PERSON><PERSON> vocals and around 2 minutes of half mumble, half finished <PERSON><PERSON><PERSON> vocals. Snippets leaked Feb 15th 2021. Latest snippet leaked Feb 1st 2025.\", \"date\": 17383680, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"737073a4f3d0e855bda013df4ccc0bdc\", \"url\": \"https://api.pillowcase.su/api/download/737073a4f3d0e855bda013df4ccc0bdc\", \"size\": \"2.66 MB\", \"duration\": 24.92}", "aliases": ["Downtown Love", "Timbaland Freestyle 5"], "size": "2.66 MB"}, {"id": "downtown-soul-39", "name": "Downtown Soul [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Snippet of a Timbaland session track. Sold to <PERSON> and was being groupbought before it failed. Likely from the Timbaland sessions. Features 2 and a half minutes of finished <PERSON><PERSON><PERSON> vocals and around 2 minutes of half mumble, half finished <PERSON><PERSON><PERSON> vocals. Snippets leaked Feb 15th 2021. Latest snippet leaked Feb 1st 2025.", "length": "49.68", "fileDate": 17383680, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/9ec51c33381796f3edee5697147d8fdc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9ec51c33381796f3edee5697147d8fdc\", \"key\": \"Downtown Soul\", \"title\": \"Downtown Soul [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Downtown Love\", \"Timbaland Freestyle 5\"], \"description\": \"Snippet of a Timbaland session track. Sold to <PERSON> and was being groupbought before it failed. Likely from the Timbaland sessions. Features 2 and a half minutes of finished <PERSON><PERSON><PERSON> vocals and around 2 minutes of half mumble, half finished <PERSON><PERSON><PERSON> vocals. Snippets leaked Feb 15th 2021. Latest snippet leaked Feb 1st 2025.\", \"date\": 17383680, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4bd643195dc6701ee91c935d01f421d6\", \"url\": \"https://api.pillowcase.su/api/download/4bd643195dc6701ee91c935d01f421d6\", \"size\": \"3.05 MB\", \"duration\": 49.68}", "aliases": ["Downtown Love", "Timbaland Freestyle 5"], "size": "3.05 MB"}, {"id": "downtown-soul-40", "name": "Downtown Soul [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Snippet of a Timbaland session track. Sold to <PERSON> and was being groupbought before it failed. Likely from the Timbaland sessions. Features 2 and a half minutes of finished <PERSON><PERSON><PERSON> vocals and around 2 minutes of half mumble, half finished <PERSON><PERSON><PERSON> vocals. Snippets leaked Feb 15th 2021. Latest snippet leaked Feb 1st 2025.", "length": "15.95", "fileDate": 17383680, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/2368df77d2af9b8e9f4dcf5e0fb304ba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2368df77d2af9b8e9f4dcf5e0fb304ba\", \"key\": \"Downtown Soul\", \"title\": \"Downtown Soul [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Downtown Love\", \"Timbaland Freestyle 5\"], \"description\": \"Snippet of a Timbaland session track. Sold to <PERSON> and was being groupbought before it failed. Likely from the Timbaland sessions. Features 2 and a half minutes of finished <PERSON><PERSON><PERSON> vocals and around 2 minutes of half mumble, half finished <PERSON><PERSON><PERSON> vocals. Snippets leaked Feb 15th 2021. Latest snippet leaked Feb 1st 2025.\", \"date\": 17383680, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f20d825ba56085f645d07cef3b48cb03\", \"url\": \"https://api.pillowcase.su/api/download/f20d825ba56085f645d07cef3b48cb03\", \"size\": \"2.51 MB\", \"duration\": 15.95}", "aliases": ["Downtown Love", "Timbaland Freestyle 5"], "size": "2.51 MB"}, {"id": "echelon", "name": "Echelon [V1]", "artists": [], "producers": ["DRTWRK"], "notes": "OG Filename: (8751) DRT ECHELON 130bpm\nFirst version of \"Echelon\". Rough iPhone voice memo track, fairly finished in terms of freestyled lyrics from this time. Much simpler and shorter compared to other versions.", "length": "118.19", "fileDate": 17027712, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e5e7f47c3c93640e528501380a365737", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e5e7f47c3c93640e528501380a365737\", \"key\": \"Echelon\", \"title\": \"Echelon [V1]\", \"artists\": \"(prod. DRTWRK)\", \"description\": \"OG Filename: (8751) DRT ECHELON 130bpm\\nFirst version of \\\"Echelon\\\". Rough iPhone voice memo track, fairly finished in terms of freestyled lyrics from this time. Much simpler and shorter compared to other versions.\", \"date\": 17027712, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6005718635bdb32bbb6cc48b9e804631\", \"url\": \"https://api.pillowcase.su/api/download/6005718635bdb32bbb6cc48b9e804631\", \"size\": \"4.15 MB\", \"duration\": 118.19}", "aliases": [], "size": "4.15 MB"}, {"id": "echelon-42", "name": "Echelon [V2]", "artists": [], "producers": ["DRTWRK", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: (8751) DRT ECHELON 130bpm (Kosine) REF\nFirst \"Echelon\" version with extra Kosine production, much rougher compared to the later version.", "length": "302.85", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/647e2c28aea78c81872e1d986b5c1189", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/647e2c28aea78c81872e1d986b5c1189\", \"key\": \"Echelon\", \"title\": \"Echelon [V2]\", \"artists\": \"(prod. DRTWRK & Kosine)\", \"description\": \"OG Filename: (8751) DRT ECHELON 130bpm (Kosine) REF\\nFirst \\\"Echelon\\\" version with extra Kosine production, much rougher compared to the later version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fec9e7e1303b48793999d52ab50c48db\", \"url\": \"https://api.pillowcase.su/api/download/fec9e7e1303b48793999d52ab50c48db\", \"size\": \"7.1 MB\", \"duration\": 302.85}", "aliases": [], "size": "7.1 MB"}, {"id": "echelon-43", "name": "✨ Echelon [V3]", "artists": [], "producers": ["DRTWRK", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: (8751) DRT ECHELON 130bpm (Kosine) REF V2\nFinal version with <PERSON> vocals, with a new mix of the beat post-Kosine. Originally sold by Alek.", "length": "302.9", "fileDate": 15933888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/65507729fa6479e39a2e9811aa1e4690", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65507729fa6479e39a2e9811aa1e4690\", \"key\": \"Echelon\", \"title\": \"\\u2728 Echelon [V3]\", \"artists\": \"(prod. DR<PERSON><PERSON><PERSON> & Kosine)\", \"description\": \"OG Filename: (8751) DRT ECHELON 130bpm (Kosine) REF V2\\nFinal version with Ye vocals, with a new mix of the beat post-Kosine. Originally sold by Alek.\", \"date\": 15933888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"55239fae675a0e8a0af5c7ab46593ca5\", \"url\": \"https://api.pillowcase.su/api/download/55239fae675a0e8a0af5c7ab46593ca5\", \"size\": \"7.1 MB\", \"duration\": 302.9}", "aliases": [], "size": "7.1 MB"}, {"id": "echelon-44", "name": "Echelon [V4]", "artists": [], "producers": ["DRTWRK", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Echelon_CY Demo\n<PERSON> \"Echelon\" reference track. Recorded in late December over the more finished instrumental with <PERSON><PERSON><PERSON>'s additions. Features an almost 4 minute long open verse.", "length": "302.83", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d681f41796cd08cead89d8b5b94b55ea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d681f41796cd08cead89d8b5b94b55ea\", \"key\": \"Echelon\", \"title\": \"Echelon [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. DRTWRK & Kosine)\", \"description\": \"OG Filename: Echelon_CY Demo\\nCyHi \\\"Echelon\\\" reference track. Recorded in late December over the more finished instrumental with <PERSON><PERSON><PERSON>'s additions. Features an almost 4 minute long open verse.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b32dff08597a2a89310fa2933b1d0497\", \"url\": \"https://api.pillowcase.su/api/download/b32dff08597a2a89310fa2933b1d0497\", \"size\": \"7.1 MB\", \"duration\": 302.83}", "aliases": [], "size": "7.1 MB"}, {"id": "end-of-it", "name": "End Of It", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: End Of It 126bpm\n<PERSON> on a skateboard. What else is there to say about this? Recorded December 8th, 2018 during the Timbaland sessions.", "length": "294.35", "fileDate": 15778368, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/51808c26002a05709de93776e3a5f6f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/51808c26002a05709de93776e3a5f6f1\", \"key\": \"End Of It\", \"title\": \"End Of It\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Wait For God\"], \"description\": \"OG Filename: End Of It 126bpm\\nDrake on a skateboard. What else is there to say about this? Recorded December 8th, 2018 during the Timbaland sessions.\", \"date\": 15778368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"437934f56070564d991820f877ec177a\", \"url\": \"https://api.pillowcase.su/api/download/437934f56070564d991820f877ec177a\", \"size\": \"6.97 MB\", \"duration\": 294.35}", "aliases": ["Wait For God"], "size": "6.97 MB"}, {"id": "freak", "name": "Freak [V1]", "artists": [], "producers": ["Team Timbo", "Timbaland"], "notes": "OG Filename: FRE<PERSON><PERSON> (TIimbo_TeamTimbo) V1 105bpm\nEarliest known version of \"Freak\". Features a different mix compared to the other leaked version.", "length": "123.48", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c1257aa22159ecddc40891b88e960569", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1257aa22159ecddc40891b88e960569\", \"key\": \"Freak\", \"title\": \"Freak [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. Team Timbo & Timbaland)\", \"description\": \"OG Filename: FREAK (TIimbo_TeamTimbo) V1 105bpm\\nEarliest known version of \\\"Freak\\\". Features a different mix compared to the other leaked version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"035e5614c904385a95f4dae8ba61804f\", \"url\": \"https://api.pillowcase.su/api/download/035e5614c904385a95f4dae8ba61804f\", \"size\": \"4.23 MB\", \"duration\": 123.48}", "aliases": [], "size": "4.23 MB"}, {"id": "freak-47", "name": "Freak [V5]", "artists": [], "producers": ["Timbaland", "Team Timbo", "<PERSON>", "<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: FREA<PERSON> (TIMBO-FV-AL) 195 V2 TIM/TEAMTIM/BOOGZ/ANDYC\nSong that was released as an Ant Clemons song, which was originally his reference for it.", "length": "125.78", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4b7a7c6c0b794069a9aaa9df385eac02", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4b7a7c6c0b794069a9aaa9df385eac02\", \"key\": \"Freak\", \"title\": \"Freak [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (<PERSON>d<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> & Bo<PERSON>zDaBeast)\", \"description\": \"OG Filename: FREAK (TIMBO-FV-AL) 195 V2 TIM/TEAMTIM/BOOGZ/ANDYC\\nSong that was released as an An<PERSON> Clemons song, which was originally his reference for it.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2ae3b7fb006aa63d226b038f5f0eea25\", \"url\": \"https://api.pillowcase.su/api/download/2ae3b7fb006aa63d226b038f5f0eea25\", \"size\": \"4.27 MB\", \"duration\": 125.78}", "aliases": [], "size": "4.27 MB"}, {"id": "freak-48", "name": "Freak [V6]", "artists": ["<PERSON><PERSON>"], "producers": ["Timbaland", "Team Timbo", "<PERSON>", "<PERSON>", "BoogzDaBeast"], "notes": "<PERSON><PERSON><PERSON> recorded an iPhone mumble demo for \"Freak\" that you can hear in a now removed YouTube video. Unknown if this was ever turned into a bounce.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5f203a38348cc0022a821cb40a810389", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5f203a38348cc0022a821cb40a810389\", \"key\": \"Freak\", \"title\": \"Freak [V6]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod<PERSON>, <PERSON>, <PERSON>, <PERSON> & BoogzDaBeast)\", \"description\": \"<PERSON><PERSON><PERSON> recorded an iPhone mumble demo for \\\"Freak\\\" that you can hear in a now removed YouTube video. Unknown if this was ever turned into a bounce.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "friends", "name": "Friends", "artists": [], "producers": [], "notes": "Yandhi era freestyle. Has similar vocal quality to \"Oh Yeah\", so it is possibly from the same session.", "length": "10.29", "fileDate": 16797888, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/502b3e6d91932695a2e0b115d791b5c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/502b3e6d91932695a2e0b115d791b5c8\", \"key\": \"Friends\", \"title\": \"Friends\", \"description\": \"Yandhi era freestyle. Has similar vocal quality to \\\"Oh Yeah\\\", so it is possibly from the same session.\", \"date\": 16797888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ba8d7d43a37e9f9948364a2437fc6737\", \"url\": \"https://api.pillowcase.su/api/download/ba8d7d43a37e9f9948364a2437fc6737\", \"size\": \"2.42 MB\", \"duration\": 10.29}", "aliases": [], "size": "2.42 MB"}, {"id": "garden", "name": "✨ Garden [V14]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "The-Dream"], "producers": ["<PERSON>"], "notes": "OG Filename: Garden (StereoSines_Phillip/Keys)\nDecember 2018 version that is uniquely upbeat compared to previous versions and the later 2019 versions. Features a switch to prominent club/house production halfway through, likely brought on by producer <PERSON>. CDQ file leaked as a bonus for the \"Skeletons\" groupbuy.", "length": "137.21", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/089df45b943446f04a069b84a243da46", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/089df45b943446f04a069b84a243da46\", \"key\": \"Garden\", \"title\": \"\\u2728 Garden [V14]\", \"artists\": \"(feat. <PERSON>gn, <PERSON><PERSON> & The-Dream) (prod. <PERSON>)\", \"aliases\": [\"The Garden\"], \"description\": \"OG Filename: Garden (StereoSines_Phillip/Keys)\\nDecember 2018 version that is uniquely upbeat compared to previous versions and the later 2019 versions. Features a switch to prominent club/house production halfway through, likely brought on by producer <PERSON>. CDQ file leaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"17a583256ff645a1df2f2db7927de2b5\", \"url\": \"https://api.pillowcase.su/api/download/17a583256ff645a1df2f2db7927de2b5\", \"size\": \"4.45 MB\", \"duration\": 137.21}", "aliases": ["The Garden"], "size": "4.45 MB"}, {"id": "garden-51", "name": "Garden [V16]", "artists": ["Ty Dolla $ign", "<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: Garden w/slim \nFirst known version of \"The Garden\" with vocals from <PERSON> (of 112), replacing <PERSON><PERSON> on the second verse. Reference track for the Sunday Service Choir. CDQ file leaked as a bonus for the \"Skeletons\" groupbuy.", "length": "188.64", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/163954ec9e9c312c02ec2de9a84501d7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/163954ec9e9c312c02ec2de9a84501d7\", \"key\": \"Garden\", \"title\": \"Garden [V16]\", \"artists\": \"(feat. <PERSON>ign & Slim) (prod. <PERSON>)\", \"aliases\": [\"The Garden\"], \"description\": \"OG Filename: Garden w/slim \\nFirst known version of \\\"The Garden\\\" with vocals from <PERSON> (of 112), replacing <PERSON><PERSON> on the second verse. Reference track for the Sunday Service Choir. CDQ file leaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"69a78dfb0e5f8a69c023e0b9e3c6389d\", \"url\": \"https://api.pillowcase.su/api/download/69a78dfb0e5f8a69c023e0b9e3c6389d\", \"size\": \"5.28 MB\", \"duration\": 188.64}", "aliases": ["The Garden"], "size": "5.28 MB"}, {"id": "gg-sample", "name": "GG Sample", "artists": [], "producers": [], "notes": "OG Filename: G<PERSON> Sample 90BPM BOUNCE\nRough late-December 2018 mumble demo. Snippet leaked from unreleasedounds on January 7th, 2023, with the full song leaking on October 14th, 2023.", "length": "114.74", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/be5a1292022ed496030f0779fe3f5073", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/be5a1292022ed496030f0779fe3f5073\", \"key\": \"GG Sample\", \"title\": \"GG Sample\", \"description\": \"OG Filename: GG Sample 90BPM BOUNCE\\nRough late-December 2018 mumble demo. Snippet leaked from unreleasedounds on January 7th, 2023, with the full song leaking on October 14th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d381d35c1ec2910dd77fc967a689a9e6\", \"url\": \"https://api.pillowcase.su/api/download/d381d35c1ec2910dd77fc967a689a9e6\", \"size\": \"4.09 MB\", \"duration\": 114.74}", "aliases": [], "size": "4.09 MB"}, {"id": "holla", "name": "<PERSON><PERSON> [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: JAHAAN OLD ENGLISH-holla 138BPM BOUNCE\nFreestyle from the late 2018 and early 2019 Timbaland sessions, later chopped into \"Go Now\".", "length": "286.2", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/67c23391a6e353d5669d1b99fc699a85", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/67c23391a6e353d5669d1b99fc699a85\", \"key\": \"<PERSON>lla\", \"title\": \"<PERSON>lla [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Go Now\", \"Old English\"], \"description\": \"OG Filename: JAHAAN OLD ENGLISH-holla 138BPM BOUNCE\\nFreestyle from the late 2018 and early 2019 Timbaland sessions, later chopped into \\\"Go Now\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"20bbc42f27f95f9450d56e94f215f3b0\", \"url\": \"https://api.pillowcase.su/api/download/20bbc42f27f95f9450d56e94f215f3b0\", \"size\": \"6.84 MB\", \"duration\": 286.2}", "aliases": ["Go Now", "Old English"], "size": "6.84 MB"}, {"id": "go-now", "name": "Go Now [V2]", "artists": [], "producers": ["DRTWRK", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: JAHAAN OLD ENGLISH-Go Now (DRTY)138BPM BOUNCE\nCutdown version of \"Holla\". Snippet leaked January 7th, 2023, with the full song leaking on October 14th, 2023.", "length": "96.31", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cbf1087305bf4f6c20ce0a415684aecd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cbf1087305bf4f6c20ce0a415684aecd\", \"key\": \"Go Now\", \"title\": \"Go Now [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Holla\", \"Old English\"], \"description\": \"OG Filename: JAHAAN OLD ENGLISH-Go Now (DRTY)138BPM BOUNCE\\nCutdown version of \\\"Holla\\\". Snippet leaked January 7th, 2023, with the full song leaking on October 14th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"260879c194357cb892c9910dc0107a99\", \"url\": \"https://api.pillowcase.su/api/download/260879c194357cb892c9910dc0107a99\", \"size\": \"3.8 MB\", \"duration\": 96.31}", "aliases": ["<PERSON><PERSON>", "Old English"], "size": "3.8 MB"}, {"id": "he-s-back", "name": "He's <PERSON>", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.", "length": "61.31", "fileDate": 15903648, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/feead71ad5f29468148b0f1adcf89231", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/feead71ad5f29468148b0f1adcf89231\", \"key\": \"He's Back\", \"title\": \"He's Back\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.\", \"date\": 15903648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5486417f5486ea21c3fb65ac00bb94f1\", \"url\": \"https://api.pillowcase.su/api/download/5486417f5486ea21c3fb65ac00bb94f1\", \"size\": \"3.24 MB\", \"duration\": 61.31}", "aliases": [], "size": "3.24 MB"}, {"id": "healing", "name": "Healing [V1]", "artists": [], "producers": [], "notes": "OG Filename: HEALING 132bpm RAW CHOP V1\nAnt Clemons \"Healing\" reference track. Unknown if Kanye version exists. Snippets originally surfaced in March 2021.", "length": "320.06", "fileDate": 16229376, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/419910c4533428def390a1a8cfb6176d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/419910c4533428def390a1a8cfb6176d\", \"key\": \"Healing\", \"title\": \"Healing [V1]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: HEALING 132bpm RAW CHOP V1\\nAnt Clemons \\\"Healing\\\" reference track. Unknown if Kanye version exists. Snippets originally surfaced in March 2021.\", \"date\": 16229376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c5ed42a0e451bc3a4df9d044e397eed3\", \"url\": \"https://api.pillowcase.su/api/download/c5ed42a0e451bc3a4df9d044e397eed3\", \"size\": \"7.38 MB\", \"duration\": 320.06}", "aliases": [], "size": "7.38 MB"}, {"id": "heights", "name": "Heights", "artists": [], "producers": [], "notes": "OG Filename: (8755) HEIGHTS MIA 142bpm\nRough iPhone voice memo freestyle, most likely from late 2018. The beat cuts out near the end of the song. A snippet was posted by unreleasedsounds, before a non-lossless file leaked on October 14th, 2023.", "length": "135.21", "fileDate": 17027712, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/adc3fec2ca7e49a20a5ec3430a50716f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/adc3fec2ca7e49a20a5ec3430a50716f\", \"key\": \"Heights\", \"title\": \"Heights\", \"description\": \"OG Filename: (8755) HEIGHTS MIA 142bpm\\nRough iPhone voice memo freestyle, most likely from late 2018. The beat cuts out near the end of the song. A snippet was posted by unreleasedsounds, before a non-lossless file leaked on October 14th, 2023.\", \"date\": 17027712, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4930e2671df7b982eedfbda737011506\", \"url\": \"https://api.pillowcase.su/api/download/4930e2671df7b982eedfbda737011506\", \"size\": \"4.42 MB\", \"duration\": 135.21}", "aliases": [], "size": "4.42 MB"}, {"id": "high-sound", "name": "High Sound", "artists": [], "producers": [], "notes": "OG Filename: high sound 170BPM BOUNCE\nRough Yandhi-era freestyle, from the December sessions. Was being sold as \"Freestyle 15\". First snippet came from TheSource tier 2, with another later coming from unreleasedsounds.", "length": "118.2", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0bbe94e90a79054f8866b4d483a4bc06", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0bbe94e90a79054f8866b4d483a4bc06\", \"key\": \"High Sound\", \"title\": \"High Sound\", \"aliases\": [\"Freestyle 15\"], \"description\": \"OG Filename: high sound 170BPM BOUNCE\\nRough Yandhi-era freestyle, from the December sessions. Was being sold as \\\"Freestyle 15\\\". First snippet came from TheSource tier 2, with another later coming from unreleasedsounds.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"379db110085cccfdc81f90287ef74bb8\", \"url\": \"https://api.pillowcase.su/api/download/379db110085cccfdc81f90287ef74bb8\", \"size\": \"4.15 MB\", \"duration\": 118.2}", "aliases": ["Freestyle 15"], "size": "4.15 MB"}, {"id": "hold-me-down", "name": "Hold Me Down", "artists": [], "producers": [], "notes": "OG Filename: Hold Me Down (<PERSON> 6 87.59bpm)\niPhone voice memo freestyle from December 2018. Shares some lyrics with \"<PERSON>yce Vieux\". Snippet leaked October 30th, 2022, with the full song leaking on October 14th, 2023. Lossless leaked 7/12/23.", "length": "90.42", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/794d730d5e48ac24aa760b2832acaeae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/794d730d5e48ac24aa760b2832acaeae\", \"key\": \"Hold Me Down\", \"title\": \"Hold Me Down\", \"aliases\": [\"<PERSON>der 6\"], \"description\": \"OG Filename: Hold Me Down (<PERSON> 6 87.59bpm)\\niPhone voice memo freestyle from December 2018. Shares some lyrics with \\\"Nyce Vieux\\\". Snippet leaked October 30th, 2022, with the full song leaking on October 14th, 2023. Lossless leaked 7/12/23.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b9232c57e0c314d7d7435bb79f128bbb\", \"url\": \"https://api.pillowcase.su/api/download/b9232c57e0c314d7d7435bb79f128bbb\", \"size\": \"3.7 MB\", \"duration\": 90.42}", "aliases": ["<PERSON> 6"], "size": "3.7 MB"}, {"id": "home", "name": "Home [V5]", "artists": [], "producers": ["BoogzDaBeast", "<PERSON>", "<PERSON>", "<PERSON>", "RONNY J", "Team Timbo", "Timbaland"], "notes": "OG Filename: Home (TeamTimbo_Boogz) V1 12.19.18\nFilename seen on list of Home/Follow the Light files.", "length": "147.88", "fileDate": 16891200, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8f1b31dd68fbeff29fb6ba15bb34391c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8f1b31dd68fbeff29fb6ba15bb34391c\", \"key\": \"Home\", \"title\": \"Home [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Follow The Light\"], \"description\": \"OG Filename: Home (TeamTimbo_Boogz) V1 12.19.18\\nFilename seen on list of Home/Follow the Light files.\", \"date\": 16891200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"39d0a8cf8ab6626c076d1ac10fdf93dc\", \"url\": \"https://api.pillowcase.su/api/download/39d0a8cf8ab6626c076d1ac10fdf93dc\", \"size\": \"4.62 MB\", \"duration\": 147.88}", "aliases": ["Follow The Light"], "size": "4.62 MB"}, {"id": "home-61", "name": "Home [V6]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "<PERSON>", "RONNY J", "Team Timbo", "Timbaland"], "notes": "OG Filename: Home (Timbo:TeamTimbo:Boogz) V2 12.20.18\nFilename seen on list of Home/Follow the Light files.", "length": "149.63", "fileDate": 16891200, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/433d1d37b0ee4b67aa63d66be4809230", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/433d1d37b0ee4b67aa63d66be4809230\", \"key\": \"Home\", \"title\": \"Home [V6]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Follow The Light\"], \"description\": \"OG Filename: Home (Timbo:TeamTimbo:Boogz) V2 12.20.18\\nFilename seen on list of Home/Follow the Light files.\", \"date\": 16891200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d246612d5b8b5f1e87409f7f9891263a\", \"url\": \"https://api.pillowcase.su/api/download/d246612d5b8b5f1e87409f7f9891263a\", \"size\": \"4.65 MB\", \"duration\": 149.63}", "aliases": ["Follow The Light"], "size": "4.65 MB"}, {"id": "home-62", "name": "Home [V7]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "<PERSON>", "RONNY J", "Team Timbo", "Timbaland"], "notes": "OG Filename: Home (Timbaland:TeamTimbo:Boogz) V2 12.21.18\nFilename seen on list of Home/Follow the Light files.", "length": "149.57", "fileDate": 16891200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cafa44d423d69ddade2c361563dfb90a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cafa44d423d69ddade2c361563dfb90a\", \"key\": \"Home\", \"title\": \"Home [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Follow The Light\"], \"description\": \"OG Filename: Home (Timbaland:TeamTimbo:Boogz) V2 12.21.18\\nFilename seen on list of Home/Follow the Light files.\", \"date\": 16891200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"298a43444008f4f796f61935b0f72868\", \"url\": \"https://api.pillowcase.su/api/download/298a43444008f4f796f61935b0f72868\", \"size\": \"4.65 MB\", \"duration\": 149.57}", "aliases": ["Follow The Light"], "size": "4.65 MB"}, {"id": "home-63", "name": "Home [V8]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "<PERSON>", "RONNY J", "Team Timbo", "Timbaland"], "notes": "OG Filename: Home (<PERSON>) V3\nVersion of \"Home\" with reworked production by <PERSON><PERSON><PERSON>.", "length": "149.63", "fileDate": 16112736, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6d19239bd5f5f43a661aa8cc74f9b26d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d19239bd5f5f43a661aa8cc74f9b26d\", \"key\": \"Home\", \"title\": \"Home [V8]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Follow The Light\"], \"description\": \"OG Filename: Home (Tim Drums) V3\\nVersion of \\\"Home\\\" with reworked production by <PERSON>baland.\", \"date\": 16112736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a76c40842d2a049d4a34bbd127e5b8d3\", \"url\": \"https://api.pillowcase.su/api/download/a76c40842d2a049d4a34bbd127e5b8d3\", \"size\": \"4.65 MB\", \"duration\": 149.63}", "aliases": ["Follow The Light"], "size": "4.65 MB"}, {"id": "homestead", "name": "Homestead", "artists": [], "producers": [], "notes": "OG Filename: (8759) HOMESTEAD MIA 146bpm\nDecember 2018 voice memo freestyle. Reuses the hook from \"<PERSON> <PERSON><PERSON><PERSON>'s Son\". Instrumental leaked September 20th, 2022, with the full song leaking on October 14th, 2023. Lossless leaked 7/12/23.", "length": "105.21", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3916b03fea67a0e1cfaaf270ab9517f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3916b03fea67a0e1cfaaf270ab9517f2\", \"key\": \"Homestead\", \"title\": \"Homestead\", \"description\": \"OG Filename: (8759) HOMESTEAD MIA 146bpm\\nDecember 2018 voice memo freestyle. Reuses the hook from \\\"<PERSON><PERSON>'s Son\\\". Instrumental leaked September 20th, 2022, with the full song leaking on October 14th, 2023. Lossless leaked 7/12/23.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"feba0f0e857c9881f37af43a027207a4\", \"url\": \"https://api.pillowcase.su/api/download/feba0f0e857c9881f37af43a027207a4\", \"size\": \"3.94 MB\", \"duration\": 105.21}", "aliases": [], "size": "3.94 MB"}, {"id": "i-wasn-t-christian-enough", "name": "I Wasn't Christian Enough", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.", "length": "66.56", "fileDate": 15903648, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/fe6830a1f1e4f1ab671be0890e4aafec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe6830a1f1e4f1ab671be0890e4aafec\", \"key\": \"I Wasn't Christian Enough\", \"title\": \"I Wasn't Christian Enough\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.\", \"date\": 15903648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c6bec2809c0f61485d57b4253b4bb055\", \"url\": \"https://api.pillowcase.su/api/download/c6bec2809c0f61485d57b4253b4bb055\", \"size\": \"3.32 MB\", \"duration\": 66.56}", "aliases": [], "size": "3.32 MB"}, {"id": "ig", "name": "IG", "artists": [], "producers": [], "notes": "OG Filename: (8496) IG - 10 RUN 1 132bpm\nYandhi-era freestyle. Vocals are from an iPhone voice memo. Most likely produced by Timbaland, but that remains unconfirmed.", "length": "152.73", "fileDate": 17019072, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c7e1d7ec681dbc31ed1ea61ecd60cf95", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c7e1d7ec681dbc31ed1ea61ecd60cf95\", \"key\": \"IG\", \"title\": \"IG\", \"description\": \"OG Filename: (8496) IG - 10 RUN 1 132bpm\\nYandhi-era freestyle. Vocals are from an iPhone voice memo. Most likely produced by Timbaland, but that remains unconfirmed.\", \"date\": 17019072, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a33b51dacb3a19bf6d49f4f8d3607970\", \"url\": \"https://api.pillowcase.su/api/download/a33b51dacb3a19bf6d49f4f8d3607970\", \"size\": \"4.7 MB\", \"duration\": 152.73}", "aliases": [], "size": "4.7 MB"}, {"id": "isotoner", "name": "Isotoner", "artists": [], "producers": [], "notes": "OG Filename: REF_ISOTONER\nYandhi-era CyHi reference track, most likely made in late December when his other references were. Unknown if <PERSON> recorded on this instrumental.", "length": "154.32", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a5efc98d5c926af0956cb4680d7202fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a5efc98d5c926af0956cb4680d7202fb\", \"key\": \"Isotoner\", \"title\": \"Isotoner\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: REF_ISOTONER\\nYandhi-era CyHi reference track, most likely made in late December when his other references were. Unknown if <PERSON> recorded on this instrumental.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a29d9f68fc6558814809a5c50a9ddbc7\", \"url\": \"https://api.pillowcase.su/api/download/a29d9f68fc6558814809a5c50a9ddbc7\", \"size\": \"4.73 MB\", \"duration\": 154.32}", "aliases": [], "size": "4.73 MB"}, {"id": "jam-1", "name": "Jam 1", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 1 100.4bpm\n1st jam session freestyle over a Timbaland instrumental. Shares lyrics with \"<PERSON><PERSON><PERSON>\" and \"Hold Me Down\".", "length": "64.24", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0f05aa212856a765687649f8f45ce716", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f05aa212856a765687649f8f45ce716\", \"key\": \"Jam 1\", \"title\": \"Jam 1\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 1 100.4bpm\\n1st jam session freestyle over a Timbaland instrumental. Shares lyrics with \\\"Nyce Vieux\\\" and \\\"Hold Me Down\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c073e62360dfab8efc0e514ff8bd29e\", \"url\": \"https://api.pillowcase.su/api/download/5c073e62360dfab8efc0e514ff8bd29e\", \"size\": \"3.28 MB\", \"duration\": 64.24}", "aliases": [], "size": "3.28 MB"}, {"id": "jam-3", "name": "Jam 3", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 3 82.73bpm\n3rd jam session freestyle over a Timbaland instrumental. Original snippet leaked February 2nd, 2023.", "length": "237.88", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e0753039e93a41bc4464c6f379988faf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e0753039e93a41bc4464c6f379988faf\", \"key\": \"Jam 3\", \"title\": \"Jam 3\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 3 82.73bpm\\n3rd jam session freestyle over a Timbaland instrumental. Original snippet leaked February 2nd, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5d5f56f7d0b2e376d0b66cd920c319fd\", \"url\": \"https://api.pillowcase.su/api/download/5d5f56f7d0b2e376d0b66cd920c319fd\", \"size\": \"6.06 MB\", \"duration\": 237.88}", "aliases": [], "size": "6.06 MB"}, {"id": "jam-5", "name": "Jam 5", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 5\n5th jam session freestyle over a Timbaland instrumental. <PERSON> repeating the hook from \"Cash To Burn\", as that song originated from the previous jam.", "length": "79.57", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e69e14a3c4f1a438a238288f19091304", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e69e14a3c4f1a438a238288f19091304\", \"key\": \"Jam 5\", \"title\": \"Jam 5\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 5\\n5th jam session freestyle over a Timbaland instrumental. <PERSON> repeating the hook from \\\"Cash To Burn\\\", as that song originated from the previous jam.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"904d23bd29ae7da6f567d8fdc1884372\", \"url\": \"https://api.pillowcase.su/api/download/904d23bd29ae7da6f567d8fdc1884372\", \"size\": \"3.53 MB\", \"duration\": 79.57}", "aliases": [], "size": "3.53 MB"}, {"id": "jam-7", "name": "Jam 7 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 7\n7th jam session freestyle over a Timbaland instrumental. Worked on further in late December 2018 or early January 2019.", "length": "269.01", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/b0c58c1a87254465be2c14c4a24e6b2a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b0c58c1a87254465be2c14c4a24e6b2a\", \"key\": \"Jam 7\", \"title\": \"Jam 7 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 7\\n7th jam session freestyle over a Timbaland instrumental. Worked on further in late December 2018 or early January 2019.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"75756e1389cdb8cde9767cafa3425601\", \"url\": \"https://api.pillowcase.su/api/download/75756e1389cdb8cde9767cafa3425601\", \"size\": \"6.56 MB\", \"duration\": 269.01}", "aliases": [], "size": "6.56 MB"}, {"id": "jam-7-72", "name": "Jam 7 [V2]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 7 V1\nCutdown and mixed version of \"Jam 7\".", "length": "226.87", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d01d6f217b85c8fcb98f4381b1dbbe67", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d01d6f217b85c8fcb98f4381b1dbbe67\", \"key\": \"Jam 7\", \"title\": \"Jam 7 [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 7 V1\\nCutdown and mixed version of \\\"Jam 7\\\".\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"005e8f1a9310800916f5cf8b9b320ec9\", \"url\": \"https://api.pillowcase.su/api/download/005e8f1a9310800916f5cf8b9b320ec9\", \"size\": \"5.89 MB\", \"duration\": 226.87}", "aliases": [], "size": "5.89 MB"}, {"id": "jam-7-73", "name": "Jam 7 [V3]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: jams 7 tee grizzley verse v1\nTee Grizzley \"Jam 7\" reference track. Likely made in the same sessions as other Tee Grizzley reference tracks.", "length": "268.09", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/605abe9d3dd9c77c86945868294d1474", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/605abe9d3dd9c77c86945868294d1474\", \"key\": \"Jam 7\", \"title\": \"Jam 7 [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: jams 7 tee grizzley verse v1\\nTee Grizzley \\\"Jam 7\\\" reference track. Likely made in the same sessions as other Tee Grizzley reference tracks.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b93f0b727756b389b7a5b475c625d1e7\", \"url\": \"https://api.pillowcase.su/api/download/b93f0b727756b389b7a5b475c625d1e7\", \"size\": \"6.55 MB\", \"duration\": 268.09}", "aliases": [], "size": "6.55 MB"}, {"id": "jam-8", "name": "Jam 8", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 8\n8th jam session freestyle over a Timbaland instrumental. Leaked on October 14th, 2023, with the lossless leaking later on.", "length": "136.02", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a4bbed4b3ab5e283ca8210e5d4b3c4e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a4bbed4b3ab5e283ca8210e5d4b3c4e1\", \"key\": \"Jam 8\", \"title\": \"Jam 8\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 8\\n8th jam session freestyle over a Timbaland instrumental. Leaked on October 14th, 2023, with the lossless leaking later on.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7b5487e9d4722f1c972ccf8904b2e597\", \"url\": \"https://api.pillowcase.su/api/download/7b5487e9d4722f1c972ccf8904b2e597\", \"size\": \"4.43 MB\", \"duration\": 136.02}", "aliases": [], "size": "4.43 MB"}, {"id": "jam-9", "name": "Jam 9", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 9\n9th jam session freestyle over a Timbaland instrumental.", "length": "76.67", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a78b6158961a249b6071d411b25724ef", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a78b6158961a249b6071d411b25724ef\", \"key\": \"Jam 9\", \"title\": \"Jam 9\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 9\\n9th jam session freestyle over a Timbaland instrumental.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ee67e3d0bbae27d0233662986c7f8cc6\", \"url\": \"https://api.pillowcase.su/api/download/ee67e3d0bbae27d0233662986c7f8cc6\", \"size\": \"3.48 MB\", \"duration\": 76.67}", "aliases": [], "size": "3.48 MB"}, {"id": "jam-10-76", "name": "Jam 10", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 10\n10th jam session freestyle over a Timbaland instrumental. Leaked on October 14th, 2023, with the lossless version leaking later.", "length": "192.94", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/be2273b6107a637dd4631ead4bd3c686", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/be2273b6107a637dd4631ead4bd3c686\", \"key\": \"Jam 10\", \"title\": \"Jam 10\", \"artists\": \"(prod. <PERSON>bal<PERSON>)\", \"description\": \"OG Filename: Jam 10\\n10th jam session freestyle over a Timbaland instrumental. Leaked on October 14th, 2023, with the lossless version leaking later.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d1b9d5db075a14cde6ef53f16118f2a3\", \"url\": \"https://api.pillowcase.su/api/download/d1b9d5db075a14cde6ef53f16118f2a3\", \"size\": \"5.34 MB\", \"duration\": 192.94}", "aliases": [], "size": "5.34 MB"}, {"id": "jam-11", "name": "Jam 11 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 11\n11th jam session freestyle over a Timbaland instrumental. Leaked on October 14th, 2023, with the lossless leaking later on.", "length": "86.19", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/86feedc8bded0b9fc493c5cd1f0dc9c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86feedc8bded0b9fc493c5cd1f0dc9c3\", \"key\": \"Jam 11\", \"title\": \"Jam 11 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 11\\n11th jam session freestyle over a Timbaland instrumental. Leaked on October 14th, 2023, with the lossless leaking later on.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e7545fb317b57a39763cf4c26153084a\", \"url\": \"https://api.pillowcase.su/api/download/e7545fb317b57a39763cf4c26153084a\", \"size\": \"3.64 MB\", \"duration\": 86.19}", "aliases": [], "size": "3.64 MB"}, {"id": "jam-11-78", "name": "Jam 11 [V2]", "artists": [], "producers": ["E.VAX", "Timbaland"], "notes": "OG Filename: jam 11 84bpm (evan version)\nE.VAX produced version of \"Jam 11\".", "length": "91.49", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/003face1a255fcbb3012ae1cb088d794", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/003face1a255fcbb3012ae1cb088d794\", \"key\": \"Jam 11\", \"title\": \"Jam 11 [V2]\", \"artists\": \"(prod. E.VAX & Timbaland)\", \"description\": \"OG Filename: jam 11 84bpm (evan version)\\nE.VAX produced version of \\\"Jam 11\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b83c9e3c116234dc4fc6394f74005b75\", \"url\": \"https://api.pillowcase.su/api/download/b83c9e3c116234dc4fc6394f74005b75\", \"size\": \"3.72 MB\", \"duration\": 91.49}", "aliases": [], "size": "3.72 MB"}, {"id": "jam-13", "name": "Jam 13 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 13\n13th jam session freestyle over a Timbaland instrumental. Original snippet leaked November 1st, 2022.", "length": "115.54", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cd45bce8c25a13dce047efe9fab3d890", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cd45bce8c25a13dce047efe9fab3d890\", \"key\": \"Jam 13\", \"title\": \"Jam 13 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 13\\n13th jam session freestyle over a Timbaland instrumental. Original snippet leaked November 1st, 2022.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f508d0d55ee6f32653d8ddf512907596\", \"url\": \"https://api.pillowcase.su/api/download/f508d0d55ee6f32653d8ddf512907596\", \"size\": \"4.11 MB\", \"duration\": 115.54}", "aliases": [], "size": "4.11 MB"}, {"id": "jam-13-80", "name": "Jam 13 [V2]", "artists": [], "producers": ["E.VAX", "Timbaland"], "notes": "OG Filename: jam 13 (evan version)\nE.VAX produced version of \"Jam 13\".", "length": "126.96", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c365a5d9ae13d8d350e41078832234a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c365a5d9ae13d8d350e41078832234a5\", \"key\": \"Jam 13\", \"title\": \"Jam 13 [V2]\", \"artists\": \"(prod. E.VAX & Timbaland)\", \"aliases\": [\"Bonjourno\"], \"description\": \"OG Filename: jam 13 (evan version)\\nE.VAX produced version of \\\"Jam 13\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"278dd70487ff9857fe7fca29977b7d0e\", \"url\": \"https://api.pillowcase.su/api/download/278dd70487ff9857fe7fca29977b7d0e\", \"size\": \"4.29 MB\", \"duration\": 126.96}", "aliases": ["Bonjourno"], "size": "4.29 MB"}, {"id": "jam-15", "name": "Jam 15 ", "artists": ["Chief <PERSON><PERSON>"], "producers": ["Mustard", "Timbaland"], "notes": "OG Filename: KW - Jam 15 102bpm - BOUNCE\nFrom the Kanye, Timbaland, <PERSON><PERSON><PERSON>, Chief <PERSON><PERSON> and Ballout session. Originally was being sold by propkers22, but was soon leaked after his ban.", "length": "170.88", "fileDate": 15869088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8339c2015f10cd21b02e7f85f6670608", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8339c2015f10cd21b02e7f85f6670608\", \"key\": \"Jam 15\", \"title\": \"Jam 15 \", \"artists\": \"(feat. Chief <PERSON><PERSON>) (prod. Mustard & Timbaland)\", \"description\": \"OG Filename: KW - Jam 15 102bpm - BOUNCE\\nFrom the Kanye, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Chief <PERSON><PERSON> and Ballout session. Originally was being sold by prop<PERSON><PERSON>, but was soon leaked after his ban.\", \"date\": 15869088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3efab0c9fb22e556889f8849f5e878c4\", \"url\": \"https://api.pillowcase.su/api/download/3efab0c9fb22e556889f8849f5e878c4\", \"size\": \"4.99 MB\", \"duration\": 170.88}", "aliases": [], "size": "4.99 MB"}, {"id": "jam-16", "name": "Jam 16", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> 16 - <PERSON>bo 8\nFreestyle over Timbaland Production. Original snippet leaked November 2nd, 2021.", "length": "70.73", "fileDate": 17381952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/619ff482a0d2e75948eff84ba18554bd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/619ff482a0d2e75948eff84ba18554bd\", \"key\": \"Jam 16\", \"title\": \"Jam 16\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Freestyle 8\"], \"description\": \"OG Filename: KW - Jam 16 - Timbo 8\\nFreestyle over Timbaland Production. Original snippet leaked November 2nd, 2021.\", \"date\": 17381952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7c39fb27de46d81d97dea178ee3acdf7\", \"url\": \"https://api.pillowcase.su/api/download/7c39fb27de46d81d97dea178ee3acdf7\", \"size\": \"3.39 MB\", \"duration\": 70.73}", "aliases": ["Freestyle 8"], "size": "3.39 MB"}, {"id": "jam-17", "name": "Jam 17", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: KW - Jam 17 87bpm - BOUNCE\nRough Timbaland-produced freestyle.", "length": "86.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a4e37fdc39e0ba30535fd65cda974f6a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a4e37fdc39e0ba30535fd65cda974f6a\", \"key\": \"Jam 17\", \"title\": \"Jam 17\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Jam 17 87bpm - BOUNCE\\nRough Timbaland-produced freestyle.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7430dffec093d8c5f30db2218799de78\", \"url\": \"https://api.pillowcase.su/api/download/7430dffec093d8c5f30db2218799de78\", \"size\": \"3.63 MB\", \"duration\": 86.06}", "aliases": [], "size": "3.63 MB"}, {"id": "jam-18", "name": "Jam 18", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: KW - Jam 18 118bpm (maybe) - BOUNCE\nRough Timbaland-produced demo from a jam session. Was originally being sold as \"Freestyle 7\". An original snippet leaked February 2nd, 2023.", "length": "71.4", "fileDate": 16752960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/1b5f52edd5f128a697007d4470bc844b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1b5f52edd5f128a697007d4470bc844b\", \"key\": \"Jam 18\", \"title\": \"Jam 18\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Freestyle 7\"], \"description\": \"OG Filename: KW - Jam 18 118bpm (maybe) - BOUNCE\\nRough Timbaland-produced demo from a jam session. Was originally being sold as \\\"Freestyle 7\\\". An original snippet leaked February 2nd, 2023.\", \"date\": 16752960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1c91aebd1bf47b9187c2889ee9ecd137\", \"url\": \"https://api.pillowcase.su/api/download/1c91aebd1bf47b9187c2889ee9ecd137\", \"size\": \"3.4 MB\", \"duration\": 71.4}", "aliases": ["Freestyle 7"], "size": "3.4 MB"}, {"id": "jam-19", "name": "Jam 19", "artists": [], "producers": ["Timbaland"], "notes": "Freestyle over Timbaland production. Vocals start at 0:25.", "length": "71.76", "fileDate": 16286400, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/79469924a8f1aae1afa41c45e37bf6c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/79469924a8f1aae1afa41c45e37bf6c8\", \"key\": \"Jam 19\", \"title\": \"Jam 19\", \"artists\": \"(prod. <PERSON>bal<PERSON>)\", \"description\": \"Freestyle over Timbaland production. Vocals start at 0:25.\", \"date\": 16286400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9757dccf00c04499df8819b772a6c13c\", \"url\": \"https://api.pillowcase.su/api/download/9757dccf00c04499df8819b772a6c13c\", \"size\": \"3.4 MB\", \"duration\": 71.76}", "aliases": [], "size": "3.4 MB"}, {"id": "juicy-10", "name": "Juicy 10", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON>W - juicy 10 Hoot Timbo 98bpm - BOUNCE\nFreestyle over Timbaland production. Was thought to be a 2018 Good Ass Job song under the nickname of \"Chop 10\", however it is most likely just a late 2018 freestyle idea.", "length": "46.44", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/ece0489ed24f0417d2e7aabbe1e7c4a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ece0489ed24f0417d2e7aabbe1e7c4a1\", \"key\": \"Juicy 10\", \"title\": \"Juicy 10\", \"artists\": \"(prod. Timbal<PERSON>)\", \"description\": \"OG Filename: KW - juicy 10 Hoot Timbo 98bpm - BOUNCE\\nFreestyle over Timbaland production. Was thought to be a 2018 Good Ass Job song under the nickname of \\\"Chop 10\\\", however it is most likely just a late 2018 freestyle idea.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"103da5737c65ef297723b4d497011393\", \"url\": \"https://api.pillowcase.su/api/download/103da5737c65ef297723b4d497011393\", \"size\": \"3 MB\", \"duration\": 46.44}", "aliases": [], "size": "3 MB"}, {"id": "joyride-11", "name": "Joyride 11", "artists": [], "producers": [], "notes": "OG Filename: (9107) Joyride 11 116bpm\nRough iPhone voice memo freestyle.", "length": "132.41", "fileDate": 17019072, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a2d775edc5388acf5739f313983d135b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2d775edc5388acf5739f313983d135b\", \"key\": \"Joyride 11\", \"title\": \"Joyride 11\", \"description\": \"OG Filename: (9107) Joyride 11 116bpm\\nRough iPhone voice memo freestyle.\", \"date\": 17019072, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0d3b4b7d487459bb1b868b72b33354e9\", \"url\": \"https://api.pillowcase.su/api/download/0d3b4b7d487459bb1b868b72b33354e9\", \"size\": \"4.38 MB\", \"duration\": 132.41}", "aliases": [], "size": "4.38 MB"}, {"id": "lara-croff-6", "name": "<PERSON> 6 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON><PERSON> croff 6 BOUNCE 152 BPM\nYe and <PERSON><PERSON> freestyling on a Timbaland beat.", "length": "232.54", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e9b63df085a5159f797f6058c7846b26", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9b63df085a5159f797f6058c7846b26\", \"key\": \"<PERSON> 6\", \"title\": \"<PERSON> 6 [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: lara croff 6 BOUNCE 152 BPM\\nYe and <PERSON><PERSON> freestyling on a Timbaland beat.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"622fe946ce14ed8e4c56bd52c50c2d12\", \"url\": \"https://api.pillowcase.su/api/download/622fe946ce14ed8e4c56bd52c50c2d12\", \"size\": \"5.98 MB\", \"duration\": 232.54}", "aliases": [], "size": "5.98 MB"}, {"id": "lara-croff-6-89", "name": "<PERSON> 6 [V2]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: LARA CROFF 6 New Ant Vocals\n<PERSON><PERSON> ref track for \"Lara C<PERSON>ff\". Has lines indicating <PERSON><PERSON><PERSON> produced the song.", "length": "115.26", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/842e909d9ecc5665b22d29f3bbfe9cdd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/842e909d9ecc5665b22d29f3bbfe9cdd\", \"key\": \"<PERSON> 6\", \"title\": \"<PERSON> 6 [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: LARA CROFF 6 New Ant Vocals\\nAnt Clemons ref track for \\\"<PERSON>\\\". Has lines indicating <PERSON><PERSON><PERSON> produced the song.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e57f358b42e6fb80f03315d0efa78370\", \"url\": \"https://api.pillowcase.su/api/download/e57f358b42e6fb80f03315d0efa78370\", \"size\": \"4.1 MB\", \"duration\": 115.26}", "aliases": [], "size": "4.1 MB"}, {"id": "lara-croff-9", "name": "<PERSON> 9", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> c<PERSON> 9 BOUNCE 135BPM\n<PERSON>t Clemons freestyle over a completely different beat also bounced under the title \"<PERSON>\".", "length": "136.19", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/35283116454d2f2c8b415235558a403e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/35283116454d2f2c8b415235558a403e\", \"key\": \"<PERSON> 9\", \"title\": \"<PERSON> 9\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: lara croff 9 BOUNCE 135BPM\\nAnt Clem<PERSON> freestyle over a completely different beat also bounced under the title \\\"<PERSON>ff\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8a2f45d686caace957488c6df83d97c6\", \"url\": \"https://api.pillowcase.su/api/download/8a2f45d686caace957488c6df83d97c6\", \"size\": \"4.44 MB\", \"duration\": 136.19}", "aliases": [], "size": "4.44 MB"}, {"id": "bad-taste", "name": "Bad Taste [V2]", "artists": [], "producers": ["DRTWRK"], "notes": "OG Filename: DRT YOUDONTKNOW -bad taste (DRTY you dont know) 138BPM BOUNCE\nMumble freestyle of \"Law Of Attraction\" that contains mostly mumble vocals. <PERSON> <PERSON> doing the hook of the song, and contains the first verse. Samples \"Costume Party\" by Two Door Cinema Club. Original snippet leaked October 4th, 2022.", "length": "314.09", "fileDate": 16648416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a05f0a2a4b687767743735fd80105969", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a05f0a2a4b687767743735fd80105969\", \"key\": \"Bad Taste\", \"title\": \"Bad Taste [V2]\", \"artists\": \"(prod. DRTWRK)\", \"aliases\": [\"Use This Gospel\", \"Law Of Attraction\", \"Breastplate Of Right\", \"Grab Your Armour For Protection\", \"Breastplate Of Righteousness\", \"You Don't Know\"], \"description\": \"OG Filename: DRT YOUDONTKNOW -bad taste (DRTY you dont know) 138BPM BOUNCE\\nMumble freestyle of \\\"Law Of Attraction\\\" that contains mostly mumble vocals. <PERSON> <PERSON> doing the hook of the song, and contains the first verse. <PERSON><PERSON> \\\"Costume Party\\\" by Two Door Cinema Club. Original snippet leaked October 4th, 2022.\", \"date\": 16648416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c0e649e126a3e442c358da5dc485db08\", \"url\": \"https://api.pillowcase.su/api/download/c0e649e126a3e442c358da5dc485db08\", \"size\": \"7.28 MB\", \"duration\": 314.09}", "aliases": ["Use This Gospel", "Law Of Attraction", "Breastplate Of Right", "Grab Your Armour For Protection", "Breastplate Of Righteousness", "You Don't Know"], "size": "7.28 MB"}, {"id": "law-of-attraction", "name": "Law Of Attraction [V3] ", "artists": [], "producers": ["DRTWRK", "Team Timbo", "BoogzDaBeast", "<PERSON><PERSON>"], "notes": "OG Filename: Law Of Attraction 1st meloINS 149\nBo<PERSON>ce of \"Law Of Attraction\" with a solo <PERSON><PERSON><PERSON> hook and open verses. Features elements of later versions.", "length": "304.92", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/b372800921c899c76ac8a9df15b531b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b372800921c899c76ac8a9df15b531b7\", \"key\": \"Law Of Attraction\", \"title\": \"Law Of Attraction [V3] \", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, BoogzDaBeast & Ant <PERSON>lemons)\", \"aliases\": [\"Bad Taste\", \"Use This Gospel\", \"Breastplate Of Right\", \"Grab Your Armour For Protection\", \"Breastplate Of Righteousness\", \"You Don't Know\"], \"description\": \"OG Filename: Law Of Attraction 1st meloINS 149\\nBounce of \\\"Law Of Attraction\\\" with a solo <PERSON><PERSON><PERSON> hook and open verses. Features elements of later versions.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"224b85ea2fdd23b8f73e309e085144ef\", \"url\": \"https://api.pillowcase.su/api/download/224b85ea2fdd23b8f73e309e085144ef\", \"size\": \"7.14 MB\", \"duration\": 304.92}", "aliases": ["Bad Taste", "Use This Gospel", "Breastplate Of Right", "Grab Your Armour For Protection", "Breastplate Of Righteousness", "You Don't Know"], "size": "7.14 MB"}, {"id": "law-of-attraction-93", "name": "⭐ Law Of Attraction [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["DRTWRK", "Team Timbo", "BoogzDaBeast", "<PERSON><PERSON>"], "notes": "Was incorrectly sold under the name \"<PERSON><PERSON><PERSON>\". Reworked and released as \"Use This Gospel\" on JESUS IS KING. Has high energy freestyle vocals from <PERSON> alongside the chorus now being covered by <PERSON><PERSON>. Some of the freestyle can also be seen in 500 Days In UCLA.", "length": "201.09", "fileDate": 15633216, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8e1552a02727868ebdaa64e11b4e8ce3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8e1552a02727868ebdaa64e11b4e8ce3\", \"key\": \"Law Of Attraction\", \"title\": \"\\u2b50 Law Of Attraction [V4]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, Team <PERSON>bo, BoogzDaBeast & Ant <PERSON>)\", \"aliases\": [\"Bad Taste\", \"Use This Gospel\", \"Breastplate Of Right\", \"Grab Your Armour For Protection\", \"Breastplate Of Righteousness\", \"You Don't Know\"], \"description\": \"Was incorrectly sold under the name \\\"Chakras\\\". Reworked and released as \\\"Use This Gospel\\\" on JESUS IS KING. Has high energy freestyle vocals from <PERSON> alongside the chorus now being covered by <PERSON><PERSON>. Some of the freestyle can also be seen in 500 Days In UCLA.\", \"date\": 15633216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7bf979dceb90368e93568573c1f3faed\", \"url\": \"https://api.pillowcase.su/api/download/7bf979dceb90368e93568573c1f3faed\", \"size\": \"5.47 MB\", \"duration\": 201.09}", "aliases": ["Bad Taste", "Use This Gospel", "Breastplate Of Right", "Grab Your Armour For Protection", "Breastplate Of Righteousness", "You Don't Know"], "size": "5.47 MB"}, {"id": "law-of-attraction-94", "name": "Law Of Attraction [V5] ", "artists": [], "producers": ["DRTWRK", "Team Timbo", "BoogzDaBeast", "<PERSON><PERSON>"], "notes": "OG Filename: Law of Attraction_Cy De<PERSON>\nEarlier of the two CyHi \"Law Of Attraction\" references, from late December 2018. Features the <PERSON> harmonizing vocals.", "length": "159.91", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a7343b736f7f3be276b2930976fed0a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7343b736f7f3be276b2930976fed0a1\", \"key\": \"Law Of Attraction\", \"title\": \"Law Of Attraction [V5] \", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>bo, BoogzDaBeast & Ant <PERSON>lemons)\", \"aliases\": [\"Bad Taste\", \"Use This Gospel\", \"Breastplate Of Right\", \"Grab Your Armour For Protection\", \"Breastplate Of Righteousness\", \"You Don't Know\"], \"description\": \"OG Filename: Law of Attraction_Cy Demo\\nEarlier of the two CyHi \\\"Law Of Attraction\\\" references, from late December 2018. Features the <PERSON> harmonizing vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9d0626d2152cb48312f0645ac96bd937\", \"url\": \"https://api.pillowcase.su/api/download/9d0626d2152cb48312f0645ac96bd937\", \"size\": \"4.82 MB\", \"duration\": 159.91}", "aliases": ["Bad Taste", "Use This Gospel", "Breastplate Of Right", "Grab Your Armour For Protection", "Breastplate Of Righteousness", "You Don't Know"], "size": "4.82 MB"}, {"id": "law-of-attraction-95", "name": "Law Of Attraction [V6] ", "artists": [], "producers": ["DRTWRK", "Team Timbo", "BoogzDaBeast", "<PERSON><PERSON>"], "notes": "OG Filename: Law Of Attraction-(BOOGZ EDIT) (TeamTimbo_Boogz) 148bpm 01-29-19\nFeatures a different arrangement with a new outro, along with <PERSON><PERSON><PERSON> back on the hook. Verses are open.", "length": "227.9", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/f28b4aadd6e45be41056ea8178a0190f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f28b4aadd6e45be41056ea8178a0190f\", \"key\": \"Law Of Attraction\", \"title\": \"Law Of Attraction [V6] \", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, BoogzDaBeast & Ant Clemons)\", \"aliases\": [\"Bad Taste\", \"Use This Gospel\", \"Breastplate Of Right\", \"Grab Your Armour For Protection\", \"Breastplate Of Righteousness\", \"You Don't Know\"], \"description\": \"OG Filename: Law Of Attraction-(BOOGZ EDIT) (TeamTimbo_Boogz) 148bpm 01-29-19\\nFeatures a different arrangement with a new outro, along with <PERSON><PERSON><PERSON> back on the hook. Verses are open.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ab1fc20c5720039761ac7924729af4bd\", \"url\": \"https://api.pillowcase.su/api/download/ab1fc20c5720039761ac7924729af4bd\", \"size\": \"5.9 MB\", \"duration\": 227.9}", "aliases": ["Bad Taste", "Use This Gospel", "Breastplate Of Right", "Grab Your Armour For Protection", "Breastplate Of Righteousness", "You Don't Know"], "size": "5.9 MB"}, {"id": "lil-floyd-1", "name": "<PERSON> Floyd 1 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: KW - 1 64bpm (maybe) - BOUNCE\nMumble freestyle over Timbaland production.", "length": "90.05", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/fb563c30c211570cd4fafb6714367717", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fb563c30c211570cd4fafb6714367717\", \"key\": \"Lil Floyd 1\", \"title\": \"Lil Floyd 1 [V1]\", \"artists\": \"(prod. <PERSON>bal<PERSON>)\", \"aliases\": [\"Timbaland Freestyle 1\"], \"description\": \"OG Filename: KW - 1 64bpm (maybe) - BOUNCE\\nMumble freestyle over Timbaland production.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f8c740462262d4466209214564ffa59f\", \"url\": \"https://api.pillowcase.su/api/download/f8c740462262d4466209214564ffa59f\", \"size\": \"3.7 MB\", \"duration\": 90.05}", "aliases": ["Timbaland Freestyle 1"], "size": "3.7 MB"}, {"id": "lil-floyd-1-97", "name": "✨ <PERSON> Floyd 1 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "OG Filename: lil floyd 1 redo 127.60 #2 ( 128.010 BPM\nFreestyle over Timbaland production. <PERSON><PERSON><PERSON> vocals: 0:28s to 1:30s. <PERSON>ly vocals: 1:33s to 2:17s Put up for sale by <PERSON> on TheSource. A new snippet leaked July 2022, with a low quality snippet recorded from a VC leaked in October 2022.", "length": "142.52", "fileDate": 16958592, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/86da2c3ac956c0bebd7a9a387e1ef44e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86da2c3ac956c0bebd7a9a387e1ef44e\", \"key\": \"Lil Floyd 1\", \"title\": \"\\u2728 <PERSON> Floyd 1 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Timbal<PERSON>)\", \"aliases\": [\"Timbaland Freestyle 1\"], \"description\": \"OG Filename: lil floyd 1 redo 127.60 #2 ( 128.010 BPM\\nFreestyle over Timbaland production. Kanye vocals: 0:28s to 1:30s. <PERSON><PERSON> vocals: 1:33s to 2:17s Put up for sale by <PERSON> on TheSource. A new snippet leaked July 2022, with a low quality snippet recorded from a VC leaked in October 2022.\", \"date\": 16958592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b08bd75c45a7a8393004642f55993745\", \"url\": \"https://api.pillowcase.su/api/download/b08bd75c45a7a8393004642f55993745\", \"size\": \"4.54 MB\", \"duration\": 142.52}", "aliases": ["Timbaland Freestyle 1"], "size": "4.54 MB"}, {"id": "look-what-nerds-do", "name": "Look What Nerds Do", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.", "length": "149.32", "fileDate": 15903648, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4a2cfb0739aa71a7743709ec091f85a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a2cfb0739aa71a7743709ec091f85a6\", \"key\": \"Look What Nerds Do\", \"title\": \"Look What Nerds Do\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.\", \"date\": 15903648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3277910203ed98498b6fe2f44180b497\", \"url\": \"https://api.pillowcase.su/api/download/3277910203ed98498b6fe2f44180b497\", \"size\": \"4.65 MB\", \"duration\": 149.32}", "aliases": [], "size": "4.65 MB"}, {"id": "murda", "name": "<PERSON><PERSON>", "artists": [], "producers": [], "notes": "Yandhi era freestyle. Has similar vocal quality to \"Oh Yeah\", so it is possibly from the same session.", "length": "6.86", "fileDate": 16797888, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/b30bff98c226e1417fb76c9a51eccdff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b30bff98c226e1417fb76c9a51eccdff\", \"key\": \"Murda\", \"title\": \"Murda\", \"description\": \"Yandhi era freestyle. Has similar vocal quality to \\\"Oh Yeah\\\", so it is possibly from the same session.\", \"date\": 16797888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6281d776f31e9d18e2aadb59e458a62f\", \"url\": \"https://api.pillowcase.su/api/download/6281d776f31e9d18e2aadb59e458a62f\", \"size\": \"2.37 MB\", \"duration\": 6.86}", "aliases": [], "size": "2.37 MB"}, {"id": "new-body", "name": "New Body [V21]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["BoogzDaBeast", "RONNY J"], "notes": "OG Filename: _New Body - BIZ Intro - (_boogz revisited) 11-24-18 (with YE VERSE)\nHas added record scratches to the intro as well as different drums. Has the same drums as the JIK version and the Consequence reference. Used on a December tracklist.", "length": "252.71", "fileDate": 15698880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/89b91e894b3cc5e772763bc6fb1f8424", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/89b91e894b3cc5e772763bc6fb1f8424\", \"key\": \"New Body\", \"title\": \"New Body [V21]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> $ign) (prod. BoogzDaBeast & RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: _New Body - BIZ Intro - (_boogz revisited) 11-24-18 (with YE VERSE)\\nHas added record scratches to the intro as well as different drums. Has the same drums as the JIK version and the Consequence reference. Used on a December tracklist.\", \"date\": 15698880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"837ae6b372eefb7174a4b6821ab46a24\", \"url\": \"https://api.pillowcase.su/api/download/837ae6b372eefb7174a4b6821ab46a24\", \"size\": \"6.3 MB\", \"duration\": 252.71}", "aliases": ["Can't Wait To See Your New Body"], "size": "6.3 MB"}, {"id": "nothing-s-gonna-stop-me", "name": "Nothing's Gonna Stop Me", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.", "length": "65.75", "fileDate": 15903648, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/fecea6c0624d90aac3daf7faa58c6d29", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fecea6c0624d90aac3daf7faa58c6d29\", \"key\": \"Nothing's Gonna Stop Me\", \"title\": \"Nothing's Gonna Stop Me\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.\", \"date\": 15903648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f97de458629459d0b9efe7f0fa6d7412\", \"url\": \"https://api.pillowcase.su/api/download/f97de458629459d0b9efe7f0fa6d7412\", \"size\": \"3.31 MB\", \"duration\": 65.75}", "aliases": [], "size": "3.31 MB"}, {"id": "oh-merciful-god", "name": "Oh Merciful God", "artists": [], "producers": [], "notes": "Yandhi era freestyle. Has similar vocal quality to \"Oh Yeah\", so it is possibly from the same session.", "length": "6.86", "fileDate": 16797888, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4ae76abc05cbfe054671de5551e9ff6a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4ae76abc05cbfe054671de5551e9ff6a\", \"key\": \"Oh Merciful God\", \"title\": \"Oh Merciful God\", \"description\": \"Yandhi era freestyle. Has similar vocal quality to \\\"Oh Yeah\\\", so it is possibly from the same session.\", \"date\": 16797888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"dd0d59011bb556430f7d57ea97e22015\", \"url\": \"https://api.pillowcase.su/api/download/dd0d59011bb556430f7d57ea97e22015\", \"size\": \"2.37 MB\", \"duration\": 6.86}", "aliases": [], "size": "2.37 MB"}, {"id": "oh-yeah", "name": "Oh Yeah [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> OH YEAH\nRough track with extremely unfinished vocals. Leaked in mid-October 2019.", "length": "231.78", "fileDate": 15708384, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a326376e0962f6a66be1d379464f9f90", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a326376e0962f6a66be1d379464f9f90\", \"key\": \"Oh Yeah\", \"title\": \"Oh Yeah [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON> YEAH\\nRough track with extremely unfinished vocals. Leaked in mid-October 2019.\", \"date\": 15708384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"515d958185ef5da8624649de026f3e2f\", \"url\": \"https://api.pillowcase.su/api/download/515d958185ef5da8624649de026f3e2f\", \"size\": \"5.97 MB\", \"duration\": 231.78}", "aliases": [], "size": "5.97 MB"}, {"id": "oh-yeah-104", "name": "Oh Yeah [V2]", "artists": [], "producers": ["<PERSON>", "FaltyDL", "E.VAX"], "notes": "OG Filename: Trust FaltyDL & Evan\nVersion of \"Oh Yeah\" with different production.", "length": "232", "fileDate": 16889472, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/31df4b4b79c9da17f364950bd74c798a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/31df4b4b79c9da17f364950bd74c798a\", \"key\": \"Oh Yeah\", \"title\": \"Oh Yeah [V2]\", \"artists\": \"(prod. <PERSON>, FaltyDL & E.VAX)\", \"description\": \"OG Filename: Trust FaltyDL & Evan\\nVersion of \\\"Oh Yeah\\\" with different production.\", \"date\": 16889472, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f6ca65833da063cc52e93b30f17a0187\", \"url\": \"https://api.pillowcase.su/api/download/f6ca65833da063cc52e93b30f17a0187\", \"size\": \"5.97 MB\", \"duration\": 232}", "aliases": [], "size": "5.97 MB"}, {"id": "overdose", "name": "Overdose [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: CONSEQUENCE_Overdose OPEN VRS (HFC F&A Piano)\nReference track for \"Overdose\" done by Consequence. Has some <PERSON><PERSON><PERSON> Melly vocals, and an open verse. Leaked on October 14th, 2023.", "length": "199.52", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c18fc6c01dfc6c72b13024120e3b9cb2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c18fc6c01dfc6c72b13024120e3b9cb2\", \"key\": \"Overdose\", \"title\": \"Overdose [V1]\", \"artists\": \"(ref. Consequence) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: CONSEQUENCE_Overdose OPEN VRS (HFC F&A Piano)\\nReference track for \\\"Overdose\\\" done by Consequence. Has some <PERSON><PERSON><PERSON>ly vocals, and an open verse. Leaked on October 14th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"eb380dadf508eeb204bccc6dc6281952\", \"url\": \"https://api.pillowcase.su/api/download/eb380dadf508eeb204bccc6dc6281952\", \"size\": \"5.45 MB\", \"duration\": 199.52}", "aliases": [], "size": "5.45 MB"}, {"id": "ozark-10", "name": "Ozark 10 [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: Ozark 10 v1 137bpm\nShort 42 second freestyle, from December 2018 Yandhi sessions.", "length": "42.04", "fileDate": 15751584, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cc56fa06907c5169df2bdf32238d0390", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc56fa06907c5169df2bdf32238d0390\", \"key\": \"Ozark 10\", \"title\": \"Ozark 10 [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Ozarka\"], \"description\": \"OG Filename: Ozark 10 v1 137bpm\\nShort 42 second freestyle, from December 2018 Yandhi sessions.\", \"date\": 15751584, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6ade683a22e83c04deae61b1620fee2d\", \"url\": \"https://api.pillowcase.su/api/download/6ade683a22e83c04deae61b1620fee2d\", \"size\": \"2.93 MB\", \"duration\": 42.04}", "aliases": ["Ozarka"], "size": "2.93 MB"}, {"id": "ozark-10-107", "name": "Ozark 10 [V2]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: Ozark 10 v2 137bpm\nLonger version recorded December 8th, 2018, made after <PERSON> got off of the phone with who was facetiming him while he recorded the previous version. Leaked in early April 2020.", "length": "144.38", "fileDate": 15859584, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6d1a6467f40cca0bb287ba1889a6b37a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d1a6467f40cca0bb287ba1889a6b37a\", \"key\": \"Ozark 10\", \"title\": \"Ozark 10 [V2]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Ozarka\"], \"description\": \"OG Filename: Ozark 10 v2 137bpm\\nLonger version recorded December 8th, 2018, made after <PERSON> got off of the phone with who was facetiming him while he recorded the previous version. Leaked in early April 2020.\", \"date\": 15859584, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6c6e25edc1ea591262f07d1c4138309b\", \"url\": \"https://api.pillowcase.su/api/download/6c6e25edc1ea591262f07d1c4138309b\", \"size\": \"4.57 MB\", \"duration\": 144.38}", "aliases": ["Ozarka"], "size": "4.57 MB"}, {"id": "photo", "name": "Photo [V2]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON><PERSON>e 1-Photo 84BPM team timbo BOUNCE\nKany<PERSON> mumbling over a beat built around camera flashes. Sold on leakth.is by a user named <PERSON><PERSON><PERSON>. Instrumental and acapella later leaked as a bonus for the Fya Man \"Flowers\" reference <PERSON><PERSON><PERSON>.", "length": "148.61", "fileDate": 15949440, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3f1ed4863ba40e30555f09ce629c679d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f1ed4863ba40e30555f09ce629c679d\", \"key\": \"Photo\", \"title\": \"Photo [V2]\", \"artists\": \"(prod. <PERSON>baland)\", \"description\": \"OG Filename: Kanye 1-Photo 84BPM team timbo BOUNCE\\nKanye mumbling over a beat built around camera flashes. Sold on leakth.is by a user named <PERSON><PERSON><PERSON>. Instrumental and acapella later leaked as a bonus for the Fya Man \\\"Flowers\\\" reference JoeBuy.\", \"date\": 15949440, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7464686fe60726a0c845487b64af33de\", \"url\": \"https://api.pillowcase.su/api/download/7464686fe60726a0c845487b64af33de\", \"size\": \"4.63 MB\", \"duration\": 148.61}", "aliases": [], "size": "4.63 MB"}, {"id": "photo-109", "name": "Photo [V3]", "artists": [], "producers": ["Timbaland"], "notes": "Consequence reference track for \"Photo\". Played on Consequence's Instagram live February 17th, 2025.", "length": "111.49", "fileDate": 17397504, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/ddb7ffe47d642a601409b4ab273e5e94", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ddb7ffe47d642a601409b4ab273e5e94\", \"key\": \"Photo\", \"title\": \"Photo [V3]\", \"artists\": \"(ref. Consequence) (prod. <PERSON>bal<PERSON>)\", \"description\": \"Consequence reference track for \\\"Photo\\\". Played on Consequence's Instagram live February 17th, 2025.\", \"date\": 17397504, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"92e153ba2f74f36bac847bce8bec47d5\", \"url\": \"https://api.pillowcase.su/api/download/92e153ba2f74f36bac847bce8bec47d5\", \"size\": \"4.04 MB\", \"duration\": 111.49}", "aliases": [], "size": "4.04 MB"}, {"id": "photo-110", "name": "Photo [V4]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Photo_CY\n<PERSON><PERSON><PERSON> \"Photo\" reference track. Uses an alternate version of the beat that is a bit simpler from the one in the <PERSON><PERSON><PERSON> demo.", "length": "142.85", "fileDate": 15949440, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/edfdd32b38f17615066066e8508a0091", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edfdd32b38f17615066066e8508a0091\", \"key\": \"Photo\", \"title\": \"Photo [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Photo_CY\\nCyHi \\\"Photo\\\" reference track. Uses an alternate version of the beat that is a bit simpler from the one in the Kanye demo.\", \"date\": 15949440, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"870e9e954766fb2f622045d63d056f30\", \"url\": \"https://api.pillowcase.su/api/download/870e9e954766fb2f622045d63d056f30\", \"size\": \"4.54 MB\", \"duration\": 142.85}", "aliases": [], "size": "4.54 MB"}, {"id": "jam-2", "name": "Jam 2 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 2 73.34bpm\n2nd jam freestyle over a Timbaland instrumental, later reworked into \"Pillow Talk\". Leaked on October 14th, 2023.", "length": "59.92", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/40cc5ca9af9c8373b2d62c22112526d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/40cc5ca9af9c8373b2d62c22112526d2\", \"key\": \"Jam 2\", \"title\": \"Jam 2 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Pillow Talk\"], \"description\": \"OG Filename: Jam 2 73.34bpm\\n2nd jam freestyle over a Timbaland instrumental, later reworked into \\\"Pillow Talk\\\". Leaked on October 14th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7e58be0e01803e0aec2e53a1bae4b00e\", \"url\": \"https://api.pillowcase.su/api/download/7e58be0e01803e0aec2e53a1bae4b00e\", \"size\": \"3.21 MB\", \"duration\": 59.92}", "aliases": ["Pillow Talk"], "size": "3.21 MB"}, {"id": "pillow-talk", "name": "Pillow Talk [V2]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 2 (Pillow Talk) 73 bpm\nBetter mixed version of \"Jam 2\", retitled to be \"Pillow Talk\" and extended by more than a minute. Snippet leaked October 30th, 2022 before being leaked on October 14th, 2023, with the lossless leaking later.", "length": "131.51", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/13c76813a41dbb5d1f455a2852c68667", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/13c76813a41dbb5d1f455a2852c68667\", \"key\": \"Pillow Talk\", \"title\": \"Pillow Talk [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Jam 2 (Pillow Talk) 73 bpm\\nBetter mixed version of \\\"Jam 2\\\", retitled to be \\\"Pillow Talk\\\" and extended by more than a minute. Snippet leaked October 30th, 2022 before being leaked on October 14th, 2023, with the lossless leaking later.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0988152ae6de3d019fa81f5c991bb668\", \"url\": \"https://api.pillowcase.su/api/download/0988152ae6de3d019fa81f5c991bb668\", \"size\": \"4.36 MB\", \"duration\": 131.51}", "aliases": [], "size": "4.36 MB"}, {"id": "jam-12", "name": "Jam 12 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 12\n12th jam session freestyle over a Timbaland instrumental, later reworked into \"Got A Problem\". Leaked on October 14th, 2023, with the lossless leaking later.", "length": "200.36", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5d79e81960d84f3c2e4b73f724f149c7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d79e81960d84f3c2e4b73f724f149c7\", \"key\": \"Jam 12\", \"title\": \"Jam 12 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Got A Problem\"], \"description\": \"OG Filename: Jam 12\\n12th jam session freestyle over a Timbaland instrumental, later reworked into \\\"Got A Problem\\\". Leaked on October 14th, 2023, with the lossless leaking later.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"39c3c1aef3b96dfb39f6abcf13a06dea\", \"url\": \"https://api.pillowcase.su/api/download/39c3c1aef3b96dfb39f6abcf13a06dea\", \"size\": \"5.46 MB\", \"duration\": 200.36}", "aliases": ["Got A Problem"], "size": "5.46 MB"}, {"id": "got-a-problem", "name": "✨ Got A Problem [V2]", "artists": [], "producers": ["AllDay", "<PERSON>", "<PERSON>", "Timbaland"], "notes": "OG Filename: Got a problem (Jam 12) 82bpm V1.1 (<PERSON><PERSON><PERSON>) V1.1\nLater version of \"Jam 12\", now renamed. Features additional production from <PERSON>, <PERSON>, and AllDay. Leaked on October 30th, 2019.", "length": "184.46", "fileDate": 15723936, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/231c895c9710a75bfed009fbbccda65f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/231c895c9710a75bfed009fbbccda65f\", \"key\": \"Got A Problem\", \"title\": \"\\u2728 Got A Problem [V2]\", \"artists\": \"(prod. <PERSON>, <PERSON>, <PERSON> & Tim<PERSON>and)\", \"aliases\": [\"Jam 12\"], \"description\": \"OG Filename: Got a problem (Jam 12) 82bpm V1.1 (<PERSON><PERSON><PERSON><PERSON><PERSON>) V1.1\\nLater version of \\\"Jam 12\\\", now renamed. Features additional production from <PERSON>, <PERSON>, and AllDay. Leaked on October 30th, 2019.\", \"date\": 15723936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5920665435588283e0a84a7744d1ec3c\", \"url\": \"https://api.pillowcase.su/api/download/5920665435588283e0a84a7744d1ec3c\", \"size\": \"5.21 MB\", \"duration\": 184.46}", "aliases": ["Jam 12"], "size": "5.21 MB"}, {"id": "got-a-problem-115", "name": "Got A Problem [V3]", "artists": [], "producers": ["AllDay", "<PERSON>", "<PERSON>", "Timbaland"], "notes": "OG Filename: Got a problem (Jam 12) 82bpm V2\nLater version with added production. Put up for sale by unreleasedsounds.", "length": "22.31", "fileDate": 16612992, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/7729dbbaef477879cd6c10e1c0734870", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7729dbbaef477879cd6c10e1c0734870\", \"key\": \"Got A Problem\", \"title\": \"Got A Problem [V3]\", \"artists\": \"(prod. <PERSON><PERSON>, <PERSON>, <PERSON> & Tim<PERSON>and)\", \"aliases\": [\"Jam 12\"], \"description\": \"OG Filename: Got a problem (Jam 12) 82bpm V2\\nLater version with added production. Put up for sale by unreleasedsounds.\", \"date\": 16612992, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"534ef6d7e4fdaa48294033002b3e5dfd\", \"url\": \"https://api.pillowcase.su/api/download/534ef6d7e4fdaa48294033002b3e5dfd\", \"size\": \"2.61 MB\", \"duration\": 22.31}", "aliases": ["Jam 12"], "size": "2.61 MB"}, {"id": "got-a-problem-116", "name": "Got A Problem [V4]", "artists": [], "producers": ["AllDay", "<PERSON>", "E.VAX", "<PERSON>", "Timbaland"], "notes": "OG Filename: i got a problem (jam12, evan version) 82bpm\nVersion of \"Jam 12\" with production from E.VAX.", "length": "153.41", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/725f8c6bb7139be9e7ce39d6ed22e111", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/725f8c6bb7139be9e7ce39d6ed22e111\", \"key\": \"Got A Problem\", \"title\": \"Got A Problem [V4]\", \"artists\": \"(prod. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> & Tim<PERSON>)\", \"aliases\": [\"Jam 12\"], \"description\": \"OG Filename: i got a problem (jam12, evan version) 82bpm\\nVersion of \\\"Jam 12\\\" with production from E.VAX.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1b780f2814ae5d59600ac199b7daccae\", \"url\": \"https://api.pillowcase.su/api/download/1b780f2814ae5d59600ac199b7daccae\", \"size\": \"4.71 MB\", \"duration\": 153.41}", "aliases": ["Jam 12"], "size": "4.71 MB"}, {"id": "pull-up", "name": "Pull Up", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.", "length": "87.09", "fileDate": 15903648, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/ef3a9cb678b010a7245d73d9c09ff45b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef3a9cb678b010a7245d73d9c09ff45b\", \"key\": \"Pull Up\", \"title\": \"Pull Up\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.\", \"date\": 15903648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7a690c8ae9c15d51a637ec8546965902\", \"url\": \"https://api.pillowcase.su/api/download/7a690c8ae9c15d51a637ec8546965902\", \"size\": \"3.65 MB\", \"duration\": 87.09}", "aliases": [], "size": "3.65 MB"}, {"id": "redlo", "name": "<PERSON><PERSON>", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: redlo redo BOUNCE 91BPM &\nKanye West - Wasten Me REV 200333\nMumble demo collab with <PERSON><PERSON> leaked on November 18th, 2020. Was brought back in March 2020, but unknown if anything was done to the song other than rebouncing it.", "length": "127.95", "fileDate": 16056576, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/01701dfdfaaf45d395c0324169f8b846", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/01701dfdfaaf45d395c0324169f8b846\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON>\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"Wasten Me\"], \"description\": \"OG Filename: redlo redo BOUNCE 91BPM &\\nKanye West - Wasten Me REV 200333\\nMumble demo collab with <PERSON><PERSON> leaked on November 18th, 2020. Was brought back in March 2020, but unknown if anything was done to the song other than rebouncing it.\", \"date\": 16056576, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ba7cd0315d2fe480078828086292c467\", \"url\": \"https://api.pillowcase.su/api/download/ba7cd0315d2fe480078828086292c467\", \"size\": \"4.3 MB\", \"duration\": 127.95}", "aliases": ["<PERSON><PERSON>"], "size": "4.3 MB"}, {"id": "sky-city", "name": "⭐ Sky City [V25]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>", "070 Shake", "The-Dream", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["BoogzDaBeast", "Team Timbo", "Timbaland"], "notes": "OG Filename: YE - Sky City (BOOGZ NWA DRUMS) team timbo short version V1\nA later, unmastered, version with a rough mix. This version contains Timbaland drums, and a random cut in <PERSON>'s intro. Leaked in early October 2019, under the name \"We Can Fly\".", "length": "243.1", "fileDate": 15701472, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/fd133187fd6aec662d05dfe6aae98877", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fd133187fd6aec662d05dfe6aae98877\", \"key\": \"Sky City\", \"title\": \"\\u2b50 Sky City [V25]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON>, 07<PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>aBeast, Team Timbo & Timbaland)\", \"aliases\": [\"City In The Sky\", \"Loyalty\", \"Ooh Child\", \"Ooh Child Things Are Gonna Get Easier\"], \"description\": \"OG Filename: YE - Sky City (BOOGZ NWA DRUMS) team timbo short version V1\\nA later, unmastered, version with a rough mix. This version contains <PERSON><PERSON><PERSON> drums, and a random cut in <PERSON>'s intro. Leaked in early October 2019, under the name \\\"We Can Fly\\\".\", \"date\": 15701472, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8303a0c3ce1d94c367acaa6cc3df3713\", \"url\": \"https://api.pillowcase.su/api/download/8303a0c3ce1d94c367acaa6cc3df3713\", \"size\": \"6.15 MB\", \"duration\": 243.1}", "aliases": ["City In The Sky", "Loyalty", "Ooh <PERSON>", "Ooh Child Things Are Gonna Get Easier"], "size": "6.15 MB"}, {"id": "souleros", "name": "Souleros [V1]", "artists": [], "producers": [], "notes": "OG FIlename: Souleros- animated 152BPM BOUNCE\nMumble demo of \"Souleros\". Leaked by \"Not Alek\".", "length": "280.7", "fileDate": 16958592, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/50ba1ef98ee1884222f0177fe9d52a50", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/50ba1ef98ee1884222f0177fe9d52a50\", \"key\": \"Souleros\", \"title\": \"Souleros [V1]\", \"description\": \"OG FIlename: Souleros- animated 152BPM BOUNCE\\nMumble demo of \\\"Souleros\\\". Leaked by \\\"Not Alek\\\".\", \"date\": 16958592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f0928aaeeb61e44febccc3299252db09\", \"url\": \"https://api.pillowcase.su/api/download/f0928aaeeb61e44febccc3299252db09\", \"size\": \"6.75 MB\", \"duration\": 280.7}", "aliases": [], "size": "6.75 MB"}, {"id": "souleros-121", "name": "Souleros [V2]", "artists": [], "producers": [], "notes": "OG Filename: Me2 Souleros CyHi 122318\nA CyHi reference track for \"Souleros\". Has a hook, verse, and long open sections.", "length": "280.52", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/fd029d3115e2699088da7c42dc67c0ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fd029d3115e2699088da7c42dc67c0ce\", \"key\": \"Souleros\", \"title\": \"Souleros [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Me2\"], \"description\": \"OG Filename: Me2 Souleros CyHi 122318\\nA CyHi reference track for \\\"Souleros\\\". Has a hook, verse, and long open sections.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c5bba73e7e0ef83895833e77ee0fc918\", \"url\": \"https://api.pillowcase.su/api/download/c5bba73e7e0ef83895833e77ee0fc918\", \"size\": \"6.75 MB\", \"duration\": 280.52}", "aliases": ["Me2"], "size": "6.75 MB"}, {"id": "spread-your-wings", "name": "Spread Your Wings [V2]", "artists": [], "producers": ["AllDay", "Timbaland", "Team Timbo", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Spread your wings V2 (Video Vocal)\nCut-down of the freestyle. Snippet leaked October 4th, 2022. Full song leaked for free October 4th, 2022. \"Video Vocal\" in the filename refers to them using a video <PERSON><PERSON><PERSON> recorded for vocals in the intro.", "length": "393.29", "fileDate": 16648416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cc35c17f76b4437aeb21cbc6703ec5f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc35c17f76b4437aeb21cbc6703ec5f8\", \"key\": \"Spread Your Wings\", \"title\": \"Spread Your Wings [V2]\", \"artists\": \"(prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Bo<PERSON>zDaBeast & Andy C)\", \"aliases\": [\"Spread Your Wings (Get Back)\", \"Off The Meds\", \"Spread My Wings\"], \"description\": \"OG Filename: Spread your wings V2 (Video Vocal)\\nCut-down of the freestyle. Snippet leaked October 4th, 2022. Full song leaked for free October 4th, 2022. \\\"Video Vocal\\\" in the filename refers to them using a video <PERSON><PERSON><PERSON> recorded for vocals in the intro.\", \"date\": 16648416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e04af8d38d98c33b7c57dab45ab19c02\", \"url\": \"https://api.pillowcase.su/api/download/e04af8d38d98c33b7c57dab45ab19c02\", \"size\": \"8.55 MB\", \"duration\": 393.29}", "aliases": ["Spread Your Wings (Get Back)", "Off The Meds", "Spread My Wings"], "size": "8.55 MB"}, {"id": "spread-your-wings-123", "name": "Spread Your Wings [V3]", "artists": [], "producers": ["AllDay", "Timbaland", "Team Timbo", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Spread your wings V2 T Grizz 2\n<PERSON><PERSON> G<PERSON>zley \"Spread Your Wings\" reference track. Uses a somewhat different beat to V2. Leaked in full on February 2nd, 2023.", "length": "145.9", "fileDate": 16752960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5558918df323e875c048da53aabac855", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5558918df323e875c048da53aabac855\", \"key\": \"Spread Your Wings\", \"title\": \"Spread Your Wings [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Team Timbo, BoogzDaBeast & Andy C)\", \"aliases\": [\"Spread Your Wings (Get Back)\", \"Off The Meds\", \"Spread My Wings\"], \"description\": \"OG Filename: Spread your wings V2 T Grizz 2\\nTee Grizzley \\\"Spread Your Wings\\\" reference track. Uses a somewhat different beat to V2. Leaked in full on February 2nd, 2023.\", \"date\": 16752960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"14a75b30003845c662de524b345c75d3\", \"url\": \"https://api.pillowcase.su/api/download/14a75b30003845c662de524b345c75d3\", \"size\": \"4.59 MB\", \"duration\": 145.9}", "aliases": ["Spread Your Wings (Get Back)", "Off The Meds", "Spread My Wings"], "size": "4.59 MB"}, {"id": "spread-your-wings-124", "name": "Spread Your Wings [V5]", "artists": [], "producers": ["AllDay", "Timbaland", "Team Timbo", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Spread your wings V4 Ye Vocals 12.19.18\nLeaked as a bonus for the \"Skeletons\" groupbuy.", "length": "152.38", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/7084464a2b27dc983d320b7719223da2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7084464a2b27dc983d320b7719223da2\", \"key\": \"Spread Your Wings\", \"title\": \"Spread Your Wings [V5]\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, BoogzDaBeast & Andy C)\", \"aliases\": [\"Spread Your Wings (Get Back)\", \"Off The Meds\", \"Spread My Wings\"], \"description\": \"OG Filename: Spread your wings V4 Ye Vocals 12.19.18\\nLeaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"be8bc0721fdae3a09586060604f92806\", \"url\": \"https://api.pillowcase.su/api/download/be8bc0721fdae3a09586060604f92806\", \"size\": \"4.69 MB\", \"duration\": 152.38}", "aliases": ["Spread Your Wings (Get Back)", "Off The Meds", "Spread My Wings"], "size": "4.69 MB"}, {"id": "spread-your-wings-125", "name": "⭐ Spread Your Wings (Get Back) [V6]", "artists": [], "producers": ["AllDay", "Timbaland", "Team Timbo", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: SPREAD YOUR WINGS V5 (Get Back) (<PERSON>) 12.21.18\n<PERSON><PERSON> finished Yandhi-era song. Sold and leaked along with the stems, incorrectly under the name \"Bye Bye Baby.\" Possibly the same song as \"Spread My Wings\" from 2020, but unconfirmed.", "length": "152.31", "fileDate": 15633216, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c896f367116581146f59212c1f4356f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c896f367116581146f59212c1f4356f1\", \"key\": \"Spread Your Wings (Get Back)\", \"title\": \"\\u2b50 Spread Your Wings (Get Back) [V6]\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON><PERSON>, Team <PERSON>, BoogzDaBeast & Andy C)\", \"aliases\": [\"Spread Your Wings\", \"Off The Meds\", \"Spread My Wings\"], \"description\": \"OG Filename: SPREAD YOUR WINGS V5 (Get Back) (Ye Vocals) 12.21.18\\nFully finished Yandhi-era song. Sold and leaked along with the stems, incorrectly under the name \\\"Bye Bye Baby.\\\" Possibly the same song as \\\"Spread My Wings\\\" from 2020, but unconfirmed.\", \"date\": 15633216, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0df14ca384abdad569e6e29393a0f2e5\", \"url\": \"https://api.pillowcase.su/api/download/0df14ca384abdad569e6e29393a0f2e5\", \"size\": \"4.69 MB\", \"duration\": 152.31}", "aliases": ["Spread Your Wings", "Off The Meds", "Spread My Wings"], "size": "4.69 MB"}, {"id": "superheroes", "name": "Superheroes", "artists": [], "producers": [], "notes": "OG Filename: aa-superheroes 137BPM BOUNCE\nSong made in December 2018. Said to have \"some mumble\" and an \"acapella outro\" similar to \"Chakras\". The first snippet leaked on January 9th, 2023 from unreleasedsounds, with a longer low quality snippet coming on April 29th, 2023.", "length": "585.24", "fileDate": 16958592, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/91c8a65f5b60d251a248481d31fc88da", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91c8a65f5b60d251a248481d31fc88da\", \"key\": \"Superheroes\", \"title\": \"Superheroes\", \"description\": \"OG Filename: aa-superheroes 137BPM BOUNCE\\nSong made in December 2018. Said to have \\\"some mumble\\\" and an \\\"acapella outro\\\" similar to \\\"Chakras\\\". The first snippet leaked on January 9th, 2023 from unreleasedsounds, with a longer low quality snippet coming on April 29th, 2023.\", \"date\": 16958592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a979d9706772f69839ed2dcd18c2b123\", \"url\": \"https://api.pillowcase.su/api/download/a979d9706772f69839ed2dcd18c2b123\", \"size\": \"11.6 MB\", \"duration\": 585.24}", "aliases": [], "size": "11.6 MB"}, {"id": "the-storm", "name": "The Storm [V22]", "artists": ["XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": ["FnZ", "E.VAX", "RONNY J", "MIKE DEAN", "Team Timbo", "Timbaland"], "notes": "OG Filename: The Storm (Timbaland:TeamTimbo) V1 12.21.18\nLeaked as a bonus for the \"Skeletons\" groupbuy.", "length": "170.26", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/58c6fdf33d393ea22c4a7bd26f5a01bf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/58c6fdf33d393ea22c4a7bd26f5a01bf\", \"key\": \"The Storm\", \"title\": \"The Storm [V22]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gn, <PERSON><PERSON> & tizhimself) (prod. Fn<PERSON>, E.<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Team Timbo & Timbaland)\", \"aliases\": [\"Everything We Need\", \"Jeet\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: The Storm (Timbaland:TeamTimbo) V1 12.21.18\\nLeaked as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b9d616f0bf6cbb85f081f8fe9a8dae46\", \"url\": \"https://api.pillowcase.su/api/download/b9d616f0bf6cbb85f081f8fe9a8dae46\", \"size\": \"4.98 MB\", \"duration\": 170.26}", "aliases": ["Everything We Need", "<PERSON>et", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "4.98 MB"}, {"id": "xxx-the-storm", "name": "XXX / The Storm [V23]", "artists": ["XXXTENTACION", "Ty Dolla $ign", "<PERSON><PERSON>", "tizhimself"], "producers": [], "notes": "OG Filename: XXX - The Storm v1\nHas the same V1 beat but with extra effects, proper mixing and no Kid <PERSON> verse. Leaked on February 5th, 2023.", "length": "168.28", "fileDate": 16755552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/71acd5841ca872802ac374f42873822d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/71acd5841ca872802ac374f42873822d\", \"key\": \"XXX / The Storm\", \"title\": \"XXX / The Storm [V23]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ign, <PERSON><PERSON> & tizhim<PERSON>)\", \"aliases\": [\"Everything We Need\", \"We Begin\", \"Jeet\", \"We Begin After The Storm Inside\"], \"description\": \"OG Filename: XXX - The Storm v1\\nHas the same V1 beat but with extra effects, proper mixing and no Kid Cudi verse. Leaked on February 5th, 2023.\", \"date\": 16755552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7cda9986cfb1a309ac86e492ff031adc\", \"url\": \"https://api.pillowcase.su/api/download/7cda9986cfb1a309ac86e492ff031adc\", \"size\": \"4.95 MB\", \"duration\": 168.28}", "aliases": ["Everything We Need", "We Begin", "<PERSON>et", "We Begin After The Storm Inside"], "size": "4.95 MB"}, {"id": "tom-raider-2", "name": "<PERSON> 2", "artists": [], "producers": [], "notes": "OG Filename: (8503) <PERSON> 2 97.5bpm\nRough late Yandhi freestyle with very short <PERSON><PERSON><PERSON> vocals.", "length": "147.69", "fileDate": 16891200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0652843e8dc0190cb3abe7dea5f53b15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0652843e8dc0190cb3abe7dea5f53b15\", \"key\": \"Tom Raider 2\", \"title\": \"Tom Raider 2\", \"description\": \"OG Filename: (8503) <PERSON> 2 97.5bpm\\nRough late Yandhi freestyle with very short <PERSON><PERSON><PERSON> vocals.\", \"date\": 16891200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"105c2674968a16bd835ed4f86ec9e827\", \"url\": \"https://api.pillowcase.su/api/download/105c2674968a16bd835ed4f86ec9e827\", \"size\": \"4.62 MB\", \"duration\": 147.69}", "aliases": [], "size": "4.62 MB"}, {"id": "tom-raider-4", "name": "<PERSON> 4 [V1]", "artists": [], "producers": [], "notes": "OG Filename: (video 8502) tom raider 4 86.86bpm\nRough, and extremely short in terms of Ye vocals, iPhone voice memo freestyle, from the December Yandhi sessions. Leaked on October 14th, 2023 and the lossless leaking 7/12/23.", "length": "63.45", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6842c4ae06021bdf3763ad9205f59fcb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6842c4ae06021bdf3763ad9205f59fcb\", \"key\": \"Tom Raider 4\", \"title\": \"<PERSON> Raider 4 [V1]\", \"description\": \"OG Filename: (video 8502) tom raider 4 86.86bpm\\nRough, and extremely short in terms of <PERSON> vocals, iPhone voice memo freestyle, from the December Yandhi sessions. Leaked on October 14th, 2023 and the lossless leaking 7/12/23.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4aaf702bcaa44d21ca5dc4ea5a644b08\", \"url\": \"https://api.pillowcase.su/api/download/4aaf702bcaa44d21ca5dc4ea5a644b08\", \"size\": \"3.27 MB\", \"duration\": 63.45}", "aliases": [], "size": "3.27 MB"}, {"id": "tom-raider-4-131", "name": "<PERSON> 4 [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> 4 BOUNCE 88BPM\nAnother version of \"Tom Raider 4\" but this time with <PERSON><PERSON> vocals.", "length": "109.65", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0ea57d72004c3aa817dfbd2c687dd377", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0ea57d72004c3aa817dfbd2c687dd377\", \"key\": \"Tom Raider 4\", \"title\": \"Tom Raider 4 [V2]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: Tom Raider 4 BOUNCE 88BPM\\nAnother version of \\\"Tom Raider 4\\\" but this time with <PERSON><PERSON> vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fae2d400ee34fabb28ed5080c0e01422\", \"url\": \"https://api.pillowcase.su/api/download/fae2d400ee34fabb28ed5080c0e01422\", \"size\": \"4.01 MB\", \"duration\": 109.65}", "aliases": [], "size": "4.01 MB"}, {"id": "tom-raider-7", "name": "✨ <PERSON> 7", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: KW - tom raider 7 87bpm - BOUNCE\nMumble track with <PERSON><PERSON><PERSON>ly. Refound by pablo999_ on May 2nd 2024.", "length": "164.3", "fileDate": 17146080, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6ca76d24a5abcadaf0461e36fb61264d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ca76d24a5abcadaf0461e36fb61264d\", \"key\": \"Tom Raider 7\", \"title\": \"\\u2728 Tom Raider 7\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: KW - tom raider 7 87bpm - BOUNCE\\nMumble track with <PERSON><PERSON><PERSON>. Refound by pablo999_ on May 2nd 2024.\", \"date\": 17146080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0f92f88b366575060977193553b228f5\", \"url\": \"https://api.pillowcase.su/api/download/0f92f88b366575060977193553b228f5\", \"size\": \"4.89 MB\", \"duration\": 164.3}", "aliases": [], "size": "4.89 MB"}, {"id": "jam-14", "name": "Jam 14 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Jam 14\n14th jam session freestyler over a Timbaland instrumental. Would later be reworked into \"Too Excited\", and some of the vocals in this version would later be cut up and reused for \"Me Too\". Leaked on October 14th, 2023, with the lossless leaking later.", "length": "171.52", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/223944da1a0972a9e6fc64323d0151dc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/223944da1a0972a9e6fc64323d0151dc\", \"key\": \"Jam 14\", \"title\": \"Jam 14 [V1]\", \"artists\": \"(prod. <PERSON>bal<PERSON>)\", \"aliases\": [\"<PERSON> Too\", \"Too Excited\"], \"description\": \"OG Filename: Jam 14\\n14th jam session freestyler over a Timbaland instrumental. Would later be reworked into \\\"Too Excited\\\", and some of the vocals in this version would later be cut up and reused for \\\"Me Too\\\". Leaked on October 14th, 2023, with the lossless leaking later.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1d378547d445912c1648037e19c6b33f\", \"url\": \"https://api.pillowcase.su/api/download/1d378547d445912c1648037e19c6b33f\", \"size\": \"5 MB\", \"duration\": 171.52}", "aliases": ["Me Too", "Too Excited"], "size": "5 MB"}, {"id": "too-excited", "name": "Too Excited [V2]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: JAM 14 (too excited) (normal) 120bpm\nBetter mixed version of \"Jam 14\", and the first version with the title \"Too Excited\". Leaked on October 14th, 2023.", "length": "172.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/dc169d36aa60d3a6dc7bde10e3da776a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc169d36aa60d3a6dc7bde10e3da776a\", \"key\": \"Too Excited\", \"title\": \"Too Excited [V2]\", \"artists\": \"(prod. <PERSON>bal<PERSON>)\", \"aliases\": [\"<PERSON> Too\", \"Jam 14\"], \"description\": \"OG Filename: JAM 14 (too excited) (normal) 120bpm\\nBetter mixed version of \\\"Jam 14\\\", and the first version with the title \\\"Too Excited\\\". Leaked on October 14th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"95a974d86afb7b0e9f41a39a764792f5\", \"url\": \"https://api.pillowcase.su/api/download/95a974d86afb7b0e9f41a39a764792f5\", \"size\": \"5.01 MB\", \"duration\": 172.06}", "aliases": ["Me Too", "Jam 14"], "size": "5.01 MB"}, {"id": "too-excited-135", "name": "Too Excited [V3]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON><PERSON> 14 (too excited) (quarter early) 120bpm\nAlternate version of the mixed \"Jam 14\", starting the clapping a fourth of a beat earlier on the song. Leaked on October 14th, 2023.", "length": "172.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/19173bde490d141346ed9764da853d9f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/19173bde490d141346ed9764da853d9f\", \"key\": \"Too Excited\", \"title\": \"Too Excited [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON> Too\", \"Jam 14\"], \"description\": \"OG Filename: JAM 14 (too excited) (quarter early) 120bpm\\nAlternate version of the mixed \\\"Jam 14\\\", starting the clapping a fourth of a beat earlier on the song. Leaked on October 14th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"10fe4e15aaf1f034cf352ee1de9395c5\", \"url\": \"https://api.pillowcase.su/api/download/10fe4e15aaf1f034cf352ee1de9395c5\", \"size\": \"5.01 MB\", \"duration\": 172.06}", "aliases": ["Me Too", "Jam 14"], "size": "5.01 MB"}, {"id": "jam-14-136", "name": "Jam 14 [V4]", "artists": [], "producers": ["Timbaland"], "notes": "Version of \"Jam 14\" that cuts all of the \"Too Excited\" portions, as it was used to create \"Me Too\". Leaked by <PERSON><PERSON><PERSON> after someone pointed out that the snippet he posted was different to the version that leaked. Unrelated to the Ant Clemons ref of the same name.", "length": "59.62", "fileDate": 15766272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/24aa7bbed34f788f34e27181a7c36ae5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/24aa7bbed34f788f34e27181a7c36ae5\", \"key\": \"Jam 14\", \"title\": \"Jam 14 [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON> Too\", \"Too Excited\"], \"description\": \"Version of \\\"Jam 14\\\" that cuts all of the \\\"Too Excited\\\" portions, as it was used to create \\\"Me Too\\\". Leaked by <PERSON><PERSON><PERSON> after someone pointed out that the snippet he posted was different to the version that leaked. Unrelated to the Ant Clemons ref of the same name.\", \"date\": 15766272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5930c2fed1437b3b876d6064805e8978\", \"url\": \"https://api.pillowcase.su/api/download/5930c2fed1437b3b876d6064805e8978\", \"size\": \"3.21 MB\", \"duration\": 59.62}", "aliases": ["Me Too", "Too Excited"], "size": "3.21 MB"}, {"id": "me-too", "name": "Me Too [V5]", "artists": [], "producers": ["E.VAX", "Timbaland"], "notes": "OG Filename: ME TOO (jam 14 evan version)\nE.VAX produced version of \"Jam 14\". Was leaked along with \"Bye Bye Baby\" after a groupbuy.", "length": "66.05", "fileDate": 15766272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/519c63c3d225951a76d2b78b8a781f28", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/519c63c3d225951a76d2b78b8a781f28\", \"key\": \"Me Too\", \"title\": \"Me Too [V5]\", \"artists\": \"(prod. E.VAX & Timbaland)\", \"aliases\": [\"Too Excited\", \"Jam 14\"], \"description\": \"OG Filename: ME TOO (jam 14 evan version)\\nE.VAX produced version of \\\"Jam 14\\\". Was leaked along with \\\"Bye Bye Baby\\\" after a groupbuy.\", \"date\": 15766272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f7dc55b0158ba006e6af2603612148e4\", \"url\": \"https://api.pillowcase.su/api/download/f7dc55b0158ba006e6af2603612148e4\", \"size\": \"3.31 MB\", \"duration\": 66.05}", "aliases": ["Too Excited", "Jam 14"], "size": "3.31 MB"}, {"id": "jam-14-138", "name": "✨ Jam 14 [V6]", "artists": [], "producers": ["StereoSine", "Timbaland"], "notes": "OG Filename: Jam 14 StereoSine -2\nLater version with Stereosine production, with the OG file being leaked by Alek on June 27th, 2022 as a bonus for the \"Skeletons\" groupbuy. Bounced December 22, 2018.", "length": "183.17", "fileDate": 15825888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d4ad5f8d097de4851d18c6d910f41034", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d4ad5f8d097de4851d18c6d910f41034\", \"key\": \"Jam 14\", \"title\": \"\\u2728 Jam 14 [V6]\", \"artists\": \"(prod. StereoSine & Timbaland)\", \"aliases\": [\"Too Excited\", \"Me Too\"], \"description\": \"OG Filename: Jam 14 StereoSine -2\\nLater version with Stereosine production, with the OG file being leaked by Alek on June 27th, 2022 as a bonus for the \\\"Skeletons\\\" groupbuy. Bounced December 22, 2018.\", \"date\": 15825888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a04e1002aaa3b589ca75a78ca24b2c43\", \"url\": \"https://api.pillowcase.su/api/download/a04e1002aaa3b589ca75a78ca24b2c43\", \"size\": \"5.19 MB\", \"duration\": 183.17}", "aliases": ["Too Excited", "Me Too"], "size": "5.19 MB"}, {"id": "jam-14-139", "name": "Jam 14 [V7]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "StereoSine", "Timbaland"], "notes": "OG Filename: Jam 14 StereoSine (Kosine Edit)\nKosine edit of StereoSine's \"Jam 14\" version, features cleaner and more toned down production.", "length": "186.29", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6f969d32a0c75811fea4c134b0ddfee3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f969d32a0c75811fea4c134b0ddfee3\", \"key\": \"Jam 14\", \"title\": \"Jam 14 [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, StereoSine & Timbaland)\", \"aliases\": [\"Too Excited\", \"Me Too\"], \"description\": \"OG Filename: Jam 14 StereoSine (Kosine Edit)\\nKosine edit of StereoSine's \\\"Jam 14\\\" version, features cleaner and more toned down production.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2406295652ce24af831ca20e92003020\", \"url\": \"https://api.pillowcase.su/api/download/2406295652ce24af831ca20e92003020\", \"size\": \"5.24 MB\", \"duration\": 186.29}", "aliases": ["Too Excited", "Me Too"], "size": "5.24 MB"}, {"id": "too-excited-140", "name": "Too Excited [V8]", "artists": [], "producers": ["Timbaland"], "notes": "Recording snippet from the Timbaland session. Said by <PERSON><PERSON> to be an earlier version than the one that later leaked in full, as it has a completely different melody compared to the other versions we know of, however the video of Timbaland previewing this video was posted in March of 2019, so it could also be later.", "length": "3.42", "fileDate": 15524352, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/761fdc6e8f0a8bbd35151cf431d2cc3d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/761fdc6e8f0a8bbd35151cf431d2cc3d\", \"key\": \"Too Excited\", \"title\": \"Too Excited [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Recording snippet from the Timbaland session. Said by <PERSON><PERSON> to be an earlier version than the one that later leaked in full, as it has a completely different melody compared to the other versions we know of, however the video of <PERSON><PERSON><PERSON> previewing this video was posted in March of 2019, so it could also be later.\", \"date\": 15524352, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9c38a20a352807b1675a6044ba043c9e\", \"url\": \"https://api.pillowcase.su/api/download/9c38a20a352807b1675a6044ba043c9e\", \"size\": \"2.31 MB\", \"duration\": 3.42}", "aliases": [], "size": "2.31 MB"}, {"id": "to-much-tuesday", "name": "To Much Tuesday", "artists": [], "producers": [], "notes": "OG Filename: to much tuesday 97BPM BOUNCE\nFreestyle over a choir and guitar, has some pitched up singing. Said to be from December 2018.", "length": "89.26", "fileDate": 16730496, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/1409c5b4805e15e3bb1abe4a6a51c8d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1409c5b4805e15e3bb1abe4a6a51c8d2\", \"key\": \"To Much Tuesday\", \"title\": \"To Much Tuesday\", \"description\": \"OG Filename: to much tuesday 97BPM BOUNCE\\nFreestyle over a choir and guitar, has some pitched up singing. Said to be from December 2018.\", \"date\": 16730496, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6dc31554f89ca78eb0507ebec2ae49ca\", \"url\": \"https://api.pillowcase.su/api/download/6dc31554f89ca78eb0507ebec2ae49ca\", \"size\": \"3.68 MB\", \"duration\": 89.26}", "aliases": [], "size": "3.68 MB"}, {"id": "triple-six", "name": "Triple Six", "artists": [], "producers": [], "notes": "Yandhi era freestyle. Has similar vocal quality to \"Oh Yeah\", so it is possibly from the same session.", "length": "6.86", "fileDate": 16797888, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a3bb6d8cd56087ef845e5fb50e49c401", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a3bb6d8cd56087ef845e5fb50e49c401\", \"key\": \"Triple Six\", \"title\": \"Triple Six\", \"description\": \"Yandhi era freestyle. Has similar vocal quality to \\\"Oh Yeah\\\", so it is possibly from the same session.\", \"date\": 16797888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"df8b2f973996495fef3d4a91ffe5f6dc\", \"url\": \"https://api.pillowcase.su/api/download/df8b2f973996495fef3d4a91ffe5f6dc\", \"size\": \"2.37 MB\", \"duration\": 6.86}", "aliases": [], "size": "2.37 MB"}, {"id": "pulp-fiction", "name": "Pulp Fiction [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: Pulp Fiction 18 18 164bpm\nSong created during the Yandhi sessions, some mumble and some clear words. Has 16 minutes of <PERSON><PERSON><PERSON> singing and speaking on a light instrumental. Leaked by <PERSON><PERSON> because he hates <PERSON><PERSON>. <PERSON><PERSON> leaked later on.", "length": "943.02", "fileDate": 16060032, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0ecbea16dcd77adba73cb27969628b3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0ecbea16dcd77adba73cb27969628b3c\", \"key\": \"Pulp Fiction\", \"title\": \"Pulp Fiction [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Survive\", \"<PERSON>\"], \"description\": \"OG Filename: Pulp Fiction 18 18 164bpm\\nSong created during the Yandhi sessions, some mumble and some clear words. Has 16 minutes of <PERSON><PERSON><PERSON> singing and speaking on a light instrumental. Leaked by <PERSON><PERSON> because he hates <PERSON><PERSON>. Loss<PERSON> leaked later on.\", \"date\": 16060032, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7b185d86b0d5a272e7c2c0de02cbfd36\", \"url\": \"https://api.pillowcase.su/api/download/7b185d86b0d5a272e7c2c0de02cbfd36\", \"size\": \"17.3 MB\", \"duration\": 943.02}", "aliases": ["Survive", "Sunshine"], "size": "17.3 MB"}, {"id": "sunshine", "name": "Sunshine [V2]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON> (<PERSON><PERSON>) 164 bpm\nCut-down version of \"Pulp Fiction\". Put up for sale by <PERSON> on TheSource.", "length": "188.83", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0d319d7bc5e9b3edf0e47e3cb24577db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d319d7bc5e9b3edf0e47e3cb24577db\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V2]\", \"artists\": \"(prod. <PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Pulp Fiction\", \"Survive\"], \"description\": \"OG Filename: <PERSON> (Mosley) 164 bpm\\nCut-down version of \\\"Pulp Fiction\\\". Put up for sale by <PERSON> on TheSource.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8a44a0b3e18987fbcb6e69c71484c49a\", \"url\": \"https://api.pillowcase.su/api/download/8a44a0b3e18987fbcb6e69c71484c49a\", \"size\": \"5.28 MB\", \"duration\": 188.83}", "aliases": ["Pulp Fiction", "Survive"], "size": "5.28 MB"}, {"id": "survive", "name": "Survive [V3]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "notes": "Vocals end at 3:18s, then it is just instrumental 'till the end. <PERSON> Kanye vocals. Put up for sale by <PERSON> on TheSource.", "length": "245.98", "fileDate": 16174080, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/38206ae5ef451f806793ad128f2d91e7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/38206ae5ef451f806793ad128f2d91e7\", \"key\": \"Survive\", \"title\": \"Survive [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Pulp Fiction\", \"Sunshine\"], \"description\": \"Vocals end at 3:18s, then it is just instrumental 'till the end. No Ka<PERSON> vocals. Put up for sale by <PERSON> on TheSource.\", \"date\": 16174080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4aed28d78457cff8b7fb7453667ea140\", \"url\": \"https://api.pillowcase.su/api/download/4aed28d78457cff8b7fb7453667ea140\", \"size\": \"6.19 MB\", \"duration\": 245.98}", "aliases": ["Pulp Fiction", "Sunshine"], "size": "6.19 MB"}, {"id": "timbaland-freestyle-2", "name": "Timbaland Freestyle 2", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Freestyle over <PERSON><PERSON><PERSON> production. <PERSON><PERSON><PERSON> vocals: 0:14s to 0:59s. <PERSON><PERSON> vocals: 1:00s to 2:57s. Put up for sale by <PERSON> on TheSource.", "length": "10.27", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3b23e0d89775cadb3d309c4774d28eac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3b23e0d89775cadb3d309c4774d28eac\", \"key\": \"Timbaland Freestyle 2\", \"title\": \"Timbaland Freestyle 2\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Freestyle over Timbaland production. <PERSON><PERSON><PERSON> vocals: 0:14s to 0:59s. <PERSON><PERSON> vocals: 1:00s to 2:57s. Put up for sale by <PERSON> on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6886e6cf3511a904b29351cf05a84bb3\", \"url\": \"https://api.pillowcase.su/api/download/6886e6cf3511a904b29351cf05a84bb3\", \"size\": \"2.42 MB\", \"duration\": 10.27}", "aliases": [], "size": "2.42 MB"}, {"id": "timbaland-freestyle-2-147", "name": "Timbaland Freestyle 2", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Freestyle over <PERSON><PERSON><PERSON> production. <PERSON><PERSON><PERSON> vocals: 0:14s to 0:59s. <PERSON><PERSON> vocals: 1:00s to 2:57s. Put up for sale by <PERSON> on TheSource.", "length": "22.73", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/40aa674590b97de4375087df2e014e78", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/40aa674590b97de4375087df2e014e78\", \"key\": \"Timbaland Freestyle 2\", \"title\": \"Timbaland Freestyle 2\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Freestyle over Timbaland production. <PERSON><PERSON><PERSON> vocals: 0:14s to 0:59s. <PERSON><PERSON> vocals: 1:00s to 2:57s. Put up for sale by <PERSON> on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d9f62bf579f26324c77dbdc6d188604a\", \"url\": \"https://api.pillowcase.su/api/download/d9f62bf579f26324c77dbdc6d188604a\", \"size\": \"2.62 MB\", \"duration\": 22.73}", "aliases": [], "size": "2.62 MB"}, {"id": "timbaland-freestyle-3", "name": "Timbaland Freestyle 3 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: (8497) <PERSON> x <PERSON> bagg new 134bpm\nOriginal version of \"Timbaland Freestyle 3\" with very short <PERSON><PERSON><PERSON> vocals.", "length": "157.61", "fileDate": 16891200, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8190d2f04ce66e4b47d6fd78d0efcad2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8190d2f04ce66e4b47d6fd78d0efcad2\", \"key\": \"Timbaland Freestyle 3\", \"title\": \"Timbaland Freestyle 3 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: (8497) Tim x <PERSON> bagg new 134bpm\\nOriginal version of \\\"Timbaland Freestyle 3\\\" with very short <PERSON><PERSON><PERSON> vocals.\", \"date\": 16891200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0365bbc3d018254808d293927e8dbacd\", \"url\": \"https://api.pillowcase.su/api/download/0365bbc3d018254808d293927e8dbacd\", \"size\": \"4.78 MB\", \"duration\": 157.61}", "aliases": [], "size": "4.78 MB"}, {"id": "timbaland-freestyle-3-149", "name": "Timbaland Freestyle 3 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.", "length": "44.64", "fileDate": 16610400, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e7b7f691e43e245535720d03c415ce3a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7b7f691e43e245535720d03c415ce3a\", \"key\": \"Timbaland Freestyle 3\", \"title\": \"Timbaland Freestyle 3 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.\", \"date\": 16610400, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"31e1a697689cf61726b8b2c3c1a8f0a6\", \"url\": \"https://api.pillowcase.su/api/download/31e1a697689cf61726b8b2c3c1a8f0a6\", \"size\": \"2.97 MB\", \"duration\": 44.64}", "aliases": [], "size": "2.97 MB"}, {"id": "timbaland-freestyle-3-150", "name": "Timbaland Freestyle 3 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.", "length": "14.55", "fileDate": 16610400, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/33e074297ad1f5a03fbe369f67549dd5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/33e074297ad1f5a03fbe369f67549dd5\", \"key\": \"Timbaland Freestyle 3\", \"title\": \"Timbaland Freestyle 3 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.\", \"date\": 16610400, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0f329542a7f8d37920604b717a8da25a\", \"url\": \"https://api.pillowcase.su/api/download/0f329542a7f8d37920604b717a8da25a\", \"size\": \"2.49 MB\", \"duration\": 14.55}", "aliases": [], "size": "2.49 MB"}, {"id": "timbaland-freestyle-3-151", "name": "Timbaland Freestyle 3 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.", "length": "13.51", "fileDate": 16610400, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8a484eef3f953fcdfcbf261f61f43fa6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8a484eef3f953fcdfcbf261f61f43fa6\", \"key\": \"Timbaland Freestyle 3\", \"title\": \"Timbaland Freestyle 3 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.\", \"date\": 16610400, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"adabac388099211a7e325b8fca56d9d6\", \"url\": \"https://api.pillowcase.su/api/download/adabac388099211a7e325b8fca56d9d6\", \"size\": \"2.47 MB\", \"duration\": 13.51}", "aliases": [], "size": "2.47 MB"}, {"id": "timbaland-freestyle-3-152", "name": "Timbaland Freestyle 3 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Timbaland"], "notes": "Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.", "length": "157.61", "fileDate": 16610400, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8ce382eab95abfea1b9f4b37e604e47a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ce382eab95abfea1b9f4b37e604e47a\", \"key\": \"Timbaland Freestyle 3\", \"title\": \"Timbaland Freestyle 3 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Freestyle track from Timbaland sessions in December 2018. A new snippet leaked August 21st, 2022.\", \"date\": 16610400, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a60782627dee03b7e4286618f1a18b3b\", \"url\": \"https://api.pillowcase.su/api/download/a60782627dee03b7e4286618f1a18b3b\", \"size\": \"4.78 MB\", \"duration\": 157.61}", "aliases": [], "size": "4.78 MB"}, {"id": "wakey-wakey", "name": "Wakey Wakey [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Wakey_Kosine_110bpm\n<PERSON><PERSON><PERSON> mumble \"Wakey Wakey\" reference track. Leaked alongside the Ant Clemons reference track for \"Home\".", "length": "280.61", "fileDate": 16112736, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/96047c0474d23edf49f36cd662814fcf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/96047c0474d23edf49f36cd662814fcf\", \"key\": \"Wakey Wakey\", \"title\": \"Wakey Wakey [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Wakey Wakey_Kosine_110bpm\\nKosine mumble \\\"Wakey Wakey\\\" reference track. Leaked alongside the Ant Clemons reference track for \\\"Home\\\".\", \"date\": 16112736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c25047d993f75121f4c9a2194f5a545c\", \"url\": \"https://api.pillowcase.su/api/download/c25047d993f75121f4c9a2194f5a545c\", \"size\": \"6.75 MB\", \"duration\": 280.61}", "aliases": [], "size": "6.75 MB"}, {"id": "wakey-wakey-154", "name": "Wakey Wakey [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON>. _ <PERSON><PERSON><PERSON>_<PERSON><PERSON>\nHas more finished <PERSON><PERSON><PERSON> vocals, saxophone and extra background vocals not present in the previous version. Leaked by <PERSON><PERSON> on June 27th, 2022 as a bonus for the \"Skeletons\" groupbuy.", "length": "281.52", "fileDate": 16562880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e42c27af30b71403582c6e8d6e3b30a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e42c27af30b71403582c6e8d6e3b30a0\", \"key\": \"Wakey Wakey\", \"title\": \"Wakey Wakey [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>y Wakey. _ <PERSON><PERSON><PERSON>_De<PERSON>\\nHas more finished <PERSON><PERSON><PERSON> vocals, saxophone and extra background vocals not present in the previous version. Leaked by <PERSON><PERSON> on June 27th, 2022 as a bonus for the \\\"Skeletons\\\" groupbuy.\", \"date\": 16562880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a1e338c68cd5a205add9475284f8ab23\", \"url\": \"https://api.pillowcase.su/api/download/a1e338c68cd5a205add9475284f8ab23\", \"size\": \"6.76 MB\", \"duration\": 281.52}", "aliases": [], "size": "6.76 MB"}, {"id": "jam-6", "name": "Jam 6 [V1]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: <PERSON> 6 (UCLA alt)\n6th jam session freestyle over a <PERSON><PERSON><PERSON> instrumental. Later transformed into \"Welcome To UCLA\".", "length": "411.19", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6d7f80cce5a050f6c0f01cf362f7a4a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d7f80cce5a050f6c0f01cf362f7a4a1\", \"key\": \"Jam 6\", \"title\": \"Jam 6 [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Welcome To UCLA\", \"UCLA\"], \"description\": \"OG Filename: Jam 6 (UCLA alt)\\n6th jam session freestyle over a Timbaland instrumental. Later transformed into \\\"Welcome To UCLA\\\".\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a0a883297c90d65537f93fbbb491e23\", \"url\": \"https://api.pillowcase.su/api/download/4a0a883297c90d65537f93fbbb491e23\", \"size\": \"8.84 MB\", \"duration\": 411.19}", "aliases": ["Welcome To UCLA", "UCLA"], "size": "8.84 MB"}, {"id": "welcome-to-ucla", "name": "Welcome To UCLA [V2]", "artists": [], "producers": ["Timbaland"], "notes": "OG Filename: Welcome To UCLA (1st Rough arrangement)\nVery unfinished track. While it's possible a LOVE EVERYONE version of the song exists as was long claimed, this version was made during the Timbaland sessions in December 2018 and the song likely was made in this era. It contains one of the sounds <PERSON><PERSON><PERSON> used in \"Jam 7\".", "length": "138.07", "fileDate": 15698880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/88a9d6c34248355ab726cbc64e5e46d9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/88a9d6c34248355ab726cbc64e5e46d9\", \"key\": \"Welcome To UCLA\", \"title\": \"Welcome To UCLA [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Jam 6\", \"UCLA\"], \"description\": \"OG Filename: Welcome To UCLA (1st Rough arrangement)\\nVery unfinished track. While it's possible a LOVE EVERYONE version of the song exists as was long claimed, this version was made during the Timbaland sessions in December 2018 and the song likely was made in this era. It contains one of the sounds Tim<PERSON><PERSON> used in \\\"Jam 7\\\".\", \"date\": 15698880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d72620a571ae0aad4eaa71f90ad04321\", \"url\": \"https://api.pillowcase.su/api/download/d72620a571ae0aad4eaa71f90ad04321\", \"size\": \"4.47 MB\", \"duration\": 138.07}", "aliases": ["Jam 6", "UCLA"], "size": "4.47 MB"}, {"id": "wouldn-t-be-here", "name": "Wouldn't Be Here", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.", "length": "117.89", "fileDate": 15903648, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/49f71d48f490f1c2d5f5c4af9155ce84", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/49f71d48f490f1c2d5f5c4af9155ce84\", \"key\": \"Wouldn't Be Here\", \"title\": \"Wouldn't Be Here\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"One of 6 freestyles produced by <PERSON><PERSON><PERSON>. Has the live freestyle vocals overlayed on the original beat.\", \"date\": 15903648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1310e5459f7a813ab44108c84dbd32fe\", \"url\": \"https://api.pillowcase.su/api/download/1310e5459f7a813ab44108c84dbd32fe\", \"size\": \"4.14 MB\", \"duration\": 117.89}", "aliases": [], "size": "4.14 MB"}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "Untitled freestyle leaked as a bonus for completing the \"God's Test\" groupbuy. Era unknown.", "length": "125.5", "fileDate": 16581024, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/9882dd50c873190e626147990ef2d99e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9882dd50c873190e626147990ef2d99e\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Ain't No Freestyle\"], \"description\": \"Untitled freestyle leaked as a bonus for completing the \\\"God's Test\\\" groupbuy. Era unknown.\", \"date\": 16581024, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e9d38cc5cc79e9bcb6968404cc6d6bff\", \"url\": \"https://api.pillowcase.su/api/download/e9d38cc5cc79e9bcb6968404cc6d6bff\", \"size\": \"4.26 MB\", \"duration\": 125.5}", "aliases": ["Ain't No Freestyle"], "size": "4.26 MB"}, {"id": "-159", "name": "??? [V1]", "artists": [], "producers": [], "notes": "<PERSON>'s song \"Anything Is Possible\" initially started out as a song for <PERSON><PERSON><PERSON>, when <PERSON><PERSON><PERSON><PERSON> played his short film for Ka<PERSON>e which featured the song and <PERSON><PERSON><PERSON> freestyled as it played. Footage of this would be shown in the trailer for <PERSON><PERSON><PERSON><PERSON>'s documentary, <PERSON><PERSON><PERSON>y also named Anything Is Possible, posted December 19th, 2024.", "length": "7", "fileDate": 17345664, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c55e0b5b28de51f7b2f368bb37794e44", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c55e0b5b28de51f7b2f368bb37794e44\", \"key\": \"???\", \"title\": \"??? [V1]\", \"aliases\": [\"Anything Is Possible\"], \"description\": \"<PERSON>'s song \\\"Anything Is Possible\\\" initially started out as a song for <PERSON><PERSON><PERSON>, when <PERSON><PERSON><PERSON><PERSON> played his short film for <PERSON><PERSON><PERSON> which featured the song and <PERSON><PERSON><PERSON> freestyled as it played. Footage of this would be shown in the trailer for <PERSON><PERSON><PERSON><PERSON>'s documentary, a<PERSON><PERSON><PERSON> also named Anything Is Possible, posted December 19th, 2024.\", \"date\": 17345664, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"00ac69858560536680d1c4eb9188b855\", \"url\": \"https://api.pillowcase.su/api/download/00ac69858560536680d1c4eb9188b855\", \"size\": \"2.37 MB\", \"duration\": 7}", "aliases": ["Anything Is Possible"], "size": "2.37 MB"}, {"id": "-160", "name": "??? [V1]", "artists": [], "producers": [], "notes": "<PERSON>'s song \"Anything Is Possible\" initially started out as a song for <PERSON><PERSON><PERSON>, when <PERSON><PERSON><PERSON><PERSON> played his short film for Ka<PERSON>e which featured the song and <PERSON><PERSON><PERSON> freestyled as it played. Footage of this would be shown in the trailer for <PERSON><PERSON><PERSON><PERSON>'s documentary, <PERSON><PERSON><PERSON>y also named Anything Is Possible, posted December 19th, 2024.", "length": "17.37", "fileDate": 17345664, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c3c49507f1e272596bb61439cbb15059", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3c49507f1e272596bb61439cbb15059\", \"key\": \"???\", \"title\": \"??? [V1]\", \"aliases\": [\"Anything Is Possible\"], \"description\": \"<PERSON>'s song \\\"Anything Is Possible\\\" initially started out as a song for <PERSON><PERSON><PERSON>, when <PERSON><PERSON><PERSON><PERSON> played his short film for <PERSON><PERSON><PERSON> which featured the song and <PERSON><PERSON><PERSON> freestyled as it played. Footage of this would be shown in the trailer for <PERSON><PERSON><PERSON><PERSON>'s documentary, a<PERSON><PERSON><PERSON> also named Anything Is Possible, posted December 19th, 2024.\", \"date\": 17345664, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8dbd7eb9191e40b65adcbb1557f8a75f\", \"url\": \"https://api.pillowcase.su/api/download/8dbd7eb9191e40b65adcbb1557f8a75f\", \"size\": \"2.53 MB\", \"duration\": 17.37}", "aliases": ["Anything Is Possible"], "size": "2.53 MB"}, {"id": "-161", "name": "???", "artists": [], "producers": ["BONGO ByTheWay"], "notes": "2018 Kanye track produced by BONGO. Snippet posted by slapstick.", "length": "8.28", "fileDate": 17243712, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d9a72f41c7aafa4765a203cc6d55096a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9a72f41c7aafa4765a203cc6d55096a\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"aliases\": [\"Finding Us\"], \"description\": \"2018 Kanye track produced by BONGO. Snippet posted by slapstick.\", \"date\": 17243712, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"45479f6381ecc3d59a5e5c073e6a1de5\", \"url\": \"https://api.pillowcase.su/api/download/45479f6381ecc3d59a5e5c073e6a1de5\", \"size\": \"2.39 MB\", \"duration\": 8.28}", "aliases": ["Finding Us"], "size": "2.39 MB"}, {"id": "-162", "name": "???", "artists": [], "producers": ["E.VAX", "Timbaland"], "notes": "Freestyle over production by E.VAX and Timbaland. Vocals end at 1:14 then it is just instrumental. Was being sold as \"Freestyle 1\". Put up for sale by <PERSON> on TheSource.", "length": "9.87", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/91ea58ba4606fed3fa63c848c75b8a2c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91ea58ba4606fed3fa63c848c75b8a2c\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. E.VAX & Timbaland)\", \"aliases\": [\"Freestyle 1\"], \"description\": \"Freestyle over production by E.VAX and Timbaland. Vocals end at 1:14 then it is just instrumental. Was being sold as \\\"Freestyle 1\\\". Put up for sale by <PERSON> on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0ec03fba861db5e97b1ee2bfce82eeb8\", \"url\": \"https://api.pillowcase.su/api/download/0ec03fba861db5e97b1ee2bfce82eeb8\", \"size\": \"2.42 MB\", \"duration\": 9.87}", "aliases": ["Freestyle 1"], "size": "2.42 MB"}, {"id": "-163", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Freestyle over Timbaland production. Was being sold as \"Freestyle 2\". Put up for sale by <PERSON> on TheSource.", "length": "7.16", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0b9ec89b2aae964ae32a9de473fec70b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b9ec89b2aae964ae32a9de473fec70b\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Timbaland)\", \"aliases\": [\"Freestyle 2\"], \"description\": \"Freestyle over Timbaland production. Was being sold as \\\"Freestyle 2\\\". Put up for sale by <PERSON> Goblin on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2ff4f2cd70141a4fc40512042eb7a461\", \"url\": \"https://api.pillowcase.su/api/download/2ff4f2cd70141a4fc40512042eb7a461\", \"size\": \"2.37 MB\", \"duration\": 7.16}", "aliases": ["Freestyle 2"], "size": "2.37 MB"}, {"id": "-164", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Freestyle over Timbaland production. Was being sold as \"Freestyle 3\". Put up for sale by <PERSON> on TheSource.", "length": "9.61", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c11f2a01e1da483d7ca6d176e918f13f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c11f2a01e1da483d7ca6d176e918f13f\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Timbaland)\", \"aliases\": [\"Freestyle 3\"], \"description\": \"Freestyle over Timbaland production. Was being sold as \\\"Freestyle 3\\\". Put up for sale by <PERSON> Goblin on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3a2942dd4ad0ed75f5ff05dee5f2207a\", \"url\": \"https://api.pillowcase.su/api/download/3a2942dd4ad0ed75f5ff05dee5f2207a\", \"size\": \"2.41 MB\", \"duration\": 9.61}", "aliases": ["Freestyle 3"], "size": "2.41 MB"}, {"id": "-165", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Freestyle over Timbaland production. Was being sold as \"Freestyle 4\". Put up for sale by <PERSON> on TheSource.", "length": "7.68", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3dc2c0dadfd6c8a97e876435d0d44f24", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3dc2c0dadfd6c8a97e876435d0d44f24\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Timbaland)\", \"aliases\": [\"Freestyle 4\"], \"description\": \"Freestyle over Timbaland production. Was being sold as \\\"Freestyle 4\\\". Put up for sale by <PERSON> Goblin on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f9c03b80cf07893fa97d2447ece2993\", \"url\": \"https://api.pillowcase.su/api/download/8f9c03b80cf07893fa97d2447ece2993\", \"size\": \"2.38 MB\", \"duration\": 7.68}", "aliases": ["Freestyle 4"], "size": "2.38 MB"}, {"id": "-166", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Untitled freestyle over Timbaland production. Was being sold as \"Freestyle 5\" by <PERSON> on TheSource. Later snippet leaked from unreleasedsounds on February 2nd, 2023.", "length": "7.29", "fileDate": 16752960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/30c3cccde6fd3a23294f058831b96b1b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/30c3cccde6fd3a23294f058831b96b1b\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Timbaland)\", \"aliases\": [\"Freestyle 5\"], \"description\": \"Untitled freestyle over Timbaland production. Was being sold as \\\"Freestyle 5\\\" by <PERSON> on TheSource. Later snippet leaked from unreleasedsounds on February 2nd, 2023.\", \"date\": 16752960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b24223fdee78b024a46cbbee0584cabd\", \"url\": \"https://api.pillowcase.su/api/download/b24223fdee78b024a46cbbee0584cabd\", \"size\": \"2.37 MB\", \"duration\": 7.29}", "aliases": ["Freestyle 5"], "size": "2.37 MB"}, {"id": "-167", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Untitled freestyle over Timbaland production. Was being sold as \"Freestyle 5\" by <PERSON> on TheSource. Later snippet leaked from unreleasedsounds on February 2nd, 2023.", "length": "15.1", "fileDate": 16752960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c49a1a4712930873dfb432e6f45a5a4c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c49a1a4712930873dfb432e6f45a5a4c\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Timbal<PERSON>)\", \"aliases\": [\"Freestyle 5\"], \"description\": \"Untitled freestyle over Timbaland production. Was being sold as \\\"Freestyle 5\\\" by <PERSON> on TheSource. Later snippet leaked from unreleasedsounds on February 2nd, 2023.\", \"date\": 16752960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"255fb9b7baa51664187f11be77ed4489\", \"url\": \"https://api.pillowcase.su/api/download/255fb9b7baa51664187f11be77ed4489\", \"size\": \"2.5 MB\", \"duration\": 15.1}", "aliases": ["Freestyle 5"], "size": "2.5 MB"}, {"id": "-168", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Freestyle over Timbaland production. Was being sold as \"Freestyle 10\". Snippet from TheSource tier 2.", "length": "8.99", "fileDate": 16111872, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/923eb2c1489c43b91f8e1a512b1bd4c7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/923eb2c1489c43b91f8e1a512b1bd4c7\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Timbaland)\", \"aliases\": [\"Freestyle 10\"], \"description\": \"Freestyle over Timbaland production. Was being sold as \\\"Freestyle 10\\\". Snippet from TheSource tier 2.\", \"date\": 16111872, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c8b9665172ba9dcc45e8a10fd3b3dd6\", \"url\": \"https://api.pillowcase.su/api/download/2c8b9665172ba9dcc45e8a10fd3b3dd6\", \"size\": \"2.4 MB\", \"duration\": 8.99}", "aliases": ["Freestyle 10"], "size": "2.4 MB"}, {"id": "-169", "name": "???", "artists": [], "producers": [], "notes": "Was being sold as \"Freestyle 11\". Snippet from TheSource tier 2.", "length": "9.09", "fileDate": 16111872, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/7e7875484e9d796ea37644c69bbb0bec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e7875484e9d796ea37644c69bbb0bec\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Freestyle 11\"], \"description\": \"Was being sold as \\\"Freestyle 11\\\". Snippet from TheSource tier 2.\", \"date\": 16111872, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e0987877cef1dbc14724e6c119c3c2b0\", \"url\": \"https://api.pillowcase.su/api/download/e0987877cef1dbc14724e6c119c3c2b0\", \"size\": \"2.4 MB\", \"duration\": 9.09}", "aliases": ["Freestyle 11"], "size": "2.4 MB"}, {"id": "-170", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Snippet posted by TheSource and Gumy. Mistaken to be demo known as \"Away From Home\". Was being sold as \"Kanye West G\" on TheSource.", "length": "14.14", "fileDate": 15952896, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/7d628b0ea66f40f16150d4af7c2c5ea9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7d628b0ea66f40f16150d4af7c2c5ea9\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Kanye <PERSON> G\"], \"description\": \"Snippet posted by TheSource and Gumy. Mistaken to be demo known as \\\"Away From Home\\\". Was being sold as \\\"Kanye West G\\\" on TheSource.\", \"date\": 15952896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f5673160d5197be329c84728a06cc1a1\", \"url\": \"https://api.pillowcase.su/api/download/f5673160d5197be329c84728a06cc1a1\", \"size\": \"2.48 MB\", \"duration\": 14.14}", "aliases": ["Kanye West G"], "size": "2.48 MB"}, {"id": "-171", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Snippet posted by TheSource and Gumy. Mistaken to be demo known as \"Away From Home\". Was being sold as \"Kanye West G\" on TheSource.", "length": "10.66", "fileDate": 15952896, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3391b2968784996ac9020d211a5f5d4e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3391b2968784996ac9020d211a5f5d4e\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Kanye <PERSON> G\"], \"description\": \"Snippet posted by TheSource and Gumy. Mistaken to be demo known as \\\"Away From Home\\\". Was being sold as \\\"Kanye West G\\\" on TheSource.\", \"date\": 15952896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6c960881f3891db05d68a6ae9cb2747\", \"url\": \"https://api.pillowcase.su/api/download/d6c960881f3891db05d68a6ae9cb2747\", \"size\": \"2.43 MB\", \"duration\": 10.66}", "aliases": ["Kanye West G"], "size": "2.43 MB"}, {"id": "-172", "name": "???", "artists": [], "producers": ["Timbaland"], "notes": "Snippet posted by TheSource and Gumy. Mistaken to be demo known as \"Away From Home\". Was being sold as \"Kanye West G\" on TheSource.", "length": "25.1", "fileDate": 15952896, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6ea5b5011d37e7f96df3fbad9fe9c920", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ea5b5011d37e7f96df3fbad9fe9c920\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Kanye West G\"], \"description\": \"Snippet posted by TheSource and Gumy. Mistaken to be demo known as \\\"Away From Home\\\". Was being sold as \\\"Kanye West G\\\" on TheSource.\", \"date\": 15952896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a31d3453dc78f13a055018dbd2cbe10\", \"url\": \"https://api.pillowcase.su/api/download/4a31d3453dc78f13a055018dbd2cbe10\", \"size\": \"2.66 MB\", \"duration\": 25.1}", "aliases": ["Kanye West G"], "size": "2.66 MB"}, {"id": "-173", "name": "???", "artists": [], "producers": ["BONGO ByTheWay"], "notes": "2018 Kanye track produced by BONGO. Snippet posted by slapstick.", "length": "6.9", "fileDate": 17243712, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8b931c7ce99f4381b625577a5ff651ea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b931c7ce99f4381b625577a5ff651ea\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"description\": \"2018 Kanye track produced by BONGO. Snippet posted by slapstick.\", \"date\": 17243712, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"48a62a83c4fd39d8bc39f18cf49bb7cd\", \"url\": \"https://api.pillowcase.su/api/download/48a62a83c4fd39d8bc39f18cf49bb7cd\", \"size\": \"2.37 MB\", \"duration\": 6.9}", "aliases": [], "size": "2.37 MB"}, {"id": "-174", "name": "???", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Mumble freestyle over a trap beat. Was wrongly thought to be \"Wakey Wakey\".", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8444508befec4a06f48ad92d47f41f11", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8444508befec4a06f48ad92d47f41f11\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"Mumble freestyle over a trap beat. Was wrongly thought to be \\\"Wakey Wakey\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "-175", "name": "???", "artists": [], "producers": [], "notes": "Yandhi era freestyle. Has similar vocal quality to \"Oh Yeah\", so it is possibly from the same session.", "length": "6.86", "fileDate": 16797888, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/1ca448249beb7b7f8a0349c280d64b69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ca448249beb7b7f8a0349c280d64b69\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Vibes\"], \"description\": \"Yandhi era freestyle. Has similar vocal quality to \\\"Oh Yeah\\\", so it is possibly from the same session.\", \"date\": 16797888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d8207c5a2776b0f884d9a29865873578\", \"url\": \"https://api.pillowcase.su/api/download/d8207c5a2776b0f884d9a29865873578\", \"size\": \"2.37 MB\", \"duration\": 6.86}", "aliases": ["Vibes"], "size": "2.37 MB"}, {"id": "-176", "name": "???", "artists": [], "producers": [], "notes": "Yandhi era freestyle. Has similar vocal quality to \"Oh Yeah\", so it is possibly from the same session.", "length": "6.86", "fileDate": 16797888, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/69cbdf1f0c9adee8227de7c16bb263bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/69cbdf1f0c9adee8227de7c16bb263bb\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Vibes\"], \"description\": \"Yandhi era freestyle. Has similar vocal quality to \\\"Oh Yeah\\\", so it is possibly from the same session.\", \"date\": 16797888, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b83ab15d55347d49d4b4a187dadda86b\", \"url\": \"https://api.pillowcase.su/api/download/b83ab15d55347d49d4b4a187dadda86b\", \"size\": \"2.37 MB\", \"duration\": 6.86}", "aliases": ["Vibes"], "size": "2.37 MB"}, {"id": "forever", "name": "Juice WRLD - Forever [V1]", "artists": [], "producers": ["<PERSON>", "JRHITMAKER"], "notes": "Original version of \"Forever\" made during the sessions for Juice WRLD's album Death Race For Love. Song is fully completed with a chorus and two verses. Recorded on November 14th, 2018. Was thought to be leaked in full, but was confirmed later on to not be the full file, and that we are missing about 10 seconds.", "length": "200.75", "fileDate": 17053632, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0c81a59d2f5e76a999b20749514c0699", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0c81a59d2f5e76a999b20749514c0699\", \"key\": \"Forever\", \"title\": \"Juice WRLD - Forever [V1]\", \"artists\": \"(prod. <PERSON> & JRHITMAKER)\", \"aliases\": [\"Timeouts\"], \"description\": \"Original version of \\\"Forever\\\" made during the sessions for Juice WRLD's album Death Race For Love. Song is fully completed with a chorus and two verses. Recorded on November 14th, 2018. Was thought to be leaked in full, but was confirmed later on to not be the full file, and that we are missing about 10 seconds.\", \"date\": 17053632, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"810d781f2d9eeed64d1eb2dd7c0f62ce\", \"url\": \"https://api.pillowcase.su/api/download/810d781f2d9eeed64d1eb2dd7c0f62ce\", \"size\": \"5.47 MB\", \"duration\": 200.75}", "aliases": ["Timeouts"], "size": "5.47 MB"}, {"id": "ego-death", "name": "Ty Dolla $ign - Ego Death [V3]", "artists": ["Kanye West", "FKA twigs", "serpentwithfeet"], "producers": ["Skrillex", "BoogzDaBeast"], "notes": "First previewed during Ty Dolla $ign's 2019 Coachella set. Leaked in February 2020 after a groupbuy from SongShop. Has fully uncensored <PERSON><PERSON><PERSON> vocals.", "length": "229.41", "fileDate": 15828480, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/48360f58059d4b6b5c8a4cdba4f3806f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48360f58059d4b6b5c8a4cdba4f3806f\", \"key\": \"Ego Death\", \"title\": \"Ty Dolla $ign - Ego Death [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, FKA twigs & serpentwithfeet) (prod. Skrillex & BoogzDaBeast)\", \"description\": \"First previewed during Ty Dolla $ign's 2019 Coachella set. Leaked in February 2020 after a groupbuy from SongShop. Has fully uncensored <PERSON><PERSON><PERSON> vocals.\", \"date\": 15828480, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2fff5608dbbe1b4f5ce093bb005c7ee8\", \"url\": \"https://api.pillowcase.su/api/download/2fff5608dbbe1b4f5ce093bb005c7ee8\", \"size\": \"5.93 MB\", \"duration\": 229.41}", "aliases": [], "size": "5.93 MB"}, {"id": "ego-death-179", "name": "Ty Dolla $ign - Ego Death [V4]", "artists": ["Kanye West", "FKA twigs", "serpentwithfeet"], "producers": ["<PERSON><PERSON>", "BoogzDaBeast"], "notes": "A version previewed some time in January 2019. Has completely different drums compared to the other leak. Produced by <PERSON><PERSON>.", "length": "59.95", "fileDate": 15463008, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5959c9db4b9a8bdddf68892f685d7d89", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5959c9db4b9a8bdddf68892f685d7d89\", \"key\": \"Ego Death\", \"title\": \"Ty Dolla $ign - Ego Death [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, FKA twigs & serpentwithfeet) (prod. Dre Sinatra & BoogzDaBeast)\", \"description\": \"A version previewed some time in January 2019. Has completely different drums compared to the other leak. Produced by <PERSON><PERSON>.\", \"date\": 15463008, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a27cf77df858b73a22120c3f536feb68\", \"url\": \"https://api.pillowcase.su/api/download/a27cf77df858b73a22120c3f536feb68\", \"size\": \"2.74 MB\", \"duration\": 59.95}", "aliases": [], "size": "2.74 MB"}, {"id": "ego-death-180", "name": "Ty Dolla $ign - Ego Death [V6]", "artists": ["FKA twigs", "serpentwithfeet"], "producers": ["<PERSON><PERSON>", "BoogzDaBeast"], "notes": "Version of \"Ego Death\" with no <PERSON><PERSON><PERSON> vocals.", "length": "202.01", "fileDate": 16972416, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/94bbf90a81dbcd9ec3d37cd0205cc9ab", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/94bbf90a81dbcd9ec3d37cd0205cc9ab\", \"key\": \"Ego Death\", \"title\": \"Ty Dolla $ign - Ego Death [V6]\", \"artists\": \"(feat. <PERSON><PERSON> twigs & serpentwithfeet) (prod. <PERSON><PERSON> & BoogzDaBeast)\", \"description\": \"Version of \\\"Ego Death\\\" with no <PERSON><PERSON><PERSON> vocals.\", \"date\": 16972416, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a1bedbad44c55cadb208b4c676a7ed01\", \"url\": \"https://api.pillowcase.su/api/download/a1bedbad44c55cadb208b4c676a7ed01\", \"size\": \"5.49 MB\", \"duration\": 202.01}", "aliases": [], "size": "5.49 MB"}, {"id": "one-minute", "name": "XXXTENTACION - One Minute [V6]", "artists": ["Kanye West", "<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: One Minute (ft Ye) - Clean (KH Final Mix v4) (MASTERED DK)\nCensored OG file of \"One Minute\", may be the same as the one on streaming.", "length": "197.76", "fileDate": 16661376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c6fa7d594822ce87b77bc5e7aeb772b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c6fa7d594822ce87b77bc5e7aeb772b6\", \"key\": \"One Minute\", \"title\": \"XXXTENTACION - One Minute [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: One Minute (ft Ye) - Clean (KH Final Mix v4) (MASTERED DK)\\nCensored OG file of \\\"One Minute\\\", may be the same as the one on streaming.\", \"date\": 16661376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1da5ade55a357ae0c80fa633f87e2deb\", \"url\": \"https://api.pillowcase.su/api/download/1da5ade55a357ae0c80fa633f87e2deb\", \"size\": \"5.42 MB\", \"duration\": 197.76}", "aliases": [], "size": "5.42 MB"}, {"id": "mixed-personalities", "name": "<PERSON><PERSON><PERSON>ly - Mixed Personalities [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "Solo version of \"Mixed Personalities\" that is almost a completely different song. There are different verses, different vocal takes, and raw vocals and the whole song is unmixed and rough.", "length": "231.66", "fileDate": 15909696, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/767c50ccdaf6848657a6808aaf35e0a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/767c50ccdaf6848657a6808aaf35e0a1\", \"key\": \"Mixed Personalities\", \"title\": \"<PERSON>N<PERSON> Melly - Mixed Personalities [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Solo version of \\\"Mixed Personalities\\\" that is almost a completely different song. There are different verses, different vocal takes, and raw vocals and the whole song is unmixed and rough.\", \"date\": 15909696, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ea31cecd21254834a75a55ddc4c0507\", \"url\": \"https://api.pillowcase.su/api/download/5ea31cecd21254834a75a55ddc4c0507\", \"size\": \"5.97 MB\", \"duration\": 231.66}", "aliases": [], "size": "5.97 MB"}, {"id": "mixed-personalities-183", "name": "<PERSON><PERSON><PERSON> - Mixed Personalities [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "Same as the previous version, but fully mixed.", "length": "16.07", "fileDate": 16666560, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/42f160be874e9e84b0b2448caaf8615c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/42f160be874e9e84b0b2448caaf8615c\", \"key\": \"Mixed Personalities\", \"title\": \"<PERSON><PERSON><PERSON> Melly - Mixed Personalities [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Same as the previous version, but fully mixed.\", \"date\": 16666560, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"44894b01637dda051cec0758a767b00f\", \"url\": \"https://api.pillowcase.su/api/download/44894b01637dda051cec0758a767b00f\", \"size\": \"2.51 MB\", \"duration\": 16.07}", "aliases": [], "size": "2.51 MB"}, {"id": "mixed-personalities-184", "name": "<PERSON><PERSON><PERSON> - Mixed Personalities [V3]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>", "Timbaland"], "notes": "Solo version of \"Mixed Personalities\" by <PERSON><PERSON><PERSON> with completely different drums. Leaked by <PERSON><PERSON> along with the Pro Tools of this demo.", "length": "317.35", "fileDate": 16061760, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0a9cf07ab3a0e342b9d2ae802823c487", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a9cf07ab3a0e342b9d2ae802823c487\", \"key\": \"Mixed Personalities\", \"title\": \"<PERSON><PERSON><PERSON> Melly - Mixed Personalities [V3]\", \"artists\": \"(prod. C-<PERSON><PERSON>z & Timbaland)\", \"description\": \"Solo version of \\\"Mixed Personalities\\\" by <PERSON><PERSON><PERSON> with completely different drums. Leaked by <PERSON><PERSON> along with the Pro Tools of this demo.\", \"date\": 16061760, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"22a4b800632553890df5e4900743dbc8\", \"url\": \"https://api.pillowcase.su/api/download/22a4b800632553890df5e4900743dbc8\", \"size\": \"7.33 MB\", \"duration\": 317.35}", "aliases": [], "size": "7.33 MB"}, {"id": "mixed-personalities-185", "name": "<PERSON><PERSON><PERSON> - Mixed Personalities [V4]", "artists": ["Kanye West"], "producers": ["<PERSON><PERSON><PERSON><PERSON>", "Timbaland"], "notes": "Closest version of \"Mixed Personalities\" to release. This version has an alt mix compared to final.", "length": "228.48", "fileDate": 17267904, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/78e539734767e023f75b7a4e79ac9472", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/78e539734767e023f75b7a4e79ac9472\", \"key\": \"Mixed Personalities\", \"title\": \"<PERSON><PERSON><PERSON> Melly - Mixed Personalities [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. C-<PERSON><PERSON> & Timbaland)\", \"description\": \"Closest version of \\\"Mixed Personalities\\\" to release. This version has an alt mix compared to final.\", \"date\": 17267904, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"20a67a10a8af8b4ef43c89b4480546b2\", \"url\": \"https://api.pillowcase.su/api/download/20a67a10a8af8b4ef43c89b4480546b2\", \"size\": \"5.92 MB\", \"duration\": 228.48}", "aliases": [], "size": "5.92 MB"}, {"id": "rich-nigga-shit", "name": "Young Thug - <PERSON>gga <PERSON> [V2]", "artists": ["Juice WRLD"], "producers": ["<PERSON><PERSON>erre <PERSON>", "Kanye West"], "notes": "OG Filename: THUG x JUICEWRLD - RICH NIGGA SHIT (BAINZRUFF)\nOG file of the released Young Thug and Juice WRLD song that reuses production from \"Amistad\".", "length": "177.51", "fileDate": 16660512, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c2130d632baeadf2b0c2b0e81544a0db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c2130d632baeadf2b0c2b0e81544a0db\", \"key\": \"Rich Nigga Shit\", \"title\": \"Young Thug - Rich Nigga Shit [V2]\", \"artists\": \"(feat. <PERSON><PERSON> WRLD) (prod. <PERSON>'erre <PERSON> & Kanye <PERSON>)\", \"aliases\": [\"Amistad\"], \"description\": \"OG Filename: THUG x JUICEWRLD - RICH NIGGA SHIT (BAINZRUFF)\\nOG file of the released Young Thug and Juice WRLD song that reuses production from \\\"Amistad\\\".\", \"date\": 16660512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"436360d06262e285cb2300f9498fa516\", \"url\": \"https://api.pillowcase.su/api/download/436360d06262e285cb2300f9498fa516\", \"size\": \"5.1 MB\", \"duration\": 177.51}", "aliases": ["Amistad"], "size": "5.1 MB"}, {"id": "aliens-187", "name": "Aliens [V28]", "artists": [], "producers": ["Timbaland", "<PERSON>", "<PERSON>"], "notes": "OG Filename: Aliens New Drums 02.26.2019\nVersion of \"Aliens\" from February 2019, with different drums. We do not have an official bounce, however, we do have drum stems which leaked on June 27th, 2022.", "length": "", "fileDate": 16562880, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/1a63ec68ac16dcc03bfa94c6aa623f91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1a63ec68ac16dcc03bfa94c6aa623f91\", \"key\": \"Aliens\", \"title\": \"Aliens [V28]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: Aliens New Drums 02.26.2019\\nVersion of \\\"Aliens\\\" from February 2019, with different drums. We do not have an official bounce, however, we do have drum stems which leaked on June 27th, 2022.\", \"date\": 16562880, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"]}", "aliases": ["Space X"], "size": ""}, {"id": "alien-188", "name": "Alien [V30]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: 04.09.19 <PERSON><PERSON><PERSON> T \"Alien\" reference track. Has 1:30 of <PERSON><PERSON><PERSON> vocals which is the same exact amount of <PERSON><PERSON><PERSON> vocals on V1 with his verse. Was being sold on TheSource with the snippet being posted Sep 27th 2020. Another LQ snippet leaked Aug 27th 2024. <PERSON> would finally leak the song.", "length": "160.61", "fileDate": 17383680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3e36517d6d5927deba66eaa891850721", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3e36517d6d5927deba66eaa891850721\", \"key\": \"Alien\", \"title\": \"Alien [V30]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Space X\"], \"description\": \"OG Filename: 04.09.19 Pusha Ref\\nPusha T \\\"Alien\\\" reference track. Has 1:30 of <PERSON><PERSON><PERSON> vocals which is the same exact amount of <PERSON><PERSON><PERSON> vocals on V1 with his verse. Was being sold on TheSource with the snippet being posted Sep 27th 2020. Another LQ snippet leaked Aug 27th 2024. <PERSON> would finally leak the song.\", \"date\": 17383680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2e08f1480eee702d55861cbbd6154d9d\", \"url\": \"https://api.pillowcase.su/api/download/2e08f1480eee702d55861cbbd6154d9d\", \"size\": \"4.83 MB\", \"duration\": 160.61}", "aliases": ["Space X"], "size": "4.83 MB"}, {"id": "all-dreams-real-189", "name": "All Dreams Real [V13]", "artists": [], "producers": [], "notes": "OG Filename: All Dreams Real\nVersion of \"All Dreams Real\" from (likely) mid-2019. Contains a lot of new production.", "length": "120.82", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e84c3e815f9a066942253ffb7fca58fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e84c3e815f9a066942253ffb7fca58fc\", \"key\": \"All Dreams Real\", \"title\": \"All Dreams Real [V13]\", \"description\": \"OG Filename: All Dreams Real\\nVersion of \\\"All Dreams Real\\\" from (likely) mid-2019. Contains a lot of new production.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f442e53cbad6362039cb404d7e4a6309\", \"url\": \"https://api.pillowcase.su/api/download/f442e53cbad6362039cb404d7e4a6309\", \"size\": \"4.19 MB\", \"duration\": 120.82}", "aliases": [], "size": "4.19 MB"}, {"id": "amazing", "name": "✨ Amazing", "artists": ["Sunday Service Choir"], "producers": ["Skrillex"], "notes": "OG Filename: Amazing ref - Ant & Choir - 03.21.19\nMarch 2019-era track with <PERSON><PERSON><PERSON>x production, <PERSON><PERSON> vocals, and a \"choir\". Not confirmed to be Sunday Service Choir, but is most likely their vocals judging from the time period. Original snippet leaked November 18th, 2022.", "length": "212.76", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5974024d7f4ba26db77ca33a8a0b9023", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5974024d7f4ba26db77ca33a8a0b9023\", \"key\": \"Amazing\", \"title\": \"\\u2728 Amazing\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. Sunday Service Choir) (prod. <PERSON>krillex)\", \"description\": \"OG Filename: Amazing ref - Ant & Choir - 03.21.19\\nMarch 2019-era track with <PERSON><PERSON><PERSON><PERSON> production, <PERSON><PERSON> vocals, and a \\\"choir\\\". Not confirmed to be Sunday Service Choir, but is most likely their vocals judging from the time period. Original snippet leaked November 18th, 2022.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a239fdf9827eb81b08055991704a65be\", \"url\": \"https://api.pillowcase.su/api/download/a239fdf9827eb81b08055991704a65be\", \"size\": \"5.66 MB\", \"duration\": 212.76}", "aliases": [], "size": "5.66 MB"}, {"id": "brothers", "name": "Brothers [V17]", "artists": ["<PERSON>"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>", "Bink!"], "notes": "Has a different instrumental and also has <PERSON> along with a <PERSON><PERSON><PERSON> verse. Was uploaded to producer <PERSON>'s website.", "length": "260.13", "fileDate": 15619392, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/f378f8a22c0e13f62aa6a68fb8daadb7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f378f8a22c0e13f62aa6a68fb8daadb7\", \"key\": \"Brothers\", \"title\": \"Brothers [V17]\", \"artists\": \"(feat. <PERSON>) (prod. 7 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>k!)\", \"description\": \"Has a different instrumental and also has <PERSON> along with a <PERSON><PERSON><PERSON> verse. Was uploaded to producer Seven's website.\", \"date\": 15619392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"64f2e89fd7fa6dfdf577885cedac10b5\", \"url\": \"https://api.pillowcase.su/api/download/64f2e89fd7fa6dfdf577885cedac10b5\", \"size\": \"6.42 MB\", \"duration\": 260.13}", "aliases": [], "size": "6.42 MB"}, {"id": "brothers-192", "name": "Brothers [V18]", "artists": [], "producers": [], "notes": "Alternate version of \"Brothers\" with no feature, different drums and extra sample under the refrain. Played on a Consequence Instagram Live. Leaked as part of the Pressure groupbuy.", "length": "228.17", "fileDate": 17166816, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d925e6b005e7fa0358cbae08c477d864", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d925e6b005e7fa0358cbae08c477d864\", \"key\": \"Brothers\", \"title\": \"Brothers [V18]\", \"description\": \"Alternate version of \\\"Brothers\\\" with no feature, different drums and extra sample under the refrain. Played on a Consequence Instagram Live. Leaked as part of the Pressure groupbuy.\", \"date\": 17166816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d2d5540cbdcb7ff073b87c226cea1749\", \"url\": \"https://api.pillowcase.su/api/download/d2d5540cbdcb7ff073b87c226cea1749\", \"size\": \"5.91 MB\", \"duration\": 228.17}", "aliases": [], "size": "5.91 MB"}, {"id": "brothers-193", "name": "⭐ Brothers [V19]", "artists": ["<PERSON>"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>", "Bink!"], "notes": "Release-ready version that was posted on producer <PERSON><PERSON><PERSON>'s Instagram. According to <PERSON><PERSON>, it didn't release because the producers were fighting about royalties.", "length": "224.57", "fileDate": 15619392, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/14f3202388320e7d213e2d66f5cefe0f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/14f3202388320e7d213e2d66f5cefe0f\", \"key\": \"Brothers\", \"title\": \"\\u2b50 Brothers [V19]\", \"artists\": \"(feat. <PERSON>) (prod. 7 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>k!)\", \"description\": \"Release-ready version that was posted on producer <PERSON><PERSON><PERSON>'s Instagram. According to <PERSON><PERSON>, it didn't release because the producers were fighting about royalties.\", \"date\": 15619392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"710584b4d76d99c1d88534cbf77755f4\", \"url\": \"https://api.pillowcase.su/api/download/710584b4d76d99c1d88534cbf77755f4\", \"size\": \"5.85 MB\", \"duration\": 224.57}", "aliases": [], "size": "5.85 MB"}, {"id": "cash-to-burn-194", "name": "Cash To Burn [V8]", "artists": [], "producers": ["AllDay", "Timbaland", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "BoogzDaBeast"], "notes": "Version of \"Cash To Burn\" from June 2019 that is the open verse of the Styles P reference track.", "length": "56.06", "fileDate": 17170272, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/eeadb5664f838738e667192ef0821062", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eeadb5664f838738e667192ef0821062\", \"key\": \"Cash To Burn\", \"title\": \"Cash To Burn [V8]\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> & BoogzDaBeast)\", \"description\": \"Version of \\\"Cash To Burn\\\" from June 2019 that is the open verse of the Styles P reference track.\", \"date\": 17170272, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"31414e28f6ed7da68363a23ff2dc55a5\", \"url\": \"https://api.pillowcase.su/api/download/31414e28f6ed7da68363a23ff2dc55a5\", \"size\": \"3.15 MB\", \"duration\": 56.06}", "aliases": [], "size": "3.15 MB"}, {"id": "cash", "name": "⭐ Cash [V9]", "artists": ["<PERSON>"], "producers": ["AllDay", "Timbaland", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "BoogzDaBeast"], "notes": "OG FIlename: CASH NEW HORNS LATEST\nRecorded in February 2019 when <PERSON><PERSON><PERSON> linked up with <PERSON> and <PERSON>. <PERSON>'s verse is a reference for <PERSON><PERSON><PERSON>, and has more finished <PERSON><PERSON><PERSON> chorus. Full version leaked in February 2020.", "length": "149.98", "fileDate": 15805152, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/885e71e5cd918d999dc1e68337419008", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/885e71e5cd918d999dc1e68337419008\", \"key\": \"Cash\", \"title\": \"\\u2b50 Cash [V9]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> & Bo<PERSON>zDaBeast)\", \"aliases\": [\"Cash To Burn\"], \"description\": \"OG FIlename: CASH NEW HORNS LATEST\\nRecorded in February 2019 when <PERSON><PERSON><PERSON> linked up with <PERSON> and <PERSON>'s verse is a reference for <PERSON><PERSON><PERSON>, and has more finished <PERSON>ny<PERSON> chorus. Full version leaked in February 2020.\", \"date\": 15805152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cbf5c7ff78c90a7151fe4271b9d9c7dd\", \"url\": \"https://api.pillowcase.su/api/download/cbf5c7ff78c90a7151fe4271b9d9c7dd\", \"size\": \"4.66 MB\", \"duration\": 149.98}", "aliases": ["Cash To Burn"], "size": "4.66 MB"}, {"id": "cash-to-burn-196", "name": "Cash To Burn [V11]", "artists": ["<PERSON>"], "producers": ["AllDay", "Timbaland", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: Jam 4 (Cash to burn) (85.1) V2 BUJU\nVersion of \"Cash To Burn\" that samples Bu<PERSON>.", "length": "125.57", "fileDate": 16968960, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e6e6dc0d329b333a5b9f51bd5f8f8cfd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e6e6dc0d329b333a5b9f51bd5f8f8cfd\", \"key\": \"Cash To Burn\", \"title\": \"Cash To Burn [V11]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> & BoogzDaBeast)\", \"description\": \"OG Filename: Jam 4 (Cash to burn) (85.1) V2 BUJU\\nVersion of \\\"Cash To Burn\\\" that samples <PERSON>u<PERSON>.\", \"date\": 16968960, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"87d3564694750ae6c7915d423ea8cbfc\", \"url\": \"https://api.pillowcase.su/api/download/87d3564694750ae6c7915d423ea8cbfc\", \"size\": \"4.27 MB\", \"duration\": 125.57}", "aliases": [], "size": "4.27 MB"}, {"id": "cecilia-lost-little-things", "name": "<PERSON> Lost Little Things [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON> <PERSON> Lost Little Things_freestyle\nEarliest known version of what would eventually become \"The Brenda Song\". Assumed to be from 2019 due to how the mic sounds, later brought back in 2020. Samples a cover of \"Dear Prudence\" by The Beatles.", "length": "146.91", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/93c705cc0043f3744544344e77c2bbbc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/93c705cc0043f3744544344e77c2bbbc\", \"key\": \"<PERSON> Lost Little Things\", \"title\": \"<PERSON> Lost Little Things [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"The Brenda Song\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x <PERSON> <PERSON> <PERSON> Lost Little Things_freestyle\\nEarliest known version of what would eventually become \\\"The Brenda Song\\\". Assumed to be from 2019 due to how the mic sounds, later brought back in 2020. Samples a cover of \\\"Dear Prudence\\\" by The Beatles.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6027963e6ce05b8fef45908be4957f8e\", \"url\": \"https://api.pillowcase.su/api/download/6027963e6ce05b8fef45908be4957f8e\", \"size\": \"4.61 MB\", \"duration\": 146.91}", "aliases": ["The <PERSON>"], "size": "4.61 MB"}, {"id": "christ-198", "name": "Christ [V4]", "artists": [], "producers": ["<PERSON>", "<PERSON>", "E.VAX"], "notes": "OG Filename: <PERSON> (<PERSON> Version) 160 bpm - <PERSON> 190228\nVersion of \"Christ\" that was intended for release on the Stem Player.", "length": "116.96", "fileDate": 17340480, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/eab2a4f9f48415332e921e28cbaa8549", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eab2a4f9f48415332e921e28cbaa8549\", \"key\": \"Christ\", \"title\": \"Christ [V4]\", \"artists\": \"(ref. The WRLDFMS <PERSON>) (prod. <PERSON>, <PERSON> & E.VAX)\", \"description\": \"OG Filename: Christ (Evan Version) 160 bpm - <PERSON> Ref 190228\\nVersion of \\\"Christ\\\" that was intended for release on the Stem Player.\", \"date\": 17340480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a37d8bc52182fd822ea60d1c07c8b051\", \"url\": \"https://api.pillowcase.su/api/download/a37d8bc52182fd822ea60d1c07c8b051\", \"size\": \"4.13 MB\", \"duration\": 116.96}", "aliases": [], "size": "4.13 MB"}, {"id": "everything-we-need", "name": "Everything We Need [V24]", "artists": ["<PERSON><PERSON>", "Sunday Service Choir", "XXXTENTACION"], "producers": ["<PERSON>", "<PERSON>"], "notes": "2019 version of \"The Storm\", includes an earlier version of the \"Everything We Need\" hook and the Sunday Service Choir vocals. Beat uses V3 and V4 instrumental. XXXTENTACI<PERSON>'s vocals are briefly used but fade away quickly and don't include any of the explicit parts. Recorded February 2019.", "length": "136.99", "fileDate": 15805152, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/ae3d48e718aba5e5e5d9304007e759a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae3d48e718aba5e5e5d9304007e759a5\", \"key\": \"Everything We Need\", \"title\": \"Everything We Need [V24]\", \"artists\": \"(feat. <PERSON><PERSON>, Sunday Service Choir & XXXTENTACION) (prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Jeet\", \"The Storm\", \"We Begin\", \"XXX\", \"We Begin After The Storm Inside\"], \"description\": \"2019 version of \\\"The Storm\\\", includes an earlier version of the \\\"Everything We Need\\\" hook and the Sunday Service Choir vocals. Beat uses V3 and V4 instrumental. XXXTENTACI<PERSON>'s vocals are briefly used but fade away quickly and don't include any of the explicit parts. Recorded February 2019.\", \"date\": 15805152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0cb54fefa464e0e4b964b245b2fb19cc\", \"url\": \"https://api.pillowcase.su/api/download/0cb54fefa464e0e4b964b245b2fb19cc\", \"size\": \"4.45 MB\", \"duration\": 136.99}", "aliases": ["<PERSON>et", "The Storm", "We Begin", "XXX", "We Begin After The Storm Inside"], "size": "4.45 MB"}, {"id": "routine", "name": "Routine", "artists": [], "producers": ["FnZ", "RONNY J"], "notes": "OG Filename: <PERSON><PERSON> (Routine) - <PERSON><PERSON> Ref\nReference for <PERSON><PERSON><PERSON>, unknown if he ever recorded on this instrumental however. Leaked August 11, 2021.", "length": "207.34", "fileDate": 16286400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5b281a7069287eddc92d5fdba2335b54", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5b281a7069287eddc92d5fdba2335b54\", \"key\": \"Routine\", \"title\": \"Routine\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. FnZ & RONNY J)\", \"aliases\": [\"Guts\"], \"description\": \"OG Filename: Guts (Routine) - <PERSON><PERSON> Ref\\nReference for <PERSON><PERSON><PERSON>, unknown if he ever recorded on this instrumental however. Leaked August 11, 2021.\", \"date\": 16286400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7406eaf24c49c57ce636a1634b55b232\", \"url\": \"https://api.pillowcase.su/api/download/7406eaf24c49c57ce636a1634b55b232\", \"size\": \"5.57 MB\", \"duration\": 207.34}", "aliases": ["Guts"], "size": "5.57 MB"}, {"id": "how-ya-feel", "name": "How Ya Feel [V1]", "artists": [], "producers": [], "notes": "On a March 2019 Yandhi tracklist. Samples \"Vodopad\" by <PERSON><PERSON>. Snippet leaked on September 24th, 2023 as part of the 500 Days In UCLA documentary.", "length": "33.12", "fileDate": 16955136, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/bbf85528045869cacfe869605bd23413", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbf85528045869cacfe869605bd23413\", \"key\": \"How Ya Feel\", \"title\": \"How Ya Feel [V1]\", \"description\": \"On a March 2019 Yandhi tracklist. Samples \\\"Vodopad\\\" by <PERSON><PERSON>. Snippet leaked on September 24th, 2023 as part of the 500 Days In UCLA documentary.\", \"date\": 16955136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"391f869b6a8f3d2aa3941e470135d5d1\", \"url\": \"https://api.pillowcase.su/api/download/391f869b6a8f3d2aa3941e470135d5d1\", \"size\": \"2.79 MB\", \"duration\": 33.12}", "aliases": [], "size": "2.79 MB"}, {"id": "in-the-morning", "name": "✨ In The Morning [V1]", "artists": [], "producers": ["AllDay"], "notes": "OG Filename: Allday x Ye - in the morning freestyle\nOriginal mumble freestyle, likely from April 2019. Was sent to Sia. Was later reused in part for the DONDA 2020 version of \"I Know God Breathed On This\". Samples \"In the Morning\" by Soulsavers. Snippet leaked November 23rd, 2022.", "length": "349.73", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/235981c75e4eee8849f88a78d244241a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/235981c75e4eee8849f88a78d244241a\", \"key\": \"In The Morning\", \"title\": \"\\u2728 In The Morning [V1]\", \"artists\": \"(prod. AllDay)\", \"description\": \"OG Filename: Allday x Ye - in the morning freestyle\\nOriginal mumble freestyle, likely from April 2019. Was sent to Sia. Was later reused in part for the DONDA 2020 version of \\\"I Know God Breathed On This\\\". <PERSON><PERSON> \\\"In the Morning\\\" by Soulsavers. Snippet leaked November 23rd, 2022.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d787bbfef8ccd51cb85f15b6b2e8e281\", \"url\": \"https://api.pillowcase.su/api/download/d787bbfef8ccd51cb85f15b6b2e8e281\", \"size\": \"7.85 MB\", \"duration\": 349.73}", "aliases": [], "size": "7.85 MB"}, {"id": "law-of-attraction-203", "name": "Law Of Attraction [V7]", "artists": [], "producers": ["DRTWRK", "BoogzDaBeast", "<PERSON><PERSON>"], "notes": "OG Filename: Law Of Attraction <PERSON><PERSON> ref - 03.16.19\nVersion of \"Law of Attraction\" with <PERSON><PERSON> on the intro and hook, and lyrics rewritten from <PERSON><PERSON>' version. Has some <PERSON><PERSON><PERSON> background vocals. Recorded in mid-March 2019.", "length": "123.29", "fileDate": 16032384, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cc9935568ec3c9c0efc1c45df50150b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc9935568ec3c9c0efc1c45df50150b7\", \"key\": \"Law Of Attraction\", \"title\": \"Law Of Attraction [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>, BoogzDaBeast & Ant Clemons)\", \"aliases\": [\"Bad Taste\", \"Use This Gospel\", \"Breastplate Of Right\", \"Grab Your Armour For Protection\", \"Breastplate Of Righteousness\", \"You Don't Know\"], \"description\": \"OG Filename: Law Of Attraction Dua Lipa ref - 03.16.19\\nVersion of \\\"Law of Attraction\\\" with <PERSON><PERSON> on the intro and hook, and lyrics rewritten from <PERSON><PERSON>' version. Has some Kanye background vocals. Recorded in mid-March 2019.\", \"date\": 16032384, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c36c4e1023964d552436cdd3b0f6449\", \"url\": \"https://api.pillowcase.su/api/download/2c36c4e1023964d552436cdd3b0f6449\", \"size\": \"4.23 MB\", \"duration\": 123.29}", "aliases": ["Bad Taste", "Use This Gospel", "Breastplate Of Right", "Grab Your Armour For Protection", "Breastplate Of Righteousness", "You Don't Know"], "size": "4.23 MB"}, {"id": "life<PERSON><PERSON><PERSON>", "name": "lifeoftheloop [V1]", "artists": [], "producers": ["AllDay"], "notes": "OG Filename: Allday x Ye - <PERSON><PERSON>heloop freestyle\nThe original April 2019 freestyle for \"Life Of The Party\", first said to exist by Waterfalls. Recorded in the same session as \"In the Morning\" and other AllDay and BoogzDaBeast freestyles. Freestyle vocals and sample loop leaked as part of the Twilite Tone \"Life of the Party\" session on September 19, 2023. Linked is an bounce using these stems.", "length": "169.46", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/702e06efbdd640cd93ee7c000d3ba0b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/702e06efbdd640cd93ee7c000d3ba0b7\", \"key\": \"lifeoftheloop\", \"title\": \"lifeoftheloop [V1]\", \"artists\": \"(prod. AllDay)\", \"aliases\": [\"Life Of The Party\"], \"description\": \"OG Filename: Allday x Ye - lifeoftheloop freestyle\\nThe original April 2019 freestyle for \\\"Life Of The Party\\\", first said to exist by Waterfalls. Recorded in the same session as \\\"In the Morning\\\" and other AllDay and BoogzDaBeast freestyles. Freestyle vocals and sample loop leaked as part of the Twilite Tone \\\"Life of the Party\\\" session on September 19, 2023. Linked is an bounce using these stems.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9aba9835bb2b4000d13800efcba90aa2\", \"url\": \"https://api.pillowcase.su/api/download/9aba9835bb2b4000d13800efcba90aa2\", \"size\": \"4.97 MB\", \"duration\": 169.46}", "aliases": ["Life Of The Party"], "size": "4.97 MB"}, {"id": "linda-perhacs", "name": "✨ <PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON> <PERSON> freestyle\nRough freestyle over a sample, made in the same April session as \"In The Morning\" & \"Soul Children\". Samples \"Hey, Who Really Cares\" by <PERSON>. Falsely labeled as \"Hostage\" when the file first leaked, before the OG filename was made public. Leaked on September 11th, 2023.", "length": "153.6", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e8a6ca316865f4194b1c0089d6919ccc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8a6ca316865f4194b1c0089d6919ccc\", \"key\": \"<PERSON>\", \"title\": \"\\u2728 <PERSON>\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aB<PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON> <PERSON> freestyle\\nRough freestyle over a sample, made in the same April session as \\\"In The Morning\\\" & \\\"Soul Children\\\". <PERSON><PERSON> \\\"Hey, Who Really Cares\\\" by <PERSON>. Falsely labeled as \\\"Hostage\\\" when the file first leaked, before the OG filename was made public. Leaked on September 11th, 2023.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"567bdca0c4c1a0ce8bcd4efa108bfa51\", \"url\": \"https://api.pillowcase.su/api/download/567bdca0c4c1a0ce8bcd4efa108bfa51\", \"size\": \"4.71 MB\", \"duration\": 153.6}", "aliases": [], "size": "4.71 MB"}, {"id": "new-body-206", "name": "New Body [V23]", "artists": ["<PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["RONNY J"], "notes": "Consequence reference track for \"New Body\". Has the same <PERSON> $ign and <PERSON><PERSON> vocals as earlier leaked versions. <PERSON><PERSON> vocals. Consequence has vocals from 0:29s to 1:20s. Put up for sale by <PERSON> on TheSource on November 19, 2020. Leaked on October 29, 2023.", "length": "253.51", "fileDate": 16057440, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cf738333d27f23defbf603f2c497ae7f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf738333d27f23defbf603f2c497ae7f\", \"key\": \"New Body\", \"title\": \"New Body [V23]\", \"artists\": \"(ref. Consequence) (feat. <PERSON><PERSON> & <PERSON> $ign) (prod. RONNY J)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"Consequence reference track for \\\"New Body\\\". Has the same Ty Dolla $ign and <PERSON><PERSON> vocals as earlier leaked versions. <PERSON><PERSON> vocals. Consequence has vocals from 0:29s to 1:20s. Put up for sale by <PERSON> on TheSource on November 19, 2020. Leaked on October 29, 2023.\", \"date\": 16057440, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9668c60fa3282e06a27bda0ec9278f22\", \"url\": \"https://api.pillowcase.su/api/download/9668c60fa3282e06a27bda0ec9278f22\", \"size\": \"6.32 MB\", \"duration\": 253.51}", "aliases": ["Can't Wait To See Your New Body"], "size": "6.32 MB"}, {"id": "ny-dreams", "name": "NY Dreams", "artists": [], "producers": ["AllDay"], "notes": "OG Filename: Allday x Ye - Nydreams freestyle\n2019 Yandhi era freestyle produced by Allday. Snippet leaked October 14th, 2024.", "length": "5.78", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/67da59daf06d140109cdd24ebd3013ec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/67da59daf06d140109cdd24ebd3013ec\", \"key\": \"NY Dreams\", \"title\": \"NY Dreams\", \"artists\": \"(prod. AllDay)\", \"description\": \"OG Filename: Allday x Ye - Nydreams freestyle\\n2019 Yandhi era freestyle produced by Allday. Snippet leaked October 14th, 2024.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4133a1dd605d8c6538a582fb5cc76814\", \"url\": \"https://api.pillowcase.su/api/download/4133a1dd605d8c6538a582fb5cc76814\", \"size\": \"2.35 MB\", \"duration\": 5.78}", "aliases": [], "size": "2.35 MB"}, {"id": "on-god", "name": "✨ On God [V2]", "artists": [], "producers": ["BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: ON GOD - <PERSON>a Lipa-grid ref 03.28.19\nSolo Du<PERSON> reference over the Yandhi version of the song, which has no drums. Recorded after it was reported by The Sun that <PERSON> was looking to work with <PERSON><PERSON>.", "length": "124.99", "fileDate": 16032384, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a9602241ff925821c2d370c6d449e81e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a9602241ff925821c2d370c6d449e81e\", \"key\": \"On God\", \"title\": \"\\u2728 On God [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON>zDaBeast & <PERSON>)\", \"description\": \"OG Filename: ON GOD - Dua Lipa-grid ref 03.28.19\\nSolo Dua Lipa reference over the Yandhi version of the song, which has no drums. Recorded after it was reported by The Sun that <PERSON> was looking to work with <PERSON><PERSON>.\", \"date\": 16032384, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"71d72f720094976c1ca1e963ef92d50a\", \"url\": \"https://api.pillowcase.su/api/download/71d72f720094976c1ca1e963ef92d50a\", \"size\": \"4.26 MB\", \"duration\": 124.99}", "aliases": [], "size": "4.26 MB"}, {"id": "on-god-209", "name": "On God [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "BoogzDaBeast", "<PERSON>"], "notes": "iPhone vocals demo recorded over the beat, reworked by <PERSON><PERSON><PERSON><PERSON>. Vocals start at 0:49s till the end. Put up for sale by <PERSON> on TheSource.", "length": "14.21", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/500239b98507c667ff0b8d4c55fa290b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/500239b98507c667ff0b8d4c55fa290b\", \"key\": \"On God\", \"title\": \"On God [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"iPhone vocals demo recorded over the beat, reworked by <PERSON><PERSON><PERSON><PERSON>. Vocals start at 0:49s till the end. Put up for sale by <PERSON> on TheSource.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4031beb1d22cffd33b00f44b90cf7081\", \"url\": \"https://api.pillowcase.su/api/download/4031beb1d22cffd33b00f44b90cf7081\", \"size\": \"532 kB\", \"duration\": 14.21}", "aliases": [], "size": "532 kB"}, {"id": "raimonds-pauls", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON> freestyle\nRough freestyle over a <PERSON><PERSON><PERSON> sample.", "length": "145.2", "fileDate": 17016480, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8ecf9ab2157202a891f55e7df51b1e8c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8ecf9ab2157202a891f55e7df51b1e8c\", \"key\": \"<PERSON><PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON>\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>B<PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x <PERSON> <PERSON> <PERSON><PERSON><PERSON> freestyle\\nRough freestyle over a Raimonds Pauls sample.\", \"date\": 17016480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"100ac041d68bd5136c688afc46a9eb8d\", \"url\": \"https://api.pillowcase.su/api/download/100ac041d68bd5136c688afc46a9eb8d\", \"size\": \"4.58 MB\", \"duration\": 145.2}", "aliases": [], "size": "4.58 MB"}, {"id": "selah", "name": "<PERSON><PERSON> [V22]", "artists": [], "producers": ["E.VAX"], "notes": "Version of \"Selah\" from February 2019. This version is the same as the March version but without the choir.", "length": "199.38", "fileDate": 16888608, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/dbcda6570e871fe03ac008f8cc29c355", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dbcda6570e871fe03ac008f8cc29c355\", \"key\": \"<PERSON>lah\", \"title\": \"<PERSON><PERSON> [V22]\", \"artists\": \"(prod. E.VAX)\", \"aliases\": [\"<PERSON><PERSON><PERSON>\", \"The Chakra\", \"Chakra\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"Version of \\\"Selah\\\" from February 2019. This version is the same as the March version but without the choir.\", \"date\": 16888608, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b4f31d0fb87589b2b06e05c076a6cab8\", \"url\": \"https://api.pillowcase.su/api/download/b4f31d0fb87589b2b06e05c076a6cab8\", \"size\": \"5.45 MB\", \"duration\": 199.38}", "aliases": ["Chakras", "The Chakra", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "5.45 MB"}, {"id": "selah-212", "name": "<PERSON><PERSON> [V23]", "artists": ["Sunday Service Choir"], "producers": ["E.VAX"], "notes": "Version of \"Selah\" from March 2019. Contains choir throughout and alternate production.", "length": "223.39", "fileDate": 16888608, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/2549946e2fc53ec735d5e515b2b7efb2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2549946e2fc53ec735d5e515b2b7efb2\", \"key\": \"<PERSON>lah\", \"title\": \"<PERSON><PERSON> [V23]\", \"artists\": \"(feat. Sunday Service Choir) (prod. E.VAX)\", \"aliases\": [\"Chakra<PERSON>\", \"The Chakra\", \"Chakra\", \"I Love It (Remix)\", \"Good Morning\"], \"description\": \"Version of \\\"Selah\\\" from March 2019. Contains choir throughout and alternate production.\", \"date\": 16888608, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"99b057f20bd945b0065c87d48ac465df\", \"url\": \"https://api.pillowcase.su/api/download/99b057f20bd945b0065c87d48ac465df\", \"size\": \"5.83 MB\", \"duration\": 223.39}", "aliases": ["Chakras", "The Chakra", "Chakra", "I Love It (Remix)", "Good Morning"], "size": "5.83 MB"}, {"id": "sever", "name": "Sever", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: <PERSON> x <PERSON> - sever freestyle\nFreestyle titled \"Sever.\" Produced by E.VAX.", "length": "71.86", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5a35008a7993c0112a3b485e7001c5d0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5a35008a7993c0112a3b485e7001c5d0\", \"key\": \"Sever\", \"title\": \"Sever\", \"artists\": \"(prod. E.VAX)\", \"description\": \"OG Filename: <PERSON> x <PERSON> sever freestyle\\nFreestyle titled \\\"Sever.\\\" Produced by E.VAX.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"573bf03f39551cae766e50846168e93c\", \"url\": \"https://api.pillowcase.su/api/download/573bf03f39551cae766e50846168e93c\", \"size\": \"3.41 MB\", \"duration\": 71.86}", "aliases": [], "size": "3.41 MB"}, {"id": "shoot-it", "name": "Shoot It ", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON>_12pm_Shoot it - YE + Ref_02.17.19\nExtremely rough and short track with only 2 lines of <PERSON><PERSON><PERSON> mumble vocals. Leaked by leakth.is user Bao.", "length": "88.61", "fileDate": 15725664, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/37c597be28ca319e487f700a05af8c7b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37c597be28ca319e487f700a05af8c7b\", \"key\": \"Shoot It\", \"title\": \"Shoot It \", \"artists\": \"(ref. The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON> <PERSON><PERSON>_12pm_Shoot it - YE + Ref_02.17.19\\nExtremely rough and short track with only 2 lines of <PERSON><PERSON><PERSON> mumble vocals. Leaked by leakth.is user Bao.\", \"date\": 15725664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1d7d31c713b996d46b8a982f78cae7bf\", \"url\": \"https://api.pillowcase.su/api/download/1d7d31c713b996d46b8a982f78cae7bf\", \"size\": \"3.67 MB\", \"duration\": 88.61}", "aliases": [], "size": "3.67 MB"}, {"id": "soul-children", "name": "✨ Soul Children", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON>z x Ye - Soul Children freestyle\nRough freestyle produced by <PERSON><PERSON><PERSON>. Could possibly be \"Thank You\" on one of the July 2020 DONDA tracklist. Samples \"Midnight Sunshine\" by The Soul Children. Original snippet leaked on May 13th, 2023.", "length": "203.81", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/f652c7a23fee979c214bcdc6409292a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f652c7a23fee979c214bcdc6409292a0\", \"key\": \"Soul Children\", \"title\": \"\\u2728 Soul Children\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>B<PERSON><PERSON>)\", \"aliases\": [\"Thank You\"], \"description\": \"OG Filename: Boogz x Ye - Soul Children freestyle\\nRough freestyle produced by <PERSON><PERSON><PERSON>. Could possibly be \\\"Thank You\\\" on one of the July 2020 DONDA tracklist. Samples \\\"Midnight Sunshine\\\" by The Soul Children. Original snippet leaked on May 13th, 2023.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e672ec3358d9b2d99f8c9b2ebdaa41f7\", \"url\": \"https://api.pillowcase.su/api/download/e672ec3358d9b2d99f8c9b2ebdaa41f7\", \"size\": \"5.52 MB\", \"duration\": 203.81}", "aliases": ["Thank You"], "size": "5.52 MB"}, {"id": "sunday", "name": "Sunday [V1]", "artists": [], "producers": ["Timbaland", "AllDay"], "notes": "OG Filename: New A+F Beat Ref\nOriginal \"Closed On Sunday\" freestyle, from April 2019. <PERSON> explained that after they found the \"Martín Fierro\" sample loop, <PERSON><PERSON><PERSON> \"recorded his raps on it, and then we replaced the guitars and recorded the vocals with <PERSON><PERSON><PERSON>'s gospel chorus\".", "length": "27.1", "fileDate": 16887744, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/9b8c54189853798758e1d963614b8f69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9b8c54189853798758e1d963614b8f69\", \"key\": \"Sunday\", \"title\": \"Sunday [V1]\", \"artists\": \"(prod. Timbaland & AllDay)\", \"aliases\": [\"Closed on Sunday\"], \"description\": \"OG Filename: New A+F Beat Ref\\nOriginal \\\"Closed On Sunday\\\" freestyle, from April 2019. <PERSON> explained that after they found the \\\"Mart\\u00edn Fierro\\\" sample loop, <PERSON><PERSON><PERSON> \\\"recorded his raps on it, and then we replaced the guitars and recorded the vocals with <PERSON><PERSON><PERSON>'s gospel chorus\\\".\", \"date\": 16887744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2abc6172379955c449e64c60ef32ca53\", \"url\": \"https://api.pillowcase.su/api/download/2abc6172379955c449e64c60ef32ca53\", \"size\": \"2.69 MB\", \"duration\": 27.1}", "aliases": ["Closed on Sunday"], "size": "2.69 MB"}, {"id": "that-was-a-good-one", "name": "That Was A Good One [V2]", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: that was a good one\nRough 2019 E.VAX cut-down freestyle, mostly mumble. Leaked after a groupbuy.", "length": "176.09", "fileDate": 16771104, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3c708612a0c1efc697cb948f38eaf6de", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3c708612a0c1efc697cb948f38eaf6de\", \"key\": \"That Was A Good One\", \"title\": \"That Was A Good One [V2]\", \"artists\": \"(prod. E.VAX)\", \"description\": \"OG Filename: that was a good one\\nRough 2019 E.VAX cut-down freestyle, mostly mumble. Leaked after a groupbuy.\", \"date\": 16771104, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0dae1c81295f58b4945b5c068528e142\", \"url\": \"https://api.pillowcase.su/api/download/0dae1c81295f58b4945b5c068528e142\", \"size\": \"5.07 MB\", \"duration\": 176.09}", "aliases": [], "size": "5.07 MB"}, {"id": "the-garden", "name": "The Garden [V17]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden - Chor Piano loop w Slim 02.28.19\nFirst known version of \"The Garden\" from 2019. Keeps some elements of the late 2018 versions, such as <PERSON>'s vocals, but includes a lot of changes, including: adding the Sunday Service Choir, removing most instrumentation, and cutting <PERSON> $ign's vocals.", "length": "154.29", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e8caacaa8ad7e6df302bf19c459183f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8caacaa8ad7e6df302bf19c459183f2\", \"key\": \"The Garden\", \"title\": \"The Garden [V17]\", \"artists\": \"(feat. <PERSON>, Sunday Service Choir & Ty Dolla $ign)\", \"description\": \"OG Filename: The Garden - Chor Piano loop w Slim 02.28.19\\nFirst known version of \\\"The Garden\\\" from 2019. Keeps some elements of the late 2018 versions, such as <PERSON>'s vocals, but includes a lot of changes, including: adding the Sunday Service Choir, removing most instrumentation, and cutting <PERSON> <PERSON> $ign's vocals.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"06615c0ca4c57ee8a44de7c3beeddf80\", \"url\": \"https://api.pillowcase.su/api/download/06615c0ca4c57ee8a44de7c3beeddf80\", \"size\": \"4.73 MB\", \"duration\": 154.29}", "aliases": [], "size": "4.73 MB"}, {"id": "the-garden-219", "name": "The Garden [V18]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden - Choir - loop - shortened\nEdit of the February 28th \"The Garden\" version which shortens the loop of the choirs vocals.", "length": "120", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/dd1f41ea97c72eda102974c6eb6a451a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd1f41ea97c72eda102974c6eb6a451a\", \"key\": \"The Garden\", \"title\": \"The Garden [V18]\", \"artists\": \"(feat. <PERSON>, <PERSON> Service Choir & Ty Dolla $ign)\", \"description\": \"OG Filename: The Garden - Choir - loop - shortened\\nEdit of the February 28th \\\"The Garden\\\" version which shortens the loop of the choirs vocals.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9874a49941093b64c7d67e627559d7a5\", \"url\": \"https://api.pillowcase.su/api/download/9874a49941093b64c7d67e627559d7a5\", \"size\": \"4.18 MB\", \"duration\": 120}", "aliases": [], "size": "4.18 MB"}, {"id": "the-garden-220", "name": "The Garden [V19]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden - Choir Loop 03.04.19\n Track 2 on <PERSON>'s August tracklist and a August 30th tracklist. Contains only <PERSON> and Sunday Service Choir vocals, with a little bit of <PERSON>gn's vocals being looped. Original snippet leaked June 16th, 2023.", "length": "120", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5c353e2b80d2d6baee54c43c2e584ea7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5c353e2b80d2d6baee54c43c2e584ea7\", \"key\": \"The Garden\", \"title\": \"The Garden [V19]\", \"artists\": \"(feat. <PERSON>, Sunday Service Choir & Ty <PERSON> $ign)\", \"description\": \"OG Filename: The Garden - Choir Loop 03.04.19\\n Track 2 on <PERSON>'s August tracklist and a August 30th tracklist. Contains only <PERSON> and Sunday Service Choir vocals, with a little bit of <PERSON> $ign's vocals being looped. Original snippet leaked June 16th, 2023.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"21fea3e2a00dcbb1e77a686565213fe2\", \"url\": \"https://api.pillowcase.su/api/download/21fea3e2a00dcbb1e77a686565213fe2\", \"size\": \"4.18 MB\", \"duration\": 120}", "aliases": [], "size": "4.18 MB"}, {"id": "the-garden-221", "name": "The Garden [V20]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden - Choir - loop - extended bass\nA different mix of the March 4th, 2019 \"The Garden\" version, adding more bass to the song.", "length": "120", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0a0d9f3b40fe2f7fc531ff2fecb8c8bf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a0d9f3b40fe2f7fc531ff2fecb8c8bf\", \"key\": \"The Garden\", \"title\": \"The Garden [V20]\", \"artists\": \"(feat. <PERSON>, <PERSON> Service Choir & Ty Dolla $ign)\", \"description\": \"OG Filename: The Garden - Choir - loop - extended bass\\nA different mix of the March 4th, 2019 \\\"The Garden\\\" version, adding more bass to the song.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d9dd01629b1c52d4a2a6f42da47c8c49\", \"url\": \"https://api.pillowcase.su/api/download/d9dd01629b1c52d4a2a6f42da47c8c49\", \"size\": \"4.18 MB\", \"duration\": 120}", "aliases": [], "size": "4.18 MB"}, {"id": "the-garden-222", "name": "The Garden [V21]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden - Choir - loop - muted bass\nA different mix of the March 4th, 2019 \"The Garden\" version, removing all bass from the song.", "length": "120", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c8ef8381943d6428f081c0797e07a86c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8ef8381943d6428f081c0797e07a86c\", \"key\": \"The Garden\", \"title\": \"The Garden [V21]\", \"artists\": \"(feat. <PERSON>, <PERSON> Service Choir & Ty Dolla $ign)\", \"description\": \"OG Filename: The Garden - Choir - loop - muted bass\\nA different mix of the March 4th, 2019 \\\"The Garden\\\" version, removing all bass from the song.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fe21861a5ca8c713618404a91364a04e\", \"url\": \"https://api.pillowcase.su/api/download/fe21861a5ca8c713618404a91364a04e\", \"size\": \"4.18 MB\", \"duration\": 120}", "aliases": [], "size": "4.18 MB"}, {"id": "the-garden-223", "name": "The Garden [V22]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden - shorter loop new bass - 03.19.19\nUpdated take of the March \"The Garden\" version, reworking the bass seen on the March 4th version and shortening the length of the song.", "length": "85.71", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/2c523d350d02a57920954fe10ab8ad5c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2c523d350d02a57920954fe10ab8ad5c\", \"key\": \"The Garden\", \"title\": \"The Garden [V22]\", \"artists\": \"(feat. <PERSON>, <PERSON> Service Choir & Ty Dolla $ign)\", \"description\": \"OG Filename: The Garden - shorter loop new bass - 03.19.19\\nUpdated take of the March \\\"The Garden\\\" version, reworking the bass seen on the March 4th version and shortening the length of the song.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5139d3f23fa849b83e32163c20786c73\", \"url\": \"https://api.pillowcase.su/api/download/5139d3f23fa849b83e32163c20786c73\", \"size\": \"3.63 MB\", \"duration\": 85.71}", "aliases": [], "size": "3.63 MB"}, {"id": "the-garden-224", "name": "The Garden [V23]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "OG Filename: The Garden 100 - 03.28.19\nLater version of the March 19th \"The Garden\" version, decreasing the BPM of the song to 100.", "length": "96", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d136af25c01e85e5ca61681662b2b38d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d136af25c01e85e5ca61681662b2b38d\", \"key\": \"The Garden\", \"title\": \"The Garden [V23]\", \"artists\": \"(feat. <PERSON>, <PERSON> Service Choir & Ty Dolla $ign)\", \"description\": \"OG Filename: The Garden 100 - 03.28.19\\nLater version of the March 19th \\\"The Garden\\\" version, decreasing the BPM of the song to 100.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d78cc17b06b9b6df4be4f1a1a5b4eaea\", \"url\": \"https://api.pillowcase.su/api/download/d78cc17b06b9b6df4be4f1a1a5b4eaea\", \"size\": \"3.79 MB\", \"duration\": 96}", "aliases": [], "size": "3.79 MB"}, {"id": "the-garden-225", "name": "The Garden [V24]", "artists": ["<PERSON>", "Sunday Service Choir", "Ty Dolla $ign"], "producers": [], "notes": "Version of \"The Garden\" intended for release on the Stem Player.", "length": "86.23", "fileDate": 17339616, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a0721f0f866154ed113fbf68149ada91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0721f0f866154ed113fbf68149ada91\", \"key\": \"The Garden\", \"title\": \"The Garden [V24]\", \"artists\": \"(feat. <PERSON>, <PERSON> Service Choir & Ty Dolla $ign)\", \"description\": \"Version of \\\"The Garden\\\" intended for release on the Stem Player.\", \"date\": 17339616, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7e86c997228bfcf404fed3755a43554c\", \"url\": \"https://api.pillowcase.su/api/download/7e86c997228bfcf404fed3755a43554c\", \"size\": \"1.67 MB\", \"duration\": 86.23}", "aliases": [], "size": "1.67 MB"}, {"id": "to-the-light", "name": "To The Light [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> and <PERSON> Piano Jam\nFirst version of \"To The Light\", recorded as a freestyle. Some of the Kanye vocals from this version can be heard in the outro of the <PERSON> version of the song. Original snippet leaked July 21st, 2020, with two more leaking May 17th, 2024. North can be heard at 1:13.", "length": "161.35", "fileDate": 17159904, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/539ad69dbf8694dc56178da003da850d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/539ad69dbf8694dc56178da003da850d\", \"key\": \"To The Light\", \"title\": \"To The Light [V1]\", \"aliases\": [\"I Go To The Light\", \"The Light\"], \"description\": \"OG Filename: KW and Dream Piano Jam\\nFirst version of \\\"To The Light\\\", recorded as a freestyle. Some of the <PERSON><PERSON><PERSON> vocals from this version can be heard in the outro of the <PERSON> version of the song. Original snippet leaked July 21st, 2020, with two more leaking May 17th, 2024. North can be heard at 1:13.\", \"date\": 17159904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"05ba07aed2b3ae4a7db20706d6bfaf05\", \"url\": \"https://api.pillowcase.su/api/download/05ba07aed2b3ae4a7db20706d6bfaf05\", \"size\": \"4.84 MB\", \"duration\": 161.35}", "aliases": ["I Go To The Light", "The Light"], "size": "4.84 MB"}, {"id": "the-light", "name": "The Light [V2]", "artists": [], "producers": [], "notes": "OG Filename: The Light - 02.25.19 slim fix\nVersion of \"To The Light\" with <PERSON> reference vocals. The song was found in a Kano database in 2022, as there was plans to use it on a commercial for the Clear Stem Player on August 25th, and later to be released via the Stem Player, listed as a JESUS IS KING track. Likely the same version used for future Yandhi and DONDA tracklists because of this very reason. Leaked after the JESUS IS KING Bundle groupbuy.", "length": "132.98", "fileDate": 16888608, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/9f769b1a245db47695f12041bfe139bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9f769b1a245db47695f12041bfe139bb\", \"key\": \"The Light\", \"title\": \"The Light [V2]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"I Go To The Light\", \"To The Light\"], \"description\": \"OG Filename: The Light - 02.25.19 slim fix\\nVersion of \\\"To The Light\\\" with Slim reference vocals. The song was found in a Kano database in 2022, as there was plans to use it on a commercial for the Clear Stem Player on August 25th, and later to be released via the Stem Player, listed as a JESUS IS KING track. Likely the same version used for future Yandhi and DONDA tracklists because of this very reason. Leaked after the JESUS IS KING Bundle groupbuy.\", \"date\": 16888608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9f26497f63c0b061a6e6a9ae3da6a81e\", \"url\": \"https://api.pillowcase.su/api/download/9f26497f63c0b061a6e6a9ae3da6a81e\", \"size\": \"4.38 MB\", \"duration\": 132.98}", "aliases": ["I Go To The Light", "To The Light"], "size": "4.38 MB"}, {"id": "unhomme", "name": "Unhomme [V1]", "artists": [], "producers": ["Allday"], "notes": "OG Filename: <PERSON><PERSON> x Ye - Unhomme Freestyle\nOriginal \"Baptized\" freestyle, later cut-down into verses for JESUS IS KING-era files. Likely from around April 2019. Instrumental leaked December 12th, 2024.", "length": "151.8", "fileDate": 17339616, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a34f5db7278a0149ab6a3c8d307f8adc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a34f5db7278a0149ab6a3c8d307f8adc\", \"key\": \"Unhomme\", \"title\": \"Unhomme [V1]\", \"artists\": \"(prod. Allday)\", \"aliases\": [\"Baptized\", \"Every Knee Shall Bow\", \"Jesus Is Lord\"], \"description\": \"OG Filename: Allday x Ye - Unhomme Freestyle\\nOriginal \\\"Baptized\\\" freestyle, later cut-down into verses for JESUS IS KING-era files. Likely from around April 2019. Instrumental leaked December 12th, 2024.\", \"date\": 17339616, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e239c91aef1c956ca2102d4f6cd706df\", \"url\": \"https://api.pillowcase.su/api/download/e239c91aef1c956ca2102d4f6cd706df\", \"size\": \"4.69 MB\", \"duration\": 151.8}", "aliases": ["Baptized", "Every Knee Shall Bow", "Jesus Is Lord"], "size": "4.69 MB"}, {"id": "water", "name": "Water [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON><PERSON> x <PERSON> <PERSON> <PERSON> Hack Water_freestyle\n A demo with <PERSON><PERSON><PERSON> on the instrumental for 8 minutes. Seen on a file list sent to Sia for JESUS IS KING. A version of this was performed at Coachella back in April 2019.", "length": "472.54", "fileDate": 16126560, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/8e022bd54d28b1f22b34123d22bf4c68", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8e022bd54d28b1f22b34123d22bf4c68\", \"key\": \"Water\", \"title\": \"Water [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x <PERSON> <PERSON> <PERSON>ck Water_freestyle\\n A demo with <PERSON><PERSON><PERSON>sty<PERSON> on the instrumental for 8 minutes. Seen on a file list sent to Sia for JESUS IS KING. A version of this was performed at Coachella back in April 2019.\", \"date\": 16126560, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e1d996f475014403afac137eac96fc92\", \"url\": \"https://api.pillowcase.su/api/download/e1d996f475014403afac137eac96fc92\", \"size\": \"9.82 MB\", \"duration\": 472.54}", "aliases": [], "size": "9.82 MB"}, {"id": "water-230", "name": "Water [V2]", "artists": [], "producers": ["BoogzDaBeast", "ROBOTSCOTT"], "notes": "OG Filename: Water KW ANT robot\nSnippets posted with clips of a potential scrapped music video with an early version of \"Water\" in December 2019. Has a solo <PERSON><PERSON> intro with no Sunday Service Choir. Confirmed by <PERSON><PERSON> that he made this reference track the Tuesday before <PERSON><PERSON> on the And The Writer Is podcast. CDQ snippet leaked August 23rd, 2021.", "length": "476.98", "fileDate": 16580160, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4f2d577aba58101336c2f1d4e5d171d4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4f2d577aba58101336c2f1d4e5d171d4\", \"key\": \"Water\", \"title\": \"Water [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast & ROBOTSCOTT)\", \"description\": \"OG Filename: Water KW ANT robot\\nSnippets posted with clips of a potential scrapped music video with an early version of \\\"Water\\\" in December 2019. Has a solo An<PERSON> Clemons intro with no Sunday Service Choir. Confirmed by <PERSON><PERSON> that he made this reference track the Tuesday before Coachella on the And The Writer Is podcast. CDQ snippet leaked August 23rd, 2021.\", \"date\": 16580160, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8e8f00164c58f9b9bd9ca3f33d45fa54\", \"url\": \"https://api.pillowcase.su/api/download/8e8f00164c58f9b9bd9ca3f33d45fa54\", \"size\": \"9.89 MB\", \"duration\": 476.98}", "aliases": [], "size": "9.89 MB"}, {"id": "water-231", "name": "Water [V3]", "artists": [], "producers": ["BoogzDaBeast", "Timbaland"], "notes": "OG Filename: Water New Lyrics 3rd Verse & Vamp\nWriters demo for \"Water\", recorded a couple days before Coachella.", "length": "101.7", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "Low Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/9de2fa0d46f9a86228f7685a858edbd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9de2fa0d46f9a86228f7685a858edbd7\", \"key\": \"Water\", \"title\": \"Water [V3]\", \"artists\": \"(ref. <PERSON><PERSON>, <PERSON> & The WRLDFMS <PERSON>) (prod. BoogzDaBeast & Timbaland)\", \"description\": \"OG Filename: Water New Lyrics 3rd Verse & Vamp\\nWriters demo for \\\"Water\\\", recorded a couple days before Coachella.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"2578eac6115f27fe02371eb9475c555d\", \"url\": \"https://api.pillowcase.su/api/download/2578eac6115f27fe02371eb9475c555d\", \"size\": \"3.07 MB\", \"duration\": 101.7}", "aliases": [], "size": "3.07 MB"}, {"id": "-232", "name": "???", "artists": [], "producers": ["KAYTRANADA"], "notes": "OG Filename: <PERSON> 2258\nKaytranada beat for <PERSON> from April 2019. Apparently on an album copy along with \"Garden\", \"Water\", \"Take Me To The Light\", \"Survive\", and more.", "length": "219.74", "fileDate": 17139168, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/9993a1e00e9e4101b60b51ce83a8e97a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9993a1e00e9e4101b60b51ce83a8e97a\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. KAYTRANADA)\", \"description\": \"OG Filename: Ye 2258\\nKaytranada beat for Ye from April 2019. Apparently on an album copy along with \\\"Garden\\\", \\\"Water\\\", \\\"Take Me To The Light\\\", \\\"Survive\\\", and more.\", \"date\": 17139168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e283d2a0e51130969cfce5f0dd778fcb\", \"url\": \"https://api.pillowcase.su/api/download/e283d2a0e51130969cfce5f0dd778fcb\", \"size\": \"5.77 MB\", \"duration\": 219.74}", "aliases": [], "size": "5.77 MB"}, {"id": "-233", "name": "???", "artists": [], "producers": [], "notes": "2019 Yandhi era ref done by <PERSON><PERSON> Clemons. Snippet leaked October 14th, 2024.", "length": "5.52", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/0108b88b7a7f693857d55b68decdf01c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0108b88b7a7f693857d55b68decdf01c\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Act A Fool\"], \"description\": \"2019 Yandhi era ref done by <PERSON><PERSON> Clem<PERSON>. Snippet leaked October 14th, 2024.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"51ae915c6cc362bc84a575794319922e\", \"url\": \"https://api.pillowcase.su/api/download/51ae915c6cc362bc84a575794319922e\", \"size\": \"2.34 MB\", \"duration\": 5.52}", "aliases": ["Act A Fool"], "size": "2.34 MB"}, {"id": "momma-i-hit-a-lick", "name": "2 Chainz - Mom<PERSON> I Hit A Lick [V5]", "artists": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Version of \"Momma I Hit A Lick\" that has <PERSON><PERSON><PERSON> and <PERSON><PERSON> but no <PERSON><PERSON> Snippet leaked December 2nd, 2024.", "length": "9.46", "fileDate": 17330976, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e1a50c04d9988c48517c648af764d2fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e1a50c04d9988c48517c648af764d2fd\", \"key\": \"<PERSON><PERSON> I Hit A Lick\", \"title\": \"2 <PERSON>z - Mom<PERSON> I Hit A Lick [V5]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Version of \\\"Momma I Hit A Lick\\\" that has <PERSON><PERSON><PERSON> and <PERSON> but no Ye. Snippet leaked December 2nd, 2024.\", \"date\": 17330976, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"99959a682f34c2c1250801f13936a386\", \"url\": \"https://api.pillowcase.su/api/download/99959a682f34c2c1250801f13936a386\", \"size\": \"2.41 MB\", \"duration\": 9.46}", "aliases": [], "size": "2.41 MB"}, {"id": "momma-i-hit-a-lick-235", "name": "2 Chainz - <PERSON><PERSON> I Hit A Lick [V6]", "artists": ["Kanye West", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Version with short <PERSON><PERSON><PERSON> vocals in the intro, different 2 Chainz verses, <PERSON><PERSON><PERSON> vocals and extended <PERSON><PERSON> verse. Snippet posted on TheSource Tier 2.", "length": "9.3", "fileDate": 16111872, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/cf1d3ce31521f55fa6e4f24918ab9246", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf1d3ce31521f55fa6e4f24918ab9246\", \"key\": \"<PERSON><PERSON> I Hit A Lick\", \"title\": \"2 <PERSON>z - Mom<PERSON> I Hit A Lick [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Version with short <PERSON><PERSON><PERSON> vocals in the intro, different 2 <PERSON><PERSON> verses, <PERSON><PERSON><PERSON> vocals and extended <PERSON><PERSON> verse. Snippet posted on TheSource Tier 2.\", \"date\": 16111872, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d7afa43c8a582c5caa2d21597e0a0800\", \"url\": \"https://api.pillowcase.su/api/download/d7afa43c8a582c5caa2d21597e0a0800\", \"size\": \"2.41 MB\", \"duration\": 9.3}", "aliases": [], "size": "2.41 MB"}, {"id": "star-time", "name": "88-<PERSON> - Star Time [V6]", "artists": ["Kanye West", "G-Eazy", "The-Dream"], "producers": ["88-<PERSON>", "<PERSON> <PERSON>"], "notes": "OG Filename: Star Time G Ruff1\nVersion of Star Time with a finished <PERSON><PERSON><PERSON> verse, <PERSON><PERSON><PERSON> on the hook, and 2 minutes of open. Has a G-Eazy verse used in later versions. Leaked after a groupbuy.", "length": "271.99", "fileDate": 16988832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/e6d05dd55dd99404e9eb36b64e4452a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e6d05dd55dd99404e9eb36b64e4452a5\", \"key\": \"Star Time\", \"title\": \"88-Keys - Star Time [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> & The-Dream) (prod. 88-<PERSON> & Anderson .Paak)\", \"aliases\": [\"The Mind Is Powerful\", \"Start Time\"], \"description\": \"OG Filename: Star Time G Ruff1\\nVersion of Star Time with a finished Kanye verse, <PERSON>-<PERSON> on the hook, and 2 minutes of open. Has a G-Eazy verse used in later versions. Leaked after a groupbuy.\", \"date\": 16988832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4275c01c162581125312b3d34876d8a9\", \"url\": \"https://api.pillowcase.su/api/download/4275c01c162581125312b3d34876d8a9\", \"size\": \"6.61 MB\", \"duration\": 271.99}", "aliases": ["The Mind Is Powerful", "Start Time"], "size": "6.61 MB"}, {"id": "star-time-237", "name": "88-<PERSON> - Star Time [V7]", "artists": ["G-Eazy", "IDK", "<PERSON> <PERSON>", "???"], "producers": ["88-<PERSON>", "<PERSON> <PERSON>"], "notes": "OG Filename: * x\nLater version of \"Start Time\" from around February 2019 made for To Be Liked... A Social Experiment. Has G-Eazy and IDK doing verses, <PERSON> on the hook, and an unknown artist beatboxing at the end. Non-OG file originally leaked after a groupbuy. Exported with iTunes 12.8.3.", "length": "233.77", "fileDate": 17343936, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/3a97e4b76346ed329739749426e14a01", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a97e4b76346ed329739749426e14a01\", \"key\": \"Star Time\", \"title\": \"88-Keys - Star Time [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & ???) (prod. 88-<PERSON> & Anderson .Paak)\", \"aliases\": [\"The Mind Is Powerful\", \"Start Time\"], \"description\": \"OG Filename: * x\\nLater version of \\\"Start Time\\\" from around February 2019 made for To Be Liked... A Social Experiment. Has G-Eazy and IDK doing verses, <PERSON> <PERSON> on the hook, and an unknown artist beatboxing at the end. Non-OG file originally leaked after a groupbuy. Exported with iTunes 12.8.3.\", \"date\": 17343936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d524e3b5ba93a8880f67d6f246bb4423\", \"url\": \"https://api.pillowcase.su/api/download/d524e3b5ba93a8880f67d6f246bb4423\", \"size\": \"6 MB\", \"duration\": 233.77}", "aliases": ["The Mind Is Powerful", "Start Time"], "size": "6 MB"}, {"id": "drip", "name": "<PERSON><PERSON><PERSON> The Crownholder - <PERSON><PERSON> ", "artists": ["Consequence", "Kanye West"], "producers": [], "notes": "OG Filename: Drip NEW HORNS\nVersion of \"Drip\", a song by Consequence's son <PERSON><PERSON><PERSON> The Crownholder, with vocals from <PERSON> on the hook. Released officially on August 19th, 2020 without <PERSON>'s vocals.", "length": "138.77", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/caa4ab2534fe93425b73f0fda884cacd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/caa4ab2534fe93425b73f0fda884cacd\", \"key\": \"Drip\", \"title\": \"Caiden The Crownholder - Drip \", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"All the Drip\"], \"description\": \"OG Filename: Drip NEW HORNS\\nVersion of \\\"Drip\\\", a song by <PERSON><PERSON>quence's son <PERSON><PERSON><PERSON> The Crownholder, with vocals from <PERSON> on the hook. Released officially on August 19th, 2020 without <PERSON>'s vocals.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d1afa766614013f375d5a7bc6de46ebb\", \"url\": \"https://api.pillowcase.su/api/download/d1afa766614013f375d5a7bc6de46ebb\", \"size\": \"4.48 MB\", \"duration\": 138.77}", "aliases": ["All the Drip"], "size": "4.48 MB"}, {"id": "metta-world-peace", "name": "<PERSON> and the Lights - Metta World Peace [V10]", "artists": ["<PERSON>", "Kanye West"], "producers": [], "notes": "OG Filename: Metta World Peace FS 01.24.19\nVersion of \"Metta World Peace\" with no verses. <PERSON><PERSON> backing vocals. A single line from <PERSON><PERSON><PERSON> is kept as part of the chorus, he says \"Keep 'em guessin'\". Likely from around when <PERSON> took the song for himself, stripping back the song's development to incorporate new verses and make it a solo song.", "length": "210.34", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/821904d19cdb4b5dd4c2e4c2505d6b11", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/821904d19cdb4b5dd4c2e4c2505d6b11\", \"key\": \"Metta World Peace\", \"title\": \"<PERSON> and the Lights - Metta World Peace [V10]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Take Me To The Light\"], \"description\": \"OG Filename: Metta World Peace FS 01.24.19\\nVersion of \\\"Metta World Peace\\\" with no verses. <PERSON><PERSON> backing vocals. A single line from <PERSON><PERSON><PERSON> is kept as part of the chorus, he says \\\"Keep 'em guessin'\\\". Likely from around when <PERSON> took the song for himself, stripping back the song's development to incorporate new verses and make it a solo song.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b4e4f46a69f71b4c7dd3b42139f3c652\", \"url\": \"https://api.pillowcase.su/api/download/b4e4f46a69f71b4c7dd3b42139f3c652\", \"size\": \"5.63 MB\", \"duration\": 210.34}", "aliases": ["You Still Take Me To The Light", "Take Me To The Light"], "size": "5.63 MB"}, {"id": "metta-world-peace-240", "name": "<PERSON> and the Lights - Metta World Peace [V11]", "artists": ["<PERSON>", "Kanye West"], "producers": [], "notes": "OG Filename: Metta World Peace FS 01.24.19 w vocal\nVersion of \"Metta World Peace\" with a new verse from <PERSON> and the Lights.", "length": "193.3", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4361b6c8ae736bde42f497810639879b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4361b6c8ae736bde42f497810639879b\", \"key\": \"Metta World Peace\", \"title\": \"<PERSON> and the Lights - Metta World Peace [V11]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Take Me To The Light\"], \"description\": \"OG Filename: Metta World Peace FS 01.24.19 w vocal\\nVersion of \\\"Metta World Peace\\\" with a new verse from <PERSON> and the Lights.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1fa118acc2cd133d072f5eda82152101\", \"url\": \"https://api.pillowcase.su/api/download/1fa118acc2cd133d072f5eda82152101\", \"size\": \"5.35 MB\", \"duration\": 193.3}", "aliases": ["You Still Take Me To The Light", "Take Me To The Light"], "size": "5.35 MB"}, {"id": "metta-world-peace-241", "name": "<PERSON> and the Lights - Metta World Peace [V12]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: Metta World Peace 01.28.19 add Ant Vox\nVersion of \"Metta World Peace\" that has new vocals from <PERSON><PERSON>.", "length": "196.04", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/f93c5ebdebd85d26f696946b33d44308", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f93c5ebdebd85d26f696946b33d44308\", \"key\": \"Metta World Peace\", \"title\": \"<PERSON> and the Lights - Metta World Peace [V12]\", \"artists\": \"(feat. <PERSON><PERSON> & Bon Iver)\", \"aliases\": [\"You Still Take Me To The Light\", \"Take Me To The Light\"], \"description\": \"OG Filename: Metta World Peace 01.28.19 add <PERSON><PERSON>\\nVersion of \\\"Metta World Peace\\\" that has new vocals from <PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f772750ad3417777670857264b0f265d\", \"url\": \"https://api.pillowcase.su/api/download/f772750ad3417777670857264b0f265d\", \"size\": \"5.4 MB\", \"duration\": 196.04}", "aliases": ["You Still Take Me To The Light", "Take Me To The Light"], "size": "5.4 MB"}, {"id": "metta-world-peace-242", "name": "<PERSON> and the Lights - Metta World Peace [V13]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: Metta World Peace 01.28.19 add <PERSON><PERSON> and <PERSON>\nHas minor mixing differences compared to the previous version.", "length": "196.04", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/5261ef7c2ecea9d6ac10b1057bc5fdb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5261ef7c2ecea9d6ac10b1057bc5fdb0\", \"key\": \"Metta World Peace\", \"title\": \"<PERSON> and the Lights - Metta World Peace [V13]\", \"artists\": \"(feat. <PERSON><PERSON> & Bon I<PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Take Me To The Light\"], \"description\": \"OG Filename: Metta World Peace 01.28.19 add <PERSON><PERSON> and <PERSON>\\nHas minor mixing differences compared to the previous version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5a05c16b7b612252392527616ef218d4\", \"url\": \"https://api.pillowcase.su/api/download/5a05c16b7b612252392527616ef218d4\", \"size\": \"5.4 MB\", \"duration\": 196.04}", "aliases": ["You Still Take Me To The Light", "Take Me To The Light"], "size": "5.4 MB"}, {"id": "ass-shots", "name": "French Montana - Ass Shots [V5]", "artists": ["Kanye West", "<PERSON>'ron"], "producers": [], "notes": "Version of \"Ass Shots\" from 2019, era and exact date unknown. Snippets leaked in May 2022. This version has the old intro still attached unlike newer versions.", "length": "61.81", "fileDate": 16513632, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/b7a2de2e00f0a912af22157134e1b273", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b7a2de2e00f0a912af22157134e1b273\", \"key\": \"Ass Shots\", \"title\": \"French Montana - Ass Shots [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Cam<PERSON>ron)\", \"aliases\": [\"Ass Shot\", \"Bang\"], \"description\": \"Version of \\\"Ass Shots\\\" from 2019, era and exact date unknown. Snippets leaked in May 2022. This version has the old intro still attached unlike newer versions.\", \"date\": 16513632, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a235121cfe95a74752e99a2739c38fb1\", \"url\": \"https://api.pillowcase.su/api/download/a235121cfe95a74752e99a2739c38fb1\", \"size\": \"3.25 MB\", \"duration\": 61.81}", "aliases": ["<PERSON><PERSON> Shot", "<PERSON>"], "size": "3.25 MB"}, {"id": "ass-shots-244", "name": "French Montana - Ass Shots [V6]", "artists": ["Kanye West", "<PERSON>'ron"], "producers": [], "notes": "Version of \"Ass Shots\" from 2019, era and exact date unknown. Has a different instrumental compared to the other versions. Samples \"Painted\" by MS MR.", "length": "245.11", "fileDate": 17053632, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/ea82fb634102154e62c68b7e1d0fbc41", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ea82fb634102154e62c68b7e1d0fbc41\", \"key\": \"Ass Shots\", \"title\": \"French Montana - Ass Shots [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Cam'ron)\", \"aliases\": [\"Ass Shot\", \"Bang\"], \"description\": \"Version of \\\"Ass Shots\\\" from 2019, era and exact date unknown. Has a different instrumental compared to the other versions. Samples \\\"Painted\\\" by MS MR.\", \"date\": 17053632, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"62c36115aaf35ffb5d15ffcf390eb5ff\", \"url\": \"https://api.pillowcase.su/api/download/62c36115aaf35ffb5d15ffcf390eb5ff\", \"size\": \"6.18 MB\", \"duration\": 245.11}", "aliases": ["<PERSON><PERSON> Shot", "<PERSON>"], "size": "6.18 MB"}, {"id": "i-just-wanna-know", "name": "<PERSON><PERSON><PERSON> - I Just Wanna Know", "artists": ["Cardi B", "Ty Dolla $ign"], "producers": [], "notes": "Original <PERSON><PERSON><PERSON> song where his vocals for \"Do It\" comes from, his pre-chorus and chorus vocals were turned into a verse. The song was meant to release on Ty Dolla $ign's album \"Featuring Ty Dolla $ign\" but for unknown reasons did not make it, he later took <PERSON><PERSON><PERSON>'s verse from this song for \"Do It\". <PERSON><PERSON> is also featured on the song but was cut from \"Do It\".", "length": "202.7", "fileDate": 15960672, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/597c3e6d5724b03d7dd5a9288f8da619", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/597c3e6d5724b03d7dd5a9288f8da619\", \"key\": \"I Just Wanna Know\", \"title\": \"<PERSON><PERSON><PERSON> - I Just Wanna Know\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> $ign)\", \"aliases\": [\"Do It\"], \"description\": \"Original Nipsey Hussle song where his vocals for \\\"Do It\\\" comes from, his pre-chorus and chorus vocals were turned into a verse. The song was meant to release on Ty Dolla $ign's album \\\"Featuring Ty Dolla $ign\\\" but for unknown reasons did not make it, he later took <PERSON><PERSON><PERSON>'s verse from this song for \\\"Do It\\\". <PERSON><PERSON> is also featured on the song but was cut from \\\"Do It\\\".\", \"date\": 15960672, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b47eb9893f8502fdb2ee0bc8e63b35ab\", \"url\": \"https://api.pillowcase.su/api/download/b47eb9893f8502fdb2ee0bc8e63b35ab\", \"size\": \"5.5 MB\", \"duration\": 202.7}", "aliases": ["Do It"], "size": "5.5 MB"}, {"id": "coming-home", "name": "<PERSON><PERSON><PERSON> T - Coming Home [V10]", "artists": ["Ms. <PERSON><PERSON>"], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON>", "MIKE DEAN"], "notes": "A version of \"Coming Home\" that contains alternate vocal takes from <PERSON><PERSON> and a much longer outro. There are also slight instrumental differences. Leaked a few months before the song's official release.", "length": "361.22", "fileDate": 15607296, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/f91615c91613e86168a988ff4de9d6e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f91615c91613e86168a988ff4de9d6e4\", \"key\": \"Coming Home\", \"title\": \"Pusha T - Coming Home [V10]\", \"artists\": \"(feat. Ms. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Afta U\", \"After You\", \"Reaper\"], \"description\": \"A version of \\\"Coming Home\\\" that contains alternate vocal takes from Lauryn Hill and a much longer outro. There are also slight instrumental differences. Leaked a few months before the song's official release.\", \"date\": 15607296, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3e97ae35fe915e4b60a627b9d7a44cac\", \"url\": \"https://api.pillowcase.su/api/download/3e97ae35fe915e4b60a627b9d7a44cac\", \"size\": \"8.04 MB\", \"duration\": 361.22}", "aliases": ["Afta U", "After You", "Reaper"], "size": "8.04 MB"}, {"id": "pray-for-me", "name": "SAINt JHN - Pray For Me [V1]", "artists": [], "producers": [], "notes": "Original version of \"Pray For Me\", made after <PERSON><PERSON><PERSON> died.", "length": "181.24", "fileDate": 16079904, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/4a194acdb880b1469057659017268d0b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a194acdb880b1469057659017268d0b\", \"key\": \"Pray For Me\", \"title\": \"SAINt JHN - Pray For Me [V1]\", \"aliases\": [\"Pray 4 Me\"], \"description\": \"Original version of \\\"Pray For Me\\\", made after <PERSON><PERSON><PERSON> died.\", \"date\": 16079904, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"351213fa76086d263cdbd6bae32a3101\", \"url\": \"https://api.pillowcase.su/api/download/351213fa76086d263cdbd6bae32a3101\", \"size\": \"5.16 MB\", \"duration\": 181.24}", "aliases": ["Pray 4 Me"], "size": "5.16 MB"}, {"id": "pray-for-me-248", "name": "SAINt JHN - Pray For Me [V1]", "artists": [], "producers": [], "notes": "Original version of \"Pray For Me\", made after <PERSON><PERSON><PERSON> died.", "length": "", "fileDate": 16079904, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://www.hotnewhiphop.com/349405-saint-j<PERSON>-kanye-west-and-buju-banton-may-drop-a-collab-album-news", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.hotnewhiphop.com/349405-saint-jhn-kanye-west-and-buju-banton-may-drop-a-collab-album-news\", \"key\": \"Pray For Me\", \"title\": \"SAINt JHN - Pray For Me [V1]\", \"aliases\": [\"Pray 4 Me\"], \"description\": \"Original version of \\\"Pray For Me\\\", made after <PERSON><PERSON><PERSON> died.\", \"date\": 16079904, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Pray 4 Me"], "size": ""}, {"id": "the-glade", "name": "<PERSON> - The Glade [V2]", "artists": [], "producers": ["Ace G", "<PERSON>"], "notes": "Version without <PERSON> played by <PERSON> in an Instagram Live on October 14th.", "length": "67.2", "fileDate": 16026336, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/c2f20bfddf6b60357aee85122fa02e14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c2f20bfddf6b60357aee85122fa02e14\", \"key\": \"The Glade\", \"title\": \"<PERSON> - The Glade [V2]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Ashes\", \"Beauty From Ashes\", \"Beauty To Ashes\", \"Up From The Ashes\"], \"description\": \"Version without <PERSON> played by <PERSON> in an Instagram Live on October 14th.\", \"date\": 16026336, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ac610fe92063966612c2a3eed5747413\", \"url\": \"https://api.pillowcase.su/api/download/ac610fe92063966612c2a3eed5747413\", \"size\": \"3.33 MB\", \"duration\": 67.2}", "aliases": ["Ashes", "Beauty From Ashes", "Beauty To Ashes", "Up From The Ashes"], "size": "3.33 MB"}, {"id": "the-glade-250", "name": "<PERSON> - The Glade [V3]", "artists": ["<PERSON>"], "producers": ["Ace G", "<PERSON>"], "notes": "OG Filename: the glade [041519] {reference 10}\nEarly 2019 version of \"The Glade\" with a <PERSON> feature. <PERSON> originally <PERSON>'s song before he gave it to <PERSON><PERSON><PERSON> for JESUS IS KING. Some of <PERSON>'s lyrics would be reused in <PERSON><PERSON><PERSON>'s rendition of the song. <PERSON><PERSON> has said a voice memo exists where <PERSON><PERSON><PERSON> states that \"the melody [for the song] came from <PERSON>\", and it was \"about a Phoenix rising from the ashes\".", "length": "226.89", "fileDate": 16844544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/76751342a7ba26f41614d1f145e5886f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76751342a7ba26f41614d1f145e5886f\", \"key\": \"The Glade\", \"title\": \"<PERSON> - The Glade [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Ashes\", \"Beauty From Ashes\", \"Beauty To Ashes\", \"Up From The Ashes\"], \"description\": \"OG Filename: the glade [041519] {reference 10}\\nEarly 2019 version of \\\"The Glade\\\" with a Daniel Caesar feature. Was originally <PERSON>'s song before he gave it to <PERSON><PERSON><PERSON> for JESUS IS KING. Some of <PERSON>'s lyrics would be reused in <PERSON><PERSON><PERSON>'s rendition of the song. <PERSON><PERSON> has said a voice memo exists where <PERSON><PERSON><PERSON> states that \\\"the melody [for the song] came from <PERSON>\\\", and it was \\\"about a Phoenix rising from the ashes\\\".\", \"date\": 16844544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"aa895bcccc7c07af725ee891036ff6ee\", \"url\": \"https://api.pillowcase.su/api/download/aa895bcccc7c07af725ee891036ff6ee\", \"size\": \"5.89 MB\", \"duration\": 226.89}", "aliases": ["Ashes", "Beauty From Ashes", "Beauty To Ashes", "Up From The Ashes"], "size": "5.89 MB"}, {"id": "so-drunk", "name": "<PERSON> - So Drunk [V3]", "artists": ["Bad Bunny", "<PERSON>"], "producers": [], "notes": "<PERSON> song where <PERSON>'s vocals for \"Drunk\" comes from. <PERSON> is also featured on the song but <PERSON> and <PERSON> were both cut from \"Drunk\".", "length": "230.69", "fileDate": 17034624, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/375df886c724deb8bec7645804e2c587", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/375df886c724deb8bec7645804e2c587\", \"key\": \"So Drunk\", \"title\": \"<PERSON> - <PERSON> Drunk [V3]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"aliases\": [\"Drunk\"], \"description\": \"<PERSON> song where <PERSON>'s vocals for \\\"Drunk\\\" comes from. <PERSON> is also featured on the song but <PERSON> and <PERSON> were both cut from \\\"Drunk\\\".\", \"date\": 17034624, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9ef73ac761dd9967ebddc2478fbf0700\", \"url\": \"https://api.pillowcase.su/api/download/9ef73ac761dd9967ebddc2478fbf0700\", \"size\": \"5.95 MB\", \"duration\": 230.69}", "aliases": ["Drunk"], "size": "5.95 MB"}, {"id": "ego-death-252", "name": "Ty Dolla $ign - Ego Death [V7]", "artists": ["FKA twigs", "serpentwithfeet"], "producers": ["Skrillex"], "notes": "OG Filename: EGO DEATH skril YE VERSE\nHas alternate production and structure compared to release. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy. Has an open verse and more FKA twigs vocals.", "length": "196.04", "fileDate": 17400096, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/dcf9638399d3ba36e4fae75f60fe52f9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dcf9638399d3ba36e4fae75f60fe52f9\", \"key\": \"Ego Death\", \"title\": \"Ty Dolla $ign - Ego Death [V7]\", \"artists\": \"(feat. FKA twigs & serpentwithfeet) (prod. Skrillex)\", \"description\": \"OG Filename: EGO DEATH skril YE VERSE\\nHas alternate production and structure compared to release. Leaked as a bonus for the Uzi \\\"Don't Jump\\\" groupbuy. Has an open verse and more FKA twigs vocals.\", \"date\": 17400096, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5a2c14697545d6eae2764cc2b1c950ae\", \"url\": \"https://api.pillowcase.su/api/download/5a2c14697545d6eae2764cc2b1c950ae\", \"size\": \"5.39 MB\", \"duration\": 196.04}", "aliases": [], "size": "5.39 MB"}, {"id": "ego-death-253", "name": "Ty Dolla $ign - Ego Death [V9]", "artists": ["Kanye West", "FKA twigs", "serpentwithfeet"], "producers": ["Skrillex"], "notes": "OG Filename: Ego death  4.3.19\nHas an extra verse from <PERSON> and different production. Also includes a beatswitch meant to be a transition into the following track. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy.", "length": "281.79", "fileDate": 17400096, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/6139ba93e5aa4fb5917d2dee7d4a9a04", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6139ba93e5aa4fb5917d2dee7d4a9a04\", \"key\": \"Ego Death\", \"title\": \"Ty Dolla $ign - Ego Death [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, FKA twigs & serpentwithfeet) (prod. Skrillex)\", \"description\": \"OG Filename: Ego death  4.3.19\\nHas an extra verse from <PERSON> and different production. Also includes a beatswitch meant to be a transition into the following track. Leaked as a bonus for the Uzi \\\"Don't Jump\\\" groupbuy.\", \"date\": 17400096, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6199c0b17d08ea37df03b826cd8bb981\", \"url\": \"https://api.pillowcase.su/api/download/6199c0b17d08ea37df03b826cd8bb981\", \"size\": \"6.77 MB\", \"duration\": 281.79}", "aliases": [], "size": "6.77 MB"}, {"id": "big-boody-bitch", "name": "Young Thug - <PERSON> [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Original song where <PERSON>'s verse for \"River\" comes from. Made on February 8th, 2019 and features <PERSON><PERSON> who was cut off the song.", "length": "14.86", "fileDate": 15495840, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/ab599bc3e475c8e41cf3ca3325ee0054", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab599bc3e475c8e41cf3ca3325ee0054\", \"key\": \"Big Boody Bitch\", \"title\": \"Young Thug - Big Boody Bitch [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"River\"], \"description\": \"Original song where <PERSON>'s verse for \\\"River\\\" comes from. Made on February 8th, 2019 and features <PERSON><PERSON> who was cut off the song.\", \"date\": 15495840, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3c0d7cc87b47a40ccf02eedb6ec2924d\", \"url\": \"https://api.pillowcase.su/api/download/3c0d7cc87b47a40ccf02eedb6ec2924d\", \"size\": \"2.49 MB\", \"duration\": 14.86}", "aliases": ["River"], "size": "2.49 MB"}, {"id": "big-boody-bitch-255", "name": "Young Thug - <PERSON> [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Original song where <PERSON>'s verse for \"River\" comes from. Made on February 8th, 2019 and features <PERSON><PERSON> who was cut off the song.", "length": "19.83", "fileDate": 15495840, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/d0bee57ee076632d7e5daca67660d35b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d0bee57ee076632d7e5daca67660d35b\", \"key\": \"Big Boody Bitch\", \"title\": \"Young Thug - Big Boody Bitch [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"River\"], \"description\": \"Original song where <PERSON>'s verse for \\\"River\\\" comes from. Made on February 8th, 2019 and features <PERSON><PERSON> who was cut off the song.\", \"date\": 15495840, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e8e2de7a4bb3514361fc067c29137214\", \"url\": \"https://api.pillowcase.su/api/download/e8e2de7a4bb3514361fc067c29137214\", \"size\": \"2.57 MB\", \"duration\": 19.83}", "aliases": ["River"], "size": "2.57 MB"}, {"id": "big-boody-bitch-256", "name": "Young Thug - <PERSON> [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Original song where <PERSON>'s verse for \"River\" comes from. Made on February 8th, 2019 and features <PERSON><PERSON> who was cut off the song.", "length": "14.86", "fileDate": 15495840, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yandhi-v2", "originalUrl": "https://pillowcase.su/f/a958f2d6650b7edeb24297cde432a897", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a958f2d6650b7edeb24297cde432a897\", \"key\": \"Big Boody Bitch\", \"title\": \"Young Thug - Big Boody Bitch [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"River\"], \"description\": \"Original song where <PERSON>'s verse for \\\"River\\\" comes from. Made on February 8th, 2019 and features <PERSON><PERSON> who was cut off the song.\", \"date\": 15495840, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5682614db25203e98dc2c040e8ea7ea9\", \"url\": \"https://api.pillowcase.su/api/download/5682614db25203e98dc2c040e8ea7ea9\", \"size\": \"2.49 MB\", \"duration\": 14.86}", "aliases": ["River"], "size": "2.49 MB"}]}