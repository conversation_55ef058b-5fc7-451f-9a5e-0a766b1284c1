{"id": "ye", "name": "ye", "description": "<PERSON> discusses topics in <PERSON><PERSON><PERSON>'s life, including mental health, family, and addiction. He also explicitly announced his diagnosis of bipolar disorder through the album's artwork and a proclamation within the album. The seven-song project, created in Jackson Hole, Wyoming, was released alongside five other projects. <PERSON><PERSON><PERSON> revealed in an interview that after his infamous TMZ interview (in which he stated that slavery was a choice), he completely re-did his album with an entirely new theme.", "backgroundColor": "rgb(21, 44, 64)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17HXWMuSb6lGi2j_Y3wUQT3gM_0Jb0dJKGyVVi6gQzy7MCMXyTvAfGrEd4JcGVrUNOcW8iCGVuqCGi8wF-NvA7SKWKYJmT3AWubBfiPMcbbR0lpARRHKSPuw-JCUUB3Q5wD1jIQ31wMMqCVj5Q?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "all-mine", "name": "All Mine [V4]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "In the background of the Ty Dolla $ign vocal stems for \"All Mine\", a beat with an alternate melody and harder drums is audible. The link leads to a boosted version to make the beat audible.", "length": "3.03", "fileDate": 16607808, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/80fd2fd99401304b78a3436f572b5286", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80fd2fd99401304b78a3436f572b5286\", \"key\": \"All Mine\", \"title\": \"All Mine [V4]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"In the background of the Ty Dolla $ign vocal stems for \\\"All Mine\\\", a beat with an alternate melody and harder drums is audible. The link leads to a boosted version to make the beat audible.\", \"date\": 16607808, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"6b9c2aca2c2280b7eb06e30f621eb025\", \"url\": \"https://api.pillowcase.su/api/download/6b9c2aca2c2280b7eb06e30f621eb025\", \"size\": \"480 kB\", \"duration\": 3.03}", "aliases": [], "size": "480 kB"}, {"id": "all-mine-2", "name": "All Mine [V4]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "In the background of the Ty Dolla $ign vocal stems for \"All Mine\", a beat with an alternate melody and harder drums is audible. The link leads to a boosted version to make the beat audible.", "length": "2.61", "fileDate": 16607808, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/e8347e4dac239ff57cc135f3417b4e73", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8347e4dac239ff57cc135f3417b4e73\", \"key\": \"All Mine\", \"title\": \"All Mine [V4]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"In the background of the Ty Dolla $ign vocal stems for \\\"All Mine\\\", a beat with an alternate melody and harder drums is audible. The link leads to a boosted version to make the beat audible.\", \"date\": 16607808, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"42a383ba63a5db61be182437e6e1e266\", \"url\": \"https://api.pillowcase.su/api/download/42a383ba63a5db61be182437e6e1e266\", \"size\": \"473 kB\", \"duration\": 2.61}", "aliases": [], "size": "473 kB"}, {"id": "all-mine-3", "name": "All Mine [V4]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "In the background of the Ty Dolla $ign vocal stems for \"All Mine\", a beat with an alternate melody and harder drums is audible. The link leads to a boosted version to make the beat audible.", "length": "5.83", "fileDate": 16607808, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/77a980e45c450a9433373a0846577f53", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/77a980e45c450a9433373a0846577f53\", \"key\": \"All Mine\", \"title\": \"All Mine [V4]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"In the background of the Ty Dolla $ign vocal stems for \\\"All Mine\\\", a beat with an alternate melody and harder drums is audible. The link leads to a boosted version to make the beat audible.\", \"date\": 16607808, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"68fd93824334393569032f914afcd94d\", \"url\": \"https://api.pillowcase.su/api/download/68fd93824334393569032f914afcd94d\", \"size\": \"525 kB\", \"duration\": 5.83}", "aliases": [], "size": "525 kB"}, {"id": "all-mine-4", "name": "🗑️ All Mine [V5]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: All Mine Tassho <PERSON>f\n<PERSON> reference track. Appears to be recorded via iPhone. Interpolates the flow from \"Smooth Criminal\" by <PERSON>, of all things.", "length": "53.9", "fileDate": 16985376, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/f9729c307ef55f392b45ebc6468e3ce5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f9729c307ef55f392b45ebc6468e3ce5\", \"key\": \"All Mine\", \"title\": \"\\ud83d\\uddd1\\ufe0f All Mine [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: All Mine Tassho Ref\\nTassho Pearce reference track. Appears to be recorded via iPhone. Interpolates the flow from \\\"Smooth Criminal\\\" by <PERSON>, of all things.\", \"date\": 16985376, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1696c3adec4b2f6aae49e5b90fdbeef7\", \"url\": \"https://api.pillowcase.su/api/download/1696c3adec4b2f6aae49e5b90fdbeef7\", \"size\": \"1.29 MB\", \"duration\": 53.9}", "aliases": [], "size": "1.29 MB"}, {"id": "all-mine-5", "name": "All Mine [V6]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "MIKE DEAN", "<PERSON>", "the Lights", "<PERSON>"], "notes": "OG Filename: All Mine REF 05.25.18\nHas only the <PERSON><PERSON> Clem<PERSON> intro/hook, rest is open.", "length": "125.96", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/a6c9eec864c96b45c857816fe024ab70", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6c9eec864c96b45c857816fe024ab70\", \"key\": \"All Mine\", \"title\": \"All Mine [V6]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Francis & the Lights & Scott <PERSON>)\", \"description\": \"OG Filename: All Mine REF 05.25.18\\nHas only the <PERSON><PERSON> intro/hook, rest is open.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dd4730d64ee72c96ec90a99cad3b1a88\", \"url\": \"https://api.pillowcase.su/api/download/dd4730d64ee72c96ec90a99cad3b1a88\", \"size\": \"2.45 MB\", \"duration\": 125.96}", "aliases": [], "size": "2.45 MB"}, {"id": "all-mine-6", "name": "All Mine [V7]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>"], "producers": ["Kanye West", "MIKE DEAN", "<PERSON>", "the Lights", "<PERSON>"], "notes": "OG Filename: All Mine REF 05.26.18 \nHas no <PERSON><PERSON><PERSON> vocals.", "length": "125.96", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/bd5d7c018b333857e2f8feb14c8e9a1c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bd5d7c018b333857e2f8feb14c8e9a1c\", \"key\": \"All Mine\", \"title\": \"All Mine [V7]\", \"artists\": \"(feat. <PERSON>gn & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Francis & the Lights & Scott Carter)\", \"description\": \"OG Filename: All Mine REF 05.26.18 \\nHas no Ka<PERSON><PERSON> vocals.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d0e307fa5f6004ae5575ad302457a00c\", \"url\": \"https://api.pillowcase.su/api/download/d0e307fa5f6004ae5575ad302457a00c\", \"size\": \"2.45 MB\", \"duration\": 125.96}", "aliases": [], "size": "2.45 MB"}, {"id": "all-mine-7", "name": "All Mine [V8]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>"], "producers": [], "notes": "<PERSON><PERSON><PERSON> has said that <PERSON><PERSON><PERSON> contributed the \"Stormy Daniels\" line, in a June 2018 interview. In response to <PERSON><PERSON> posting the ye cover to Twitter, <PERSON><PERSON><PERSON> played his reference track on Instagram Live. Dated after <PERSON> $ign recorded his vocals as his vocals are present on this version.", "length": "301.8", "fileDate": 16896384, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/17143e0dcc486140ccc611c918933812", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/17143e0dcc486140ccc611c918933812\", \"key\": \"All Mine\", \"title\": \"All Mine [V8]\", \"artists\": \"(ref. Consequence) (feat. <PERSON>gn & <PERSON>t <PERSON>)\", \"description\": \"<PERSON><PERSON><PERSON> has said that <PERSON><PERSON><PERSON> contributed the \\\"Stormy Daniels\\\" line, in a June 2018 interview. In response to <PERSON><PERSON> posting the ye cover to Twitter, <PERSON><PERSON><PERSON> played his reference track on Instagram Live. Dated after <PERSON>gn recorded his vocals as his vocals are present on this version.\", \"date\": 16896384, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"69ef0b740b2ced8e475217d7b7c1d6d2\", \"url\": \"https://api.pillowcase.su/api/download/69ef0b740b2ced8e475217d7b7c1d6d2\", \"size\": \"5.26 MB\", \"duration\": 301.8}", "aliases": [], "size": "5.26 MB"}, {"id": "all-mine-8", "name": "All Mine [V9]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "MIKE DEAN", "<PERSON>", "the Lights", "<PERSON>"], "notes": "OG Filename: all mine fyaa reff\nFya Man reference track, seemingly iPhone vocals.", "length": "116.61", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/7e0720df309b78800f0bcec0a9f0bf2e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e0720df309b78800f0bcec0a9f0bf2e\", \"key\": \"All Mine\", \"title\": \"All Mine [V9]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Francis & the Lights & Scott Carter)\", \"description\": \"OG Filename: all mine fyaa reff\\nFya Man reference track, seemingly iPhone vocals.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6472f953588c0b244a1861710078898\", \"url\": \"https://api.pillowcase.su/api/download/d6472f953588c0b244a1861710078898\", \"size\": \"2.3 MB\", \"duration\": 116.61}", "aliases": [], "size": "2.3 MB"}, {"id": "all-mine-9", "name": "All Mine [V10]", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>"], "producers": [], "notes": "Has different vocal takes and unused lyrics, and some parts of the production are different compared to release. Includes the same Ty Doll<PERSON> $ign and <PERSON><PERSON> vocals as released version.", "length": "11.49", "fileDate": 16057440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/20f4480e371bde39e663b194f06ecf18", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20f4480e371bde39e663b194f06ecf18\", \"key\": \"All Mine\", \"title\": \"All Mine [V10]\", \"artists\": \"(feat. <PERSON>ign & <PERSON><PERSON>)\", \"description\": \"Has different vocal takes and unused lyrics, and some parts of the production are different compared to release. Includes the same Ty Doll<PERSON> $ign and <PERSON><PERSON> vocals as released version.\", \"date\": 16057440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0f88a6054296b7f7029526d5167da7d2\", \"url\": \"https://api.pillowcase.su/api/download/0f88a6054296b7f7029526d5167da7d2\", \"size\": \"615 kB\", \"duration\": 11.49}", "aliases": [], "size": "615 kB"}, {"id": "bad-people", "name": "Bad People [V3]", "artists": ["<PERSON>"], "producers": ["Kanye West", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Bad People REF 05.25.18\nHas a different structure and is longer than later versions. Only 24 seconds of <PERSON><PERSON><PERSON> vocals.", "length": "221.05", "fileDate": 16953408, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/de3be6541c36fe4f74d7aefb68d0e61c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/de3be6541c36fe4f74d7aefb68d0e61c\", \"key\": \"Bad People\", \"title\": \"Bad People [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"OG Filename: Bad People REF 05.25.18\\nHas a different structure and is longer than later versions. Only 24 seconds of <PERSON>ny<PERSON> vocals.\", \"date\": 16953408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b878365cfcb32627346ec3cd333791f4\", \"url\": \"https://api.pillowcase.su/api/download/b878365cfcb32627346ec3cd333791f4\", \"size\": \"3.97 MB\", \"duration\": 221.05}", "aliases": ["White Label"], "size": "3.97 MB"}, {"id": "bad-people-11", "name": "Bad People [V4]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "<PERSON>"], "notes": "Played by <PERSON><PERSON><PERSON> in a live, he cuts off the snippet before the school shooting line.", "length": "50.36", "fileDate": 16695936, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/d4bfd6736db84e91ad863d193f64cf81", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d4bfd6736db84e91ad863d193f64cf81\", \"key\": \"Bad People\", \"title\": \"Bad People [V4]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"Played by Consequence in a live, he cuts off the snippet before the school shooting line.\", \"date\": 16695936, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3162cac0ebb62503b6235fd36dee8352\", \"url\": \"https://api.pillowcase.su/api/download/3162cac0ebb62503b6235fd36dee8352\", \"size\": \"1.24 MB\", \"duration\": 50.36}", "aliases": ["White Label"], "size": "1.24 MB"}, {"id": "bad-people-12", "name": "Bad People [V5]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "<PERSON>", "<PERSON>"], "notes": "OG Filename: Bad People [<PERSON>] REF 05.25.18\nHas alternate production done by <PERSON>. No longer has the <PERSON> vocals, now and moving forward.", "length": "134.06", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/27c18fc6f4b081b13ac80d83e98e90cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/27c18fc6f4b081b13ac80d83e98e90cf\", \"key\": \"Bad People\", \"title\": \"Bad People [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"OG Filename: Bad People [Rick Version] REF 05.25.18\\nHas alternate production done by <PERSON>. No longer has the <PERSON> vocals, now and moving forward.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"94efab6b59a918a007d04d3c9c9ad34a\", \"url\": \"https://api.pillowcase.su/api/download/94efab6b59a918a007d04d3c9c9ad34a\", \"size\": \"2.58 MB\", \"duration\": 134.06}", "aliases": ["White Label"], "size": "2.58 MB"}, {"id": "bad-people-13", "name": "Bad People [V6] ", "artists": [], "producers": [], "notes": "OG Filename: Bad People Verse 1\nFirst <PERSON> voice memo reference track.", "length": "71.57", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/4245ede765c332ff024d4577873f3f9b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4245ede765c332ff024d4577873f3f9b\", \"key\": \"Bad People\", \"title\": \"Bad People [V6] \", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"OG Filename: Bad People Verse 1\\nFirst <PERSON> voice memo reference track.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"958e25a54af2cba4e740f51c2925a71e\", \"url\": \"https://api.pillowcase.su/api/download/958e25a54af2cba4e740f51c2925a71e\", \"size\": \"1 MB\", \"duration\": 71.57}", "aliases": ["White Label"], "size": "1 MB"}, {"id": "bad-people-14", "name": "Bad People [V7] ", "artists": [], "producers": [], "notes": "OG Filename: Bad People Verse 2\nSecond <PERSON> voice memo reference track.", "length": "66.62", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/63d1e16407a35cf3e6a3517b4de7e8a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63d1e16407a35cf3e6a3517b4de7e8a6\", \"key\": \"Bad People\", \"title\": \"Bad People [V7] \", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"OG Filename: Bad People Verse 2\\nSecond <PERSON> voice memo reference track.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"77a19483f6bc4c0f43d76aa032fa3471\", \"url\": \"https://api.pillowcase.su/api/download/77a19483f6bc4c0f43d76aa032fa3471\", \"size\": \"964 kB\", \"duration\": 66.62}", "aliases": ["White Label"], "size": "964 kB"}, {"id": "bad-people-15", "name": "Bad People [V8]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Has alternate production done by <PERSON><PERSON><PERSON><PERSON>. Unknown if <PERSON> is still featured. Shown on one of <PERSON><PERSON><PERSON><PERSON>'s livestreams.", "length": "0.68", "fileDate": 16106688, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/4dbf91b97f040df7863a5127b13b6ba2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4dbf91b97f040df7863a5127b13b6ba2\", \"key\": \"Bad People\", \"title\": \"Bad People [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"Has alternate production done by <PERSON><PERSON><PERSON><PERSON>. Unknown if <PERSON> is still featured. Shown on one of <PERSON><PERSON>er<PERSON>'s livestreams.\", \"date\": 16106688, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9036122314073130b4fcda53914eb157\", \"url\": \"https://api.pillowcase.su/api/download/9036122314073130b4fcda53914eb157\", \"size\": \"442 kB\", \"duration\": 0.68}", "aliases": ["White Label"], "size": "442 kB"}, {"id": "i-feel-free", "name": "🗑️ I Feel Free [V7]", "artists": [], "producers": [], "notes": "<PERSON><PERSON><PERSON> reference track. Has around nine minutes of open verse.", "length": "611.29", "fileDate": 16633728, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/ca610b0806a374ab1c8b48b3d6869092", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ca610b0806a374ab1c8b48b3d6869092\", \"key\": \"I Feel Free\", \"title\": \"\\ud83d\\uddd1\\ufe0f I Feel Free [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"I Feel Free\", \"Freeee (Ghost Town Pt. 2)\"], \"description\": \"Jeremih reference track. Has around nine minutes of open verse.\", \"date\": 16633728, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"be0ae9f31910b5f74de90c16be75a0a4\", \"url\": \"https://api.pillowcase.su/api/download/be0ae9f31910b5f74de90c16be75a0a4\", \"size\": \"10.2 MB\", \"duration\": 611.29}", "aliases": ["I Feel Free", "Freeee (Ghost Town Pt. 2)"], "size": "10.2 MB"}, {"id": "i-feel-free-17", "name": "I Feel Free [V8]", "artists": [], "producers": [], "notes": "Consequence reference, played by him on a July 18th Instagram Live. Has different <PERSON><PERSON><PERSON> vocals from all known versions that are currently avaliable", "length": "60.31", "fileDate": 17212608, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/da747554c0acdf1e4c5edd1fa4667121", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/da747554c0acdf1e4c5edd1fa4667121\", \"key\": \"I Feel Free\", \"title\": \"I Feel Free [V8]\", \"artists\": \"(ref. Consequence)\", \"aliases\": [\"<PERSON>\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"Consequence reference, played by him on a July 18th Instagram Live. Has different Kanye vocals from all known versions that are currently avaliable\", \"date\": 17212608, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d7591a2c2bed60cde003668f5f1120cb\", \"url\": \"https://api.pillowcase.su/api/download/d7591a2c2bed60cde003668f5f1120cb\", \"size\": \"1.4 MB\", \"duration\": 60.31}", "aliases": ["Free", "Freeee (Ghost Town Pt. 2)"], "size": "1.4 MB"}, {"id": "freeee", "name": "Freeee [V9]", "artists": [], "producers": [], "notes": "<PERSON><PERSON><PERSON> solo demo featuring alternate lyrics and mumbles. The Kanye vocals on this version are the same as the vocals on release.", "length": "157.44", "fileDate": 15620256, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/7b73098335b42040c4affa79dd87551b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7b73098335b42040c4affa79dd87551b\", \"key\": \"<PERSON>ee\", \"title\": \"<PERSON>ee [V9]\", \"aliases\": [\"Free\", \"I Feel Free\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"<PERSON><PERSON><PERSON> solo demo featuring alternate lyrics and mumbles. The Kanye vocals on this version are the same as the vocals on release.\", \"date\": 15620256, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1a79670cf149396ce947018a126b4353\", \"url\": \"https://api.pillowcase.su/api/download/1a79670cf149396ce947018a126b4353\", \"size\": \"2.95 MB\", \"duration\": 157.44}", "aliases": ["Free", "I Feel Free", "Freeee (Ghost Town Pt. 2)"], "size": "2.95 MB"}, {"id": "freeee-19", "name": "Freeee [V10]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "An unfinished instrumental of \"<PERSON><PERSON>\" is available which features <PERSON>ign. It has the same structure as the release version, but with different instruments. Because <PERSON> recorded for \"All Mine\" on May 22nd, this version is most likely from around that date.", "length": "206.42", "fileDate": "", "leakDate": "", "availableLength": "Beat Only", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/483be130b0a732b01b43c736bdd7092b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/483be130b0a732b01b43c736bdd7092b\", \"key\": \"<PERSON>ee\", \"title\": \"<PERSON><PERSON> [V10]\", \"artists\": \"(feat. <PERSON>ign)\", \"aliases\": [\"Free\", \"I Feel Free\", \"<PERSON><PERSON> (Ghost Town Pt. 2)\"], \"description\": \"An unfinished instrumental of \\\"Freeee\\\" is available which features Ty Dolla $ign. It has the same structure as the release version, but with different instruments. Because <PERSON> recorded for \\\"All Mine\\\" on May 22nd, this version is most likely from around that date.\", \"date\": null, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1ce25b4d13701ffd1c326e2a0f12680f\", \"url\": \"https://api.pillowcase.su/api/download/1ce25b4d13701ffd1c326e2a0f12680f\", \"size\": \"3.73 MB\", \"duration\": 206.42}", "aliases": ["Free", "I Feel Free", "Freeee (Ghost Town Pt. 2)"], "size": "3.73 MB"}, {"id": "i-feel-free-20", "name": "I Feel Free [V11]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: I Feel Free REF 05.25.18\nEarliest version with <PERSON> $ign vocals. <PERSON>'s vocal take that was used for the final version, with the exception of extra mumble vocals. Has open.", "length": "255.1", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/58fb6671e1f343b4621ae22d5e00de08", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/58fb6671e1f343b4621ae22d5e00de08\", \"key\": \"I Feel Free\", \"title\": \"I Feel Free [V11]\", \"artists\": \"(feat. <PERSON>ign)\", \"aliases\": [\"I Feel Free\", \"Freeee (Ghost Town Pt. 2)\"], \"description\": \"OG Filename: I Feel Free REF 05.25.18\\nEarliest version with <PERSON> Dolla $ign vocals. Has <PERSON><PERSON><PERSON>'s vocal take that was used for the final version, with the exception of extra mumble vocals. Has open.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"89a1a8834e37afa69de01a158fcbc660\", \"url\": \"https://api.pillowcase.su/api/download/89a1a8834e37afa69de01a158fcbc660\", \"size\": \"4.51 MB\", \"duration\": 255.1}", "aliases": ["I Feel Free", "Freeee (Ghost Town Pt. 2)"], "size": "4.51 MB"}, {"id": "i-thought-about-killing-you", "name": "I Thought About Killing You [V5]", "artists": [], "producers": [], "notes": "Basically the same as release, but has some mixing differences and still includes the \"slave\" line. Stem bounce.", "length": "279.95", "fileDate": 16961184, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/c7f55e985d9f2792c589a85847715d21", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c7f55e985d9f2792c589a85847715d21\", \"key\": \"I Thought About Killing You\", \"title\": \"I Thought About Killing You [V5]\", \"aliases\": [\"I Know I Know\"], \"description\": \"Basically the same as release, but has some mixing differences and still includes the \\\"slave\\\" line. Stem bounce.\", \"date\": 16961184, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8bba323bc72860bed4b75b97f5ba3de1\", \"url\": \"https://api.pillowcase.su/api/download/8bba323bc72860bed4b75b97f5ba3de1\", \"size\": \"4.91 MB\", \"duration\": 279.95}", "aliases": ["I Know I Know"], "size": "4.91 MB"}, {"id": "let-it-go", "name": "Let It Go [V3]", "artists": [], "producers": ["<PERSON>", "MIKE DEAN"], "notes": "OG Filename: Let it Go 05.25.18 [<PERSON> and MD]\nFeatured on an early leaked tracklist for ye. <PERSON> mumble <PERSON><PERSON><PERSON> singing vocals and 3 minutes of open. Leaked after a successful groupbuy.", "length": "261.26", "fileDate": 17394912, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/a18fb677e8f8b025a38e5ff585889802", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a18fb677e8f8b025a38e5ff585889802\", \"key\": \"Let It Go\", \"title\": \"Let It Go [V3]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: Let it Go 05.25.18 [AD and MD]\\nFeatured on an early leaked tracklist for ye. <PERSON> mumble <PERSON><PERSON><PERSON> singing vocals and 3 minutes of open. Leaked after a successful groupbuy.\", \"date\": 17394912, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6fa861976a6923d369759c164af55fff\", \"url\": \"https://api.pillowcase.su/api/download/6fa861976a6923d369759c164af55fff\", \"size\": \"4.62 MB\", \"duration\": 261.26}", "aliases": [], "size": "4.62 MB"}, {"id": "no-mistakes", "name": "No Mistakes [V1]", "artists": ["<PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: No Mistakes REF 05.26.18\nEarliest known version. Differences include: production, only <PERSON> on the chorus, mumble <PERSON><PERSON><PERSON> vocals, and a completely different mix. Original snippet leaked February 3rd, 2022.", "length": "137.94", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/dfc98b11c9705de16c7a0df3174e9aae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dfc98b11c9705de16c7a0df3174e9aae\", \"key\": \"No Mistakes\", \"title\": \"No Mistakes [V1]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: No Mistakes REF 05.26.18\\nEarliest known version. Differences include: production, only <PERSON> on the chorus, mumble <PERSON><PERSON> vocals, and a completely different mix. Original snippet leaked February 3rd, 2022.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e22dc05393952401fe0f50c4853e1f5f\", \"url\": \"https://api.pillowcase.su/api/download/e22dc05393952401fe0f50c4853e1f5f\", \"size\": \"2.64 MB\", \"duration\": 137.94}", "aliases": [], "size": "2.64 MB"}, {"id": "no-mistakes-24", "name": "No Mistakes [V2]", "artists": ["<PERSON>", "<PERSON>"], "producers": [], "notes": "Further along version with a more finished vocal take with alternate lyrics that are not heard on release. Was put up for sale on TheSource in 2020.", "length": "9.35", "fileDate": 15778368, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/2fcff33e196484f5611aba33946fd3a7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2fcff33e196484f5611aba33946fd3a7\", \"key\": \"No Mistakes\", \"title\": \"No Mistakes [V2]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"description\": \"Further along version with a more finished vocal take with alternate lyrics that are not heard on release. Was put up for sale on TheSource in 2020.\", \"date\": 15778368, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"96d67e424714ef9c23dc8c0432073d79\", \"url\": \"https://api.pillowcase.su/api/download/96d67e424714ef9c23dc8c0432073d79\", \"size\": \"581 kB\", \"duration\": 9.35}", "aliases": [], "size": "581 kB"}, {"id": "take-me-to-the-light", "name": "Take Me To The Light [V1]", "artists": ["<PERSON>"], "producers": ["Kanye West", "benny blanco"], "notes": "OG Filename: Take Me To The Light REF 5.28.18\nEarly demo version. <PERSON><PERSON><PERSON> has rough vocals and elements of \"Fine Line\" are reused, including <PERSON>'s vocals and parts of the instrumental. Includes a beat switch and a short verse that was reused for \"I Thought About Killing You\" at the end. Contains lyrics from \"Freeee\".", "length": "171.69", "fileDate": 15710976, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/e44f5e234aa034154c1259e56fed0a91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e44f5e234aa034154c1259e56fed0a91\", \"key\": \"Take Me To The Light\", \"title\": \"Take Me To The Light [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON> blan<PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"OG Filename: Take Me To The Light REF 5.28.18\\nEarly demo version. <PERSON><PERSON><PERSON> has rough vocals and elements of \\\"Fine Line\\\" are reused, including <PERSON>'s vocals and parts of the instrumental. Includes a beat switch and a short verse that was reused for \\\"I Thought About Killing You\\\" at the end. Contains lyrics from \\\"Freeee\\\".\", \"date\": 15710976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"40a3f5331b44e37923fbfc4c2429fb22\", \"url\": \"https://api.pillowcase.su/api/download/40a3f5331b44e37923fbfc4c2429fb22\", \"size\": \"3.18 MB\", \"duration\": 171.69}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "3.18 MB"}, {"id": "metta-world-peace", "name": "Metta World Peace [V2]", "artists": ["<PERSON>", "The WRLDFMS <PERSON>"], "producers": ["Kanye West", "benny blanco"], "notes": "OG Filename: Metta World End REF FOR TONY\nOutro idea with <PERSON> and <PERSON> backing vocals. Instrumental is slightly evolved from V1.", "length": "44.35", "fileDate": 16714944, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/808aac228a9d9ea21ce35302cd2a37c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/808aac228a9d9ea21ce35302cd2a37c8\", \"key\": \"Metta World Peace\", \"title\": \"Metta World Peace [V2]\", \"artists\": \"(feat. <PERSON> & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON> blan<PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Take Me To The Light\"], \"description\": \"OG Filename: Metta World End REF FOR TONY\\nOutro idea with <PERSON> and <PERSON> backing vocals. Instrumental is slightly evolved from V1.\", \"date\": 16714944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"39c5f1b9e5a7cc964a7bfe957b647c70\", \"url\": \"https://api.pillowcase.su/api/download/39c5f1b9e5a7cc964a7bfe957b647c70\", \"size\": \"1.14 MB\", \"duration\": 44.35}", "aliases": ["You Still Take Me To The Light", "Take Me To The Light"], "size": "1.14 MB"}, {"id": "take-me-to-the-light-27", "name": "Take Me to the Light [V3]", "artists": ["<PERSON> and the Lights", "<PERSON> the Rapper", "<PERSON>", "<PERSON>"], "producers": ["Kanye West", "benny blanco", "<PERSON> and the Lights", "<PERSON>", "Cashmere Cat", "<PERSON><PERSON>", "<PERSON>"], "notes": "First version with <PERSON> and the Lights and <PERSON>. Features a Chance verse and minor production changes. Likely not <PERSON>' song at this point.", "length": "221.88", "fileDate": 15674688, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/e1ce8c5181470bfcef61e977b812e403", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e1ce8c5181470bfcef61e977b812e403\", \"key\": \"Take Me to the Light\", \"title\": \"Take Me to the Light [V3]\", \"artists\": \"(feat. <PERSON> and the Lights, <PERSON> the <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and the Lights, <PERSON>, <PERSON><PERSON> Cat, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"First version with <PERSON> and the Lights and <PERSON>. Features a Chance verse and minor production changes. Likely not <PERSON>' song at this point.\", \"date\": 15674688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7d2d79850af92533bac4605852d4871c\", \"url\": \"https://api.pillowcase.su/api/download/7d2d79850af92533bac4605852d4871c\", \"size\": \"3.98 MB\", \"duration\": 221.88}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "3.98 MB"}, {"id": "take-me-to-the-light-28", "name": "Take Me To The Light [V4]", "artists": ["<PERSON> and the Lights", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Kanye West", "benny blanco", "<PERSON> and the Lights", "<PERSON>", "Cashmere Cat", "<PERSON><PERSON>", "<PERSON>"], "notes": "Includes an <PERSON><PERSON> verse at the end. Unclear if <PERSON><PERSON> is doing a reference verse for <PERSON><PERSON><PERSON>, or a feature.", "length": "217.47", "fileDate": 15805152, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/d9774f1d36a9f11ea6ae4544856d599f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9774f1d36a9f11ea6ae4544856d599f\", \"key\": \"Take Me To The Light\", \"title\": \"Take Me To The Light [V4]\", \"artists\": \"(feat. <PERSON> and the Lights, <PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and the Lights, <PERSON>, <PERSON><PERSON> Cat, B<PERSON> & <PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"Includes an <PERSON><PERSON> verse at the end. Unclear if <PERSON><PERSON> is doing a reference verse for <PERSON><PERSON><PERSON>, or a feature.\", \"date\": 15805152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"17ab6d3ee462e7877b853aa95319061b\", \"url\": \"https://api.pillowcase.su/api/download/17ab6d3ee462e7877b853aa95319061b\", \"size\": \"3.91 MB\", \"duration\": 217.47}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "3.91 MB"}, {"id": "take-me-to-the-light-29", "name": "Take Me To The Light [V5]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> TakeMeToTheLight Ref1\nFya Man reference track, has <PERSON><PERSON><PERSON> vocals. Exact date is unknown, but could only be from May 30th or 31st according to the metadata.", "length": "60.92", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/7c55abe233cc23bad6f32bc976183029", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c55abe233cc23bad6f32bc976183029\", \"key\": \"Take Me To The Light\", \"title\": \"Take Me To The Light [V5]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"OG Filename: Fya TakeMeToTheLight Ref1\\nFya Man reference track, has <PERSON><PERSON><PERSON> vocals. Exact date is unknown, but could only be from May 30th or 31st according to the metadata.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2c87551a7d46145b71313e0324bcca82\", \"url\": \"https://api.pillowcase.su/api/download/2c87551a7d46145b71313e0324bcca82\", \"size\": \"1.41 MB\", \"duration\": 60.92}", "aliases": ["You Still Take Me To The Light", "Metta World Peace"], "size": "1.41 MB"}, {"id": "violent-nights", "name": "Violent Nights [V4]", "artists": [], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>", "MIKE DEAN"], "notes": "Version of the song with alternate production that includes trap drums over the instrumental. Is open verse, containing only the hook. The stems say \"brothers\" in the filename. Snippet available is a VC recording.", "length": "75.08", "fileDate": 15746400, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/4e647e583a998e78be214737a8a55566", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e647e583a998e78be214737a8a55566\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V4]\", \"artists\": \"(ref. 070 Shake) (prod. 7 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Version of the song with alternate production that includes trap drums over the instrumental. Is open verse, containing only the hook. The stems say \\\"brothers\\\" in the filename. Snippet available is a VC recording.\", \"date\": 15746400, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"16dcec86b8a4c7676ab3715690974e1d\", \"url\": \"https://api.pillowcase.su/api/download/16dcec86b8a4c7676ab3715690974e1d\", \"size\": \"1.63 MB\", \"duration\": 75.08}", "aliases": [], "size": "1.63 MB"}, {"id": "violent-nights-31", "name": "Violent Nights [V5]", "artists": ["070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG FIlename: Violent Nights REF 5.22 [FOR CyHi]\nMumble demo. Originally put up for sale on TheSource November 19th, 2020.", "length": "174.22", "fileDate": 17159904, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/2bd836694fc157ed794a89fd852c5b2c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2bd836694fc157ed794a89fd852c5b2c\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V5]\", \"artists\": \"(feat. 070 Shake) (prod. 7 <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"OG FIlename: Violent Nights REF 5.22 [FOR CyHi]\\nMumble demo. Originally put up for sale on TheSource November 19th, 2020.\", \"date\": 17159904, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e9b04468cdfb513aecdb9bbc5fc86ce7\", \"url\": \"https://api.pillowcase.su/api/download/e9b04468cdfb513aecdb9bbc5fc86ce7\", \"size\": \"3.22 MB\", \"duration\": 174.22}", "aliases": [], "size": "3.22 MB"}, {"id": "violent-nights-32", "name": "Violent Nights [V6]", "artists": ["Ty Dolla $ign", "070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Violent Nights REF 5.24\nBasically the same as the May 25th version of the song but with different mixing and is 3 seconds shorter.", "length": "200.63", "fileDate": 17330112, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/2307314ba3e1e116c8c5602bba34e9d6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2307314ba3e1e116c8c5602bba34e9d6\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V6]\", \"artists\": \"(ref. <PERSON> $ign) (feat. <PERSON> $ign & 070 Shake) (prod. 7 <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Violent Nights REF 5.24\\nBasically the same as the May 25th version of the song but with different mixing and is 3 seconds shorter.\", \"date\": 17330112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d5ac861a3e1815902fbb80ee80b43435\", \"url\": \"https://api.pillowcase.su/api/download/d5ac861a3e1815902fbb80ee80b43435\", \"size\": \"3.64 MB\", \"duration\": 200.63}", "aliases": [], "size": "3.64 MB"}, {"id": "violent-nights-33", "name": "Violent Nights [V7]", "artists": ["Ty Dolla $ign", "070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Violent Nights REF 5.25.18\nHas new <PERSON><PERSON><PERSON> vocals with <PERSON> $ign reference vocals over the mumble lines. Leaked as a bonus for \"The Storm\" groupbuy.", "length": "202.93", "fileDate": 15725664, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/fe4274b280731e93cb7015b80efa7553", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe4274b280731e93cb7015b80efa7553\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V7]\", \"artists\": \"(ref. <PERSON> $ign) (feat. <PERSON> $ign & 070 Shake) (prod. 7 <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Violent Nights REF 5.25.18\\nHas new Kanye vocals with <PERSON> <PERSON> $ign reference vocals over the mumble lines. Leaked as a bonus for \\\"The Storm\\\" groupbuy.\", \"date\": 15725664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f6cc2e541e31ac7fd0682384bce3e0e5\", \"url\": \"https://api.pillowcase.su/api/download/f6cc2e541e31ac7fd0682384bce3e0e5\", \"size\": \"3.68 MB\", \"duration\": 202.93}", "aliases": [], "size": "3.68 MB"}, {"id": "violent-nights-34", "name": "Violent Nights [V8]", "artists": ["070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "Consequence reference track. <PERSON> played on his IG live in November 2022, and again on August 11th, 2024.", "length": "110.34", "fileDate": 17233344, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/bba98c450e6c05aabb987f2f3303046c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bba98c450e6c05aabb987f2f3303046c\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V8]\", \"artists\": \"(ref. Consequence) (feat. 070 Shake) (prod. 7 <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence reference track. <PERSON> played on his IG live in November 2022, and again on August 11th, 2024.\", \"date\": 17233344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a4a1e2bd5b3a4de22dd72ecf16aae521\", \"url\": \"https://api.pillowcase.su/api/download/a4a1e2bd5b3a4de22dd72ecf16aae521\", \"size\": \"2.2 MB\", \"duration\": 110.34}", "aliases": [], "size": "2.2 MB"}, {"id": "violent-nights-35", "name": "Violent Nights [V8]", "artists": ["070 Shake"], "producers": ["7 Aurel<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "Consequence reference track. <PERSON> played on his IG live in November 2022, and again on August 11th, 2024.", "length": "94.44", "fileDate": 17233344, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/3f3c41bc52e1e71305923d7c81e4cc70", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f3c41bc52e1e71305923d7c81e4cc70\", \"key\": \"Violent Nights\", \"title\": \"Violent Nights [V8]\", \"artists\": \"(ref. Consequence) (feat. 070 Shake) (prod. 7 <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"Consequence reference track. <PERSON> played on his IG live in November 2022, and again on August 11th, 2024.\", \"date\": 17233344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4ad81b48c18b9b5a3da2f926f12eab96\", \"url\": \"https://api.pillowcase.su/api/download/4ad81b48c18b9b5a3da2f926f12eab96\", \"size\": \"1.94 MB\", \"duration\": 94.44}", "aliases": [], "size": "1.94 MB"}, {"id": "wouldn-t-leave", "name": "Wouldn't Leave [V5]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: wouldnt leave verse 3 ta<PERSON><PERSON> ref\nHas voice memo reference vocals for a verse.", "length": "58.73", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/9d3e1b6c2939c5ae4c25c94497688d74", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d3e1b6c2939c5ae4c25c94497688d74\", \"key\": \"Wouldn't Leave\", \"title\": \"Wouldn't Leave [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: wouldnt leave verse 3 tassho ref\\nHas voice memo reference vocals for a verse.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7c085ffb635807c2dd2ba0a45f934984\", \"url\": \"https://api.pillowcase.su/api/download/7c085ffb635807c2dd2ba0a45f934984\", \"size\": \"4.14 MB\", \"duration\": 58.73}", "aliases": [], "size": "4.14 MB"}, {"id": "wouldn-t-leave-37", "name": "Wouldn't Leave [V6]", "artists": ["<PERSON><PERSON><PERSON>", "PARTYNEXTDOOR", "Ty Dolla $ign"], "producers": ["Kanye West", "Ty Dolla $ign", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: Wouldn't Leave REF 05.25.18\nFirst verse is similar to release, and the third verse has mumble. The Ty <PERSON> $ign outro on this version is used as a bridge.", "length": "253.28", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/774f9fffe3aa038e88bb5a71903c6fb2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/774f9fffe3aa038e88bb5a71903c6fb2\", \"key\": \"Wouldn't Leave\", \"title\": \"Wouldn't Leave [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, PARTYNEXTDOOR & <PERSON> $ign) (prod. <PERSON><PERSON><PERSON>, <PERSON> $ign, <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Wouldn't Leave REF 05.25.18\\nFirst verse is similar to release, and the third verse has mumble. The Ty Dolla $ign outro on this version is used as a bridge.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"42a3eca629d82061b06edb8534d170fa\", \"url\": \"https://api.pillowcase.su/api/download/42a3eca629d82061b06edb8534d170fa\", \"size\": \"4.48 MB\", \"duration\": 253.28}", "aliases": [], "size": "4.48 MB"}, {"id": "wouldn-t-leave-38", "name": "Wouldn't Leave [V7]", "artists": [], "producers": ["Kanye West", "Ty Dolla $ign", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: wouldn<PERSON>ave fya 05272018 ref\n<PERSON><PERSON> said he \"did the Ye album and co-wrote the song 'Wouldn't Leave'\". His reference vocals were meant for the third verse.", "length": "34.25", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/8523d0edc7f49ca3b03089ec5f0f0229", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8523d0edc7f49ca3b03089ec5f0f0229\", \"key\": \"Wouldn't Leave\", \"title\": \"Wouldn't Leave [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> $ign, <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: wouldntleave fya 05272018 ref\\n<PERSON><PERSON> <PERSON> said he \\\"did the Ye album and co-wrote the song 'Wouldn't Leave'\\\". His reference vocals were meant for the third verse.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"87f822ba1fd1b0c71847cfba9e760e3d\", \"url\": \"https://api.pillowcase.su/api/download/87f822ba1fd1b0c71847cfba9e760e3d\", \"size\": \"980 kB\", \"duration\": 34.25}", "aliases": [], "size": "980 kB"}, {"id": "wouldn-t-leave-39", "name": "Wouldn't Leave [V7]", "artists": [], "producers": ["Kanye West", "Ty Dolla $ign", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: wouldn<PERSON>ave fya 05272018 ref\n<PERSON><PERSON> said he \"did the Ye album and co-wrote the song 'Wouldn't Leave'\". His reference vocals were meant for the third verse.", "length": "", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://www.revolt.tv/article/2022-01-27/150051/fya-man-kanye-west-donda-interview/", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.revolt.tv/article/2022-01-27/150051/fya-man-kanye-<PERSON>-donda-interview/\", \"key\": \"Wouldn't Leave\", \"title\": \"Wouldn't Leave [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> $ign, <PERSON>KE DEAN & <PERSON>)\", \"description\": \"OG Filename: wouldntleave fya 05272018 ref\\nFya Man said he \\\"did the Ye album and co-wrote the song 'Wouldn't Leave'\\\". His reference vocals were meant for the third verse.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "exctacty", "name": "Exctacty [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Exctacty Dawson Version 5.16.18\nEarly instrumental, has production differences.", "length": "238.79", "fileDate": 17159904, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/ef4512c8dfe4187ccb0dfc2d00e7786d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef4512c8dfe4187ccb0dfc2d00e7786d\", \"key\": \"Exctacty\", \"title\": \"Exctacty [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"XTCY\", \"Extacy\"], \"description\": \"OG Filename: Exctacty Dawson Version 5.16.18\\nEarly instrumental, has production differences.\", \"date\": 17159904, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e75221e650b1a42460062f12c3b8dee6\", \"url\": \"https://api.pillowcase.su/api/download/e75221e650b1a42460062f12c3b8dee6\", \"size\": \"4.25 MB\", \"duration\": 238.79}", "aliases": ["XTCY", "Extacy"], "size": "4.25 MB"}, {"id": "extacy", "name": "🗑️ Extacy [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON><PERSON> (<PERSON>)\nRough <PERSON> reference track, he does one bar every ~13 seconds.", "length": "239.88", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/b97f2a0beb11e6ef322655649fdc09c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b97f2a0beb11e6ef322655649fdc09c0\", \"key\": \"Extacy\", \"title\": \"\\ud83d\\uddd1\\ufe0f Extacy [V3]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"XTCY\", \"Exctacty\"], \"description\": \"OG Filename: Extasy (<PERSON>)\\nRough <PERSON> reference track, he does one bar every ~13 seconds.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"80df94ae0af7869b3bd04cf3807440eb\", \"url\": \"https://api.pillowcase.su/api/download/80df94ae0af7869b3bd04cf3807440eb\", \"size\": \"7.04 MB\", \"duration\": 239.88}", "aliases": ["XTCY", "Exctacty"], "size": "7.04 MB"}, {"id": "xtasy", "name": "Xtasy [V4]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> & <PERSON> - Xtasy_1\nCut-down <PERSON> reference track, with some re-recorded lines, and an added verse. Filename suggests it has 070 Shake vocals, but there are none. Original snippet leaked November 18th, 2022.", "length": "168.87", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/6aea9363c6f4413c30e5223482ee627f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6aea9363c6f4413c30e5223482ee627f\", \"key\": \"Xtasy\", \"title\": \"Xtasy [V4]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Extacy\", \"Exctacty\", \"XTCY\"], \"description\": \"OG Filename: <PERSON> - Xtasy_1\\nCut-down <PERSON> reference track, with some re-recorded lines, and an added verse. Filename suggests it has 070 Shake vocals, but there are none. Original snippet leaked November 18th, 2022.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c41ecc0cef7cdc8f38849bf772bc0e2\", \"url\": \"https://api.pillowcase.su/api/download/5c41ecc0cef7cdc8f38849bf772bc0e2\", \"size\": \"5.91 MB\", \"duration\": 168.87}", "aliases": ["Extacy", "Exctacty", "XTCY"], "size": "5.91 MB"}, {"id": "exctacty-43", "name": "Exctacty [V6]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Exctacty Dawson Version REF (5.20.18)\nAnother early instrumental, with production differences. Significantly shorter compared to the May 16th version.", "length": "110.66", "fileDate": 17159904, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/28428327b5e53173b26f34f06c07546d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/28428327b5e53173b26f34f06c07546d\", \"key\": \"Exctacty\", \"title\": \"Exctacty [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"XTCY\", \"Extacy\"], \"description\": \"OG Filename: Exctacty Dawson Version REF (5.20.18)\\nAnother early instrumental, with production differences. Significantly shorter compared to the May 16th version.\", \"date\": 17159904, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"60372faac5fd84f2378000ecec056371\", \"url\": \"https://api.pillowcase.su/api/download/60372faac5fd84f2378000ecec056371\", \"size\": \"2.2 MB\", \"duration\": 110.66}", "aliases": ["XTCY", "Extacy"], "size": "2.2 MB"}, {"id": "exctacty-44", "name": "Exctacty [V7]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Exctacty 05.24.18\nAnother version of the instrumental with production differences.", "length": "131.94", "fileDate": 17159904, "leakDate": "", "availableLength": "Beat Only", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/f24f07ba901fed462213c6b964beb5d6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f24f07ba901fed462213c6b964beb5d6\", \"key\": \"Exctacty\", \"title\": \"Exctacty [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"XTCY\", \"Extacy\"], \"description\": \"OG Filename: Exctacty 05.24.18\\nAnother version of the instrumental with production differences.\", \"date\": 17159904, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3cae23df7278e2e22026fa04d0a56c6d\", \"url\": \"https://api.pillowcase.su/api/download/3cae23df7278e2e22026fa04d0a56c6d\", \"size\": \"2.54 MB\", \"duration\": 131.94}", "aliases": ["XTCY", "Extacy"], "size": "2.54 MB"}, {"id": "extacty", "name": "⭐ Extacty [V8]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Exctacty REF 5.28.18\nSimilar to release but with completely different lyrics and some production differences. Original snippet surfaced September 25th, 2019.", "length": "143.39", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/42b7427a68b4ddbb98fbca040398d122", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/42b7427a68b4ddbb98fbca040398d122\", \"key\": \"Extacty\", \"title\": \"\\u2b50 Extacty [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Extacy\", \"Exctacty\", \"XTCY\"], \"description\": \"OG Filename: Exctacty REF 5.28.18\\nSimilar to release but with completely different lyrics and some production differences. Original snippet surfaced September 25th, 2019.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c69ce9b3eb957fbf42ccef6129565731\", \"url\": \"https://api.pillowcase.su/api/download/c69ce9b3eb957fbf42ccef6129565731\", \"size\": \"5.5 MB\", \"duration\": 143.39}", "aliases": ["Extacy", "Exctacty", "XTCY"], "size": "5.5 MB"}, {"id": "exctacty-46", "name": "Exctacty [V9]", "artists": [], "producers": ["Kanye West", "<PERSON>", "???"], "notes": "OG Filename: Exctacty 05.30.18 REF 93bpm\nReworked version of \"XTCY\". Has a re-recorded verse, similar to the released version. Has vocal changes in verse 1, mumble in the chorus, and then just instrumental with a synth.", "length": "127.55", "fileDate": 17159904, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/91f2538240615067363cea891701545e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91f2538240615067363cea891701545e\", \"key\": \"Exctacty\", \"title\": \"Exctacty [V9]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & ???)\", \"aliases\": [\"XTCY\", \"Extacy\"], \"description\": \"OG Filename: Exctacty 05.30.18 REF 93bpm\\nReworked version of \\\"XTCY\\\". Has a re-recorded verse, similar to the released version. Has vocal changes in verse 1, mumble in the chorus, and then just instrumental with a synth.\", \"date\": 17159904, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f74a7e485edc956806e21900a2a5b109\", \"url\": \"https://api.pillowcase.su/api/download/f74a7e485edc956806e21900a2a5b109\", \"size\": \"2.47 MB\", \"duration\": 127.55}", "aliases": ["XTCY", "Extacy"], "size": "2.47 MB"}, {"id": "exctacty-47", "name": "Exctacty [V10]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Exctacty 05.31.18 REF\nBasically the same as release but with worse mixing, and one mumble bar at 2:26.", "length": "178.65", "fileDate": 17330112, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/63b09add2e83812fa6b3df8cc00cc332", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63b09add2e83812fa6b3df8cc00cc332\", \"key\": \"Exctacty\", \"title\": \"Exctacty [V10]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"XTCY\", \"Extacy\"], \"description\": \"OG Filename: Exctacty 05.31.18 REF\\nBasically the same as release but with worse mixing, and one mumble bar at 2:26.\", \"date\": 17330112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e9d1c01a14f447eb1629eb2bb89d94f8\", \"url\": \"https://api.pillowcase.su/api/download/e9d1c01a14f447eb1629eb2bb89d94f8\", \"size\": \"3.29 MB\", \"duration\": 178.65}", "aliases": ["XTCY", "Extacy"], "size": "3.29 MB"}, {"id": "yikes", "name": "Yikes [V5]", "artists": [], "producers": [], "notes": "Tassho Pearce reference track.", "length": "128.47", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/cf83c9d641041aed60d0b06016349d5b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf83c9d641041aed60d0b06016349d5b\", \"key\": \"Yikes\", \"title\": \"Yikes [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"<PERSON><PERSON><PERSON> reference track.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9f80fbb5808b34c317e9a476fc9c2980\", \"url\": \"https://api.pillowcase.su/api/download/9f80fbb5808b34c317e9a476fc9c2980\", \"size\": \"5.26 MB\", \"duration\": 128.47}", "aliases": [], "size": "5.26 MB"}, {"id": "yikes-49", "name": "Yikes [V6]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>erre <PERSON>", "Ape<PERSON>"], "notes": "OG Filename: YIKES Ref 05.25.28\nHas production and mixing differences, and mumble verses. <PERSON> can be heard doing an \"aye\" adlib after the first hook.", "length": "128.6", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/f69ae6d066bd1e55c6999e9a97101452", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f69ae6d066bd1e55c6999e9a97101452\", \"key\": \"Yikes\", \"title\": \"Yikes [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & A<PERSON><PERSON>)\", \"description\": \"OG Filename: YIKES Ref 05.25.28\\nHas production and mixing differences, and mumble verses. <PERSON> can be heard doing an \\\"aye\\\" adlib after the first hook.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c4596360df0259f0e88c709b9b03506\", \"url\": \"https://api.pillowcase.su/api/download/5c4596360df0259f0e88c709b9b03506\", \"size\": \"2.49 MB\", \"duration\": 128.6}", "aliases": [], "size": "2.49 MB"}, {"id": "yikes-50", "name": "✨ Yikes [V7]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>erre <PERSON>", "Ape<PERSON>"], "notes": "OG Filename: Yikes REF 05.26.18 3am\nHas a fully finished alt verse, and features production and mixing differences. <PERSON> can be heard doing an \"aye\" adlib after the first hook.", "length": "142.71", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/85635434f4b1ad30ca211809761a8fea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/85635434f4b1ad30ca211809761a8fea\", \"key\": \"Yikes\", \"title\": \"\\u2728 Yikes [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & A<PERSON><PERSON>)\", \"description\": \"OG Filename: Yikes REF 05.26.18 3am\\nHas a fully finished alt verse, and features production and mixing differences. <PERSON> can be heard doing an \\\"aye\\\" adlib after the first hook.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"11ffa6f562c440e26e5b17e04e42851f\", \"url\": \"https://api.pillowcase.su/api/download/11ffa6f562c440e26e5b17e04e42851f\", \"size\": \"2.71 MB\", \"duration\": 142.71}", "aliases": [], "size": "2.71 MB"}, {"id": "yikes-51", "name": "Yikes [V9]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>erre <PERSON>", "Ape<PERSON>"], "notes": "Features a slightly different mixdown and vocal takes with an unfinished last verse. References <PERSON> calling for people to pray for <PERSON><PERSON><PERSON>, which occurred after his TMZ meltdown. Leaked after a groupbuy.", "length": "194.87", "fileDate": 15701472, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/3d8135efe269336a913f7419d11f30cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3d8135efe269336a913f7419d11f30cf\", \"key\": \"Yikes\", \"title\": \"Yikes [V9]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"Features a slightly different mixdown and vocal takes with an unfinished last verse. References <PERSON> calling for people to pray for <PERSON><PERSON><PERSON>, which occurred after his TMZ meltdown. Leaked after a groupbuy.\", \"date\": 15701472, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"91ead1470f1854b733c10a7774bf61f1\", \"url\": \"https://api.pillowcase.su/api/download/91ead1470f1854b733c10a7774bf61f1\", \"size\": \"3.55 MB\", \"duration\": 194.87}", "aliases": [], "size": "3.55 MB"}, {"id": "yikes-52", "name": "✨ Yikes [V10]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>erre <PERSON>", "MIKE DEAN", "Ape<PERSON>"], "notes": "Version with new production that includes synths, piano and electric guitar. Said by Waterfalls to be dated from a couple days before the release of ye. Also stated to have been made before the track was worked on by MIKE DEAN, but this is not true.", "length": "83.6", "fileDate": 16816896, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/652e6c3c7cd5f8eb0022ea94dd36c527", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/652e6c3c7cd5f8eb0022ea94dd36c527\", \"key\": \"Yikes\", \"title\": \"\\u2728 Yikes [V10]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MIKE DEAN & Apex Martin)\", \"description\": \"Version with new production that includes synths, piano and electric guitar. Said by <PERSON><PERSON> to be dated from a couple days before the release of ye. Also stated to have been made before the track was worked on by MIKE DEAN, but this is not true.\", \"date\": 16816896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"889bae9327893cf7e7be44072bcec4a5\", \"url\": \"https://api.pillowcase.su/api/download/889bae9327893cf7e7be44072bcec4a5\", \"size\": \"1.77 MB\", \"duration\": 83.6}", "aliases": [], "size": "1.77 MB"}, {"id": "yikes-53", "name": "Yikes [V11]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>erre <PERSON>", "MIKE DEAN", "Ape<PERSON>"], "notes": "Alternate mix that first surfaced on dbree in July 2019. Contains a tag at the end of the song.", "length": "188", "fileDate": 16215552, "leakDate": "", "availableLength": "Tagged", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/3441a69da3ca9aade948c303599feae0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3441a69da3ca9aade948c303599feae0\", \"key\": \"Yikes\", \"title\": \"Yikes [V11]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>KE <PERSON> & Ape<PERSON>)\", \"description\": \"Alternate mix that first surfaced on dbree in July 2019. Contains a tag at the end of the song.\", \"date\": 16215552, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"588bffd79d402368166ed39c80626388\", \"url\": \"https://api.pillowcase.su/api/download/588bffd79d402368166ed39c80626388\", \"size\": \"3.44 MB\", \"duration\": 188}", "aliases": [], "size": "3.44 MB"}, {"id": "yikes-54", "name": "Yikes [V13]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>erre <PERSON>", "MIKE DEAN", "Ape<PERSON>"], "notes": "OG Filename: Yikes Ref 2\nAlternate mix.", "length": "201.43", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/5a74f329a8f6958b33221e15d9696334", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5a74f329a8f6958b33221e15d9696334\", \"key\": \"Yikes\", \"title\": \"Yi<PERSON> [V13]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MIKE DEAN & Ape<PERSON>)\", \"description\": \"OG Filename: Yikes Ref 2\\nAlternate mix.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"397091b62428f4c91e7e8ab50031ae83\", \"url\": \"https://api.pillowcase.su/api/download/397091b62428f4c91e7e8ab50031ae83\", \"size\": \"3.65 MB\", \"duration\": 201.43}", "aliases": [], "size": "3.65 MB"}, {"id": "open-letter", "name": "<PERSON> the Rapper - Open Letter", "artists": [], "producers": [], "notes": "OG Filename: OpenLetter_WithSFX_GJ_52218\nSolo Chance song that was considered for Good Ass Job. Unknown if <PERSON><PERSON><PERSON> ever recorded for this. Leaked as part of a 62 gigabyte mass leak.", "length": "160.05", "fileDate": 17200512, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/d61d4f7b9638fc92cf13e0d0130424f7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d61d4f7b9638fc92cf13e0d0130424f7\", \"key\": \"Open Letter\", \"title\": \"<PERSON> the Rapper - Open Letter\", \"aliases\": [\"BreakItDown\"], \"description\": \"OG Filename: OpenLetter_WithSFX_GJ_52218\\nSolo Chance song that was considered for Good Ass Job. Unknown if <PERSON><PERSON><PERSON> ever recorded for this. Leaked as part of a 62 gigabyte mass leak.\", \"date\": 17200512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4dda83b6ad86cdd9f892b5042f84acf1\", \"url\": \"https://api.pillowcase.su/api/download/4dda83b6ad86cdd9f892b5042f84acf1\", \"size\": \"2.99 MB\", \"duration\": 160.05}", "aliases": ["BreakItDown"], "size": "2.99 MB"}, {"id": "bad-people-56", "name": "Nas - Bad People [V9]", "artists": ["Kanye West"], "producers": ["Kanye West", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Bad People REF 6.14.18\nVersion with only <PERSON><PERSON><PERSON> vocals. Made the day before <PERSON><PERSON> released. Leaked after a groupbuy.", "length": "220.99", "fileDate": 17169408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/deea27eab8ef8eb83bf3d739dda01da2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/deea27eab8ef8eb83bf3d739dda01da2\", \"key\": \"Bad People\", \"title\": \"Nas - Bad People [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>z<PERSON>aB<PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"OG Filename: Bad People REF 6.14.18\\nVersion with only <PERSON><PERSON><PERSON> vocals. Made the day before <PERSON><PERSON> released. Leaked after a groupbuy.\", \"date\": 17169408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ddb96a1add5c8d1e62da915a825de305\", \"url\": \"https://api.pillowcase.su/api/download/ddb96a1add5c8d1e62da915a825de305\", \"size\": \"4.23 MB\", \"duration\": 220.99}", "aliases": ["White Label"], "size": "4.23 MB"}, {"id": "bad-people-57", "name": "Nas - Bad People [V10]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Bad People BOUNCE 6.14.18\nVersion with only <PERSON><PERSON> vocals. Made the day before <PERSON><PERSON> released. Leaked after a groupbuy.", "length": "220.99", "fileDate": 17169408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/ad2638dfb9523df9b3c2d71aed352177", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad2638dfb9523df9b3c2d71aed352177\", \"key\": \"Bad People\", \"title\": \"Nas - Bad People [V10]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>DaB<PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"White Label\"], \"description\": \"OG Filename: Bad People BOUNCE 6.14.18\\nVersion with only <PERSON><PERSON> vocals. Made the day before <PERSON><PERSON> released. Leaked after a groupbuy.\", \"date\": 17169408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fb13d58cfc819acd64736f2e7e206f87\", \"url\": \"https://api.pillowcase.su/api/download/fb13d58cfc819acd64736f2e7e206f87\", \"size\": \"4.23 MB\", \"duration\": 220.99}", "aliases": ["White Label"], "size": "4.23 MB"}, {"id": "cops-shot-the-kid", "name": "Nas - <PERSON><PERSON> Shot The Kid [V2]", "artists": ["Kanye West", "<PERSON> 3000"], "producers": ["Kanye West"], "notes": "Version with a cut André 3000 feature. He didn't want his verse on the final song. Played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.", "length": "73.03", "fileDate": 16033248, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/2b016c527fc7516d17fa8052869abc97", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b016c527fc7516d17fa8052869abc97\", \"key\": \"<PERSON><PERSON> Shot The Kid\", \"title\": \"<PERSON><PERSON> <PERSON> <PERSON><PERSON> Shot The Kid [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Andr\\u00e9 3000) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Version with a cut Andr\\u00e9 3000 feature. He didn't want his verse on the final song. Played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.\", \"date\": 16033248, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"be64df736c19464840ee9497ec9ca237\", \"url\": \"https://api.pillowcase.su/api/download/be64df736c19464840ee9497ec9ca237\", \"size\": \"1.02 MB\", \"duration\": 73.03}", "aliases": [], "size": "1.02 MB"}, {"id": "everything", "name": "Nas - everything [V2]", "artists": ["Kanye West", "The-Dream", "<PERSON>"], "producers": [], "notes": "Mumble version that was later punched in by <PERSON><PERSON><PERSON>. Was played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.", "length": "", "fileDate": 16033248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/f0d2f27d91d65a8ca45846e44ef03389", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0d2f27d91d65a8ca45846e44ef03389\", \"key\": \"everything\", \"title\": \"Nas - everything [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Mumble version that was later punched in by <PERSON><PERSON><PERSON>. Was played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.\", \"date\": 16033248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "finding-forever", "name": "Nas - Finding Forever [V3]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: FINDING FOREVER NAS X ANT X BON IVER...\nFeatures <PERSON><PERSON>. Original VC recording surfaced on dbree in 2020. Leaked as a bonus for the \"This Is The Glory\" groupbuy.", "length": "164.19", "fileDate": 16638048, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/4b404dfe451861d847d0f22a4d6450bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4b404dfe451861d847d0f22a4d6450bb\", \"key\": \"Finding Forever\", \"title\": \"Nas - Finding Forever [V3]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>AN & <PERSON>)\", \"aliases\": [\"Simple Things\"], \"description\": \"OG Filename: FINDING FOREVER NAS X ANT X BON IVER...\\nFeatures Ant Clemons. Original VC recording surfaced on dbree in 2020. Leaked as a bonus for the \\\"This Is The Glory\\\" groupbuy.\", \"date\": 16638048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f87a8b91d2d473a9c07c94912ae5dfe\", \"url\": \"https://api.pillowcase.su/api/download/8f87a8b91d2d473a9c07c94912ae5dfe\", \"size\": \"3.06 MB\", \"duration\": 164.19}", "aliases": ["Simple Things"], "size": "3.06 MB"}, {"id": "we-got-love", "name": "<PERSON><PERSON> - We Got Love [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: We Got Love_ref 1\nNOVA WAV reference track. Has different production compared to later versions along with an extra verse.", "length": "159.07", "fileDate": 16664832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/f3f91285210f94c5d69ff902f554184e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f3f91285210f94c5d69ff902f554184e\", \"key\": \"We Got Love\", \"title\": \"<PERSON><PERSON> - We Got Love [V1]\", \"artists\": \"(ref. NOVA WAV) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: We Got Love_ref 1\\nNOVA WAV reference track. Has different production compared to later versions along with an extra verse.\", \"date\": 16664832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8cc9a3a5a6880310ac48ba55ca44fef9\", \"url\": \"https://api.pillowcase.su/api/download/8cc9a3a5a6880310ac48ba55ca44fef9\", \"size\": \"5.75 MB\", \"duration\": 159.07}", "aliases": [], "size": "5.75 MB"}, {"id": "we-got-love-62", "name": "<PERSON><PERSON> - We Got Love [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Played at the K.T.S.E. listening party. Slightly different from release.", "length": "210.08", "fileDate": 15296256, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/8982ff713c316468f65a1eddbd40f3ab", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8982ff713c316468f65a1eddbd40f3ab\", \"key\": \"We Got Love\", \"title\": \"<PERSON><PERSON> - We Got Love [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Played at the K.T.S.E. listening party. Slightly different from release.\", \"date\": 15296256, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f59c7e42af691178cd56d9e70ac40e32\", \"url\": \"https://api.pillowcase.su/api/download/f59c7e42af691178cd56d9e70ac40e32\", \"size\": \"3.79 MB\", \"duration\": 210.08}", "aliases": [], "size": "3.79 MB"}, {"id": "we-got-love-63", "name": "<PERSON><PERSON> - We Got Love [V3]", "artists": ["Ms. <PERSON><PERSON>"], "producers": ["Kanye West"], "notes": "OG Filename: WE GOT LOVE\nScrapped from K.T.S.E. at the last second, being dated the day of the album's release. Full non-OG file leaked following the listening party in July 2018 and later repurposed for <PERSON><PERSON>, but finally officially released for her following album.", "length": "245.13", "fileDate": 17178912, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "ye", "originalUrl": "https://pillowcase.su/f/0d83b0827d085b01b997cbdfa0a3b3b5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d83b0827d085b01b997cbdfa0a3b3b5\", \"key\": \"We Got Love\", \"title\": \"<PERSON><PERSON> - We Got Love [V3]\", \"artists\": \"(feat. Ms. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: WE GOT LOVE\\nScrapped from K.T.S.E. at the last second, being dated the day of the album's release. Full non-OG file leaked following the listening party in July 2018 and later repurposed for <PERSON><PERSON>, but finally officially released for her following album.\", \"date\": 17178912, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eb19f658a85e6b180dd7867264dbfccb\", \"url\": \"https://api.pillowcase.su/api/download/eb19f658a85e6b180dd7867264dbfccb\", \"size\": \"4.54 MB\", \"duration\": 245.13}", "aliases": [], "size": "4.54 MB"}]}