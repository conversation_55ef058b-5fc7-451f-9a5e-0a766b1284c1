{"id": "yeezus-2", "name": "Yeezus 2", "description": "After <PERSON> and <PERSON><PERSON><PERSON> cut down <PERSON><PERSON><PERSON> to the final ten tracks, <PERSON><PERSON><PERSON> still saw potential in much of the cut material. Thus, shortly after <PERSON>ez<PERSON> was released, an EP of leftovers titled Lost Yeezus was already being teased. The project then evolved into a full-fledged album of mostly new material, with Yeezus 2 acting as a codename before they could choose the last name. This project would develop into So Help Me God as the songs evolved.", "backgroundColor": "rgb(50, 46, 40)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17HoPY6oLKrtszZ5-LeYaEwRUtRHtqYq9N7pAJnWfERq6Mka1ryrAmEBOBvtrgLaHLqzZJsEOo7qBWygXd2Rb4agEe-lDNxSYtJZ7p5nS_flPa3EeFRnDdVtrbKS_2pORzDOGpu4VTb5-hxGv2w?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "about-time", "name": "About Time", "artists": [], "producers": [], "notes": "OG Filename: KW - About Time Ref (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble. Sam<PERSON> \"He Said Goodbye\" by <PERSON><PERSON>.", "length": "42.65", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/ede72dd454c0a0cdccd9f131e3b24879", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ede72dd454c0a0cdccd9f131e3b24879\", \"key\": \"About Time\", \"title\": \"About Time\", \"description\": \"OG Filename: KW - About Time Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble. Samples \\\"He Said Goodbye\\\" by <PERSON><PERSON>.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3c3ff30fe3bd64cff99868a29fde50f9\", \"url\": \"https://api.pillowcase.su/api/download/3c3ff30fe3bd64cff99868a29fde50f9\", \"size\": \"1.95 MB\", \"duration\": 42.65}", "aliases": [], "size": "1.95 MB"}, {"id": "afta-u", "name": "Afta U [V1]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: 20131108 160106\nOriginal iPhone recording, from a few days prior to the earliest known copy of Yeezus 2. Features additional chatting from <PERSON><PERSON><PERSON>, including mentions of wanting a choir to mimic the vocals in the instrumental. Leaked alongside the sample loop titled \"Afta U\".", "length": "50.18", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/83d13c595ce09b0d64dbb83fc7dea68f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/83d13c595ce09b0d64dbb83fc7dea68f\", \"key\": \"Afta U\", \"title\": \"Afta U [V1]\", \"artists\": \"(prod. Dom $olo)\", \"aliases\": [\"After You\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: 20131108 160106\\nOriginal iPhone recording, from a few days prior to the earliest known copy of Yeezus 2. Features additional chatting from <PERSON><PERSON><PERSON>, including mentions of wanting a choir to mimic the vocals in the instrumental. Leaked alongside the sample loop titled \\\"Afta U\\\".\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c345332561ba5e872e0ef190b3fbbd26\", \"url\": \"https://api.pillowcase.su/api/download/c345332561ba5e872e0ef190b3fbbd26\", \"size\": \"1.66 MB\", \"duration\": 50.18}", "aliases": ["After You", "Coming Home", "Reaper"], "size": "1.66 MB"}, {"id": "after-you", "name": "After You [V2]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: KW - Dom $olo - After You\nFound in the earliest known copy of Yeezus 2. Shorter freestyle than later versions. Most likely dated November 11th, alongside the other Dom $olo demo found in this Yeezus 2 copy. Shares a very similar beat as <PERSON><PERSON><PERSON>'s \"Coming Home\" as well as <PERSON><PERSON>'s \"Reaper\".", "length": "86.51", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e80e3044ee45c6b47793775c8e8f68da", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e80e3044ee45c6b47793775c8e8f68da\", \"key\": \"After You\", \"title\": \"After You [V2]\", \"artists\": \"(prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: KW - Dom $olo - After You\\nFound in the earliest known copy of Yeezus 2. Shorter freestyle than later versions. Most likely dated November 11th, alongside the other Dom $olo demo found in this Yeezus 2 copy. Shares a very similar beat as <PERSON><PERSON><PERSON>'s \\\"Coming Home\\\" as well as <PERSON><PERSON>'s \\\"Reaper\\\".\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"adb1ec12f524ff6690965ba7d708ce2f\", \"url\": \"https://api.pillowcase.su/api/download/adb1ec12f524ff6690965ba7d708ce2f\", \"size\": \"2.65 MB\", \"duration\": 86.51}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "2.65 MB"}, {"id": "after-you-4", "name": "After You [V3]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: KW - After You Ref (1.7.14), KW - afta u loop\nSimilar to the April version, with less production.", "length": "84.3", "fileDate": 17095104, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c64a6bd54db7aea60f06bcebb917a7b0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c64a6bd54db7aea60f06bcebb917a7b0\", \"key\": \"After You\", \"title\": \"After You [V3]\", \"artists\": \"(prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: KW - After You Ref (1.7.14), KW - afta u loop\\nSimilar to the April version, with less production.\", \"date\": 17095104, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"edb0bbeafeefdc7d54d4071d15774bbd\", \"url\": \"https://api.pillowcase.su/api/download/edb0bbeafeefdc7d54d4071d15774bbd\", \"size\": \"2.61 MB\", \"duration\": 84.3}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "2.61 MB"}, {"id": "after-you-5", "name": "After You [V4]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: KW - After <PERSON> (2.17.14)\n<PERSON> reference track.", "length": "149.37", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/bfcda39e70f955d88b4b2b7198091b26", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bfcda39e70f955d88b4b2b7198091b26\", \"key\": \"After You\", \"title\": \"After You [V4]\", \"artists\": \"(ref. KIRBY) (prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: KW - After <PERSON> (2.17.14)\\nKirby Lauryen reference track.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"91c5d90ab4c826c1240d389dc4a90110\", \"url\": \"https://api.pillowcase.su/api/download/91c5d90ab4c826c1240d389dc4a90110\", \"size\": \"3.65 MB\", \"duration\": 149.37}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "3.65 MB"}, {"id": "after-you-6", "name": "After You [V5]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: After You (<PERSON> 2.28.14)\n<PERSON> reference track.", "length": "184.97", "fileDate": 17095104, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/efc2bf45942d9ea507263e06ab1e8fdc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/efc2bf45942d9ea507263e06ab1e8fdc\", \"key\": \"After You\", \"title\": \"After You [V5]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: After You (<PERSON>f 2.28.14)\\nAndrea Martin reference track.\", \"date\": 17095104, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"479b58da52128ba2d02a13529e906755\", \"url\": \"https://api.pillowcase.su/api/download/479b58da52128ba2d02a13529e906755\", \"size\": \"4.22 MB\", \"duration\": 184.97}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "4.22 MB"}, {"id": "after-you-7", "name": "After You [V6]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: After You (<PERSON> Dew Ref 3.1.14)\nSam Dew reference track.", "length": "161.85", "fileDate": 17095104, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b67b0ee159adf94cc39c71fdcb1ceaf6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b67b0ee159adf94cc39c71fdcb1ceaf6\", \"key\": \"After You\", \"title\": \"After You [V6]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: After You (Sam Dew Ref 3.1.14)\\nSam Dew reference track.\", \"date\": 17095104, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b050eb7da737591bd58257a9abc1c3e6\", \"url\": \"https://api.pillowcase.su/api/download/b050eb7da737591bd58257a9abc1c3e6\", \"size\": \"3.85 MB\", \"duration\": 161.85}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "3.85 MB"}, {"id": "all-your-fault", "name": "All Your Fault [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - All Your Fault Ref (1.7.14)\nEarliest known version of the song. Has less vocal effects than later versions. Seen on a January 12th tracklist.", "length": "143.94", "fileDate": 17021664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/8deaa1845f88eef01024fcdaeeb99c34", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8deaa1845f88eef01024fcdaeeb99c34\", \"key\": \"All Your Fault\", \"title\": \"All Your Fault [V1]\", \"aliases\": [\"Tom Cruise\"], \"description\": \"OG Filename: KW - All Your Fault Ref (1.7.14)\\nEarliest known version of the song. Has less vocal effects than later versions. Seen on a January 12th tracklist.\", \"date\": 17021664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7f47505a751ce25e9276307c0173fec9\", \"url\": \"https://api.pillowcase.su/api/download/7f47505a751ce25e9276307c0173fec9\", \"size\": \"3.57 MB\", \"duration\": 143.94}", "aliases": ["<PERSON>"], "size": "3.57 MB"}, {"id": "tom-cruise", "name": "<PERSON> [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON> (2.11.14)\nIncluded on a whiteboard from early 2014. Contains mostly mumble vocals. Was later given to <PERSON>, released as \"All Your Fault\".", "length": "144.09", "fileDate": 15723936, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/7267fc39dde41b557c102c00d027eef8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7267fc39dde41b557c102c00d027eef8\", \"key\": \"Tom Cruise\", \"title\": \"Tom Cruise [V2]\", \"aliases\": [\"All Your Fault\"], \"description\": \"OG Filename: KW - <PERSON> (2.11.14)\\nIncluded on a whiteboard from early 2014. Contains mostly mumble vocals. Was later given to <PERSON>, released as \\\"All Your Fault\\\".\", \"date\": 15723936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ddd893861c7f2e0c06b1958bf5ea5532\", \"url\": \"https://api.pillowcase.su/api/download/ddd893861c7f2e0c06b1958bf5ea5532\", \"size\": \"3.57 MB\", \"duration\": 144.09}", "aliases": ["All Your Fault"], "size": "3.57 MB"}, {"id": "back-up-off-the-ledge", "name": "Back Up Off The Ledge", "artists": [], "producers": [], "notes": "OG Filename: KW - Back Up Off The Ledge Ref (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble and a beat switch.", "length": "217.34", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e0e4e22e3b4e4ee73867ebf7035e25a4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e0e4e22e3b4e4ee73867ebf7035e25a4\", \"key\": \"Back Up Off The Ledge\", \"title\": \"Back Up Off The Ledge\", \"description\": \"OG Filename: KW - Back Up Off The Ledge Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble and a beat switch.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7d0bdb83dd1e95f261753fcab9652e63\", \"url\": \"https://api.pillowcase.su/api/download/7d0bdb83dd1e95f261753fcab9652e63\", \"size\": \"4.74 MB\", \"duration\": 217.34}", "aliases": [], "size": "4.74 MB"}, {"id": "big-body", "name": "Big Body [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Big Body Ref (1.8.14)\nFirst version of \"Sanctified\" by <PERSON> as it was originally a solo-Ye Yeezus 2 idea. Exact differencies are unknown. <PERSON> can be heard in the bleed for <PERSON>'s vocals. Snippets leaked December 5th, 2024.", "length": "5.54", "fileDate": 17333568, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/eea31d1252d7a702a498c4922187ec1d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eea31d1252d7a702a498c4922187ec1d\", \"key\": \"Big Body\", \"title\": \"Big Body [V1]\", \"aliases\": [\"Sanctified\"], \"description\": \"OG Filename: KW - Big Body Ref (1.8.14)\\nFirst version of \\\"Sanctified\\\" by <PERSON> as it was originally a solo-Ye Yeezus 2 idea. Exact differencies are unknown. <PERSON> can be heard in the bleed for <PERSON>'s vocals. Snippets leaked December 5th, 2024.\", \"date\": 17333568, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"95c7d06fe823d21a9aa29b1498bb0a18\", \"url\": \"https://api.pillowcase.su/api/download/95c7d06fe823d21a9aa29b1498bb0a18\", \"size\": \"1.35 MB\", \"duration\": 5.54}", "aliases": ["Sanctified"], "size": "1.35 MB"}, {"id": "big-body-12", "name": "Big Body [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Big Body Ref (1.8.14)\nFirst version of \"Sanctified\" by <PERSON> as it was originally a solo-Ye Yeezus 2 idea. Exact differencies are unknown. <PERSON> can be heard in the bleed for <PERSON>'s vocals. Snippets leaked December 5th, 2024.", "length": "6.9", "fileDate": 17333568, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/48585ed5dd779f2b6e6337a3d20e315d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48585ed5dd779f2b6e6337a3d20e315d\", \"key\": \"Big Body\", \"title\": \"Big Body [V1]\", \"aliases\": [\"Sanctified\"], \"description\": \"OG Filename: KW - Big Body Ref (1.8.14)\\nFirst version of \\\"Sanctified\\\" by <PERSON> as it was originally a solo-Ye Yeezus 2 idea. Exact differencies are unknown. <PERSON> can be heard in the bleed for <PERSON>'s vocals. Snippets leaked December 5th, 2024.\", \"date\": 17333568, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5eb6ca1b503d726f5e42de9474c809b6\", \"url\": \"https://api.pillowcase.su/api/download/5eb6ca1b503d726f5e42de9474c809b6\", \"size\": \"1.37 MB\", \"duration\": 6.9}", "aliases": ["Sanctified"], "size": "1.37 MB"}, {"id": "big-body-13", "name": "Big Body [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - Big Body With <PERSON> Idea (1.8.14)\nOriginal version of \"Sanctified\" with vocals from <PERSON>, unclear if it's a feature or a reference. Snippet leaked December 5th, 2024.", "length": "12.04", "fileDate": 17333568, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/109c85b927d5e4d1773b1fb4d78871e6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/109c85b927d5e4d1773b1fb4d78871e6\", \"key\": \"Big Body\", \"title\": \"Big Body [V2]\", \"aliases\": [\"Sanctified\"], \"description\": \"OG Filename: KW - Big Body With Hook Idea (1.8.14)\\nOriginal version of \\\"Sanctified\\\" with vocals from <PERSON>, unclear if it's a feature or a reference. Snippet leaked December 5th, 2024.\", \"date\": 17333568, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c57060700bbe8493a330ed554bbe1c4\", \"url\": \"https://api.pillowcase.su/api/download/6c57060700bbe8493a330ed554bbe1c4\", \"size\": \"1.46 MB\", \"duration\": 12.04}", "aliases": ["Sanctified"], "size": "1.46 MB"}, {"id": "black-skinhead", "name": "Black Skinhead (Remix) [V2]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filename: BSH RMX Ref 10.5.13 FOR DVNO\nFeatures slight mixing differences from the other versions.", "length": "257.36", "fileDate": 16709760, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c4b0187173442c7798cf0703c4f6baf4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4b0187173442c7798cf0703c4f6baf4\", \"key\": \"Black Skinhead (Remix)\", \"title\": \"Black Skinhead (Remix) [V2]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON> Made-It & Lido)\", \"aliases\": [\"Rule The World\", \"Everybody Wants To Rule The World\"], \"description\": \"OG Filename: BSH RMX Ref 10.5.13 FOR DVNO\\nFeatures slight mixing differences from the other versions.\", \"date\": 16709760, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5f31add019234bc62c3f4370af510cfa\", \"url\": \"https://api.pillowcase.su/api/download/5f31add019234bc62c3f4370af510cfa\", \"size\": \"5.39 MB\", \"duration\": 257.36}", "aliases": ["Rule The World", "Everybody Wants To Rule The World"], "size": "5.39 MB"}, {"id": "black-skinhead-15", "name": "⭐️ Black Skinhead (Remix) [V3]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filename: Black Skinhead Remix 10.13.13\nMade during the Yeezus 2 sessions. Tagged version with part of the intro removed leaked in January 2016. Full untagged version leaked in May 2021. Lossless file leaked in December 2022.", "length": "257.67", "fileDate": 16220736, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/820c2b996dacbd241daf8b09c97b82fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/820c2b996dacbd241daf8b09c97b82fc\", \"key\": \"Black Skinhead (Remix)\", \"title\": \"\\u2b50\\ufe0f Black Skinhead (Remix) [V3]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON> Made-It & Lido)\", \"aliases\": [\"Rule The World\", \"Everybody Wants To Rule The World\"], \"description\": \"OG Filename: Black Skinhead Remix 10.13.13\\nMade during the Yeezus 2 sessions. Tagged version with part of the intro removed leaked in January 2016. Full untagged version leaked in May 2021. Lossless file leaked in December 2022.\", \"date\": 16220736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8819d55eabef894fb64e75888946253e\", \"url\": \"https://api.pillowcase.su/api/download/8819d55eabef894fb64e75888946253e\", \"size\": \"5.39 MB\", \"duration\": 257.67}", "aliases": ["Rule The World", "Everybody Wants To Rule The World"], "size": "5.39 MB"}, {"id": "black-skinhead-16", "name": "Black Skinhead (Remix) [V3] [Sample Clearance Version]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filename: 32 BLKKK SKKKN (RMX) FOR CLEARANC\nSample clearance version of the above song.", "length": "257.44", "fileDate": 16711488, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/aceda2ff7f9b75af707966346e59b84a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aceda2ff7f9b75af707966346e59b84a\", \"key\": \"Black Skinhead (Remix)\", \"title\": \"Black Skinhead (Remix) [V3] [Sample Clearance Version]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON> Made-It & Lido)\", \"aliases\": [\"Rule The World\", \"Everybody Wants To Rule The World\"], \"description\": \"OG Filename: 32 BLKKK SKKKN (RMX) FOR CLEARANC\\nSample clearance version of the above song.\", \"date\": 16711488, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6a1750d0c6c49a2f480cfb9e3f6021a0\", \"url\": \"https://api.pillowcase.su/api/download/6a1750d0c6c49a2f480cfb9e3f6021a0\", \"size\": \"5.38 MB\", \"duration\": 257.44}", "aliases": ["Rule The World", "Everybody Wants To Rule The World"], "size": "5.38 MB"}, {"id": "black-skinhead-17", "name": "<PERSON> Skinhead (Remix) [V3] [Tour Mix]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filename: Black Skinhead Remix - Yeezus Tour\nYeezus Tour mix that's seemingly identical to other versions.", "length": "257.72", "fileDate": 16709760, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2706467a1e4eb7cc8295a8fb9e79294f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2706467a1e4eb7cc8295a8fb9e79294f\", \"key\": \"Black Skinhead (Remix)\", \"title\": \"Black Skinhead (Remix) [V3] [Tour Mix]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON> Made-It & Lido)\", \"aliases\": [\"Rule The World\", \"Everybody Wants To Rule The World\"], \"description\": \"OG Filename: Black Skinhead Remix - Yeezus Tour\\nYeezus Tour mix that's seemingly identical to other versions.\", \"date\": 16709760, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"26806d5a174730953daacc5624a26e0f\", \"url\": \"https://api.pillowcase.su/api/download/26806d5a174730953daacc5624a26e0f\", \"size\": \"5.39 MB\", \"duration\": 257.72}", "aliases": ["Rule The World", "Everybody Wants To Rule The World"], "size": "5.39 MB"}, {"id": "everybody-wants-to-rule-the-world", "name": "Everybody Wants To Rule The World [V4]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filename: Everybody Wants To Rule The World FOR TRACKING 128 BPM\nEarliest version of \"Everybody Wants To Rule The World\", intended for <PERSON><PERSON><PERSON> to record over. Missing most <PERSON><PERSON><PERSON> vocals and has a different mix as well as a slightly different instrumental.", "length": "255.62", "fileDate": 16711488, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0aa85f2badc8200d0541975b50935a79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0aa85f2badc8200d0541975b50935a79\", \"key\": \"Everybody Wants To Rule The World\", \"title\": \"Everybody Wants To Rule The World [V4]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON> Made-It & Lido)\", \"aliases\": [\"Rule The World\", \"Black Skinhead (Remix)\"], \"description\": \"OG Filename: Everybody Wants To Rule The World FOR TRACKING 128 BPM\\nEarliest version of \\\"Everybody Wants To Rule The World\\\", intended for <PERSON><PERSON><PERSON> to record over. Missing most <PERSON><PERSON><PERSON> vocals and has a different mix as well as a slightly different instrumental.\", \"date\": 16711488, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7836d4b902fc144455475a6bd56977cd\", \"url\": \"https://api.pillowcase.su/api/download/7836d4b902fc144455475a6bd56977cd\", \"size\": \"5.35 MB\", \"duration\": 255.62}", "aliases": ["Rule The World", "Black Skinhead (Remix)"], "size": "5.35 MB"}, {"id": "everybody-wants-to-rule-the-world-19", "name": "Everybody Wants To Rule The World [V5]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filenames: Everybody Wants To Rule The Wold Ref (11.8.13) &\nBSH (rmx)_Everybody Wants To Rule The Wold (11.8.13)\nYeezus 2 song created with elements from \"Black Skinhead (Remix)\". <PERSON><PERSON> recorded during the 2013 VMAs afterparty. Contains some production from \"Black Skinhead\". Has a mumble-ish Ye verse completely different from the original song.", "length": "188.65", "fileDate": 16970688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/ed41665c4610d7b06cd581f46b9696fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ed41665c4610d7b06cd581f46b9696fd\", \"key\": \"Everybody Wants To Rule The World\", \"title\": \"Everybody Wants To Rule The World [V5]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON>-It & Lido)\", \"aliases\": [\"Rule The World\", \"Black Skinhead (Remix)\"], \"description\": \"OG Filenames: Everybody Wants To Rule The Wold Ref (11.8.13) &\\nBSH (rmx)_Everybody Wants To Rule The Wold (11.8.13)\\nYeezus 2 song created with elements from \\\"Black Skinhead (Remix)\\\". <PERSON><PERSON> recorded during the 2013 VMAs afterparty. Contains some production from \\\"Black Skinhead\\\". Has a mumble-ish Ye verse completely different from the original song.\", \"date\": 16970688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3c40bd2ddad96b7eac3682fb8945d9ca\", \"url\": \"https://api.pillowcase.su/api/download/3c40bd2ddad96b7eac3682fb8945d9ca\", \"size\": \"4.28 MB\", \"duration\": 188.65}", "aliases": ["Rule The World", "Black Skinhead (Remix)"], "size": "4.28 MB"}, {"id": "black-skinhead-20", "name": "<PERSON> Skinhead (Remix) [V7]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "Version with a different Ye freestyle about going on PornHub to look at the 'facials' section.", "length": "10.78", "fileDate": 17215200, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c6bc28148350695d607e28d1cd68ba1b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c6bc28148350695d607e28d1cd68ba1b\", \"key\": \"Black Skinhead (Remix)\", \"title\": \"Black Skinhead (Remix) [V7]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON> Made-It & Lido)\", \"aliases\": [\"Rule The World\", \"Everybody Wants To Rule The World\"], \"description\": \"Version with a different Ye freestyle about going on PornHub to look at the 'facials' section.\", \"date\": 17215200, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"86cc8818b3a9a19afe0998cadb9d35a0\", \"url\": \"https://api.pillowcase.su/api/download/86cc8818b3a9a19afe0998cadb9d35a0\", \"size\": \"1.43 MB\", \"duration\": 10.78}", "aliases": ["Rule The World", "Everybody Wants To Rule The World"], "size": "1.43 MB"}, {"id": "rule-the-world", "name": "Rule The World [V8]", "artists": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON> Made-It", "Lido"], "notes": "OG Filename: KW - Rule The World Ref (1.10.14)\nJanuary 2014 version of \"Rule The World\". Features a much more further along beat, separating itself from the original \"Black Skinhead Remix\" instrumentals. It's currently unknown if <PERSON> recorded any vocals. So far all that's leaked is it's beat.", "length": "240.62", "fileDate": 16972416, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/97d3bc1fe74c58911d798240447b3c7d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/97d3bc1fe74c58911d798240447b3c7d\", \"key\": \"Rule The World\", \"title\": \"Rule The World [V8]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON>) (prod. <PERSON>-It & Lido)\", \"aliases\": [\"Everybody Wants To Rule The World\", \"Black Skinhead Remix\"], \"description\": \"OG Filename: KW - Rule The World Ref (1.10.14)\\nJanuary 2014 version of \\\"Rule The World\\\". Features a much more further along beat, separating itself from the original \\\"Black Skinhead Remix\\\" instrumentals. It's currently unknown if <PERSON> recorded any vocals. So far all that's leaked is it's beat.\", \"date\": 16972416, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"04b4cc1385e956c743fb9c20bc49b3cc\", \"url\": \"https://api.pillowcase.su/api/download/04b4cc1385e956c743fb9c20bc49b3cc\", \"size\": \"5.11 MB\", \"duration\": 240.62}", "aliases": ["Everybody Wants To Rule The World", "Black Skinhead Remix"], "size": "5.11 MB"}, {"id": "creep-theme", "name": "Creep Theme", "artists": [], "producers": [], "notes": "OG Filename: 17 KW - Creep Theme Ref (1.28.14)\n<PERSON><PERSON><PERSON> talking over a very weird beat. Song samples \"Moribund the Burgermeister\" by <PERSON>. Original snippet leaked December 3rd, 2020.", "length": "103.31", "fileDate": 16749504, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/472197307fe67053d7e5fd0cf38e209e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/472197307fe67053d7e5fd0cf38e209e\", \"key\": \"Creep Theme\", \"title\": \"Creep Theme\", \"description\": \"OG Filename: 17 KW - Creep Theme Ref (1.28.14)\\n<PERSON><PERSON><PERSON> talking over a very weird beat. Song samples \\\"Moribund the Burgermeister\\\" by <PERSON>. Original snippet leaked December 3rd, 2020.\", \"date\": 16749504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"53212f5abcd52135d9ec12b4e513ee69\", \"url\": \"https://api.pillowcase.su/api/download/53212f5abcd52135d9ec12b4e513ee69\", \"size\": \"2.92 MB\", \"duration\": 103.31}", "aliases": [], "size": "2.92 MB"}, {"id": "crosses", "name": "Crosses", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> (1.7.14)\nRough January 2014 Yeezus 2 freestyle. Samples a <PERSON><PERSON> stand up performance in the intro.", "length": "82.19", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0a29c69e08e44af64be7587f2ef30f91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a29c69e08e44af64be7587f2ef30f91\", \"key\": \"Crosses\", \"title\": \"Crosses\", \"description\": \"OG Filename: KW - <PERSON><PERSON> Ref (1.7.14)\\nRough January 2014 Yeezus 2 freestyle. Samples a <PERSON>ie Mac stand up performance in the intro.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"104f9661fd8d5039dc2b316ee78bb77f\", \"url\": \"https://api.pillowcase.su/api/download/104f9661fd8d5039dc2b316ee78bb77f\", \"size\": \"2.58 MB\", \"duration\": 82.19}", "aliases": [], "size": "2.58 MB"}, {"id": "don-t-get-up", "name": "Don't Get Up [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W- <PERSON><PERSON> Get Up Ref (1.25.14)\nEarliest known version of \"Don't Get Up\". Contains mumble. Leaked as a bonus for the Yung Lean \"Stop Looking For This\" reference track <PERSON><PERSON><PERSON>.", "length": "70.28", "fileDate": 16801344, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/f041bda954b92530561b07503ba45156", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f041bda954b92530561b07503ba45156\", \"key\": \"Don't Get Up\", \"title\": \"Don't Get Up [V1]\", \"description\": \"OG Filename: KW- Dont Get Up Ref (1.25.14)\\nEarliest known version of \\\"Don't Get Up\\\". Contains mumble. Leaked as a bonus for the Yung Lean \\\"Stop Looking For This\\\" reference track JoeB<PERSON>.\", \"date\": 16801344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"24fdf93c63f8a7718739ea285cbd0513\", \"url\": \"https://api.pillowcase.su/api/download/24fdf93c63f8a7718739ea285cbd0513\", \"size\": \"2.39 MB\", \"duration\": 70.28}", "aliases": [], "size": "2.39 MB"}, {"id": "don-t-get-up-25", "name": "Don't Get Up [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - <PERSON><PERSON> Get Up Marsha Ref 2.19.14\n<PERSON><PERSON> Ambrosious \"Don't Get Up\" reference track. Original snippet leaked March 26th, 2023.", "length": "46.85", "fileDate": 16815168, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e7299fc9a385de3bf45224fa8051e4cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7299fc9a385de3bf45224fa8051e4cf\", \"key\": \"Don't Get Up\", \"title\": \"Don't Get Up [V2]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Dont Get Up Marsha Ref 2.19.14\\nMarsha Ambrosious \\\"Don't Get Up\\\" reference track. Original snippet leaked March 26th, 2023.\", \"date\": 16815168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fc3bec367d82c8a1f4c020e59a34c79e\", \"url\": \"https://api.pillowcase.su/api/download/fc3bec367d82c8a1f4c020e59a34c79e\", \"size\": \"2.01 MB\", \"duration\": 46.85}", "aliases": [], "size": "2.01 MB"}, {"id": "don-t-get-up-26", "name": "✨ Don't Get Up [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - Don't Get Up Livvi Ref 2.20.14\n<PERSON><PERSON> \"Don't Get Up\" reference track. Leaked as a bonus for the Yung Lean \"Stop Looking For This\" reference track <PERSON><PERSON><PERSON>.", "length": "307.41", "fileDate": 16801344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/6ec5639f075ac0c38f309fb0e0a6bd0b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ec5639f075ac0c38f309fb0e0a6bd0b\", \"key\": \"Don't Get Up\", \"title\": \"\\u2728 Don't Get Up [V3]\", \"artists\": \"(ref. <PERSON><PERSON>an<PERSON>)\", \"description\": \"OG Filename: KW - Don't Get Up Livvi Ref 2.20.14\\nLivvi Franc \\\"Don't Get Up\\\" reference track. Leaked as a bonus for the Yung Lean \\\"Stop Looking For This\\\" reference track JoeBuy.\", \"date\": 16801344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4de1e8f7e0355f511a0b54292c544578\", \"url\": \"https://api.pillowcase.su/api/download/4de1e8f7e0355f511a0b54292c544578\", \"size\": \"6.18 MB\", \"duration\": 307.41}", "aliases": [], "size": "6.18 MB"}, {"id": "don-t-get-up-27", "name": "Don't Get Up [V4]", "artists": [], "producers": [], "notes": "OG Filename: KW- Don't Get Up Andrew Ref 2.23.14\n<PERSON> \"Don't Get Up\" reference track. Has a more developed instrumental. Leaked as a bonus for the Yung Lean \"Stop Looking For This\" reference track <PERSON><PERSON><PERSON>.", "length": "137.67", "fileDate": 16801344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e6b78b9bb2bef721fb5b220bffceaa05", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e6b78b9bb2bef721fb5b220bffceaa05\", \"key\": \"Don't Get Up\", \"title\": \"Don't Get Up [V4]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"OG Filename: K<PERSON>- Don't Get Up Andrew Ref 2.23.14\\n<PERSON><PERSON><PERSON> \\\"Don't Get Up\\\" reference track. Has a more developed instrumental. Leaked as a bonus for the Yung Lean \\\"Stop Looking For This\\\" reference track JoeBuy.\", \"date\": 16801344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c4c0f3c0af872e17a52618bebf0fdd2a\", \"url\": \"https://api.pillowcase.su/api/download/c4c0f3c0af872e17a52618bebf0fdd2a\", \"size\": \"3.46 MB\", \"duration\": 137.67}", "aliases": [], "size": "3.46 MB"}, {"id": "don-t-want-to-think", "name": "Don't Want To Think", "artists": [], "producers": [], "notes": "OG Filename: KW - Don't Want To Think Ref (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble.", "length": "276.42", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b308aadb135c426ab679b9eb94a40326", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b308aadb135c426ab679b9eb94a40326\", \"key\": \"Don't Want To Think\", \"title\": \"Don't Want To Think\", \"description\": \"OG Filename: KW - Don't Want To Think Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4766eeced7ca6897bf201956c3e2092b\", \"url\": \"https://api.pillowcase.su/api/download/4766eeced7ca6897bf201956c3e2092b\", \"size\": \"5.69 MB\", \"duration\": 276.42}", "aliases": [], "size": "5.69 MB"}, {"id": "fall-into-you", "name": "✨ Fall Into You", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: KW - Fall Into You Ref (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble singing vocals with <PERSON><PERSON><PERSON> mostly just saying \"fall into you\".", "length": "87.41", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/750e1eed7d6f354a2d50a9cf3da5dee8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/750e1eed7d6f354a2d50a9cf3da5dee8\", \"key\": \"Fall Into You\", \"title\": \"\\u2728 Fall Into You\", \"artists\": \"(prod. 88-Keys)\", \"description\": \"OG Filename: KW - Fall Into You Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble singing vocals with <PERSON><PERSON><PERSON> mostly just saying \\\"fall into you\\\".\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"43b4ef6fddd76e1683cc10b51ebf0939\", \"url\": \"https://api.pillowcase.su/api/download/43b4ef6fddd76e1683cc10b51ebf0939\", \"size\": \"2.66 MB\", \"duration\": 87.41}", "aliases": [], "size": "2.66 MB"}, {"id": "father-stretch-my-hands", "name": "Father <PERSON><PERSON>ch <PERSON> [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Father <PERSON><PERSON><PERSON>f (11.11.13)\nFirst version of \"Father Stretch My Hands\" from the earliest known copy of Yeezus 2. Contains around two minutes of open verses before <PERSON><PERSON><PERSON> sings a few mumble notes, followed by more instrumental.", "length": "218.78", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c352b856f0acc0f4a67b44259d47add7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c352b856f0acc0f4a67b44259d47add7\", \"key\": \"Father Stretch My Hands\", \"title\": \"Father Stretch My Hands [V1]\", \"aliases\": [\"Father Stretch My Hands\", \"Pt. 1\", \"Father Stretch\", \"Father\"], \"description\": \"OG Filename: KW - Father Stretch My Hands Ref (11.11.13)\\nFirst version of \\\"Father Stretch My Hands\\\" from the earliest known copy of Yeezus 2. Contains around two minutes of open verses before <PERSON><PERSON><PERSON> sings a few mumble notes, followed by more instrumental.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d26528545ec112aa5d9b72cb7cbb5e47\", \"url\": \"https://api.pillowcase.su/api/download/d26528545ec112aa5d9b72cb7cbb5e47\", \"size\": \"4.76 MB\", \"duration\": 218.78}", "aliases": ["Father <PERSON><PERSON>ch <PERSON>", "Pt. 1", "<PERSON>", "Father"], "size": "4.76 MB"}, {"id": "finnese", "name": "Finnese [V1]", "artists": ["<PERSON>"], "producers": ["Southside"], "notes": "OG Filename: finnese\nVersion of <PERSON>'s \"Quintana, Pt. 2\". Appears as track 11 in a Yeezus 2 album zip. Has no <PERSON><PERSON><PERSON> vocals and is unknown if he ever did record for the song. Later released on <PERSON>' mixtape Days Before Rodeo and features <PERSON><PERSON><PERSON>. Features a different intro, a slightly rough first verse, and an extra verse in place of the outro. <PERSON>' vocals could be a reference track for <PERSON><PERSON><PERSON> but is has not been confirmed. OG File leaked after a groupbuy.", "length": "198.87", "fileDate": 17021664, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/7223af4f597907d3203e8dbac29e7e56", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7223af4f597907d3203e8dbac29e7e56\", \"key\": \"Finnese\", \"title\": \"Finnese [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Quintana\", \"Pt. 2\"], \"description\": \"OG Filename: finnese\\nVersion of <PERSON>'s \\\"Quintana, Pt. 2\\\". Appears as track 11 in a Yeezus 2 album zip. Has no <PERSON><PERSON><PERSON> vocals and is unknown if he ever did record for the song. Later released on <PERSON>' mixtape Days Before Rodeo and features <PERSON>.<PERSON>. Features a different intro, a slightly rough first verse, and an extra verse in place of the outro. <PERSON>' vocals could be a reference track for <PERSON><PERSON><PERSON> but is has not been confirmed. OG File leaked after a groupbuy.\", \"date\": 17021664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1c16c0610388a50431ba1ab2ffd9f0fb\", \"url\": \"https://api.pillowcase.su/api/download/1c16c0610388a50431ba1ab2ffd9f0fb\", \"size\": \"4.44 MB\", \"duration\": 198.87}", "aliases": ["Quintana", "Pt. 2"], "size": "4.44 MB"}, {"id": "quintana-pt-2", "name": "<PERSON>, Pt. 2 [V2]", "artists": [], "producers": ["Southside"], "notes": "Later version of \"Quintana, Pt. 2\" that features a different intro, and seemingly a different vocal take for the first few lines; it's unknown if <PERSON><PERSON><PERSON><PERSON>'s part was added yet. Snippet comes from an old video of <PERSON> playing DB<PERSON> cuts with <PERSON>J<PERSON><PERSON> at a show in late July 2014, posted on Tumblr by <PERSON><PERSON><PERSON>.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://kyorgyoti.tumblr.com/post/93246460107/travi-scott-rodeo-preview", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://kyorgyoti.tumblr.com/post/93246460107/travi-scott-rodeo-preview\", \"key\": \"Quintana, Pt. 2\", \"title\": \"<PERSON>, Pt. 2 [V2]\", \"artists\": \"(prod. South<PERSON>)\", \"aliases\": [\"Finnese\"], \"description\": \"Later version of \\\"Quintana, Pt. 2\\\" that features a different intro, and seemingly a different vocal take for the first few lines; it's unknown if <PERSON>.I.'s part was added yet. Snippet comes from an old video of <PERSON> playing DBR cuts with YesJulz at a show in late July 2014, posted on Tumblr by <PERSON><PERSON><PERSON>.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Finnese"], "size": ""}, {"id": "quintana-pt-2-33", "name": "<PERSON>, Pt. 2 [V2]", "artists": [], "producers": ["Southside"], "notes": "Later version of \"Quintana, Pt. 2\" that features a different intro, and seemingly a different vocal take for the first few lines; it's unknown if <PERSON><PERSON><PERSON><PERSON>'s part was added yet. Snippet comes from an old video of <PERSON> playing DB<PERSON> cuts with <PERSON>J<PERSON><PERSON> at a show in late July 2014, posted on Tumblr by <PERSON><PERSON><PERSON>.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/25ccb7c923a38c6f79f6ce1b833c1a4b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/25ccb7c923a38c6f79f6ce1b833c1a4b\", \"key\": \"Quintana, Pt. 2\", \"title\": \"<PERSON>, Pt. 2 [V2]\", \"artists\": \"(prod. South<PERSON>)\", \"aliases\": [\"Finnese\"], \"description\": \"Later version of \\\"Quintana, Pt. 2\\\" that features a different intro, and seemingly a different vocal take for the first few lines; it's unknown if <PERSON>.I<PERSON>'s part was added yet. Snippet comes from an old video of <PERSON> playing DBR cuts with YesJulz at a show in late July 2014, posted on Tumblr by <PERSON><PERSON><PERSON>.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Finnese"], "size": ""}, {"id": "god-level", "name": "God Level [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON> Level Ref (1.7.14)\nInital freestyle.", "length": "108.29", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d2f7436a691362c3a320738d64d23ebd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d2f7436a691362c3a320738d64d23ebd\", \"key\": \"God Level\", \"title\": \"God Level [V1]\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level Ref (1.7.14)\\nInital freestyle.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3b933a7cee22bd0aa9352021481ca391\", \"url\": \"https://api.pillowcase.su/api/download/3b933a7cee22bd0aa9352021481ca391\", \"size\": \"3 MB\", \"duration\": 108.29}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3 MB"}, {"id": "god-level-35", "name": "God Level [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level Livvi Ref 2.20.14\n<PERSON><PERSON> reference track that's mostly mumble and seems to just be a melody idea for <PERSON><PERSON><PERSON> to eventually mumble himself. Features <PERSON><PERSON> doing Desiigner adlibs before Desiigner adlibs were a thing.", "length": "188.02", "fileDate": 16968096, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d8b60ecf14d83298533c7bb5e22290d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d8b60ecf14d83298533c7bb5e22290d8\", \"key\": \"God Level\", \"title\": \"God Level [V2]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level Livvi Ref 2.20.14\\nLivvi Franc reference track that's mostly mumble and seems to just be a melody idea for <PERSON><PERSON><PERSON> to eventually mumble himself. Features <PERSON><PERSON> doing Desiigner adlibs before Desiigner adlibs were a thing.\", \"date\": 16968096, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3102faabbc04568849f3d27a3a9cace2\", \"url\": \"https://api.pillowcase.su/api/download/3102faabbc04568849f3d27a3a9cace2\", \"size\": \"4.27 MB\", \"duration\": 188.02}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "4.27 MB"}, {"id": "god-level-36", "name": "God Level [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> Level (<PERSON> 2.23.14)\nSam Dew reference track. Has new production, a Sam bridge, background vocals and reference vocals.", "length": "125.38", "fileDate": 16973280, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/fbd9e507b94c1d24a23b820faef72862", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fbd9e507b94c1d24a23b820faef72862\", \"key\": \"God Level\", \"title\": \"God Level [V3]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: God Level (Sam Dew 2.23.14)\\nSam Dew reference track. Has new production, a Sam bridge, background vocals and reference vocals.\", \"date\": 16973280, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"16a887a5149c1971da65f63275f9f8aa\", \"url\": \"https://api.pillowcase.su/api/download/16a887a5149c1971da65f63275f9f8aa\", \"size\": \"3.27 MB\", \"duration\": 125.38}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3.27 MB"}, {"id": "god-level-37", "name": "God Level [V4]", "artists": [], "producers": [], "notes": "OG Filename: God Level (Boots Ref 3.1.14)\nBOOTS reference track. Has different bridge production and some <PERSON><PERSON><PERSON> vocals.", "length": "146.37", "fileDate": 16924896, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d29032fa88180913bd7a1400f76b621b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d29032fa88180913bd7a1400f76b621b\", \"key\": \"God Level\", \"title\": \"God Level [V4]\", \"artists\": \"(ref. BOOTS)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: God Level (Boots Ref 3.1.14)\\nBOOTS reference track. Has different bridge production and some Kanye vocals.\", \"date\": 16924896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4614f5d9be62f11fc45b88424bf1d409\", \"url\": \"https://api.pillowcase.su/api/download/4614f5d9be62f11fc45b88424bf1d409\", \"size\": \"3.61 MB\", \"duration\": 146.37}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3.61 MB"}, {"id": "god-level-38", "name": "God Level [V5]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> (<PERSON> 3.1.14)\n<PERSON> reference track.", "length": "202.28", "fileDate": 17025984, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/79bfbcebe6f46863176435d7656b7502", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/79bfbcebe6f46863176435d7656b7502\", \"key\": \"God Level\", \"title\": \"God Level [V5]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: <PERSON> Level (Andrea Ref 3.1.14)\\nAndrea Martin reference track.\", \"date\": 17025984, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"179ff0d17813459ead093d71a4ad08b1\", \"url\": \"https://api.pillowcase.su/api/download/179ff0d17813459ead093d71a4ad08b1\", \"size\": \"4.5 MB\", \"duration\": 202.28}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "4.5 MB"}, {"id": "good-lord", "name": "Good Lord", "artists": [], "producers": ["Paris Bueller"], "notes": "OG Filename: 18 KW - <PERSON> Lord <PERSON> (1.28.14)\nMumble track produced by Paris Bueller.", "length": "94.4", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/f7200afefc9b06d8aacdede39ba12a1d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f7200afefc9b06d8aacdede39ba12a1d\", \"key\": \"Good Lord\", \"title\": \"Good Lord\", \"artists\": \"(prod. <PERSON> Bueller)\", \"description\": \"OG Filename: 18 KW - <PERSON> Lord Ref (1.28.14)\\nMumble track produced by Paris Bueller.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"61371c601c5c5221c773b611f4dce243\", \"url\": \"https://api.pillowcase.su/api/download/61371c601c5c5221c773b611f4dce243\", \"size\": \"2.77 MB\", \"duration\": 94.4}", "aliases": [], "size": "2.77 MB"}, {"id": "gut-feeling", "name": "<PERSON><PERSON>", "artists": [], "producers": ["88-<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble.", "length": "79.78", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/1ff9ab4d0b71798d7141e31bad3823c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ff9ab4d0b71798d7141e31bad3823c3\", \"key\": \"Gut Feeling\", \"title\": \"Gut Feeling\", \"artists\": \"(prod. 88-Keys)\", \"description\": \"OG Filename: <PERSON>W - <PERSON><PERSON> Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"884e443f6ec6d286737269d053103001\", \"url\": \"https://api.pillowcase.su/api/download/884e443f6ec6d286737269d053103001\", \"size\": \"2.54 MB\", \"duration\": 79.78}", "aliases": [], "size": "2.54 MB"}, {"id": "hold-me-close", "name": "Hold Me Close [V1]", "artists": [], "producers": ["S1"], "notes": "OG Filename: KW S1 Hold Me Close\nEarly voice memo version. Samples \"Rock The Boat\" by <PERSON><PERSON><PERSON>.", "length": "46.44", "fileDate": 17021664, "leakDate": "", "availableLength": "OG File", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b34fdc167554f2911897bc863c7c6b7d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b34fdc167554f2911897bc863c7c6b7d\", \"key\": \"Hold Me Close\", \"title\": \"Hold Me Close [V1]\", \"artists\": \"(prod. S1)\", \"description\": \"OG Filename: KW S1 Hold Me Close\\nEarly voice memo version. Samples \\\"Rock The Boat\\\" by <PERSON><PERSON><PERSON>.\", \"date\": 17021664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"59daed0895e0987aa6277cc2d8659dc9\", \"url\": \"https://api.pillowcase.su/api/download/59daed0895e0987aa6277cc2d8659dc9\", \"size\": \"1.63 MB\", \"duration\": 46.44}", "aliases": [], "size": "1.63 MB"}, {"id": "hold-me-close-42", "name": "Hold Me Close [V2]", "artists": [], "producers": ["S1"], "notes": "OG Filename: KW - Hold Me Close Ref (1.6.14)\nEarliest in-studio version.", "length": "248", "fileDate": 17021664, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0ed4a049faddabeb224f77447a4d2636", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0ed4a049faddabeb224f77447a4d2636\", \"key\": \"Hold Me Close\", \"title\": \"Hold Me Close [V2]\", \"artists\": \"(prod. S1)\", \"description\": \"OG Filename: KW - Hold Me Close Ref (1.6.14)\\nEarliest in-studio version.\", \"date\": 17021664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5c735c4c7b2e9b3d1d6b445930de6356\", \"url\": \"https://api.pillowcase.su/api/download/5c735c4c7b2e9b3d1d6b445930de6356\", \"size\": \"5.23 MB\", \"duration\": 248}", "aliases": [], "size": "5.23 MB"}, {"id": "hold-me-close-43", "name": "Hold Me Close [V3]", "artists": [], "producers": ["S1"], "notes": "OG Filename: KW - Hold Me Close Ref (1.28.14)\nInstrumental-only version. Unknown how different it is from previous versions.", "length": "247.85", "fileDate": 17021664, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/9bee899b7157b6270d7cf3236c794370", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9bee899b7157b6270d7cf3236c794370\", \"key\": \"Hold Me Close\", \"title\": \"Hold Me Close [V3]\", \"artists\": \"(prod. S1)\", \"description\": \"OG Filename: KW - Hold Me Close Ref (1.28.14)\\nInstrumental-only version. Unknown how different it is from previous versions.\", \"date\": 17021664, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dbdc61dd3d78be7dd1e6e71713d2f6be\", \"url\": \"https://api.pillowcase.su/api/download/dbdc61dd3d78be7dd1e6e71713d2f6be\", \"size\": \"5.23 MB\", \"duration\": 247.85}", "aliases": [], "size": "5.23 MB"}, {"id": "hummer", "name": "<PERSON>mmer", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filename: <PERSON>W - <PERSON> $olo - <PERSON>mmer <PERSON> (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble. Beat was given to <PERSON><PERSON><PERSON><PERSON><PERSON>, released as \"Downward\".", "length": "121.77", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/9179b1c255c744e22d7a95477418add7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9179b1c255c744e22d7a95477418add7\", \"key\": \"Hummer\", \"title\": \"Hummer\", \"artists\": \"(prod. <PERSON> $olo)\", \"description\": \"OG Filename: KW - Dom $olo - Hummer Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble. Beat was given to <PERSON><PERSON><PERSON><PERSON><PERSON>, released as \\\"Downward\\\".\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f09211b966fb563c105451a7f4364645\", \"url\": \"https://api.pillowcase.su/api/download/f09211b966fb563c105451a7f4364645\", \"size\": \"3.21 MB\", \"duration\": 121.77}", "aliases": [], "size": "3.21 MB"}, {"id": "i-am-not-here", "name": "I Am Not Here [V5]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I Am Not Here (Clearance Voice-1)\nSample clearance version, with sample clearance tags. 4 minutes in length. Very similar to the final mix, besides a different effect being used on <PERSON><PERSON><PERSON>'s voice at 1:50, and different mixing that makes it noticeably louder.", "length": "252.1", "fileDate": 16711488, "leakDate": "", "availableLength": "Tagged", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/55834ef20163a85ab4a7effc2a797b4d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/55834ef20163a85ab4a7effc2a797b4d\", \"key\": \"I Am Not Here\", \"title\": \"I Am Not Here [V5]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I Am Not Home\"], \"description\": \"OG Filename: I Am Not Here (Clearance Voice-1)\\nSample clearance version, with sample clearance tags. 4 minutes in length. Very similar to the final mix, besides a different effect being used on <PERSON><PERSON><PERSON>'s voice at 1:50, and different mixing that makes it noticeably louder.\", \"date\": 16711488, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ffdb8f695b1dcd34d1c6f98cc498ec3e\", \"url\": \"https://api.pillowcase.su/api/download/ffdb8f695b1dcd34d1c6f98cc498ec3e\", \"size\": \"4.32 MB\", \"duration\": 252.1}", "aliases": ["I Am Not Home"], "size": "4.32 MB"}, {"id": "i-am-not-home", "name": "⭐ I Am Not Home [V6]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I Am Not Home - FINAL MIX\nFinal mix. Has a slightly different structure compared to the previous versions of the song. <PERSON> played at a few shows from the Yeezus tour as an extended version. Samples \"<PERSON> Preciso <PERSON>, <PERSON><PERSON>\" by <PERSON><PERSON>. LQ file originally leaked March 7th, 2023.", "length": "253.89", "fileDate": 16816896, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b8b6fefa95722be1d4706936c468345e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b8b6fefa95722be1d4706936c468345e\", \"key\": \"I Am Not Home\", \"title\": \"\\u2b50 I Am Not Home [V6]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I Am Not Here\"], \"description\": \"OG Filename: I Am Not Home - FINAL MIX\\nFinal mix. Has a slightly different structure compared to the previous versions of the song. <PERSON> played at a few shows from the Yeezus tour as an extended version. <PERSON><PERSON> \\\"\\u00c9 Pre<PERSON>o <PERSON>, <PERSON>u Amigo\\\" by Erasmo <PERSON>. LQ file originally leaked March 7th, 2023.\", \"date\": 16816896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5e5a037177233b4536fa30615cc722c8\", \"url\": \"https://api.pillowcase.su/api/download/5e5a037177233b4536fa30615cc722c8\", \"size\": \"5.32 MB\", \"duration\": 253.89}", "aliases": ["I Am Not Here"], "size": "5.32 MB"}, {"id": "i-am-not-home-47", "name": "I Am Not Home [V7]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I Am Not Home - Yeezus Show Edit (11.19.13)\nVersion used during the Yeezus tour. Similar to the final mix, but shortened for stadium performances.", "length": "37.08", "fileDate": 16780608, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/04056df0343ae29491f43c640b002885", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/04056df0343ae29491f43c640b002885\", \"key\": \"I Am Not Home\", \"title\": \"I Am Not Home [V7]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"I Am Not Here\"], \"description\": \"OG Filename: I Am Not Home - Yeezus Show Edit (11.19.13)\\nVersion used during the Yeezus tour. Similar to the final mix, but shortened for stadium performances.\", \"date\": 16780608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6b19f68ab62c9b0ce5e9f4568a4fbc15\", \"url\": \"https://api.pillowcase.su/api/download/6b19f68ab62c9b0ce5e9f4568a4fbc15\", \"size\": \"1.86 MB\", \"duration\": 37.08}", "aliases": ["I Am Not Here"], "size": "1.86 MB"}, {"id": "i-could-tell", "name": "I Could Tell", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - <PERSON> <PERSON> (11.11.13)\nSong from the earliest known copy of Yeezus 2. Very short freestyle.", "length": "23.35", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/381c4253712a6174309b4d4b864d7744", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/381c4253712a6174309b4d4b864d7744\", \"key\": \"I Could Tell\", \"title\": \"I Could Tell\", \"description\": \"OG Filename: KW - <PERSON> Could Tell Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Very short freestyle.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"912a402107efec832a460488375987da\", \"url\": \"https://api.pillowcase.su/api/download/912a402107efec832a460488375987da\", \"size\": \"1.64 MB\", \"duration\": 23.35}", "aliases": [], "size": "1.64 MB"}, {"id": "i-m-gonna-be-alright", "name": "I'm Gonna Be Alright", "artists": [], "producers": ["Paris Bueller"], "notes": "OG Filename: 21 KW - I'm Gonna Be Alright Ref (1.25.14)\nRough January 2014 freestyle, produced by <PERSON> Bueller. Features iPhone vocals.", "length": "126.56", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/79a58a965e2aeb68c70c4801a21420c5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/79a58a965e2aeb68c70c4801a21420c5\", \"key\": \"I'm Gonna Be Alright\", \"title\": \"I'm Gonna Be Alright\", \"artists\": \"(prod. <PERSON> Bueller)\", \"description\": \"OG Filename: 21 KW - I'm Gonna Be Alright Ref (1.25.14)\\nRough January 2014 freestyle, produced by <PERSON> Bueller. Features iPhone vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5e2f04513fec195db9d3f4424422414b\", \"url\": \"https://api.pillowcase.su/api/download/5e2f04513fec195db9d3f4424422414b\", \"size\": \"3.29 MB\", \"duration\": 126.56}", "aliases": [], "size": "3.29 MB"}, {"id": "in-the-way-right", "name": "In The Way Right", "artists": [], "producers": [], "notes": "OG Filename: 22 KW - In The Way Right Ref (1.25.14)\nRough freestyle. Samples \"<PERSON> Of Cleves\" by <PERSON>.", "length": "114.42", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2ed53feb1d8e6d6d4e731d89fe156aa2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ed53feb1d8e6d6d4e731d89fe156aa2\", \"key\": \"In The Way Right\", \"title\": \"In The Way Right\", \"description\": \"OG Filename: 22 KW - In The Way Right Ref (1.25.14)\\nRough freestyle. Samples \\\"Anne Of Cleves\\\" by <PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ba852213801ba973406e83e134014376\", \"url\": \"https://api.pillowcase.su/api/download/ba852213801ba973406e83e134014376\", \"size\": \"3.09 MB\", \"duration\": 114.42}", "aliases": [], "size": "3.09 MB"}, {"id": "it-s-only-right", "name": "It's Only Right [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - It's Only Right (1.7.14)\nMumble freestyle.", "length": "100.28", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/6ada5fb6de31532d640d5ebc3e036175", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ada5fb6de31532d640d5ebc3e036175\", \"key\": \"It's Only Right\", \"title\": \"It's Only Right [V1]\", \"aliases\": [\"Lonely Life\"], \"description\": \"OG Filename: KW - It's Only Right (1.7.14)\\nMumble freestyle.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"91c236be7d99a7a412b0af721aedc45f\", \"url\": \"https://api.pillowcase.su/api/download/91c236be7d99a7a412b0af721aedc45f\", \"size\": \"2.87 MB\", \"duration\": 100.28}", "aliases": ["Lonely Life"], "size": "2.87 MB"}, {"id": "it-s-only-right-52", "name": "It's Only Right [V2]", "artists": [], "producers": [], "notes": "Has more polished and finished production.", "length": "10.19", "fileDate": 17191008, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/762a674f1ebfc394a21a2b50ea80b9aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/762a674f1ebfc394a21a2b50ea80b9aa\", \"key\": \"It's Only Right\", \"title\": \"It's Only Right [V2]\", \"aliases\": [\"Lonely Life\"], \"description\": \"Has more polished and finished production.\", \"date\": 17191008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"232538d0a233baee92877e4e1a66cffa\", \"url\": \"https://api.pillowcase.su/api/download/232538d0a233baee92877e4e1a66cffa\", \"size\": \"1.43 MB\", \"duration\": 10.19}", "aliases": ["Lonely Life"], "size": "1.43 MB"}, {"id": "live-together", "name": "Live Together [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Live Together Ref (11.11.13)\nSong from the earliest known copy of Yeezus 2. <PERSON><PERSON> freestyling for about 10 seconds until he starts talking about the beat. Samples \"Why Can't We Live Together\" by <PERSON><PERSON>.", "length": "279.15", "fileDate": 16779744, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/1e3aa7dca8ae839b433dd540d11835b6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e3aa7dca8ae839b433dd540d11835b6\", \"key\": \"Live Together\", \"title\": \"Live Together [V1]\", \"aliases\": [\"Why Can't We Live Together\"], \"description\": \"OG Filename: KW - Live Together Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. <PERSON><PERSON> freestyling for about 10 seconds until he starts talking about the beat. <PERSON><PERSON> \\\"Why Can't We Live Together\\\" by <PERSON><PERSON>.\", \"date\": 16779744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5729e5501b127f4ab295c5936bbbccaf\", \"url\": \"https://api.pillowcase.su/api/download/5729e5501b127f4ab295c5936bbbccaf\", \"size\": \"5.73 MB\", \"duration\": 279.15}", "aliases": ["Why Can't We Live Together"], "size": "5.73 MB"}, {"id": "why-can-t-we-live-together", "name": "Why Can't We Live Together [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - Why Can't We Live Together Ref (1.7.14)\nUpdated version, where it just replays one part over and over again.", "length": "72.55", "fileDate": 16779744, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/f8e6521f42d40c5623bc4dd870fbddf7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f8e6521f42d40c5623bc4dd870fbddf7\", \"key\": \"Why Can't We Live Together\", \"title\": \"Why Can't We Live Together [V2]\", \"aliases\": [\"Live Together\"], \"description\": \"OG Filename: KW - Why Can't We Live Together Ref (1.7.14)\\nUpdated version, where it just replays one part over and over again.\", \"date\": 16779744, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"59f762406033331983ac3ac35d377b5e\", \"url\": \"https://api.pillowcase.su/api/download/59f762406033331983ac3ac35d377b5e\", \"size\": \"2.42 MB\", \"duration\": 72.55}", "aliases": ["Live Together"], "size": "2.42 MB"}, {"id": "losing-me", "name": "Losing Me", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> - <PERSON><PERSON> <PERSON> (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble.", "length": "107.7", "fileDate": 16792704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c8e8a563c63952b3789165088d963d06", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c8e8a563c63952b3789165088d963d06\", \"key\": \"Losing Me\", \"title\": \"Losing Me\", \"description\": \"OG Filename: KW - Losing Me Ref (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble.\", \"date\": 16792704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"edb968ab15143cce5cabebab7fb79752\", \"url\": \"https://api.pillowcase.su/api/download/edb968ab15143cce5cabebab7fb79752\", \"size\": \"2.99 MB\", \"duration\": 107.7}", "aliases": [], "size": "2.99 MB"}, {"id": "man-up", "name": "Man Up [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> Up (1.3.14)\nFirst freestyle. Does not have any <PERSON> Thug vocals.", "length": "7.11", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/07485b43840ae3dc97040362ed523879", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/07485b43840ae3dc97040362ed523879\", \"key\": \"Man Up\", \"title\": \"Man Up [V1]\", \"aliases\": [\"Bad Night\", \"Rap Tarantino\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: KW - Man Up (1.3.14)\\nFirst freestyle. Does not have any Young Thug vocals.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"440d0de40e478d74d0d67115bf0ad7d1\", \"url\": \"https://api.pillowcase.su/api/download/440d0de40e478d74d0d67115bf0ad7d1\", \"size\": \"1.38 MB\", \"duration\": 7.11}", "aliases": ["Bad Night", "<PERSON>", "Too Re<PERSON>ss", "Bad Guy"], "size": "1.38 MB"}, {"id": "man-up-57", "name": "Man Up [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> Up (1.3.14)\nFirst freestyle. Does not have any <PERSON> Thug vocals.", "length": "7.04", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/22ab25f04379e594888b83ef79a9a3f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/22ab25f04379e594888b83ef79a9a3f5\", \"key\": \"Man Up\", \"title\": \"Man Up [V1]\", \"aliases\": [\"Bad Night\", \"Rap Tarantino\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: KW - Man Up (1.3.14)\\nFirst freestyle. Does not have any Young Thug vocals.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"53f7246fead3f9f81ef30232d3c0e416\", \"url\": \"https://api.pillowcase.su/api/download/53f7246fead3f9f81ef30232d3c0e416\", \"size\": \"1.38 MB\", \"duration\": 7.04}", "aliases": ["Bad Night", "<PERSON>", "Too Re<PERSON>ss", "Bad Guy"], "size": "1.38 MB"}, {"id": "man-up-58", "name": "Man Up [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - Man Up Young Thug Ref 2 (1.10.14)\nSolo Young Thug, apparently a reference for <PERSON><PERSON><PERSON>. Has slightly different production than the one with <PERSON><PERSON><PERSON>.", "length": "143.94", "fileDate": 17006112, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/747ddf3c7083e793ec44607e25b7251e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/747ddf3c7083e793ec44607e25b7251e\", \"key\": \"Man Up\", \"title\": \"Man Up [V3]\", \"artists\": \"(ref. <PERSON> Thug)\", \"aliases\": [\"Bad Night\", \"Rap Tarantino\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: KW - Man Up Young Thug Ref 2 (1.10.14)\\nSolo Young Thug, apparently a reference for <PERSON><PERSON><PERSON>. Has slightly different production than the one with <PERSON><PERSON><PERSON>.\", \"date\": 17006112, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7c09f25387efee96aeba06edca376878\", \"url\": \"https://api.pillowcase.su/api/download/7c09f25387efee96aeba06edca376878\", \"size\": \"3.57 MB\", \"duration\": 143.94}", "aliases": ["Bad Night", "<PERSON>", "Too Re<PERSON>ss", "Bad Guy"], "size": "3.57 MB"}, {"id": "man-up-59", "name": "Man Up [V4]", "artists": ["<PERSON> Thug"], "producers": [], "notes": "OG Filename: KW - Man Up Ref (1.28.14)\nFirst version with <PERSON><PERSON><PERSON>. Found on the leaked Yeezus 2 tracklist. May have been made February of 2014, judging from the filename. Originally leaked tagged September 8th, 2020, before leaking untagged on November 18th, 2022 as a bonus for the \"Our King\" groupbuy.", "length": "159.91", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/a019bcb49f4cb49676e86a648e2b7773", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a019bcb49f4cb49676e86a648e2b7773\", \"key\": \"Man Up\", \"title\": \"Man Up [V4]\", \"artists\": \"(feat. <PERSON> Thug)\", \"aliases\": [\"Bad Night\", \"Rap Tarantino\", \"Bad Guy\"], \"description\": \"OG Filename: KW - Man Up Ref (1.28.14)\\nFirst version with <PERSON><PERSON><PERSON>. Found on the leaked Yeezus 2 tracklist. May have been made February of 2014, judging from the filename. Originally leaked tagged September 8th, 2020, before leaking untagged on November 18th, 2022 as a bonus for the \\\"Our King\\\" groupbuy.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dab9b76becca938256174dccb984e5e5\", \"url\": \"https://api.pillowcase.su/api/download/dab9b76becca938256174dccb984e5e5\", \"size\": \"3.82 MB\", \"duration\": 159.91}", "aliases": ["Bad Night", "<PERSON>", "Bad Guy"], "size": "3.82 MB"}, {"id": "money-pussy-alcohol", "name": "Money Pussy Alcohol [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: KW - Money Pussy Al<PERSON> (1.9.14)\nFirst version of \"M.P.A.\" by Pusha T, as it was originally a solo-Ye Yeezus 2 idea. Contains only the hook that would later be ripped straight from this file onto the released Pusha T version, alongside a long open section.", "length": "124.94", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e859395a0e91624a0fbbd0fa7c4828bf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e859395a0e91624a0fbbd0fa7c4828bf\", \"key\": \"Money Pussy Alcohol\", \"title\": \"Money Pussy Alcohol [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"M.P.A.\"], \"description\": \"OG Filename: KW - Money Pussy Alcohol Ref (1.9.14)\\nFirst version of \\\"M.P.A.\\\" by Pusha T, as it was originally a solo-Ye Yeezus 2 idea. Contains only the hook that would later be ripped straight from this file onto the released Pusha T version, alongside a long open section.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8914aae7c83d87367d4d29b8b84aba96\", \"url\": \"https://api.pillowcase.su/api/download/8914aae7c83d87367d4d29b8b84aba96\", \"size\": \"3.26 MB\", \"duration\": 124.94}", "aliases": ["M.P.A."], "size": "3.26 MB"}, {"id": "my-smile", "name": "My Smile", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON> Ref (11.11.13)\nFrom the earliest known copy of Yeezus 2. Contains mumble.", "length": "142.03", "fileDate": 16792704, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/5d32c0c207f9227c6d189b992a91da70", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d32c0c207f9227c6d189b992a91da70\", \"key\": \"My Smile\", \"title\": \"My Smile\", \"description\": \"OG Filename: KW - <PERSON> Smile Ref (11.11.13)\\nFrom the earliest known copy of Yeezus 2. Contains mumble.\", \"date\": 16792704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"afe0337e8420b846c4a331be5813b6e2\", \"url\": \"https://api.pillowcase.su/api/download/afe0337e8420b846c4a331be5813b6e2\", \"size\": \"3.53 MB\", \"duration\": 142.03}", "aliases": [], "size": "3.53 MB"}, {"id": "cathy-s-arrogance", "name": "<PERSON>'s Arrogance [V1]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Original freestyle, seen in a January 2014 copy of Yeezus 2.", "length": "15.75", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/632c6fdfc5c19db7062fe0bbcee93282", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/632c6fdfc5c19db7062fe0bbcee93282\", \"key\": \"<PERSON>'s Arrogance\", \"title\": \"<PERSON>'s Arrogance [V1]\", \"artists\": \"(prod. 88-<PERSON>)\", \"aliases\": [\"Never Lose\"], \"description\": \"Original freestyle, seen in a January 2014 copy of Yeezus 2.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"98f3b1dfbebcdedbb58760a4dd394b47\", \"url\": \"https://api.pillowcase.su/api/download/98f3b1dfbebcdedbb58760a4dd394b47\", \"size\": \"1.51 MB\", \"duration\": 15.75}", "aliases": ["Never Lose"], "size": "1.51 MB"}, {"id": "never-lose", "name": "Never Lose [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - <PERSON>e <PERSON>f (1.6.14)\nTitle on a early 2014 whiteboard. Has an earlier instrumental and no open. Original snippet leaked August 25, 2020.", "length": "105", "fileDate": 16749504, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b0a42bbad887019374749d7202c44c81", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b0a42bbad887019374749d7202c44c81\", \"key\": \"Never Lose\", \"title\": \"<PERSON> Lose [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KW - Never Lose Ref (1.6.14)\\nTitle on a early 2014 whiteboard. Has an earlier instrumental and no open. Original snippet leaked August 25, 2020.\", \"date\": 16749504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4b094a91e2fb0ac6d6b4808c17c789b4\", \"url\": \"https://api.pillowcase.su/api/download/4b094a91e2fb0ac6d6b4808c17c789b4\", \"size\": \"2.94 MB\", \"duration\": 105}", "aliases": [], "size": "2.94 MB"}, {"id": "never-lose-64", "name": "Never Lose [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - <PERSON>f (2.11.14)\nHas a long open section.", "length": "185.64", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e34d4e85263590871dfba3aa8848dae4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e34d4e85263590871dfba3aa8848dae4\", \"key\": \"Never Lose\", \"title\": \"<PERSON> Lose [V3]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KW - Never Lose Ref (2.11.14)\\nHas a long open section.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f39b438248824cb6f72da46a8ab39603\", \"url\": \"https://api.pillowcase.su/api/download/f39b438248824cb6f72da46a8ab39603\", \"size\": \"4.23 MB\", \"duration\": 185.64}", "aliases": [], "size": "4.23 MB"}, {"id": "new-angels", "name": "✨ New Angels [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - New Angels Ref (1.7.14)\nYeezus 2 era throwaway. First 46 seconds have no mumble, apart from that it has singing with some mumble. Lossless unedited file leaked in 2022.", "length": "198.77", "fileDate": 14557536, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/a7375dcf06e16b18bd60412477326f4b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7375dcf06e16b18bd60412477326f4b\", \"key\": \"New Angels\", \"title\": \"\\u2728 New Angels [V1]\", \"description\": \"OG Filename: KW - New Angels Ref (1.7.14)\\nYeezus 2 era throwaway. First 46 seconds have no mumble, apart from that it has singing with some mumble. Lossless unedited file leaked in 2022.\", \"date\": 14557536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"58644f0285a085c196b708b8c01e5256\", \"url\": \"https://api.pillowcase.su/api/download/58644f0285a085c196b708b8c01e5256\", \"size\": \"4.44 MB\", \"duration\": 198.77}", "aliases": [], "size": "4.44 MB"}, {"id": "nobody", "name": "Nobody [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> (1.7.14)\nFeatured on a whiteboard from early 2014. Was given to Chief <PERSON>, for his album of the same name, <PERSON>. Features the same vocals used in other versions, but with the production that was used on the released version. Samples \"Brother's Gonna Work It Out\" by <PERSON>. Leaked as a bonus for the \"We Did It Kid\" groupbuy.", "length": "164.87", "fileDate": 16826400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/dfe29a35fe460a14b9fad139a90d0210", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dfe29a35fe460a14b9fad139a90d0210\", \"key\": \"Nobody\", \"title\": \"Nobody [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>W - <PERSON> (1.7.14)\\nFeatured on a whiteboard from early 2014. Was given to Chief <PERSON>, for his album of the same name, <PERSON>. Features the same vocals used in other versions, but with the production that was used on the released version. Samples \\\"Brother's Gonna Work It Out\\\" by <PERSON>. Leaked as a bonus for the \\\"We Did It Kid\\\" groupbuy.\", \"date\": 16826400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f480de51c30fb33a7c2189daa14e70fd\", \"url\": \"https://api.pillowcase.su/api/download/f480de51c30fb33a7c2189daa14e70fd\", \"size\": \"3.9 MB\", \"duration\": 164.87}", "aliases": [], "size": "3.9 MB"}, {"id": "nobody-67", "name": "Nobody [V2]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: 15 KW - <PERSON> <PERSON><PERSON> (1.28.14)\nHas drums sampled from \"I'm Glad You're Mine\" by <PERSON>. Original snippet leaked November 24th, 2022.", "length": "197.84", "fileDate": 16748640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c45168eebf98638502e7edbf26171309", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c45168eebf98638502e7edbf26171309\", \"key\": \"Nobody\", \"title\": \"Nobody [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 15 KW - <PERSON> Ref <PERSON> (1.28.14)\\nHas drums sampled from \\\"I'm Glad You're Mine\\\" by <PERSON>. Original snippet leaked November 24th, 2022.\", \"date\": 16748640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3d3ab54848080b0ef131ba38741b384b\", \"url\": \"https://api.pillowcase.su/api/download/3d3ab54848080b0ef131ba38741b384b\", \"size\": \"4.43 MB\", \"duration\": 197.84}", "aliases": [], "size": "4.43 MB"}, {"id": "nobody-68", "name": "Nobody [V3]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON><PERSON> - <PERSON> (1.28.14)\nHas alternate drums.", "length": "197.84", "fileDate": 16816896, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/db796b123a857569da1daf3952c6560a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/db796b123a857569da1daf3952c6560a\", \"key\": \"Nobody\", \"title\": \"Nobody [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> - <PERSON> (1.28.14)\\nHas alternate drums.\", \"date\": 16816896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"13de21e14652f0893ccb4d2c1a0229f9\", \"url\": \"https://api.pillowcase.su/api/download/13de21e14652f0893ccb4d2c1a0229f9\", \"size\": \"4.43 MB\", \"duration\": 197.84}", "aliases": [], "size": "4.43 MB"}, {"id": "nobody-69", "name": "Nobody [V4]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: KW - <PERSON> Ref (2.11.14)\nVersion of \"Nobody\" with the same content as the January 7th version, but is longer due to it being restructured. Original snippet leaked April 11th, 2023, with the full song later leaking as a bonus for the \"We Did It Kid\" groupbuy.", "length": "201.96", "fileDate": 16826400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/62f1c0084194030b35b292596feceba3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/62f1c0084194030b35b292596feceba3\", \"key\": \"Nobody\", \"title\": \"Nobody [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: KW - <PERSON> Ref (2.11.14)\\nVersion of \\\"Nobody\\\" with the same content as the January 7th version, but is longer due to it being restructured. Original snippet leaked April 11th, 2023, with the full song later leaking as a bonus for the \\\"We Did It Kid\\\" groupbuy.\", \"date\": 16826400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1a5cb112b330bbb46d9d755f2c35aa7c\", \"url\": \"https://api.pillowcase.su/api/download/1a5cb112b330bbb46d9d755f2c35aa7c\", \"size\": \"4.5 MB\", \"duration\": 201.96}", "aliases": [], "size": "4.5 MB"}, {"id": "nobody-70", "name": "Nobody [V5]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: <PERSON>W - <PERSON> <PERSON> (2.19.14)\n<PERSON><PERSON> Ambrosio<PERSON> reference track. Original snippet leaked April 11th, 2023.", "length": "68.04", "fileDate": 16824672, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/ef5101fc555c00e21ff841328d6cea22", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef5101fc555c00e21ff841328d6cea22\", \"key\": \"Nobody\", \"title\": \"Nobody [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: KW - <PERSON> <PERSON> (2.19.14)\\nMarsha Ambrosious reference track. Original snippet leaked April 11th, 2023.\", \"date\": 16824672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"acd272a0bf1cc1bdac3abc083f54a939\", \"url\": \"https://api.pillowcase.su/api/download/acd272a0bf1cc1bdac3abc083f54a939\", \"size\": \"2.35 MB\", \"duration\": 68.04}", "aliases": [], "size": "2.35 MB"}, {"id": "nobody-71", "name": "Nobody [V6]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: KW - <PERSON> (2.24.14)\n<PERSON> \"Nobody\" reference track. Original snippet leaked April 11th, 2023, with the full song later leaking as a bonus for the \"We Did It Kid\" groupbuy.", "length": "70.1", "fileDate": 16826400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/a2d756616847853618b50fe9a7abca67", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2d756616847853618b50fe9a7abca67\", \"key\": \"Nobody\", \"title\": \"Nobody [V6]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>W - <PERSON> (2.24.14)\\n<PERSON><PERSON><PERSON> \\\"Nobody\\\" reference track. Original snippet leaked April 11th, 2023, with the full song later leaking as a bonus for the \\\"We Did It Kid\\\" groupbuy.\", \"date\": 16826400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e29b712cf3cb5779bacab22addc0cb00\", \"url\": \"https://api.pillowcase.su/api/download/e29b712cf3cb5779bacab22addc0cb00\", \"size\": \"2.39 MB\", \"duration\": 70.1}", "aliases": [], "size": "2.39 MB"}, {"id": "nobody-72", "name": "Nobody [V7]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Nobody (<PERSON> 3.1.14)\n<PERSON> \"Nobody\" reference track. Latest known version before it was given to Chief <PERSON><PERSON>. Original snippet leaked April 11th, 2023, with the full song later leaking as a bonus for the \"We Did It Kid\" groupbuy.", "length": "263.77", "fileDate": 16826400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3414a690ee996b74c6a001ca3805e317", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3414a690ee996b74c6a001ca3805e317\", \"key\": \"Nobody\", \"title\": \"Nobody [V7]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Nobody (<PERSON> 3.1.14)\\n<PERSON><PERSON><PERSON> \\\"Nobody\\\" reference track. Latest known version before it was given to Chief <PERSON><PERSON>. Original snippet leaked April 11th, 2023, with the full song later leaking as a bonus for the \\\"We Did It Kid\\\" groupbuy.\", \"date\": 16826400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a8d120bd25a8d76428f259763888f305\", \"url\": \"https://api.pillowcase.su/api/download/a8d120bd25a8d76428f259763888f305\", \"size\": \"5.49 MB\", \"duration\": 263.77}", "aliases": [], "size": "5.49 MB"}, {"id": "on-my-mind", "name": "On My Mind", "artists": [], "producers": ["Mustard"], "notes": "OG Filename: KW - On My Mind Ref (1.6.14)\nCollaboration with <PERSON><PERSON> made after the two co-produced the beat to <PERSON>' \"Sanctified\". Was originally thought to be the \"MM\" track on <PERSON><PERSON><PERSON>'s Yeezus 2 Spotify playlist, but that turned out to be \"Mrs. <PERSON>sery\".", "length": "146.82", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e2fae5f32903e0d3d9deb05a7080e3f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e2fae5f32903e0d3d9deb05a7080e3f4\", \"key\": \"On My Mind\", \"title\": \"On My Mind\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"My Mind\"], \"description\": \"OG Filename: KW - On My Mind Ref (1.6.14)\\nCollaboration with <PERSON><PERSON> made after the two co-produced the beat to <PERSON>' \\\"Sanctified\\\". Was originally thought to be the \\\"MM\\\" track on CyHi's Yeezus 2 Spotify playlist, but that turned out to be \\\"Mrs. Misery\\\".\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3c4486bab518e99a8cfec57de67fabf5\", \"url\": \"https://api.pillowcase.su/api/download/3c4486bab518e99a8cfec57de67fabf5\", \"size\": \"3.61 MB\", \"duration\": 146.82}", "aliases": ["My Mind"], "size": "3.61 MB"}, {"id": "rich-girl", "name": "Rich Girl [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON> (1.28.14)\n2014 beat from a Yeezus 2 copy. Original snippet leaked October 14th, 2024. Samples \"<PERSON> Girl\" by <PERSON> & <PERSON>.", "length": "149.14", "fileDate": 17329248, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/1c7c71c5e154a186961afe60b1904ef4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c7c71c5e154a186961afe60b1904ef4\", \"key\": \"Rich Girl\", \"title\": \"<PERSON> Girl [V3]\", \"description\": \"OG Filename: <PERSON><PERSON> <PERSON> <PERSON> (1.28.14)\\n2014 beat from a Yeezus 2 copy. Original snippet leaked October 14th, 2024. <PERSON><PERSON> \\\"Rich Girl\\\" by <PERSON>.\", \"date\": 17329248, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dd11c222a5a444a94c736bf15d43d004\", \"url\": \"https://api.pillowcase.su/api/download/dd11c222a5a444a94c736bf15d43d004\", \"size\": \"3.65 MB\", \"duration\": 149.14}", "aliases": [], "size": "3.65 MB"}, {"id": "roses-and-revolvers", "name": "Roses and Revolvers", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - Roses and Revolvers Ref 1.9.14\nFreestyle produced by <PERSON>.", "length": "221.6", "fileDate": 16956000, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/099f7e2fcb9865b4f02649228a572e8c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/099f7e2fcb9865b4f02649228a572e8c\", \"key\": \"Roses and Revolvers\", \"title\": \"Roses and Revolvers\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KW - Roses and Revolvers Ref 1.9.14\\nFreestyle produced by <PERSON>.\", \"date\": 16956000, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5d66c5ed177cf128d75dc8a07b38752e\", \"url\": \"https://api.pillowcase.su/api/download/5d66c5ed177cf128d75dc8a07b38752e\", \"size\": \"4.81 MB\", \"duration\": 221.6}", "aliases": [], "size": "4.81 MB"}, {"id": "special", "name": "Special [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Special Ref (1.7.14)\n Earliest known version. Features a rougher and less developed instrumental, and more mumble vocals.\nEditor Note: All 4 versions of \"Special\" wont upload to Pillowcase, keep Pixeldrain.", "length": "131.79", "fileDate": 15975360, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pixeldrain.com/u/Bdwnr7iq", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pixeldrain.com/u/Bdwnr7iq\", \"key\": \"Special\", \"title\": \"Special [V1]\", \"description\": \"OG Filename: KW - Special Ref (1.7.14)\\n Earliest known version. Features a rougher and less developed instrumental, and more mumble vocals.\\nEditor Note: All 4 versions of \\\"Special\\\" wont upload to Pillowcase, keep Pixeldrain.\", \"date\": 15975360, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2afffbdbb4e3de5fbae388c2f6f7f6ad\", \"url\": \"https://api.pillowcase.su/api/download/2afffbdbb4e3de5fbae388c2f6f7f6ad\", \"size\": \"3.37 MB\", \"duration\": 131.79}", "aliases": [], "size": "3.37 MB"}, {"id": "special-77", "name": "Special [V2]", "artists": [], "producers": [], "notes": "A cut-down of the previous version with refined piano and better structure. Could be from January 28th, as that's when some January 7th demos were worked on further. Previously thought to be later than the \"Bitch Please\" version.", "length": "", "fileDate": 15961536, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/08cb768d5128ae99d9594c2a4b33990c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08cb768d5128ae99d9594c2a4b33990c\", \"key\": \"Special\", \"title\": \"Special [V2]\", \"description\": \"A cut-down of the previous version with refined piano and better structure. Could be from January 28th, as that's when some January 7th demos were worked on further. Previously thought to be later than the \\\"Bitch Please\\\" version.\", \"date\": 15961536, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "so-special", "name": "So Special [V3]", "artists": [], "producers": [], "notes": "OG Filenames: KW - So Special Ref (1.9.14) & \nKW - S0 Special Ref (1.9.14)\nSame as the previous version but 4 bars shorter at the very end.", "length": "105.18", "fileDate": 17021664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pixeldrain.com/u/KsLnBNeG", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pixeldrain.com/u/KsLnBNeG\", \"key\": \"So Special\", \"title\": \"So Special [V3]\", \"aliases\": [\"Special\"], \"description\": \"OG Filenames: KW - So Special Ref (1.9.14) & \\nKW - S0 Special Ref (1.9.14)\\nSame as the previous version but 4 bars shorter at the very end.\", \"date\": 17021664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9a04037b51a2706282dd6cf083bf8fa3\", \"url\": \"https://api.pillowcase.su/api/download/9a04037b51a2706282dd6cf083bf8fa3\", \"size\": \"2.95 MB\", \"duration\": 105.18}", "aliases": ["Special"], "size": "2.95 MB"}, {"id": "thank-you", "name": "Thank You [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - Thank You\nInstrumental for an early version.", "length": "142.5", "fileDate": 16955136, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/f89393d39df7f04cc105acbb6e9bfa2a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f89393d39df7f04cc105acbb6e9bfa2a\", \"key\": \"Thank You\", \"title\": \"Thank You [V2]\", \"description\": \"OG Filename: KW - Thank You\\nInstrumental for an early version.\", \"date\": 16955136, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"202f3879472a4a9f898bf23e58886103\", \"url\": \"https://api.pillowcase.su/api/download/202f3879472a4a9f898bf23e58886103\", \"size\": \"3.54 MB\", \"duration\": 142.5}", "aliases": [], "size": "3.54 MB"}, {"id": "thank-you-80", "name": "Thank You [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - Thank You Marsha 2.19.14\nMarsha Ambrosious reference track.", "length": "259.85", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/63c3598bedf2a0df4d152dbd8875dd42", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/63c3598bedf2a0df4d152dbd8875dd42\", \"key\": \"Thank You\", \"title\": \"Thank You [V3]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Thank You Marsha 2.19.14\\nMarsha Ambrosious reference track.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8781838c24299138e2490c607c9566d7\", \"url\": \"https://api.pillowcase.su/api/download/8781838c24299138e2490c607c9566d7\", \"size\": \"5.42 MB\", \"duration\": 259.85}", "aliases": [], "size": "5.42 MB"}, {"id": "thank-you-81", "name": "Thank You [V4]", "artists": [], "producers": [], "notes": "OG Filename: KW - Thank You Livvi 2.20.14\nLivvi Franc reference track.", "length": "384.74", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0a0cc1ad875307afd1c28f8aa841d158", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a0cc1ad875307afd1c28f8aa841d158\", \"key\": \"Thank You\", \"title\": \"Thank You [V4]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Thank You Livvi 2.20.14\\nLivvi Franc reference track.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a624b27992c818c6e0f7237bd6315e09\", \"url\": \"https://api.pillowcase.su/api/download/a624b27992c818c6e0f7237bd6315e09\", \"size\": \"7.42 MB\", \"duration\": 384.74}", "aliases": [], "size": "7.42 MB"}, {"id": "thank-you-82", "name": "Thank You [V5]", "artists": [], "producers": [], "notes": "OG Filename: KW - Thank You Andrew 2.24.14 (Sec 40)\n<PERSON> reference track.", "length": "70", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e17978ff6c324a4d192856c882c85617", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e17978ff6c324a4d192856c882c85617\", \"key\": \"Thank You\", \"title\": \"Thank You [V5]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"OG Filename: KW - Thank You Andrew 2.24.14 (Sec 40)\\nAnd<PERSON> reference track.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"03981f6972858b88e55b9470c4174870\", \"url\": \"https://api.pillowcase.su/api/download/03981f6972858b88e55b9470c4174870\", \"size\": \"2.38 MB\", \"duration\": 70}", "aliases": [], "size": "2.38 MB"}, {"id": "use-your-love", "name": "✨ Use Your Love", "artists": [], "producers": ["DJ <PERSON><PERSON>"], "notes": "OG Filename: KW - Use Your Love Ref (1.8.14)\nFreestyle seen in a filelist. Only the instrumental has been leaked. Samples \"Your Love\" by The Outfield.", "length": "120.19", "fileDate": "", "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/94f9ac4eb1a62649848b0bb5492f1a78", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/94f9ac4eb1a62649848b0bb5492f1a78\", \"key\": \"Use Your Love\", \"title\": \"\\u2728 Use Your Love\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KW - Use Your Love Ref (1.8.14)\\nFreestyle seen in a filelist. Only the instrumental has been leaked. <PERSON><PERSON> \\\"Your Love\\\" by The Outfield.\", \"date\": null, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"310bf958648ca45158379d65dd2211b7\", \"url\": \"https://api.pillowcase.su/api/download/310bf958648ca45158379d65dd2211b7\", \"size\": \"3.19 MB\", \"duration\": 120.19}", "aliases": [], "size": "3.19 MB"}, {"id": "soul-dog", "name": "Soul Dog [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Soul <PERSON> (1.6.14)\nMostly mumble freestyle containing more vocals than other versions. Samples \"Can't Stop Loving You\" by Soul Dog.", "length": "219.06", "fileDate": 16816896, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/882ee5021f68855f77053c09cb2555b3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/882ee5021f68855f77053c09cb2555b3\", \"key\": \"Soul Dog\", \"title\": \"Soul Dog [V1]\", \"aliases\": [\"When I See It\", \"Tell Your Friends\"], \"description\": \"OG Filename: KW - Soul Dog Ref (1.6.14)\\nMostly mumble freestyle containing more vocals than other versions. <PERSON><PERSON> \\\"Can't Stop Loving You\\\" by Soul Dog.\", \"date\": 16816896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"920df43e03ea9d2257ec2a516b94a386\", \"url\": \"https://api.pillowcase.su/api/download/920df43e03ea9d2257ec2a516b94a386\", \"size\": \"4.77 MB\", \"duration\": 219.06}", "aliases": ["When I See It", "Tell Your Friends"], "size": "4.77 MB"}, {"id": "when-i-see-it", "name": "When I See It [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - When I See It Ref (2.11.14)\nFeatures about 1:30 of vocals, and a minute of open verse.", "length": "154.69", "fileDate": 16714944, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/da8fa6c0876698654bf1fbf25a96bbc8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/da8fa6c0876698654bf1fbf25a96bbc8\", \"key\": \"When I See It\", \"title\": \"When I See It [V2]\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"OG Filename: KW - When I See It Ref (2.11.14)\\nFeatures about 1:30 of vocals, and a minute of open verse.\", \"date\": 16714944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"864819b0e258226958eeca444fcf435b\", \"url\": \"https://api.pillowcase.su/api/download/864819b0e258226958eeca444fcf435b\", \"size\": \"3.74 MB\", \"duration\": 154.69}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "3.74 MB"}, {"id": "when-i-see-it-86", "name": "When I See It [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - When I See It <PERSON><PERSON> (2.19.14) \nMarsha Ambrosious reference track. Snippet leaked on April 28th, 2024.", "length": "15.67", "fileDate": 17142624, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/096ea5f94e361d33265fdd225f80487f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/096ea5f94e361d33265fdd225f80487f\", \"key\": \"When I See It\", \"title\": \"When I See It [V3]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"OG Filename: KW - When I See It Marsha Ref (2.19.14) \\nMarsha Ambrosious reference track. Snippet leaked on April 28th, 2024.\", \"date\": 17142624, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6e2a0f5be6d8252b6fe6e4ac49c28686\", \"url\": \"https://api.pillowcase.su/api/download/6e2a0f5be6d8252b6fe6e4ac49c28686\", \"size\": \"1.51 MB\", \"duration\": 15.67}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "1.51 MB"}, {"id": "wolves", "name": "Wolves [V2]", "artists": [], "producers": [], "notes": "OG Filename: W\nEarly version with no vocals. Leaked after a groupbuy.", "length": "179.25", "fileDate": 17216064, "leakDate": "", "availableLength": "Beat Only", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0aba0dcff86537c83fa461424108dbef", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0aba0dcff86537c83fa461424108dbef\", \"key\": \"Wolves\", \"title\": \"Wolves [V2]\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: W\\nEarly version with no vocals. Leaked after a groupbuy.\", \"date\": 17216064, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f81922918b3e3126db23d5fa9c033ba3\", \"url\": \"https://api.pillowcase.su/api/download/f81922918b3e3126db23d5fa9c033ba3\", \"size\": \"4.13 MB\", \"duration\": 179.25}", "aliases": ["Lost"], "size": "4.13 MB"}, {"id": "wolves-88", "name": "Wolves [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - Wolves Ref 2.11.14\nHas the same first verse as the release version of the song.", "length": "166.4", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/57a380d02218cdfaebde05dbf967df01", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/57a380d02218cdfaebde05dbf967df01\", \"key\": \"Wolves\", \"title\": \"Wolves [V3]\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: KW - Wolves Ref 2.11.14\\nHas the same first verse as the release version of the song.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"034c4a808785e19ded262fc6b67d53d7\", \"url\": \"https://api.pillowcase.su/api/download/034c4a808785e19ded262fc6b67d53d7\", \"size\": \"3.93 MB\", \"duration\": 166.4}", "aliases": ["Lost"], "size": "3.93 MB"}, {"id": "wolves-89", "name": "Wolves [V4]", "artists": [], "producers": [], "notes": "OG Filename: KW - Wolves Livvi Ref 2.20.14\nLivvi Franc reference track. Features drastically different production than later versions of the song.", "length": "351.75", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/cffd90d81635e77cdd40e5a8e47fbabd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cffd90d81635e77cdd40e5a8e47fbabd\", \"key\": \"Wolves\", \"title\": \"Wolves [V4]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: KW - Wolves Livvi Ref 2.20.14\\nLivvi Franc reference track. Features drastically different production than later versions of the song.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a2f667a4e1bbe20a026b56f76f05bebf\", \"url\": \"https://api.pillowcase.su/api/download/a2f667a4e1bbe20a026b56f76f05bebf\", \"size\": \"6.9 MB\", \"duration\": 351.75}", "aliases": ["Lost"], "size": "6.9 MB"}, {"id": "wolves-90", "name": "Wolves [V5]", "artists": [], "producers": [], "notes": "OG Filename: KW - Wolves Boots Ref 2.28.14\nBoots reference track. Has different production from later versions of the song.", "length": "409.3", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b10ffc87aca1408c1ecb3a78fcf6dc32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b10ffc87aca1408c1ecb3a78fcf6dc32\", \"key\": \"Wolves\", \"title\": \"Wolves [V5]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: KW - Wolves Boots Ref 2.28.14\\nBoots reference track. Has different production from later versions of the song.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a73ed48d5ab9c3ef47402e46f75023ad\", \"url\": \"https://api.pillowcase.su/api/download/a73ed48d5ab9c3ef47402e46f75023ad\", \"size\": \"7.82 MB\", \"duration\": 409.3}", "aliases": ["Lost"], "size": "7.82 MB"}, {"id": "wolves-91", "name": "Wolves [V6]", "artists": [], "producers": [], "notes": "OG Filename: Wolves Andrea <PERSON> 2.28.14\n<PERSON> reference track.", "length": "230.25", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/56047a8f089f0ae29fe450b604dd4c57", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/56047a8f089f0ae29fe450b604dd4c57\", \"key\": \"Wolves\", \"title\": \"Wolves [V6]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: Wolves Andrea Ref 2.28.14\\nAnd<PERSON> reference track.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"84cda69fabbdc54db6a44c28230d9565\", \"url\": \"https://api.pillowcase.su/api/download/84cda69fabbdc54db6a44c28230d9565\", \"size\": \"4.95 MB\", \"duration\": 230.25}", "aliases": ["Lost"], "size": "4.95 MB"}, {"id": "wolves-92", "name": "Wolves [V7]", "artists": [], "producers": [], "notes": "OG Filename: Wolves Sam Dew Ref 3.1.14\nSam Dew reference track.", "length": "191.89", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/f306c863626f746d58785611758a03c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f306c863626f746d58785611758a03c8\", \"key\": \"Wolves\", \"title\": \"Wolves [V7]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: Wolves Sam Dew Ref 3.1.14\\nSam Dew reference track.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ef3fb2b36e798ffa8b31e716563c06b8\", \"url\": \"https://api.pillowcase.su/api/download/ef3fb2b36e798ffa8b31e716563c06b8\", \"size\": \"4.34 MB\", \"duration\": 191.89}", "aliases": ["Lost"], "size": "4.34 MB"}, {"id": "wondering", "name": "Wondering [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - <PERSON><PERSON> Re<PERSON> (1.8.14)\nRough demo appearing on a whiteboard from early 2014. Samples \"Got Me Wondering\" by <PERSON><PERSON>.", "length": "75.59", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b711226fe0f0fdf884b72f5ec335e191", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b711226fe0f0fdf884b72f5ec335e191\", \"key\": \"Wondering\", \"title\": \"Wondering [V1]\", \"description\": \"OG Filename: <PERSON><PERSON> - <PERSON>ing Ref (1.8.14)\\nRough demo appearing on a whiteboard from early 2014. Samples \\\"Got Me Wondering\\\" by <PERSON><PERSON>.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"03bef0d56b582319296a771f188dc5d5\", \"url\": \"https://api.pillowcase.su/api/download/03bef0d56b582319296a771f188dc5d5\", \"size\": \"2.47 MB\", \"duration\": 75.59}", "aliases": [], "size": "2.47 MB"}, {"id": "wondering-94", "name": "Wondering [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON><PERSON> Ref (1.9.14)\nSimilar to later versions but with a slightly different outro.", "length": "207.87", "fileDate": 16955136, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/8877e6bd3bd7039638a4f1909a3ec65d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8877e6bd3bd7039638a4f1909a3ec65d\", \"key\": \"Wondering\", \"title\": \"Wondering [V2]\", \"description\": \"OG Filename: K<PERSON> - <PERSON>ing Ref (1.9.14)\\nSimilar to later versions but with a slightly different outro.\", \"date\": 16955136, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"64b7bd20630e0fd8dc79cf60d3ac5e28\", \"url\": \"https://api.pillowcase.su/api/download/64b7bd20630e0fd8dc79cf60d3ac5e28\", \"size\": \"4.59 MB\", \"duration\": 207.87}", "aliases": [], "size": "4.59 MB"}, {"id": "wondering-95", "name": "Wondering [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> - <PERSON><PERSON> Ref (1.28.14)\nFurther developed version. Instrumental leaked September 24th, 2023.", "length": "204.94", "fileDate": 16955136, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/46b480726f170c62941acf529f468cd2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/46b480726f170c62941acf529f468cd2\", \"key\": \"Wondering\", \"title\": \"Wondering [V3]\", \"description\": \"OG Filename: KW - <PERSON>ing Ref (1.28.14)\\nFurther developed version. Instrumental leaked September 24th, 2023.\", \"date\": 16955136, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1f4f2f901c47d9e82e4eb612a883e0fc\", \"url\": \"https://api.pillowcase.su/api/download/1f4f2f901c47d9e82e4eb612a883e0fc\", \"size\": \"4.54 MB\", \"duration\": 204.94}", "aliases": [], "size": "4.54 MB"}, {"id": "you-ain-t-no-model", "name": "✨ You Ain't No Model [V2]", "artists": [], "producers": [], "notes": "OG Filenames: <PERSON>W - You Ain't No Model Ref 2 (1.7.14) & \nKW - You Ain't No Model Ref (1.7.14)\nEarlier version bounced as just \"Ref\" but later renamed to \"Ref 2\". Has a longer beatswitch with more vocals compared to \"Ref 3\".", "length": "227.1", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/19b6e847ebd4af0b82b25052bd148668", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/19b6e847ebd4af0b82b25052bd148668\", \"key\": \"You Ain't No Model\", \"title\": \"\\u2728 You Ain't No Model [V2]\", \"aliases\": [\"Model\", \"Model Type\"], \"description\": \"OG Filenames: KW - You Ain't No Model Ref 2 (1.7.14) & \\nKW - You Ain't No Model Ref (1.7.14)\\nEarlier version bounced as just \\\"Ref\\\" but later renamed to \\\"Ref 2\\\". Has a longer beatswitch with more vocals compared to \\\"Ref 3\\\".\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d14b11d0ccf2f58aa7b310344c15201a\", \"url\": \"https://api.pillowcase.su/api/download/d14b11d0ccf2f58aa7b310344c15201a\", \"size\": \"4.9 MB\", \"duration\": 227.1}", "aliases": ["Model", "Model Type"], "size": "4.9 MB"}, {"id": "you-ain-t-no-model-97", "name": "You Ain't No Model [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - You Ain't No Model Ref 3 (1.7.14)\nHas a beat switch and different drums.", "length": "198.91", "fileDate": 16779744, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2d31322fc363b9a8239cb33115ce970e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d31322fc363b9a8239cb33115ce970e\", \"key\": \"You Ain't No Model\", \"title\": \"You Ain't No Model [V3]\", \"aliases\": [\"Model\", \"Model Type\"], \"description\": \"OG Filename: KW - You Ain't No Model Ref 3 (1.7.14)\\nHas a beat switch and different drums.\", \"date\": 16779744, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0bd35e0f9a4c1a2f3b1fc33a1041a705\", \"url\": \"https://api.pillowcase.su/api/download/0bd35e0f9a4c1a2f3b1fc33a1041a705\", \"size\": \"4.44 MB\", \"duration\": 198.91}", "aliases": ["Model", "Model Type"], "size": "4.44 MB"}, {"id": "you-ain-t-no-model-98", "name": "You Ain't No Model [V4]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> Ain't No Model Ref (1.28.14)\nFound on the leaked Yeezus 2 tracklist. Includes few Kanye lyrics, mostly just the song's title repeated with a few extra ad-libs. Vocals are taken from a low quality recording and background noise is present. He never re-recorded this hook. Originally leaked as a bonus for the \"Welcome To My Life\" groupbuy, before the OG file later leaked.", "length": "63.92", "fileDate": 16804800, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/cf93ae0d01adc62548565e7d308150f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf93ae0d01adc62548565e7d308150f4\", \"key\": \"You Ain't No Model\", \"title\": \"You Ain't No Model [V4]\", \"aliases\": [\"Model\", \"Model Type\"], \"description\": \"OG Filename: KW - You Ain't No Model Ref (1.28.14)\\nFound on the leaked Yeezus 2 tracklist. Includes few Kanye lyrics, mostly just the song's title repeated with a few extra ad-libs. Vocals are taken from a low quality recording and background noise is present. He never re-recorded this hook. Originally leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy, before the OG file later leaked.\", \"date\": 16804800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d63b4fc0f2d9043d2783b7520915625e\", \"url\": \"https://api.pillowcase.su/api/download/d63b4fc0f2d9043d2783b7520915625e\", \"size\": \"2.29 MB\", \"duration\": 63.92}", "aliases": ["Model", "Model Type"], "size": "2.29 MB"}, {"id": "you-ain-t-no-model-99", "name": "You Ain't No Model [V5]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - You Ain't No Model <PERSON> (2.20.14)\nA later version with <PERSON><PERSON> vocals. Has a different beatswitch.", "length": "259.27", "fileDate": 16779744, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/21789a0c5b625942c09f1208616c0874", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/21789a0c5b625942c09f1208616c0874\", \"key\": \"You Ain't No Model\", \"title\": \"You Ain't No Model [V5]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Model\", \"Model Type\"], \"description\": \"OG Filename: KW - You Ain't No Model Livvi Ref (2.20.14)\\nA later version with <PERSON><PERSON> vocals. Has a different beatswitch.\", \"date\": 16779744, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"62f78bbb30bd88191b26829a3f65e165\", \"url\": \"https://api.pillowcase.su/api/download/62f78bbb30bd88191b26829a3f65e165\", \"size\": \"5.41 MB\", \"duration\": 259.27}", "aliases": ["Model", "Model Type"], "size": "5.41 MB"}, {"id": "", "name": "???", "artists": [], "producers": ["Hit-Boy"], "notes": "OG Filename: KW - Hit-<PERSON> (11.11.13)\nSong from the earliest known copy of Yeezus 2. Contains mumble.", "length": "118", "fileDate": 16792704, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/de73ff80b668ddd131094300b815ec04", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/de73ff80b668ddd131094300b815ec04\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON>-<PERSON>)\", \"aliases\": [\"Hit-Boy Idea\"], \"description\": \"OG Filename: KW - Hit-Boy <PERSON>f (11.11.13)\\nSong from the earliest known copy of Yeezus 2. Contains mumble.\", \"date\": 16792704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25e11eac97bf5cdc1a04206ba25ef516\", \"url\": \"https://api.pillowcase.su/api/download/25e11eac97bf5cdc1a04206ba25ef516\", \"size\": \"3.15 MB\", \"duration\": 118}", "aliases": ["Hit-Boy I<PERSON>"], "size": "3.15 MB"}, {"id": "-101", "name": "??? [V2]", "artists": ["The Weeknd"], "producers": ["MIKE DEAN"], "notes": "Early version of the Weeknd song \"King of the Fall\" that is said to be in a April Yeezus 2 copy. A rough vocal take of the song with mumble and is also rumored to be produced by MIKE DEAN with a different beat than final.", "length": "228.17", "fileDate": 17038080, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e7e4c078f8170bd529f9fe9a71832b12", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7e4c078f8170bd529f9fe9a71832b12\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(feat. The Weeknd) (prod. <PERSON><PERSON> DEAN)\", \"aliases\": [\"King Of The Fall\"], \"description\": \"Early version of the Weeknd song \\\"King of the Fall\\\" that is said to be in a April Yeezus 2 copy. A rough vocal take of the song with mumble and is also rumored to be produced by MIKE DEAN with a different beat than final.\", \"date\": 17038080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cf22ca1e41bcdc04e82e0b83778fbc45\", \"url\": \"https://api.pillowcase.su/api/download/cf22ca1e41bcdc04e82e0b83778fbc45\", \"size\": \"4.16 MB\", \"duration\": 228.17}", "aliases": ["King Of The Fall"], "size": "4.16 MB"}, {"id": "might-not", "name": "The Weeknd - Might Not [V3]", "artists": [], "producers": ["MIKE DEAN"], "notes": "Demo under the name \"Might Not\". Origianl snippet was posted back in 2021 and was known as \"ROI\", but that name is fake.", "length": "305.74", "fileDate": 17038080, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/570c426040779d1c8dad6446137a6d99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/570c426040779d1c8dad6446137a6d99\", \"key\": \"Might Not\", \"title\": \"The Weeknd - Might Not [V3]\", \"artists\": \"(prod. <PERSON><PERSON> DEAN)\", \"aliases\": [\"King Of The Fall\", \"ROI\"], \"description\": \"Demo under the name \\\"Might Not\\\". Origianl snippet was posted back in 2021 and was known as \\\"ROI\\\", but that name is fake.\", \"date\": 17038080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9134abb40bc1e5cdab47002cebf85a05\", \"url\": \"https://api.pillowcase.su/api/download/9134abb40bc1e5cdab47002cebf85a05\", \"size\": \"5.41 MB\", \"duration\": 305.74}", "aliases": ["King Of The Fall", "ROI"], "size": "5.41 MB"}, {"id": "been-trill", "name": "DJ <PERSON><PERSON> <PERSON> <PERSON> [V1]", "artists": ["Treated Crew"], "producers": ["DJ <PERSON><PERSON>"], "notes": "OG Filename: BEENTRILL REAL\nFirst version of \"Been Trill\" made. Contains open verses. Snippet was played on a technically released EP that <PERSON> made, but seemingly off most platforms. Leaked in full on December 27, 2022.", "length": "185.86", "fileDate": 16720992, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/edc188a9a51d084d3938a552ce64cfe6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edc188a9a51d084d3938a552ce64cfe6\", \"key\": \"<PERSON> Trill\", \"title\": \"<PERSON> <PERSON><PERSON> <PERSON> Been <PERSON>ll [V1]\", \"artists\": \"(feat. Treated <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3500\", \"I Been Trill\"], \"description\": \"OG Filename: BEENTRILL REAL\\nFirst version of \\\"Been Trill\\\" made. Contains open verses. Snippet was played on a technically released EP that <PERSON> made, but seemingly off most platforms. Leaked in full on December 27, 2022.\", \"date\": 16720992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"854a8ead3ec4663806bc12740df6671a\", \"url\": \"https://api.pillowcase.su/api/download/854a8ead3ec4663806bc12740df6671a\", \"size\": \"4.24 MB\", \"duration\": 185.86}", "aliases": ["3500", "<PERSON> <PERSON>"], "size": "4.24 MB"}, {"id": "i-been-trill", "name": "Treated Crew - <PERSON> <PERSON> [V2]", "artists": ["<PERSON>"], "producers": ["DJ <PERSON><PERSON>"], "notes": "OG Filename: I BEEN TRILL TREATED CREW\nAccording to DJ <PERSON><PERSON>, \"Been Trill\" originally had Treated Crew member <PERSON><PERSON>, fellow Chicago rapper <PERSON>, and <PERSON> on it before being given to <PERSON><PERSON><PERSON>. <PERSON> has 46 seconds of vocals, part of which were reused for the chorus of \"3500\", and \"the rest of the song contains vocals from Treated <PERSON> (Mic Terror & Z Money)\". This was likely the version of the track sent to <PERSON><PERSON><PERSON>.", "length": "317.49", "fileDate": 16714080, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/85b38c9486ccd57f654e480d7a76cb92", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/85b38c9486ccd57f654e480d7a76cb92\", \"key\": \"I Been Trill\", \"title\": \"Treated <PERSON> - <PERSON> <PERSON> [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3500\", \"Been Trill\"], \"description\": \"OG Filename: I BEEN TRILL TREATED CREW\\nAccording to <PERSON>, \\\"Been Trill\\\" originally had Treated Crew member <PERSON><PERSON>, fellow Chicago rapper <PERSON>, and <PERSON> on it before being given to <PERSON><PERSON><PERSON>. <PERSON> has 46 seconds of vocals, part of which were reused for the chorus of \\\"3500\\\", and \\\"the rest of the song contains vocals from <PERSON><PERSON><PERSON> <PERSON> (Mic Terror & Z Money)\\\". This was likely the version of the track sent to Kanye.\", \"date\": 16714080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"df6d8ee344cca1d523c11c6c18912115\", \"url\": \"https://api.pillowcase.su/api/download/df6d8ee344cca1d523c11c6c18912115\", \"size\": \"6.34 MB\", \"duration\": 317.49}", "aliases": ["3500", "<PERSON>"], "size": "6.34 MB"}, {"id": "white-ferrari", "name": "<PERSON> - White Ferrari [V2]", "artists": [], "producers": [], "notes": "Version of \"White Ferrari\" that is similar to what he played at one of his Coachella performences. Partial low quality snippet leaked December 28th, 2023.", "length": "98.56", "fileDate": 17037216, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c89b22fb4fc950b24803dc154a511e61", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c89b22fb4fc950b24803dc154a511e61\", \"key\": \"White Ferrari\", \"title\": \"Frank <PERSON> - White Ferrari [V2]\", \"description\": \"Version of \\\"White Ferrari\\\" that is similar to what he played at one of his Coachella performences. Partial low quality snippet leaked December 28th, 2023.\", \"date\": 17037216, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"021c69ee420c11b7b81f5e2534688eae\", \"url\": \"https://api.pillowcase.su/api/download/021c69ee420c11b7b81f5e2534688eae\", \"size\": \"2.05 MB\", \"duration\": 98.56}", "aliases": [], "size": "2.05 MB"}, {"id": "trophy", "name": "Future - Trophy [V2]", "artists": ["Kanye West"], "producers": ["Metro Boomin", "Rock City"], "notes": "OG Filename: 24 Future - Trophy <PERSON><PERSON> (1.29.14)\n<PERSON><PERSON><PERSON> sent his verse \"in 24 hours\" of Future reaching out. Leaked after a GB finished. Has worse mixing compared to release, also does not have <PERSON>'s verse.", "length": "148.51", "fileDate": 17329248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/821c543a704313f6247d598e6ee0f484", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/821c543a704313f6247d598e6ee0f484\", \"key\": \"Trophy\", \"title\": \"Future - Trophy [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Metro Boomin & Rock City)\", \"aliases\": [\"I Won\"], \"description\": \"OG Filename: 24 Future - Trophy KW Verse (1.29.14)\\<PERSON><PERSON><PERSON><PERSON> sent his verse \\\"in 24 hours\\\" of Future reaching out. Leaked after a GB finished. Has worse mixing compared to release, also does not have <PERSON>'s verse.\", \"date\": 17329248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b82316afd0fe96b7f8fdb3e09e9b8373\", \"url\": \"https://api.pillowcase.su/api/download/b82316afd0fe96b7f8fdb3e09e9b8373\", \"size\": \"3.64 MB\", \"duration\": 148.51}", "aliases": ["I Won"], "size": "3.64 MB"}, {"id": "turn-up", "name": "<PERSON> - Turn Up", "artists": [], "producers": ["Mustard"], "notes": "OG Filename: Turn Up (MIX 01)\nThrowa<PERSON> <PERSON> song using the same beat later given to <PERSON> for \"I Don't Fuck With You\". Doesn't contain <PERSON><PERSON><PERSON> production at this point.", "length": "80.07", "fileDate": 16548192, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/344d7fc9380dc12e5645c8fa2ff468f7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/344d7fc9380dc12e5645c8fa2ff468f7\", \"key\": \"Turn Up\", \"title\": \"<PERSON> - Turn Up\", \"artists\": \"(prod. Mustard)\", \"aliases\": [\"I Don't Fuck With You\"], \"description\": \"OG Filename: Turn Up (MIX 01)\\nThrowaway Justin Bieber song using the same beat later given to <PERSON> for \\\"I Don't Fuck With You\\\". Doesn't contain Kanye production at this point.\", \"date\": 16548192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"efac1890d3c3aeb24c0deea9dcaac37d\", \"url\": \"https://api.pillowcase.su/api/download/efac1890d3c3aeb24c0deea9dcaac37d\", \"size\": \"2.54 MB\", \"duration\": 80.07}", "aliases": ["I Don't Fuck With You"], "size": "2.54 MB"}, {"id": "big-body-108", "name": "<PERSON> - Big Body [V3]", "artists": [], "producers": ["Mustard"], "notes": "Before <PERSON><PERSON><PERSON> redid the beat, <PERSON><PERSON> revealed he had a totally different version. Unknown if there are any vocals on this version, or if the beat had been officially given to <PERSON> yet. He plays the instrumental of the beat at 1:26.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://youtu.be/A4r_7yci9Gs?t=86", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://youtu.be/A4r_7yci9Gs?t=86\", \"key\": \"Big Body\", \"title\": \"<PERSON> Body [V3]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Sanctified\"], \"description\": \"Before <PERSON><PERSON><PERSON> redid the beat, <PERSON><PERSON> revealed he had a totally different version. Unknown if there are any vocals on this version, or if the beat had been officially given to <PERSON> yet. He plays the instrumental of the beat at 1:26.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Sanctified"], "size": ""}, {"id": "big-body-109", "name": "<PERSON> - Big Body [V4]", "artists": ["Kanye West"], "producers": ["Kanye West", "Mustard"], "notes": "May be the same as V4, but this is unknown. Has some different Kanye lines compared to final.", "length": "10.19", "fileDate": 17191872, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/deaa67120e70d8220b114a0504c425bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/deaa67120e70d8220b114a0504c425bb\", \"key\": \"Big Body\", \"title\": \"<PERSON> [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>e West & Mustard)\", \"aliases\": [\"Sanctified\"], \"description\": \"May be the same as V4, but this is unknown. Has some different Kanye lines compared to final.\", \"date\": 17191872, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"79a1ae4281cbaa93fde09fcbac0b1c03\", \"url\": \"https://api.pillowcase.su/api/download/79a1ae4281cbaa93fde09fcbac0b1c03\", \"size\": \"1.43 MB\", \"duration\": 10.19}", "aliases": ["Sanctified"], "size": "1.43 MB"}, {"id": "sanctified", "name": "<PERSON> - Sanctified [V8]", "artists": ["Kanye West", "Big Sean", "<PERSON>"], "producers": ["Kanye West", "Mustard"], "notes": "Original version of the <PERSON> song featuring <PERSON><PERSON><PERSON> and <PERSON>. Very slight changes to beat with no lyric changes.", "length": "389.12", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0e0f3bff1bfc816100aa450cc76339cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0e0f3bff1bfc816100aa450cc76339cf\", \"key\": \"Sanctified\", \"title\": \"<PERSON> - Sanctified [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & Mustard)\", \"aliases\": [\"Big Body\"], \"description\": \"Original version of the <PERSON> song featuring <PERSON><PERSON><PERSON> and <PERSON>. Very slight changes to beat with no lyric changes.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d3f8ce1782eefd014f9199a08ebb085f\", \"url\": \"https://api.pillowcase.su/api/download/d3f8ce1782eefd014f9199a08ebb085f\", \"size\": \"7.49 MB\", \"duration\": 389.12}", "aliases": ["Big Body"], "size": "7.49 MB"}, {"id": "after-you-111", "name": "✨ After You [V7]", "artists": [], "producers": ["Dom $olo"], "notes": "OG Filenames: KW - After You Ref @ 83 BPM (4.21.14) &\nAY\n Freestyle that has new vocals compared to the previous versions. Confirmed to be \"AY\" on CyHi's Yeezus 2 playlist. Sold by <PERSON><PERSON> on leakth.is.", "length": "300.77", "fileDate": 15989184, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/7525e8a16f3e0b5bf2892e16102411e2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7525e8a16f3e0b5bf2892e16102411e2\", \"key\": \"After You\", \"title\": \"\\u2728 After You [V7]\", \"artists\": \"(prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filenames: KW - After You Ref @ 83 BPM (4.21.14) &\\nAY\\n Freestyle that has new vocals compared to the previous versions. Confirmed to be \\\"AY\\\" on CyHi's Yeezus 2 playlist. Sold by Alek on leakth.is.\", \"date\": 15989184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"04af6baa66c149fe629b8d954f62ecf9\", \"url\": \"https://api.pillowcase.su/api/download/04af6baa66c149fe629b8d954f62ecf9\", \"size\": \"6.07 MB\", \"duration\": 300.77}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "6.07 MB"}, {"id": "after-you-112", "name": "<PERSON><PERSON><PERSON> T - After You [V8]", "artists": ["<PERSON>"], "producers": ["Dom $olo"], "notes": "OG Filename: After You Rough_01\nEarly version of \"Coming Home\" with <PERSON> singing the hook rather than <PERSON><PERSON>. Potentially a reference track for <PERSON><PERSON><PERSON> that was repurposed as <PERSON><PERSON><PERSON>'s song later on.", "length": "219.87", "fileDate": 15975360, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/7be90ea56ba91c9168d610774f7559c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7be90ea56ba91c9168d610774f7559c3\", \"key\": \"After You\", \"title\": \"<PERSON>usha T - After You [V8]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> $olo)\", \"aliases\": [\"Afta U\", \"Coming Home\", \"Reaper\"], \"description\": \"OG Filename: After You Rough_01\\nEarly version of \\\"Coming Home\\\" with <PERSON> singing the hook rather than <PERSON><PERSON>. Potentially a reference track for <PERSON><PERSON><PERSON> that was repurposed as <PERSON><PERSON><PERSON>'s song later on.\", \"date\": 15975360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f80e3389392c51dc82acfc5c4119b67d\", \"url\": \"https://api.pillowcase.su/api/download/f80e3389392c51dc82acfc5c4119b67d\", \"size\": \"4.78 MB\", \"duration\": 219.87}", "aliases": ["Afta U", "Coming Home", "Reaper"], "size": "4.78 MB"}, {"id": "coming-home", "name": "<PERSON><PERSON><PERSON> T - Coming Home [V9]", "artists": [], "producers": ["Dom $olo", "<PERSON><PERSON>"], "notes": "OG Filename (?): Coming Home MTS\nVersion of \"Coming Home\" with additional production by <PERSON><PERSON>. Partial snippet for an edit added the Pusha T version's vocals by stems leaked Dec 19th 2019. Stem bounce leaked alongside the full stem edit.", "length": "185.06", "fileDate": 17101152, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2b70fe97a51a55b06c935fd64e5cabf5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b70fe97a51a55b06c935fd64e5cabf5\", \"key\": \"Coming Home\", \"title\": \"Pusha T - Coming Home [V9]\", \"artists\": \"(prod. <PERSON> <PERSON>olo & <PERSON><PERSON>)\", \"aliases\": [\"Afta U\", \"After You\", \"Reaper\"], \"description\": \"OG Filename (?): Coming Home MTS\\nVersion of \\\"Coming Home\\\" with additional production by <PERSON><PERSON>. Partial snippet for an edit added the Pusha T version's vocals by stems leaked Dec 19th 2019. Stem bounce leaked alongside the full stem edit.\", \"date\": 17101152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d68c0e624ea7725444c5a84dd11162ab\", \"url\": \"https://api.pillowcase.su/api/download/d68c0e624ea7725444c5a84dd11162ab\", \"size\": \"4.22 MB\", \"duration\": 185.06}", "aliases": ["Afta U", "After You", "Reaper"], "size": "4.22 MB"}, {"id": "all-day", "name": "All Day [V2]", "artists": [], "producers": ["Velous"], "notes": "OG Freestyle. Good microphone vocals begin at 1:30. Snippets originally leaked July 23rd & October 16th, 2024. Leaked as a blind bonus for the second \"THIRSTY\" groupbuy.", "length": "296.62", "fileDate": 17341344, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d7a5173aa0232e22387ea719c725e21a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d7a5173aa0232e22387ea719c725e21a\", \"key\": \"All Day\", \"title\": \"All Day [V2]\", \"artists\": \"(prod. Velous)\", \"description\": \"OG Freestyle. Good microphone vocals begin at 1:30. Snippets originally leaked July 23rd & October 16th, 2024. Leaked as a blind bonus for the second \\\"THIRSTY\\\" groupbuy.\", \"date\": 17341344, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1ae52ba22b159a2efac396d8321b7560\", \"url\": \"https://api.pillowcase.su/api/download/1ae52ba22b159a2efac396d8321b7560\", \"size\": \"7.16 MB\", \"duration\": 296.62}", "aliases": [], "size": "7.16 MB"}, {"id": "all-day-115", "name": "All Day [V4]", "artists": [], "producers": ["Velous", "<PERSON>"], "notes": "Solo demo, with no beat change. Elements of \"However You Want It\" have been added. Was thought to be the same song as \"See How These Niggas Is\", however this is not true. Leaked in August of 2014.", "length": "134.47", "fileDate": 14068512, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/510346d28e02108479c8d90283293261", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/510346d28e02108479c8d90283293261\", \"key\": \"All Day\", \"title\": \"All Day [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Solo demo, with no beat change. Elements of \\\"However You Want It\\\" have been added. Was thought to be the same song as \\\"See How These Niggas Is\\\", however this is not true. Leaked in August of 2014.\", \"date\": 14068512, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7fa7b1d7141a0b9f68202a289795f348\", \"url\": \"https://api.pillowcase.su/api/download/7fa7b1d7141a0b9f68202a289795f348\", \"size\": \"3.42 MB\", \"duration\": 134.47}", "aliases": [], "size": "3.42 MB"}, {"id": "all-day-116", "name": "All Day [V5]", "artists": ["<PERSON>"], "producers": ["Velous", "<PERSON>"], "notes": "OG Filename: KW - All Day Ref (6.22.14)\n\"All Day\" was one of few songs played at Kanye's spontaneous Yeezus 2 listening event at Cafe Royale. Features a different beat compared to the previous version and further elements of \"However You Want It\" added, such as the <PERSON> vocals which aren't confirmed to be on the previous version. The <PERSON> outro is also extended with new vocals. A low quality recording from the event surfaced in 2014, with a CDQ snippet later surfacing on February 3rd, 2023.", "length": "294.43", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c3459e10e654866553231e7af34ae158", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3459e10e654866553231e7af34ae158\", \"key\": \"All Day\", \"title\": \"All Day [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW - All Day Ref (6.22.14)\\n\\\"All Day\\\" was one of few songs played at Kanye's spontaneous Yeezus 2 listening event at Cafe Royale. Features a different beat compared to the previous version and further elements of \\\"However You Want It\\\" added, such as the <PERSON> vocals which aren't confirmed to be on the previous version. The <PERSON> outro is also extended with new vocals. A low quality recording from the event surfaced in 2014, with a CDQ snippet later surfacing on February 3rd, 2023.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3ecde5eded0152e14e2bc34ae572bc7f\", \"url\": \"https://api.pillowcase.su/api/download/3ecde5eded0152e14e2bc34ae572bc7f\", \"size\": \"5.98 MB\", \"duration\": 294.43}", "aliases": [], "size": "5.98 MB"}, {"id": "all-day-117", "name": "All Day (Remix) [V1]", "artists": ["<PERSON>", "<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: all day PROTOTYPE\nPrototype remix. Unknown exactly when made. Has open verses", "length": "165.17", "fileDate": 16986240, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3cab387ec1d716020a7f4d5c8399de8a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3cab387ec1d716020a7f4d5c8399de8a\", \"key\": \"All Day (Remix)\", \"title\": \"All Day (Remix) [V1]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: all day PROTOTYPE\\nPrototype remix. Unknown exactly when made. Has open verses\", \"date\": 16986240, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f23dc5ee6e10c71946770fdc810a179a\", \"url\": \"https://api.pillowcase.su/api/download/f23dc5ee6e10c71946770fdc810a179a\", \"size\": \"3.9 MB\", \"duration\": 165.17}", "aliases": [], "size": "3.9 MB"}, {"id": "all-day-118", "name": "All Day (Remix) [V2]", "artists": ["<PERSON>", "<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: ALL DAY MALIKXHASS RUFF\nRemix done by <PERSON>.", "length": "229.59", "fileDate": 16986240, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/bceaf91ba09619c9e0c4906d4fb6a272", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bceaf91ba09619c9e0c4906d4fb6a272\", \"key\": \"All Day (Remix)\", \"title\": \"All Day (Remix) [V2]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: ALL DAY MALIKXHASS RUFF\\nRemix done by <PERSON>.\", \"date\": 16986240, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"13f9f95e4726d1388aae0933410855b5\", \"url\": \"https://api.pillowcase.su/api/download/13f9f95e4726d1388aae0933410855b5\", \"size\": \"4.94 MB\", \"duration\": 229.59}", "aliases": [], "size": "4.94 MB"}, {"id": "tom-cruise-119", "name": "<PERSON> [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON> (4.18.14)\nLater version, nearly double the length of all other versions, and longer than the release. Features are unconfirmed, but may include <PERSON> and <PERSON>.", "length": "266.45", "fileDate": 16952544, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/4ebc8d49ba6378f95c4eb494424c0029", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4ebc8d49ba6378f95c4eb494424c0029\", \"key\": \"Tom Cruise\", \"title\": \"<PERSON> Cruise [V3]\", \"aliases\": [\"All Your Fault\"], \"description\": \"OG Filename: KW - <PERSON>f (4.18.14)\\nLater version, nearly double the length of all other versions, and longer than the release. Features are unconfirmed, but may include <PERSON> and <PERSON>.\", \"date\": 16952544, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"140cfeb1ecf78d08ef6a81b6820bf6e2\", \"url\": \"https://api.pillowcase.su/api/download/140cfeb1ecf78d08ef6a81b6820bf6e2\", \"size\": \"5.53 MB\", \"duration\": 266.45}", "aliases": ["All Your Fault"], "size": "5.53 MB"}, {"id": "awesome", "name": "Awesome [V8]", "artists": [], "producers": ["Hit-Boy", "MIKE DEAN"], "notes": "OG Filename: KW - Awesome Ref 6 (5.28.14)\nOn a early 2014 whiteboard. More finalized mix of the Yeezus-era version of \"Awesome\", also of a higher quality.", "length": "261.05", "fileDate": 16513632, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/0f535d13b584c8859d75b8bd89115299", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f535d13b584c8859d75b8bd89115299\", \"key\": \"Awesome\", \"title\": \"Awesome [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: KW - Awesome Ref 6 (5.28.14)\\nOn a early 2014 whiteboard. More finalized mix of the Yeezus-era version of \\\"Awesome\\\", also of a higher quality.\", \"date\": 16513632, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4d241131a936658e062cd3fa85427c4f\", \"url\": \"https://api.pillowcase.su/api/download/4d241131a936658e062cd3fa85427c4f\", \"size\": \"5.44 MB\", \"duration\": 261.05}", "aliases": [], "size": "5.44 MB"}, {"id": "awesome-121", "name": "Awesome [V9]", "artists": ["KIRBY"], "producers": ["Hit-Boy", "MIKE DEAN"], "notes": "OG Filename: AWESOME Idea 1 / BGV\nVersion of \"Awesome\" with <PERSON> backing vocals. Unknown when this was from, but likely early Yeezus 2 like the rest of her references.", "length": "261.62", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/6ecfe8ee301ec517b70affa3fe15417b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ecfe8ee301ec517b70affa3fe15417b\", \"key\": \"Awesome\", \"title\": \"Awesome [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: AWESOME Idea 1 / BGV\\nVersion of \\\"Awesome\\\" with <PERSON> backing vocals. Unknown when this was from, but likely early Yeezus 2 like the rest of her references.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7286508184e13e9fcbe9134432b53aad\", \"url\": \"https://api.pillowcase.su/api/download/7286508184e13e9fcbe9134432b53aad\", \"size\": \"5.45 MB\", \"duration\": 261.62}", "aliases": [], "size": "5.45 MB"}, {"id": "bang", "name": "Bang [V1]", "artists": [], "producers": ["Foreign Tech"], "notes": "Solo version, would later be given to French Montana.", "length": "10.19", "fileDate": 17191008, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d938425e91892e8434def0cd2718515b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d938425e91892e8434def0cd2718515b\", \"key\": \"Bang\", \"title\": \"Bang [V1]\", \"artists\": \"(prod. Foreign Tech)\", \"aliases\": [\"Ass Shot\", \"Ass Shots\"], \"description\": \"Solo version, would later be given to French Montana.\", \"date\": 17191008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"23012f07e737db69829c570648ebb476\", \"url\": \"https://api.pillowcase.su/api/download/23012f07e737db69829c570648ebb476\", \"size\": \"1.43 MB\", \"duration\": 10.19}", "aliases": ["<PERSON><PERSON> Shot", "Ass Shots"], "size": "1.43 MB"}, {"id": "bang-123", "name": "French Montana - Bang [V2]", "artists": ["Kanye West"], "producers": ["Foreign Tech"], "notes": "Version with a different beat compared to the 2016 beat. Snippet posted and deleted by French <PERSON>.", "length": "15.12", "fileDate": 14048640, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/48d86513f93014d205e21a7760abe219", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48d86513f93014d205e21a7760abe219\", \"key\": \"<PERSON>\", \"title\": \"French Montana - Bang [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Foreign Tech)\", \"aliases\": [\"Ass Shot\", \"Ass Shots\"], \"description\": \"Version with a different beat compared to the 2016 beat. Snippet posted and deleted by French Montana.\", \"date\": 14048640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d4235c6cd20bae5922fbe5979d3fc191\", \"url\": \"https://api.pillowcase.su/api/download/d4235c6cd20bae5922fbe5979d3fc191\", \"size\": \"1.38 MB\", \"duration\": 15.12}", "aliases": ["<PERSON><PERSON> Shot", "Ass Shots"], "size": "1.38 MB"}, {"id": "four-letter-word", "name": "Four Letter Word [V1]", "artists": ["<PERSON>"], "producers": [], "notes": "Likely the earliest version of \"Depiction of the Devil\" with <PERSON><PERSON><PERSON> vocals. Has a different, mumblier vocal take then the one that would end up being used for the majority of the other versions. All known versions of the song leaked together after a groupbuy.", "length": "209.76", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/edc099a41f37e2722ba5dba2b4360d97", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edc099a41f37e2722ba5dba2b4360d97\", \"key\": \"Four Letter Word\", \"title\": \"Four Letter Word [V1]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"Souls\", \"Depiction of the Devil\"], \"description\": \"Likely the earliest version of \\\"Depiction of the Devil\\\" with <PERSON><PERSON><PERSON> vocals. Has a different, mumblier vocal take then the one that would end up being used for the majority of the other versions. All known versions of the song leaked together after a groupbuy.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"72feeabeeb6dc17abd10b38030ac20a1\", \"url\": \"https://api.pillowcase.su/api/download/72feeabeeb6dc17abd10b38030ac20a1\", \"size\": \"4.62 MB\", \"duration\": 209.76}", "aliases": ["Souls", "Depiction of the Devil"], "size": "4.62 MB"}, {"id": "souls", "name": "Souls [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - <PERSON> Ref (4.3.14)\nJohn Legend reference track.", "length": "293.55", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2733ba872530b3f50e809d5124732d8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2733ba872530b3f50e809d5124732d8d\", \"key\": \"Souls\", \"title\": \"Souls [V2]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Souls Ref (4.3.14)\\nJohn Legend reference track.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6e39906e1c533ac96889ae950fa00db9\", \"url\": \"https://api.pillowcase.su/api/download/6e39906e1c533ac96889ae950fa00db9\", \"size\": \"5.96 MB\", \"duration\": 293.55}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "5.96 MB"}, {"id": "depiction-of-the-devil", "name": "Depiction of the Devil [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> of the Devil JL Ref (4.5.14)\nDifferent version of the John Legend reference track, has slightly alternate production and lacks the long open verse.", "length": "208.9", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/9d56991d9d1a2f9ff64811ebca181382", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d56991d9d1a2f9ff64811ebca181382\", \"key\": \"Depiction of the Devil\", \"title\": \"Depiction of the Devil [V3]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Souls\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Depiciton of the Devil JL Ref (4.5.14)\\nDifferent version of the John Legend reference track, has slightly alternate production and lacks the long open verse.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f358710402d919b538a321f5966d2f5b\", \"url\": \"https://api.pillowcase.su/api/download/f358710402d919b538a321f5966d2f5b\", \"size\": \"4.61 MB\", \"duration\": 208.9}", "aliases": ["Souls", "Four Letter Word"], "size": "4.61 MB"}, {"id": "depiction-of-the-devil-127", "name": "Depiction of the Devil [V4]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> of the Devil Ye <PERSON> (4.5.14)\nInitial Kanye freestyle, would end up being used for the majority of other versions.", "length": "208.9", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d5d50bdecbc4ed439367290f16683358", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d5d50bdecbc4ed439367290f16683358\", \"key\": \"Depiction of the Devil\", \"title\": \"Depiction of the Devil [V4]\", \"aliases\": [\"Souls\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Depiciton of the Devil Ye Ref (4.5.14)\\nInitial Kanye freestyle, would end up being used for the majority of other versions.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ae316aa557b48e3369b559800a42376a\", \"url\": \"https://api.pillowcase.su/api/download/ae316aa557b48e3369b559800a42376a\", \"size\": \"4.61 MB\", \"duration\": 208.9}", "aliases": ["Souls", "Four Letter Word"], "size": "4.61 MB"}, {"id": "depiction-of-the-devil-128", "name": "Depiction of the Devil [V5]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON>W - Depiction of The Devil NG Ref (4.9.14)\nHas alternate production done by <PERSON>. <PERSON><PERSON> vocals.", "length": "281.7", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/455509a81725f8332f49f67953e9f1a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/455509a81725f8332f49f67953e9f1a9\", \"key\": \"Depiction of the Devil\", \"title\": \"Depiction of the Devil [V5]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Souls\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Depiction of The Devil NG Ref (4.9.14)\\nHas alternate production done by <PERSON>. <PERSON>ks vocals.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f8e75b2481946f2f023c0124a4d3fc31\", \"url\": \"https://api.pillowcase.su/api/download/f8e75b2481946f2f023c0124a4d3fc31\", \"size\": \"5.77 MB\", \"duration\": 281.7}", "aliases": ["Souls", "Four Letter Word"], "size": "5.77 MB"}, {"id": "souls-129", "name": "Souls [V6]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - Souls (4.13.14)\nLater version of the Noah Goldstein alt production.", "length": "252.78", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/9184d971372c59e7119165bfebfd5912", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9184d971372c59e7119165bfebfd5912\", \"key\": \"Souls\", \"title\": \"Souls [V6]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Souls (4.13.14)\\nLater version of the Noah Goldstein alt production.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3809dd18fda8ced716b6a5ef0cd95e95\", \"url\": \"https://api.pillowcase.su/api/download/3809dd18fda8ced716b6a5ef0cd95e95\", \"size\": \"5.31 MB\", \"duration\": 252.78}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "5.31 MB"}, {"id": "depiction-of-the-devil-130", "name": "Depiction of the Devil [V7]", "artists": [], "producers": [], "notes": "OG Filename: KW - Depiction of the Devil Ref (4.15.14)\n<PERSON> reference track.", "length": "120.76", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/edd92d6baf5a42e10cb28b3758ddeddd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edd92d6baf5a42e10cb28b3758ddeddd\", \"key\": \"Depiction of the Devil\", \"title\": \"Depiction of the Devil [V7]\", \"artists\": \"(ref. <PERSON>IRB<PERSON>)\", \"aliases\": [\"Souls\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Depiction of the Devil Ref (4.15.14)\\nKirby Lauryen reference track.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4343710e0f352668c835564737862043\", \"url\": \"https://api.pillowcase.su/api/download/4343710e0f352668c835564737862043\", \"size\": \"3.2 MB\", \"duration\": 120.76}", "aliases": ["Souls", "Four Letter Word"], "size": "3.2 MB"}, {"id": "depiction-of-the-devil-131", "name": "Depiction of the Devil [V8]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>W - Depiction of the <PERSON> Ref (4.18.14)\nSeems to be the same as the initial Kanye freestyle, but with a better mix and the open instrumental cut out.", "length": "115.85", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/7a8a76af3f498dd2ab73d2fdf634998a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a8a76af3f498dd2ab73d2fdf634998a\", \"key\": \"Depiction of the Devil\", \"title\": \"Depiction of the Devil [V8]\", \"aliases\": [\"Souls\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Depiction of the Devil Ref (4.18.14)\\nSeems to be the same as the initial Kanye freestyle, but with a better mix and the open instrumental cut out.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3b2d9836fb67ee3d4e74f9ba7d078ac7\", \"url\": \"https://api.pillowcase.su/api/download/3b2d9836fb67ee3d4e74f9ba7d078ac7\", \"size\": \"3.12 MB\", \"duration\": 115.85}", "aliases": ["Souls", "Four Letter Word"], "size": "3.12 MB"}, {"id": "souls-132", "name": "Souls [V9]", "artists": [], "producers": [], "notes": "OG Filename: Souls 91.5 bpm\nHas alternate production, sampling \"One of These Nights\" by The Eagles.", "length": "317.14", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/dd3bdb473744f96a46fb10bf518dbc37", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd3bdb473744f96a46fb10bf518dbc37\", \"key\": \"Souls\", \"title\": \"Souls [V9]\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: Souls 91.5 bpm\\nHas alternate production, sampling \\\"One of These Nights\\\" by The Eagles.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0d345345e92a087b6e9d03ffa465c594\", \"url\": \"https://api.pillowcase.su/api/download/0d345345e92a087b6e9d03ffa465c594\", \"size\": \"6.34 MB\", \"duration\": 317.14}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "6.34 MB"}, {"id": "souls-133", "name": "Souls [V10]", "artists": [], "producers": [], "notes": "OG Filename: KW - <PERSON> Ref (4.20.14)\nSame as the above version, but with the long open verse at the end cut out.", "length": "146.92", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d03808dce4c46643b2c3b53cdaf00e5f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d03808dce4c46643b2c3b53cdaf00e5f\", \"key\": \"Souls\", \"title\": \"Souls [V10]\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Souls Ref (4.20.14)\\nSame as the above version, but with the long open verse at the end cut out.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4cc00a9cd1078a7ad3f1d928c33a071d\", \"url\": \"https://api.pillowcase.su/api/download/4cc00a9cd1078a7ad3f1d928c33a071d\", \"size\": \"3.62 MB\", \"duration\": 146.92}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "3.62 MB"}, {"id": "souls-134", "name": "Souls [V11]", "artists": [], "producers": ["Sak Pase"], "notes": "OG Filename: KW - Souls Ref (4.22.14)\nHas added production from Sak Pase.", "length": "212.43", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/dd37495addd1c29bfb0927da41175384", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd37495addd1c29bfb0927da41175384\", \"key\": \"Souls\", \"title\": \"Souls [V11]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Souls Ref (4.22.14)\\nHas added production from Sak Pase.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"20bab950a4d94483eecc7e20a0ac5508\", \"url\": \"https://api.pillowcase.su/api/download/20bab950a4d94483eecc7e20a0ac5508\", \"size\": \"4.66 MB\", \"duration\": 212.43}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "4.66 MB"}, {"id": "souls-135", "name": "Souls [V12]", "artists": [], "producers": [], "notes": "OG Filename: KW - Souls Ref (4.25.14)\nVersion that is very similar to V10, but is slightly shorter.", "length": "125.93", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/afc9635f501d28ba2e42f61c83056f9d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afc9635f501d28ba2e42f61c83056f9d\", \"key\": \"Souls\", \"title\": \"Souls [V12]\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Souls Ref (4.25.14)\\nVersion that is very similar to V10, but is slightly shorter.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"19327bbab4746d80d5557dbd6b05dead\", \"url\": \"https://api.pillowcase.su/api/download/19327bbab4746d80d5557dbd6b05dead\", \"size\": \"3.28 MB\", \"duration\": 125.93}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "3.28 MB"}, {"id": "souls-136", "name": "Souls [V13]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> Voice Memo (4.26.14)\nVoice memo freestyle.", "length": "125.85", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/48f78c66a263d8eddeac363a52ea8cff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48f78c66a263d8eddeac363a52ea8cff\", \"key\": \"Souls\", \"title\": \"Souls [V13]\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"OG Filename: KW - Souls Voice Memo (4.26.14)\\nVoice memo freestyle.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1a5e5d5ca39769053ba8d7c330e04980\", \"url\": \"https://api.pillowcase.su/api/download/1a5e5d5ca39769053ba8d7c330e04980\", \"size\": \"3.28 MB\", \"duration\": 125.85}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "3.28 MB"}, {"id": "souls-137", "name": "Souls [V14]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "Has alternate production done by <PERSON>. <PERSON> harmonizing in the outro.", "length": "200.88", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/9828381f5e7a2853fdb0dee2f16d7aa6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9828381f5e7a2853fdb0dee2f16d7aa6\", \"key\": \"Souls\", \"title\": \"Souls [V14]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Depiction of the Devil\", \"Four Letter Word\"], \"description\": \"Has alternate production done by <PERSON>. Has <PERSON> harmonizing in the outro.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d490e444cd23591b6e53ae828d143a4b\", \"url\": \"https://api.pillowcase.su/api/download/d490e444cd23591b6e53ae828d143a4b\", \"size\": \"4.48 MB\", \"duration\": 200.88}", "aliases": ["Depiction of the Devil", "Four Letter Word"], "size": "4.48 MB"}, {"id": "depiction-of-the-devil-138", "name": "Depiction of the Devil [V15]", "artists": [], "producers": [], "notes": "Version sent to Hudson Mohawke because <PERSON><PERSON><PERSON> wanted him to put drums on it. Has <PERSON><PERSON> beatboxing over the instrumental to indicate how the drums should be.", "length": "21.05", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d9e60c0f235360e352eb7330fe562989", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9e60c0f235360e352eb7330fe562989\", \"key\": \"Depiction of the Devil\", \"title\": \"Depiction of the Devil [V15]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Souls\", \"Four Letter Word\"], \"description\": \"Version sent to Hudson Mohawke because <PERSON><PERSON><PERSON> wanted him to put drums on it. Has <PERSON><PERSON><PERSON> beatboxing over the instrumental to indicate how the drums should be.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fab72bc2b56364887f9cb8b86047a93a\", \"url\": \"https://api.pillowcase.su/api/download/fab72bc2b56364887f9cb8b86047a93a\", \"size\": \"1.6 MB\", \"duration\": 21.05}", "aliases": ["Souls", "Four Letter Word"], "size": "1.6 MB"}, {"id": "333", "name": "333 [V1]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: 333\nSong included in <PERSON><PERSON><PERSON>'s Yeezus 2 playlist. Earliest known version of \"FourFiveSeconds\".", "length": "131.81", "fileDate": 17216064, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/75ca1044904cdbfb6670c814621fb916", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/75ca1044904cdbfb6670c814621fb916\", \"key\": \"333\", \"title\": \"333 [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3\", \"FourFiveSeconds\", \"Don't Wanna Fight\"], \"description\": \"OG Filename: 333\\nSong included in CyHi's Yeezus 2 playlist. Earliest known version of \\\"FourFiveSeconds\\\".\", \"date\": 17216064, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b5133375f048bf00b11fc098f44d95fe\", \"url\": \"https://api.pillowcase.su/api/download/b5133375f048bf00b11fc098f44d95fe\", \"size\": \"3.37 MB\", \"duration\": 131.81}", "aliases": ["3", "FourFiveSeconds", "Don't Wanna Fight"], "size": "3.37 MB"}, {"id": "3", "name": "3 [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: KW - 3 Ref\nVery early version of \"FourFiveSeconds\", entitled \"3\". Almost entirely mumble, with some <PERSON> vocals in the background.", "length": "102.18", "fileDate": 16728768, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/bb6460020050d116caeb1849f84749c8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb6460020050d116caeb1849f84749c8\", \"key\": \"3\", \"title\": \"3 [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"FourFiveSeconds\", \"Don't Wanna Fight\"], \"description\": \"OG Filename: KW - 3 Ref\\nVery early version of \\\"FourFiveSeconds\\\", entitled \\\"3\\\". Almost entirely mumble, with some <PERSON> vocals in the background.\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1f6b22c7aa7ce35f94be4508185068df\", \"url\": \"https://api.pillowcase.su/api/download/1f6b22c7aa7ce35f94be4508185068df\", \"size\": \"2.9 MB\", \"duration\": 102.18}", "aliases": ["FourFiveSeconds", "Don't Wanna Fight"], "size": "2.9 MB"}, {"id": "3-141", "name": "3 [V4]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: KW - 3 Ref (5.11.14)\nHas an updated beat compared to V1 and is nearly twice as long. Contains drums made out of chopped up firework noises.", "length": "188.13", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/698351c0a872cfd68f0e5dc56e9a6bb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/698351c0a872cfd68f0e5dc56e9a6bb0\", \"key\": \"3\", \"title\": \"3 [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"FourFiveSeconds\", \"Don't Wanna Fight\"], \"description\": \"OG Filename: KW - 3 Ref (5.11.14)\\nHas an updated beat compared to V1 and is nearly twice as long. Contains drums made out of chopped up firework noises.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6117b71a5d81b21f5b26fce1229d9b1c\", \"url\": \"https://api.pillowcase.su/api/download/6117b71a5d81b21f5b26fce1229d9b1c\", \"size\": \"4.28 MB\", \"duration\": 188.13}", "aliases": ["FourFiveSeconds", "Don't Wanna Fight"], "size": "4.28 MB"}, {"id": "god-level-142", "name": "God Level [V6]", "artists": [], "producers": ["88-<PERSON>", "<PERSON>"], "notes": "OG Filename: God Level NG Bridge Ref\nVersion of \"God Level\" with production from <PERSON>. Most likely made around March or April alongside the other versions of \"God Level\" incorporating \"Don't Get Up\".", "length": "133.92", "fileDate": 16901568, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/1baa62c10920d8ae75a7037f5d5c6ae8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1baa62c10920d8ae75a7037f5d5c6ae8\", \"key\": \"God Level\", \"title\": \"God Level [V6]\", \"artists\": \"(prod. 88-<PERSON> & <PERSON>)\", \"description\": \"OG Filename: God Level NG Bridge Ref\\nVersion of \\\"God Level\\\" with production from <PERSON>. Most likely made around March or April alongside the other versions of \\\"God Level\\\" incorporating \\\"Don't Get Up\\\".\", \"date\": 16901568, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f721580eb66c6484b1c595d2990c2dd5\", \"url\": \"https://api.pillowcase.su/api/download/f721580eb66c6484b1c595d2990c2dd5\", \"size\": \"3.41 MB\", \"duration\": 133.92}", "aliases": [], "size": "3.41 MB"}, {"id": "god-level-143", "name": "God Level [V7]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level Bridge Ref 1 (3.25.14)\nVersion of \"God Level\", made from portions of \"Don't Get Up\". Features more mumble vocals. Original snippet leaked March 26th, 2023.", "length": "68.49", "fileDate": 16801344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/6ed7b12dc4cc1113d08fb32b223005f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ed7b12dc4cc1113d08fb32b223005f1\", \"key\": \"God Level\", \"title\": \"God Level [V7]\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level Bridge Ref 1 (3.25.14)\\nVersion of \\\"God Level\\\", made from portions of \\\"Don't Get Up\\\". Features more mumble vocals. Original snippet leaked March 26th, 2023.\", \"date\": 16801344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fd46ce9c74600a3dbcd2afabdfe03f94\", \"url\": \"https://api.pillowcase.su/api/download/fd46ce9c74600a3dbcd2afabdfe03f94\", \"size\": \"2.36 MB\", \"duration\": 68.49}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "2.36 MB"}, {"id": "god-level-144", "name": "God Level [V8]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level Bridge Ref 2 (3.25.14)\nVersion of \"God Level\" with a different production take on the bridge and no vocals. Otherwise identical to the previous version.", "length": "96.88", "fileDate": 16924896, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/08d6310bf080085b15f199f8732e02e5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08d6310bf080085b15f199f8732e02e5\", \"key\": \"God Level\", \"title\": \"God Level [V8]\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level Bridge Ref 2 (3.25.14)\\nVersion of \\\"God Level\\\" with a different production take on the bridge and no vocals. Otherwise identical to the previous version.\", \"date\": 16924896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"39211c216dcabfc7d8e2f49eefc3d5df\", \"url\": \"https://api.pillowcase.su/api/download/39211c216dcabfc7d8e2f49eefc3d5df\", \"size\": \"2.81 MB\", \"duration\": 96.88}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "2.81 MB"}, {"id": "god-level-145", "name": "God Level [V9]", "artists": [], "producers": [], "notes": "OG Filename: KW - GL Bridge Ref (3.28.14)\nVersion of \"God Level\" with a different production take on the bridge.", "length": "133.92", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/1cd2e6e850be3b2d608860b38e92ba66", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1cd2e6e850be3b2d608860b38e92ba66\", \"key\": \"God Level\", \"title\": \"God Level [V9]\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - GL Bridge Ref (3.28.14)\\nVersion of \\\"God Level\\\" with a different production take on the bridge.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3b99c15825a7d57c78559da7eaff26e6\", \"url\": \"https://api.pillowcase.su/api/download/3b99c15825a7d57c78559da7eaff26e6\", \"size\": \"3.41 MB\", \"duration\": 133.92}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3.41 MB"}, {"id": "god-level-146", "name": "God Level [V10]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level Ref (4.2.14)\nVersion of \"God Level\" that features updated production with a bridge that resembles later versions. Leaked as a bonus for the \"God Got Me\" groupbuy.", "length": "156.7", "fileDate": 16936992, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/4bd49fec75b0f6cb919ec54691597696", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bd49fec75b0f6cb919ec54691597696\", \"key\": \"God Level\", \"title\": \"God Level [V10]\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level Ref (4.2.14)\\nVersion of \\\"God Level\\\" that features updated production with a bridge that resembles later versions. Leaked as a bonus for the \\\"God Got Me\\\" groupbuy.\", \"date\": 16936992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cf9b4588b946109945954f65002bce1b\", \"url\": \"https://api.pillowcase.su/api/download/cf9b4588b946109945954f65002bce1b\", \"size\": \"3.77 MB\", \"duration\": 156.7}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3.77 MB"}, {"id": "god-level-147", "name": "✨ God Level [V12]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level CyHi Ref 2 (4.10.14)\nCyHi reference track. Has <PERSON><PERSON><PERSON> doing reference for the verses, bridge, and refrain, and also has more production compared to previous versions.", "length": "154.02", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3a14ac5a48dd2d01fb8282495e0a8553", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a14ac5a48dd2d01fb8282495e0a8553\", \"key\": \"God Level\", \"title\": \"\\u2728 God Level [V12]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level CyHi Ref 2 (4.10.14)\\nCyHi reference track. Has <PERSON><PERSON><PERSON> doing reference for the verses, bridge, and refrain, and also has more production compared to previous versions.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"21de04f268c05337bc3b39d50f6a3a13\", \"url\": \"https://api.pillowcase.su/api/download/21de04f268c05337bc3b39d50f6a3a13\", \"size\": \"3.73 MB\", \"duration\": 154.02}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3.73 MB"}, {"id": "god-level-148", "name": "God Level [V13]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level CyHi Ref 3 (4.10.14)\nThird CyHi \"God Level\" reference track. <PERSON><PERSON><PERSON> does his own take of \"God Level\", departing from <PERSON><PERSON><PERSON>'s original mumble ref idea. Cuts early at the bridge.", "length": "77.06", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c4a7451565d73dad701e2279ae0da77c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4a7451565d73dad701e2279ae0da77c\", \"key\": \"God Level\", \"title\": \"God Level [V13]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level CyHi Ref 3 (4.10.14)\\nThird CyHi \\\"God Level\\\" reference track. <PERSON><PERSON><PERSON> does his own take of \\\"God Level\\\", departing from <PERSON><PERSON><PERSON>'s original mumble ref idea. Cuts early at the bridge.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dc7da5e15ac204a99c6d98a36d1f9568\", \"url\": \"https://api.pillowcase.su/api/download/dc7da5e15ac204a99c6d98a36d1f9568\", \"size\": \"2.5 MB\", \"duration\": 77.06}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "2.5 MB"}, {"id": "god-level-149", "name": "God Level [V14]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Version of <PERSON><PERSON><PERSON> \"God Level\" with <PERSON><PERSON><PERSON> doing the bridge and a new CyHi ref. Snippet leaked March 16th, 2024.", "length": "9.59", "fileDate": 17105472, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/adb4194b5dd714eb7e10d52de4ec4810", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/adb4194b5dd714eb7e10d52de4ec4810\", \"key\": \"God Level\", \"title\": \"God Level [V14]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"Version of CyHi \\\"God Level\\\" with <PERSON><PERSON><PERSON> doing the bridge and a new CyHi ref. Snippet leaked March 16th, 2024.\", \"date\": 17105472, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"392994044e677aab1ebc114a49ec670d\", \"url\": \"https://api.pillowcase.su/api/download/392994044e677aab1ebc114a49ec670d\", \"size\": \"1.42 MB\", \"duration\": 9.59}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "1.42 MB"}, {"id": "god-level-150", "name": "God Level [V15]", "artists": [], "producers": [], "notes": "OG Filename: KW - God Level Ref (4.24.14)\nEarly version of \"God Level\" with verses from <PERSON><PERSON><PERSON>. Has less production and more mumble. Samples \"Kolumbo\" by <PERSON>. Snippet leaked June 7th, 2022.", "length": "11.66", "fileDate": 16545600, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b57bc63780d08cfc149f843fd54c78a7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b57bc63780d08cfc149f843fd54c78a7\", \"key\": \"God Level\", \"title\": \"God Level [V15]\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - God Level Ref (4.24.14)\\nEarly version of \\\"God Level\\\" with verses from Kanye. Has less production and more mumble. Sam<PERSON> \\\"Kolumbo\\\" by <PERSON>. Snippet leaked June 7th, 2022.\", \"date\": 16545600, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"23a0490e299a823dd322c46c223e0e93\", \"url\": \"https://api.pillowcase.su/api/download/23a0490e299a823dd322c46c223e0e93\", \"size\": \"1.45 MB\", \"duration\": 11.66}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "1.45 MB"}, {"id": "god-level-151", "name": "God Level [V16]", "artists": [], "producers": ["88-<PERSON>", "Hudson Mohawke", "MIKE DEAN", "<PERSON>"], "notes": "Version of \"God Level\" closely resembling the one used in the Adidas advertisement, containing a lot of open space and less production. Posted on the Adidas Soundcloud page on May 24th, 2014 and removed before or on May 27th, 2014.", "length": "156.84", "fileDate": 14008896, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3437ea63a6b3b1f1a68742eeb61f7054", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3437ea63a6b3b1f1a68742eeb61f7054\", \"key\": \"God Level\", \"title\": \"God Level [V16]\", \"artists\": \"(prod. 88-<PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"Version of \\\"God Level\\\" closely resembling the one used in the Adidas advertisement, containing a lot of open space and less production. Posted on the Adidas Soundcloud page on May 24th, 2014 and removed before or on May 27th, 2014.\", \"date\": 14008896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"aa7e17e578a503689cca1892b2a11999\", \"url\": \"https://api.pillowcase.su/api/download/aa7e17e578a503689cca1892b2a11999\", \"size\": \"3.77 MB\", \"duration\": 156.84}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "3.77 MB"}, {"id": "god-level-152", "name": "God Level [V17]", "artists": [], "producers": ["88-<PERSON>", "Hudson Mohawke", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: BRIDGE GL KW (5.15.14)\n<PERSON> reference for the \"God Level\" bridge. Original snippet leaked June 7th, 2022.", "length": "182.52", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/5ba77813bd7f3638a1312d76368c1eeb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ba77813bd7f3638a1312d76368c1eeb\", \"key\": \"God Level\", \"title\": \"God Level [V17]\", \"artists\": \"(ref. <PERSON>IRB<PERSON>) (prod. <PERSON>-<PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: BRIDGE GL KW (5.15.14)\\nKirby Lauryen reference for the \\\"God Level\\\" bridge. Original snippet leaked June 7th, 2022.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3e038ce6babb86e7487eac5d56de1e5c\", \"url\": \"https://api.pillowcase.su/api/download/3e038ce6babb86e7487eac5d56de1e5c\", \"size\": \"4.18 MB\", \"duration\": 182.52}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "4.18 MB"}, {"id": "god-level-153", "name": "God Level [V18]", "artists": [], "producers": ["88-<PERSON>", "Hudson Mohawke", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: KW - Tony <PERSON> Bridge Idea\nVersion of \"God Level\" with <PERSON> doing a reference for the bridge. Some of the Kirby Lau<PERSON>en reference track can be heard. Dated one day before the version used in the Adidas commercial. Leaked after a groupbuy.", "length": "71.72", "fileDate": 16238016, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c54acfcdbe503c8f6c1e92b23c61f5aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c54acfcdbe503c8f6c1e92b23c61f5aa\", \"key\": \"God Level\", \"title\": \"God Level [V18]\", \"artists\": \"(ref. The WRLDFMS <PERSON> & <PERSON>IRBY) (prod. 88-<PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - <PERSON> Bridge Idea\\nVersion of \\\"God Level\\\" with <PERSON> doing a reference for the bridge. Some of the Kirby Lauryen reference track can be heard. Dated one day before the version used in the Adidas commercial. Leaked after a groupbuy.\", \"date\": 16238016, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"61a8592f939300ebafe4a3ddcbc90db6\", \"url\": \"https://api.pillowcase.su/api/download/61a8592f939300ebafe4a3ddcbc90db6\", \"size\": \"2.41 MB\", \"duration\": 71.72}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "2.41 MB"}, {"id": "god-level-154", "name": "⭐ God Level [V19]", "artists": [], "producers": ["88-<PERSON>", "Hudson Mohawke", "MIKE DEAN", "<PERSON>"], "notes": "OG Filename: KW - <PERSON> Level Ref (5.22.14)\nVersion of the song which features three different beats. This song also shares a sample with \"Pressure\". An edited version of the track was officially used in an Adidas commercial in 2014. Samples \"Stop, Look, Listen To Your Heart\" by <PERSON> & <PERSON>.", "length": "243.72", "fileDate": 15634944, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/c9b5b94d712f53df40db33cfdb819ce4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9b5b94d712f53df40db33cfdb819ce4\", \"key\": \"God Level\", \"title\": \"\\u2b50 God Level [V19]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Do You Really Ever Love\", \"Don't Get Up\"], \"description\": \"OG Filename: KW - <PERSON> Ref (5.22.14)\\nVersion of the song which features three different beats. This song also shares a sample with \\\"Pressure\\\". An edited version of the track was officially used in an Adidas commercial in 2014. <PERSON><PERSON> \\\"Stop, Look, Listen To Your Heart\\\" by <PERSON> & <PERSON>.\", \"date\": 15634944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2f74b67992784c70b78e249470a6cfb3\", \"url\": \"https://api.pillowcase.su/api/download/2f74b67992784c70b78e249470a6cfb3\", \"size\": \"5.17 MB\", \"duration\": 243.72}", "aliases": ["Do You Really Ever Love", "Don't Get Up"], "size": "5.17 MB"}, {"id": "idea-2", "name": "Idea 2 [V1]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: PM <PERSON><PERSON> 2 3.4.14\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together. <PERSON>'s whistling would later be reused for \"However You Want It,\" and finally \"All Day\".", "length": "556.14", "fileDate": 17006976, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/cd765526c9e8d7b1fe91ada0099a95ea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cd765526c9e8d7b1fe91ada0099a95ea\", \"key\": \"Idea 2\", \"title\": \"Idea 2 [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"However You Want It\", \"All Day\"], \"description\": \"OG Filename: PM KW Idea 2 3.4.14\\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together. <PERSON>'s whistling would later be reused for \\\"However You Want It,\\\" and finally \\\"All Day\\\".\", \"date\": 17006976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"05a8dce58b5914b428f4459d98b2128f\", \"url\": \"https://api.pillowcase.su/api/download/05a8dce58b5914b428f4459d98b2128f\", \"size\": \"10.2 MB\", \"duration\": 556.14}", "aliases": ["However You Want It", "All Day"], "size": "10.2 MB"}, {"id": "however-you-want-it", "name": "However You Want It [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: However You Want It ref (3.7.14)\nEarlier version of \"However You Want It,\" chopped up for the leaked version. Partially available as it was used for later versions, but the full thing is not available.", "length": "293.11", "fileDate": 17013888, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/453cd758d321d32f5f36570a45fd727a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/453cd758d321d32f5f36570a45fd727a\", \"key\": \"However You Want It\", \"title\": \"However You Want It [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"All Day\"], \"description\": \"OG Filename: However You Want It ref (3.7.14)\\nEarlier version of \\\"However You Want It,\\\" chopped up for the leaked version. Partially available as it was used for later versions, but the full thing is not available.\", \"date\": 17013888, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c7cfae8d5077af9f784d96976e1d10ad\", \"url\": \"https://api.pillowcase.su/api/download/c7cfae8d5077af9f784d96976e1d10ad\", \"size\": \"5.95 MB\", \"duration\": 293.11}", "aliases": ["All Day"], "size": "5.95 MB"}, {"id": "how-ever-you-want-it", "name": "How Ever You Want It [V3]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: How Ever You Want It Rough Mix PT 17\nRougher mix of \"However You Want It.\"", "length": "293.11", "fileDate": 17013888, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3d43c0c289caf12bacaac0ac7ac1bc2e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3d43c0c289caf12bacaac0ac7ac1bc2e\", \"key\": \"How Ever You Want It\", \"title\": \"How Ever You Want It [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"All Day\"], \"description\": \"OG Filename: How Ever You Want It Rough Mix PT 17\\nRougher mix of \\\"However You Want It.\\\"\", \"date\": 17013888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"917cead5f47c9667c3c6556db84c7421\", \"url\": \"https://api.pillowcase.su/api/download/917cead5f47c9667c3c6556db84c7421\", \"size\": \"5.95 MB\", \"duration\": 293.11}", "aliases": ["All Day"], "size": "5.95 MB"}, {"id": "however-you-want-it-158", "name": "However You Want It [V4]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: PAUL & KANYE ROUGH MIX HH 17\nSong combined with \"All Day.\" Contains the <PERSON> vocals and <PERSON><PERSON><PERSON> vocal sample that appear on later \"All Day\" versions. <PERSON> somewhat finished autotuned <PERSON><PERSON><PERSON> vocals, and an early version of the beat - the <PERSON> whistle melody, as well as the electric guitar riff. Part of this version is layered under <PERSON> on the version played at the Cafe Royale event.", "length": "294.66", "fileDate": 17013888, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/298113d7bc3887178aeef11b9f67b924", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/298113d7bc3887178aeef11b9f67b924\", \"key\": \"However You Want It\", \"title\": \"However You Want It [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"All Day\"], \"description\": \"OG Filename: PAUL & KANYE ROUGH MIX HH 17\\nSong combined with \\\"All Day.\\\" Contains the <PERSON> vocals and <PERSON><PERSON><PERSON> vocal sample that appear on later \\\"All Day\\\" versions. <PERSON> somewhat finished autotuned <PERSON><PERSON><PERSON> vocals, and an early version of the beat - the <PERSON> whistle melody, as well as the electric guitar riff. Part of this version is layered under <PERSON> on the version played at the Cafe Royale event.\", \"date\": 17013888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"95bb092b9f85564c5e3e830a5967d217\", \"url\": \"https://api.pillowcase.su/api/download/95bb092b9f85564c5e3e830a5967d217\", \"size\": \"5.98 MB\", \"duration\": 294.66}", "aliases": ["All Day"], "size": "5.98 MB"}, {"id": "i-can-t-let-go", "name": "I Can't Let Go", "artists": [], "producers": [], "notes": "OG Filename: KW - I Can't Let Go Ref (4.24.14)\nYeezus 2 era freestyle. Samples \"Can't Run This Race Alone\" by Flying Eagles Gospel Singers.", "length": "180.59", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/fc6e1dccdb4e79f62bab81a12dcafa6e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fc6e1dccdb4e79f62bab81a12dcafa6e\", \"key\": \"I Can't Let Go\", \"title\": \"I Can't Let Go\", \"description\": \"OG Filename: KW - I Can't Let Go Ref (4.24.14)\\nYeezus 2 era freestyle. Samples \\\"Can't Run This Race Alone\\\" by Flying Eagles Gospel Singers.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"54e4c2c26d3bd1b2d781287ef6538578\", \"url\": \"https://api.pillowcase.su/api/download/54e4c2c26d3bd1b2d781287ef6538578\", \"size\": \"4.15 MB\", \"duration\": 180.59}", "aliases": [], "size": "4.15 MB"}, {"id": "idea-1", "name": "Idea 1", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: PM <PERSON><PERSON> 1 3.4.14\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together.", "length": "285.14", "fileDate": 17018208, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b2884528d6c087a841ecc4a1c7885581", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b2884528d6c087a841ecc4a1c7885581\", \"key\": \"Idea 1\", \"title\": \"Idea 1\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: PM KW Idea 1 3.4.14\\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together.\", \"date\": 17018208, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"649940b3bd6a85c3437c9d6061388998\", \"url\": \"https://api.pillowcase.su/api/download/649940b3bd6a85c3437c9d6061388998\", \"size\": \"5.83 MB\", \"duration\": 285.14}", "aliases": [], "size": "5.83 MB"}, {"id": "idea-3", "name": "Idea 3", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> 3 3.4.14\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together.", "length": "368.79", "fileDate": 16976736, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/beb955f3285211be6377cc4ddb69d07c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/beb955f3285211be6377cc4ddb69d07c\", \"key\": \"Idea 3\", \"title\": \"Idea 3\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON> 3 3.4.14\\nFreestyle created when <PERSON> and <PERSON><PERSON> first met up and started working together.\", \"date\": 16976736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"54e24aab962c0a48dac98f042dd8cfb3\", \"url\": \"https://api.pillowcase.su/api/download/54e24aab962c0a48dac98f042dd8cfb3\", \"size\": \"7.17 MB\", \"duration\": 368.79}", "aliases": [], "size": "7.17 MB"}, {"id": "idea-4", "name": "Idea 4", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: KW x PM - Idea 4 3.3.14\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together.", "length": "753.89", "fileDate": 16981056, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/576a1f27142e1d1cd72a4159ac18aff7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/576a1f27142e1d1cd72a4159ac18aff7\", \"key\": \"Idea 4\", \"title\": \"Idea 4\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: KW x PM - Idea 4 3.3.14\\nFreestyle created when <PERSON> and <PERSON><PERSON><PERSON> first met up and started working together.\", \"date\": 16981056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"03be01893d23245e921c8eccd5323de4\", \"url\": \"https://api.pillowcase.su/api/download/03be01893d23245e921c8eccd5323de4\", \"size\": \"13.3 MB\", \"duration\": 753.89}", "aliases": [], "size": "13.3 MB"}, {"id": "i-feel-like-that", "name": "I Feel Like That [V1]", "artists": [], "producers": ["MIKE DEAN"], "notes": "\"I Feel Like That\" was created in the <PERSON> sessions, sampling a talkbox present in an alternate version of \"Only One\". 2 MIKE DEAN stems for the song (sample chop & 808) are dated April 23rd, 2014, so the song likely originated around or on this date.", "length": "174.55", "fileDate": 16816896, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2ecee3075e8cef9b0ef46a831adb3cbd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2ecee3075e8cef9b0ef46a831adb3cbd\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V1]\", \"artists\": \"(prod. <PERSON><PERSON> DEAN)\", \"description\": \"\\\"I Feel Like That\\\" was created in the <PERSON> sessions, sampling a talkbox present in an alternate version of \\\"Only One\\\". 2 MIKE DEAN stems for the song (sample chop & 808) are dated April 23rd, 2014, so the song likely originated around or on this date.\", \"date\": 16816896, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5de1bccfd49209947768fc87dd7432a9\", \"url\": \"https://api.pillowcase.su/api/download/5de1bccfd49209947768fc87dd7432a9\", \"size\": \"4.06 MB\", \"duration\": 174.55}", "aliases": [], "size": "4.06 MB"}, {"id": "i-feel-like-that-164", "name": "✨ I Feel Like That [V2]", "artists": ["<PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: KW - I Feel Like That Ref (4.26.14)\nVersion of the song with simpler production and a Frank <PERSON> feature at the end of the song. Was reportably owned by MusicMafia at one point.", "length": "319.91", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3662dfbeecbc2a444c50c7648d12abf0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3662dfbeecbc2a444c50c7648d12abf0\", \"key\": \"I Feel Like That\", \"title\": \"\\u2728 I Feel Like That [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: KW - I Feel Like That Ref (4.26.14)\\nVersion of the song with simpler production and a Frank Ocean feature at the end of the song. Was reportably owned by MusicMafia at one point.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"092f9eaadbd5907186ebb9469375787f\", \"url\": \"https://api.pillowcase.su/api/download/092f9eaadbd5907186ebb9469375787f\", \"size\": \"6.38 MB\", \"duration\": 319.91}", "aliases": [], "size": "6.38 MB"}, {"id": "i-feel-like-that-165", "name": "I Feel Like That [V3]", "artists": [], "producers": ["Hudson Mohawke", "MIKE DEAN"], "notes": "Partial stem bounce of an early verison of \"I Feel LIke That\".", "length": "291.83", "fileDate": 16816896, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/d81e687d97505e1d99b5adbfba35035e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d81e687d97505e1d99b5adbfba35035e\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V3]\", \"artists\": \"(prod. <PERSON> & MIKE DEAN)\", \"description\": \"Partial stem bounce of an early verison of \\\"I Feel LIke That\\\".\", \"date\": 16816896, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1cff0bda327dc63c30dc79fd57525b41\", \"url\": \"https://api.pillowcase.su/api/download/1cff0bda327dc63c30dc79fd57525b41\", \"size\": \"5.93 MB\", \"duration\": 291.83}", "aliases": [], "size": "5.93 MB"}, {"id": "i-feel-like-that-166", "name": "I Feel Like That [V4]", "artists": [], "producers": ["Hudson Mohawke", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: KW - I Feel Like That Ref (6.28.14)\nVersion similar in structure to the music video version, sharing vocals and production elements with it. Snippets originally leaked February 14th, 2023 & April 16th, 2023.", "length": "226.1", "fileDate": 16816896, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/4954e56a7d10af5081732ad8827eb358", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4954e56a7d10af5081732ad8827eb358\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V4]\", \"artists\": \"(prod. <PERSON>, <PERSON>, <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - I Feel Like That Ref (6.28.14)\\nVersion similar in structure to the music video version, sharing vocals and production elements with it. Snippets originally leaked February 14th, 2023 & April 16th, 2023.\", \"date\": 16816896, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6dbfd43a25021c2189f89ff647ee14f0\", \"url\": \"https://api.pillowcase.su/api/download/6dbfd43a25021c2189f89ff647ee14f0\", \"size\": \"4.88 MB\", \"duration\": 226.1}", "aliases": [], "size": "4.88 MB"}, {"id": "keahole", "name": "<PERSON><PERSON><PERSON> [V1]", "artists": [], "producers": ["Hudson Mohawke"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> (7.15.14) \n<PERSON>-produced freestyle. Leaked as a bonus for the November 11th, 2013 Yeezus 2 copy groupbuy. The file was originally not known to be the same as \"<PERSON><PERSON><PERSON>\", a song name seen on a list of songs provided by a trustworthy source, as it was originally known under the fan-given name \"Maybe Next Time\". This was until a snippet of the beat for \"Keahole\" was posted, which matched the beat for this freestyle.", "length": "228.94", "fileDate": 16793568, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b83319553075cc0dda3bc881a72d6df4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b83319553075cc0dda3bc881a72d6df4\", \"key\": \"<PERSON><PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON> [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> (7.15.14) \\nRough Hudson Mohawke-produced freestyle. Leaked as a bonus for the November 11th, 2013 Yeezus 2 copy groupbuy. The file was originally not known to be the same as \\\"<PERSON>ahole\\\", a song name seen on a list of songs provided by a trustworthy source, as it was originally known under the fan-given name \\\"Maybe Next Time\\\". This was until a snippet of the beat for \\\"Keahole\\\" was posted, which matched the beat for this freestyle.\", \"date\": 16793568, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5e3d13fc5a4283ffab2c4df29a0861c3\", \"url\": \"https://api.pillowcase.su/api/download/5e3d13fc5a4283ffab2c4df29a0861c3\", \"size\": \"4.93 MB\", \"duration\": 228.94}", "aliases": [], "size": "4.93 MB"}, {"id": "keahole-168", "name": "<PERSON><PERSON><PERSON> [V2]", "artists": [], "producers": ["Hudson Mohawke"], "notes": "OG Filename: <PERSON><PERSON><PERSON> ft <PERSON><PERSON><PERSON> \nAlternate version of the beat. Has no vocals.", "length": "228.55", "fileDate": 17432928, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3933adaec9a835e4a89269f836b9dbe8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3933adaec9a835e4a89269f836b9dbe8\", \"key\": \"<PERSON><PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON> [V2]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> ft <PERSON><PERSON><PERSON> \\nAlternate version of the beat. Has no vocals.\", \"date\": 17432928, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d91300207677cc2aa7da965614effb1f\", \"url\": \"https://api.pillowcase.su/api/download/d91300207677cc2aa7da965614effb1f\", \"size\": \"4.92 MB\", \"duration\": 228.55}", "aliases": [], "size": "4.92 MB"}, {"id": "mrs-misery", "name": "Mrs. <PERSON> [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - <PERSON><PERSON> (4.3.14) &\n<PERSON><PERSON> demo from Yeezus 2. This is the \"MM\" on the CyHi's Yeezus 2 Spotify playlist. Said to have been recorded the same day as \"Only One\". The file leaked by <PERSON><PERSON><PERSON> is a stem edit with <PERSON>'s vocals added on, which weren't recorded until two days after <PERSON><PERSON><PERSON>'s ref was made. Actual file leaked February 16th, 2023.", "length": "189.72", "fileDate": 16765056, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/5cf614747317cd609b43f1728ce7e729", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5cf614747317cd609b43f1728ce7e729\", \"key\": \"Mrs. <PERSON>sery\", \"title\": \"Mrs. <PERSON>sery [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KW - Mrs. <PERSON><PERSON><PERSON> (4.3.14) &\\nMM\\nMumble demo from Yeezus 2. This is the \\\"MM\\\" on the CyHi's Yeezus 2 Spotify playlist. Said to have been recorded the same day as \\\"Only One\\\". The file leaked by <PERSON><PERSON><PERSON> is a stem edit with <PERSON>'s vocals added on, which weren't recorded until two days after <PERSON><PERSON><PERSON>'s ref was made. Actual file leaked February 16th, 2023.\", \"date\": 16765056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7e37913087b50d278a94047e0f16fdfe\", \"url\": \"https://api.pillowcase.su/api/download/7e37913087b50d278a94047e0f16fdfe\", \"size\": \"4.3 MB\", \"duration\": 189.72}", "aliases": [], "size": "4.3 MB"}, {"id": "mrs-misery-170", "name": "Mrs. <PERSON> [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW - Mrs. <PERSON><PERSON><PERSON> (4.5.14)\nJohn Legend \"Mrs. <PERSON>sery\" reference track. Edited version containing his vocals originally leaked October 2nd, 2022.", "length": "133.77", "fileDate": 16724448, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/593ae47fbf1d0328660e740d6d1e5bc5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/593ae47fbf1d0328660e740d6d1e5bc5\", \"key\": \"Mrs. <PERSON>sery\", \"title\": \"Mrs. <PERSON><PERSON><PERSON> [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: KW - Mrs. Misery JL Ref (4.5.14)\\nJohn <PERSON> \\\"Mrs. Misery\\\" reference track. Edited version containing his vocals originally leaked October 2nd, 2022.\", \"date\": 16724448, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"278a9ce798646e68fbd2d52a0486d918\", \"url\": \"https://api.pillowcase.su/api/download/278a9ce798646e68fbd2d52a0486d918\", \"size\": \"3.4 MB\", \"duration\": 133.77}", "aliases": [], "size": "3.4 MB"}, {"id": "mrs-misery-171", "name": "Mrs. <PERSON> [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KW REF Mrs <PERSON> (7.3.14)\n<PERSON> reference for \"Mrs. <PERSON><PERSON>\".", "length": "185.84", "fileDate": 16720992, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/39594e7965ca549154efc328ef810731", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/39594e7965ca549154efc328ef810731\", \"key\": \"Mrs. <PERSON>ser<PERSON>\", \"title\": \"Mrs. <PERSON>ser<PERSON> [V3]\", \"artists\": \"(ref. <PERSON>IR<PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: KW REF Mrs Misery (7.3.14)\\nKirby Lauryen reference for \\\"Mrs. Misery\\\".\", \"date\": 16720992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a6163044513410065ae3c32861b8ea25\", \"url\": \"https://api.pillowcase.su/api/download/a6163044513410065ae3c32861b8ea25\", \"size\": \"4.24 MB\", \"duration\": 185.84}", "aliases": [], "size": "4.24 MB"}, {"id": "my-city", "name": "My City", "artists": [], "producers": ["Mustard"], "notes": "OG Filename: KW - My City Ref (5.??.14)\n2014 Yeezus 2 throwaway produced by Mustard. Snippets originally leaked September 22nd, 2023 & January 16th, 2024.", "length": "74.02", "fileDate": 17056224, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/920b031642aa698d23cf00e543348418", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/920b031642aa698d23cf00e543348418\", \"key\": \"My City\", \"title\": \"My City\", \"artists\": \"(prod. <PERSON>ard)\", \"description\": \"OG Filename: KW - My City Ref (5.??.14)\\n2014 Yeezus 2 throwaway produced by Mustard. Snippets originally leaked September 22nd, 2023 & January 16th, 2024.\", \"date\": 17056224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5e15e3699f58e668f93f481b1f02e36a\", \"url\": \"https://api.pillowcase.su/api/download/5e15e3699f58e668f93f481b1f02e36a\", \"size\": \"2.45 MB\", \"duration\": 74.02}", "aliases": [], "size": "2.45 MB"}, {"id": "never-let-me-go", "name": "Never Let Me Go [V1]", "artists": [], "producers": [], "notes": "OG Filename: never let me go reff\nVery undeveloped, mostly Kanye mumble singing. Later evolved into \"Pressure.\" Likely from around May 2014.", "length": "160", "fileDate": 16263936, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/5e98ba28aed95bad52d1815b96db7108", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e98ba28aed95bad52d1815b96db7108\", \"key\": \"Never Let Me Go\", \"title\": \"Never Let Me Go [V1]\", \"aliases\": [\"CAN U BE\", \"Pressure\"], \"description\": \"OG Filename: never let me go reff\\nVery undeveloped, mostly Kanye mumble singing. Later evolved into \\\"Pressure.\\\" Likely from around May 2014.\", \"date\": 16263936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1c61ea95471bf3dafa0296007400aab9\", \"url\": \"https://api.pillowcase.su/api/download/1c61ea95471bf3dafa0296007400aab9\", \"size\": \"3.82 MB\", \"duration\": 160}", "aliases": ["CAN U BE", "Pressure"], "size": "3.82 MB"}, {"id": "never-let-me-go-174", "name": "Never Let Me Go [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> Never Let Me Go Idea1. New<PERSON>ook\n<PERSON> reference for \"Never Let Me Go\". According to <PERSON><PERSON>, the Rih in the filename does not mean <PERSON><PERSON><PERSON>.", "length": "114.73", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/72f04c406538f4a0838343c22955aec4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/72f04c406538f4a0838343c22955aec4\", \"key\": \"Never Let Me Go\", \"title\": \"Never Let Me Go [V2]\", \"artists\": \"(ref. KIRBY)\", \"aliases\": [\"CAN U BE\", \"Pressure\"], \"description\": \"OG Filename: Rih Never Let Me Go Idea1. NewHook\\nKirby Lauryen reference for \\\"Never Let Me Go\\\". According to <PERSON><PERSON>, the Rih in the filename does not mean Rihanna.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2a626f482d2f0e9e1f86051ba4346d34\", \"url\": \"https://api.pillowcase.su/api/download/2a626f482d2f0e9e1f86051ba4346d34\", \"size\": \"3.1 MB\", \"duration\": 114.73}", "aliases": ["CAN U BE", "Pressure"], "size": "3.1 MB"}, {"id": "never-let-me-go-175", "name": "Never Let Me Go [V3]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> 1 Never Let Me Go 6.12.14\nA second <PERSON> \"Never Let Me Go\" reference.", "length": "184.35", "fileDate": 17028576, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/3a5580b4ad4a3c91d6909f981eecb01f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a5580b4ad4a3c91d6909f981eecb01f\", \"key\": \"Never Let Me Go\", \"title\": \"Never Let Me Go [V3]\", \"artists\": \"(ref. KIRBY)\", \"aliases\": [\"CAN U BE\", \"Pressure\"], \"description\": \"OG Filename: Rih 1 Never Let Me Go 6.12.14\\nA second <PERSON> \\\"Never Let Me Go\\\" reference.\", \"date\": 17028576, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"255093d92537d268c6b43706d3aa9a0f\", \"url\": \"https://api.pillowcase.su/api/download/255093d92537d268c6b43706d3aa9a0f\", \"size\": \"4.21 MB\", \"duration\": 184.35}", "aliases": ["CAN U BE", "Pressure"], "size": "4.21 MB"}, {"id": "never-let-me-go-176", "name": "Never Let Me Go [V4]", "artists": [], "producers": [], "notes": "Comes from a video recorded by <PERSON><PERSON>, but his production is not heard. <PERSON><PERSON> likely created this loop while he was coming up for ideas on how to rework the song.", "length": "100.55", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/8dd7d5c4c05980600ecaa2c8038c49d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8dd7d5c4c05980600ecaa2c8038c49d2\", \"key\": \"Never Let Me Go\", \"title\": \"Never Let Me Go [V4]\", \"aliases\": [\"CAN U BE\", \"Pressure\"], \"description\": \"Comes from a video recorded by <PERSON><PERSON>, but his production is not heard. <PERSON><PERSON> likely created this loop while he was coming up for ideas on how to rework the song.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"cfe6c43e57c2474e5305f371d016f322\", \"url\": \"https://api.pillowcase.su/api/download/cfe6c43e57c2474e5305f371d016f322\", \"size\": \"2.87 MB\", \"duration\": 100.55}", "aliases": ["CAN U BE", "Pressure"], "size": "2.87 MB"}, {"id": "never-lose-177", "name": "Never Lose [V4]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Never Lose x KW\nA version of \"Never Lose\" with <PERSON> reference vocals. Leaked in the Jabba mass leak.", "length": "181.26", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/e20e6522776be1effe3307d4261c0d6b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e20e6522776be1effe3307d4261c0d6b\", \"key\": \"Never Lose\", \"title\": \"Never Lose [V4]\", \"artists\": \"(ref. KIRB<PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: Never Lose x KW\\nA version of \\\"Never Lose\\\" with <PERSON> reference vocals. Leaked in the Jabba mass leak.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4c7e2674c57b4e0070dab94936e978ac\", \"url\": \"https://api.pillowcase.su/api/download/4c7e2674c57b4e0070dab94936e978ac\", \"size\": \"4.16 MB\", \"duration\": 181.26}", "aliases": [], "size": "4.16 MB"}, {"id": "never-tried-this", "name": "✨ Never Tried This", "artists": [], "producers": ["Velous"], "notes": "OG Filename: KW - Never Tried This Ref (6.1.14)\n2014 Yeezus 2 throwaway that has very similar drums to \"All Day\". Only snippet available before it leaked was recorded from a voice chat. Leaked in full after a $300 GB finished.", "length": "212.58", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/33419e1499d0454e4cc44b6be490218f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/33419e1499d0454e4cc44b6be490218f\", \"key\": \"Never Tried This\", \"title\": \"\\u2728 Never Tried This\", \"artists\": \"(prod. Velous)\", \"description\": \"OG Filename: KW - Never Tried This Ref (6.1.14)\\n2014 Yeezus 2 throwaway that has very similar drums to \\\"All Day\\\". Only snippet available before it leaked was recorded from a voice chat. Leaked in full after a $300 GB finished.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2da3b92a835cc05adceb328b2c2619e0\", \"url\": \"https://api.pillowcase.su/api/download/2da3b92a835cc05adceb328b2c2619e0\", \"size\": \"4.67 MB\", \"duration\": 212.58}", "aliases": [], "size": "4.67 MB"}, {"id": "new-angels-179", "name": "New Angels [V2]", "artists": [], "producers": [], "notes": "OG Filename: NEW ANGELS 6.14.14\nInitial reference track for \"New Angels\" done by <PERSON>.", "length": "199.21", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/95dea6339e610da200ef857cf3938ab5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/95dea6339e610da200ef857cf3938ab5\", \"key\": \"New Angels\", \"title\": \"New Angels [V2]\", \"artists\": \"(ref. KIRBY)\", \"description\": \"OG Filename: NEW ANGELS 6.14.14\\nInitial reference track for \\\"New Angels\\\" done by <PERSON>.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4bb5e29e21a9ccdd78804373de0871c5\", \"url\": \"https://api.pillowcase.su/api/download/4bb5e29e21a9ccdd78804373de0871c5\", \"size\": \"4.45 MB\", \"duration\": 199.21}", "aliases": [], "size": "4.45 MB"}, {"id": "new-angels-180", "name": "New Angels [V3]", "artists": [], "producers": [], "notes": "OG Filename: NEW ANGELS NEW REFERENCE 6.26.14\nLater reference track for \"New Angels\" done by <PERSON>. Has some slightly different lines.", "length": "200.67", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/ba688c705ef2e31a0367b88ca24eaf56", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ba688c705ef2e31a0367b88ca24eaf56\", \"key\": \"New Angels\", \"title\": \"New Angels [V3]\", \"artists\": \"(ref. KIRBY)\", \"description\": \"OG Filename: NEW ANGELS NEW REFERENCE 6.26.14\\nLater reference track for \\\"New Angels\\\" done by <PERSON>. Has some slightly different lines.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d87898d7fee9414130127ac5bf04bb16\", \"url\": \"https://api.pillowcase.su/api/download/d87898d7fee9414130127ac5bf04bb16\", \"size\": \"4.47 MB\", \"duration\": 200.67}", "aliases": [], "size": "4.47 MB"}, {"id": "nina-chop", "name": "✨ <PERSON> [V2]", "artists": [], "producers": ["Kanye West", "Hudson Mohawke"], "notes": "OG Filename: NINA CHOP HM RUFF 80 BPM\nVersion produced by Hudson Mohawke. Made sometime in April 2014. Original snippet leaked May 13th, 2024.", "length": "222.07", "fileDate": 17159040, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/a2f1a63201bf4ca5573d642fa8ad1d8b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2f1a63201bf4ca5573d642fa8ad1d8b\", \"key\": \"<PERSON>\", \"title\": \"\\u2728 <PERSON> [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & Hudson Mohawke)\", \"aliases\": [\"Famous\"], \"description\": \"OG Filename: NINA CHOP HM RUFF 80 BPM\\nVersion produced by Hudson Mohawke. Made sometime in April 2014. Original snippet leaked May 13th, 2024.\", \"date\": 17159040, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"394ac8df0f71a7a92c207a4f492ae469\", \"url\": \"https://api.pillowcase.su/api/download/394ac8df0f71a7a92c207a4f492ae469\", \"size\": \"4.82 MB\", \"duration\": 222.07}", "aliases": ["Famous"], "size": "4.82 MB"}, {"id": "idea-3-182", "name": "Idea 3 [V1]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: KW x PM - Idea 3 4.3.14\nOriginal idea for \"Only One,\" edited for later versions.", "length": "625.82", "fileDate": 17018208, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/286f26578dd6c0d3f3ca2cbd644915f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/286f26578dd6c0d3f3ca2cbd644915f4\", \"key\": \"Idea 3\", \"title\": \"Idea 3 [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Only One\"], \"description\": \"OG Filename: KW x PM - Idea 3 4.3.14\\nOriginal idea for \\\"Only One,\\\" edited for later versions.\", \"date\": 17018208, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5cd4196195b9cd862689bb0d6fc1dd71\", \"url\": \"https://api.pillowcase.su/api/download/5cd4196195b9cd862689bb0d6fc1dd71\", \"size\": \"11.3 MB\", \"duration\": 625.82}", "aliases": ["Only One"], "size": "11.3 MB"}, {"id": "only-one", "name": "Only One [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> - Only One REF (4.3.14)\nJohn Legend \"Only One\" reference track. Leaked alongside the acapella and instrumental.", "length": "261.53", "fileDate": 16529184, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2921cee8967aa313070e8b63372dba7e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2921cee8967aa313070e8b63372dba7e\", \"key\": \"Only One\", \"title\": \"Only One [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON> - Only One REF (4.3.14)\\nJohn <PERSON> \\\"Only One\\\" reference track. Leaked alongside the acapella and instrumental.\", \"date\": 16529184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"495b417659cd98bd138db0630b53a171\", \"url\": \"https://api.pillowcase.su/api/download/495b417659cd98bd138db0630b53a171\", \"size\": \"5.45 MB\", \"duration\": 261.53}", "aliases": [], "size": "5.45 MB"}, {"id": "only-one-184", "name": "Only One [V3]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filenames: KW - Only One Ref (4.5.14) &\nOO\nIncluded on CyHi's Yeezus 2 playlist. A rough mumble freestyle of \"Only One\". Original snippet leaked in January 2022.", "length": "256.77", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/16971f86df82f65b2de9651f0a6684a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16971f86df82f65b2de9651f0a6684a1\", \"key\": \"Only One\", \"title\": \"Only One [V3]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filenames: KW - Only One Ref (4.5.14) &\\nOO\\nIncluded on CyHi's Yeezus 2 playlist. A rough mumble freestyle of \\\"Only One\\\". Original snippet leaked in January 2022.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"45e2d0b244d5fc618b881afa7d3430ca\", \"url\": \"https://api.pillowcase.su/api/download/45e2d0b244d5fc618b881afa7d3430ca\", \"size\": \"5.37 MB\", \"duration\": 256.77}", "aliases": [], "size": "5.37 MB"}, {"id": "only-one-185", "name": "Only One [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "Version of \"Only One\" with talkbox vocals from <PERSON><PERSON> (confirmed by ASCAP listing for \"I Feel Like That\"). Later sampled and chopped up for \"I Feel Like That.\" Recorded sometime before April 23rd, 2014 - when MIKE DEAN sampled it.", "length": "26.26", "fileDate": 16816896, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/183a893c9eaa57d6a4cc28f14c830092", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/183a893c9eaa57d6a4cc28f14c830092\", \"key\": \"Only One\", \"title\": \"Only One [V4]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"Version of \\\"Only One\\\" with talkbox vocals from <PERSON><PERSON> (confirmed by ASCAP listing for \\\"I Feel Like That\\\"). Later sampled and chopped up for \\\"I Feel Like That.\\\" Recorded sometime before April 23rd, 2014 - when MIKE DEAN sampled it.\", \"date\": 16816896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"482285a80515e1ec3704f7846a5d3046\", \"url\": \"https://api.pillowcase.su/api/download/482285a80515e1ec3704f7846a5d3046\", \"size\": \"1.68 MB\", \"duration\": 26.26}", "aliases": [], "size": "1.68 MB"}, {"id": "piss-on-your-grave", "name": "Piss On Your Grave [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Original \"Piss On Your Grave\" freestyle, recorded over the base sample (bleed can be heard in <PERSON>'s vocal stem). Contains extra vocals as shown in the 88-Keys snippet.", "length": "116.23", "fileDate": 15632352, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/069a6299be2af79349cfee216360573f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/069a6299be2af79349cfee216360573f\", \"key\": \"Piss On Your Grave\", \"title\": \"Piss On Your Grave [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Original \\\"Piss On Your Grave\\\" freestyle, recorded over the base sample (bleed can be heard in <PERSON>'s vocal stem). Contains extra vocals as shown in the 88-Keys snippet.\", \"date\": 15632352, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3e59b123cdd4b426c300648cd1338582\", \"url\": \"https://api.pillowcase.su/api/download/3e59b123cdd4b426c300648cd1338582\", \"size\": \"3.12 MB\", \"duration\": 116.23}", "aliases": [], "size": "3.12 MB"}, {"id": "piss-on-your-grave-187", "name": "Piss On Your Grave [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Version of the \"Piss On Your Grave\" freestyle with a new & completely different beat from 88-Keys. Likely just a one-off version.", "length": "", "fileDate": 17268768, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/1d46e435372cb24a1571a0ed773fe94c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1d46e435372cb24a1571a0ed773fe94c\", \"key\": \"Piss On Your Grave\", \"title\": \"Piss On Your Grave [V2]\", \"artists\": \"(prod. 88-Keys)\", \"description\": \"Version of the \\\"Piss On Your Grave\\\" freestyle with a new & completely different beat from 88-Keys. Likely just a one-off version.\", \"date\": 17268768, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "see-how-these-niggas-is", "name": "See How These Niggas Is [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - See How These Ni**as <PERSON> (6.1.14)\nFreestyle with some coherent lines. Previewed at a listening party for Yeezus 2. <PERSON> was later used by <PERSON><PERSON><PERSON> on his song \"Did It Anyway\". Played directly after \"All Day\", but is confirmed to be a separate song.", "length": "186.75", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/294be1be164dbeaac51b0da608f869eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/294be1be164dbeaac51b0da608f869eb\", \"key\": \"See How These Niggas Is\", \"title\": \"See How These Niggas Is [V2]\", \"aliases\": [\"Be That Nigga\"], \"description\": \"OG Filename: KW - See How These Ni**as Is Ref (6.1.14)\\nFreestyle with some coherent lines. Previewed at a listening party for Yeezus 2. <PERSON> was later used by <PERSON><PERSON><PERSON> on his song \\\"Did It Anyway\\\". Played directly after \\\"All Day\\\", but is confirmed to be a separate song.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"95ff52415aa3b4642e48ead8f903e7b3\", \"url\": \"https://api.pillowcase.su/api/download/95ff52415aa3b4642e48ead8f903e7b3\", \"size\": \"4.25 MB\", \"duration\": 186.75}", "aliases": ["Be <PERSON> Nigga"], "size": "4.25 MB"}, {"id": "special-189", "name": "Special [V4]", "artists": [], "producers": [], "notes": "Version played at the Cafe Royal listening party. Has a different instrumental that lacks the piano, contains no verses and a hook with slightly different lyrics than other versions.", "length": "", "fileDate": 14088384, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://www.youtube.com/watch?v=_dYYDf7f-X4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=_dYYDf7f-X4\", \"key\": \"Special\", \"title\": \"Special [V4]\", \"description\": \"Version played at the Cafe Royal listening party. Has a different instrumental that lacks the piano, contains no verses and a hook with slightly different lyrics than other versions.\", \"date\": 14088384, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "special-190", "name": "Special [V4]", "artists": [], "producers": [], "notes": "Version played at the Cafe Royal listening party. Has a different instrumental that lacks the piano, contains no verses and a hook with slightly different lyrics than other versions.", "length": "90.8", "fileDate": 14088384, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pixeldrain.com/u/gLQSvzuk", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pixeldrain.com/u/gLQSvzuk\", \"key\": \"Special\", \"title\": \"Special [V4]\", \"description\": \"Version played at the Cafe Royal listening party. Has a different instrumental that lacks the piano, contains no verses and a hook with slightly different lyrics than other versions.\", \"date\": 14088384, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a6f85a22bc458d8e094c04f350f5c8c4\", \"url\": \"https://api.pillowcase.su/api/download/a6f85a22bc458d8e094c04f350f5c8c4\", \"size\": \"2.72 MB\", \"duration\": 90.8}", "aliases": [], "size": "2.72 MB"}, {"id": "the-world", "name": "The World [V1]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON> (Master)\nInstrumental in in a April 2014 folder called \"Mitus - April (kanye&jcole)\". It has been confirmed by <PERSON><PERSON> in a May interview that <PERSON> recorded over this but we won't be getting the vox. Posted again on his Spore website (still without vocals) in 2024.", "length": "235.65", "fileDate": 17238528, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/b72106a1e00af54f9af70f207bacd1d6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b72106a1e00af54f9af70f207bacd1d6\", \"key\": \"The World\", \"title\": \"The World [V1]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON><PERSON> (Master)\\nInstrumental in in a April 2014 folder called \\\"Mitus - April (kanye&jcole)\\\". It has been confirmed by <PERSON><PERSON> in a May interview that <PERSON> recorded over this but we won't be getting the vox. Posted again on his Spore website (still without vocals) in 2024.\", \"date\": 17238528, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(255, 255, 255)\", \"rgb(69, 188, 255)\"], \"id\": \"76bdf01d24c07ea77cbfde3253acb834\", \"url\": \"https://api.pillowcase.su/api/download/76bdf01d24c07ea77cbfde3253acb834\", \"size\": \"5.03 MB\", \"duration\": 235.65}", "aliases": [], "size": "5.03 MB"}, {"id": "too-reckless", "name": "✨ Too Reckless [V5]", "artists": ["<PERSON> Thug"], "producers": [], "notes": "OG Filename: KW - <PERSON>ss Ref (5.6.14)\nVersion from May 2014. Contains the same vocals as earlier versions, but with added production.", "length": "187.89", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/765b16eaffc3a81289276931916221a7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/765b16eaffc3a81289276931916221a7\", \"key\": \"<PERSON> Reckless\", \"title\": \"\\u2728 <PERSON> Reckless [V5]\", \"artists\": \"(feat. <PERSON>hug)\", \"aliases\": [\"Man Up\", \"Bad Night\", \"Rap Tarantino\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: KW - <PERSON> Ref (5.6.14)\\nVersion from May 2014. Contains the same vocals as earlier versions, but with added production.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e185e419ccb161d6a98af9a50d4b0321\", \"url\": \"https://api.pillowcase.su/api/download/e185e419ccb161d6a98af9a50d4b0321\", \"size\": \"4.27 MB\", \"duration\": 187.89}", "aliases": ["Man Up", "Bad Night", "<PERSON>", "Too Re<PERSON>ss", "Bad Guy"], "size": "4.27 MB"}, {"id": "when-i-see-it-193", "name": "When I See It [V6]", "artists": [], "producers": [], "notes": "OG Filename: WISI\nVersion of \"When I See It\" found on CyHi's \"ypm\" playlist. Just instrumental. Leaked after a groupbuy.", "length": "116.04", "fileDate": 17216064, "leakDate": "", "availableLength": "Beat Only", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/20a0cfe44a900fda9f76b15568a01163", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20a0cfe44a900fda9f76b15568a01163\", \"key\": \"When I See It\", \"title\": \"When I See It [V6]\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"OG Filename: WISI\\nVersion of \\\"When I See It\\\" found on CyHi's \\\"ypm\\\" playlist. Just instrumental. Leaked after a groupbuy.\", \"date\": 17216064, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ca6ab30125befb4300b29da2c1ffc1f6\", \"url\": \"https://api.pillowcase.su/api/download/ca6ab30125befb4300b29da2c1ffc1f6\", \"size\": \"3.12 MB\", \"duration\": 116.04}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "3.12 MB"}, {"id": "evolve", "name": "Evolve [V7]", "artists": ["<PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: Evolve Noel ref_1\nSame as other OVO Radio version except uncensored and higher quality, resurfaced after an uncensored version of the episode was found. Part of <PERSON>'s verse is used in later versions, with an extra line not found in this one. Unclear if this was a version edited specifically for OVO Radio, but considering the verse is from Kanye versions of the song and the beat is the less developed version used in other Kanye versions, it's likely this is the same initial recording <PERSON> did for the song in 2014.", "length": "184.4", "fileDate": 14415840, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/8213c4b893ef819f408cfc77dcc2866b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8213c4b893ef819f408cfc77dcc2866b\", \"key\": \"Evolve\", \"title\": \"Evolve [V7]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"aliases\": [\"Soul Dog\", \"When I See It\", \"Tell Your Friends\"], \"description\": \"OG Filename: Evolve Noel ref_1\\nSame as other OVO Radio version except uncensored and higher quality, resurfaced after an uncensored version of the episode was found. Part of <PERSON>'s verse is used in later versions, with an extra line not found in this one. Unclear if this was a version edited specifically for OVO Radio, but considering the verse is from Kanye versions of the song and the beat is the less developed version used in other Kanye versions, it's likely this is the same initial recording <PERSON> did for the song in 2014.\", \"date\": 14415840, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"13e488e8400de4f11fadea0ea6859a07\", \"url\": \"https://api.pillowcase.su/api/download/13e488e8400de4f11fadea0ea6859a07\", \"size\": \"4.21 MB\", \"duration\": 184.4}", "aliases": ["Soul Dog", "When I See It", "Tell Your Friends"], "size": "4.21 MB"}, {"id": "evolve-195", "name": "Evolve [V7] [Clean]", "artists": ["<PERSON>"], "producers": ["MIKE DEAN"], "notes": "<PERSON> version of \"When I See It\", played on <PERSON>'s OVOSound Radio. Contains a OVOSound tag and is censored.", "length": "158.18", "fileDate": 14415840, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/76258657cbba54ea88c56db3bd06c33c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76258657cbba54ea88c56db3bd06c33c\", \"key\": \"Evolve\", \"title\": \"Evolve [V7] [Clean]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"aliases\": [\"Soul Dog\", \"When I See It\", \"Tell Your Friends\"], \"description\": \"Solo Drake version of \\\"When I See It\\\", played on <PERSON>'s OVOSound Radio. Contains a OVOSound tag and is censored.\", \"date\": 14415840, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"bc9d131f33fa6c38481ca4c25034de34\", \"url\": \"https://api.pillowcase.su/api/download/bc9d131f33fa6c38481ca4c25034de34\", \"size\": \"3.79 MB\", \"duration\": 158.18}", "aliases": ["Soul Dog", "When I See It", "Tell Your Friends"], "size": "3.79 MB"}, {"id": "when-i-see-it-196", "name": "⭐ When I See It [V9]", "artists": ["The Weeknd", "<PERSON>"], "producers": ["MIKE DEAN", "Hudson Mohawke"], "notes": "OG Filename: KW - When I see It Ref (5.10.14)\nThe original version of The Weeknd track \"Tell Your Friends\" originally meant for Yeezus 2 and eventually So Help Me God. Released version features part of <PERSON><PERSON><PERSON>'s first verse muffled. Originally teased by GOODMUSICINSIDER in 2014. Features The Weeknd and <PERSON> and has a MIKE DEAN outro with <PERSON><PERSON><PERSON> drums. The <PERSON><PERSON><PERSON> verse is a longer mumble ref compared to the \"When I See It\" demo he publicly released, and <PERSON>'s verse has some minor differences from his officially released remix.", "length": "299.65", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/286146b7bcf8b1249455e351c23b1908", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/286146b7bcf8b1249455e351c23b1908\", \"key\": \"When I See It\", \"title\": \"\\u2b50 When I See It [V9]\", \"artists\": \"(feat. <PERSON> Weeknd & Drake) (prod. <PERSON><PERSON> DEAN & Hudson Mohawke)\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"OG Filename: KW - When I see It Ref (5.10.14)\\nThe original version of The Weeknd track \\\"Tell Your Friends\\\" originally meant for Yeezus 2 and eventually So Help Me God. Released version features part of <PERSON><PERSON><PERSON>'s first verse muffled. Originally teased by GOODMUSICINSIDER in 2014. Features The Weeknd and Drake and has a MIKE DEAN outro with <PERSON><PERSON><PERSON> drums. The Kanye verse is a longer mumble ref compared to the \\\"When I See It\\\" demo he publicly released, and <PERSON>'s verse has some minor differences from his officially released remix.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"34786cfa6fce2a1a6107bf825e698b0b\", \"url\": \"https://api.pillowcase.su/api/download/34786cfa6fce2a1a6107bf825e698b0b\", \"size\": \"6.06 MB\", \"duration\": 299.65}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "6.06 MB"}, {"id": "when-i-see-it-197", "name": "When I See It [V10]", "artists": ["The Weeknd"], "producers": [], "notes": "OG Filename: KW - When I See It Ref For <PERSON> @ 74.5 BPM\nThis version does not have any Drake, intended for <PERSON> to record on.", "length": "141.93", "fileDate": 16715808, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/23224f74da1d5a05305d12501e2bb2c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23224f74da1d5a05305d12501e2bb2c3\", \"key\": \"When I See It\", \"title\": \"When I See It [V10]\", \"artists\": \"(feat. The Weeknd)\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"OG Filename: KW - When I See It Ref For Kirby @ 74.5 BPM\\nThis version does not have any Drake, intended for <PERSON> to record on.\", \"date\": 16715808, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1101b121b7ebcc77b149567e0b048381\", \"url\": \"https://api.pillowcase.su/api/download/1101b121b7ebcc77b149567e0b048381\", \"size\": \"3.54 MB\", \"duration\": 141.93}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "3.54 MB"}, {"id": "when-i-see-it-198", "name": "When I See It [V11]", "artists": [], "producers": [], "notes": "OG Filename: When I See It 6.17.14\n<PERSON> reference track \"When I See It\". This reference would later be used by <PERSON><PERSON><PERSON> when he released his own solo version on Souncloud.", "length": "132.34", "fileDate": 16715808, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/51b9479ae9046661b87880a464eeee5a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/51b9479ae9046661b87880a464eeee5a\", \"key\": \"When I See It\", \"title\": \"When I See It [V11]\", \"artists\": \"(ref. KIRBY)\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"OG Filename: When I See It 6.17.14\\nKirby Lauryen reference track \\\"When I See It\\\". This reference would later be used by <PERSON><PERSON><PERSON> when he released his own solo version on Souncloud.\", \"date\": 16715808, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9a5ad349a73b7406215ef09ccd897941\", \"url\": \"https://api.pillowcase.su/api/download/9a5ad349a73b7406215ef09ccd897941\", \"size\": \"3.38 MB\", \"duration\": 132.34}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "3.38 MB"}, {"id": "when-i-see-it-199", "name": "When I See It [V12]", "artists": ["The Weeknd"], "producers": [], "notes": "More finalized version of \"When I See It\", played at the Cafe Royal listening party. Has a full verse from The Weeknd. <PERSON><PERSON> starts with the sample instead of The Weeknd's hook. It's unknown whether his verse was recorded in the same session as his hook, and if any other vocals were different. Snippet originally surfaced on TinyPic on October 1st, 2014.", "length": "3.24", "fileDate": 14121216, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/7c8afd5869936152771f6b78ec76a178", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c8afd5869936152771f6b78ec76a178\", \"key\": \"When I See It\", \"title\": \"When I See It [V12]\", \"artists\": \"(feat. The Weeknd)\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"More finalized version of \\\"When I See It\\\", played at the Cafe Royal listening party. Has a full verse from The Weeknd. <PERSON><PERSON> starts with the sample instead of The Weeknd's hook. It's unknown whether his verse was recorded in the same session as his hook, and if any other vocals were different. Snippet originally surfaced on TinyPic on October 1st, 2014.\", \"date\": 14121216, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"aa35589c21c7ea219dcf4a5e956ec774\", \"url\": \"https://api.pillowcase.su/api/download/aa35589c21c7ea219dcf4a5e956ec774\", \"size\": \"1.31 MB\", \"duration\": 3.24}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "1.31 MB"}, {"id": "when-i-see-it-200", "name": "When I See It [V12]", "artists": ["The Weeknd"], "producers": [], "notes": "More finalized version of \"When I See It\", played at the Cafe Royal listening party. Has a full verse from The Weeknd. <PERSON><PERSON> starts with the sample instead of The Weeknd's hook. It's unknown whether his verse was recorded in the same session as his hook, and if any other vocals were different. Snippet originally surfaced on TinyPic on October 1st, 2014.", "length": "7.5", "fileDate": 14121216, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/795b07e2cccaa0ef600ad547fb534a41", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/795b07e2cccaa0ef600ad547fb534a41\", \"key\": \"When I See It\", \"title\": \"When I See It [V12]\", \"artists\": \"(feat. The Weeknd)\", \"aliases\": [\"Soul Dog\", \"Tell Your Friends\"], \"description\": \"More finalized version of \\\"When I See It\\\", played at the Cafe Royal listening party. Has a full verse from The Weeknd. <PERSON><PERSON> starts with the sample instead of The Weeknd's hook. It's unknown whether his verse was recorded in the same session as his hook, and if any other vocals were different. Snippet originally surfaced on TinyPic on October 1st, 2014.\", \"date\": 14121216, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2f0e30b91075d373cf69c859694fcc0a\", \"url\": \"https://api.pillowcase.su/api/download/2f0e30b91075d373cf69c859694fcc0a\", \"size\": \"1.38 MB\", \"duration\": 7.5}", "aliases": ["Soul Dog", "Tell Your Friends"], "size": "1.38 MB"}, {"id": "-201", "name": "???", "artists": [], "producers": ["<PERSON>"], "notes": "Freestyle from an unknown era. Most likely recorded late 2013 - early 2015 as that was when <PERSON><PERSON><PERSON> was recording over Young Chop beats. Snippet leaked July 23rd, 2024.", "length": "14.11", "fileDate": 17216928, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/ceb6fcc0fa2449b23facae4fbd2b44b4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ceb6fcc0fa2449b23facae4fbd2b44b4\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"45 Mill\"], \"description\": \"Freestyle from an unknown era. Most likely recorded late 2013 - early 2015 as that was when <PERSON><PERSON><PERSON> was recording over Young Chop beats. Snippet leaked July 23rd, 2024.\", \"date\": 17216928, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"82528a7bb5636dd35278b57351752525\", \"url\": \"https://api.pillowcase.su/api/download/82528a7bb5636dd35278b57351752525\", \"size\": \"1.49 MB\", \"duration\": 14.11}", "aliases": ["45 Mill"], "size": "1.49 MB"}, {"id": "-202", "name": "???", "artists": [], "producers": [], "notes": "Yeezus 2 voice memo. <PERSON> mumble singing and Kanye beatboxing. From the Ye x Paul McCartney sessions.", "length": "280.37", "fileDate": 17005248, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/dd7d1ee4acc63628aa7dacaac3c6615b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd7d1ee4acc63628aa7dacaac3c6615b\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Holy Water\"], \"description\": \"<PERSON>ezus 2 voice memo. Has mumble singing and Kany<PERSON> beatboxing. From the Ye x <PERSON> sessions.\", \"date\": 17005248, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e47123141adbc44805edbb2f8543c780\", \"url\": \"https://api.pillowcase.su/api/download/e47123141adbc44805edbb2f8543c780\", \"size\": \"5.75 MB\", \"duration\": 280.37}", "aliases": ["Holy Water"], "size": "5.75 MB"}, {"id": "321-scan", "name": "JAY-Z - 321 Scan", "artists": ["<PERSON>"], "producers": ["London on da Track"], "notes": "OG Filename: <PERSON><PERSON><PERSON>- 321 Scan - KL ref - -B day 1 Paris-\nJAY-Z track that reuses his vocals from the Watch the Throne version of \"Can't Look In My Eyes\". Was also wrongfully thought to been worked on by <PERSON><PERSON> for his solo project due to the filename, but it's an engineer's initials.", "length": "355.33", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/bb70bd2cbeaa517a8ec6e752a9ce4f85", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb70bd2cbeaa517a8ec6e752a9ce4f85\", \"key\": \"321 Scan\", \"title\": \"JAY-<PERSON> - 321 Scan\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>\\u00e9) (prod. London on da Track)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON>- 321 Scan - KL ref - -B day 1 Paris-\\nJAY-Z track that reuses his vocals from the Watch the Throne version of \\\"Can't Look In My Eyes\\\". Was also wrongfully thought to been worked on by <PERSON><PERSON> for his solo project due to the filename, but it's an engineer's initials.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2f55a8c91b6a28fe24f368338082d1e6\", \"url\": \"https://api.pillowcase.su/api/download/2f55a8c91b6a28fe24f368338082d1e6\", \"size\": \"6.95 MB\", \"duration\": 355.33}", "aliases": [], "size": "6.95 MB"}, {"id": "4-4-4-4", "name": "Keyon Christ - 4 4 4 4 [V1]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: 4 4 4 4 MTS\nOriginal <PERSON><PERSON> instrumental later reused for a version of \"Saint Pablo\". File is dated 2014.", "length": "254.54", "fileDate": 16055712, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/13dd457f134c79a848655d06d68fad14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/13dd457f134c79a848655d06d68fad14\", \"key\": \"4 4 4 4\", \"title\": \"<PERSON><PERSON> Christ - 4 4 4 4 [V1]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"<PERSON> Pablo\"], \"description\": \"OG Filename: 4 4 4 4 MTS\\nOriginal <PERSON><PERSON> instrumental later reused for a version of \\\"Saint Pablo\\\". File is dated 2014.\", \"date\": 16055712, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"adf2a5a5f6cf7751b552b38cc6fb8fc2\", \"url\": \"https://api.pillowcase.su/api/download/adf2a5a5f6cf7751b552b38cc6fb8fc2\", \"size\": \"5.34 MB\", \"duration\": 254.54}", "aliases": ["Saint Pablo"], "size": "5.34 MB"}, {"id": "4-4-4-4-205", "name": "Keyon Christ - 4 4 4 4 [V2]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Second version of \"4 4 4 4\". Played by <PERSON><PERSON> in his Discord.", "length": "48.61", "fileDate": 16077312, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/a0b7897a46bb900d2569851311474680", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0b7897a46bb900d2569851311474680\", \"key\": \"4 4 4 4\", \"title\": \"<PERSON><PERSON> - 4 4 4 4 [V2]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Saint Pablo\"], \"description\": \"Second version of \\\"4 4 4 4\\\". Played by <PERSON><PERSON> in his Discord.\", \"date\": 16077312, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ec12916ae64072c898f5a17d843c22c5\", \"url\": \"https://api.pillowcase.su/api/download/ec12916ae64072c898f5a17d843c22c5\", \"size\": \"2.04 MB\", \"duration\": 48.61}", "aliases": ["Saint Pablo"], "size": "2.04 MB"}, {"id": "-206", "name": "<PERSON><PERSON> - ???", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "<PERSON><PERSON>-produced instrumental that was added to later versions of \"FML\". Instrumental snippet uploaded by <PERSON><PERSON>.", "length": "60.5", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/4b03d96fe4d81dcc38ea4280cca45ef9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4b03d96fe4d81dcc38ea4280cca45ef9\", \"key\": \"???\", \"title\": \"<PERSON><PERSON> Christ - ???\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"<PERSON><PERSON> Christ-produced instrumental that was added to later versions of \\\"FML\\\". Instrumental snippet uploaded by <PERSON><PERSON>.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7b30a6bfe602784c19ade51242b2d440\", \"url\": \"https://api.pillowcase.su/api/download/7b30a6bfe602784c19ade51242b2d440\", \"size\": \"2.23 MB\", \"duration\": 60.5}", "aliases": [], "size": "2.23 MB"}, {"id": "m-p-a", "name": "<PERSON><PERSON><PERSON> T - M.P.A. [V2]", "artists": ["Kanye West"], "producers": ["Kanye West"], "notes": "OG Filename: MPA - Rough 1\n2014 version of \"M.P.A.\" with mostly different lyrics throughout.", "length": "", "fileDate": 17191008, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/2614dfa5b30ea3e1bd695772b5286a9c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2614dfa5b30ea3e1bd695772b5286a9c\", \"key\": \"M.P.A.\", \"title\": \"Pusha T - M.P.A. [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Money Pussy Alcohol\"], \"description\": \"OG Filename: MPA - Rough 1\\n2014 version of \\\"M.P.A.\\\" with mostly different lyrics throughout.\", \"date\": 17191008, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Money Pussy Alcohol"], "size": ""}, {"id": "-208", "name": "<PERSON> - ??? [V1]", "artists": [], "producers": [], "notes": "Early version of \"Champions\" with just a <PERSON> chorus. He planned to record verses, but likely never did. Was untitled up until right before release. Previewed by Low Pros via Instagram in August 2014. A fake edit leaked in 2018.", "length": "", "fileDate": 14068512, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "yeezus-2", "originalUrl": "https://www.instagram.com/p/rtMf7iJcVc/?igshid=10jzavqdrrvel", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.instagram.com/p/rtMf7iJcVc/?igshid=10jzavqdrrvel\", \"key\": \"???\", \"title\": \"<PERSON> - ??? [V1]\", \"aliases\": [\"Champions\", \"Round and Round\"], \"description\": \"Early version of \\\"Champions\\\" with just a <PERSON> chorus. He planned to record verses, but likely never did. Was untitled up until right before release. Previewed by Low Pros via Instagram in August 2014. A fake edit leaked in 2018.\", \"date\": 14068512, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Champions", "Round and Round"], "size": ""}, {"id": "gone", "name": "The Weeknd - Gone", "artists": [], "producers": ["High Klassified"], "notes": "A Weeknd song that uses lyrics that would later become a verse on \"When I See It\" / \"Tell Your Friends\". Previewed by High Klassified in July of 2015, but probably made prior to the verse being added to \"When I See It\". The song leaked in full in May of 2016.", "length": "161.33", "fileDate": 14620608, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "yeezus-2", "originalUrl": "https://pillowcase.su/f/db072748008ad1c25379a1d528054bf6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/db072748008ad1c25379a1d528054bf6\", \"key\": \"Gone\", \"title\": \"The Weeknd - Gone\", \"artists\": \"(prod. High Klassified)\", \"description\": \"A Weeknd song that uses lyrics that would later become a verse on \\\"When I See It\\\" / \\\"Tell Your Friends\\\". Previewed by High Klassified in July of 2015, but probably made prior to the verse being added to \\\"When I See It\\\". The song leaked in full in May of 2016.\", \"date\": 14620608, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"99d2e0ba78252cfa7b8edbfbf5224b85\", \"url\": \"https://api.pillowcase.su/api/download/99d2e0ba78252cfa7b8edbfbf5224b85\", \"size\": \"3.84 MB\", \"duration\": 161.33}", "aliases": [], "size": "3.84 MB"}]}