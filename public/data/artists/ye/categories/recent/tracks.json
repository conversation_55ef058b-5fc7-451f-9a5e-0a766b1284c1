[{"era": "WW3", "tracks": [{"id": "6f6d06c992cc9f23712415788fba1406", "title": "BIANCA [V1]", "artists": null, "length": "1:48", "ogFilename": null, "notes": "Played on DJ Akademiks stream April 2nd, 2025.", "tags": ["Partial"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/4530331fb33c1828bd3bbe69afb0ee51", "key": "BIANCA", "description": "Played on DJ Akademiks stream April 2nd, 2025.", "date": 17435520, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/6f6d06c992cc9f23712415788fba1406", "size": "1.9 MB", "duration": 108.04}]}, {"era": "Yeezus 2", "tracks": [{"id": "45604003bd6962af1b87a3c202ca733a", "title": "Do You Really Ever Love", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:11", "ogFilename": "ASR Sample Outro Only &", "notes": "KW - Do You Really Ever Love Seen from a list of SWISH-era Post Malone references, but later confirmed to actually be from Yeezus 2. Also present as the outro to the last version of \"God Level\". Leaked as a bonus for a Soakbuy.", "tags": ["OG File", "Lossless"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/2bee7ca794f00ac68b0d4abaa6c72a14", "key": "Do You Really Ever Love", "description": "OG Filenames: <PERSON><PERSON> Sample Outro Only &\nKW - Do You Really Ever Love\nSeen from a list of SWISH-era Post Malone references, but later confirmed to actually be from Yeezus 2. Also present as the outro to the last version of \"God Level\". Leaked as a bonus for a Soakbuy.", "date": 17433792, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/45604003bd6962af1b87a3c202ca733a", "size": "2.4 MB", "duration": 71.07}, {"id": "d91300207677cc2aa7da965614effb1f", "title": "<PERSON><PERSON><PERSON> [V2]", "artists": "(prod. <PERSON>)", "length": "3:48", "ogFilename": "Keahole ft Kanye West", "notes": "Alternate version of the beat. Has no vocals.", "tags": ["Beat Only"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/3933adaec9a835e4a89269f836b9dbe8", "key": "Ke<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON><PERSON> ft <PERSON><PERSON><PERSON> \nAlternate version of the beat. Has no vocals.", "date": 17432928, "available": ["Beat Only", "rgb(255, 255, 255)", "rgb(142, 124, 195)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d91300207677cc2aa7da965614effb1f", "size": "4.92 MB", "duration": 228.55}]}, {"era": "So Help Me God", "tracks": [{"id": "c219ccdc58f0cac7dc9fd2f06b717836", "title": "<PERSON><PERSON> [V3]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, Charlie Heat & Theophilus London)", "length": "2:19", "ogFilename": null, "notes": "An early version with <PERSON><PERSON><PERSON> on the hook with extra vocals and adlibs. Contains two Ye verses and <PERSON> vocals layered on the Theo hook. Leaked alongside its stems in a Soakbuy.", "tags": ["Lossless"], "aliases": ["(Waves)"], "type": "track", "originalUrl": "https://pillowcase.su/f/95ab7bba78818663da0425d664de8eb0", "key": "<PERSON><PERSON>", "description": "An early version with <PERSON><PERSON><PERSON> on the hook with extra vocals and adlibs. Contains two Ye verses and <PERSON> vocals layered on the Theo hook. Leaked alongside its stems in a Soakbuy.", "date": 17432928, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/c219ccdc58f0cac7dc9fd2f06b717836", "size": "4.65 MB", "duration": 139.85}]}, {"era": "The Life Of Pablo", "tracks": [{"id": "6d889b0d6f956593d1a79357ccd39a95", "title": "<PERSON><PERSON><PERSON> [V3]", "artists": "(prod. Hudson Mohawke & Blood Orange)", "length": "4:07", "ogFilename": "Keahole ft Blood Orange", "notes": "Way later on version with Blood Orange production added.", "tags": ["Beat Only"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/8146627a232870f52aa60a66807d11c9", "key": "Ke<PERSON><PERSON>", "description": "OG Filename: Keahole ft Blood Orange\nWay later on version with Blood Orange production added.", "date": 17432928, "available": ["Beat Only", "rgb(255, 255, 255)", "rgb(142, 124, 195)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6d889b0d6f956593d1a79357ccd39a95", "size": "5.48 MB", "duration": 247.46}]}, {"era": "JESUS IS LORD", "tracks": [{"id": "ae1d2b8f47842f9c982558df73d3cc2c", "title": "<PERSON> - <PERSON>mp [V1]", "artists": "(feat. <PERSON><PERSON><PERSON>)", "length": "0:12", "ogFilename": null, "notes": "Has rough mumble vocals from <PERSON><PERSON><PERSON><PERSON> leaked March 30th, 2025, in response to <PERSON> claiming in an interview with DJ <PERSON><PERSON><PERSON><PERSON> that 4 songs he did in Wyoming eventually ended up on <PERSON>'s album Utopia, meaning this version likely has the Travis feature from the <PERSON><PERSON> Rich verison, but was <PERSON>' song at the time. Unknown if <PERSON><PERSON> is on this version. Exact era is unknown, but said to originate from the Wyoming sessions.", "tags": ["Snippet"], "aliases": ["(<PERSON>)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6a692878b7affb7545a09fc61a305020", "key": "Big Champ", "description": "Has rough mumble vocals from <PERSON><PERSON><PERSON><PERSON> leaked March 30th, 2025, in response to <PERSON> claiming in an interview with DJ <PERSON><PERSON><PERSON><PERSON> that 4 songs he did in Wyoming eventually ended up on <PERSON>'s album Utopia, meaning this version likely has the Travis feature from the <PERSON><PERSON> Rich verison, but was <PERSON>' song at the time. Unknown if <PERSON><PERSON> is on this version. Exact era is unknown, but said to originate from the Wyoming sessions.", "date": 17432928, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/ae1d2b8f47842f9c982558df73d3cc2c", "size": "2.24 MB", "duration": 12.28}]}, {"era": "DONDA [V1]", "tracks": [{"id": "d4dcb3483e985b178d7fface03e5f4b4", "title": "<PERSON> - <PERSON><PERSON><PERSON> [V1]", "artists": "(feat. <PERSON><PERSON><PERSON>)", "length": "0:09", "ogFilename": null, "notes": "Made sometime in 2020. <PERSON> vocals.", "tags": ["Snippet"], "aliases": ["(DELRESTO (ECHOES))"], "type": "track", "originalUrl": "https://pillowcase.su/f/3cc8710a8d5ef909f53fdfc973311cd4", "key": "Delresto", "description": "Made sometime in 2020. <PERSON> vocals.", "date": 17432928, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/d4dcb3483e985b178d7fface03e5f4b4", "size": "4.09 MB", "duration": 9.82}]}, {"era": "BULLY", "tracks": [{"id": "db8e02161c0b455946be35a55800ba08", "title": "The Game - Free Suge", "artists": "(feat. <PERSON><PERSON><PERSON> & King Combs)", "length": "0:30", "ogFilename": null, "notes": "Snippet posted March 29th 2025. Recorded some time in 2025. Has real mumble Ye vocals. Samples \"Everybody Loves the Sunshine\" by <PERSON>. Will not drop as the sample cannot be cleared.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/03eb326a5b8fbb4d16dae1ab8ff5ac39", "key": "Free Suge", "description": "Snippet posted March 29th 2025. Recorded some time in 2025. Has real mumble Ye vocals. Samples \"Everybody Loves the Sunshine\" by <PERSON>. Will not drop as the sample cannot be cleared.", "date": 17432064, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/db8e02161c0b455946be35a55800ba08", "size": "1.36 MB", "duration": 30.75}]}, {"era": "My Beautiful Dark Twisted Fantasy", "tracks": [{"id": "51a5020efe35870fe8ae17fd6884eade", "title": "Pure [V1]", "artists": null, "length": "3:34", "ogFilename": "Pure Sample Track", "notes": "Listed by HipHopDX as a bonus track for the planned My Beautiful Dark Twisted Fantasy Deluxe. Mumble demo, lacks the <PERSON><PERSON> vocals. Samples \"Gonna Get Along Without You Now\" by <PERSON>.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/2ed24ec11169f050af6ee8ea10beab93", "key": "Pure", "description": "OG Filename: Pure Sample Track\nListed by HipHopDX as a bonus track for the planned My Beautiful Dark Twisted Fantasy Deluxe. Mumble demo, lacks the <PERSON><PERSON> vocals. Samples \"Gonna Get Along Without You Now\" by <PERSON>.", "date": 17431200, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/51a5020efe35870fe8ae17fd6884eade", "size": "5.11 MB", "duration": 214.75}]}, {"era": "YEBU", "tracks": [{"id": "40f8a9a45ac1e7fb867a16939688e7b0", "title": "Wolverine", "artists": "(ref. <PERSON>) (prod. 454, <PERSON><PERSON>, & <PERSON><PERSON>)", "length": "2:51", "ogFilename": "Wolverine -<PERSON> ruff-", "notes": "Apparently a reference track made for <PERSON>. Released officially on <PERSON>' album You Only Die 1nce. Leaked as a bonus for the \"Pure\" groupbuy.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/f24902a6856aca417bd390141c8fd22a", "key": "Wolverine", "description": "OG Filename: <PERSON> -<PERSON> ruff-\nApparently a reference track made for <PERSON>. Released officially on <PERSON>' album You Only Die 1nce. Leaked as a bonus for the \"Pure\" groupbuy.", "date": 17429472, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/40f8a9a45ac1e7fb867a16939688e7b0", "size": "3.67 MB", "duration": 171.08}]}, {"era": "The College Dropout", "tracks": [{"id": "generated_id_The_College_Dropout_9_0", "title": "<PERSON><PERSON><PERSON> - You Don't Know [V1]", "artists": "(feat. <PERSON>nye West & GLC)", "length": null, "ogFilename": null, "originalUrl": "https://pillowcase.su/f/48e805f85afd4844210103b23994e75d", "notes": "Has an alternate <PERSON><PERSON><PERSON> verse and unused G<PERSON> vocals.", "tags": ["Tagged"], "aliases": []}]}, {"era": "<PERSON><PERSON><PERSON>", "tracks": [{"id": "d99b9c7c06870f3eb8e6d679bbc4b9d6", "title": "Liquor [V10]", "artists": "(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE DEAN)", "length": "5:30", "ogFilename": "KW - Liquor - AS R1", "notes": "Has louder vocal mixing on <PERSON><PERSON><PERSON> compared to the version below alongside some alternate structure compared to release.", "tags": ["OG File", "Lossless"], "aliases": ["(Hold My Liquor)"], "type": "track", "originalUrl": "https://pillowcase.su/f/d8c3b2982eb70c3efd7bff5317151355", "key": "Liquor", "description": "OG Filename: KW - Liquor - AS R1\nHas louder vocal mixing on <PERSON><PERSON><PERSON> compared to the version below alongside some alternate structure compared to release.", "date": 17426880, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/d99b9c7c06870f3eb8e6d679bbc4b9d6", "size": "7.36 MB", "duration": 330.02}]}, {"era": "BULLY", "tracks": [{"id": "2bda262c673e03228a9d05c89b047ef0", "title": "LAST BREATH [V5]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "2:07", "ogFilename": null, "notes": "Features <PERSON><PERSON><PERSON> on the hook and some of the second verse with additional production during the hook. <PERSON> has AI vocals from <PERSON>.", "tags": ["Low Quality"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/f74412d3c198ae8f09516bbfaeb4201c", "key": "LAST BREATH", "description": "Features <PERSON><PERSON><PERSON> on the hook and some of the second verse with additional production during the hook. <PERSON> has AI vocals from <PERSON>.", "date": 17423424, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Low Quality", "rgb(255, 255, 255)", "rgb(231, 0, 0)"], "url": "https://api.pillowcase.su/api/download/2bda262c673e03228a9d05c89b047ef0", "size": "2.91 MB", "duration": 127.43}]}, {"era": "WW3", "tracks": [{"id": "b9d2ebabbc06a759206149d9befb1862", "title": "HIGHS AND LOWS 2", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:44", "ogFilename": null, "notes": "Mashup made by <PERSON>, combining the instrumental from the drumless version of \"World War 3\", with vocals from \"Highs and Lows\".", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/1d9e88fa17d6198c36b43214c5ae2cb8", "key": "HIGHS AND LOWS 2", "description": "Mashup made by <PERSON>, combining the instrumental from the drumless version of \"World War 3\", with vocals from \"Highs and Lows\".", "date": 17423424, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/b9d2ebabbc06a759206149d9befb1862", "size": "2.55 MB", "duration": 104.67}]}, {"era": "BULLY", "tracks": [{"id": "d9444e8af8aa65fcdb1657c8afd0efef", "title": "BEAUTY AND THE BEAST [V7]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:45", "ogFilename": "BEAUTY AND THE BEAST - Ye SP DRUMS 10.22.24", "notes": "Found in the November BULLY visual album copy.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/ac19743bdcdc04e213970c39a2e0c2a1", "key": "BEAUTY AND THE BEAST", "description": "OG Filename: BEAUTY AND THE BEAST - Ye SP DRUMS 10.22.24\nFound in the November BULLY visual album copy.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d9444e8af8aa65fcdb1657c8afd0efef", "size": "2.56 MB", "duration": 105.77}, {"id": "633d4d6a5da279c2037e169a984a4a75", "title": "BEAUTY AND THE BEAST [V8]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:47", "ogFilename": null, "notes": "Found in the December BULLY visual album copy. Drums are quiter than previous versions, and has different echo.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/79cb856072b728e32a8158c820e8a5cc", "key": "BEAUTY AND THE BEAST", "description": "Found in the December BULLY visual album copy. Drums are quiter than previous versions, and has different echo.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/633d4d6a5da279c2037e169a984a4a75", "size": "2.58 MB", "duration": 107.08}, {"id": "58a1b9a59bd8e19d369a9c135df5f9c9", "title": "BULLY [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:01", "ogFilename": "Bully - Ye WHISPER 10.27.14", "notes": "Version found in the November BULLY visual album copy. The 14 in the filename is a mistake. <PERSON><PERSON> from The Simpsons.", "tags": [], "aliases": ["(SEROTONIN)"], "type": "track", "originalUrl": "https://pillowcase.su/f/243483087f887900d146680b4b8c1b79", "key": "BULLY", "description": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> WHISPER 10.27.14\nVersion found in the November BULLY visual album copy. The 14 in the filename is a mistake. <PERSON><PERSON> from The Simpsons.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/58a1b9a59bd8e19d369a9c135df5f9c9", "size": "2.81 MB", "duration": 121.32}, {"id": "214cac71798a39e70d80a7788b309803", "title": "SEROTONIN [V5]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:42", "ogFilename": null, "notes": "Found in in the December BULLY visual album copy posted by <PERSON> on Twitter - meaning it is dated on or before December 23rd, 2024. Compared to the November version, it is louder, has a different mix, uses a different AI model, has extra chorus and open, and adds echo over part of the sample chop.", "tags": [], "aliases": ["(BULLY)"], "type": "track", "originalUrl": "https://pillowcase.su/f/0d980e88d648c57fd672b45b772c8d89", "key": "SEROTONIN", "description": "Found in in the December BULLY visual album copy posted by <PERSON> on Twitter - meaning it is dated on or before December 23rd, 2024. Compared to the November version, it is louder, has a different mix, uses a different AI model, has extra chorus and open, and adds echo over part of the sample chop.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/214cac71798a39e70d80a7788b309803", "size": "3.47 MB", "duration": 162.47}, {"id": "715e052667c52ba341c94358790dfc57", "title": "CAN'T HURRY LOVE [V4]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:05", "ogFilename": "Can't Hurry Love - Ye OG Snr We 10.24.14", "notes": "Later version also found in the November BULLY visual album copy. The 14 in the filename is a mistake. Has different drums and mix compared to the version in the December visual album copy.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/df991910f8e9bcbff4a3f405fd588abf", "key": "CAN'T HURRY LOVE", "description": "OG Filename: Can't Hurry <PERSON> - Ye OG Snr We 10.24.14\nLater version also found in the November BULLY visual album copy. The 14 in the filename is a mistake. Has different drums and mix compared to the version in the December visual album copy.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/715e052667c52ba341c94358790dfc57", "size": "2.88 MB", "duration": 125.71}, {"id": "0db962ebb24d51ac2fb8f410f325a8b7", "title": "CAN'T HURRY LOVE [V5]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:53", "ogFilename": null, "notes": "Found in the December BULLY visual album copy posted by <PERSON> on Twitter - meaning it is dated on or before December 23rd, 2024.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/815a936a3974133a99cbad02be893c80", "key": "CAN'T HURRY LOVE", "description": "Found in the December BULLY visual album copy posted by <PERSON> on Twitter - meaning it is dated on or before December 23rd, 2024.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/0db962ebb24d51ac2fb8f410f325a8b7", "size": "2.69 MB", "duration": 113.54}, {"id": "e1b8a4551b2392c01f664cc755cc7f02", "title": "CIRCLES [V2]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:15", "ogFilename": "Circles - Ye 10.19.24 Drum Fills 2", "notes": "Found in the November BULLY visual album copy. Has bitcrush over the drums, and a shorter open compared to later versions - with an extra chorus after.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/fa84ec5b95ce2549e6e8ab1c14628750", "key": "CIRCLES", "description": "OG Filename: Circles - Ye 10.19.24 Drum Fills 2\nFound in the November BULLY visual album copy. Has bitcrush over the drums, and a shorter open compared to later versions - with an extra chorus after.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/e1b8a4551b2392c01f664cc755cc7f02", "size": "3.05 MB", "duration": 135.98}, {"id": "1a1b6903eb684da5e11496968d33b27a", "title": "CIRCLES [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:42", "ogFilename": null, "notes": "<PERSON> played during the Grammy afterparty on February 3rd, 2025. <PERSON><PERSON> \"Huit octobre 1971\" by Cortex. Found in full in the December BULLY visual album copy posted by Ye to Twitter - meaning it is dated on or before December 23rd, 2024. Adds bitcrush over the chorus vocals, and an echo at 0:46.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/47ad8d3bcdfde514c956866b0b1ba053", "key": "CIRCLES", "description": "<PERSON> played during the Grammy afterparty on February 3rd, 2025. <PERSON><PERSON> \"Huit octobre 1971\" by Cortex. Found in full in the December BULLY visual album copy posted by Ye to Twitter - meaning it is dated on or before December 23rd, 2024. Adds bitcrush over the chorus vocals, and an echo at 0:46.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/1a1b6903eb684da5e11496968d33b27a", "size": "3.47 MB", "duration": 162.47}, {"id": "e8b583473f1b4ef52618314ceaa1225d", "title": "HIGHS AND LOWS [V2]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:45", "ogFilename": "Highs and Lows - Ye New Verse 10.25.14", "notes": "Found in the November BULLY visual album copy. The 14 in the filename is a mistake. Uses a worse AI model compared to the December visual album copy version.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/98c31364f9a57276fbb7ff3c85d2f915", "key": "HIGHS AND LOWS", "description": "OG Filename: Highs and Lows - Ye New Verse 10.25.14\nFound in the November BULLY visual album copy. The 14 in the filename is a mistake. Uses a worse AI model compared to the December visual album copy version.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/e8b583473f1b4ef52618314ceaa1225d", "size": "2.56 MB", "duration": 105.41}, {"id": "5cb68b416c292c5eb2c1a8947e7be82d", "title": "HIGHS AND LOWS [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:48", "ogFilename": null, "notes": "Played by <PERSON><PERSON><PERSON> during an alleged BULLY event in Switzerland. Vocals are <PERSON><PERSON> <PERSON><PERSON> & interpolates \"soleil soleil\" by <PERSON><PERSON>. Original snippet leaked January 4th, 2025. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024. Adds a crackle to the intro, and the beat is louder.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/75d9f2429d17f543233cc7841a3421d6", "key": "HIGHS AND LOWS", "description": "Played by <PERSON><PERSON><PERSON> during an alleged BULLY event in Switzerland. Vocals are <PERSON><PERSON> <PERSON><PERSON> & interpolates \"soleil soleil\" by <PERSON><PERSON>. Original snippet leaked January 4th, 2025. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024. Adds a crackle to the intro, and the beat is louder.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5cb68b416c292c5eb2c1a8947e7be82d", "size": "2.61 MB", "duration": 108.93}, {"id": "e16ab97d3b78d34c972fb22b4073ddb2", "title": "LAST BREATH [V4]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:05", "ogFilename": null, "notes": "<PERSON> played at the Grammys afterparty on February 3rd, 2025. <PERSON> <PERSON> vocals in Spanish, and samples \"Bésame Mama\" by <PERSON><PERSON><PERSON>. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024. Beat now builds up to a drop. Some parts were rerecorded, with some vocals and drums from the November version removed.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/4e4edd164aea6b2e4ccc0ad6c59ecc32", "key": "LAST BREATH", "description": "<PERSON> played at the Grammys afterparty on February 3rd, 2025. <PERSON> <PERSON> vocals in Spanish, and samples \"Bésame Mama\" by <PERSON><PERSON><PERSON>. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024. Beat now builds up to a drop. Some parts were rerecorded, with some vocals and drums from the November version removed.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/e16ab97d3b78d34c972fb22b4073ddb2", "size": "2.88 MB", "duration": 125.68}, {"id": "9475b5862b5c61cac8ee818ee7bab8e0", "title": "MELROSE [V8]", "artists": "(feat. <PERSON>ign & <PERSON><PERSON><PERSON>) (prod. TheLabCook & Ojivolta)", "length": "3:41", "ogFilename": null, "notes": "BULLY-era verison of \"Melrose\" found in the December BULLY visual album copy posted by <PERSON> to his Twitter. Has new vocals from <PERSON>ign, and <PERSON> vocals. Dated on or before December 23rd, 2024.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/f18f36d717241d92562c43db2d7a024e", "key": "MELROSE", "description": "BULLY-era verison of \"Melrose\" found in the December BULLY visual album copy posted by <PERSON> to his Twitter. Has new vocals from <PERSON>ign, and <PERSON> vocals. Dated on or before December 23rd, 2024.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/9475b5862b5c61cac8ee818ee7bab8e0", "size": "4.41 MB", "duration": 221.12}, {"id": "1053da2de9b166a2169c1c18baf24491", "title": "PREACHER MAN [V7]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "3:01", "ogFilename": null, "notes": "Updated version with slight lyric changes and new production. Played at the Grammys afterparty on February 3rd, 2025. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/5758d75b38ccc3bc07f08cdc5172d2c6", "key": "PREACHER MAN", "description": "Updated version with slight lyric changes and new production. Played at the Grammys afterparty on February 3rd, 2025. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/1053da2de9b166a2169c1c18baf24491", "size": "3.78 MB", "duration": 181.62}, {"id": "632727c611e45c1617b505990e947104", "title": "THIS ONE HERE [V7]", "artists": null, "length": "3:17", "ogFilename": "This One Here - YE VOX 10.22.24 OG TEMPO FILM MIX", "notes": "Found in the November BULLY visual album copy. Has different mixing compared to the December version but apart from that it's the same.", "tags": [], "aliases": ["(Time", "Showtime)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6013b57f1e1cee78f46d0169311d1814", "key": "THIS ONE HERE", "description": "OG Filename: This One Here - YE VOX 10.22.24 OG TEMPO FILM MIX\nFound in the November BULLY visual album copy. Has different mixing compared to the December version but apart from that it's the same.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/632727c611e45c1617b505990e947104", "size": "4.03 MB", "duration": 197.47}, {"id": "43feb14dec34d404678cc789abf25bf9", "title": "THIS ONE HERE [V8]", "artists": null, "length": "3:19", "ogFilename": null, "notes": "Removes the old instrumental in favor of one that samples \"Walk on the Wild Side\" by <PERSON>. <PERSON> over <PERSON>. Allegedly played at the private BULLY LP in Tokyo. Original snippets leaked January 23rd, 2025. The acapella (isolated using AI) was leaked after a different version was chosen to be GB'd instead of this one. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024.", "tags": [], "aliases": ["(Time", "Showtime)"], "type": "track", "originalUrl": "https://pillowcase.su/f/2401fd4fd56001044c2edfb4b24b3685", "key": "THIS ONE HERE", "description": "Removes the old instrumental in favor of one that samples \"Walk on the Wild Side\" by <PERSON>. <PERSON> over <PERSON>. Allegedly played at the private BULLY LP in Tokyo. Original snippets leaked January 23rd, 2025. The acapella (isolated using AI) was leaked after a different version was chosen to be GB'd instead of this one. Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/43feb14dec34d404678cc789abf25bf9", "size": "4.06 MB", "duration": 199.39}, {"id": "30d89263aca7fc72318c70c317550a87", "title": "WHITE LINES [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:13", "ogFilename": "WHITE LINES - Ye rather tell 2 10.25.14", "notes": "Found in the November BULLY visual album copy. The 14 in the filename is a mistake.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/d0c26def7cd7fabe806bbce963d091f8", "key": "WHITE LINES", "description": "OG Filename: <PERSON><PERSON><PERSON><PERSON> LINES - Ye rather tell 2 10.25.14\nFound in the November BULLY visual album copy. The 14 in the filename is a mistake.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/30d89263aca7fc72318c70c317550a87", "size": "3.01 MB", "duration": 133.68}, {"id": "13a03ba575092e2617b2d78b4947cfe5", "title": "WHITE LINES [V4]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:59", "ogFilename": null, "notes": "Played at a private BULLY listening party. Originally was thought to sample \"Close To You\" by <PERSON>, but it just samples the same sample, a live Stevie Wonder talkbox cover of \"Close To You\" by The Carpenters. Found in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024. <PERSON><PERSON> is looped.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/aa19e31aa40ad0f20a7421dd0d6c7428", "key": "WHITE LINES", "description": "Played at a private BULLY listening party. Originally was thought to sample \"Close To You\" by <PERSON>, but it just samples the same sample, a live Stevie Wonder talkbox cover of \"Close To You\" by The Carpenters. Found in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024. <PERSON><PERSON> is looped.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/13a03ba575092e2617b2d78b4947cfe5", "size": "3.74 MB", "duration": 179.08}, {"id": "d944503f1dd89481b59d3471ac918429", "title": "???", "artists": null, "length": "3:26", "ogFilename": null, "notes": "Song played during <PERSON>'s film premiere on February 23rd, 2025. Covers \"Vitamin C\" by <PERSON> (using AI), but switches the chorus for the one from \"Losing Your Mind\" by <PERSON><PERSON>, which itself interpolates \"Vitamin C\". Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024.", "tags": ["Lossless"], "aliases": ["(Losing Your Mind)"], "type": "track", "originalUrl": "https://pillowcase.su/f/13658f5ca17721c30739c4101e872031", "key": "???", "description": "Song played during <PERSON>'s film premiere on February 23rd, 2025. Covers \"Vitamin C\" by <PERSON> (using AI), but switches the chorus for the one from \"Losing Your Mind\" by <PERSON><PERSON>, which itself interpolates \"Vitamin C\". Found in full in the December BULLY visual album copy posted by <PERSON> to Twitter - meaning it is dated on or before December 23rd, 2024.", "date": 17422560, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/d944503f1dd89481b59d3471ac918429", "size": "4.18 MB", "duration": 206.63}]}, {"era": "JESUS IS LORD", "tracks": [{"id": "77327dde55aa94631de69fa446eb9255", "title": "Glory [V1]", "artists": "(ref. <PERSON>) (feat. Sunday Service Choir)", "length": "2:15", "ogFilename": "Glory - 01.09.20 <PERSON><PERSON><PERSON>", "notes": "KayCyy reference track. Has unheard Sunday Service Choir vocals. Interpolates the Protestant version of \"Our Father\". Snippet originally leaked February 9th, 2025, with the full thing leaking March 16th, 2025.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>'s Glory", "<PERSON><PERSON>", "South Carolina)"], "type": "track", "originalUrl": "https://pillowcase.su/f/3edacd2e90cd2741384d2fbe97d32c6a", "key": "Glory", "description": "OG Filename: Glory - 01.09.20 <PERSON><PERSON><PERSON> Ref\n<PERSON> reference track. Has unheard Sunday Service Choir vocals. Interpolates the Protestant version of \"Our Father\". Snippet originally leaked February 9th, 2025, with the full thing leaking March 16th, 2025.", "date": 17420832, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/77327dde55aa94631de69fa446eb9255", "size": "4.21 MB", "duration": 135.33}]}, {"era": "WW3", "tracks": [{"id": "6950a0154231a4a0fbd8e2bce76f348f", "title": "??? [V2]", "artists": "(prod. Quadwoofer & Sheffmade)", "length": "1:17", "ogFilename": null, "notes": "Snippet posted by <PERSON> himself on Twitter. Samples \"I Get High (On Your Memory)\" by <PERSON><PERSON>. <PERSON> talks about voting for <PERSON>, doing nitrous, and reading <PERSON><PERSON> before bed. According to <PERSON> himself, the vocals are not AI. Is untitled at this point. Seemingly identical to release, just in lower quality and has worse mixing.", "tags": ["Partial"], "aliases": ["(WORLD WAR 3", "WW3)"], "type": "track", "originalUrl": "https://pillowcase.su/f/a445cc1f7ce8b738a3d253f930073a21", "key": "???", "description": "Snippet posted by <PERSON> himself on Twitter. Samples \"I Get High (On Your Memory)\" by <PERSON><PERSON>. <PERSON> talks about voting for <PERSON>, doing nitrous, and reading <PERSON><PERSON> before bed. According to <PERSON> himself, the vocals are not AI. Is untitled at this point. Seemingly identical to release, just in lower quality and has worse mixing.", "date": 17419968, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/6950a0154231a4a0fbd8e2bce76f348f", "size": "2.1 MB", "duration": 77.01}, {"id": "f04df56e86de14d27e708b51b3b61e6b", "title": "WORLD WAR 3 [V3]", "artists": "(prod. Quadwoofer & Sheffmade)", "length": "2:01", "ogFilename": null, "notes": "Drumless version, posted by Ye to Twitter on March 15th, 2025. Cuts off early.", "tags": ["Partial", "Low Quality"], "aliases": ["(WW3)"], "type": "track", "originalUrl": "https://pillowcase.su/f/f405fe68cc0b32052f3bce34935001af", "key": "WORLD WAR 3", "description": "Drumless version, posted by Ye to Twitter on March 15th, 2025. Cuts off early.", "date": 17419968, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["Low Quality", "rgb(255, 255, 255)", "rgb(231, 0, 0)"], "url": "https://api.pillowcase.su/api/download/f04df56e86de14d27e708b51b3b61e6b", "size": "2.82 MB", "duration": 121.65}, {"id": "b2445b83f8006897707047d76bb823ec", "title": "Diddy - Lonely Roads Still Go To Sunshine", "artists": "(feat. <PERSON><PERSON><PERSON>, <PERSON>, Jasmine Williams & North West)", "length": "3:50", "ogFilename": null, "notes": "Song posted by <PERSON> to Twitter, notably in a 72p quality video. Features a prison phone call intro between <PERSON> and <PERSON><PERSON> with no actual <PERSON> verse. Unknown if <PERSON><PERSON><PERSON> produced it.", "tags": ["Low Quality"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/9528bd521181ee557d67e9726d81dc51", "key": "Lonely Roads Still Go To Sunshine", "description": "Song posted by <PERSON> to Twitter, notably in a 72p quality video. Features a prison phone call intro between <PERSON> and <PERSON><PERSON> with no actual <PERSON> verse. Unknown if <PERSON><PERSON><PERSON> produced it.", "date": 17419968, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Low Quality", "rgb(255, 255, 255)", "rgb(231, 0, 0)"], "url": "https://api.pillowcase.su/api/download/b2445b83f8006897707047d76bb823ec", "size": "4.56 MB", "duration": 230.87}, {"id": "dd031d027808c3f963d9fbeca1b05601", "title": "FREE DIDDY", "artists": "(ref. <PERSON>)", "length": "0:16", "ogFilename": null, "notes": "<PERSON> solo song from 2025, with lyrics most likely being intended for <PERSON> to record over. Snippet leaked on March 14th, 2025.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/3217c4e7d6defbdd76b9350e2cf51f85", "key": "FREE DIDDY", "description": "<PERSON> solo song from 2025, with lyrics most likely being intended for <PERSON> to record over. Snippet leaked on March 14th, 2025.", "date": 17419104, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/dd031d027808c3f963d9fbeca1b05601", "size": "1.13 MB", "duration": 16.17}]}, {"era": "SWISH", "tracks": [{"id": "2dc4466e08fdb5239546ec7d85b3f7f1", "title": "Come and Go [V2]", "artists": "(ref. <PERSON>)", "length": "3:03", "ogFilename": null, "notes": "Version with reference vocals from <PERSON><PERSON>. <PERSON><PERSON> would reuse this take on the 2018 Rambo Savage song \"That's On You\". Acapella leaked March 13th, 2025.", "tags": ["Partial"], "aliases": ["(That's On You)"], "type": "track", "originalUrl": "https://pillowcase.su/f/083f917eeb8351cb5f9c99e3010142af", "key": "Come and Go", "description": "Version with reference vocals from <PERSON><PERSON>. <PERSON><PERSON> would reuse this take on the 2018 Rambo Savage song \"That's On You\". Acapella leaked March 13th, 2025.", "date": 17418240, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2dc4466e08fdb5239546ec7d85b3f7f1", "size": "3.15 MB", "duration": 183.43}]}, {"era": "So Help Me God", "tracks": [{"id": "787f7d5bda3fb6ee59abe0ea616ac1ab", "title": "That's On You [V1]", "artists": "(prod. <PERSON>)", "length": "2:45", "ogFilename": null, "notes": "2014 - 2015 version with more vocals and a Charlie Heat beat. Original snippet leaked April 17th, 2024. Leaked as a bonus for a Soakbuy.", "tags": [], "aliases": ["(Come and Go)"], "type": "track", "originalUrl": "https://pillowcase.su/f/190656f6575c60182b8a2006ce3905db", "key": "That's On You", "description": "2014 - 2015 version with more vocals and a Charlie Heat beat. Original snippet leaked April 17th, 2024. Leaked as a bonus for a Soakbuy.", "date": 17415648, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/787f7d5bda3fb6ee59abe0ea616ac1ab", "size": "5.06 MB", "duration": 165.54}]}, {"era": "808s & Heartbreak", "tracks": [{"id": "4f1b0d4d57da71fa32f7849a270fd4cc", "title": "Frozen Winter", "artists": "(prod. <PERSON><PERSON><PERSON>, No I.D. & <PERSON>)", "length": "2:44", "ogFilename": "Frozen Winter.1 RUFF", "notes": "Early version of \"Coldest Winter\" with alternate lines, including punch-ins where <PERSON><PERSON><PERSON> changes the \"coldest\" in \"coldest winter\" to \"frozen\", for some reason. Otherwise similar production-wise to release. Leaked after a groupbuy.", "tags": ["OG File", "Lossless"], "aliases": ["(Coldest Winter)"], "type": "track", "originalUrl": "https://pillowcase.su/f/ff5a1e089b94d4dc62c2010cfb747601", "key": "Frozen Winter", "description": "OG Filename: Frozen Winter.1 RUFF\nEarly version of \"Coldest Winter\" with alternate lines, including punch-ins where <PERSON><PERSON><PERSON> changes the \"coldest\" in \"coldest winter\" to \"frozen\", for some reason. Otherwise similar production-wise to release. Leaked after a groupbuy.", "date": 17413920, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/4f1b0d4d57da71fa32f7849a270fd4cc", "size": "3 MB", "duration": 164.78}, {"id": "8761f4ac41b390bf026a75f18aea83fb", "title": "Supernova [V2]", "artists": "(prod. <PERSON><PERSON><PERSON> & <PERSON>)", "length": "4:33", "ogFilename": "SUPERNOVA RUFF W Ye REF", "notes": "Version of \"Supernova\" that is further along than the previous version, with a slightly more fleshed-out hook idea. Has open near the end. Leaked after a groupbuy.", "tags": ["OG File", "Lossless"], "aliases": ["(Takin' Off)"], "type": "track", "originalUrl": "https://pillowcase.su/f/cb1e8e335f27bfa582f3065482828915", "key": "Supernova", "description": "OG Filename: SUPERNOVA RUFF W Ye REF\nVersion of \"Supernova\" that is further along than the previous version, with a slightly more fleshed-out hook idea. Has open near the end. Leaked after a groupbuy.", "date": 17413920, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/8761f4ac41b390bf026a75f18aea83fb", "size": "4.75 MB", "duration": 273.85}, {"id": "85c4216f18841158595ce471eafb57df", "title": "Tell Everybody That You Know [V2]", "artists": "(prod. <PERSON>, <PERSON><PERSON><PERSON> & Jeff <PERSON>)", "length": "4:41", "ogFilename": "TELL EVERYBODY RUFF WITH YE", "notes": "Early mumble version of \"See You In My Nightmares\", with just the hook idea present. Leaked after a groupbuy.", "tags": ["OG File", "Lossless"], "aliases": ["(See You In My Nightmares)"], "type": "track", "originalUrl": "https://pillowcase.su/f/2435c83ded140f9d43f2844451228810", "key": "Tell Everybody That You Know", "description": "OG Filename: TELL EVERYBODY RUFF WITH YE\nEarly mumble version of \"See You In My Nightmares\", with just the hook idea present. Leaked after a groupbuy.", "date": 17413920, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/85c4216f18841158595ce471eafb57df", "size": "4.86 MB", "duration": 281.22}, {"id": "1ecdcbca049636e30d462a036a0cebbd", "title": "Cookin' [V3]", "artists": "(feat. Mr<PERSON> <PERSON>) (prod. <PERSON><PERSON><PERSON>, Larry Gold & Plain Pat)", "length": "4:08", "ogFilename": "Cookin' V2.2 Ruff mix", "notes": "Early version of \"Wecome To Heartbreak\" with Mr<PERSON> replacing what would later be <PERSON>'s part. Missing the verse where <PERSON><PERSON><PERSON> actually says \"welcome to heartbreak\". Leaked after a groupbuy.", "tags": ["OG File", "Lossless"], "aliases": ["(Welcome To Heartbreak)"], "type": "track", "originalUrl": "https://pillowcase.su/f/33cc4722ef3165fa563cbe65f265babb", "key": "Cookin'", "description": "OG Filename: Cookin' V2.2 Ruff mix\nEarly version of \"Wecome To Heartbreak\" with Mr<PERSON> replacing what would later be <PERSON>'s part. Missing the verse where <PERSON><PERSON><PERSON> actually says \"welcome to heartbreak\". Leaked after a groupbuy.", "date": 17413920, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/1ecdcbca049636e30d462a036a0cebbd", "size": "4.34 MB", "duration": 248.75}]}, {"era": "SWISH", "tracks": [{"id": "0c2c976eba0554aab3c45b6c6ce8d5ab", "title": "Bad News [V1]", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)", "length": "4:02", "ogFilename": null, "notes": "Remix of \"Bad News\" intended to accompany the Soundcloud released \"Say You Will\" remix. Has alternate production from other versions, incorporating new violin.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/1f0a3c85567c80fe0c1eb593ff68e830", "key": "Bad News", "description": "Remix of \"Bad News\" intended to accompany the Soundcloud released \"Say You Will\" remix. Has alternate production from other versions, incorporating new violin.", "date": 17410464, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/0c2c976eba0554aab3c45b6c6ce8d5ab", "size": "4.1 MB", "duration": 242.99}]}, {"era": "DONDA [V1]", "tracks": [{"id": "37e2f3407c1f56769b7d539a24dfe4e4", "title": "Colors [V2]", "artists": "(feat. Sunday Service Choir)", "length": "1:52", "ogFilename": "Colors - 09.17.20SS Choir Record_3.5", "notes": "Sunday Service Choir cover of the hook.", "tags": ["OG File"], "aliases": ["(Grace and Mercy", "Grace N Mercy)"], "type": "track", "originalUrl": "https://pillowcase.su/f/a7e336a54b75a7ad6d00890ce414f0e6", "key": "Colors", "description": "OG Filename: Colors - 09.17.20SS Choir Record_3.5\nSunday Service Choir cover of the hook.", "date": 17409600, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/37e2f3407c1f56769b7d539a24dfe4e4", "size": "5.73 MB", "duration": 112.06}, {"id": "1e6cd256b07300c54114ae546b59a063", "title": "Stand United [V3]", "artists": "(ref. <PERSON><PERSON>) (feat. SA<PERSON>t JHN & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>, P<PERSON><PERSON> & BoogzDaBeast)", "length": "2:59", "ogFilename": null, "notes": "Cutdown version with punch-ins from Dem Jointz. Snippet leaked Sep 25th 2024. Stems rebounced in September 2023 to send to French Montana, but the original cutdown is from 2020 as Dem Jointz wasn't working with <PERSON> during this time. Leaked after a groupbuy. Buju's and SAINt JHN's stems aren't labeled properly, so the proper bounce likely doesn't sound like this. Probably the version from the December 2020 Hype Williams visual album.", "tags": ["Lossless"], "aliases": ["(Stand Up)"], "type": "track", "originalUrl": "https://pillowcase.su/f/c1b444d40719765449f7b5e88a6e8bbb", "key": "Stand United", "description": "Cutdown version with punch-ins from Dem Jointz. Snippet leaked Sep 25th 2024. Stems rebounced in September 2023 to send to French Montana, but the original cutdown is from 2020 as Dem Jointz wasn't working with <PERSON> during this time. Leaked after a groupbuy. Buju's and SAINt JHN's stems aren't labeled properly, so the proper bounce likely doesn't sound like this. Probably the version from the December 2020 Hype Williams visual album.", "date": 17409600, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/1e6cd256b07300c54114ae546b59a063", "size": "3.81 MB", "duration": 179.87}]}, {"era": "YEBU", "tracks": [{"id": "cbbf561180588cd6f49dfb138bf09f72", "title": "French Montana - Stand Up [V5]", "artists": "(feat. <PERSON><PERSON><PERSON>, SAINt JHN & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>, Pyr<PERSON> & BoogzDaBeast)", "length": "2:45", "ogFilename": "04 _STAND UP ye x french (1)", "notes": "Version of \"Stand United\" with French Montana punch-in lines. Still contains <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>H<PERSON> vocals however not their full verses. Also includes <PERSON><PERSON> Jointz's \"INCOMING\" tag.", "tags": ["OG File"], "aliases": ["(Stand United)"], "type": "track", "originalUrl": "https://pillowcase.su/f/939c79469b3f56a27f9cc84d4c3e0a98", "key": "Stand Up", "description": "OG Filename: 04 _STAND UP ye x french (1)\nVersion of \"Stand United\" with French Montana punch-in lines. Still contains <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> JHN vocals however not their full verses. Also includes Dem Jointz's \"INCOMING\" tag.", "date": 17409600, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/cbbf561180588cd6f49dfb138bf09f72", "size": "3.59 MB", "duration": 165.86}]}, {"era": "JESUS IS KING", "tracks": [{"id": "f94054366b25f16253d59f0dd7aca51f", "title": "Bound 3 [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Budgie Beats)", "length": "4:02", "ogFilename": "Bound3 10.09.19 Swizz and <PERSON>", "notes": "Solo Kanye freestyle version, featuring a lot more vocals from him than the version with features. Has a lot of \"listenable and fire\" mumble, according to Waterfalls. A video of <PERSON><PERSON><PERSON> rapping the song surfaced but it was muted, was also played by accident for a split second at one of the JESUS IS KING listening parties. Original snippets leaked July 30th, 2023, and February 7th & 25th, 2025. Leaked after an attempted Soakbuy, made unsuccessful by the leaking of the Donda version which is a rebounce.", "tags": ["OG File"], "aliases": ["(Bound)"], "type": "track", "originalUrl": "https://pillowcase.su/f/2b4bab6e3d8bfcd7f1882cef3f1415c2", "key": "Bound 3", "description": "OG Filename: Bound3 10.09.19 <PERSON>wi<PERSON> and <PERSON>f\n<PERSON> Kanye freestyle version, featuring a lot more vocals from him than the version with features. Has a lot of \"listenable and fire\" mumble, according to Waterfalls. A video of <PERSON><PERSON><PERSON> rapping the song surfaced but it was muted, was also played by accident for a split second at one of the JESUS IS KING listening parties. Original snippets leaked July 30th, 2023, and February 7th & 25th, 2025. Leaked after an attempted Soakbuy, made unsuccessful by the leaking of the Donda version which is a rebounce.", "date": 17408736, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/f94054366b25f16253d59f0dd7aca51f", "size": "4.18 MB", "duration": 242.4}]}, {"era": "DONDA [V1]", "tracks": [{"id": "e0f7d97cda6e7c88b13eb6f77b85bd2d", "title": "Bound [V7]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Budgie Beats)", "length": "4:02", "ogFilename": "KW Bound - 07.22.20 For Ye", "notes": "\"Bound 3 Chop\" was listed on a internal 07/20/20 album review document. Samples \"Perfect Peace\" by The Voices of Zion. Rebounce of the October 19th, 2019 version. Forceleaked during a Soakbuy for an earlier version.", "tags": ["OG File"], "aliases": ["(Bound 3)"], "type": "track", "originalUrl": "https://pillowcase.su/f/60e707035ee85a888bcf6d73009a5076", "key": "Bound", "description": "OG Filename: <PERSON><PERSON> Bound - 07.22.20 For Ye\n\"Bound 3 Chop\" was listed on a internal 07/20/20 album review document. Samples \"Perfect Peace\" by The Voices of Zion. Rebounce of the October 19th, 2019 version. Forceleaked during a Soakbuy for an earlier version.", "date": 17408736, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/e0f7d97cda6e7c88b13eb6f77b85bd2d", "size": "7.82 MB", "duration": 242.41}]}, {"era": "Yan<PERSON> [V1]", "tracks": [{"id": "f607be4c3aed20d154179704a65c4e2e", "title": "Hurricane [V16]", "artists": "(ref. 070 Shake) (feat. <PERSON><PERSON> & Ty Dolla $ign) (prod. <PERSON><PERSON><PERSON><PERSON>a<PERSON><PERSON>t, Nascent & RONNY J)", "length": "4:21", "ogFilename": "Hurricane 8.28.18 <PERSON> Vocals", "notes": "Filedate says it was recorded 8.28.18, however this doesn't really make sense with \"Hurricane's\" evolution, so it's safe to assume this was a misspelling of 9.28.18. Snippet of <PERSON>'s vocal take leaked February 22nd, 2025. Longer snippet of the full track leaked February 23rd, 2025, showing it includes <PERSON>'s infamous \"grandma couch\" verse.", "tags": ["Partial"], "aliases": ["(Don't Let Me Down", "Hurricanes", "80 Degrees)"], "type": "track", "originalUrl": "https://pillowcase.su/f/b03a4f73716777597525d6d45ab4b1c4", "key": "Hurricane", "description": "OG Filename: Hurricane 8.28.18 Shake Vocals\nFiledate says it was recorded 8.28.18, however this doesn't really make sense with \"Hurricane's\" evolution, so it's safe to assume this was a misspelling of 9.28.18. Snippet of <PERSON>'s vocal take leaked February 22nd, 2025. Longer snippet of the full track leaked February 23rd, 2025, showing it includes <PERSON>'s infamous \"grandma couch\" verse.", "date": 17406144, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f607be4c3aed20d154179704a65c4e2e", "size": "4.88 MB", "duration": 261.98}]}, {"era": "BULLY", "tracks": [{"id": "16f4ce7b6fdaf2318d4001401e7f7ae1", "title": "PREACHER MAN [V8]", "artists": "(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "3:01", "ogFilename": null, "notes": "Version with updated production and punch-ins from <PERSON> Moose.", "tags": ["Lossless"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/e33a57c0174dcd3efee4ad4071fad369", "key": "PREACHER MAN", "description": "Version with updated production and punch-ins from <PERSON> Moose.", "date": 17405280, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/16f4ce7b6fdaf2318d4001401e7f7ae1", "size": "3.77 MB", "duration": 181.43}]}, {"era": "Late Registration", "tracks": [{"id": "0182d0c59cf82e2881dd272c7224c003", "title": "Diamonds [V3]", "artists": null, "length": "4:04", "ogFilename": null, "notes": "An alternate vocal take can be heard in the bleed of the harp stem found in a ProTools session that leaked February 25th, 2025.", "tags": ["Partial", "Low Quality"], "aliases": ["(Born <PERSON>", "Diamonds From Sierra Leone)"], "type": "track", "originalUrl": "https://pillowcase.su/f/66c825a999022fb1c42d7177328a3a66", "key": "Diamonds", "description": "An alternate vocal take can be heard in the bleed of the harp stem found in a ProTools session that leaked February 25th, 2025.", "date": 17404416, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["Low Quality", "rgb(255, 255, 255)", "rgb(231, 0, 0)"], "url": "https://api.pillowcase.su/api/download/0182d0c59cf82e2881dd272c7224c003", "size": "6.95 MB", "duration": 244.43}, {"id": "56902204bd03846349404fd01eaad27f", "title": "Diamonds [V4]", "artists": "(prod. <PERSON><PERSON><PERSON>, <PERSON> & Devo <PERSON>teen)", "length": "4:06", "ogFilename": "DIAMONDSAD122_01", "notes": "Alternate mix for \"Diamonds From Sierra Leone\", bounced from a leaked ProTools session. Re-bounced March 21st, 2008 for an unknown reason.", "tags": ["Lossless"], "aliases": ["(Born <PERSON>", "Diamonds From Sierra Leone)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6efa1e87e52ffe78784e85f241729cf6", "key": "Diamonds", "description": "OG Filename: DIAMONDSAD122_01\nAlternate mix for \"Diamonds From Sierra Leone\", bounced from a leaked ProTools session. Re-bounced March 21st, 2008 for an unknown reason.", "date": 17404416, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/56902204bd03846349404fd01eaad27f", "size": "6.97 MB", "duration": 246.07}, {"id": "296446e88c0f3c933813cd1b089ea047", "title": "Diamonds [V5]", "artists": "(prod. <PERSON><PERSON><PERSON>, <PERSON> & Devo <PERSON>teen)", "length": "4:06", "ogFilename": "DIAMONDS mix2_03", "notes": "Alternate mix for \"Diamonds From Sierra Leone\", bounced from a leaked ProTools session. Re-bounced March 21st, 2008 for an unknown reason.", "tags": ["Lossless"], "aliases": ["(Born <PERSON>", "Diamonds From Sierra Leone)"], "type": "track", "originalUrl": "https://pillowcase.su/f/a58ab72338f9e78ec8280c74bef631d5", "key": "Diamonds", "description": "OG Filename: DIAMONDS mix2_03\nAlternate mix for \"Diamonds From Sierra Leone\", bounced from a leaked ProTools session. Re-bounced March 21st, 2008 for an unknown reason.", "date": 17404416, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/296446e88c0f3c933813cd1b089ea047", "size": "6.98 MB", "duration": 246.44}, {"id": "41a6be5c0228c483dc5dd28f390251e3", "title": "Diamonds [V6]", "artists": "(prod. <PERSON><PERSON><PERSON>, <PERSON> & Devo <PERSON>teen)", "length": "4:10", "ogFilename": "DIAMONDS mix2 CLN_05", "notes": "Same as previous version, but censored. Re-bounced March 21st, 2008 for an unknown reason.", "tags": ["Lossless"], "aliases": ["(Born <PERSON>", "Diamonds From Sierra Leone)"], "type": "track", "originalUrl": "https://pillowcase.su/f/19cd3b74c9253cfc7508be1f61e51efe", "key": "Diamonds", "description": "OG Filename: DIAMONDS mix2 CLN_05\nSame as previous version, but censored. Re-bounced March 21st, 2008 for an unknown reason.", "date": 17404416, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/41a6be5c0228c483dc5dd28f390251e3", "size": "7.05 MB", "duration": 250.95}, {"id": "e39f1bf6f4ccc199114f06c41916d018", "title": "Diamonds [V7]", "artists": "(prod. <PERSON><PERSON><PERSON>, <PERSON> & Devo <PERSON>teen)", "length": "4:09", "ogFilename": "DIAMONDS mix3_06", "notes": "Alternate mix for \"Diamonds From Sierra Leone\", bounced from a leaked ProTools session. Re-bounced March 21st, 2008 for an unknown reason.", "tags": ["Lossless"], "aliases": ["(Born <PERSON>", "Diamonds From Sierra Leone)"], "type": "track", "originalUrl": "https://pillowcase.su/f/f223424edf5bee0d5c700d5eb0b7e0b5", "key": "Diamonds", "description": "OG Filename: DIAMONDS mix3_06\nAlternate mix for \"Diamonds From Sierra Leone\", bounced from a leaked ProTools session. Re-bounced March 21st, 2008 for an unknown reason.", "date": 17404416, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/e39f1bf6f4ccc199114f06c41916d018", "size": "7.03 MB", "duration": 249.72}, {"id": "be451c1ee9a076236518d8eb613c52fe", "title": "Diamonds [V8]", "artists": "(prod. <PERSON><PERSON><PERSON>, <PERSON> & Devo <PERSON>teen)", "length": "4:04", "ogFilename": "DIAMONDS mix3 CLN_01", "notes": "Same as previous version, but censored. Re-bounced March 21st, 2008 for an unknown reason.", "tags": ["Lossless"], "aliases": ["(Born <PERSON>", "Diamonds From Sierra Leone)"], "type": "track", "originalUrl": "https://pillowcase.su/f/969783975a460dbc3209520ed18cba36", "key": "Diamonds", "description": "OG Filename: DIAMONDS mix3 CLN_01\nSame as previous version, but censored. Re-bounced March 21st, 2008 for an unknown reason.", "date": 17404416, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/be451c1ee9a076236518d8eb613c52fe", "size": "6.95 MB", "duration": 244.39}]}, {"era": "BULLY", "tracks": [{"id": "cd53c6926bf790b7ce434ca4b8a35ccf", "title": "The Game - Tina [V1]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "0:31", "ogFilename": null, "notes": "Video of The Game recording his vocals to an early version of the song posted to <PERSON><PERSON><PERSON>'s Instagram February 25th, 2025. Samples a clip of <PERSON>' (<PERSON>'s <PERSON>) walkout on the Jennifer Hudson Show.", "tags": ["Snippet", "Recording"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/937b8c9575d7c4c2b13967d581916f74", "key": "<PERSON>", "description": "Video of The Game recording his vocals to an early version of the song posted to <PERSON><PERSON><PERSON>'s Instagram February 25th, 2025. Samples a clip of <PERSON>' (<PERSON>'s <PERSON>) walkout on the Jennifer Hudson Show.", "date": 17404416, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["Recording", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "url": "https://api.pillowcase.su/api/download/cd53c6926bf790b7ce434ca4b8a35ccf", "size": "1.38 MB", "duration": 31.9}, {"id": "d367a0f2ccc7b7f1adf1e688bc748922", "title": "The Game - Tina [V2]", "artists": "(feat. <PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "1:12", "ogFilename": null, "notes": "High quality version posted to <PERSON><PERSON><PERSON>'s Instagram story shortly after the previous version. Has different drums compared to the recording snippet. Samples \"What's Love Got To Do With It\" by <PERSON>. Has vocals from <PERSON> of Dipset.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/e37206d85d4a40d2105dd28b95a6541d", "key": "<PERSON>", "description": "High quality version posted to <PERSON><PERSON><PERSON>'s Instagram story shortly after the previous version. Has different drums compared to the recording snippet. Samples \"What's Love Got To Do With It\" by <PERSON>. Has vocals from <PERSON> of Dipset.", "date": 17404416, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d367a0f2ccc7b7f1adf1e688bc748922", "size": "2.03 MB", "duration": 72.26}]}, {"era": "808s & Heartbreak", "tracks": [{"id": "e08422bc37398857368b37d4bac99ea2", "title": "Smoked Up [V1]", "artists": "(prod. Justice)", "length": "3:57", "ogFilename": null, "notes": "Initial freestyle. All freestyles were recorded September 25th, 2008, however the beat is dated September 23rd, 2008. Leaked after a successful Soakbuy.", "tags": ["Lossless"], "aliases": ["(Justice Beat)"], "type": "track", "originalUrl": "https://pillowcase.su/f/7e2ec17b63637963d3731bf5c88ef341", "key": "Smoked Up", "description": "Initial freestyle. All freestyles were recorded September 25th, 2008, however the beat is dated September 23rd, 2008. Leaked after a successful Soakbuy.", "date": 17403552, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/e08422bc37398857368b37d4bac99ea2", "size": "4.16 MB", "duration": 237.41}, {"id": "77a612498fa0c3c349d5289df11a945b", "title": "Smoked Up [V2]", "artists": "(prod. Justice)", "length": "3:57", "ogFilename": null, "notes": "Second freestyle. Has further along production. Leaked after a successful Soakbuy.", "tags": ["Lossless"], "aliases": ["(Justice Beat)"], "type": "track", "originalUrl": "https://pillowcase.su/f/9c0686ab53b5b4f339a5d4c9403d12a9", "key": "Smoked Up", "description": "Second freestyle. Has further along production. Leaked after a successful Soakbuy.", "date": 17403552, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/77a612498fa0c3c349d5289df11a945b", "size": "4.16 MB", "duration": 237.41}, {"id": "48d6fd52a2060c30d5730042a8c9097d", "title": "Smoked Up [V3]", "artists": "(prod. Justice)", "length": "3:58", "ogFilename": null, "notes": "Third freestyle. Leaked after a successful Soakbuy. <PERSON><PERSON><PERSON> mentions jews in this freestyle near the start.", "tags": ["Lossless"], "aliases": ["(Justice Beat)"], "type": "track", "originalUrl": "https://pillowcase.su/f/37046df0649a6b7d86d0abf9c747b9f7", "key": "Smoked Up", "description": "Third freestyle. Leaked after a successful Soakbuy. <PERSON><PERSON><PERSON> mentions jews in this freestyle near the start.", "date": 17403552, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/48d6fd52a2060c30d5730042a8c9097d", "size": "4.17 MB", "duration": 238.09}, {"id": "b739ed840ceb273fce149bccb36b3fe4", "title": "Smoked Up [V4]", "artists": "(prod. Justice)", "length": "3:58", "ogFilename": null, "notes": "Fourth freestyle. Initially thought to be from Good Ass Job, later found to originate from 808s & Heartbreak era. Has a hard electronic sound, stemming from the Justice production. Mostly mumble, at one point <PERSON><PERSON><PERSON> references <PERSON>. Leaked after a successful Soakbuy. This and all previous versions were intended to have additional production from <PERSON> and <PERSON> alongside the Justice produced beat, although those tracks are empty in the session.", "tags": ["Lossless"], "aliases": ["(Justice Beat)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6397f0de834c381350744cabfcfe2d8f", "key": "Smoked Up", "description": "Fourth freestyle. Initially thought to be from Good Ass Job, later found to originate from 808s & Heartbreak era. Has a hard electronic sound, stemming from the Justice production. Mostly mumble, at one point <PERSON><PERSON><PERSON> references <PERSON>. Leaked after a successful Soakbuy. This and all previous versions were intended to have additional production from <PERSON> and <PERSON> alongside the Justice produced beat, although those tracks are empty in the session.", "date": 17403552, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/b739ed840ceb273fce149bccb36b3fe4", "size": "4.18 MB", "duration": 238.24}, {"id": "fbdf0d2ff71f709d18a8161e59bafe96", "title": "Smoked Up [V5]", "artists": "(prod. Justice)", "length": "3:58", "ogFilename": null, "notes": "Latest version found in the ProTools sessions. Has the same freestyle as the fourth version however the vocals are mixed differently.", "tags": ["Lossless"], "aliases": ["(Justice Beat)"], "type": "track", "originalUrl": "https://pillowcase.su/f/ca1a11e77c20cedc1f28432e4f3df8b1", "key": "Smoked Up", "description": "Latest version found in the ProTools sessions. Has the same freestyle as the fourth version however the vocals are mixed differently.", "date": 17403552, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/fbdf0d2ff71f709d18a8161e59bafe96", "size": "4.18 MB", "duration": 238.24}]}, {"era": "BULLY", "tracks": [{"id": "acdec242fc7a8fb31151c50a9b9b1c22", "title": "???", "artists": null, "length": "0:29", "ogFilename": null, "notes": "Song played at <PERSON>'s film premiere February 23rd, 2025. Samples \"Sexy Boy\" by AIR.", "tags": ["Snippet", "Recording"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/57719948b9ba0a057af861b25f38383c", "key": "???", "description": "Song played at <PERSON>'s film premiere February 23rd, 2025. Samples \"Sexy Boy\" by AIR.", "date": 17402688, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["Recording", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "url": "https://api.pillowcase.su/api/download/acdec242fc7a8fb31151c50a9b9b1c22", "size": "1.35 MB", "duration": 29.96}]}, {"era": "Good Ass Job", "tracks": [{"id": "882157468667796bbf655bf809238c03", "title": "<PERSON> - Diamonds [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Maddscientist)", "length": "3:40", "ogFilename": null, "notes": "Rough demo track <PERSON><PERSON><PERSON> recorded for <PERSON> after he happened to hear her song \"Diamonds\" due to them both using the same studio. Recorded after 808s & Heartbreak.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/2ddef9d3097e3124dc7853611c754b46", "key": "Diamonds", "description": "Rough demo track <PERSON><PERSON><PERSON> recorded for <PERSON> after he happened to hear her song \"Diamonds\" due to them both using the same studio. Recorded after 808s & Heartbreak.", "date": 17401824, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/882157468667796bbf655bf809238c03", "size": "3.97 MB", "duration": 220.13}]}, {"era": "So Help Me God", "tracks": [{"id": "f000f70e25489552169a3b96d1468184", "title": "French Montana - Ass Shots [V3]", "artists": "(feat. <PERSON><PERSON><PERSON> & Cam'ron)", "length": "4:07", "ogFilename": "Ass Shots _ BBRef1", "notes": "More finished version. Edited file leaked sometime in Dec 2016, with an unedited but still not OG file leaking later in 2025.", "tags": [], "aliases": ["(Ass Shot", "<PERSON>)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6e7c5f5981776004a7569bfcc17752a3", "key": "Ass Shots", "description": "OG Filename: Ass Shots _ BBRef1\nMore finished version. Edited file leaked sometime in Dec 2016, with an unedited but still not OG file leaking later in 2025.", "date": 17401824, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f000f70e25489552169a3b96d1468184", "size": "6.37 MB", "duration": 247.33}]}, {"era": "SWISH", "tracks": [{"id": "27f1811875800f34faa2a1c6ca332253", "title": "Don't Jump [V4]", "artists": "(ref. <PERSON>)", "length": "1:47", "ogFilename": "<PERSON>zi - Don't Jump Ref", "notes": "<PERSON> reference track. Leaked after a Soakbuy.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/c9adf2b513f384e8ee5c463bdba825d3", "key": "Don't Jump", "description": "OG Filename: Uzi - Don't Jump Ref\nLil <PERSON> reference track. Leaked after a Soakbuy.", "date": 17401824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/27f1811875800f34faa2a1c6ca332253", "size": "1.93 MB", "duration": 107.49}, {"id": "27c1a61d16cbb7ac1edf6b39dce511f8", "title": "Don't Jump [V5]", "artists": "(ref. <PERSON><PERSON>)", "length": "1:01", "ogFilename": "Mez - Don't Jump Ref", "notes": "Mez reference track. Leaked after a Soakbuy.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/ecb057bf4147c6865f9324a1afd12f55", "key": "Don't Jump", "description": "OG Filename: Mez - Don't Jump Ref\n<PERSON> reference track. Leaked after a Soakbuy.", "date": 17401824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/27c1a61d16cbb7ac1edf6b39dce511f8", "size": "1.2 MB", "duration": 61.49}, {"id": "e6683351e4d0ed828e7740f335d3c0bf", "title": "No More Parties in LA [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "3:39", "ogFilename": "KW - No More Parties In LA Ref (9.2.15)", "notes": "Has the <PERSON><PERSON><PERSON> vocals up until the spray tan line, then they rest is open. Leaked as a bonus for a Soakbuy.", "tags": ["OG File"], "aliases": ["(No Parties In LA)"], "type": "track", "originalUrl": "https://pillowcase.su/f/8fe38dfa37f1eae8eeb45186a9307546", "key": "No More Parties in LA", "description": "OG Filename: KW - No More Parties In LA Ref (9.2.15)\nHas the <PERSON><PERSON><PERSON> vocals up until the spray tan line, then they rest is open. Leaked as a bonus for a Soakbuy.", "date": 17401824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/e6683351e4d0ed828e7740f335d3c0bf", "size": "3.73 MB", "duration": 219.63}, {"id": "b65b7ff245f6bb9490845d644afae9e7", "title": "No More Parties In LA [V5]", "artists": "(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "0:51", "ogFilename": "Uzi - No More Parties In LA Ref", "notes": "Very short reference done by <PERSON>. <PERSON><PERSON> mumble raps 2 lines and then asks for the autotune to be turned down. Leaked as a bonus for a Soakbuy.", "tags": ["OG File"], "aliases": ["(No Parties In LA)"], "type": "track", "originalUrl": "https://pillowcase.su/f/d05286abc18c3879c14a671b009d9f3f", "key": "No More Parties In LA", "description": "OG Filename: Uzi - No More Parties In LA Ref\nVery short reference done by <PERSON>. <PERSON><PERSON> mumble raps 2 lines and then asks for the autotune to be turned down. Leaked as a bonus for a Soakbuy.", "date": 17401824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b65b7ff245f6bb9490845d644afae9e7", "size": "1.04 MB", "duration": 51.54}, {"id": "bd48d2eb7a9dedb2fe0c9dc74c678bc8", "title": "No More Parties In LA [V6]", "artists": "(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "0:54", "ogFilename": "KW - No More Parties In LA Ref (10.22.15)", "notes": "Short Gizzle reference track. Leaked as a bonus for a Soakbuy.", "tags": ["OG File"], "aliases": ["(No Parties In LA)"], "type": "track", "originalUrl": "https://pillowcase.su/f/e8f81567a2bc60b9ff6cbf75fb8e90f5", "key": "No More Parties In LA", "description": "OG Filename: KW - No More Parties In LA Ref (10.22.15)\nShort Gizzle reference track. Leaked as a bonus for a Soakbuy.", "date": 17401824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/bd48d2eb7a9dedb2fe0c9dc74c678bc8", "size": "1.09 MB", "duration": 54.73}]}, {"era": "BULLY", "tracks": [{"id": "9018e6f8f05362ca6294ae2288aa8589", "title": "BEAUTY AND THE BEAST [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "1:47", "ogFilename": null, "notes": "Posted to the YZY Gap Archive Instagram Page. Very similar to the version that was officially released by Ye in October 2024, but with an alternate mix and no crowd in the intro.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/523abc2fa024ee51fc16699a2d1a0a6b", "key": "BEAUTY AND THE BEAST", "description": "Posted to the YZY Gap Archive Instagram Page. Very similar to the version that was officially released by Ye in October 2024, but with an alternate mix and no crowd in the intro.", "date": 17401824, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/9018e6f8f05362ca6294ae2288aa8589", "size": "2.59 MB", "duration": 107.21}]}, {"era": "Graduation", "tracks": [{"id": "dae09c1a383b23fe717f532b311418d2", "title": "Champion [V1]", "artists": "(prod. Kanye West & AllDay)", "length": "0:12", "ogFilename": null, "notes": "Original version of \"Champion\" which is said to have two completely different, finished verses. One of the verses' lyrics were posted on kanyeuniversitycity by <PERSON> himself. Most of the verse was reused from \"Ready To Go\", by Scratch. Snippets leaked November 21st, 2024, including one showing an alternate outro with additional vocals.Another snippet leaked February 21st, 2025. \"<PERSON><PERSON> won't you tell me how the fuck you feel Cause you annoying me with all that grill When you in the presence of royalty…kneel The Dolce was killing girls like <PERSON><PERSON> Thats why haters want to jack son for his ice cube's like <PERSON><PERSON><PERSON> And keep a lemon face like they sippin on ocean spray Cause we toast to say we made it to the poster Jay And send a couple models over this way With their disposable camera that want to pose with <PERSON> got me smelling sweeter than potpourri Fuck a float today I need a whole parade What am I supposed to say <PERSON><PERSON> at the end of the day when I hit the stage (Did you realize…that you were a champion in their eyes)\"", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/4b39fa11c03381d5c918382fc5adb0e9", "key": "Champion", "description": "Original version of \"Champion\" which is said to have two completely different, finished verses. One of the verses' lyrics were posted on kanyeuniversitycity by <PERSON> himself. Most of the verse was reused from \"Ready To Go\", by Scratch. Snippets leaked November 21st, 2024, including one showing an alternate outro with additional vocals.Another snippet leaked February 21st, 2025.\n\n\"<PERSON><PERSON> won't you tell me how the fuck you feel\nCause you annoying me with all that grill\nWhen you in the presence of royalty…kneel\nThe Dolce was killing girls like <PERSON><PERSON>\nThats why haters want to jack son for his ice cube's like <PERSON><PERSON><PERSON>\nAnd keep a lemon face like they sippin on ocean spray\nCause we toast to say we made it to the poster Jay\nAnd send a couple models over this way\nWith their disposable camera that want to pose with <PERSON> got me smelling sweeter than potpourri\nFuck a float today I need a whole parade\nWhat am I supposed to say\n<PERSON><PERSON> at the end of the day when I hit the stage\n(Did you realize…that you were a champion in their eyes)\"", "date": 17400960, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/dae09c1a383b23fe717f532b311418d2", "size": "2.25 MB", "duration": 12}, {"id": "9123793ae035818b5f81df8b0dedf711", "title": "Champion [V1]", "artists": "(prod. Kanye West & AllDay)", "length": "0:11", "ogFilename": null, "notes": "Original version of \"Champion\" which is said to have two completely different, finished verses. One of the verses' lyrics were posted on kanyeuniversitycity by <PERSON> himself. Most of the verse was reused from \"Ready To Go\", by Scratch. Snippets leaked November 21st, 2024, including one showing an alternate outro with additional vocals.Another snippet leaked February 21st, 2025. \"<PERSON><PERSON> won't you tell me how the fuck you feel Cause you annoying me with all that grill When you in the presence of royalty…kneel The Dolce was killing girls like <PERSON><PERSON> Thats why haters want to jack son for his ice cube's like <PERSON><PERSON><PERSON> And keep a lemon face like they sippin on ocean spray Cause we toast to say we made it to the poster Jay And send a couple models over this way With their disposable camera that want to pose with <PERSON> got me smelling sweeter than potpourri Fuck a float today I need a whole parade What am I supposed to say <PERSON><PERSON> at the end of the day when I hit the stage (Did you realize…that you were a champion in their eyes)\"", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/cd7b314091d096cc757dd9abe48131c1", "key": "Champion", "description": "Original version of \"Champion\" which is said to have two completely different, finished verses. One of the verses' lyrics were posted on kanyeuniversitycity by <PERSON> himself. Most of the verse was reused from \"Ready To Go\", by Scratch. Snippets leaked November 21st, 2024, including one showing an alternate outro with additional vocals.Another snippet leaked February 21st, 2025.\n\n\"<PERSON><PERSON> won't you tell me how the fuck you feel\nCause you annoying me with all that grill\nWhen you in the presence of royalty…kneel\nThe Dolce was killing girls like <PERSON><PERSON>\nThats why haters want to jack son for his ice cube's like <PERSON><PERSON><PERSON>\nAnd keep a lemon face like they sippin on ocean spray\nCause we toast to say we made it to the poster Jay\nAnd send a couple models over this way\nWith their disposable camera that want to pose with <PERSON> got me smelling sweeter than potpourri\nFuck a float today I need a whole parade\nWhat am I supposed to say\n<PERSON><PERSON> at the end of the day when I hit the stage\n(Did you realize…that you were a champion in their eyes)\"", "date": 17400960, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/9123793ae035818b5f81df8b0dedf711", "size": "2.24 MB", "duration": 11.5}, {"id": "d2fba654ee0c6ac1d4282ead22f09911", "title": "Champion [V1]", "artists": "(prod. Kanye West & AllDay)", "length": "0:09", "ogFilename": null, "notes": "Original version of \"Champion\" which is said to have two completely different, finished verses. One of the verses' lyrics were posted on kanyeuniversitycity by <PERSON> himself. Most of the verse was reused from \"Ready To Go\", by Scratch. Snippets leaked November 21st, 2024, including one showing an alternate outro with additional vocals.Another snippet leaked February 21st, 2025. \"<PERSON><PERSON> won't you tell me how the fuck you feel Cause you annoying me with all that grill When you in the presence of royalty…kneel The Dolce was killing girls like <PERSON><PERSON> Thats why haters want to jack son for his ice cube's like <PERSON><PERSON><PERSON> And keep a lemon face like they sippin on ocean spray Cause we toast to say we made it to the poster Jay And send a couple models over this way With their disposable camera that want to pose with <PERSON> got me smelling sweeter than potpourri Fuck a float today I need a whole parade What am I supposed to say <PERSON><PERSON> at the end of the day when I hit the stage (Did you realize…that you were a champion in their eyes)\"", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/31fb32e86a0b8a01d15f02814d8ac43e", "key": "Champion", "description": "Original version of \"Champion\" which is said to have two completely different, finished verses. One of the verses' lyrics were posted on kanyeuniversitycity by <PERSON> himself. Most of the verse was reused from \"Ready To Go\", by Scratch. Snippets leaked November 21st, 2024, including one showing an alternate outro with additional vocals.Another snippet leaked February 21st, 2025.\n\n\"<PERSON><PERSON> won't you tell me how the fuck you feel\nCause you annoying me with all that grill\nWhen you in the presence of royalty…kneel\nThe Dolce was killing girls like <PERSON><PERSON>\nThats why haters want to jack son for his ice cube's like <PERSON><PERSON><PERSON>\nAnd keep a lemon face like they sippin on ocean spray\nCause we toast to say we made it to the poster Jay\nAnd send a couple models over this way\nWith their disposable camera that want to pose with <PERSON> got me smelling sweeter than potpourri\nFuck a float today I need a whole parade\nWhat am I supposed to say\n<PERSON><PERSON> at the end of the day when I hit the stage\n(Did you realize…that you were a champion in their eyes)\"", "date": 17400960, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/d2fba654ee0c6ac1d4282ead22f09911", "size": "2.21 MB", "duration": 9.5}]}, {"era": "JESUS IS KING", "tracks": [{"id": "f29e36749c13ebcfedd52f719b083055", "title": "Ty Dolla $ign - Ego Death [V13]", "artists": "(feat. <PERSON><PERSON><PERSON>, FKA twigs & serpentwithfeet) (prod. Skrillex & BoogzDaBeast)", "length": "3:49", "ogFilename": null, "notes": "A bonus for the Uzi \"Don't Jump\" groupbuy was an April 20th version of \"Ego Death\" with stems. This however turned out to be a May 13th copy of the stems for an unknown reason.", "tags": ["Lossless"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/b1206d6a1e9a5e746fa2ce4b32d20041", "key": "Ego Death", "description": "A bonus for the Uzi \"Don't Jump\" groupbuy was an April 20th version of \"Ego Death\" with stems. This however turned out to be a May 13th copy of the stems for an unknown reason.", "date": 17400960, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/f29e36749c13ebcfedd52f719b083055", "size": "3.97 MB", "duration": 229.25}]}, {"era": "Donda 2", "tracks": [{"id": "4de7affef02419bf303f8fe8fe76d35d", "title": "<PERSON><PERSON> - City Underground [V1]", "artists": "(prod. <PERSON>l & REMED)", "length": "0:19", "ogFilename": "City underground prod adam...", "notes": "According to <PERSON><PERSON> this was originally a song she created in 2022 before it was given to Ty & Ye for VULTURES. LQ snippet played on an Instagram Live January 2nd, 2025. CDQ snippet and recording posted by <PERSON> on Feb 21st 2025.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/8e6f1f9de04cf4a5e614021a88319cca", "key": "City Underground", "description": "OG Filename: City underground prod adam...\nAccording to <PERSON><PERSON> this was originally a song she created in 2022 before it was given to Ty & Ye for VULTURES. LQ snippet played on an Instagram Live January 2nd, 2025. CDQ snippet and recording posted by <PERSON> on Feb 21st 2025.", "date": 17400960, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4de7affef02419bf303f8fe8fe76d35d", "size": "609 kB", "duration": 19.75}, {"id": "8debd8b325a584d4c5503bca6046639e", "title": "<PERSON><PERSON> - City Underground [V1]", "artists": "(prod. <PERSON>l & REMED)", "length": "0:09", "ogFilename": "City underground prod adam...", "notes": "According to <PERSON><PERSON> this was originally a song she created in 2022 before it was given to Ty & Ye for VULTURES. LQ snippet played on an Instagram Live January 2nd, 2025. CDQ snippet and recording posted by <PERSON> on Feb 21st 2025.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/12cecfd16fdb5719a4059eb474143a35", "key": "City Underground", "description": "OG Filename: City underground prod adam...\nAccording to <PERSON><PERSON> this was originally a song she created in 2022 before it was given to Ty & Ye for VULTURES. LQ snippet played on an Instagram Live January 2nd, 2025. CDQ snippet and recording posted by <PERSON> on Feb 21st 2025.", "date": 17400960, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/8debd8b325a584d4c5503bca6046639e", "size": "448 kB", "duration": 9.67}, {"id": "a50107ee3532714e2d7bea7561dd9a8c", "title": "<PERSON><PERSON> - City Underground [V1]", "artists": "(prod. <PERSON>l & REMED)", "length": "0:30", "ogFilename": "City underground prod adam...", "notes": "According to <PERSON><PERSON> this was originally a song she created in 2022 before it was given to Ty & Ye for VULTURES. LQ snippet played on an Instagram Live January 2nd, 2025. CDQ snippet and recording posted by <PERSON> on Feb 21st 2025.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/aeb32313ed567a934fc6bb7ba3438ea1", "key": "City Underground", "description": "OG Filename: City underground prod adam...\nAccording to <PERSON><PERSON> this was originally a song she created in 2022 before it was given to Ty & Ye for VULTURES. LQ snippet played on an Instagram Live January 2nd, 2025. CDQ snippet and recording posted by <PERSON> on Feb 21st 2025.", "date": 17400960, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/a50107ee3532714e2d7bea7561dd9a8c", "size": "777 kB", "duration": 30.22}]}, {"era": "BULLY", "tracks": [{"id": "b1eb0596c7bd5111cc0bca04aabdf16b", "title": "PREACHER MAN [V9]", "artists": "(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "3:02", "ogFilename": null, "notes": "Posted to the YZY Gap Archive Instagram page. Said to be the \"January Version\". Similar to the previous version, except with alternate mixing and an additional punch-in, as well as the sample being pictched down at 2:01.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/7fc070ae3260edea735829191977aa85", "key": "PREACHER MAN", "description": "Posted to the YZY Gap Archive Instagram page. Said to be the \"January Version\". Similar to the previous version, except with alternate mixing and an additional punch-in, as well as the sample being pictched down at 2:01.", "date": 17400960, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/b1eb0596c7bd5111cc0bca04aabdf16b", "size": "3.8 MB", "duration": 182.86}]}, {"era": "<PERSON><PERSON> [V2]", "tracks": [{"id": "5a2c14697545d6eae2764cc2b1c950ae", "title": "Ty Dolla $ign - Ego Death [V7]", "artists": "(feat. FKA twigs & serpentwithfeet) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:16", "ogFilename": "EGO DEATH skril YE VERSE", "notes": "Has alternate production and structure compared to release. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy. Has an open verse and more FKA twigs vocals.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/dcf9638399d3ba36e4fae75f60fe52f9", "key": "Ego Death", "description": "OG Filename: EGO DEATH skril YE VERSE\nHas alternate production and structure compared to release. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy. Has an open verse and more FKA twigs vocals.", "date": 17400096, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/5a2c14697545d6eae2764cc2b1c950ae", "size": "5.39 MB", "duration": 196.04}, {"id": "6199c0b17d08ea37df03b826cd8bb981", "title": "Ty Dolla $ign - Ego Death [V9]", "artists": "(feat. <PERSON><PERSON><PERSON>, FKA twigs & serpentwithfeet) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "4:41", "ogFilename": "Ego death 4.3.19", "notes": "Has an extra verse from <PERSON> and different production. Also includes a beatswitch meant to be a transition into the following track. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/6139ba93e5aa4fb5917d2dee7d4a9a04", "key": "Ego Death", "description": "OG Filename: Ego death  4.3.19\nHas an extra verse from <PERSON> and different production. Also includes a beatswitch meant to be a transition into the following track. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy.", "date": 17400096, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6199c0b17d08ea37df03b826cd8bb981", "size": "6.77 MB", "duration": 281.79}]}, {"era": "JESUS IS KING", "tracks": [{"id": "fbe2203cd5c479543f7e9839a54538d4", "title": "Ty Dolla $ign - Ego Death [V10]", "artists": "(feat. <PERSON><PERSON><PERSON>, FKA twigs & serpentwithfeet) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "4:06", "ogFilename": "EGO DEATH skril verson edit 2", "notes": "Unknown exactly when made, but was exported with iTunes ********, so on or before Feb 8th 2019. Has alternate production and structure compared to release. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/8954419e505ec94bee1358fcdb072b3d", "key": "Ego Death", "description": "OG Filename: EGO DEATH skril verson edit 2\nUnknown exactly when made, but was exported with iTunes ********, so on or before Feb 8th 2019. Has alternate production and structure compared to release. Leaked as a bonus for the Uzi \"Don't Jump\" groupbuy.", "date": 17400096, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/fbe2203cd5c479543f7e9839a54538d4", "size": "6.21 MB", "duration": 246.88}]}, {"era": "Thank God For Drugs", "tracks": [{"id": "fc4ba01b70a6761ec3ef5a6b587b781c", "title": "Hold My Liquor [V5]", "artists": "(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE DEAN)", "length": "0:03", "ogFilename": null, "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/c67e7b8263039d217d71a9d4a2b4e8aa", "key": "Hold My Liquor", "description": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "date": 17399232, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/fc4ba01b70a6761ec3ef5a6b587b781c", "size": "336 kB", "duration": 3.24}, {"id": "8ae96db0a512e90411efcd599205356f", "title": "Hold My Liquor [V5]", "artists": "(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE DEAN)", "length": "0:05", "ogFilename": null, "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/ad5f5b20ebd8baa9b8a1f7909206f9c6", "key": "Hold My Liquor", "description": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "date": 17399232, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/8ae96db0a512e90411efcd599205356f", "size": "368 kB", "duration": 5.2}, {"id": "564f473600a492db4bdfb8869e8a5d4e", "title": "Hold My Liquor [V5]", "artists": "(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE DEAN)", "length": "0:07", "ogFilename": null, "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/cde14502a3c41b668a94679be3153b97", "key": "Hold My Liquor", "description": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "date": 17399232, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/564f473600a492db4bdfb8869e8a5d4e", "size": "401 kB", "duration": 7.24}, {"id": "85cd7fb96374fe113902acff142dc1b3", "title": "Hold My Liquor [V5]", "artists": "(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE DEAN)", "length": "0:10", "ogFilename": null, "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/7a7ff039d9179ea67d6be8104f67a190", "key": "Hold My Liquor", "description": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "date": 17399232, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/85cd7fb96374fe113902acff142dc1b3", "size": "449 kB", "duration": 10.27}, {"id": "5d6590d555dfa8a659bd618bdfdd6936", "title": "Hold My Liquor [V5]", "artists": "(feat. Chief <PERSON><PERSON> & <PERSON>) (prod. <PERSON> & MIKE DEAN)", "length": "0:25", "ogFilename": null, "notes": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "tags": ["Snippet"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/94f177a497627a914192e3a03576803f", "key": "Hold My Liquor", "description": "A longer Chief <PERSON><PERSON> verse exists. The verse was likely taken from the solo reference track. Original snippet leaked May 31st, 2024. More snippets leaked February 19th, 2025. Edit of the Chief <PERSON><PERSON> verse is also attached, which leaked due to it being in early session files.", "date": 17399232, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5d6590d555dfa8a659bd618bdfdd6936", "size": "692 kB", "duration": 25.44}]}, {"era": "VULTURES 3", "tracks": [{"id": "2c33aee76e66003c534c891edd5eba28", "title": "Ty Dolla $ign - Wheels Fall Off [V4]", "artists": "(feat. <PERSON><PERSON><PERSON> & The Hooligans) (prod. <PERSON><PERSON><PERSON>)", "length": "0:22", "ogFilename": null, "notes": "Version previwed in a video posted to <PERSON>'s YouTube. The part before <PERSON>'s verse is slightly longer compared to release, and there are <PERSON> backing vocals on <PERSON>'s verse aswell. Other differences are currently unknown.", "tags": ["Snippet", "Recording"], "aliases": ["(Wheels)"], "type": "track", "originalUrl": "https://pillowcase.su/f/daf657391487b98fd3ae437fd63f6bc9", "key": "Wheels Fall Off", "description": "Version previwed in a video posted to <PERSON>'s YouTube. The part before <PERSON>'s verse is slightly longer compared to release, and there are <PERSON> backing vocals on <PERSON>'s verse aswell. Other differences are currently unknown.", "date": 17399232, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["Recording", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "url": "https://api.pillowcase.su/api/download/2c33aee76e66003c534c891edd5eba28", "size": "1.24 MB", "duration": 22.92}]}, {"era": "<PERSON><PERSON> [V2]", "tracks": [{"id": "92e153ba2f74f36bac847bce8bec47d5", "title": "Photo [V3]", "artists": "(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)", "length": "1:51", "ogFilename": null, "notes": "Consequence reference track for \"Photo\". Played on Consequence's Instagram live February 17th, 2025.", "tags": ["Partial", "Recording"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/ddb7ffe47d642a601409b4ab273e5e94", "key": "Photo", "description": "Consequence reference track for \"Photo\". Played on Consequence's Instagram live February 17th, 2025.", "date": 17397504, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["Recording", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "url": "https://api.pillowcase.su/api/download/92e153ba2f74f36bac847bce8bec47d5", "size": "4.04 MB", "duration": 111.49}]}, {"era": "BULLY", "tracks": [{"id": "acdcee20bfef5c057bc34f0b90c52cbb", "title": "BEAUTY AND THE BEAST [V9]", "artists": "(ref. Consequence)", "length": "1:32", "ogFilename": null, "notes": "After the song was released on yeezy.com, Consequence replied to a Tweet about the release saying that he, \"wrote the crazy verse to this shit!!! Might have to Leak\". Played partially on Consequence's Instagram live February 17th, 2025. File attached is edited to splice together the entire verse.", "tags": ["Partial", "Recording"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/d881abf312bd38e01e4479a6804c5dc0", "key": "BEAUTY AND THE BEAST", "description": "After the song was released on yeezy.com, Consequence replied to a Tweet about the release saying that he, \"wrote the crazy verse to this shit!!! Might have to Leak\". Played partially on Consequence's Instagram live February 17th, 2025. File attached is edited to splice together the entire verse.", "date": 17397504, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["Recording", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "url": "https://api.pillowcase.su/api/download/acdcee20bfef5c057bc34f0b90c52cbb", "size": "1.61 MB", "duration": 92.26}]}, {"era": "ye", "tracks": [{"id": "6fa861976a6923d369759c164af55fff", "title": "Let It Go [V3]", "artists": "(prod. Andrew Dawson & MIKE DEAN)", "length": "4:21", "ogFilename": "Let it Go 05.25.18 [AD and MD]", "notes": "Featured on an early leaked tracklist for ye. <PERSON> mumble <PERSON><PERSON><PERSON> singing vocals and 3 minutes of open. Leaked after a successful groupbuy.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/a18fb677e8f8b025a38e5ff585889802", "key": "Let It Go", "description": "OG Filename: Let it Go 05.25.18 [<PERSON> and MD]\nFeatured on an early leaked tracklist for ye. <PERSON> mumble <PERSON><PERSON><PERSON> singing vocals and 3 minutes of open. Leaked after a successful groupbuy.", "date": 17394912, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/6fa861976a6923d369759c164af55fff", "size": "4.62 MB", "duration": 261.26}]}, {"era": "Good Ass Job (2018)", "tracks": [{"id": "b0e6b3483fdcfdb425b64e487e7780b2", "title": "???", "artists": "(prod. ???)", "length": "1:39", "ogFilename": "Chance Ben 3", "notes": "GAJ 2018 Instrumental. Leaked as a bonus for the blind \"Let It Go\" groupbuy. Originates from Hitler era, as the file is dated 2017.", "tags": ["OG File"], "aliases": ["(Head Up)"], "type": "track", "originalUrl": "https://pillowcase.su/f/841f40d559145b80cd85203f1d79e870", "key": "???", "description": "OG Filename: <PERSON> Ben 3\nGAJ 2018 Instrumental. Leaked as a bonus for the blind \"Let It Go\" groupbuy. Originates from Hitler era, as the file is dated 2017.", "date": 17394912, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/b0e6b3483fdcfdb425b64e487e7780b2", "size": "2.31 MB", "duration": 99.29}]}, {"era": "JESUS IS KING", "tracks": [{"id": "d8274995c67fc59c986ea490a877dd00", "title": "Let It Go [V7]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "0:54", "ogFilename": "Let It Go - Lab 003 V3", "notes": "<PERSON><PERSON><PERSON> produced version of \"Let It Go\". Contains no vocals.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/7ab8a029938afaabbb4300f15db5b8ce", "key": "Let It Go", "description": "OG Filename: Let It Go - Lab 003 V3\nLabrinth produced version of \"Let It Go\". Contains no vocals.", "date": 17394912, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d8274995c67fc59c986ea490a877dd00", "size": "1.17 MB", "duration": 54.14}, {"id": "173a727e8040e9269addab1674427e9f", "title": "Let It Go [V8]", "artists": "(prod. <PERSON>)", "length": "3:31", "ogFilename": "Let it go FedeInsReplay 81.8bpm", "notes": "Alternate instrumental similar to the version below. Likely the version <PERSON> recorded over.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/48549ae271a8bc74978417e9f5fa1172", "key": "Let It Go", "description": "OG Filename: Let it go FedeInsReplay 81.8bpm\nAlternate instrumental similar to the version below. Likely the version <PERSON> recorded over.", "date": 17394912, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/173a727e8040e9269addab1674427e9f", "size": "3.68 MB", "duration": 211.27}, {"id": "c785d9460f97a045eb363bf47180d873", "title": "Let It Go [V9]", "artists": "(feat. <PERSON>) (prod. <PERSON>)", "length": "3:30", "ogFilename": "Let it go Fede Replay 83.40", "notes": "Version of \"Let It Go\" with <PERSON> singing the sample, most likely JESUS IS KING-era.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/15eeec894befcc37effc73af6f6150b1", "key": "Let It Go", "description": "OG Filename: Let it go Fede Replay 83.40\nVersion of \"Let It Go\" with <PERSON> singing the sample, most likely JESUS IS KING-era.", "date": 17394912, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/c785d9460f97a045eb363bf47180d873", "size": "3.67 MB", "duration": 210.1}]}, {"era": "VULTURES 2", "tracks": [{"id": "e7ac08d3b60c2e6a84934b4c1555d0c3", "title": "MY SOUL [V25]", "artists": "(feat. <PERSON> & <PERSON>) (prod. BoogzDaBeast & FnZ)", "length": "2:50", "ogFilename": "20240517 MY SOUL MOOSE ROUGH EDWIN", "notes": "Has a feature from <PERSON> Moose, and is censored.", "tags": ["OG File", "Lossless"], "aliases": ["(Faithful", "Fightin Fire", "Fighting Fires)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6a5f440067cd01d1286eecf79d79ae58", "key": "MY SOUL", "description": "OG Filename: 20240517 MY SOUL MOOSE ROUGH EDWIN\nHas a feature from Young Moose, and is censored.", "date": 17394912, "available": ["OG File", "rgb(255, 255, 255)", "rgb(42, 159, 102)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/e7ac08d3b60c2e6a84934b4c1555d0c3", "size": "3.18 MB", "duration": 170.23}]}, {"era": "VULTURES 3", "tracks": [{"id": "generated_id_VULTURES_3_48_0", "title": "Ty Dolla $ign - Wheels Fall Off [V3]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "Wheels Fall Off - EXTENDED - Dirty (FM)...", "notes": "Extended version that has the King Combs feature. Snippet posted Feb 13th 2025. Differences are unknown from the first released version, but they both have different filenames.", "tags": ["Snippet", "Recording"], "originalUrl": "https://pillowcase.su/f/cba420af97745788f32891562050d86f", "aliases": ["(Wheels)"]}]}, {"era": "So Help Me God", "tracks": [{"id": "06aa73a169b53b74c17b617cb4bc3aa0", "title": "???", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "0:11", "ogFilename": null, "notes": "In a 2014 interview, French Montana said that producer <PERSON><PERSON><PERSON> played him \"like 1,000 songs\" in the process of working on his album Mac & Cheese 4, and that <PERSON><PERSON><PERSON> - who was helping French with the album - \"picked like two beats\" to work on. <PERSON> <PERSON> is an idiot because we know that <PERSON><PERSON><PERSON> produced \"All Day\", \"Highlights\", and \"Never Tried This\", which is more than 2 songs. Snippet for another <PERSON><PERSON>us produced freestyle leaked February 10th, 2025.", "tags": ["Snippet"], "aliases": ["(I Know When I Know It)"], "type": "track", "originalUrl": "https://pillowcase.su/f/483efdf523f28e668f3c38e3137ddfc7", "key": "???", "description": "In a 2014 interview, French Montana said that producer <PERSON><PERSON><PERSON> played him \"like 1,000 songs\" in the process of working on his album Mac & Cheese 4, and that <PERSON><PERSON><PERSON> - who was helping French with the album - \"picked like two beats\" to work on. <PERSON> <PERSON> is an idiot because we know that <PERSON><PERSON><PERSON> produced \"All Day\", \"Highlights\", and \"Never Tried This\", which is more than 2 songs. Snippet for another <PERSON><PERSON>us produced freestyle leaked February 10th, 2025.", "date": 17391456, "available": ["Snippet", "rgb(255, 255, 255)", "rgb(153, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/06aa73a169b53b74c17b617cb4bc3aa0", "size": "2.59 MB", "duration": 11.08}]}]