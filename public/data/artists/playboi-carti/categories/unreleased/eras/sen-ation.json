{"id": "sen-ation", "name": "Sen$ation", "description": "A mixtape made in 2013, probably scrapped, but it might've been released.", "backgroundColor": "rgb(108, 0, 131)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17EZo1V9PhxjDHecWIC98y322VGd0jzF121q29pNFKME-qKIoaqUKrzGcG4XT6JkoXnmwCe4xUyS_-JP7I8qGMdcv4LUM8Z9swpTU41PvESg3lkZtYu7WiVDT9emDJd2njVoE5zyoZNIHrDGoN7Z-w?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "10kk-a-couple-thousand-for-the-hoe", "name": "⭐ 10kk a Couple Thousand$ For The Hoe$", "artists": [], "producers": ["$ir <PERSON>"], "notes": "OG Filename: 10 a Verse.L (1)\nOG Filename (Metadata): 10 a Vers\nWas originally uploaded to SoundCloud in 2013 but later taken down. OG File leaked on July 6, 2024.\n A song carti dropped and reposted 5 times. The Song Was Originally Titled 10 a Verse, <PERSON><PERSON> forgot to change the SoundCloud url after changing the name, so that's why it still shows up as 10 a Verse.", "length": "", "fileDate": 13781664, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/b323c76512ee42d2cbb20a88d25dd9a2/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b323c76512ee42d2cbb20a88d25dd9a2/play\", \"key\": \"10kk a Couple Thousand$ For The Hoe$\", \"title\": \"\\u2b50 10kk a Couple Thousand$ For The Hoe$\", \"artists\": \"(prod. $ir <PERSON>)\", \"aliases\": [\"10 a Verse\"], \"description\": \"OG Filename: 10 a Verse.L (1)\\nOG Filename (Metadata): 10 a Vers\\nWas originally uploaded to SoundCloud in 2013 but later taken down. OG File leaked on July 6, 2024.\\n A song carti dropped and reposted 5 times. The Song Was Originally Titled 10 a Verse, <PERSON><PERSON> forgot to change the SoundCloud url after changing the name, so that's why it still shows up as 10 a Verse.\", \"date\": 13781664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["10 a Verse"], "size": ""}, {"id": "faster", "name": "⭐ Faster", "artists": [], "producers": ["Naughty Gawd"], "notes": "Samples \"Find Your Way\" from the Final Fantasy VIII soundtrack.", "length": "", "fileDate": 14877216, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/c63314a52ed550c500d7c9ef297be6e1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c63314a52ed550c500d7c9ef297be6e1/play\", \"key\": \"Faster\", \"title\": \"\\u2b50 Faster\", \"artists\": \"(prod. Naughty Gawd)\", \"description\": \"<PERSON><PERSON> \\\"Find Your Way\\\" from the Final Fantasy VIII soundtrack.\", \"date\": 14877216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "for-my-set", "name": "For My Set", "artists": [], "producers": ["Indolympus"], "notes": "From 2013. Released and hosted by <PERSON><PERSON> in 2015.", "length": "", "fileDate": 14465952, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/952716242bfcfde6b45daaa05e945259/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/952716242bfcfde6b45daaa05e945259/play\", \"key\": \"For My Set\", \"title\": \"For My Set\", \"artists\": \"(prod. Indolympus)\", \"description\": \"From 2013. Released and hosted by terrorusa in 2015.\", \"date\": 14465952, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "h2o", "name": "H2O", "artists": [], "producers": [], "notes": "Uploaded to YouTube in the Summer of 2013 But later taken down.", "length": "2:26", "fileDate": 15402528, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/5d7a1c1d0e9614df62c020cbed810ee4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5d7a1c1d0e9614df62c020cbed810ee4/play\", \"key\": \"H2O\", \"title\": \"H2O\", \"description\": \"Uploaded to YouTube in the Summer of 2013 But later taken down.\", \"date\": 15402528, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ca697fcd8c1af9440f91fd96893c72e\", \"url\": \"https://api.pillowcase.su/api/download/5ca697fcd8c1af9440f91fd96893c72e\", \"size\": \"3.88 MB\", \"duration\": 146.64}", "aliases": [], "size": "3.88 MB"}, {"id": "outchea", "name": "⭐ Outchea", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON>\nWas released alongside a music video in 2013 but seemingly taken down everywhere. Released on Mar 28 9:30 pm and was the lead single for $ensation.", "length": "3:37", "fileDate": 13644288, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/35727d443a5708a4d7b1f36adfa4579b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/35727d443a5708a4d7b1f36adfa4579b/play\", \"key\": \"Outchea\", \"title\": \"\\u2b50 Outchea\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Outchea\\nWas released alongside a music video in 2013 but seemingly taken down everywhere. Released on Mar 28 9:30 pm and was the lead single for $ensation.\", \"date\": 13644288, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"59c5d044901b9d1160444c60c0f25140\", \"url\": \"https://api.pillowcase.su/api/download/59c5d044901b9d1160444c60c0f25140\", \"size\": \"3.27 MB\", \"duration\": 217.56}", "aliases": [], "size": "3.27 MB"}, {"id": "paper", "name": "Paper", "artists": [], "producers": ["019dexter"], "notes": "OG Filename: Carti - Paper\nReleased a single by <PERSON><PERSON><PERSON> in 2015, Not much information. Song Was Later Deleted & OG File Surfaced September 16, 2021.", "length": "3:16", "fileDate": 14459904, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/1c7c4ed06c0f6ae7a8eca4ede54e2a96/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1c7c4ed06c0f6ae7a8eca4ede54e2a96/play\", \"key\": \"Paper\", \"title\": \"Paper\", \"artists\": \"(prod. 019dexter)\", \"description\": \"OG Filename: Carti - Paper\\nReleased a single by <PERSON><PERSON><PERSON> in 2015, Not much information. Song Was Later Deleted & OG File Surfaced September 16, 2021.\", \"date\": 14459904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e631ce440d22740adc1ad9ad8d7586c3\", \"url\": \"https://api.pillowcase.su/api/download/e631ce440d22740adc1ad9ad8d7586c3\", \"size\": \"3.9 MB\", \"duration\": 196.08}", "aliases": [], "size": "3.9 MB"}, {"id": "so-cold", "name": "⭐ So Cold", "artists": ["A$AP Rocky"], "producers": ["J Endo Productions"], "notes": "OG Filename: socold\nFrom 2013. Metadata implies it is from a project titled \"Sen$ation\". Likely the first time <PERSON> and <PERSON><PERSON> collaborated. Has no <PERSON> verse only backing vocals.", "length": "2:04", "fileDate": 16728768, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/7d8d347636fda47d70eeee0aa2a39f0d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7d8d347636fda47d70eeee0aa2a39f0d/play\", \"key\": \"So Cold\", \"title\": \"\\u2b50 So Cold\", \"artists\": \"(feat. A$AP Rocky) (prod. J Endo Productions)\", \"description\": \"OG Filename: socold\\nFrom 2013. Metadata implies it is from a project titled \\\"Sen$ation\\\". Likely the first time <PERSON> and <PERSON><PERSON> collaborated. Has no Rocky verse only backing vocals.\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9c4f76ef0dbb6f3b6a8146d4d80e155e\", \"url\": \"https://api.pillowcase.su/api/download/9c4f76ef0dbb6f3b6a8146d4d80e155e\", \"size\": \"2.52 MB\", \"duration\": 124.01}", "aliases": [], "size": "2.52 MB"}, {"id": "terror-shit", "name": "⭐ Terror Shit", "artists": [], "producers": ["MelroseZee"], "notes": "From 2013.", "length": "4:18", "fileDate": 14413248, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/e1d50bc62718f2127977192183ccce03/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e1d50bc62718f2127977192183ccce03/play\", \"key\": \"Terror Shit\", \"title\": \"\\u2b50 Terror Shit\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"From 2013.\", \"date\": 14413248, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f2e9d6699381ac93fc7b3a4e4d82df83\", \"url\": \"https://api.pillowcase.su/api/download/f2e9d6699381ac93fc7b3a4e4d82df83\", \"size\": \"5.66 MB\", \"duration\": 258.24}", "aliases": [], "size": "5.66 MB"}, {"id": "this-ca-h", "name": "⭐ This Ca$h", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["MelroseZee"], "notes": "Originally released around May 31, 2013", "length": "3:46", "fileDate": "", "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/a64e857ff974a83a2a5b165e1ee98d5b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a64e857ff974a83a2a5b165e1ee98d5b/play\", \"key\": \"This Ca$h\", \"title\": \"\\u2b50 This Ca$h\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Cash\"], \"description\": \"Originally released around May 31, 2013\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"175e14fb6fda7cb11d34dc4e81bf9670\", \"url\": \"https://api.pillowcase.su/api/download/175e14fb6fda7cb11d34dc4e81bf9670\", \"size\": \"5.15 MB\", \"duration\": 226.59}", "aliases": ["Cash"], "size": "5.15 MB"}, {"id": "wrist-frozen", "name": "<PERSON><PERSON>", "artists": ["6$cooby", "<PERSON>"], "producers": ["454"], "notes": "No tags and isn't slowed. Leaked in HQ on May 9th, 2022.", "length": "3:46", "fileDate": 16520544, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/3df40dfdf1e10a0c0c12a48c89873990/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3df40dfdf1e10a0c0c12a48c89873990/play\", \"key\": \"Wrist Frozen\", \"title\": \"Wrist Frozen\", \"artists\": \"(feat. 6$<PERSON><PERSON><PERSON> & <PERSON>) (prod. 454)\", \"description\": \"No tags and isn't slowed. Leaked in HQ on May 9th, 2022.\", \"date\": 16520544, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"db6d2d6b0d5174757853d0a8c76a5040\", \"url\": \"https://api.pillowcase.su/api/download/db6d2d6b0d5174757853d0a8c76a5040\", \"size\": \"5.15 MB\", \"duration\": 226.39}", "aliases": [], "size": "5.15 MB"}, {"id": "y<PERSON><PERSON><PERSON><PERSON>", "name": "⭐ YUNGXANHOE", "artists": [], "producers": ["454", "Ethereal"], "notes": "OG Filename: Ps2 GLITCHES x XAN HOE\nMade in November - December 2013. Released as a single on Play<PERSON><PERSON>'s Soundcloud on January 8th, 2014. The same instrumental was also used by XXXTENTACION on his track \"Bloodstains\"", "length": "2:56", "fileDate": 16208640, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/334edba7ceee3cbf4410ea55a1eab5aa/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/334edba7ceee3cbf4410ea55a1eab5aa/play\", \"key\": \"YUNGXANHOE\", \"title\": \"\\u2b50 YUNGXANHOE\", \"artists\": \"(prod. 454 & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"XAN HOE\"], \"description\": \"OG Filename: Ps2 GLITCHES x XAN HOE\\nMade in November - December 2013. Released as a single on Play<PERSON><PERSON>'s Soundcloud on January 8th, 2014. The same instrumental was also used by XXXTENTACION on his track \\\"Bloodstains\\\"\", \"date\": 16208640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"69ef5f86cd2bbc2eadde5b62a1717271\", \"url\": \"https://api.pillowcase.su/api/download/69ef5f86cd2bbc2eadde5b62a1717271\", \"size\": \"2.94 MB\", \"duration\": 176.69}", "aliases": ["XAN HOE"], "size": "2.94 MB"}, {"id": "dope", "name": "<PERSON><PERSON> (Slowed)", "artists": [], "producers": ["<PERSON>"], "notes": "A slowed snippet of <PERSON><PERSON> was leaked as an outro to H2O. Unreleated to the other carti song called <PERSON>pe.", "length": "1:38", "fileDate": 15402528, "leakDate": "", "labels": ["CD Quality", "Partial"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/730f7d0bd29e51fffa660bf6e07df0dd/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/730f7d0bd29e51fffa660bf6e07df0dd/play\", \"key\": \"<PERSON><PERSON> (Slowed)\", \"title\": \"<PERSON><PERSON> (Slowed)\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"A slowed snippet of Dope was leaked as an outro to H2O. Unreleated to the other carti song called Dope.\", \"date\": 15402528, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c5608b3dc24ff2ccb068d877b5f41b4\", \"url\": \"https://api.pillowcase.su/api/download/2c5608b3dc24ff2ccb068d877b5f41b4\", \"size\": \"3.1 MB\", \"duration\": 98.11}", "aliases": [], "size": "3.1 MB"}, {"id": "dope-13", "name": "🥇 Dope ", "artists": [], "producers": ["<PERSON>"], "notes": "A slowed snippet of <PERSON><PERSON> was leaked as an outro to H2O, what means that a normal version of the song exists. Unreleated to the other carti song called <PERSON><PERSON>. Said to have been uploaded in full to youtube before being deleted/lost.", "length": "0:21", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/87baa208af442fc89b15a0f27a5c423b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/87baa208af442fc89b15a0f27a5c423b/play\", \"key\": \"Dope\", \"title\": \"\\ud83e\\udd47 Dope \", \"artists\": \"(prod. <PERSON>)\", \"description\": \"A slowed snippet of Dope was leaked as an outro to H2O, what means that a normal version of the song exists. Unreleated to the other carti song called Dope. Said to have been uploaded in full to youtube before being deleted/lost.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"41d7783a4cfb56c96dcf7f6486e821a6\", \"url\": \"https://api.pillowcase.su/api/download/41d7783a4cfb56c96dcf7f6486e821a6\", \"size\": \"1.87 MB\", \"duration\": 21.31}", "aliases": [], "size": "1.87 MB"}, {"id": "flexin", "name": "🏆 Flexin' [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["???"], "notes": "From December 2013. It was not produced by <PERSON><PERSON><PERSON><PERSON> (Raiden) <PERSON><PERSON> just took a random beat from someone.", "length": "0:19", "fileDate": 16724448, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "sen-ation", "originalUrl": "https://music.froste.lol/song/f500f9393ca09359e6729bb1bfe52c89/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f500f9393ca09359e6729bb1bfe52c89/play\", \"key\": \"Flexin'\", \"title\": \"\\ud83c\\udfc6 Flexin' [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. ???)\", \"description\": \"From December 2013. It was not produced by Nneardark (Raiden) <PERSON><PERSON> just took a random beat from someone.\", \"date\": 16724448, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"95a8d8d37aa1ef5d96e64f5037e1dd09\", \"url\": \"https://api.pillowcase.su/api/download/95a8d8d37aa1ef5d96e64f5037e1dd09\", \"size\": \"1.84 MB\", \"duration\": 19.66}", "aliases": [], "size": "1.84 MB"}]}