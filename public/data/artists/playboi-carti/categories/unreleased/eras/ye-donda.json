{"id": "ye-donda", "name": "Ye - DONDA", "description": "<PERSON><PERSON> was an opportunity used by <PERSON><PERSON> as a \"rollout\" to showcase his new, somewhat softer, and more relaxed sound used on his now-scrapped album \"Narcissist\". Based on snippets and leaks, <PERSON> not only helped <PERSON><PERSON> develop a foundation for the album, but he also gave inspiration and introduced him to the sounds he uses today. Despite the non-launch of Narcissist, <PERSON><PERSON> used <PERSON><PERSON> as a stepping stone to his new works of music.", "backgroundColor": "rgb(229, 15, 34)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FWFcGHbAwDU4_pBb0Phbmypg1EA7vgC78Ct0x7F8DFETU-nHxqG3oxfuEmIb8NxtTTjdQY4CMpeQpWSchAGspG9YrYoB64rrr97McYd4z66ozIKE66F7hs-nYm3Ae7jKlCG92K0RLKLffQrCoYUQ?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "made-it", "name": "Ye - Made It [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Ojivolta"], "notes": "OG Filename: Made_It_-_OxV_x_PBC_Edit\nFirst version of Made It featuring <PERSON><PERSON><PERSON>. It still has the old beat.", "length": "4:22", "fileDate": 16979328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/6f84609cf1a4fdffd92941b36d0a211d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f84609cf1a4fdffd92941b36d0a211d\", \"key\": \"Made It\", \"title\": \"Ye - Made It [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: Made_It_-_OxV_x_PBC_Edit\\nFirst version of Made It featuring <PERSON><PERSON><PERSON>. It still has the old beat.\", \"date\": 16979328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ec34b82e1ad49b6c29c7359ba543986e\", \"url\": \"https://api.pillowcase.su/api/download/ec34b82e1ad49b6c29c7359ba543986e\", \"size\": \"11.5 MB\", \"duration\": 262.69}", "aliases": [], "size": "11.5 MB"}, {"id": "made-it-2", "name": "✨ Ye - Made It [V4]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Ojivolta"], "notes": "OG Filename: Made It - 12.23.20 JA Classic Only\nVersion of \"Made It\" featuring <PERSON><PERSON><PERSON>, with Ojivolta production and <PERSON> vocals similar to their work on \"God Breathed\". Likely made in the same session as \"Off The Grid\". <PERSON><PERSON><PERSON>'s verse is 40 seconds long, and said to be \"excellent\". Song without <PERSON><PERSON><PERSON> on it leaked on September 10, 2023 and day after version with <PERSON><PERSON> leaked in full.", "length": "4:27", "fileDate": 16943904, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/20d26a99f1cbc32b0698c95cd3156f8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20d26a99f1cbc32b0698c95cd3156f8d\", \"key\": \"Made It\", \"title\": \"\\u2728 Ye - Made It [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Ojivolta)\", \"description\": \"OG Filename: Made It - 12.23.20 JA Classic Only\\nVersion of \\\"Made It\\\" featuring <PERSON><PERSON><PERSON>, with Ojivolta production and <PERSON> vocals similar to their work on \\\"God Breathed\\\". Likely made in the same session as \\\"Off The Grid\\\". <PERSON><PERSON><PERSON>'s verse is 40 seconds long, and said to be \\\"excellent\\\". Song without <PERSON><PERSON><PERSON> on it leaked on September 10, 2023 and day after version with <PERSON><PERSON> leaked in full.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"07de30d432a9150657b924498940fe55\", \"url\": \"https://api.pillowcase.su/api/download/07de30d432a9150657b924498940fe55\", \"size\": \"11.6 MB\", \"duration\": 267.34}", "aliases": [], "size": "11.6 MB"}, {"id": "made-it-3", "name": "Ye - Made It [V6]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["88-<PERSON>"], "notes": "A version of \"Made It\" with 88-Keys production is said to exist. Stems to the songs leaked on Oct 23, 2023.", "length": "4:27", "fileDate": "", "leakDate": "", "labels": ["Lossless", "Self-Bo<PERSON><PERSON>"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/b4c5bf92b37815cc346141067f9abadc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b4c5bf92b37815cc346141067f9abadc\", \"key\": \"Made It\", \"title\": \"Ye - Made It [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 88-Keys)\", \"description\": \"A version of \\\"Made It\\\" with 88-Keys production is said to exist. Stems to the songs leaked on Oct 23, 2023.\", \"date\": null, \"available\": [\"Self-Bounce\", \"rgb(255, 255, 255)\", \"rgb(153, 153, 153)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4599723d3a004439d8691e76291dfdfd\", \"url\": \"https://api.pillowcase.su/api/download/4599723d3a004439d8691e76291dfdfd\", \"size\": \"11.6 MB\", \"duration\": 267.4}", "aliases": [], "size": "11.6 MB"}, {"id": "off-the-grid", "name": "Ye - Off The Grid [V14]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc"], "notes": "OG Filename: Off The Grid - 12.07.20 Ye Vocal Only\nVersion with open for <PERSON><PERSON>.", "length": "1:33", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/557b73187a0405981975c5fe84482a99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/557b73187a0405981975c5fe84482a99\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc)\", \"description\": \"OG Filename: Off The Grid - 12.07.20 Ye Vocal Only\\nVersion with open for <PERSON><PERSON>.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2070c54056a244ce69e1366aae6f0442\", \"url\": \"https://api.pillowcase.su/api/download/2070c54056a244ce69e1366aae6f0442\", \"size\": \"8.82 MB\", \"duration\": 93.89}", "aliases": [], "size": "8.82 MB"}, {"id": "off-the-grid-5", "name": "Ye - Off The Grid [V16]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc", "<PERSON>", "<PERSON><PERSON>"], "notes": "An early version of \"Off The Grid\", produced by 30 Roc, David & Gili was posted in September of 2021, soon after <PERSON><PERSON> released. There appears to be beat differences, mainly in percussion. Unknown exactly when this is from, but it is from December at the earliest due to the Carti adlibs.", "length": "", "fileDate": "", "leakDate": "", "labels": ["High Quality", "Partial"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/479daa5f35e7e44978876c5c4d5a1be9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/479daa5f35e7e44978876c5c4d5a1be9/play\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V16]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 <PERSON><PERSON>, <PERSON>)\", \"description\": \"An early version of \\\"Off The Grid\\\", produced by 30 Roc, David & Gil<PERSON> was posted in September of 2021, soon after <PERSON><PERSON> released. There appears to be beat differences, mainly in percussion. Unknown exactly when this is from, but it is from December at the earliest due to the <PERSON><PERSON> adlibs.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "off-the-grid-6", "name": "Ye - Off The Grid [V17]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc", "Ojivolta"], "notes": "OG Filename: Off The Grid - 12.19.20 Add OxV Chords 1\nA version of Off The Grid with drums and more <PERSON><PERSON> adlibs than the realeased version. It's more developed than the Dec 22 version of Off The Grid even tho it's recorded 4 days earlier.", "length": "1:42", "fileDate": 17088192, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/60ad3795ed9975025426e5fc2d5b1033", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/60ad3795ed9975025426e5fc2d5b1033\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V17]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc, Ojivolta)\", \"description\": \"OG Filename: Off The Grid - 12.19.20 Add OxV Chords 1\\nA version of Off The Grid with drums and more <PERSON><PERSON> adlibs than the realeased version. It's more developed than the Dec 22 version of Off The Grid even tho it's recorded 4 days earlier.\", \"date\": 17088192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1a261eb8aacb508ddb14a4b060a4afd3\", \"url\": \"https://api.pillowcase.su/api/download/1a261eb8aacb508ddb14a4b060a4afd3\", \"size\": \"8.96 MB\", \"duration\": 102.92}", "aliases": [], "size": "8.96 MB"}, {"id": "off-the-grid-7", "name": "Ye - Off The Grid [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc", "Ojivolta"], "notes": "Drumless version of \"Off The Grid\" with half-censored <PERSON><PERSON><PERSON> vocals and very different mixing to later versions. Leaked September 19th 2021.", "length": "", "fileDate": 16320960, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/0d26af0dac27107ccfdfa84ddbc66ee2/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/0d26af0dac27107ccfdfa84ddbc66ee2/play\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc, Ojivolta)\", \"description\": \"Drumless version of \\\"Off The Grid\\\" with half-censored <PERSON><PERSON><PERSON> vocals and very different mixing to later versions. Leaked September 19th 2021.\", \"date\": 16320960, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "off-the-grid-8", "name": "Ye - Off The Grid [V19]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc", "Ojivolta"], "notes": "Around the time of WLR premiere, a fan snuck into <PERSON>'s house and recorded a part of <PERSON><PERSON>'s verse. This version, has different bass slide progression, and presumably a sparkly synth on top of the main lead. Also uncensored.", "length": "0:08", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/f9bb9ffd7bc1ed4b2bb88f5002d4fd2c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f9bb9ffd7bc1ed4b2bb88f5002d4fd2c\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V19]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc, Ojivolta)\", \"description\": \"Around the time of WLR premiere, a fan snuck into <PERSON>'s house and recorded a part of <PERSON><PERSON>'s verse. This version, has different bass slide progression, and presumably a sparkly synth on top of the main lead. Also uncensored.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"b3a8ca06a5d8eb886e5b54901a07baae\", \"url\": \"https://api.pillowcase.su/api/download/b3a8ca06a5d8eb886e5b54901a07baae\", \"size\": \"7.45 MB\", \"duration\": 8.39}", "aliases": [], "size": "7.45 MB"}, {"id": "junya", "name": "<PERSON> <PERSON> <PERSON><PERSON> (Remix) [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Dr. <PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON>e WIP Idea 1.02 G Maj 148\nShares a vocal sample prominently used in \"The Joy\", which was mistaken for <PERSON><PERSON> vocals somehow. Unknown when this was made, but possibly was after the album released, as waterfalls says it was meant to be put on the Stem Player as a \"bonus package\".", "length": "0:14", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/7fd5c256b4dd622754928585598aa8a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7fd5c256b4dd622754928585598aa8a5\", \"key\": \"<PERSON><PERSON> (Remix)\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> (Remix) [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Dr. <PERSON><PERSON>)\", \"aliases\": [\"Godspeed\", \"Junya Watanabe\"], \"description\": \"OG Filename: <PERSON><PERSON> Dr Dre WIP Idea 1.02 G Maj 148\\nShares a vocal sample prominently used in \\\"The Joy\\\", which was mistaken for <PERSON><PERSON> <PERSON> vocals somehow. Unknown when this was made, but possibly was after the album released, as waterfalls says it was meant to be put on the Stem Player as a \\\"bonus package\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cb3d4f3b006db18e948395c0e702bb69\", \"url\": \"https://api.pillowcase.su/api/download/cb3d4f3b006db18e948395c0e702bb69\", \"size\": \"7.54 MB\", \"duration\": 14.45}", "aliases": ["Godspeed", "<PERSON><PERSON>"], "size": "7.54 MB"}, {"id": "junya-10", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V7]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "Track 4 on the Donda whiteboard that was shown on a Consequence post on July 17th, 2021. Played at the Atlanta listening party for Donda on July 22, 2021", "length": "", "fileDate": 16269120, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/01fcf91c274aa63fab9dc063cd28b889/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/01fcf91c274aa63fab9dc063cd28b889/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"aliases\": [\"Atlanta LP Version\"], \"description\": \"Track 4 on the Donda whiteboard that was shown on a Consequence post on July 17th, 2021. Played at the Atlanta listening party for Donda on July 22, 2021\", \"date\": 16269120, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["Atlanta LP Version"], "size": ""}, {"id": "junya-11", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V8]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON> MIKE DEAN MIX 4 MORE KNOCK AND HIGHS\nFilename shown by <PERSON><PERSON>. Leaked alongside 3gb of kanye leaks.", "length": "2:23", "fileDate": 16972416, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://plwcse.top/f/18a2d5f68b36490b9b59641ebb7d1817", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://plwcse.top/f/18a2d5f68b36490b9b59641ebb7d1817\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"aliases\": [\"<PERSON><PERSON> W<PERSON>\", \"Godspeed\"], \"description\": \"OG Filename: JUNYA MIKE DEAN MIX 4 MORE KNOCK AND HIGHS\\nFilename shown by <PERSON><PERSON>. Leaked alongside 3gb of kanye leaks.\", \"date\": 16972416, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"43889bd2a645c03e928a4812611971e2\", \"url\": \"https://api.pillowcase.su/api/download/43889bd2a645c03e928a4812611971e2\", \"size\": \"2.3 MB\", \"duration\": 143.73}", "aliases": ["<PERSON><PERSON>", "Godspeed"], "size": "2.3 MB"}, {"id": "junya-12", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V13]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "Version of <PERSON><PERSON> with a completly different <PERSON><PERSON> verse and adlibs. Only snippet we have is through stems when <PERSON><PERSON> was recording his adlibs.", "length": "1:18", "fileDate": 17164224, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/5ad25bfecb18e9647b37ed51c8db674e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ad25bfecb18e9647b37ed51c8db674e\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V13]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"Version of <PERSON><PERSON> with a completly different <PERSON><PERSON> verse and adlibs. Only snippet we have is through stems when <PERSON><PERSON> was recording his adlibs.\", \"date\": 17164224, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea2ea2bbbefd8692e160a69c3ac57ee0\", \"url\": \"https://api.pillowcase.su/api/download/ea2ea2bbbefd8692e160a69c3ac57ee0\", \"size\": \"8.57 MB\", \"duration\": 78.6}", "aliases": [], "size": "8.57 MB"}, {"id": "junya-13", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V14]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Boogz"], "notes": "OG Filename: JUNYA FV BOOGZ+ v1\nVersion of \"Junya\" with additional production based around a church organ and piano.", "length": "2:21", "fileDate": 16960320, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/ee4e18e91fb923fd42ea1c18de5ffc32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ee4e18e91fb923fd42ea1c18de5ffc32\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Boogz)\", \"description\": \"OG Filename: JUNYA FV BOOGZ+ v1\\nVersion of \\\"Junya\\\" with additional production based around a church organ and piano.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1b115d51dfb6b338640484fab4b41bc4\", \"url\": \"https://api.pillowcase.su/api/download/1b115d51dfb6b338640484fab4b41bc4\", \"size\": \"9.58 MB\", \"duration\": 141.89}", "aliases": [], "size": "9.58 MB"}, {"id": "junya-14", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V15]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Boogz"], "notes": "OG Filename: JUNYA FV BOOGZ+ v1 nodrums\nSimilar to the previous version, but drumless.", "length": "2:21", "fileDate": 16960320, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/c5f8bb489253d2f783eb92b6d64f12d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c5f8bb489253d2f783eb92b6d64f12d2\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V15]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Boogz)\", \"description\": \"OG Filename: JUNYA FV BOOGZ+ v1 nodrums\\nSimilar to the previous version, but drumless.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8114c4526063fed73098d99a98bf4ee4\", \"url\": \"https://api.pillowcase.su/api/download/8114c4526063fed73098d99a98bf4ee4\", \"size\": \"9.58 MB\", \"duration\": 141.89}", "aliases": [], "size": "9.58 MB"}, {"id": "junya-15", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V16]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Boogz"], "notes": "OG Filename: JUNYA FV BOOGZ+ v2\nSimilar to the previous two versions, but with different drums.", "length": "2:22", "fileDate": 16960320, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/56b2c0cbca6b5eab88ec93f8e910f0e9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/56b2c0cbca6b5eab88ec93f8e910f0e9\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V16]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Boogz)\", \"description\": \"OG Filename: JUNYA FV BOOGZ+ v2\\nSimilar to the previous two versions, but with different drums.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6f108aeac44a0c7972ad2c7511ed222e\", \"url\": \"https://api.pillowcase.su/api/download/6f108aeac44a0c7972ad2c7511ed222e\", \"size\": \"9.6 MB\", \"duration\": 142.7}", "aliases": [], "size": "9.6 MB"}, {"id": "junya-16", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V17]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Boogz"], "notes": "OG Filename: JUNYA FV BOOGZ+ v3\nAnother version of \"<PERSON>ya\" with additional production based around a church organ.", "length": "2:22", "fileDate": 16960320, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/e0b87b5dd7172362851b9d330576ba21", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e0b87b5dd7172362851b9d330576ba21\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V17]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Boogz)\", \"description\": \"OG Filename: JUNYA FV BOOGZ+ v3\\nAnother version of \\\"Junya\\\" with additional production based around a church organ.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e16defe0abe3f2dcb04ca3a186784e75\", \"url\": \"https://api.pillowcase.su/api/download/e16defe0abe3f2dcb04ca3a186784e75\", \"size\": \"9.6 MB\", \"duration\": 142.7}", "aliases": [], "size": "9.6 MB"}, {"id": "junya-17", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Boogz"], "notes": "OG Filename: JUNYA FV BOOGZ+ v3 nodrums\nSimilar to previous version but with no drums.", "length": "2:22", "fileDate": 16960320, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/443698bece60d3d7f95f08bdb29fc81d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/443698bece60d3d7f95f08bdb29fc81d\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Boogz)\", \"description\": \"OG Filename: JUNYA FV BOOGZ+ v3 nodrums\\nSimilar to previous version but with no drums.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8237f1eb7e39d7683ead1a0f747dd956\", \"url\": \"https://api.pillowcase.su/api/download/8237f1eb7e39d7683ead1a0f747dd956\", \"size\": \"9.6 MB\", \"duration\": 142.71}", "aliases": [], "size": "9.6 MB"}, {"id": "junya-18", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V19]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Boogz"], "notes": "OG Filename: JUNYA +FV BOOGZ+ v4 Organ\nAnother version of \"<PERSON>ya\" with additional production based around a church organ.", "length": "2:22", "fileDate": 16960320, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/3bbe32075ce844cfdf22620f940a9fe9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3bbe32075ce844cfdf22620f940a9fe9\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V19]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas & Boogz)\", \"description\": \"OG Filename: JUNYA +FV BOOGZ+ v4 Organ\\nAnother version of \\\"Junya\\\" with additional production based around a church organ.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b60312e6691f652706ea4bdfe078cf63\", \"url\": \"https://api.pillowcase.su/api/download/b60312e6691f652706ea4bdfe078cf63\", \"size\": \"9.6 MB\", \"duration\": 142.71}", "aliases": [], "size": "9.6 MB"}, {"id": "junya-19", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V20]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: Junya V1\nVersion of \"Junya\" with alt production.", "length": "", "fileDate": 16949088, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/aee277143a134b1e17d3fcb0b648cfb9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/aee277143a134b1e17d3fcb0b648cfb9/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V20]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"OG Filename: Junya V1\\nVersion of \\\"Junya\\\" with alt production.\", \"date\": 16949088, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "junya-20", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V21]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: JUNYA V3 (CLEAN 808_808 Clap)\nVersion of \"Junya\" with alt production.", "length": "", "fileDate": 16949088, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/81bb5640aa7822af3f45f3766d71fe87/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/81bb5640aa7822af3f45f3766d71fe87/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V21]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"OG Filename: JUNYA V3 (CLEAN 808_808 Clap)\\nVersion of \\\"Junya\\\" with alt production.\", \"date\": 16949088, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "junya-21", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V22]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: JUNYA V4 (CLEAN 808_New Clap)\nVersion of \"Junya\" with alt production.", "length": "", "fileDate": 16949088, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/c6b7238235b25ade15706e1938dfbe82/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/c6b7238235b25ade15706e1938dfbe82/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V22]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"OG Filename: JUNYA V4 (CLEAN 808_New Clap)\\nVersion of \\\"Junya\\\" with alt production.\", \"date\": 16949088, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "junya-22", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V26]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "Played at the second listening party at Mercedes Benz Stadium. Has more vocals from <PERSON><PERSON> that were later used in the Pt 2 on the album.", "length": "", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/d07e504664592022e41bb730bc3d9457/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/d07e504664592022e41bb730bc3d9457/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V26]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"Played at the second listening party at Mercedes Benz Stadium. Has more vocals from <PERSON><PERSON> that were later used in the Pt 2 on the album.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "junya-23", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V27]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: Junya 1.0\nVersion of <PERSON><PERSON> with different mix.", "length": "", "fileDate": 16972416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/fad81690289fc340d56feaebd07a1e72/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/fad81690289fc340d56feaebd07a1e72/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V27]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas)\", \"description\": \"OG Filename: Junya 1.0\\nVersion of Junya with different mix.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "junya-24", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V28]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON>X TEST.03_03\nPlayed at the August 26, 2021 listening party. <PERSON><PERSON><PERSON> has more adlibs and his verse is cut shorter than previous versions. Includes an alternate line at the end of the song from <PERSON>, and has his backing vocals during the <PERSON><PERSON> verse removed. Seems to be the same as the release version but uncensored. CDQ version leaked on oct 14, 2023", "length": "2:27", "fileDate": 16972416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/64edf8c7f34767458ad6e0cea14b7f1a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/64edf8c7f34767458ad6e0cea14b7f1a\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V28]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas)\", \"aliases\": [\"Chicago LP Version\"], \"description\": \"OG Filename: <PERSON><PERSON> MIX TEST.03_03\\nPlayed at the August 26, 2021 listening party. <PERSON><PERSON><PERSON> has more adlibs and his verse is cut shorter than previous versions. Includes an alternate line at the end of the song from <PERSON>, and has his backing vocals during the <PERSON><PERSON> verse removed. Seems to be the same as the release version but uncensored. CDQ version leaked on oct 14, 2023\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"38457f69d635d3168ec1894097c7c0fb\", \"url\": \"https://api.pillowcase.su/api/download/38457f69d635d3168ec1894097c7c0fb\", \"size\": \"9.68 MB\", \"duration\": 147.64}", "aliases": ["Chicago LP Version"], "size": "9.68 MB"}, {"id": "junya-25", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V29]", "artists": ["<PERSON><PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON>ya MIX TEST.04_04\nJunya version with Ty Dolla $ign, later used as a Junya Pt.2, but was first planned to be the only version on donda", "length": "3:02", "fileDate": 16972416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/ae955e9b86a3b2fb2e16f22863c51572", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae955e9b86a3b2fb2e16f22863c51572\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V29]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> $ign) (prod. Digital Nas)\", \"aliases\": [\"Junya Pt.2\"], \"description\": \"OG Filename: Junya MIX TEST.04_04\\nJunya version with Ty Dolla $ign, later used as a Junya Pt.2, but was first planned to be the only version on donda\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b4888ce6e75913fb2e2ac2999046adc5\", \"url\": \"https://api.pillowcase.su/api/download/b4888ce6e75913fb2e2ac2999046adc5\", \"size\": \"10.2 MB\", \"duration\": 182.49}", "aliases": ["Junya Pt.2"], "size": "10.2 MB"}, {"id": "junya-26", "name": "<PERSON> <PERSON> <PERSON><PERSON> [V30]", "artists": ["<PERSON><PERSON><PERSON>", "Ty Dolla $ign"], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON>ya MIX TEST.05_05\nJunya version with Ty Dolla $ign, later used as a Junya Pt.2, but was first planned to be the only version of donda. Features slightly different mixing.", "length": "3:02", "fileDate": 16972416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/172ab001e04b3f5d596f8946f5783263", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/172ab001e04b3f5d596f8946f5783263\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON> [V30]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> $ign) (prod. Digital Nas)\", \"aliases\": [\"Junya Pt.2\"], \"description\": \"OG Filename: Junya MIX TEST.05_05\\nJunya version with Ty Dolla $ign, later used as a Junya Pt.2, but was first planned to be the only version of donda. Features slightly different mixing.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0f669f542e1fbecdef3a3d5ba417db80\", \"url\": \"https://api.pillowcase.su/api/download/0f669f542e1fbecdef3a3d5ba417db80\", \"size\": \"10.2 MB\", \"duration\": 182.49}", "aliases": ["Junya Pt.2"], "size": "10.2 MB"}, {"id": "off-the-grid-27", "name": "Ye - Off The Grid [V20]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc"], "notes": "OG Filename: Off the Grid 7.26.21- FYA MAN HK AND VERSE\nVersion of \"Off The Grid\" made July 26th, 2021. Features more <PERSON><PERSON> vocals, and has <PERSON><PERSON> on the hook of the song doing reference vocals. <PERSON><PERSON> himself has said that he did \"co-writing\" for the track. Snippet leaked March 23rd, 2023. Leaked in full on July 26, 2024.", "length": "2:41", "fileDate": 17219520, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://plwcse.top/f/215a10d82081869d6faba87e86e46aba", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://plwcse.top/f/215a10d82081869d6faba87e86e46aba\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V20]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc)\", \"description\": \"OG Filename: Off the Grid 7.26.21- FYA MAN HK AND VERSE\\nVersion of \\\"Off The Grid\\\" made July 26th, 2021. Features more <PERSON><PERSON> vocals, and has <PERSON><PERSON> on the hook of the song doing reference vocals. <PERSON><PERSON> himself has said that he did \\\"co-writing\\\" for the track. Snippet leaked March 23rd, 2023. Leaked in full on July 26, 2024.\", \"date\": 17219520, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4c767db5133905261dc83a3647f509da\", \"url\": \"https://api.pillowcase.su/api/download/4c767db5133905261dc83a3647f509da\", \"size\": \"2.59 MB\", \"duration\": 161.69}", "aliases": [], "size": "2.59 MB"}, {"id": "off-the-grid-28", "name": "Off The Grid [V21]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc"], "notes": "OG Filename: Off the Grid BABY KEEM version\nVersion of \"Off The Grid\" with <PERSON> doing a reference for <PERSON>'s verse. Unknown when this version was made, likely July or August. Doesn't have <PERSON><PERSON> verse, just adlibs. Leaked in full on Sep 26, 2023. OG file leaked on July 26, 2024.", "length": "1:43", "fileDate": 17219520, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://plwcse.top/f/2fb6beb44bfe4fdf66b89d36314f2c30", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://plwcse.top/f/2fb6beb44bfe4fdf66b89d36314f2c30\", \"key\": \"Off The Grid\", \"title\": \"Off The Grid [V21]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc)\", \"description\": \"OG Filename: Off the Grid BABY KEEM version\\nVersion of \\\"Off The Grid\\\" with <PERSON> doing a reference for <PERSON>'s verse. Unknown when this version was made, likely July or August. Doesn't have <PERSON><PERSON> verse, just adlibs. Leaked in full on Sep 26, 2023. OG file leaked on July 26, 2024.\", \"date\": 17219520, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"518b7d43ef341df20b7956f4f87a5cae\", \"url\": \"https://api.pillowcase.su/api/download/518b7d43ef341df20b7956f4f87a5cae\", \"size\": \"1.65 MB\", \"duration\": 103.16}", "aliases": [], "size": "1.65 MB"}, {"id": "off-the-grid-29", "name": "Off The Grid [V22]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc"], "notes": "Played at the rehearsal. Has rougher Ye hook and no Fivio Foreign feature or beat switch.", "length": "1:53", "fileDate": 16281216, "leakDate": "", "labels": ["Performance", "Snippet"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/2399f07025f6920d0a912862a9555b36", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2399f07025f6920d0a912862a9555b36\", \"key\": \"Off The Grid\", \"title\": \"Off The Grid [V22]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc)\", \"description\": \"Played at the rehearsal. Has rougher Ye hook and no Fivio Foreign feature or beat switch.\", \"date\": 16281216, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Performance\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"id\": \"60cee997f16f21e8cb88e316986c99b7\", \"url\": \"https://api.pillowcase.su/api/download/60cee997f16f21e8cb88e316986c99b7\", \"size\": \"915 kB\", \"duration\": 113.96}", "aliases": [], "size": "915 kB"}, {"id": "off-the-grid-30", "name": "Off The Grid [V29]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "Version of the song that includes <PERSON><PERSON><PERSON> adlibs but no <PERSON><PERSON> verse. Has a new intro, and is mostly drill production.", "length": "0:15", "fileDate": 16351200, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/f0e554705f5a8d2a6b10a8c34ffd63cc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0e554705f5a8d2a6b10a8c34ffd63cc\", \"key\": \"Off The Grid\", \"title\": \"Off The Grid [V29]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Fivio Foreign) (prod. 30 Roc, AyoAA & Ojivolta)\", \"description\": \"Version of the song that includes <PERSON><PERSON><PERSON> adlibs but no <PERSON><PERSON> verse. Has a new intro, and is mostly drill production.\", \"date\": 16351200, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d56aaea47a6f5de51594f432f5fafeac\", \"url\": \"https://api.pillowcase.su/api/download/d56aaea47a6f5de51594f432f5fafeac\", \"size\": \"247 kB\", \"duration\": 15.33}", "aliases": [], "size": "247 kB"}, {"id": "off-the-grid-31", "name": "Ye - Off The Grid [V30]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "Version of the song that includes <PERSON><PERSON><PERSON> adlibs but no <PERSON><PERSON> verse. Has a new intro, and is mostly drill production", "length": "", "fileDate": 16351200, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/3de8e6d79fa1d055b5c0e5d08f70844b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/3de8e6d79fa1d055b5c0e5d08f70844b/play\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V30]\", \"artists\": \"(feat. Playboi <PERSON> & Fivio Foreign) (prod. 30 Roc, AyoAA & Ojivolta)\", \"description\": \"Version of the song that includes <PERSON><PERSON><PERSON> adlibs but no <PERSON><PERSON> verse. Has a new intro, and is mostly drill production\", \"date\": 16351200, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "off-the-grid-32", "name": "Ye - Off The Grid [V31]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>", "<PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "Played at the second listening party at Mercedes Benz Stadium at August 5, 2021. <PERSON><PERSON>'s verse features a faster beat and the song lacks <PERSON>'s verse", "length": "", "fileDate": 16281216, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "http://music.froste.lol/song/d3e5e49cd97b4301a14469b35a50f209/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/d3e5e49cd97b4301a14469b35a50f209/play\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V31]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, Fivio Foreign & Justin Austin) (prod. 30 Roc, AyoAA & Ojivolta)\", \"description\": \"Played at the second listening party at Mercedes Benz Stadium at August 5, 2021. <PERSON><PERSON>'s verse features a faster beat and the song lacks <PERSON>'s verse\", \"date\": 16281216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "off-the-grid-33", "name": "Ye - Off The Grid [V33]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>", "<PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "Off the grid MIX TEST.02_02\nHas slighly different mix than OTG that was played on Aug 26, 2021", "length": "5:39", "fileDate": 16972416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/9301271e5400235d3eafec3651666eb1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9301271e5400235d3eafec3651666eb1\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V33]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, Fivio Foreign & Justin <PERSON>) (prod. 30 Roc, AyoAA & Ojivolta)\", \"description\": \"Off the grid MIX TEST.02_02\\nHas slighly different mix than OTG that was played on Aug 26, 2021\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e801b36fc1d6b19e9638e6884102809e\", \"url\": \"https://api.pillowcase.su/api/download/e801b36fc1d6b19e9638e6884102809e\", \"size\": \"12.7 MB\", \"duration\": 339.2}", "aliases": [], "size": "12.7 MB"}, {"id": "off-the-grid-34", "name": "Ye - Off The Grid [V35]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>", "<PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "OG Filename: Off the grid MIX TEST.04_04\nPlayed at the August 26, 2021 listening party. Includes slightly different production and clearer Ye vocals. <PERSON> has a new verse at the end of the song. Seems to be same as release version but all verses except <PERSON><PERSON>'s are uncensored. CDQ version leaked on Oct 14, 2023", "length": "5:39", "fileDate": 16972416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ye-donda", "originalUrl": "https://pillowcase.su/f/e17630789324a18bbeddd8d3135962d5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e17630789324a18bbeddd8d3135962d5\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V35]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON> Foreign & Justin <PERSON>) (prod. 30 Roc, AyoAA & Ojivolta)\", \"aliases\": [\"Chicago LP Version\"], \"description\": \"OG Filename: Off the grid MIX TEST.04_04\\nPlayed at the August 26, 2021 listening party. Includes slightly different production and clearer Ye vocals. <PERSON> has a new verse at the end of the song. Seems to be same as release version but all verses except <PERSON><PERSON>'s are uncensored. CDQ version leaked on Oct 14, 2023\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6d81e09a250643b406175b790a3930f0\", \"url\": \"https://api.pillowcase.su/api/download/6d81e09a250643b406175b790a3930f0\", \"size\": \"12.7 MB\", \"duration\": 339.2}", "aliases": ["Chicago LP Version"], "size": "12.7 MB"}, {"id": "off-the-grid-35", "name": "Ye - Off The Grid [V38]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>", "<PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "OG Filename: Off the grid MIX TEST.07_07\nPlayed at the Chicago Donda listening party. Includes slightly different production and clearer Ye vocals. <PERSON> has a new verse at the end of the song. Seems to be same as release version but uncensored. Original CDQ snippet leaked December 4th, 2022. CDQ mono leaked 1/13/24.", "length": "5:38", "fileDate": 17051040, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://music.froste.lol/song/8e0123dac4f7bd37a71d8d1dcb494fff/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8e0123dac4f7bd37a71d8d1dcb494fff/play\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V38]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, Fi<PERSON> Foreign & Justin <PERSON>) (prod. 30 Roc, AyoAA & Ojivolta)\", \"description\": \"OG Filename: Off the grid MIX TEST.07_07\\nPlayed at the Chicago Donda listening party. Includes slightly different production and clearer Ye vocals. <PERSON> has a new verse at the end of the song. Seems to be same as release version but uncensored. Original CDQ snippet leaked December 4th, 2022. CDQ mono leaked 1/13/24.\", \"date\": 17051040, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0b769903161b58b9b54a8884851909a8\", \"url\": \"https://api.pillowcase.su/api/download/0b769903161b58b9b54a8884851909a8\", \"size\": \"12.7 MB\", \"duration\": 338.9}", "aliases": [], "size": "12.7 MB"}, {"id": "off-the-grid-36", "name": "Ye - Off The Grid [V38]", "artists": ["<PERSON><PERSON><PERSON>", "Fivio <PERSON>", "<PERSON>"], "producers": ["30 Roc", "AyoAA", "Ojivolta"], "notes": "OG Filename: Off the grid MIX TEST.07_07\nPlayed at the Chicago Donda listening party. Includes slightly different production and clearer Ye vocals. <PERSON> has a new verse at the end of the song. Seems to be same as release version but uncensored. Original CDQ snippet leaked December 4th, 2022. CDQ mono leaked 1/13/24.", "length": "5:40", "fileDate": 17051040, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ye-donda", "originalUrl": "https://music.froste.lol/song/40f6a81eac0e7863704ff2948061ebd9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/40f6a81eac0e7863704ff2948061ebd9/play\", \"key\": \"Off The Grid\", \"title\": \"Ye - Off The Grid [V38]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, Fivio Foreign & Justin Austin) (prod. 30 Roc, AyoAA & Ojivolta)\", \"description\": \"OG Filename: Off the grid MIX TEST.07_07\\nPlayed at the Chicago Donda listening party. Includes slightly different production and clearer Ye vocals. <PERSON> has a new verse at the end of the song. Seems to be same as release version but uncensored. Original CDQ snippet leaked December 4th, 2022. CDQ mono leaked 1/13/24.\", \"date\": 17051040, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"51db17ef2f61b17141568d6af4a19bfc\", \"url\": \"https://api.pillowcase.su/api/download/51db17ef2f61b17141568d6af4a19bfc\", \"size\": \"12.8 MB\", \"duration\": 340.15}", "aliases": [], "size": "12.8 MB"}]}