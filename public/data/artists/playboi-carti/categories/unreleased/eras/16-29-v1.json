{"id": "16-29-v1", "name": "16*29 [V1]", "description": "In September of 2017, <PERSON><PERSON><PERSON> announced via Snapchat and Twitter that he would be collaborating with <PERSON> on a joint album titled 1629*. The following month, a tour for the album was announced by the two, but it was shortly canceled afterward when <PERSON><PERSON> said he would not be going on tour with <PERSON><PERSON> because he \"needed to focus.\" The album would fall by the wayside until July of 2018, when <PERSON><PERSON> mentioned that he and <PERSON><PERSON> had recorded \"like 100 songs.\" After that, all talk of the album pretty much subsided. It should be noted that many insiders have claimed that there was never a tracklist, cover art, or marketing plan put together.", "backgroundColor": "rgb(12, 34, 22)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17GTOC6To3gcNIf02sDQFRTFV2n58HFnlJFC3Y6IGtAcNJi5UMvfEVVB4RI0I1IerUmBlwygrty_V8jJnL3SuIXTefo3G6SyRF1NKqCRYHewXRpJr_TjvLTBxdayYsep?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "big-bank", "name": "⭐ Big Bank [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> x <PERSON>zi meggriff1.4.18\nA throwaway from the 16*29 sessions. Track was originally titled after the instrumental.", "length": "2:22", "fileDate": ********, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/7c7ff8bb7c0168754f69e512b99276a4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7c7ff8bb7c0168754f69e512b99276a4/play\", \"key\": \"Big Bank\", \"title\": \"\\u2b50 Big Bank [V1]\", \"artists\": \"(prod. <PERSON><PERSON>er<PERSON> & Don Cannon)\", \"aliases\": [\"Meg Griff'\"], \"description\": \"OG Filename: <PERSON><PERSON> x Uzi meggriff1.4.18\\nA throwaway from the 16*29 sessions. Track was originally titled after the instrumental.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e61fa307208d5af58c5e6b29acf26a5d\", \"url\": \"https://api.pillowcase.su/api/download/e61fa307208d5af58c5e6b29acf26a5d\", \"size\": \"2.74 MB\", \"duration\": 142.2}", "aliases": ["<PERSON>'"], "size": "2.74 MB"}, {"id": "big-bank-2", "name": "Big Bank [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON>"], "notes": "OG Filename: Big Bank DC Main Mix -V1\nLater version of \"Big Bank\" with an alternate <PERSON><PERSON> verse.", "length": "2:18", "fileDate": ********, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/1845b5381203b710a204d6265a3b24b3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1845b5381203b710a204d6265a3b24b3/play\", \"key\": \"Big Bank\", \"title\": \"Big Bank [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & Don <PERSON>)\", \"aliases\": [\"Meg Griff'\"], \"description\": \"OG Filename: Big Bank DC Main Mix -V1\\nLater version of \\\"Big Bank\\\" with an alternate <PERSON>ti verse.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"854afe22c4ecf1b328974ff1e90e283d\", \"url\": \"https://api.pillowcase.su/api/download/854afe22c4ecf1b328974ff1e90e283d\", \"size\": \"2.68 MB\", \"duration\": 138.96}", "aliases": ["<PERSON>'"], "size": "2.68 MB"}, {"id": "home", "name": "⭐ Home [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Home w Uzi 1.3.18\nOriginal version of the Die Lit song \"Home (KOD)\" with an Uzi feature that was scrapped in the end.", "length": "3:46", "fileDate": ********, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/caee804917239e758ff2b7fe3dc379e8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/caee804917239e758ff2b7fe3dc379e8/play\", \"key\": \"Home\", \"title\": \"\\u2b50 Home [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Home (KOD)\", \"King Of Diamonds\"], \"description\": \"OG Filename: Home w Uzi 1.3.18\\nOriginal version of the Die Lit song \\\"Home (KOD)\\\" with an Uzi feature that was scrapped in the end.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cd235b6cc8c1ff2fe244073f8bf9e1dd\", \"url\": \"https://api.pillowcase.su/api/download/cd235b6cc8c1ff2fe244073f8bf9e1dd\", \"size\": \"4.09 MB\", \"duration\": 226.46}", "aliases": ["Home (KOD)", "King Of Diamonds"], "size": "4.09 MB"}, {"id": "like-i-m-swoosh", "name": "<PERSON> - Like I'm Swoosh [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Original version of the song with no <PERSON><PERSON> feature. Was track 1 on Swoosh Gods album 'Controversy' before being deleted.", "length": "1:33", "fileDate": 15849216, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/eeb0bf6491606e42cd325ee26a1f33b9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/eeb0bf6491606e42cd325ee26a1f33b9/play\", \"key\": \"Like I'm Swoosh\", \"title\": \"<PERSON> U<PERSON> Vert - Like I'm Swoosh [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Candy\"], \"description\": \"Original version of the song with no Carti feature. Was track 1 on Swoosh Gods album 'Controversy' before being deleted.\", \"date\": 15849216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7e3e4a4d95dc76b133e02b0b53ebe517\", \"url\": \"https://api.pillowcase.su/api/download/7e3e4a4d95dc76b133e02b0b53ebe517\", \"size\": \"1.96 MB\", \"duration\": 93.7}", "aliases": ["<PERSON>"], "size": "1.96 MB"}, {"id": "candy", "name": "⭐ Candy [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Carti x Uzi Candy 1.4.18\nOG Filename (Metadata): Carti Uzi X Candy\nA throwaway from the 16*29 sessions. This version has a carti feature. The beat was titled \"X Candy\".", "length": "2:37", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/d676d65e1fc76db249d68e0c0bc1e651/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d676d65e1fc76db249d68e0c0bc1e651/play\", \"key\": \"Candy\", \"title\": \"\\u2b50 Candy [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"X Candy\", \"Like I'm Swoosh\"], \"description\": \"OG Filename: Carti x Uzi Candy 1.4.18\\nOG Filename (Metadata): <PERSON><PERSON> X Candy\\nA throwaway from the 16*29 sessions. This version has a carti feature. The beat was titled \\\"X Candy\\\".\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"04fa2235ad1ea15466a112fbbc9511d5\", \"url\": \"https://api.pillowcase.su/api/download/04fa2235ad1ea15466a112fbbc9511d5\", \"size\": \"2.98 MB\", \"duration\": 157.63}", "aliases": ["X Candy", "Like I'm Swoosh"], "size": "2.98 MB"}, {"id": "might", "name": "Might ", "artists": [], "producers": [], "notes": "OG Filename: 1629__Might_-_<PERSON><PERSON>_Ruff 2\nOG File Metadata: One Time - <PERSON><PERSON> [Ruff]\nA demo containing only a short verse / chorus and an open verse", "length": "3:37", "fileDate": 16998336, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/59bd2d52a1a0c87c008194b61c3bfc24", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/59bd2d52a1a0c87c008194b61c3bfc24\", \"key\": \"Might\", \"title\": \"Might \", \"aliases\": [\"One Time\"], \"description\": \"OG Filename: 1629__Might_-_<PERSON><PERSON>_Ruff 2\\nOG File Metadata: One Time - Carti [Ruff]\\nA demo containing only a short verse / chorus and an open verse\", \"date\": 16998336, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ee2de468c41ceb1803a2296080e15f86\", \"url\": \"https://api.pillowcase.su/api/download/ee2de468c41ceb1803a2296080e15f86\", \"size\": \"3.94 MB\", \"duration\": 217.65}", "aliases": ["One Time"], "size": "3.94 MB"}, {"id": "my-division", "name": "✨ My Division*", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> x <PERSON>zi nye5 1.4.18\nAn untitled throwaway from the 16*29 sessions. <PERSON><PERSON><PERSON> was meant to record a verse for it.", "length": "1:12", "fileDate": 16263936, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/195ec714a9e8b7ae8252761ac14da3ec/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/195ec714a9e8b7ae8252761ac14da3ec/play\", \"key\": \"My Division*\", \"title\": \"\\u2728 My Division*\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"No Mercy\"], \"description\": \"OG Filename: <PERSON><PERSON> x Uzi nye5 1.4.18\\nAn untitled throwaway from the 16*29 sessions. <PERSON><PERSON><PERSON> was meant to record a verse for it.\", \"date\": 16263936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b3cf10e4a827dd227b9fe531f33a9f7c\", \"url\": \"https://api.pillowcase.su/api/download/b3cf10e4a827dd227b9fe531f33a9f7c\", \"size\": \"1.62 MB\", \"duration\": 72.1}", "aliases": ["No Mercy"], "size": "1.62 MB"}, {"id": "home-8", "name": "Home [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Home 12.6.17\nHome (KOD), before u<PERSON> recorded on it. Unknown if it has any differences from the released version.", "length": "0:05", "fileDate": 17119296, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "16-29-v1", "originalUrl": "https://music.froste.lol/song/7e08662eadadc93885ff53ebe91f342e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7e08662eadadc93885ff53ebe91f342e/play\", \"key\": \"Home\", \"title\": \"Home [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Home (KOD)\", \"King Of Diamonds\"], \"description\": \"OG Filename: Home 12.6.17\\nHome (KOD), before uzi recorded on it. Unknown if it has any differences from the released version.\", \"date\": 17119296, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a19a106cad381ff82956af22f5aed6ad\", \"url\": \"https://api.pillowcase.su/api/download/a19a106cad381ff82956af22f5aed6ad\", \"size\": \"542 kB\", \"duration\": 5.02}", "aliases": ["Home (KOD)", "King Of Diamonds"], "size": "542 kB"}]}