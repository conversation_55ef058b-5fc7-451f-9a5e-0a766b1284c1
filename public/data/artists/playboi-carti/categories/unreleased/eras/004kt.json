{"id": "004kt", "name": "004KT", "description": "In 2023 <PERSON><PERSON> would post a picture of <PERSON><PERSON><PERSON> rocking the opium upside down cross blah blah blah project was still in the works until YB got raided in April 2024, was scrapped shortly after that", "backgroundColor": "rgb(0, 0, 0)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FxGGK1H16KJkimQSAHkBZo4SZHUzZzfp67KZ0XamSbBZThy1z_Ew7vgH2hgNm_E5DLCtCwQgoXyIJUE6U9541e93TRFsazuObd6RxDfhkVBWZdnd91yMrmdrpCC3A?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "brand-new", "name": "✨ Brand New [V2]", "artists": [], "producers": ["BNYX", "HK"], "notes": "OG Filename: Brand New v2_YB_HK_Bynx\nRumured to be for 004KT with an open verse meant for <PERSON><PERSON>", "length": "", "fileDate": 17289504, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "004kt", "originalUrl": "http://music.froste.lol/song/b65e85b07b95c8e43147a60c094ee59e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/b65e85b07b95c8e43147a60c094ee59e/play\", \"key\": \"Brand New\", \"title\": \"\\u2728 Brand New [V2]\", \"artists\": \"(prod. BNYX & HK)\", \"description\": \"OG Filename: Brand New v2_YB_HK_Bynx\\nRumured to be for 004KT with an open verse meant for <PERSON><PERSON>\", \"date\": 17289504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "flex-up", "name": "<PERSON><PERSON>oy Never Broke Again - Flex Up (Remix)", "artists": [], "producers": ["Southside", "ATL Jacob", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: HA Top Flex Up v1.1_YB_Remix\nNBA YB's take on the 'Flex Up' beat from 2020. Recording leaked Jun 1, 2024, and then a CDQ snippet leaked Feb 26, 2025. Full song leaked on Mar 7, 2025 following a 3.2k gb.", "length": "3:14", "fileDate": 17413056, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "004kt", "originalUrl": "https://music.froste.lol/song/f659f3743ad17371a4a27ab1da3390dd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f659f3743ad17371a4a27ab1da3390dd\", \"key\": \"Flex Up (Remix)\", \"title\": \"<PERSON><PERSON><PERSON> Never Broke Again - Flex Up (Remix)\", \"artists\": \"(prod. <PERSON>, ATL Jacob & Pyrex)\", \"description\": \"OG Filename: HA Top Flex Up v1.1_YB_Remix\\nNBA YB's take on the 'Flex Up' beat from 2020. Recording leaked Jun 1, 2024, and then a CDQ snippet leaked Feb 26, 2025. Full song leaked on Mar 7, 2025 following a 3.2k gb.\", \"date\": 17413056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e714c79d9bcd21f45bb7c253145aa05f\", \"url\": \"https://api.pillowcase.su/api/download/e714c79d9bcd21f45bb7c253145aa05f\", \"size\": \"3.29 MB\", \"duration\": 194}", "aliases": [], "size": "3.29 MB"}, {"id": "3shit", "name": "3SHIT", "artists": [], "producers": [], "notes": "Previewed by YB. Rumored to contain an open verse for <PERSON><PERSON><PERSON>. CDQ snippet leaked Mar 7, 2025.", "length": "0:35", "fileDate": 17413056, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "004kt", "originalUrl": "https://music.froste.lol/song/57df20e0f1d7d0580e3a6e15733bcb36", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/57df20e0f1d7d0580e3a6e15733bcb36\", \"key\": \"3SHIT\", \"title\": \"3SHIT\", \"description\": \"Previewed by YB. Rumored to contain an open verse for <PERSON><PERSON><PERSON>. CDQ snippet leaked Mar 7, 2025.\", \"date\": 17413056, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7804ca24e234878304d1b1c013a9268a\", \"url\": \"https://api.pillowcase.su/api/download/7804ca24e234878304d1b1c013a9268a\", \"size\": \"755 kB\", \"duration\": 35.66}", "aliases": [], "size": "755 kB"}, {"id": "3shit-4", "name": "3SHIT", "artists": [], "producers": [], "notes": "Previewed by YB. Rumored to contain an open verse for <PERSON><PERSON><PERSON>. CDQ snippet leaked Mar 7, 2025.", "length": "0:14", "fileDate": 17413056, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "004kt", "originalUrl": "https://music.froste.lol/song/f88e2085e77758d49af1db8dc15505be", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f88e2085e77758d49af1db8dc15505be\", \"key\": \"3SHIT\", \"title\": \"3SHIT\", \"description\": \"Previewed by YB. Rumored to contain an open verse for <PERSON><PERSON><PERSON>. CDQ snippet leaked Mar 7, 2025.\", \"date\": 17413056, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"63c490b650d30b8bf96b715a47f0b53c\", \"url\": \"https://api.pillowcase.su/api/download/63c490b650d30b8bf96b715a47f0b53c\", \"size\": \"419 kB\", \"duration\": 14.68}", "aliases": [], "size": "419 kB"}, {"id": "don-t-play-with-me", "name": "Don't Play With Me*", "artists": [], "producers": [], "notes": "Previewed twice by YB. Could be on 004KT. Private bought from leakers. Previewed on discord by leakers in June 2024.", "length": "0:07", "fileDate": "", "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "004kt", "originalUrl": "https://music.froste.lol/song/4bc4e0462f833885ceb79cb33cb79174", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4bc4e0462f833885ceb79cb33cb79174\", \"key\": \"Don't Play With Me*\", \"title\": \"Don't Play With Me*\", \"aliases\": [\"Run Down\"], \"description\": \"Previewed twice by YB. Could be on 004KT. Private bought from leakers. Previewed on discord by leakers in June 2024.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"id\": \"b6f4d50c37548a959c4f9577bf219f59\", \"url\": \"https://api.pillowcase.su/api/download/b6f4d50c37548a959c4f9577bf219f59\", \"size\": \"306 kB\", \"duration\": 7.6}", "aliases": ["Run Down"], "size": "306 kB"}, {"id": "don-t-play-with-me-6", "name": "Don't Play With Me*", "artists": [], "producers": [], "notes": "Previewed twice by YB. Could be on 004KT. Private bought from leakers. Previewed on discord by leakers in June 2024.", "length": "1:11", "fileDate": "", "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "004kt", "originalUrl": "https://music.froste.lol/song/d79853b32145087460ab106a211b07b9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d79853b32145087460ab106a211b07b9\", \"key\": \"Don't Play With Me*\", \"title\": \"Don't Play With Me*\", \"aliases\": [\"Run Down\"], \"description\": \"Previewed twice by YB. Could be on 004KT. Private bought from leakers. Previewed on discord by leakers in June 2024.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"id\": \"2333ee89f052036785c9fac1fda960c9\", \"url\": \"https://api.pillowcase.su/api/download/2333ee89f052036785c9fac1fda960c9\", \"size\": \"1.32 MB\", \"duration\": 71.1}", "aliases": ["Run Down"], "size": "1.32 MB"}, {"id": "not-my-friend", "name": "Not My Friend [V1]", "artists": [], "producers": [], "notes": "Previewed by <PERSON><PERSON>. 2022, according to YB HUB Cartis part is ai but this is very unlikely.", "length": "0:31", "fileDate": 17207424, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "004kt", "originalUrl": "https://pillowcase.su/f/166f4c87ac08e6667d7a92d2d24d7948", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/166f4c87ac08e6667d7a92d2d24d7948\", \"key\": \"Not My Friend\", \"title\": \"Not My Friend [V1]\", \"description\": \"Previewed by <PERSON><PERSON>. 2022, according to YB HUB Cartis part is ai but this is very unlikely.\", \"date\": 17207424, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"e72a8cc050042ea29f45e78f394028fc\", \"url\": \"https://api.pillowcase.su/api/download/e72a8cc050042ea29f45e78f394028fc\", \"size\": \"680 kB\", \"duration\": 31.01}", "aliases": [], "size": "680 kB"}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "A 2024 song that <PERSON><PERSON> was supposed to hop on, but it did not end up happening due to a raid.", "length": "0:20", "fileDate": 17210016, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "004kt", "originalUrl": "https://pillowcase.su/f/a21464ed031f29037a6c66c5f93993d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a21464ed031f29037a6c66c5f93993d1\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"004KT Demo (1)\"], \"description\": \"A 2024 song that <PERSON><PERSON> was supposed to hop on, but it did not end up happening due to a raid.\", \"date\": 17210016, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"22b22f5e0db75b4b0233f05e0c3437e5\", \"url\": \"https://api.pillowcase.su/api/download/22b22f5e0db75b4b0233f05e0c3437e5\", \"size\": \"506 kB\", \"duration\": 20.09}", "aliases": ["004KT Demo (1)"], "size": "506 kB"}]}