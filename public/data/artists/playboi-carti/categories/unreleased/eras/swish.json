{"id": "swish", "name": "SWISH", "description": "After changing the name of his 7th solo album from So Help Me God to SWISH, <PERSON><PERSON><PERSON> began to develop all new songs throughout mid-late 2015 meant for the album, with most of them eventually making it onto the final release of The Life of Pablo. <PERSON><PERSON><PERSON> also continued to work on many So Help Me God and Yeezus 2 tracks, but by the end of 2015 and the start of 2016, <PERSON><PERSON><PERSON> had dropped most of these tracks from the tracklist for SWISH, which began to resemble the final TLOP tracklist strongly.", "backgroundColor": "rgb(255, 122, 116)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17HMtuwAOpzpjtV8cC83anyH2nzdZrvhYIrUYyDgupjlZM21wcj3GBUoT08pF92ZirA0bNw6NkdPFZneec0z6hhlzlMVsf7Bw0JlowC_aJcMkRl33p2zgCNvzD0m4pxZh6OLvQ_cRBBx9wdncU4?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "2-rihannas", "name": "2 <PERSON><PERSON><PERSON><PERSON> [V8]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: KW - 2 Rihannas Ref 1 (12.18.15)\nVersion played for Keyon Christ. Features <PERSON><PERSON><PERSON> reference vocals that he mistook for a feature, as well as the sample from \"FML\" being worked into the track. Leaked as a bonus for the finished \"Maintenance\" groupbuy.", "length": "225.09", "fileDate": 16842816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/d9167c70ab850b6345c956b98fd48f58", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9167c70ab850b6345c956b98fd48f58\", \"key\": \"2 Rihannas\", \"title\": \"2 Rihannas [V8]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Mitus Touch\"], \"description\": \"OG Filename: KW - 2 Rihannas Ref 1 (12.18.15)\\n<PERSON><PERSON><PERSON> played for <PERSON><PERSON>. Features Pusha T reference vocals that he mistook for a feature, as well as the sample from \\\"FML\\\" being worked into the track. Leaked as a bonus for the finished \\\"Maintenance\\\" groupbuy.\", \"date\": 16842816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"99eac739c8b4d309e15af217cf072682\", \"url\": \"https://api.pillowcase.su/api/download/99eac739c8b4d309e15af217cf072682\", \"size\": \"3.82 MB\", \"duration\": 225.09}", "aliases": ["<PERSON><PERSON>"], "size": "3.82 MB"}, {"id": "30-hours", "name": "30 Hours [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Original version with completely different (yet unfinished) vocals/structure, along with the sample being played out slightly differently. The original instrumental was created on December 20th, 2012. Was said to be named \"Paper Thin Lover\", but that name was fanmade.", "length": "389.36", "fileDate": 16731360, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/1d1609984555ef1607fd33e14940b5f9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1d1609984555ef1607fd33e14940b5f9\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Original version with completely different (yet unfinished) vocals/structure, along with the sample being played out slightly differently. The original instrumental was created on December 20th, 2012. Was said to be named \\\"Paper Thin Lover\\\", but that name was fanmade.\", \"date\": 16731360, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"90692fa2a8bec5ec664a58fb7984c41f\", \"url\": \"https://api.pillowcase.su/api/download/90692fa2a8bec5ec664a58fb7984c41f\", \"size\": \"6.44 MB\", \"duration\": 389.36}", "aliases": [], "size": "6.44 MB"}, {"id": "30-hours-3", "name": "30 Hours [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Said to be \"demo 2\" by <PERSON><PERSON>. Some lyrics from this freestyle are used in release, but most are unused.", "length": "172.54", "fileDate": 16732224, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/85d3bba6cb86c5415e0f6ea6f6a68b5e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/85d3bba6cb86c5415e0f6ea6f6a68b5e\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Said to be \\\"demo 2\\\" by <PERSON><PERSON>. Some lyrics from this freestyle are used in release, but most are unused.\", \"date\": 16732224, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5552f9a36e4c0503b81abbaeaf5e5c56\", \"url\": \"https://api.pillowcase.su/api/download/5552f9a36e4c0503b81abbaeaf5e5c56\", \"size\": \"2.97 MB\", \"duration\": 172.54}", "aliases": [], "size": "2.97 MB"}, {"id": "30-hours-4", "name": "30 Hours [V3]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Said by <PERSON><PERSON> to be \"demo 3\". Leaked as a bonus for the \"Welcome To My Life\" groupbuy. Originally referred to as \"College Dorm Sweetheart\", but that isn't a real title. A stem edit using vocals from this demo over the beat for \"Reborn\" made by <PERSON> leaked in 2019.", "length": "149.4", "fileDate": 16720992, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/f96dbbf13ef78dc340790e96f0533a5e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f96dbbf13ef78dc340790e96f0533a5e\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Said by <PERSON><PERSON> to be \\\"demo 3\\\". Leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy. Originally referred to as \\\"College Dorm Sweetheart\\\", but that isn't a real title. A stem edit using vocals from this demo over the beat for \\\"Reborn\\\" made by <PERSON> leaked in 2019.\", \"date\": 16720992, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"addc3e71aceb2e6a10d0a70051e7ac5d\", \"url\": \"https://api.pillowcase.su/api/download/addc3e71aceb2e6a10d0a70051e7ac5d\", \"size\": \"2.6 MB\", \"duration\": 149.4}", "aliases": [], "size": "2.6 MB"}, {"id": "30-hours-5", "name": "30 Hours [V4]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Version with rough demo verses. Includes some lines that made it into final but also some that are new. Tagged.", "length": "145.27", "fileDate": 15830208, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/e9c4934d3ac4f042681c2a8c3a8ec0eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9c4934d3ac4f042681c2a8c3a8ec0eb\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Version with rough demo verses. Includes some lines that made it into final but also some that are new. Tagged.\", \"date\": 15830208, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f63744902faf9c3d377bb6aae87988cd\", \"url\": \"https://api.pillowcase.su/api/download/f63744902faf9c3d377bb6aae87988cd\", \"size\": \"2.54 MB\", \"duration\": 145.27}", "aliases": [], "size": "2.54 MB"}, {"id": "30-hours-6", "name": "30 Hours [V5]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "<PERSON> reference track, from when <PERSON><PERSON><PERSON> was recording his part. Can be heard in the bleed on an early \"30 Hours\" vocal stem. Made in October 2015. Mentioned by <PERSON> in his 2018 Pusha T diss track \"Duppy Freestyle\". Unsure if anyone owns this in CDQ and if it's just <PERSON> doing the 2nd verse. Privately sold to members of the community during a unsuccessful groupbuy. Version linked is enhanced slightly to be able to hear <PERSON>.", "length": "129.25", "fileDate": 16052256, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/b1d8f76206c3c34b566b0d8a25833e6b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b1d8f76206c3c34b566b0d8a25833e6b\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V5]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"<PERSON> reference track, from when <PERSON><PERSON><PERSON> was recording his part. Can be heard in the bleed on an early \\\"30 Hours\\\" vocal stem. Made in October 2015. Mentioned by <PERSON> in his 2018 Pusha T diss track \\\"Duppy Freestyle\\\". Unsure if anyone owns this in CDQ and if it's just <PERSON> doing the 2nd verse. Privately sold to members of the community during a unsuccessful groupbuy. Version linked is enhanced slightly to be able to hear <PERSON>.\", \"date\": 16052256, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"4924c5ea52e96b43c5ef017d744b359a\", \"url\": \"https://api.pillowcase.su/api/download/4924c5ea52e96b43c5ef017d744b359a\", \"size\": \"2.28 MB\", \"duration\": 129.25}", "aliases": [], "size": "2.28 MB"}, {"id": "30-hours-7", "name": "30 Hours [V6]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "???"], "notes": "Consequence reference track. Has some of the lyrics that would go on to used in the final version, as well as a beat switch. Snippets played by Consequence on Instagram lives.", "length": "19.25", "fileDate": 17233344, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/65ade15173e4d367fbe2afbcfdc851cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65ade15173e4d367fbe2afbcfdc851cf\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V6]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON> & ???)\", \"description\": \"Consequence reference track. Has some of the lyrics that would go on to used in the final version, as well as a beat switch. Snippets played by Consequence on Instagram lives.\", \"date\": 17233344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e36447a01d4ea57257b8a018509f4f67\", \"url\": \"https://api.pillowcase.su/api/download/e36447a01d4ea57257b8a018509f4f67\", \"size\": \"519 kB\", \"duration\": 19.25}", "aliases": [], "size": "519 kB"}, {"id": "30-hours-8", "name": "30 Hours [V6]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "???"], "notes": "Consequence reference track. Has some of the lyrics that would go on to used in the final version, as well as a beat switch. Snippets played by Consequence on Instagram lives.", "length": "55.63", "fileDate": 17233344, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/f2ff167142e80d4230f2fca6ac0516a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f2ff167142e80d4230f2fca6ac0516a5\", \"key\": \"30 Hours\", \"title\": \"30 Hours [V6]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON> & ???)\", \"description\": \"Consequence reference track. Has some of the lyrics that would go on to used in the final version, as well as a beat switch. Snippets played by Consequence on Instagram lives.\", \"date\": 17233344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ba72a25f1c5cee67b01d3f2aff159b55\", \"url\": \"https://api.pillowcase.su/api/download/ba72a25f1c5cee67b01d3f2aff159b55\", \"size\": \"1.1 MB\", \"duration\": 55.63}", "aliases": [], "size": "1.1 MB"}, {"id": "30-hours-9", "name": "✨ 30 Hours [V7]", "artists": [], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "<PERSON> version. Has the finished release vocal take but with completely different drums and prod. VC recorded snippets surfaced in December of 2021. The full song would leak January 29th, 2023.", "length": "117.52", "fileDate": 16749504, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/09ee566a50e3f5b296acaca6a86c071a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/09ee566a50e3f5b296acaca6a86c071a\", \"key\": \"30 Hours\", \"title\": \"\\u2728 30 Hours [V7]\", \"artists\": \"(prod. <PERSON> & <PERSON><PERSON>)\", \"description\": \"<PERSON> version. Has the finished release vocal take but with completely different drums and prod. VC recorded snippets surfaced in December of 2021. The full song would leak January 29th, 2023.\", \"date\": 16749504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4b74d1561f3905ecd743a176a8635345\", \"url\": \"https://api.pillowcase.su/api/download/4b74d1561f3905ecd743a176a8635345\", \"size\": \"2.09 MB\", \"duration\": 117.52}", "aliases": [], "size": "2.09 MB"}, {"id": "anxiety", "name": "✨ Anxiety [V3]", "artists": [], "producers": [], "notes": "Mostly finished demo about cheating that was likely cut down from the earlier freestyle. Features the line \"No homo', no suspect, but you gotta tell me, was he bigger?\". Another feature can be heard in the vocal bleed. Samples \"Summertime Rolls\" by <PERSON>'s Addiction.", "length": "191.9", "fileDate": 16268256, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/c0379645f6316476e0a1b614637df2ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c0379645f6316476e0a1b614637df2ff\", \"key\": \"Anxiety\", \"title\": \"\\u2728 Anxiety [V3]\", \"aliases\": [\"Freestyle 9\"], \"description\": \"Mostly finished demo about cheating that was likely cut down from the earlier freestyle. Features the line \\\"No homo', no suspect, but you gotta tell me, was he bigger?\\\". Another feature can be heard in the vocal bleed. Samples \\\"Summertime Rolls\\\" by <PERSON>'s Addiction.\", \"date\": 16268256, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"66454f363298ade6f4a6dcff66d490d5\", \"url\": \"https://api.pillowcase.su/api/download/66454f363298ade6f4a6dcff66d490d5\", \"size\": \"3.28 MB\", \"duration\": 191.9}", "aliases": ["Freestyle 9"], "size": "3.28 MB"}, {"id": "bad-night", "name": "Bad Night [V8]", "artists": ["<PERSON> Thug"], "producers": ["Hudson Mohawke"], "notes": "Version with later production than Yeezus 2-era versions. Owned by MusicMafia. Possibly the August 16th, 2015 version.", "length": "26.85", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/639866ddc5c42e335a42d0cbaaf58ebf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/639866ddc5c42e335a42d0cbaaf58ebf\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V8]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Man Up\", \"Rap Tarantino\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"Version with later production than Yeezus 2-era versions. Owned by MusicMafia. Possibly the August 16th, 2015 version.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3a11d55c2a38be619cfa58c459150f1f\", \"url\": \"https://api.pillowcase.su/api/download/3a11d55c2a38be619cfa58c459150f1f\", \"size\": \"641 kB\", \"duration\": 26.85}", "aliases": ["Man Up", "<PERSON>", "Too Re<PERSON>ss", "Bad Guy"], "size": "641 kB"}, {"id": "bad-night-12", "name": "Bad Night [V9]", "artists": [], "producers": [], "notes": "Version found in the Audio Files folder of the leaked Pro Tools session. Only two stems from this version are present. Linked files contain a snippet discovered on March 7th, 2025 of this version (found in a Kick stem in the session) as well as two audio files from this version bounced together.", "length": "4.4", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/e2d78cf846c1f5ec9306acf61603857c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e2d78cf846c1f5ec9306acf61603857c\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V9]\", \"aliases\": [\"Rap Tarantino\", \"Man Up\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"Version found in the Audio Files folder of the leaked Pro Tools session. Only two stems from this version are present. Linked files contain a snippet discovered on March 7th, 2025 of this version (found in a Kick stem in the session) as well as two audio files from this version bounced together.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"361157a84e57d3f7d0ee35956a738d0d\", \"url\": \"https://api.pillowcase.su/api/download/361157a84e57d3f7d0ee35956a738d0d\", \"size\": \"282 kB\", \"duration\": 4.4}", "aliases": ["<PERSON>", "Man Up", "Too Re<PERSON>ss", "Bad Guy"], "size": "282 kB"}, {"id": "bad-night-13", "name": "Bad Night [V9]", "artists": [], "producers": [], "notes": "Version found in the Audio Files folder of the leaked Pro Tools session. Only two stems from this version are present. Linked files contain a snippet discovered on March 7th, 2025 of this version (found in a Kick stem in the session) as well as two audio files from this version bounced together.", "length": "271.21", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/c955c9fee8365e36aead8fbbfa6f2f69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c955c9fee8365e36aead8fbbfa6f2f69\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V9]\", \"aliases\": [\"Rap Tarantino\", \"Man Up\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"Version found in the Audio Files folder of the leaked Pro Tools session. Only two stems from this version are present. Linked files contain a snippet discovered on March 7th, 2025 of this version (found in a Kick stem in the session) as well as two audio files from this version bounced together.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"298d282c4e6289c7e93f646c647954b0\", \"url\": \"https://api.pillowcase.su/api/download/298d282c4e6289c7e93f646c647954b0\", \"size\": \"4.55 MB\", \"duration\": 271.21}", "aliases": ["<PERSON>", "Man Up", "Too Re<PERSON>ss", "Bad Guy"], "size": "4.55 MB"}, {"id": "bad-night-14", "name": "Bad Night [V10]", "artists": ["<PERSON> Thug"], "producers": [], "notes": "OG Filename: Bad Night-Tyga Ref BOUNCE\nVersion with <PERSON><PERSON> doing reference vocals for <PERSON><PERSON><PERSON>.", "length": "253.61", "fileDate": 15376608, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/f384f7d24d78209e7c81bb36b7d0bff5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f384f7d24d78209e7c81bb36b7d0bff5\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V10]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON> Thug)\", \"aliases\": [\"Rap Tarantino\", \"Man Up\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: Bad Night-Tyga Ref BOUNCE\\nVersion with <PERSON><PERSON> doing reference vocals for <PERSON><PERSON><PERSON>.\", \"date\": 15376608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"adc8df6e139f1613ec87335640dd7916\", \"url\": \"https://api.pillowcase.su/api/download/adc8df6e139f1613ec87335640dd7916\", \"size\": \"4.27 MB\", \"duration\": 253.61}", "aliases": ["<PERSON>", "Man Up", "Too Re<PERSON>ss", "Bad Guy"], "size": "4.27 MB"}, {"id": "bad-night-15", "name": "Bad Night [V11]", "artists": ["<PERSON> Thug"], "producers": ["Hudson Mohawke"], "notes": "Has a more finished beat and lyrics compared to the other versions. Portions of the beat were reused for the \"Hacker War Mission Music\" from the Watch Dogs 2 Original Soundtrack in 2016.", "length": "261.89", "fileDate": 15647040, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/12e039365dec58510e3e2aed0220c63c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/12e039365dec58510e3e2aed0220c63c\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V11]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Rap Tarantino\", \"Man Up\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"Has a more finished beat and lyrics compared to the other versions. Portions of the beat were reused for the \\\"Hacker War Mission Music\\\" from the Watch Dogs 2 Original Soundtrack in 2016.\", \"date\": 15647040, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4f1c35182bdfd05461cd515d293d98ea\", \"url\": \"https://api.pillowcase.su/api/download/4f1c35182bdfd05461cd515d293d98ea\", \"size\": \"4.4 MB\", \"duration\": 261.89}", "aliases": ["<PERSON>", "Man Up", "Too Re<PERSON>ss", "Bad Guy"], "size": "4.4 MB"}, {"id": "bad-night-16", "name": "⭐ Bad Night [V12]", "artists": ["<PERSON> Thug"], "producers": ["Hudson Mohawke"], "notes": "SWISH era song featuring <PERSON>hu<PERSON>. Has a different instrumental and beatswitch with new fully finished lyrics. Samples \"Petiatil Cx Htdui\" by Aphex Twin at the end. Originally leaked tagged.", "length": "218.07", "fileDate": 16226784, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/846f23cf6253d35ffd9c431c1ef504f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/846f23cf6253d35ffd9c431c1ef504f1\", \"key\": \"Bad Night\", \"title\": \"\\u2b50 Bad Night [V12]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Rap Tarantino\", \"Man Up\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"SWISH era song featuring <PERSON> Thug. Has a different instrumental and beatswitch with new fully finished lyrics. <PERSON><PERSON> \\\"Petiatil Cx Htdui\\\" by Aphex Twin at the end. Originally leaked tagged.\", \"date\": 16226784, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7da2d86dd1339a31fd615b4c0a228ac4\", \"url\": \"https://api.pillowcase.su/api/download/7da2d86dd1339a31fd615b4c0a228ac4\", \"size\": \"3.7 MB\", \"duration\": 218.07}", "aliases": ["<PERSON>", "Man Up", "Too Re<PERSON>ss", "Bad Guy"], "size": "3.7 MB"}, {"id": "code-red", "name": "Code Red", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: YT - Code Red Ref\nReference track for <PERSON><PERSON><PERSON> that was later added onto \"Bad Night\". Had an unsuccessful groupbuy.", "length": "370.02", "fileDate": 16140384, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/a7a7bbb825898f536d3c4650b17d82ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7a7bbb825898f536d3c4650b17d82ac\", \"key\": \"Code Red\", \"title\": \"Code Red\", \"artists\": \"(ref. <PERSON>hug) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: YT - Code Red Ref\\nReference track for <PERSON><PERSON><PERSON> that was later added onto \\\"Bad Night\\\". Had an unsuccessful groupbuy.\", \"date\": 16140384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"65b8eba64ef32a2fd0b2c0a1b58a9d95\", \"url\": \"https://api.pillowcase.su/api/download/65b8eba64ef32a2fd0b2c0a1b58a9d95\", \"size\": \"6.14 MB\", \"duration\": 370.02}", "aliases": [], "size": "6.14 MB"}, {"id": "come-and-go", "name": "Come and Go [V2]", "artists": [], "producers": [], "notes": "Version with reference vocals from <PERSON><PERSON>. <PERSON><PERSON> would reuse this take on the 2018 Rambo Savage song \"That's On You\". Acapella leaked March 13th, 2025.", "length": "183.43", "fileDate": 17418240, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/083f917eeb8351cb5f9c99e3010142af", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/083f917eeb8351cb5f9c99e3010142af\", \"key\": \"Come and Go\", \"title\": \"Come and Go [V2]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"That's On You\"], \"description\": \"Version with reference vocals from <PERSON>. U<PERSON> would reuse this take on the 2018 Rambo Savage song \\\"That's On You\\\". Acapella leaked March 13th, 2025.\", \"date\": 17418240, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2dc4466e08fdb5239546ec7d85b3f7f1\", \"url\": \"https://api.pillowcase.su/api/download/2dc4466e08fdb5239546ec7d85b3f7f1\", \"size\": \"3.15 MB\", \"duration\": 183.43}", "aliases": ["That's On You"], "size": "3.15 MB"}, {"id": "deja-vu", "name": "✨ Deja Vu", "artists": [], "producers": [], "notes": "Has <PERSON><PERSON><PERSON> mumbling over most of the beat. Likely made during the same sessions as \"No More Parties in LA\".", "length": "252.59", "fileDate": 15598656, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/73b92e69312795f9d8895c00594c4d2b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/73b92e69312795f9d8895c00594c4d2b\", \"key\": \"Deja Vu\", \"title\": \"\\u2728 Deja Vu\", \"description\": \"<PERSON> <PERSON><PERSON><PERSON> mumbling over most of the beat. Likely made during the same sessions as \\\"No More Parties in LA\\\".\", \"date\": 15598656, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b9a352e74e400ced4862d93ce91c3454\", \"url\": \"https://api.pillowcase.su/api/download/b9a352e74e400ced4862d93ce91c3454\", \"size\": \"4.26 MB\", \"duration\": 252.59}", "aliases": [], "size": "4.26 MB"}, {"id": "don-t-jump", "name": "Don't Jump [V1]", "artists": [], "producers": [], "notes": "Previewed at Yeezy Season 2. Was said to be titled \"<PERSON><PERSON>\", but this name was admitted to be fake by the person who reported it.", "length": "86.26", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/44a9a4c020a24e0cccda4f956d6459d0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/44a9a4c020a24e0cccda4f956d6459d0\", \"key\": \"Don't Jump\", \"title\": \"Don't Jump [V1]\", \"description\": \"Previewed at Yeezy Season 2. Was said to be titled \\\"Lowery\\\", but this name was admitted to be fake by the person who reported it.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2f36b6a7b8ef17412c5bb9ae1737253b\", \"url\": \"https://api.pillowcase.su/api/download/2f36b6a7b8ef17412c5bb9ae1737253b\", \"size\": \"1.59 MB\", \"duration\": 86.26}", "aliases": [], "size": "1.59 MB"}, {"id": "don-t-jump-21", "name": "Don't Jump [V2]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: KW - Don't Jump Ref (7.27.15)\nMadonna version. Snippet posted after someone who private bought the SWISH Madonna sessions got the wrong password.", "length": "5.62", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ae4fcc1d48ed4b865056139057284217", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ae4fcc1d48ed4b865056139057284217\", \"key\": \"Don't Jump\", \"title\": \"Don't Jump [V2]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: KW - Don't Jump Ref (7.27.15)\\nMadonna version. Snippet posted after someone who private bought the SWISH Madonna sessions got the wrong password.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"56e28bd3ab0c20d002b1d24085076c35\", \"url\": \"https://api.pillowcase.su/api/download/56e28bd3ab0c20d002b1d24085076c35\", \"size\": \"302 kB\", \"duration\": 5.62}", "aliases": [], "size": "302 kB"}, {"id": "don-t-jump-22", "name": "✨ Don't Jump [V3]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON>_Dont Jump_Rough_\nVersion that contains short Kanye parts and a <PERSON><PERSON> verse. This is an actual bounce from the engineer. Higher quality copy refound February 23rd, 2025.", "length": "75.63", "fileDate": 15174432, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/0fe1ba53ac768f6d00d94921cd5bfbbd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0fe1ba53ac768f6d00d94921cd5bfbbd\", \"key\": \"Don't Jump\", \"title\": \"\\u2728 Don't Jump [V3]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON>r_Dont Jump_Rough_\\nVersion that contains short Kanye parts and a <PERSON><PERSON> verse. This is an actual bounce from the engineer. Higher quality copy refound February 23rd, 2025.\", \"date\": 15174432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6276ea9cce6e41b5f8676e01aaa2d65f\", \"url\": \"https://api.pillowcase.su/api/download/6276ea9cce6e41b5f8676e01aaa2d65f\", \"size\": \"1.42 MB\", \"duration\": 75.63}", "aliases": [], "size": "1.42 MB"}, {"id": "don-t-jump-23", "name": "Don't Jump [V4]", "artists": [], "producers": [], "notes": "OG Filename: Uzi - Don't Jump Ref\nLil <PERSON> reference track. Leaked after a Soakbuy.", "length": "107.49", "fileDate": 17401824, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/c9adf2b513f384e8ee5c463bdba825d3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9adf2b513f384e8ee5c463bdba825d3\", \"key\": \"Don't Jump\", \"title\": \"Don't Jump [V4]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"OG Filename: Uzi - Don't Jump Ref\\nLil Uzi Vert reference track. Leaked after a Soakbuy.\", \"date\": 17401824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"27f1811875800f34faa2a1c6ca332253\", \"url\": \"https://api.pillowcase.su/api/download/27f1811875800f34faa2a1c6ca332253\", \"size\": \"1.93 MB\", \"duration\": 107.49}", "aliases": [], "size": "1.93 MB"}, {"id": "don-t-jump-24", "name": "Don't Jump [V5]", "artists": [], "producers": [], "notes": "OG Filename: Mez - Don't Jump Ref\n<PERSON> reference track. Leaked after a Soakbuy.", "length": "61.49", "fileDate": 17401824, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ecb057bf4147c6865f9324a1afd12f55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ecb057bf4147c6865f9324a1afd12f55\", \"key\": \"Don't Jump\", \"title\": \"Don't Jump [V5]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: Mez - Don't Jump Ref\\nMez reference track. Leaked after a Soakbuy.\", \"date\": 17401824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"27c1a61d16cbb7ac1edf6b39dce511f8\", \"url\": \"https://api.pillowcase.su/api/download/27c1a61d16cbb7ac1edf6b39dce511f8\", \"size\": \"1.2 MB\", \"duration\": 61.49}", "aliases": [], "size": "1.2 MB"}, {"id": "envious", "name": "Envious", "artists": [], "producers": [], "notes": "<PERSON> reference track from August. Done alongside the others. Unknown when the snippet leaked, or if a Ye version exists.", "length": "13.56", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/6dd71f7119ac628d78935a47dae1a3a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6dd71f7119ac628d78935a47dae1a3a9\", \"key\": \"Envious\", \"title\": \"Envious\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"<PERSON> reference track from August. Done alongside the others. Unknown when the snippet leaked, or if a Ye version exists.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1080c28b05f3ca04dde06a94c169f062\", \"url\": \"https://api.pillowcase.su/api/download/1080c28b05f3ca04dde06a94c169f062\", \"size\": \"429 kB\", \"duration\": 13.56}", "aliases": [], "size": "429 kB"}, {"id": "enya", "name": "✨ Enya [V4]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> (8.16.15)\nHas <PERSON><PERSON><PERSON> stitched onto the Post Malone reference, and an open verse.", "length": "159.9", "fileDate": 17018208, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/986f71a780f0194cd05325ac0a2fd5a4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/986f71a780f0194cd05325ac0a2fd5a4\", \"key\": \"<PERSON><PERSON>\", \"title\": \"\\u2728 <PERSON>ya [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, Charlie Heat & Theophilus London)\", \"aliases\": [\"Waves\"], \"description\": \"OG Filename: KW - <PERSON><PERSON> (8.16.15)\\nHas Theophilus London stitched onto the Post Malone reference, and an open verse.\", \"date\": 17018208, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"90b16559256bcc1d330e91de07598316\", \"url\": \"https://api.pillowcase.su/api/download/90b16559256bcc1d330e91de07598316\", \"size\": \"2.77 MB\", \"duration\": 159.9}", "aliases": ["Waves"], "size": "2.77 MB"}, {"id": "enya-27", "name": "<PERSON><PERSON> [V5]", "artists": ["<PERSON> Malone", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "<PERSON> \"Enya\" freestyle, recorded during sessions with <PERSON><PERSON><PERSON> in August 2015. Features <PERSON><PERSON><PERSON> and has <PERSON><PERSON><PERSON> vocals at the start. <PERSON> does some of the lines that <PERSON> would later use on the song, and a mix of new finished vocals and mumble. Potentially intended to be cut-down into a verse the way his \"Fade\" freestyle was. Samples \"Return To Innocence\" by Enigma. Original snippet leaked August 15th, 2020, and a VC recorded snippet leaked November 6th, 2023.", "length": "958.26", "fileDate": 17018208, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/780a6c1a385e2654d7be3b959f562664", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/780a6c1a385e2654d7be3b959f562664\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V5]\", \"artists\": \"(feat. <PERSON> & Theophilus London) (prod. <PERSON><PERSON><PERSON>, Charlie Heat & Theophilus London)\", \"aliases\": [\"Waves\"], \"description\": \"<PERSON> \\\"Enya\\\" freestyle, recorded during sessions with <PERSON><PERSON><PERSON> in August 2015. Features Theophilus <PERSON> and has <PERSON><PERSON><PERSON> vocals at the start. <PERSON> does some of the lines that <PERSON> would later use on the song, and a mix of new finished vocals and mumble. Potentially intended to be cut-down into a verse the way his \\\"Fade\\\" freestyle was. Samples \\\"Return To Innocence\\\" by Enigma. Original snippet leaked August 15th, 2020, and a VC recorded snippet leaked November 6th, 2023.\", \"date\": 17018208, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4781db50b849eec07190b7a71d06b4b9\", \"url\": \"https://api.pillowcase.su/api/download/4781db50b849eec07190b7a71d06b4b9\", \"size\": \"15.6 MB\", \"duration\": 958.26}", "aliases": ["Waves"], "size": "15.6 MB"}, {"id": "enya-28", "name": "<PERSON><PERSON> [V6]", "artists": [], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> (11.30.15)\nHas <PERSON><PERSON><PERSON> on the hook, mumble lyrics, an open outro, and a slightly different instrumental compared to the Post Malone reference.", "length": "122.23", "fileDate": 16647552, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/b38607b9109f8d4e5056e1fbfc029399", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b38607b9109f8d4e5056e1fbfc029399\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, Charlie Heat & Theophilus London)\", \"aliases\": [\"Waves\"], \"description\": \"OG Filename: KW - <PERSON><PERSON> (11.30.15)\\nHas Kanye on the hook, mumble lyrics, an open outro, and a slightly different instrumental compared to the Post Malone reference.\", \"date\": 16647552, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d4ab5399eb1dc16aaedcdc7f7967400\", \"url\": \"https://api.pillowcase.su/api/download/8d4ab5399eb1dc16aaedcdc7f7967400\", \"size\": \"2.17 MB\", \"duration\": 122.23}", "aliases": ["Waves"], "size": "2.17 MB"}, {"id": "facts", "name": "Facts [V2]", "artists": [], "producers": ["Southside", "Metro Boomin"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON> WEST - FACTS V3 (EXPLICIT)\nOG file for the Soundcloud/non-Charlie Heat version of \"Facts\".", "length": "211.15", "fileDate": 16697664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/5ca3d5a6cadaeb8b3a83bf81af01e4c9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5ca3d5a6cadaeb8b3a83bf81af01e4c9\", \"key\": \"Facts\", \"title\": \"Facts [V2]\", \"artists\": \"(prod. Southside & Metro Boomin)\", \"description\": \"OG Filename: KANYE WEST - FACTS V3 (EXPLICIT)\\nOG file for the Soundcloud/non-Charlie Heat version of \\\"Facts\\\".\", \"date\": 16697664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"09c191eac1e83023cd5a0f34c06d9209\", \"url\": \"https://api.pillowcase.su/api/download/09c191eac1e83023cd5a0f34c06d9209\", \"size\": \"3.59 MB\", \"duration\": 211.15}", "aliases": [], "size": "3.59 MB"}, {"id": "fade", "name": "Fade [V6]", "artists": ["Ty Dolla $ign"], "producers": ["Kanye West"], "notes": "Version heard in the background of a tweet from August 4, 2015. Snippet is instrumental only.", "length": "15.07", "fileDate": 14386464, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ceae8f2ab6348079daada254d2a220bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ceae8f2ab6348079daada254d2a220bb\", \"key\": \"Fade\", \"title\": \"Fade [V6]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Version heard in the background of a tweet from August 4, 2015. Snippet is instrumental only.\", \"date\": 14386464, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"13788858052a0fd8bdc070dd930b371a\", \"url\": \"https://api.pillowcase.su/api/download/13788858052a0fd8bdc070dd930b371a\", \"size\": \"332 kB\", \"duration\": 15.07}", "aliases": [], "size": "332 kB"}, {"id": "fade-31", "name": "Fade [V7]", "artists": ["Ty Dolla $ign"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON> Ref (8.15.15)\nVersion that <PERSON> recorded over. Extremely similar to the August 26th, 2015 file, without the Ye beatbox hi-hats. Can be heard in the first portion of his reference track, similar to <PERSON><PERSON>.", "length": "177", "fileDate": 17032896, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/40bd316e91e4db4c19ebad6971de62aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/40bd316e91e4db4c19ebad6971de62aa\", \"key\": \"Fade\", \"title\": \"Fade [V7]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Fade Ref (8.15.15)\\nVersion that <PERSON> recorded over. Extremely similar to the August 26th, 2015 file, without the Ye beatbox hi-hats. Can be heard in the first portion of his reference track, similar to <PERSON><PERSON>.\", \"date\": 17032896, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"85b7f4c20d1683616472b917abd3d039\", \"url\": \"https://api.pillowcase.su/api/download/85b7f4c20d1683616472b917abd3d039\", \"size\": \"3.04 MB\", \"duration\": 177}", "aliases": [], "size": "3.04 MB"}, {"id": "fade-32", "name": "Fade [V8]", "artists": ["<PERSON> Malone", "Ty Dolla $ign"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "Earliest known version with <PERSON>, recorded after <PERSON>'s eighteenth birthday party. <PERSON> freestyles for around 16 minutes, and his vocals used in the released version are taken from this freestyle. <PERSON> stated that \"[we] went in the studio...we just recorded the scratch vocals and then I wrote over it\". Same intro, chorus, bridge and first verse as the released version but in the part where <PERSON><PERSON><PERSON> says \"I love to... I wanna... I'm tryna...\" on the final version he is mumbling. Original snippet leaked November 7th, 2020.", "length": "1061.33", "fileDate": 17032896, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/7ea5a39f2082c14c217454895ade754d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7ea5a39f2082c14c217454895ade754d\", \"key\": \"Fade\", \"title\": \"Fade [V8]\", \"artists\": \"(feat. <PERSON> & Ty <PERSON> $ign) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"Earliest known version with <PERSON>, recorded after <PERSON>'s eighteenth birthday party. <PERSON> freestyles for around 16 minutes, and his vocals used in the released version are taken from this freestyle. <PERSON> stated that \\\"[we] went in the studio...we just recorded the scratch vocals and then I wrote over it\\\". Same intro, chorus, bridge and first verse as the released version but in the part where <PERSON><PERSON><PERSON> says \\\"I love to... I wanna... I'm tryna...\\\" on the final version he is mumbling. Original snippet leaked November 7th, 2020.\", \"date\": 17032896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"46d913673d3fa19c88fde007aee80315\", \"url\": \"https://api.pillowcase.su/api/download/46d913673d3fa19c88fde007aee80315\", \"size\": \"17.2 MB\", \"duration\": 1061.33}", "aliases": [], "size": "17.2 MB"}, {"id": "fade-33", "name": "Fade [V9]", "artists": ["<PERSON> Malone"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "Cut-down version of <PERSON>'s freestyle, featuring a verse not found in the released version. Only <PERSON>'s raw acapella stem for this version has leaked.", "length": "292.85", "fileDate": 16672608, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ee2faed90b1ce21e7a47ade0e2a7f124", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ee2faed90b1ce21e7a47ade0e2a7f124\", \"key\": \"Fade\", \"title\": \"Fade [V9]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"Cut-down version of <PERSON>'s freestyle, featuring a verse not found in the released version. Only <PERSON>'s raw acapella stem for this version has leaked.\", \"date\": 16672608, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9d4d1ad79ef572d9ed4c6b022b0b0f5b\", \"url\": \"https://api.pillowcase.su/api/download/9d4d1ad79ef572d9ed4c6b022b0b0f5b\", \"size\": \"2.55 MB\", \"duration\": 292.85}", "aliases": [], "size": "2.55 MB"}, {"id": "fade-34", "name": "Fade [V10]", "artists": ["Ty Dolla $ign"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: <PERSON><PERSON> Stems @ 106 BPM\nHas no <PERSON>, and beatbox <PERSON><PERSON><PERSON> drums.", "length": "177.67", "fileDate": 17169408, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/84dbc6a29dc0ec82e70da12b6c0724b3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/84dbc6a29dc0ec82e70da12b6c0724b3\", \"key\": \"Fade\", \"title\": \"Fade [V10]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: Fade Stems @ 106 BPM\\nHas no Post Malone, and beatbox Kanye drums.\", \"date\": 17169408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0cfad5d31e2cad2eb0da73f9838489d9\", \"url\": \"https://api.pillowcase.su/api/download/0cfad5d31e2cad2eb0da73f9838489d9\", \"size\": \"3.05 MB\", \"duration\": 177.67}", "aliases": [], "size": "3.05 MB"}, {"id": "fade-35", "name": "Fade [V12]", "artists": ["Ty Dolla $ign", "<PERSON> Malone"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: Fade M4\nHas a different beat. Was played at Yeezy Season 2.", "length": "199.6", "fileDate": 14557536, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ce1c2d944e9909bce01d1c3387407d2b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ce1c2d944e9909bce01d1c3387407d2b\", \"key\": \"Fade\", \"title\": \"Fade [V12]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: Fade M4\\nHas a different beat. Was played at Yeezy Season 2.\", \"date\": 14557536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3b92466b1aa4a23dd3353e9d79779d34\", \"url\": \"https://api.pillowcase.su/api/download/3b92466b1aa4a23dd3353e9d79779d34\", \"size\": \"3.41 MB\", \"duration\": 199.6}", "aliases": [], "size": "3.41 MB"}, {"id": "fade-36", "name": "Fade [V13]", "artists": ["Ty Dolla $ign", "<PERSON>"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: uzi - fade idea\nHas <PERSON> vocals. Leaked after a groupbuy.", "length": "50.31", "fileDate": 17169408, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/45192e9fde0d1c710b799f164168359b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/45192e9fde0d1c710b799f164168359b\", \"key\": \"Fade\", \"title\": \"Fade [V13]\", \"artists\": \"(feat. <PERSON>gn & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: uzi - fade idea\\nHas <PERSON> vocals. Leaked after a groupbuy.\", \"date\": 17169408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"11c913509440d3f7271368f695c8ef4e\", \"url\": \"https://api.pillowcase.su/api/download/11c913509440d3f7271368f695c8ef4e\", \"size\": \"1.02 MB\", \"duration\": 50.31}", "aliases": [], "size": "1.02 MB"}, {"id": "fade-37", "name": "Fade [V14]", "artists": ["Ty Dolla $ign", "<PERSON>"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: trav uzi fade\nHas better mixed Uzi vocals. Leaked after a groupbuy.", "length": "177.73", "fileDate": 17169408, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/6847d7e9c6f0860c427a9a09c054bc26", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6847d7e9c6f0860c427a9a09c054bc26\", \"key\": \"Fade\", \"title\": \"Fade [V14]\", \"artists\": \"(feat. <PERSON>gn & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: trav uzi fade\\nHas better mixed <PERSON><PERSON> vocals. Leaked after a groupbuy.\", \"date\": 17169408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ee3e1cb42b86ca12e06b0b1e98d80abb\", \"url\": \"https://api.pillowcase.su/api/download/ee3e1cb42b86ca12e06b0b1e98d80abb\", \"size\": \"3.06 MB\", \"duration\": 177.73}", "aliases": [], "size": "3.06 MB"}, {"id": "fade-38", "name": "Fade [V15]", "artists": ["Ty Dolla $ign"], "producers": ["Kanye West", "Benji B", "<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: uzi fade (& miloh)\n<PERSON>h reference track. Leaked after a groupbuy.", "length": "177.74", "fileDate": 17169408, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/50bd5eb26b332fd9e1537f1838ddf41f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/50bd5eb26b332fd9e1537f1838ddf41f\", \"key\": \"Fade\", \"title\": \"Fade [V15]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>ign) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: uzi fade (& miloh)\\nMiloh reference track. Leaked after a groupbuy.\", \"date\": 17169408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"aba2ab57bba2fa73c9cd4b7c437faa98\", \"url\": \"https://api.pillowcase.su/api/download/aba2ab57bba2fa73c9cd4b7c437faa98\", \"size\": \"3.06 MB\", \"duration\": 177.74}", "aliases": [], "size": "3.06 MB"}, {"id": "famous", "name": "⭐ Famous [V4]", "artists": ["<PERSON> Thug"], "producers": ["Kanye West", "<PERSON>", "Havoc"], "notes": "Has a rough Young Thug feature. Originally thought to have been late 2014-early 2015, but likely done in late 2015, as the \"Nina Chop\" and \"Famous\" beats weren't combined until mid 2015.", "length": "235.24", "fileDate": 14555808, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/45fe3a1ce1cab6224549e87fa6de09e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/45fe3a1ce1cab6224549e87fa6de09e4\", \"key\": \"Famous\", \"title\": \"\\u2b50 Famous [V4]\", \"artists\": \"(feat. <PERSON>hug) (prod. <PERSON><PERSON><PERSON>, <PERSON> Heat & Havoc)\", \"aliases\": [\"<PERSON> Chop\"], \"description\": \"Has a rough Young Thug feature. Originally thought to have been late 2014-early 2015, but likely done in late 2015, as the \\\"Nina Chop\\\" and \\\"Famous\\\" beats weren't combined until mid 2015.\", \"date\": 14555808, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b4dadefa58787f60716c2beb1f9c779f\", \"url\": \"https://api.pillowcase.su/api/download/b4dadefa58787f60716c2beb1f9c779f\", \"size\": \"6.18 MB\", \"duration\": 235.24}", "aliases": ["<PERSON>"], "size": "6.18 MB"}, {"id": "father-stretch", "name": "<PERSON> [V3]", "artists": ["<PERSON>"], "producers": ["Kanye West", "Hudson Mohawke", "MIKE DEAN"], "notes": "Has an earlier beat and <PERSON><PERSON><PERSON> mumbling. Has <PERSON>'s vocals that were later used in \"Pt. 2\". The flow of the first verse was later used in \"Pt. 2\". Leaked with stems.", "length": "267.5", "fileDate": 16457472, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/1ad641f7b4355f5248a32bed5cf399fe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ad641f7b4355f5248a32bed5cf399fe\", \"key\": \"<PERSON> Stretch\", \"title\": \"<PERSON>ch [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, Hudson Mohawke & MIKE DEAN)\", \"aliases\": [\"<PERSON> Stretch My Hands\", \"Pt. 1\"], \"description\": \"Has an earlier beat and <PERSON><PERSON><PERSON> mumbling. Has <PERSON>'s vocals that were later used in \\\"Pt. 2\\\". The flow of the first verse was later used in \\\"Pt. 2\\\". Leaked with stems.\", \"date\": 16457472, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eba614378f39b95ee3f82879341b6201\", \"url\": \"https://api.pillowcase.su/api/download/eba614378f39b95ee3f82879341b6201\", \"size\": \"4.49 MB\", \"duration\": 267.5}", "aliases": ["Father <PERSON><PERSON>ch <PERSON>", "Pt. 1"], "size": "4.49 MB"}, {"id": "father-stretch-41", "name": "<PERSON> [V4]", "artists": ["<PERSON>"], "producers": ["Kanye West", "Hudson Mohawke", "MIKE DEAN", "Havoc"], "notes": "Has added Havoc production.", "length": "271.86", "fileDate": 16457472, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/c1b0ec3a1cb5e1bd7c968ab4c4624844", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1b0ec3a1cb5e1bd7c968ab4c4624844\", \"key\": \"<PERSON> Stretch\", \"title\": \"<PERSON> St<PERSON>ch [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, MIKE DEAN & Havoc)\", \"aliases\": [\"Father Stretch My Hands\", \"Pt. 1\"], \"description\": \"Has added Havoc production.\", \"date\": 16457472, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fbb15ca258b2996aa2102c9916f65c98\", \"url\": \"https://api.pillowcase.su/api/download/fbb15ca258b2996aa2102c9916f65c98\", \"size\": \"4.56 MB\", \"duration\": 271.86}", "aliases": ["Father <PERSON><PERSON>ch <PERSON>", "Pt. 1"], "size": "4.56 MB"}, {"id": "liberated", "name": "⭐ Liberated [V5]", "artists": ["<PERSON><PERSON>"], "producers": ["Kanye West", "Hudson Mohawke", "MIKE DEAN"], "notes": "OG Filename: <PERSON><PERSON><PERSON>r_Liberated_Rough 1.0\nVersion with <PERSON><PERSON>. Engineer did not include the full song, just <PERSON><PERSON>'s verse and some <PERSON><PERSON><PERSON> vocals. Has slightly updated drums from previous versions.", "length": "153.68", "fileDate": 15174432, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/5c9aec42ee22f8a2bd29fd7085b6c1c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5c9aec42ee22f8a2bd29fd7085b6c1c0\", \"key\": \"Liberated\", \"title\": \"\\u2b50 Liberated [V5]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, Hudson Mohawke & <PERSON>)\", \"aliases\": [\"Father Stretch My Hands\", \"Father Stretch\", \"Pt. 1\"], \"description\": \"OG Filename: KLamar_Liberated_Rough 1.0\\nVersion with <PERSON><PERSON>. Engineer did not include the full song, just <PERSON><PERSON>'s verse and some <PERSON><PERSON><PERSON> vocals. Has slightly updated drums from previous versions.\", \"date\": 15174432, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"95e2aa79c98a93907d402b2cdf6466c4\", \"url\": \"https://api.pillowcase.su/api/download/95e2aa79c98a93907d402b2cdf6466c4\", \"size\": \"2.67 MB\", \"duration\": 153.68}", "aliases": ["Father <PERSON><PERSON>ch <PERSON>", "<PERSON>", "Pt. 1"], "size": "2.67 MB"}, {"id": "fml", "name": "✨ FML [V2]", "artists": ["<PERSON>"], "producers": ["Hudson Mohawke"], "notes": "OG Filename: FML @ 120 BPM Dm\nHas alternate production, two full no-mumble <PERSON> verses, and him doing a ref for <PERSON><PERSON><PERSON> on the hook that was used in later versions. Some Kanye vocals are at the end. Sam<PERSON> \"Karma (Hardline)\" by <PERSON>. Leaked after a successful Soakbuy.", "length": "192.73", "fileDate": 17390592, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/07c5d21bc2cd65a88fbb45acaab97191", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/07c5d21bc2cd65a88fbb45acaab97191\", \"key\": \"FML\", \"title\": \"\\u2728 FML [V2]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: FML @ 120 BPM Dm\\nHas alternate production, two full no-mumble <PERSON> verses, and him doing a ref for <PERSON><PERSON><PERSON> on the hook that was used in later versions. Some Kanye vocals are at the end. <PERSON><PERSON> \\\"Karma (Hardline)\\\" by <PERSON>. Leaked after a successful Soakbuy.\", \"date\": 17390592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"afa399c977f9bb70c5a470a5e54bb2b4\", \"url\": \"https://api.pillowcase.su/api/download/afa399c977f9bb70c5a470a5e54bb2b4\", \"size\": \"3.3 MB\", \"duration\": 192.73}", "aliases": [], "size": "3.3 MB"}, {"id": "fmlu", "name": "FMLU [V3]", "artists": ["<PERSON> Thug"], "producers": ["Hudson Mohawke"], "notes": "OG Filename: FMLU HM\nVersion featuring <PERSON>hug. Unknown exactly when it was made, but it uses the same instrumental as the <PERSON> reference. Originally was played in a Che Pope livestream, but a longer recorded snippet of the livestream didn't surface until July 2022. 22-second long snippet later leaked December 15th, 2023.", "length": "21.76", "fileDate": 17025984, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/02f9136fb012974794b28f02140562da", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/02f9136fb012974794b28f02140562da\", \"key\": \"FMLU\", \"title\": \"FMLU [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"FML\"], \"description\": \"OG Filename: FMLU HM\\nVersion featuring <PERSON>hu<PERSON>. Unknown exactly when it was made, but it uses the same instrumental as the <PERSON> reference. Originally was played in a Che Pope livestream, but a longer recorded snippet of the livestream didn't surface until July 2022. 22-second long snippet later leaked December 15th, 2023.\", \"date\": 17025984, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"78c7a07060f48e074a40d7668bae520a\", \"url\": \"https://api.pillowcase.su/api/download/78c7a07060f48e074a40d7668bae520a\", \"size\": \"385 kB\", \"duration\": 21.76}", "aliases": ["FML"], "size": "385 kB"}, {"id": "fml-45", "name": "FML [V4]", "artists": ["<PERSON>"], "producers": ["Hudson Mohawke"], "notes": "First version with a <PERSON><PERSON><PERSON> verse. Has a mumble verse and hook, and different production from other versions of the song. After the cutoff there is said to be a <PERSON> verse, however it's unknown if that is a separate version. Leaked with stems after a groupbuy.", "length": "81.74", "fileDate": 16443648, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/3c831e1ff652455e62f56c9e717cadf4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3c831e1ff652455e62f56c9e717cadf4\", \"key\": \"FML\", \"title\": \"FML [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"First version with a Kanye verse. Has a mumble verse and hook, and different production from other versions of the song. After the cutoff there is said to be a <PERSON> verse, however it's unknown if that is a separate version. Leaked with stems after a groupbuy.\", \"date\": 16443648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4bcfc01146ee03e5ce55dffa1d76d8f6\", \"url\": \"https://api.pillowcase.su/api/download/4bcfc01146ee03e5ce55dffa1d76d8f6\", \"size\": \"1.52 MB\", \"duration\": 81.74}", "aliases": [], "size": "1.52 MB"}, {"id": "fml-46", "name": "FML [V5]", "artists": ["<PERSON>"], "producers": ["Hudson Mohawke"], "notes": "Has different <PERSON><PERSON><PERSON> vocals from the previous version, along with instrumental differences. No open verse. Leaked after a blind groupbuy.", "length": "173.66", "fileDate": 16517952, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/9c927f430fb0944c6830ef68dfc4c213", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c927f430fb0944c6830ef68dfc4c213\", \"key\": \"FML\", \"title\": \"FML [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"Has different <PERSON><PERSON><PERSON> vocals from the previous version, along with instrumental differences. No open verse. Leaked after a blind groupbuy.\", \"date\": 16517952, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6fea20e13d307d34edc79fabcc064f3c\", \"url\": \"https://api.pillowcase.su/api/download/6fea20e13d307d34edc79fabcc064f3c\", \"size\": \"2.99 MB\", \"duration\": 173.66}", "aliases": [], "size": "2.99 MB"}, {"id": "fml-47", "name": "⭐ FML [V6]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "Hudson Mohawke"], "notes": "OG Filename: FML DK with VOX\nHas a more energetic <PERSON><PERSON><PERSON> vocal take, <PERSON> production and an open verse. Commonly referred to as the \"Shogani\" version, with how often he has played it in VC.", "length": "292.75", "fileDate": 16535232, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/499333cebe7f5c27c63b8828010d9f00", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/499333cebe7f5c27c63b8828010d9f00\", \"key\": \"FML\", \"title\": \"\\u2b50 FML [V6]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> & <PERSON>e)\", \"description\": \"OG Filename: FML DK with VOX\\nHas a more energetic <PERSON><PERSON><PERSON> vocal take, <PERSON> production and an open verse. Commonly referred to as the \\\"Shogani\\\" version, with how often he has played it in VC.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d80aca5aa7021dc722a7a84b8036c877\", \"url\": \"https://api.pillowcase.su/api/download/d80aca5aa7021dc722a7a84b8036c877\", \"size\": \"4.9 MB\", \"duration\": 292.75}", "aliases": [], "size": "4.9 MB"}, {"id": "forever", "name": "Forever [V4]", "artists": [], "producers": ["<PERSON>", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> FOREVER VOX DK\n<PERSON> version of \"Forever\". Has different drums, an additional horn section and a string arrangement. Has the same vocals (bounced in July 2015).", "length": "142.94", "fileDate": 16749504, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/b21461fedcda4a62d4b06cc68e227f3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b21461fedcda4a62d4b06cc68e227f3c\", \"key\": \"Forever\", \"title\": \"Forever [V4]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW FOREVER VOX DK\\nDarren <PERSON> version of \\\"Forever\\\". Has different drums, an additional horn section and a string arrangement. Has the same vocals (bounced in July 2015).\", \"date\": 16749504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f12b379fd2abd80adbb71eb4d24bb50d\", \"url\": \"https://api.pillowcase.su/api/download/f12b379fd2abd80adbb71eb4d24bb50d\", \"size\": \"2.5 MB\", \"duration\": 142.94}", "aliases": [], "size": "2.5 MB"}, {"id": "changing", "name": "<PERSON> - Changing [V5]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON>_<PERSON><PERSON> Ref Vox_01\n<PERSON><PERSON> reference track. The \"Forever\" beat was given to <PERSON> for use on her 2018 album, Liberation, but was cut as she never recorded. Original snippet leaked January 25th, 2023.", "length": "95.42", "fileDate": 16771968, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/2d4033988297931074414df0806e0d40", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d4033988297931074414df0806e0d40\", \"key\": \"Changing\", \"title\": \"<PERSON> - Changing [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Forever\", \"Changes\"], \"description\": \"OG Filename: Changing_<PERSON>sey Ref Vox_01\\nIlsey Juber reference track. The \\\"Forever\\\" beat was given to <PERSON> for use on her 2018 album, Liberation, but was cut as she never recorded. Original snippet leaked January 25th, 2023.\", \"date\": 16771968, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9791e2077ac0cc26816dc5ce7940e911\", \"url\": \"https://api.pillowcase.su/api/download/9791e2077ac0cc26816dc5ce7940e911\", \"size\": \"1.74 MB\", \"duration\": 95.42}", "aliases": ["Forever", "Changes"], "size": "1.74 MB"}, {"id": "fuck-the-internet", "name": "Fuck The Internet [V3]", "artists": ["BloodPop®"], "producers": ["Da<PERSON>", "Kanye West"], "notes": "Solo Post Malone freestyle, with BloodPop® background vocals. A part of the August 2015 <PERSON> and <PERSON><PERSON><PERSON> sessions. <PERSON> has said that \"<PERSON><PERSON><PERSON> produced it and did the melody and shit. Had the whole idea for it, it was called \"Instalove\". Original snippet leaked on November 7th, 2020.", "length": "512.06", "fileDate": 17032896, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/968a9f580c527560ac9a82908bdf52e2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/968a9f580c527560ac9a82908bdf52e2\", \"key\": \"Fuck The Internet\", \"title\": \"Fuck The Internet [V3]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON><PERSON><PERSON>\\u00ae) (prod. <PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Internet\", \"Instalove\"], \"description\": \"Solo Post Malone freestyle, with BloodPop\\u00ae background vocals. A part of the August 2015 <PERSON> and <PERSON><PERSON><PERSON> sessions. <PERSON> has said that \\\"<PERSON><PERSON><PERSON> produced it and did the melody and shit. Had the whole idea for it, it was called \\\"Instalove\\\". Original snippet leaked on November 7th, 2020.\", \"date\": 17032896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8b0a505efc1fec5f5c54e8aa42c6bdb8\", \"url\": \"https://api.pillowcase.su/api/download/8b0a505efc1fec5f5c54e8aa42c6bdb8\", \"size\": \"8.41 MB\", \"duration\": 512.06}", "aliases": ["Internet", "Instalove"], "size": "8.41 MB"}, {"id": "fuck-the-internet-51", "name": "✨ <PERSON> Malone - Fuck The Internet [V4]", "artists": ["Kanye West"], "producers": ["Da<PERSON>", "Kanye West"], "notes": "Version with an unfinished <PERSON><PERSON><PERSON> verse and hook. His vocals possibly came from the 2014 Madonna sessions, where BloodPop® was working with <PERSON><PERSON>. A version without <PERSON><PERSON><PERSON> leaked in 2018 and was released for <PERSON>'s 2019 album, Hollywood's Bleeding.", "length": "251.53", "fileDate": 15586560, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/3b95df757fef4505e10f82044466c13b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3b95df757fef4505e10f82044466c13b\", \"key\": \"Fuck The Internet\", \"title\": \"\\u2728 <PERSON> - Fuck The Internet [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Internet\", \"Instalove\"], \"description\": \"Version with an unfinished Ka<PERSON><PERSON> verse and hook. His vocals possibly came from the 2014 Madonna sessions, where <PERSON><PERSON><PERSON>\\u<PERSON><PERSON> was working with <PERSON><PERSON>. A version without <PERSON><PERSON><PERSON> leaked in 2018 and was released for <PERSON>'s 2019 album, Hollywood's Bleeding.\", \"date\": 15586560, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"99faf1cead11fd16bba3f9691962a7c3\", \"url\": \"https://api.pillowcase.su/api/download/99faf1cead11fd16bba3f9691962a7c3\", \"size\": \"4.24 MB\", \"duration\": 251.53}", "aliases": ["Internet", "Instalove"], "size": "4.24 MB"}, {"id": "high-life", "name": "High Life [V2]", "artists": [], "producers": [], "notes": "<PERSON> reference track. From the 2015 <PERSON> and <PERSON><PERSON><PERSON> sessions. Original snippet leaked November 7th, 2020.", "length": "128.73", "fileDate": 17032896, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/3a0e4ce9e39a39e80f1982fb6cbe6aeb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a0e4ce9e39a39e80f1982fb6cbe6aeb\", \"key\": \"High Life\", \"title\": \"High Life [V2]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"<PERSON> reference track. From the 2015 <PERSON> and <PERSON><PERSON><PERSON> sessions. Original snippet leaked November 7th, 2020.\", \"date\": 17032896, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"72053392069bbd69c818f110d789a7d2\", \"url\": \"https://api.pillowcase.su/api/download/72053392069bbd69c818f110d789a7d2\", \"size\": \"2.27 MB\", \"duration\": 128.73}", "aliases": [], "size": "2.27 MB"}, {"id": "highlights", "name": "Highlights [V4]", "artists": ["<PERSON>"], "producers": ["Velous"], "notes": "Made sometime in early September 2015 (<PERSON><PERSON> also said that he was only brought on \"a few weeks after <PERSON> released\" and <PERSON> dropped in Aug 2015). Has a similar beat to the first SWISH version but with more drums, a Madonna feature and <PERSON><PERSON> reference vocals. The first verse, while short, is finished and partially reused on the finale version, while the second verse is complete mumble and seemingly wasn't revisited on later versions. Has <PERSON><PERSON>'s reference vocals layered over <PERSON><PERSON><PERSON>'s mumble verse.", "length": "145.87", "fileDate": 14554080, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/6af0c0ce677ca560292e1a453b7fd803", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6af0c0ce677ca560292e1a453b7fd803\", \"key\": \"Highlights\", \"title\": \"Highlights [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON>) (prod. Velous)\", \"aliases\": [\"Go Pro\"], \"description\": \"Made sometime in early September 2015 (<PERSON><PERSON> also said that he was only brought on \\\"a few weeks after <PERSON> released\\\" and <PERSON> dropped in Aug 2015). Has a similar beat to the first SWISH version but with more drums, a <PERSON> feature and <PERSON><PERSON> reference vocals. The first verse, while short, is finished and partially reused on the finale version, while the second verse is complete mumble and seemingly wasn't revisited on later versions. Has <PERSON><PERSON>'s reference vocals layered over <PERSON><PERSON><PERSON>'s mumble verse.\", \"date\": 14554080, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4b936464c2357f093e1cc839c68d269b\", \"url\": \"https://api.pillowcase.su/api/download/4b936464c2357f093e1cc839c68d269b\", \"size\": \"4.74 MB\", \"duration\": 145.87}", "aliases": ["Go Pro"], "size": "4.74 MB"}, {"id": "highlights-54", "name": "Highlights [V7]", "artists": ["The WRLDFMS <PERSON>", "<PERSON> Thug"], "producers": [], "notes": "Has an early version of the first verse and a mostly completely alternate half finished, half mumble second verse. Instrumental has a different bassline and is more simplistic. Leaked with stems. Linked file is an unofficial bounce of the stems.", "length": "233.32", "fileDate": 16417728, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/7272da00f3c2923d34321819d186cbb9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7272da00f3c2923d34321819d186cbb9\", \"key\": \"Highlights\", \"title\": \"Highlights [V7]\", \"artists\": \"(feat. The WRLDFMS Tony <PERSON> & Young Thug)\", \"aliases\": [\"Go Pro\"], \"description\": \"Has an early version of the first verse and a mostly completely alternate half finished, half mumble second verse. Instrumental has a different bassline and is more simplistic. Leaked with stems. Linked file is an unofficial bounce of the stems.\", \"date\": 16417728, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c538920d7a83d84fb2ada9a5ae626292\", \"url\": \"https://api.pillowcase.su/api/download/c538920d7a83d84fb2ada9a5ae626292\", \"size\": \"3.95 MB\", \"duration\": 233.32}", "aliases": ["Go Pro"], "size": "3.95 MB"}, {"id": "highlights-55", "name": "Highlights [V8]", "artists": ["The WRLDFMS <PERSON>", "<PERSON> Thug"], "producers": ["Havoc"], "notes": "Same as the above version, but with added Havoc production.", "length": "233.31", "fileDate": 16417728, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ed5b44e63a73a75e44ebbff8402c1cb3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ed5b44e63a73a75e44ebbff8402c1cb3\", \"key\": \"Highlights\", \"title\": \"Highlights [V8]\", \"artists\": \"(feat. The WRLDFMS <PERSON> & <PERSON> Thug) (prod. Havoc)\", \"aliases\": [\"Go Pro\"], \"description\": \"Same as the above version, but with added Havoc production.\", \"date\": 16417728, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"96c21524091f46fb821e584e7be1828f\", \"url\": \"https://api.pillowcase.su/api/download/96c21524091f46fb821e584e7be1828f\", \"size\": \"3.95 MB\", \"duration\": 233.31}", "aliases": ["Go Pro"], "size": "3.95 MB"}, {"id": "highlights-56", "name": "✨ Highlights [V9]", "artists": ["The WRLDFMS <PERSON>", "<PERSON> Thug"], "producers": ["<PERSON>"], "notes": "<PERSON> version. Has additional production, drums and samples compared to release. Still has a semi-finished second verse. Was leaked for free.", "length": "307.26", "fileDate": 16509312, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/3964a8c605341ed233543df606244525", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3964a8c605341ed233543df606244525\", \"key\": \"Highlights\", \"title\": \"\\u2728 Highlights [V9]\", \"artists\": \"(feat. The WRLDFMS <PERSON> & <PERSON> Thug) (prod. <PERSON>)\", \"aliases\": [\"Go Pro\"], \"description\": \"Darren <PERSON> version. Has additional production, drums and samples compared to release. Still has a semi-finished second verse. Was leaked for free.\", \"date\": 16509312, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"96cdb0336c95721ef3ae722cfe4d32c5\", \"url\": \"https://api.pillowcase.su/api/download/96cdb0336c95721ef3ae722cfe4d32c5\", \"size\": \"5.13 MB\", \"duration\": 307.26}", "aliases": ["Go Pro"], "size": "5.13 MB"}, {"id": "hold-tight", "name": "Hold Tight [V2]", "artists": [], "producers": [], "notes": "OG Filename: uzi - hold tight\nLil <PERSON><PERSON> reference track. Features a different version of the beat than what was used on the Migos and Young Thug version. Leaked after a groupbuy.", "length": "136.8", "fileDate": 17169408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/5882eb38e0df4e3570a50f8f0f53380c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5882eb38e0df4e3570a50f8f0f53380c\", \"key\": \"Hold Tight\", \"title\": \"Hold Tight [V2]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"OG Filename: uzi - hold tight\\nLil Uzi Vert reference track. Features a different version of the beat than what was used on the Migos and Young Thug version. Leaked after a groupbuy.\", \"date\": 17169408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e5ab783a9ca58cab7fe8ebd2b7e8c38d\", \"url\": \"https://api.pillowcase.su/api/download/e5ab783a9ca58cab7fe8ebd2b7e8c38d\", \"size\": \"2.4 MB\", \"duration\": 136.8}", "aliases": [], "size": "2.4 MB"}, {"id": "i-feel-like-that", "name": "I Feel Like That [V12]", "artists": ["<PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: KW - I Feel Like That Ref 1 (7.28.15)\nLate July 2015 Madonna version of \"I Feel Like That\". Snippet posted after someone who private bought the SWISH Madonna sessions got the wrong password.", "length": "7.73", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/b9efce1c8a8d603179d0390a6be5b0dd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b9efce1c8a8d603179d0390a6be5b0dd\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V12]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: KW - I Feel Like That Ref 1 (7.28.15)\\nLate July 2015 Madonna version of \\\"I Feel Like That\\\". Snippet posted after someone who private bought the SWISH Madonna sessions got the wrong password.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a5bf9f1b3a8e4bd3b54bc170fca02fd1\", \"url\": \"https://api.pillowcase.su/api/download/a5bf9f1b3a8e4bd3b54bc170fca02fd1\", \"size\": \"336 kB\", \"duration\": 7.73}", "aliases": [], "size": "336 kB"}, {"id": "i-feel-like-that-59", "name": "I Feel Like That [V13]", "artists": ["<PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: KW - I Feel Like That Ref 2 (7.28.15)\nAnother late July 2015 Madonna version of \"I Feel Like That\". Snippet posted after someone who private bought the SWISH Madonna sessions got the wrong password.", "length": "7.11", "fileDate": 17288640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/0b95ecd42540c7115eff2b8866cc7d99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b95ecd42540c7115eff2b8866cc7d99\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V13]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: KW - I Feel Like That Ref 2 (7.28.15)\\nAnother late July 2015 Madonna version of \\\"I Feel Like That\\\". Snippet posted after someone who private bought the SWISH Madonna sessions got the wrong password.\", \"date\": 17288640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"275fe0129f4abadd7dab2c95f77f9373\", \"url\": \"https://api.pillowcase.su/api/download/275fe0129f4abadd7dab2c95f77f9373\", \"size\": \"326 kB\", \"duration\": 7.11}", "aliases": [], "size": "326 kB"}, {"id": "motivated", "name": "Motivated [V1]", "artists": [], "producers": ["TM88"], "notes": "Solo Kanye version, containing an open verse. Leaked after producer TM88 got hacked.", "length": "180.19", "fileDate": 14962752, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/448dba0d260e757c861cacea16996f8e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/448dba0d260e757c861cacea16996f8e\", \"key\": \"Motivated\", \"title\": \"Motivated [V1]\", \"artists\": \"(prod. TM88)\", \"aliases\": [\"Euro 2\", \"Switch Hands\"], \"description\": \"Solo Kanye version, containing an open verse. Leaked after producer TM88 got hacked.\", \"date\": 14962752, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"70e8598a3fff3a9b96d9724e22ff187c\", \"url\": \"https://api.pillowcase.su/api/download/70e8598a3fff3a9b96d9724e22ff187c\", \"size\": \"3.09 MB\", \"duration\": 180.19}", "aliases": ["Euro 2", "Switch Hands"], "size": "3.09 MB"}, {"id": "motivated-61", "name": "✨ A$AP Rocky - Motivated [V2]", "artists": ["Kanye West"], "producers": ["TM88"], "notes": "OG Filename: EURO2_Motivated - <PERSON><PERSON> (Switch Hands)\nHas a different song structure. Portions of the track don't feature vocals with minor effects differences. Song was given to <PERSON>, proven by the original filename. Unedited file leaked in December 2022.", "length": "181.69", "fileDate": 15552864, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/223ff794579a457e84ea88ef3ca082f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/223ff794579a457e84ea88ef3ca082f3\", \"key\": \"Motivated\", \"title\": \"\\u2728 A$AP Rocky - Motivated [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. TM88)\", \"aliases\": [\"Euro 2\", \"Switch Hands\"], \"description\": \"OG Filename: EURO2_Motivated - Asap (Switch Hands)\\nHas a different song structure. Portions of the track don't feature vocals with minor effects differences. Song was given to <PERSON>, proven by the original filename. Unedited file leaked in December 2022.\", \"date\": 15552864, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fb10099bd2fda559a8bf33f0e75fedde\", \"url\": \"https://api.pillowcase.su/api/download/fb10099bd2fda559a8bf33f0e75fedde\", \"size\": \"3.12 MB\", \"duration\": 181.69}", "aliases": ["Euro 2", "Switch Hands"], "size": "3.12 MB"}, {"id": "no-more-parties-in-la", "name": "No More Parties in LA [V3]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: KW - No More Parties In LA Ref (9.2.15)\nHas the <PERSON><PERSON><PERSON> vocals up until the spray tan line, then they rest is open. Leaked as a bonus for a Soakbuy.", "length": "219.63", "fileDate": 17401824, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/8fe38dfa37f1eae8eeb45186a9307546", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8fe38dfa37f1eae8eeb45186a9307546\", \"key\": \"No More Parties in LA\", \"title\": \"No More Parties in LA [V3]\", \"artists\": \"(prod. Madlib)\", \"aliases\": [\"No Parties In LA\"], \"description\": \"OG Filename: KW - No More Parties In LA Ref (9.2.15)\\nHas the Kanye vocals up until the spray tan line, then they rest is open. Leaked as a bonus for a Soakbuy.\", \"date\": 17401824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e6683351e4d0ed828e7740f335d3c0bf\", \"url\": \"https://api.pillowcase.su/api/download/e6683351e4d0ed828e7740f335d3c0bf\", \"size\": \"3.73 MB\", \"duration\": 219.63}", "aliases": ["No Parties In LA"], "size": "3.73 MB"}, {"id": "no-more-parties-in-la-63", "name": "No More Parties In LA [V4]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Late 2015 version. Was thought to be from 2010, but was actually recorded in late 2015 as a photo from <PERSON> has been found, with <PERSON><PERSON><PERSON> wearing the same outfit as can be seen in the video.", "length": "15.09", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/8c22618dd635ae0a59b8a3c1d96cf839", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8c22618dd635ae0a59b8a3c1d96cf839\", \"key\": \"No More Parties In LA\", \"title\": \"No More Parties In LA [V4]\", \"artists\": \"(prod. Madlib)\", \"aliases\": [\"No Parties In LA\"], \"description\": \"Late 2015 version. Was thought to be from 2010, but was actually recorded in late 2015 as a photo from <PERSON> has been found, with <PERSON><PERSON><PERSON> wearing the same outfit as can be seen in the video.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d7284e782dc5e91f7efffe28394147a0\", \"url\": \"https://api.pillowcase.su/api/download/d7284e782dc5e91f7efffe28394147a0\", \"size\": \"332 kB\", \"duration\": 15.09}", "aliases": ["No Parties In LA"], "size": "332 kB"}, {"id": "no-more-parties-in-la-64", "name": "🗑️ No More Parties In LA [V5]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Uzi - No More Parties In LA Ref\nVery short reference done by <PERSON>. <PERSON><PERSON> mumble raps 2 lines and then asks for the autotune to be turned down. Leaked as a bonus for a Soakbuy.", "length": "51.54", "fileDate": 17401824, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/d05286abc18c3879c14a671b009d9f3f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d05286abc18c3879c14a671b009d9f3f\", \"key\": \"No More Parties In LA\", \"title\": \"\\ud83d\\uddd1\\ufe0f No More Parties In LA [V5]\", \"artists\": \"(ref. <PERSON>) (prod. Madlib)\", \"aliases\": [\"No Parties In LA\"], \"description\": \"OG Filename: Uzi - No More Parties In LA Ref\\nVery short reference done by <PERSON>. <PERSON><PERSON> mumble raps 2 lines and then asks for the autotune to be turned down. Leaked as a bonus for a Soakbuy.\", \"date\": 17401824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b65b7ff245f6bb9490845d644afae9e7\", \"url\": \"https://api.pillowcase.su/api/download/b65b7ff245f6bb9490845d644afae9e7\", \"size\": \"1.04 MB\", \"duration\": 51.54}", "aliases": ["No Parties In LA"], "size": "1.04 MB"}, {"id": "no-more-parties-in-la-65", "name": "No More Parties In LA [V6]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: KW - No More Parties In LA Ref (10.22.15)\nShort Gizzle reference track. Leaked as a bonus for a Soakbuy.", "length": "54.73", "fileDate": 17401824, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/e8f81567a2bc60b9ff6cbf75fb8e90f5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8f81567a2bc60b9ff6cbf75fb8e90f5\", \"key\": \"No More Parties In LA\", \"title\": \"No More Parties In LA [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Madlib)\", \"aliases\": [\"No Parties In LA\"], \"description\": \"OG Filename: KW - No More Parties In LA Ref (10.22.15)\\nShort Gizzle reference track. Leaked as a bonus for a Soakbuy.\", \"date\": 17401824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd48d2eb7a9dedb2fe0c9dc74c678bc8\", \"url\": \"https://api.pillowcase.su/api/download/bd48d2eb7a9dedb2fe0c9dc74c678bc8\", \"size\": \"1.09 MB\", \"duration\": 54.73}", "aliases": ["No Parties In LA"], "size": "1.09 MB"}, {"id": "only", "name": "Only [V1]", "artists": [], "producers": [], "notes": "Solo Kanye version. Reuses the \"Nike sign\" line from \"Fall Out Of Heaven\". Was listed by Music Mafia but never sold. A new snippet leaked April 2023.", "length": "15.49", "fileDate": 16828128, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/520d4927fd4c5c505a701bb9f700abb7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/520d4927fd4c5c505a701bb9f700abb7\", \"key\": \"Only\", \"title\": \"Only [V1]\", \"aliases\": [\"Make U Love Me\"], \"description\": \"Solo Kanye version. Reuses the \\\"Nike sign\\\" line from \\\"Fall Out Of Heaven\\\". Was listed by Music Mafia but never sold. A new snippet leaked April 2023.\", \"date\": 16828128, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ee932f47c9cfcad90e1bb2921ea72d89\", \"url\": \"https://api.pillowcase.su/api/download/ee932f47c9cfcad90e1bb2921ea72d89\", \"size\": \"460 kB\", \"duration\": 15.49}", "aliases": ["Make U Love Me"], "size": "460 kB"}, {"id": "only-67", "name": "Only [V1]", "artists": [], "producers": [], "notes": "Solo Kanye version. Reuses the \"Nike sign\" line from \"Fall Out Of Heaven\". Was listed by Music Mafia but never sold. A new snippet leaked April 2023.", "length": "14.5", "fileDate": 16828128, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ba811672d8bcb3dc9396d2c77c7e73cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ba811672d8bcb3dc9396d2c77c7e73cf\", \"key\": \"Only\", \"title\": \"Only [V1]\", \"aliases\": [\"Make U Love Me\"], \"description\": \"Solo Kanye version. Reuses the \\\"Nike sign\\\" line from \\\"Fall Out Of Heaven\\\". Was listed by Music Mafia but never sold. A new snippet leaked April 2023.\", \"date\": 16828128, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"45d792914e02f33711b38ba9e589d50d\", \"url\": \"https://api.pillowcase.su/api/download/45d792914e02f33711b38ba9e589d50d\", \"size\": \"444 kB\", \"duration\": 14.5}", "aliases": ["Make U Love Me"], "size": "444 kB"}, {"id": "only-68", "name": "✨ Only [V3]", "artists": [], "producers": [], "notes": "OG Filename: Only Ye Reference-TYGA\nSong was thought to be titled \"Only Ye\", but it's actually a reference for \"Only\", as <PERSON><PERSON> raps a verse intended for <PERSON><PERSON><PERSON>. Most likely came from the same studio session as \"Rich Nigga Drunk\" and \"Bad Night\". This is a more finished version compared to the other solo snippet of this song that leaked from Music Mafia.", "length": "234.53", "fileDate": 15391296, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/dbbcdd7f7df54b381eeacd53cd98477a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dbbcdd7f7df54b381eeacd53cd98477a\", \"key\": \"Only\", \"title\": \"\\u2728 Only [V3]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Make U Love Me\"], \"description\": \"OG Filename: Only Ye Reference-TYGA\\nSong was thought to be titled \\\"Only Ye\\\", but it's actually a reference for \\\"Only\\\", as <PERSON><PERSON> raps a verse intended for <PERSON><PERSON><PERSON>. Most likely came from the same studio session as \\\"Rich Nigga Drunk\\\" and \\\"Bad Night\\\". This is a more finished version compared to the other solo snippet of this song that leaked from Music Mafia.\", \"date\": 15391296, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0564b71e61bfcf6df9d71109fe3550a3\", \"url\": \"https://api.pillowcase.su/api/download/0564b71e61bfcf6df9d71109fe3550a3\", \"size\": \"3.97 MB\", \"duration\": 234.53}", "aliases": ["Make U Love Me"], "size": "3.97 MB"}, {"id": "over", "name": "⭐ Over [V14]", "artists": ["<PERSON>", "The-Dream"], "producers": ["Daft Punk"], "notes": "OG Filename: KW - <PERSON> Ref (8.16.15)\nFilename seen in a folder of August 2015 tracks. Cut-down version of \"Can't Get Over Me\" with some production elements removed, less <PERSON> vocals, and re-recorded verses from <PERSON><PERSON><PERSON>, with some alternate lyrics. Still contains mumble. \n\nTLDR: There is more evidence that the song is \"Over\" than there is that the song is \"Fall Out Of Heaven.\"\nOriginally leaked as \"Fall Out Of Heaven,\" but as The Germans also originally said \"Pressure\" was titled \"Can U Be,\" it makes sense that they would rename this track as well (as both titles \"Pressure\" and \"Over\" aren't vocally stated in the respective tracks). This version of the song also contains lyrics shared with \"Famous\" and \"Only\" (both recorded in late 2015) as well as a sample later used on \"2 Rihannas\" and \"FML\" (added to both in late 2015). The Germans are confirmed to have tracks from the August 2015 folder (in the tracklists tab) such as \"Prayer\" and \"Ultimate Lie,\" as well as Post Malone references for some of those August 2015 files. Their version of \"Pressure\" is likely also August 2015 based on the stems we have for the leaked version. Most of the leaked German files for the LP7 era are late 2015 as well.", "length": "232.83", "fileDate": 14557536, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/f052e896370192500f7a50dfac760e2f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f052e896370192500f7a50dfac760e2f\", \"key\": \"Over\", \"title\": \"\\u2b50 Over [V14]\", \"artists\": \"(feat. <PERSON> & <PERSON>-Dream) (prod. Daft Punk)\", \"aliases\": [\"Can't Get Over Me\", \"Fall Out Of Heaven\"], \"description\": \"OG Filename: KW - Over Ref (8.16.15)\\nFilename seen in a folder of August 2015 tracks. Cut-down version of \\\"Can't Get Over Me\\\" with some production elements removed, less <PERSON> vocals, and re-recorded verses from <PERSON><PERSON><PERSON>, with some alternate lyrics. Still contains mumble. \\n\\nTLDR: There is more evidence that the song is \\\"Over\\\" than there is that the song is \\\"Fall Out Of Heaven.\\\"\\nOriginally leaked as \\\"Fall Out Of Heaven,\\\" but as The Germans also originally said \\\"Pressure\\\" was titled \\\"Can U Be,\\\" it makes sense that they would rename this track as well (as both titles \\\"Pressure\\\" and \\\"Over\\\" aren't vocally stated in the respective tracks). This version of the song also contains lyrics shared with \\\"Famous\\\" and \\\"Only\\\" (both recorded in late 2015) as well as a sample later used on \\\"2 Rihannas\\\" and \\\"FML\\\" (added to both in late 2015). The Germans are confirmed to have tracks from the August 2015 folder (in the tracklists tab) such as \\\"Prayer\\\" and \\\"Ultimate Lie,\\\" as well as Post Malone references for some of those August 2015 files. Their version of \\\"Pressure\\\" is likely also August 2015 based on the stems we have for the leaked version. Most of the leaked German files for the LP7 era are late 2015 as well.\", \"date\": 14557536, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c8244a2d849c02e8c368506a148d17e9\", \"url\": \"https://api.pillowcase.su/api/download/c8244a2d849c02e8c368506a148d17e9\", \"size\": \"6.14 MB\", \"duration\": 232.83}", "aliases": ["Can't Get Over Me", "Fall Out Of Heaven"], "size": "6.14 MB"}, {"id": "prayer", "name": "Prayer [V3]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON> (8.15.15)\nHas mumble <PERSON><PERSON><PERSON> vocals and features <PERSON>. Has additional production compared to the Kirby reference track.", "length": "214.73", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/4a8ca96f4b8a4ae45aa7c5394cf53645", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a8ca96f4b8a4ae45aa7c5394cf53645\", \"key\": \"Prayer\", \"title\": \"Prayer [V3]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: <PERSON>W <PERSON> <PERSON> (8.15.15)\\nHas mumble Kanye vocals and features <PERSON>. Has additional production compared to the Kirby reference track.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fc3d9e9e035d5a8e96c9b05aef2f4afc\", \"url\": \"https://api.pillowcase.su/api/download/fc3d9e9e035d5a8e96c9b05aef2f4afc\", \"size\": \"5.85 MB\", \"duration\": 214.73}", "aliases": [], "size": "5.85 MB"}, {"id": "pressure", "name": "Pressure [V6]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" that was played sometime after Glastonbury, likely at a private listening event. Seemingly identical to the version that was played at Glastonbury.", "length": "7.76", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/2b6b76deea2f17f056bc68e969027920", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2b6b76deea2f17f056bc68e969027920\", \"key\": \"Pressure\", \"title\": \"Pressure [V6]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" that was played sometime after Glastonbury, likely at a private listening event. Seemingly identical to the version that was played at Glastonbury.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c1cf773c15238d02958760b25e8f0f95\", \"url\": \"https://api.pillowcase.su/api/download/c1cf773c15238d02958760b25e8f0f95\", \"size\": \"336 kB\", \"duration\": 7.76}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "336 kB"}, {"id": "pressure-72", "name": "Pressure [V6]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" that was played sometime after Glastonbury, likely at a private listening event. Seemingly identical to the version that was played at Glastonbury.", "length": "10.03", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/9ca9d1a95bb89240f3c5190fac202485", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9ca9d1a95bb89240f3c5190fac202485\", \"key\": \"Pressure\", \"title\": \"Pressure [V6]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" that was played sometime after Glastonbury, likely at a private listening event. Seemingly identical to the version that was played at Glastonbury.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3fa0bd6c3157a51e883fe1e492d765c7\", \"url\": \"https://api.pillowcase.su/api/download/3fa0bd6c3157a51e883fe1e492d765c7\", \"size\": \"372 kB\", \"duration\": 10.03}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "372 kB"}, {"id": "pressure-73", "name": "Pressure [V6]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" that was played sometime after Glastonbury, likely at a private listening event. Seemingly identical to the version that was played at Glastonbury.", "length": "10.03", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/23eb8092b1dbd2d285d0a4bd9d05beae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23eb8092b1dbd2d285d0a4bd9d05beae\", \"key\": \"Pressure\", \"title\": \"Pressure [V6]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" that was played sometime after Glastonbury, likely at a private listening event. Seemingly identical to the version that was played at Glastonbury.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b764f534dc33b5780ce9a6d3513a7385\", \"url\": \"https://api.pillowcase.su/api/download/b764f534dc33b5780ce9a6d3513a7385\", \"size\": \"372 kB\", \"duration\": 10.03}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "372 kB"}, {"id": "pressure-74", "name": "Pressure [V7]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" with the extra lines heard in the MusicMafia version but with the original Glastonbury version's structure. <PERSON>'s vocals were later recorded and placed on top of this version, replacing <PERSON><PERSON><PERSON>'s mumble bridge. The main melody is sampled from \"Haunted\" from the Maniac Soundtrack. Also contains a minute long intro sample from the 2012 film \"Silver Linings Playbook\". The stems for this version are dated sometime between August 10th and August 19th, 2015.", "length": "240", "fileDate": 17166816, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/25aac1004a8da342a8707d2f4f684de6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/25aac1004a8da342a8707d2f4f684de6\", \"key\": \"Pressure\", \"title\": \"Pressure [V7]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" with the extra lines heard in the MusicMafia version but with the original Glastonbury version's structure. <PERSON>'s vocals were later recorded and placed on top of this version, replacing <PERSON><PERSON><PERSON>'s mumble bridge. The main melody is sampled from \\\"Haunted\\\" from the Maniac Soundtrack. Also contains a minute long intro sample from the 2012 film \\\"Silver Linings Playbook\\\". The stems for this version are dated sometime between August 10th and August 19th, 2015.\", \"date\": 17166816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1e525bab693bfbe5b7a7b76427140415\", \"url\": \"https://api.pillowcase.su/api/download/1e525bab693bfbe5b7a7b76427140415\", \"size\": \"4.05 MB\", \"duration\": 240}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "4.05 MB"}, {"id": "pressure-75", "name": "Pressure [V8]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" that was previously owned by MusicMafia, who attempted a failed groupbuy for the song priced at 2 BTC in 2017. This version of the song was likely made after the leaked version <PERSON> recorded over as it has a structure change, less mumble lines and a more on-beat bass stem (the leaked one is ~41ms off-beat). Original snippet leaked June 4th, 2017. Two new snippets leaked May 22nd and May 23rd, 2024, after an edited version with <PERSON> vocals was played live by Kick streamer <PERSON><PERSON>, who bought the song for 200k in November 2023. Fourth snippet leaked on May 25th, 2024, following a 100k groupbuy offer. <PERSON><PERSON> later played his version in full on stream on August 10th, 2024.", "length": "36.94", "fileDate": 14965344, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/d7932d1f0ddccf99c117f9ea4d9f9fa8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d7932d1f0ddccf99c117f9ea4d9f9fa8\", \"key\": \"Pressure\", \"title\": \"Pressure [V8]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" that was previously owned by MusicMafia, who attempted a failed groupbuy for the song priced at 2 BTC in 2017. This version of the song was likely made after the leaked version <PERSON> recorded over as it has a structure change, less mumble lines and a more on-beat bass stem (the leaked one is ~41ms off-beat). Original snippet leaked June 4th, 2017. Two new snippets leaked May 22nd and May 23rd, 2024, after an edited version with <PERSON> vocals was played live by Kick streamer <PERSON><PERSON>, who bought the song for 200k in November 2023. Fourth snippet leaked on May 25th, 2024, following a 100k groupbuy offer. <PERSON><PERSON> later played his version in full on stream on August 10th, 2024.\", \"date\": 14965344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3722b48fd85ffc8a5637c84f60361e22\", \"url\": \"https://api.pillowcase.su/api/download/3722b48fd85ffc8a5637c84f60361e22\", \"size\": \"802 kB\", \"duration\": 36.94}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "802 kB"}, {"id": "pressure-76", "name": "Pressure [V8]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" that was previously owned by MusicMafia, who attempted a failed groupbuy for the song priced at 2 BTC in 2017. This version of the song was likely made after the leaked version <PERSON> recorded over as it has a structure change, less mumble lines and a more on-beat bass stem (the leaked one is ~41ms off-beat). Original snippet leaked June 4th, 2017. Two new snippets leaked May 22nd and May 23rd, 2024, after an edited version with <PERSON> vocals was played live by Kick streamer <PERSON><PERSON>, who bought the song for 200k in November 2023. Fourth snippet leaked on May 25th, 2024, following a 100k groupbuy offer. <PERSON><PERSON> later played his version in full on stream on August 10th, 2024.", "length": "187.39", "fileDate": 14965344, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/a8d3794a5fc845a284e85cf07cc1a617", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a8d3794a5fc845a284e85cf07cc1a617\", \"key\": \"Pressure\", \"title\": \"Pressure [V8]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" that was previously owned by MusicMafia, who attempted a failed groupbuy for the song priced at 2 BTC in 2017. This version of the song was likely made after the leaked version <PERSON> recorded over as it has a structure change, less mumble lines and a more on-beat bass stem (the leaked one is ~41ms off-beat). Original snippet leaked June 4th, 2017. Two new snippets leaked May 22nd and May 23rd, 2024, after an edited version with <PERSON> vocals was played live by Kick streamer <PERSON><PERSON>, who bought the song for 200k in November 2023. Fourth snippet leaked on May 25th, 2024, following a 100k groupbuy offer. <PERSON><PERSON> later played his version in full on stream on August 10th, 2024.\", \"date\": 14965344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"058336369d430d93d24bc4ba4dadb5bf\", \"url\": \"https://api.pillowcase.su/api/download/058336369d430d93d24bc4ba4dadb5bf\", \"size\": \"3.21 MB\", \"duration\": 187.39}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "3.21 MB"}, {"id": "pressure-77", "name": "Pressure [V8]", "artists": [], "producers": ["Havoc"], "notes": "Version of \"Pressure\" that was previously owned by MusicMafia, who attempted a failed groupbuy for the song priced at 2 BTC in 2017. This version of the song was likely made after the leaked version <PERSON> recorded over as it has a structure change, less mumble lines and a more on-beat bass stem (the leaked one is ~41ms off-beat). Original snippet leaked June 4th, 2017. Two new snippets leaked May 22nd and May 23rd, 2024, after an edited version with <PERSON> vocals was played live by Kick streamer <PERSON><PERSON>, who bought the song for 200k in November 2023. Fourth snippet leaked on May 25th, 2024, following a 100k groupbuy offer. <PERSON><PERSON> later played his version in full on stream on August 10th, 2024.", "length": "", "fileDate": 14965344, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/86b26c7bd3cc1725cf8b4aad8d46973c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86b26c7bd3cc1725cf8b4aad8d46973c\", \"key\": \"Pressure\", \"title\": \"Pressure [V8]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version of \\\"Pressure\\\" that was previously owned by MusicMafia, who attempted a failed groupbuy for the song priced at 2 BTC in 2017. This version of the song was likely made after the leaked version <PERSON> recorded over as it has a structure change, less mumble lines and a more on-beat bass stem (the leaked one is ~41ms off-beat). Original snippet leaked June 4th, 2017. Two new snippets leaked May 22nd and May 23rd, 2024, after an edited version with <PERSON> vocals was played live by Kick streamer <PERSON><PERSON>, who bought the song for 200k in November 2023. Fourth snippet leaked on May 25th, 2024, following a 100k groupbuy offer. <PERSON><PERSON> later played his version in full on stream on August 10th, 2024.\", \"date\": 14965344, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": ""}, {"id": "pressure-78", "name": "Pressure [V9]", "artists": [], "producers": ["Havoc"], "notes": "OG Filename: pressure verse idea 1\nInitial <PERSON> reference idea for a verse. Original snippet leaked December 29th, 2024. Leaked after a successful Soakbuy.", "length": "28.17", "fileDate": 17390592, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/54b6b22274be4e1aa8ae671cbd422df9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/54b6b22274be4e1aa8ae671cbd422df9\", \"key\": \"Pressure\", \"title\": \"Pressure [V9]\", \"artists\": \"(ref. <PERSON>) (prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"OG Filename: pressure verse idea 1\\nInitial <PERSON> reference idea for a verse. Original snippet leaked December 29th, 2024. Leaked after a successful Soakbuy.\", \"date\": 17390592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"9ac575dfb6bed183c0b0971bf81ae28d\", \"url\": \"https://api.pillowcase.su/api/download/9ac575dfb6bed183c0b0971bf81ae28d\", \"size\": \"2.86 MB\", \"duration\": 28.17}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "2.86 MB"}, {"id": "pressure-79", "name": "Pressure [V10]", "artists": [], "producers": ["Havoc"], "notes": "OG Filename: pressure verse idea 2\nSecond <PERSON> reference track, included as a bonus for a Soakbuy.", "length": "58.74", "fileDate": 17390592, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/5495a32ea675c9c5e0a0c2312e00f468", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5495a32ea675c9c5e0a0c2312e00f468\", \"key\": \"Pressure\", \"title\": \"Pressure [V10]\", \"artists\": \"(ref. <PERSON>) (prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"OG Filename: pressure verse idea 2\\nSecond Bobby <PERSON> reference track, included as a bonus for a Soakbuy.\", \"date\": 17390592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"31a76cfb692b327a178209fad659c464\", \"url\": \"https://api.pillowcase.su/api/download/31a76cfb692b327a178209fad659c464\", \"size\": \"3.35 MB\", \"duration\": 58.74}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "3.35 MB"}, {"id": "pressure-80", "name": "⭐ Pressure [V11]", "artists": ["<PERSON>"], "producers": ["Havoc"], "notes": "OG Filename: trav uzi pressure\nFormer holy grail of the Kanye unreleased community. This version has <PERSON> doing the bridge and harmonizing over the \"Get Off Me\" interlude. Originally thought to have been from TurboGrafx16. Short recording of <PERSON><PERSON> playing the song over Instagram Live surfaced on October 23rd, 2020. Leaked after <PERSON><PERSON> sold the song in a $25k groupbuy, which finished in just under 9 hours with the support of multiple unreleased communities providing bonuses and donations.", "length": "240", "fileDate": 17166816, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/bc6b6bb2bc6000d74ba06099d7ebfb8b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bc6b6bb2bc6000d74ba06099d7ebfb8b\", \"key\": \"Pressure\", \"title\": \"\\u2b50 Pressure [V11]\", \"artists\": \"(feat. <PERSON>) (prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"OG Filename: trav uzi pressure\\nFormer holy grail of the Kanye unreleased community. This version has <PERSON> doing the bridge and harmonizing over the \\\"Get Off Me\\\" interlude. Originally thought to have been from TurboGrafx16. Short recording of <PERSON><PERSON> playing the song over Instagram Live surfaced on October 23rd, 2020. Leaked after Luit sold the song in a $25k groupbuy, which finished in just under 9 hours with the support of multiple unreleased communities providing bonuses and donations.\", \"date\": 17166816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8f76bea810a4569273ac44187c356a1d\", \"url\": \"https://api.pillowcase.su/api/download/8f76bea810a4569273ac44187c356a1d\", \"size\": \"4.05 MB\", \"duration\": 240}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "4.05 MB"}, {"id": "real-friends", "name": "Real Friends [V3]", "artists": [], "producers": ["Boi-1da", "<PERSON>", "Havoc"], "notes": "Solo mumble demo with a slightly differing instrumental.", "length": "265.33", "fileDate": 15970176, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/cdc92e11bc5a3bda5b260af22ea4bcc7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cdc92e11bc5a3bda5b260af22ea4bcc7\", \"key\": \"Real Friends\", \"title\": \"Real Friends [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Solo mumble demo with a slightly differing instrumental.\", \"date\": 15970176, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b7dbc474acbf3d30cb12be9f20887514\", \"url\": \"https://api.pillowcase.su/api/download/b7dbc474acbf3d30cb12be9f20887514\", \"size\": \"4.46 MB\", \"duration\": 265.33}", "aliases": [], "size": "4.46 MB"}, {"id": "real-friends-82", "name": "Real Friends [V4]", "artists": [], "producers": ["Boi-1da", "<PERSON>", "Havoc"], "notes": "OG Filename: REAL FRIENDS 113015_AB_REFv1\nA later version of the mumble demo but with <PERSON> $ign filling in the mumble with reference vocals. Some vocals were used in the final version. Leaked as a bonus for \"The Mall\" groupbuy.", "length": "240.13", "fileDate": 17019936, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/7041cf44547ac755fbae0cfcadecadae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7041cf44547ac755fbae0cfcadecadae\", \"key\": \"Real Friends\", \"title\": \"Real Friends [V4]\", \"artists\": \"(ref. <PERSON> $ign) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> & Ha<PERSON>)\", \"description\": \"OG Filename: REAL FRIENDS 113015_AB_REFv1\\nA later version of the mumble demo but with <PERSON> Dolla $ign filling in the mumble with reference vocals. Some vocals were used in the final version. Leaked as a bonus for \\\"The Mall\\\" groupbuy.\", \"date\": 17019936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1903e11ca1a26efd7bdeccc2fd3469d7\", \"url\": \"https://api.pillowcase.su/api/download/1903e11ca1a26efd7bdeccc2fd3469d7\", \"size\": \"4.05 MB\", \"duration\": 240.13}", "aliases": [], "size": "4.05 MB"}, {"id": "real-friends-83", "name": "Real Friends [V5]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: REAL FRIENDS DARREN KING REPLAY IDEAS\nVersion produced by <PERSON>. Only the beat is currently available.", "length": "139.53", "fileDate": 16719264, "leakDate": "", "availableLength": "Beat Only", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/bd5b8edc3c4595a40f21b091e0cf6ae7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bd5b8edc3c4595a40f21b091e0cf6ae7\", \"key\": \"Real Friends\", \"title\": \"Real Friends [V5]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: REAL FRIENDS DARREN KING REPLAY IDEAS\\nVersion produced by <PERSON>. Only the beat is currently available.\", \"date\": 16719264, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"698b8cb4d1a60ca4fc63b402d8d437f1\", \"url\": \"https://api.pillowcase.su/api/download/698b8cb4d1a60ca4fc63b402d8d437f1\", \"size\": \"2.44 MB\", \"duration\": 139.53}", "aliases": [], "size": "2.44 MB"}, {"id": "rich-nigga-drunk", "name": "<PERSON> [V1]", "artists": [], "producers": [], "notes": "<PERSON><PERSON> said to be in the session by <PERSON><PERSON>. Uses a earlier version of \"Only\" beat. Snippet leaked February 8th, 2023.", "length": "31.72", "fileDate": 16758144, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/53f747f0886dc6ef0a2d59afb2e4320e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/53f747f0886dc6ef0a2d59afb2e4320e\", \"key\": \"Rich Nigga Drunk\", \"title\": \"Rich Nigga Drunk [V1]\", \"description\": \"<PERSON><PERSON> said to be in the session by <PERSON><PERSON>. Uses a earlier version of \\\"Only\\\" beat. Snippet leaked February 8th, 2023.\", \"date\": 16758144, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"07d7bb7c95c279da5d094dc924027c28\", \"url\": \"https://api.pillowcase.su/api/download/07d7bb7c95c279da5d094dc924027c28\", \"size\": \"719 kB\", \"duration\": 31.72}", "aliases": [], "size": "719 kB"}, {"id": "rich-nigga-drunk-85", "name": "<PERSON> [V2]", "artists": [], "producers": [], "notes": "<PERSON><PERSON> said to be in the session by <PERSON><PERSON>. Uses a earlier version of \"Only\" beat. Snippet leaked February 8th, 2023.", "length": "32.57", "fileDate": 16758144, "leakDate": "", "availableLength": "Snippet", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/babe1fee889c1981f5d2133fdaefc309", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/babe1fee889c1981f5d2133fdaefc309\", \"key\": \"Rich Nigga Drunk\", \"title\": \"Rich Nigga Drunk [V2]\", \"description\": \"<PERSON><PERSON> said to be in the session by <PERSON><PERSON>. Uses a earlier version of \\\"Only\\\" beat. Snippet leaked February 8th, 2023.\", \"date\": 16758144, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4f63787645079d2f4a5587429bda8e09\", \"url\": \"https://api.pillowcase.su/api/download/4f63787645079d2f4a5587429bda8e09\", \"size\": \"733 kB\", \"duration\": 32.57}", "aliases": [], "size": "733 kB"}, {"id": "rich-nigga-drunk-86", "name": "✨ Rich Nigga Drunk [V5]", "artists": ["Tyga"], "producers": [], "notes": "OG Filename: <PERSON>unk Ruff- BOUNCE\nVersion with a Tyga feature. His verse is a feature, rather than reference vocals, like on \"Bad Night\" and \"Only\". Sold by Music Mafia along with the other Tyga tracks.", "length": "204.65", "fileDate": 15396480, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/9ca9e960a0baa7b5d7147f6d2220ad95", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9ca9e960a0baa7b5d7147f6d2220ad95\", \"key\": \"Rich Nigga Drunk\", \"title\": \"\\u2728 Rich Nigga Drunk [V5]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Rich Nigga Drunk Ruff- BOUNCE\\nVersion with a Tyga feature. His verse is a feature, rather than reference vocals, like on \\\"Bad Night\\\" and \\\"Only\\\". Sold by Music Mafia along with the other Tyga tracks.\", \"date\": 15396480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a45e373859bd48b8e40188f09dda52b5\", \"url\": \"https://api.pillowcase.su/api/download/a45e373859bd48b8e40188f09dda52b5\", \"size\": \"3.49 MB\", \"duration\": 204.65}", "aliases": [], "size": "3.49 MB"}, {"id": "sell-your-soul", "name": "Sell Your Soul [V1]", "artists": ["KIRBY"], "producers": [], "notes": "OG Filename: KW - Sell Your Soul Ref (8.15.15)\nVersion of \"Sell Your Soul\" without any <PERSON><PERSON><PERSON> vocals. Leaked after a groupbuy.", "length": "126.51", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/48f16139d23fad0dec92af83ac61c9c5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48f16139d23fad0dec92af83ac61c9c5\", \"key\": \"Sell Your Soul\", \"title\": \"Sell Your Soul [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: KW - Sell Your Soul Ref (8.15.15)\\nVersion of \\\"Sell Your Soul\\\" without any Kanye vocals. Leaked after a groupbuy.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0ff8c832aad4404ffee319e2a38ca16f\", \"url\": \"https://api.pillowcase.su/api/download/0ff8c832aad4404ffee319e2a38ca16f\", \"size\": \"2.24 MB\", \"duration\": 126.51}", "aliases": [], "size": "2.24 MB"}, {"id": "the-mall", "name": "✨ The Mall", "artists": [], "producers": [], "notes": "<PERSON> freestyle over the beat. Was recorded as part of the 2015 <PERSON> and <PERSON><PERSON><PERSON> sessions. <PERSON> reuses some of the lines from it on a leaked song of his called \"Red Wine & White Sheets\". Samples \"Xe2\" by <PERSON><PERSON><PERSON>.", "length": "726.21", "fileDate": 17019936, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/68eb696d4b66f56142e544baef94fd52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68eb696d4b66f56142e544baef94fd52\", \"key\": \"The Mall\", \"title\": \"\\u2728 The Mall\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"<PERSON> freestyle over the beat. Was recorded as part of the 2015 <PERSON> and <PERSON><PERSON><PERSON> sessions. <PERSON> reuses some of the lines from it on a leaked song of his called \\\"Red Wine & White Sheets\\\". <PERSON><PERSON> \\\"Xe2\\\" by Ms<PERSON><PERSON>.\", \"date\": 17019936, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"071430199f8de0b56c3cb9155fc35036\", \"url\": \"https://api.pillowcase.su/api/download/071430199f8de0b56c3cb9155fc35036\", \"size\": \"11.8 MB\", \"duration\": 726.21}", "aliases": [], "size": "11.8 MB"}, {"id": "ultimate-lie", "name": "🏆 Ultimate Lie [V4]", "artists": ["<PERSON> Thug"], "producers": [], "notes": "Version with <PERSON> vocals. Played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.", "length": "12.41", "fileDate": 16033248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/e015fa655ad296f3896207f867a77a95", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e015fa655ad296f3896207f867a77a95\", \"key\": \"Ultimate Lie\", \"title\": \"\\ud83c\\udfc6 Ultimate Lie [V4]\", \"artists\": \"(feat. <PERSON> Thug)\", \"description\": \"Version with <PERSON>hug vocals. Played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.\", \"date\": 16033248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"87faf800de2bf5e9a3585f45ae620238\", \"url\": \"https://api.pillowcase.su/api/download/87faf800de2bf5e9a3585f45ae620238\", \"size\": \"410 kB\", \"duration\": 12.41}", "aliases": [], "size": "410 kB"}, {"id": "we-fuck", "name": "WE FUCK [V2]", "artists": [], "producers": ["Hudson Mohawke", "<PERSON>", "MIKE DEAN"], "notes": "<PERSON> version of \"Freestyle 4\". Contains alternate verses and MIKE DEAN production.", "length": "201.98", "fileDate": 16268256, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/65bd6440ad028c7c530569f8300a1942", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65bd6440ad028c7c530569f8300a1942\", \"key\": \"WE FUCK\", \"title\": \"WE FUCK [V2]\", \"artists\": \"(prod. <PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Freestyle 4\", \"Fuck RT Now\"], \"description\": \"<PERSON> version of \\\"Freestyle 4\\\". Contains alternate verses and MIKE DEAN production.\", \"date\": 16268256, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c0cc4ead1784a0d0a48495c1aee92632\", \"url\": \"https://api.pillowcase.su/api/download/c0cc4ead1784a0d0a48495c1aee92632\", \"size\": \"3.44 MB\", \"duration\": 201.98}", "aliases": ["Freestyle 4", "Fuck RT Now"], "size": "3.44 MB"}, {"id": "fuck-rt-now", "name": "Fuck RT Now [V3]", "artists": [], "producers": ["Hudson Mohawke", "MIKE DEAN"], "notes": "Solo version with different lyrics. <PERSON><PERSON> is open.", "length": "10.11", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/110852b466a83cdc0963334e798a8d0c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/110852b466a83cdc0963334e798a8d0c\", \"key\": \"Fuck RT Now\", \"title\": \"Fuck RT Now [V3]\", \"artists\": \"(prod. <PERSON> & <PERSON> DEAN)\", \"aliases\": [\"Freestyle 4\", \"WE FUCK\"], \"description\": \"Solo version with different lyrics. Outro is open.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c559dcb79daa4a8b4cb249f4421bce05\", \"url\": \"https://api.pillowcase.su/api/download/c559dcb79daa4a8b4cb249f4421bce05\", \"size\": \"373 kB\", \"duration\": 10.11}", "aliases": ["Freestyle 4", "WE FUCK"], "size": "373 kB"}, {"id": "freestyle-4", "name": "Freestyle 4 [V4]", "artists": ["<PERSON> Thug"], "producers": ["Hudson Mohawke", "MIKE DEAN"], "notes": "Version of \"Freestyle 4\" with <PERSON> vocals. Played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.", "length": "7.63", "fileDate": 16033248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/7e94c07431f277f6a7b9a11637b0b4c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e94c07431f277f6a7b9a11637b0b4c2\", \"key\": \"Freestyle 4\", \"title\": \"Freestyle 4 [V4]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. <PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"Fuck RT Now\", \"WE FUCK\"], \"description\": \"Version of \\\"Freestyle 4\\\" with <PERSON>hug vocals. Played by <PERSON><PERSON> on Instagram Live on October 22nd, 2020.\", \"date\": 16033248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"21057d062c6e7a974e010d9b363062b4\", \"url\": \"https://api.pillowcase.su/api/download/21057d062c6e7a974e010d9b363062b4\", \"size\": \"333 kB\", \"duration\": 7.63}", "aliases": ["Fuck RT Now", "WE FUCK"], "size": "333 kB"}, {"id": "wolves", "name": "Wolves [V18]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "OG Filename: KW - Wolves Ref 1 (12.15.15)\nHas an additional <PERSON><PERSON><PERSON> mumble verse and alternate lines. Has additional <PERSON><PERSON><PERSON><PERSON> (sample) and <PERSON> vocals. Has new production, such as an organ. Seen on the DONDA visual album tracklist as \"Wolves Interlude\". Leaked after a blind GB.", "length": "246.02", "fileDate": 16838496, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/afe04e9b12a7d0773263941dea82f1a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afe04e9b12a7d0773263941dea82f1a0\", \"key\": \"Wolves\", \"title\": \"Wolves [V18]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON> Cat & Sinjin Hawke)\", \"aliases\": [\"Wolves Interlude\", \"Lost\"], \"description\": \"OG Filename: KW - Wolves Ref 1 (12.15.15)\\nHas an additional Kanye mumble verse and alternate lines. Has additional Bj\\u00f6rk (sample) and <PERSON> vocals. Has new production, such as an organ. Seen on the DONDA visual album tracklist as \\\"Wolves Interlude\\\". Leaked after a blind GB.\", \"date\": 16838496, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a3c7d9b2975b0a0d9579ae44c96114ce\", \"url\": \"https://api.pillowcase.su/api/download/a3c7d9b2975b0a0d9579ae44c96114ce\", \"size\": \"4.15 MB\", \"duration\": 246.02}", "aliases": ["Wolves Interlude", "Lost"], "size": "4.15 MB"}, {"id": "wolves-94", "name": "Wolves [V19]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "Version of 12.15.15 \"Wolves\" with an open verse where the mumble verse was. Likely the version sent to Havoc. Leaked after a blind GB.", "length": "245.2", "fileDate": 16838496, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ec46053234527e5b06c2e21f0b186ec2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ec46053234527e5b06c2e21f0b186ec2\", \"key\": \"Wolves\", \"title\": \"Wolves [V19]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. Cash<PERSON> Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"Version of 12.15.15 \\\"Wolves\\\" with an open verse where the mumble verse was. Likely the version sent to Havoc. Leaked after a blind GB.\", \"date\": 16838496, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6d5936700c8cc54ae58c540a663338fb\", \"url\": \"https://api.pillowcase.su/api/download/6d5936700c8cc54ae58c540a663338fb\", \"size\": \"4.14 MB\", \"duration\": 245.2}", "aliases": ["Lost"], "size": "4.14 MB"}, {"id": "wolves-95", "name": "Wolves [V20]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Havoc", "Cashmere Cat", "Sinjin Hawke"], "notes": "Version of 12.15.15 \"Wolves\" with drums from Havoc, done over the open verse version above. Leaked after a blind GB.", "length": "245.2", "fileDate": 16838496, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/aa6045a37ff28e780a854aa0654bef27", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aa6045a37ff28e780a854aa0654bef27\", \"key\": \"Wolves\", \"title\": \"Wolves [V20]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. Havoc, Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"Version of 12.15.15 \\\"Wolves\\\" with drums from Havoc, done over the open verse version above. Leaked after a blind GB.\", \"date\": 16838496, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"28788cefacc00bfbf05059e44b52e004\", \"url\": \"https://api.pillowcase.su/api/download/28788cefacc00bfbf05059e44b52e004\", \"size\": \"4.14 MB\", \"duration\": 245.2}", "aliases": ["Lost"], "size": "4.14 MB"}, {"id": "wolves-96", "name": "⭐ Wolves [V21]", "artists": ["<PERSON>", "<PERSON>"], "producers": ["Havoc", "Cashmere Cat", "Sinjin Hawke"], "notes": "A more finished Havoc version of the 12.15.15 version. Lacks the mumble verse. Has a vastly improved instrumental, with extra drums, guitar, samples, etc. Leaked after a blind Groupbuy.", "length": "246.69", "fileDate": 16838496, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/99ba6d7482d42d298af4a923a59d0ceb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/99ba6d7482d42d298af4a923a59d0ceb\", \"key\": \"Wolves\", \"title\": \"\\u2b50 Wolves [V21]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. Havoc, Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"A more finished Havoc version of the 12.15.15 version. Lacks the mumble verse. Has a vastly improved instrumental, with extra drums, guitar, samples, etc. Leaked after a blind Groupbuy.\", \"date\": 16838496, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2db3c8c0f6682c562f34f32ddb922d67\", \"url\": \"https://api.pillowcase.su/api/download/2db3c8c0f6682c562f34f32ddb922d67\", \"size\": \"4.16 MB\", \"duration\": 246.69}", "aliases": ["Lost"], "size": "4.16 MB"}, {"id": "wolves-and-robots", "name": "A$AP Ferg - Wolves and Robots", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON><PERSON>"], "notes": "A$AP Ferg 2015 throwaway, produced by Kanye & Pharrell.", "length": "303.23", "fileDate": 16078176, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/bab86b9b195154f24bde4d80883bd203", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bab86b9b195154f24bde4d80883bd203\", \"key\": \"Wolves and Robots\", \"title\": \"A$AP Ferg - Wolves and Robots\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"description\": \"A$AP Ferg 2015 throwaway, produced by Kanye & Pharrell.\", \"date\": 16078176, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ee3a33f7c975176aae3fc46e6a9a06e3\", \"url\": \"https://api.pillowcase.su/api/download/ee3a33f7c975176aae3fc46e6a9a06e3\", \"size\": \"5.06 MB\", \"duration\": 303.23}", "aliases": [], "size": "5.06 MB"}, {"id": "piss-on-your-grave", "name": "<PERSON> - <PERSON>ss On Your Grave [V5]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "Initial reference version. Similar flow and content to Demo 2, but rougher.", "length": "124.82", "fileDate": 16970688, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/6f56df97733172434f94a4602642d116", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f56df97733172434f94a4602642d116\", \"key\": \"Piss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Charlie Heat)\", \"description\": \"Initial reference version. Similar flow and content to Demo 2, but rougher.\", \"date\": 16970688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6bc44af5a00aef542eaec5ee9b883047\", \"url\": \"https://api.pillowcase.su/api/download/6bc44af5a00aef542eaec5ee9b883047\", \"size\": \"2.21 MB\", \"duration\": 124.82}", "aliases": [], "size": "2.21 MB"}, {"id": "piss-on-your-grave-99", "name": "<PERSON> - <PERSON>ss On Your Grave [V6]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: <PERSON> On Your Grave_02\nWas given to <PERSON> at this point as they were found within <PERSON> project files and are all featuring <PERSON><PERSON><PERSON>.", "length": "124.85", "fileDate": 15603840, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/a347dad178741c17d93df58320c9079f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a347dad178741c17d93df58320c9079f\", \"key\": \"Piss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Charlie Heat)\", \"description\": \"OG Filename: <PERSON> On Your Grave_02\\nWas given to <PERSON> at this point as they were found within Travis project files and are all featuring <PERSON><PERSON><PERSON>.\", \"date\": 15603840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8ad1eddb6bb4c25ede4026ca4150fbf7\", \"url\": \"https://api.pillowcase.su/api/download/8ad1eddb6bb4c25ede4026ca4150fbf7\", \"size\": \"2.21 MB\", \"duration\": 124.85}", "aliases": [], "size": "2.21 MB"}, {"id": "piss-on-your-grave-100", "name": "<PERSON> - <PERSON>ss On Your Grave [V7]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: POYG for Price\nVersion that <PERSON> used as the basis for his reference track; further along than Demo 2 with some different lyrics. Was not bounced.", "length": "124.82", "fileDate": 16970688, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/273b109ad222543394353df22e9ecab0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/273b109ad222543394353df22e9ecab0\", \"key\": \"Piss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> West & Charlie Heat)\", \"description\": \"OG Filename: POYG for Price\\nVersion that <PERSON> used as the basis for his reference track; further along than Demo 2 with some different lyrics. Was not bounced.\", \"date\": 16970688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2bc97b76dd082ac910e60906b704c5ae\", \"url\": \"https://api.pillowcase.su/api/download/2bc97b76dd082ac910e60906b704c5ae\", \"size\": \"2.21 MB\", \"duration\": 124.82}", "aliases": [], "size": "2.21 MB"}, {"id": "piss-on-your-grave-101", "name": "<PERSON> - <PERSON>ss On Your Grave [V8]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> on Your Grave_Price\nVersion with <PERSON> doing reference vocals for <PERSON>.", "length": "105.57", "fileDate": 15632352, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/25e8af7485f1cadc029c5718d99bf108", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/25e8af7485f1cadc029c5718d99bf108\", \"key\": \"Piss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V8]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Charlie Heat)\", \"description\": \"OG Filename: Piss on Your Grave_Price\\nVersion with <PERSON> doing reference vocals for <PERSON>.\", \"date\": 15632352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a24e65a0dc264be65861371a17cfa3e0\", \"url\": \"https://api.pillowcase.su/api/download/a24e65a0dc264be65861371a17cfa3e0\", \"size\": \"1.9 MB\", \"duration\": 105.57}", "aliases": [], "size": "1.9 MB"}, {"id": "piss-on-your-grave-102", "name": "<PERSON> - <PERSON>ss On Your Grave [V9]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: <PERSON> - <PERSON> on Your Grave_03 INST\nSolo version with mumble bars and an open verse.", "length": "125.4", "fileDate": 15632352, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/ef32b75500a70eb67d0fdc48bc6c0157", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ef32b75500a70eb67d0fdc48bc6c0157\", \"key\": \"Piss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Charlie Heat)\", \"description\": \"OG Filename: <PERSON> on Your Grave_03 INST\\nSolo version with mumble bars and an open verse.\", \"date\": 15632352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"595df8a21db7831fdf8749324ab83fd5\", \"url\": \"https://api.pillowcase.su/api/download/595df8a21db7831fdf8749324ab83fd5\", \"size\": \"2.22 MB\", \"duration\": 125.4}", "aliases": [], "size": "2.22 MB"}, {"id": "piss-on-your-grave-103", "name": "<PERSON> - <PERSON>ss On Your Grave [V10]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: <PERSON> On Your Grave_03\nHas minor differences from other versions.", "length": "125.04", "fileDate": 16273440, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/7491165fbb695f10023224830d6ef775", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7491165fbb695f10023224830d6ef775\", \"key\": \"<PERSON>ss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Charlie Heat)\", \"description\": \"OG Filename: <PERSON> - <PERSON> On Your Grave_03\\nHas minor differences from other versions.\", \"date\": 16273440, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1d773502814c9f6e348f2a4a5d04ed4a\", \"url\": \"https://api.pillowcase.su/api/download/1d773502814c9f6e348f2a4a5d04ed4a\", \"size\": \"2.21 MB\", \"duration\": 125.04}", "aliases": [], "size": "2.21 MB"}, {"id": "piss-on-your-grave-104", "name": "<PERSON> - <PERSON>ss On Your Grave [V11]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: <PERSON> - Piss On Your Grave_04\nLater version of the song with finished <PERSON> verse.", "length": "125.59", "fileDate": 15634944, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/46c1de588e14ca3c3c499654d62e67f9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/46c1de588e14ca3c3c499654d62e67f9\", \"key\": \"Piss On Your Grave\", \"title\": \"<PERSON> - <PERSON>ss On Your Grave [V11]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Charlie <PERSON>)\", \"description\": \"OG Filename: <PERSON> On Your Grave_04\\nLater version of the song with finished <PERSON> verse.\", \"date\": 15634944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5b3ab56823403e340684a30317b763e4\", \"url\": \"https://api.pillowcase.su/api/download/5b3ab56823403e340684a30317b763e4\", \"size\": \"2.22 MB\", \"duration\": 125.59}", "aliases": [], "size": "2.22 MB"}, {"id": "piss-on-your-grave-105", "name": "<PERSON> - <PERSON>ss on Your Grave [V12]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON>", "MIKE DEAN", "<PERSON>", "<PERSON>", "Wals Escobar", "<PERSON>"], "notes": "OG Filename: POYG MD MIX 14 11\nVersion very similar to the Apple Music release. MIKE DEAN mix.", "length": "166.53", "fileDate": 16709760, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/4e4c95a4f986f2a9ab95e4b2aa26ee50", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e4c95a4f986f2a9ab95e4b2aa26ee50\", \"key\": \"Piss on Your Grave\", \"title\": \"<PERSON> - <PERSON>ss on Your Grave [V12]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> E<PERSON>bar & <PERSON>)\", \"description\": \"OG Filename: POYG MD MIX 14 11\\nVersion very similar to the Apple Music release. MIKE DEAN mix.\", \"date\": 16709760, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d9b39028f3c0165f85372ef9bba63a0c\", \"url\": \"https://api.pillowcase.su/api/download/d9b39028f3c0165f85372ef9bba63a0c\", \"size\": \"2.88 MB\", \"duration\": 166.53}", "aliases": [], "size": "2.88 MB"}, {"id": "amazing", "name": "Amazing", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "<PERSON>", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"Amazing\".", "length": "243.86", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/f56d40d2177a493f7cb841af8f0d8711", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f56d40d2177a493f7cb841af8f0d8711\", \"key\": \"Amazing\", \"title\": \"Amazing\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"Amazing\\\".\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3c58ba2c8f22572cadef12c930e1051d\", \"url\": \"https://api.pillowcase.su/api/download/3c58ba2c8f22572cadef12c930e1051d\", \"size\": \"4.11 MB\", \"duration\": 243.86}", "aliases": [], "size": "4.11 MB"}, {"id": "bad-news", "name": "Bad News [V1]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "Remix of \"Bad News\" intended to accompany the Soundcloud released \"Say You Will\" remix. Has alternate production from other versions, incorporating new violin.", "length": "242.99", "fileDate": 17410464, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/1f0a3c85567c80fe0c1eb593ff68e830", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f0a3c85567c80fe0c1eb593ff68e830\", \"key\": \"Bad News\", \"title\": \"Bad News [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Remix of \\\"Bad News\\\" intended to accompany the Soundcloud released \\\"Say You Will\\\" remix. Has alternate production from other versions, incorporating new violin.\", \"date\": 17410464, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0c2c976eba0554aab3c45b6c6ce8d5ab\", \"url\": \"https://api.pillowcase.su/api/download/0c2c976eba0554aab3c45b6c6ce8d5ab\", \"size\": \"4.1 MB\", \"duration\": 242.99}", "aliases": [], "size": "4.1 MB"}, {"id": "bad-news-108", "name": "✨ Bad News [V2]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version the song. Adds <PERSON> shaw vocals throughout, and in a new outro interpolating the \"keep it like you never knew\" and \"didn't you know\" lines. Also adds additional orchestral production and sound effects of a gun being loaded and fired.", "length": "238.9", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/45f9187dd5ba75509c612f4529f7c22e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/45f9187dd5ba75509c612f4529f7c22e\", \"key\": \"Bad News\", \"title\": \"\\u2728 Bad News [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version the song. Adds <PERSON> shaw vocals throughout, and in a new outro interpolating the \\\"keep it like you never knew\\\" and \\\"didn't you know\\\" lines. Also adds additional orchestral production and sound effects of a gun being loaded and fired.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ea479679be7a99bb78a97b3c5270188e\", \"url\": \"https://api.pillowcase.su/api/download/ea479679be7a99bb78a97b3c5270188e\", \"size\": \"4.03 MB\", \"duration\": 238.9}", "aliases": [], "size": "4.03 MB"}, {"id": "coldest-winter", "name": "✨ Coldest Winter", "artists": ["<PERSON>"], "producers": ["Kanye West", "No I.D.", "<PERSON>", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of the song. Adds <PERSON> vocals and new strings + brass production.", "length": "259.72", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/00d3e39f3e1beb31056eebea1780c409", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00d3e39f3e1beb31056eebea1780c409\", \"key\": \"Coldest Winter\", \"title\": \"\\u2728 Coldest Winter\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of the song. Adds <PERSON> vocals and new strings + brass production.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1edca78df032d58897322f33e3b2bed5\", \"url\": \"https://api.pillowcase.su/api/download/1edca78df032d58897322f33e3b2bed5\", \"size\": \"4.37 MB\", \"duration\": 259.72}", "aliases": [], "size": "4.37 MB"}, {"id": "heartless", "name": "Heartless", "artists": [], "producers": ["Kanye West", "No I.D.", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"Heartless\". Contains added orchestral production.", "length": "207.86", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/a395ec07eb7d418b126ea453621eed12", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a395ec07eb7d418b126ea453621eed12\", \"key\": \"Heartless\", \"title\": \"Heartless\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, No I.D<PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"Heartless\\\". Contains added orchestral production.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"259c04f6e819bba9349bde94c13e34a3\", \"url\": \"https://api.pillowcase.su/api/download/259c04f6e819bba9349bde94c13e34a3\", \"size\": \"3.54 MB\", \"duration\": 207.86}", "aliases": [], "size": "3.54 MB"}, {"id": "love-lockdown", "name": "✨ Love Lockdown", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of the song. Has additional vocals from <PERSON>.", "length": "274.4", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/90cbb37b91de784f4c2310270467b735", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/90cbb37b91de784f4c2310270467b735\", \"key\": \"Love Lockdown\", \"title\": \"\\u2728 Love Lockdown\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of the song. Has additional vocals from <PERSON>.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"28bea4200d60bf0e7142a3f4128dc4a9\", \"url\": \"https://api.pillowcase.su/api/download/28bea4200d60bf0e7142a3f4128dc4a9\", \"size\": \"4.6 MB\", \"duration\": 274.4}", "aliases": [], "size": "4.6 MB"}, {"id": "paranoid", "name": "Paranoid", "artists": ["Mr <PERSON>", "<PERSON>"], "producers": ["Kanye West", "<PERSON>", "Plain Pat", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"Paranoid\". Adds brass, string, and piano production throughout.", "length": "276.99", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/c2b9ecffa6d1d9df5f4fc98e6da2d00b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c2b9ecffa6d1d9df5f4fc98e6da2d00b\", \"key\": \"Paranoid\", \"title\": \"Paranoid\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> Pat & <PERSON>)\", \"aliases\": [\"Anyway\"], \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"Paranoid\\\". Adds brass, string, and piano production throughout.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d12cc4e319231896e0cda7de0ed980d5\", \"url\": \"https://api.pillowcase.su/api/download/d12cc4e319231896e0cda7de0ed980d5\", \"size\": \"4.64 MB\", \"duration\": 276.99}", "aliases": ["Anyway"], "size": "4.64 MB"}, {"id": "pinocchio-story", "name": "Pinocchio Story", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"Pinocchio Story\". Contains the OG low quality version with added orchestral production that's in CDQ.", "length": "356.21", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/6f631743268fff550f08b80ad6df2a56", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f631743268fff550f08b80ad6df2a56\", \"key\": \"Pinocchio Story\", \"title\": \"Pinocchio Story\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"Pinocchio Story\\\". Contains the OG low quality version with added orchestral production that's in CDQ.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"58d1f910e119c671589173c4f090ea40\", \"url\": \"https://api.pillowcase.su/api/download/58d1f910e119c671589173c4f090ea40\", \"size\": \"5.91 MB\", \"duration\": 356.21}", "aliases": [], "size": "5.91 MB"}, {"id": "robocop", "name": "RoboCop", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"RoboCop\". Adds robot sound effects at the intro and new strings throughout.", "length": "266.84", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/4dda1e4e7d8642cee387a07a34d67d2e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4dda1e4e7d8642cee387a07a34d67d2e\", \"key\": \"RoboCop\", \"title\": \"RoboCop\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"RoboCop\\\". Adds robot sound effects at the intro and new strings throughout.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ffddede0cb77311c9da055207b75257e\", \"url\": \"https://api.pillowcase.su/api/download/ffddede0cb77311c9da055207b75257e\", \"size\": \"4.48 MB\", \"duration\": 266.84}", "aliases": [], "size": "4.48 MB"}, {"id": "say-you-will", "name": "Say You Will [V1]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"Say You Will\".", "length": "363", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/56c9383ee137cf65e15bcc098b660caa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/56c9383ee137cf65e15bcc098b660caa\", \"key\": \"Say You Will\", \"title\": \"Say You Will [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"Say You Will\\\".\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8ad247dc5022b9b78f8e739bad6e5fa2\", \"url\": \"https://api.pillowcase.su/api/download/8ad247dc5022b9b78f8e739bad6e5fa2\", \"size\": \"6.02 MB\", \"duration\": 363}", "aliases": [], "size": "6.02 MB"}, {"id": "say-you-will-116", "name": "Say You Will [V2]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "Higher quality file of the Hollywood Bowl version of \"Say You Will\" that was uploaded to Soundcloud.", "length": "285.13", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/dbe4d682f545f11f8083c7a47e7e52fe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dbe4d682f545f11f8083c7a47e7e52fe\", \"key\": \"Say You Will\", \"title\": \"Say You Will [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Higher quality file of the Hollywood Bowl version of \\\"Say You Will\\\" that was uploaded to Soundcloud.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8628a2990680107116115fc6d42e4625\", \"url\": \"https://api.pillowcase.su/api/download/8628a2990680107116115fc6d42e4625\", \"size\": \"4.77 MB\", \"duration\": 285.13}", "aliases": [], "size": "4.77 MB"}, {"id": "see-you-in-my-nightmares", "name": "See You In My Nightmares", "artists": ["<PERSON>"], "producers": ["Kanye West", "No I.D.", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"See You In My Nightmares\". Adds more strings and brass.", "length": "258.54", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/16d5ea37a0ea5caacea8ebbb64b044c1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16d5ea37a0ea5caacea8ebbb64b044c1\", \"key\": \"See You In My Nightmares\", \"title\": \"See You In My Nightmares\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Tell Everybody That You Know\"], \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"See You In My Nightmares\\\". Adds more strings and brass.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b125fcc4e8f2e7410267d3bf989f7b02\", \"url\": \"https://api.pillowcase.su/api/download/b125fcc4e8f2e7410267d3bf989f7b02\", \"size\": \"4.35 MB\", \"duration\": 258.54}", "aliases": ["Tell Everybody That You Know"], "size": "4.35 MB"}, {"id": "street-lights", "name": "✨ Street Lights", "artists": ["The WRLDFMS <PERSON>", "<PERSON><PERSON>"], "producers": ["Kanye West", "Mr. <PERSON>", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of the song. Contains added orchestral production.", "length": "190.63", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/a74a3488026231f96d6f8cdc9ff476d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a74a3488026231f96d6f8cdc9ff476d1\", \"key\": \"Street Lights\", \"title\": \"\\u2728 Street Lights\", \"artists\": \"(feat. The WRLDFMS <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, Mr<PERSON> & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of the song. Contains added orchestral production.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"912040278b10a6193fb00ab53e56aeb0\", \"url\": \"https://api.pillowcase.su/api/download/912040278b10a6193fb00ab53e56aeb0\", \"size\": \"3.26 MB\", \"duration\": 190.63}", "aliases": [], "size": "3.26 MB"}, {"id": "welcome-to-heartbreak", "name": "Welcome to Heartbreak", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>", "Plain Pat", "<PERSON>"], "notes": "Unofficial stem bounce of the Hollywood Bowl version of \"Welcome To Heartbreak\". Contains added orchestral production.", "length": "262.54", "fileDate": 17288640, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "swish", "originalUrl": "https://pillowcase.su/f/45225ccf8b7def856c5677e3975d4476", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/45225ccf8b7def856c5677e3975d4476\", \"key\": \"Welcome to Heartbreak\", \"title\": \"Welcome to Heartbreak\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> Pat & <PERSON>)\", \"description\": \"Unofficial stem bounce of the Hollywood Bowl version of \\\"Welcome To Heartbreak\\\". Contains added orchestral production.\", \"date\": 17288640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d2a5ebb2ff9379969f7c02cd51bc7130\", \"url\": \"https://api.pillowcase.su/api/download/d2a5ebb2ff9379969f7c02cd51bc7130\", \"size\": \"4.41 MB\", \"duration\": 262.54}", "aliases": [], "size": "4.41 MB"}]}