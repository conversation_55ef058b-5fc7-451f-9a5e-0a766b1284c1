{"id": "vultures", "name": "VULTURES", "description": "<PERSON><PERSON> was a frequent collaborator, for <PERSON> and <PERSON> Dolla $ign's joint trilogy (cut by two volumes), Vultures. 'MUSIC' was even delayed, because of the delays and reworks these albums had. During this sub-era, <PERSON><PERSON> would do some changes to his own project, due to <PERSON>'s influence. Eventhough only a very few songs were released or leaked, it's believed that 'the Vultures' and <PERSON><PERSON> may have worked on so much more material together.", "backgroundColor": "rgb(171, 157, 122)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FVQfqVswhsssM8rJ3LStEsr9IVrsamds-28a85BchHzokJBcB8UQQJ7swerYQHWIDPfMgReVWp-ymELK-313znSY6i630nUao9sfoRL4mqzlSgRXEkne9it8p6HgZU?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "field-trip", "name": "FIELD TRIP [V1]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "Wheezy"], "notes": "OG Filename: pbc 12-12-23\nVersion of \"Field Trip\" from when it was given to <PERSON><PERSON><PERSON>, and still had <PERSON> on the hook.", "length": "3:26", "fileDate": 17294688, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/37a7402727029ac45f177df8e8e752de", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37a7402727029ac45f177df8e8e752de\", \"key\": \"FIELD TRIP\", \"title\": \"FIELD TRIP [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON> & Wheezy)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"OG Filename: pbc 12-12-23\\nVersion of \\\"Field Trip\\\" from when it was given to <PERSON><PERSON><PERSON>, and still had <PERSON> on the hook.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6a494580e5a00db85647b605a8d7fd15\", \"url\": \"https://api.pillowcase.su/api/download/6a494580e5a00db85647b605a8d7fd15\", \"size\": \"4.24 MB\", \"duration\": 206.56}", "aliases": ["DISCONNECTED", "All Yours"], "size": "4.24 MB"}, {"id": "field-trip-2", "name": "¥$ - FIELD TRIP [V2]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "OG Filename: field trip 12.21.\nVersion from December 21st, 2023. Leaked during the Can U Be Groupbuy on May 26th, 2024. Said to have the worst mixing out of any version by insiders. <PERSON> vocals.", "length": "5:18", "fileDate": 17166816, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/2e702401687510e86c7407cb88c5ccef/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2e702401687510e86c7407cb88c5ccef/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V2]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"OG Filename: field trip 12.21.\\nVersion from December 21st, 2023. Leaked during the Can U Be Groupbuy on May 26th, 2024. Said to have the worst mixing out of any version by insiders. No Ye vocals.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25af1ce9fff42070d0018d166053aa50\", \"url\": \"https://api.pillowcase.su/api/download/25af1ce9fff42070d0018d166053aa50\", \"size\": \"5.26 MB\", \"duration\": 318.03}", "aliases": ["DISCONNECTED", "All Yours"], "size": "5.26 MB"}, {"id": "honor-roll", "name": "¥$ - HONOR ROLL [V6]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["TheLabCook", "Ojivolta", "Digital Nas", "Ye"], "notes": "Version with the Osbourne sample and an OG ye verse. It turns out the cut off line was saying \"jewish\" all along.", "length": "4:24", "fileDate": 17222112, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/75f7b6a822f59a3dcd533222ee1cf651/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/75f7b6a822f59a3dcd533222ee1cf651/play\", \"key\": \"HONOR ROLL\", \"title\": \"\\u00a5$ - HONOR ROLL [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> Kid) (prod. TheLabCook, Ojivolta, Digital Nas & Ye)\", \"aliases\": [\"CARNIVAL\", \"H00LIGANS\"], \"description\": \"Version with the Os<PERSON> sample and an OG ye verse. It turns out the cut off line was saying \\\"jewish\\\" all along.\", \"date\": 17222112, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f60d144787c5e5084da63783bf9e22a1\", \"url\": \"https://api.pillowcase.su/api/download/f60d144787c5e5084da63783bf9e22a1\", \"size\": \"4.4 MB\", \"duration\": 264.41}", "aliases": ["CARNIVAL", "H00LIGANS"], "size": "4.4 MB"}, {"id": "h00ligans", "name": "¥$ - H00LIGANS [V7]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["TheLabCook", "Ojivolta", "Digital Nas", "Ye"], "notes": "A stem with <PERSON><PERSON>'s adlibs on CARNIVAL (at the time most likely called H00LIGANS) on Ye verse leaked on June 9, 2024.", "length": "0:47", "fileDate": 17178912, "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/78771c19f28d695146302f7cb96c2c45", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/78771c19f28d695146302f7cb96c2c45\", \"key\": \"H00LIGANS\", \"title\": \"\\u00a5$ - H00LIGANS [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> Kid) (prod. TheLabCook, Ojivolta, Digital Nas & Ye)\", \"aliases\": [\"CARNIVAL\", \"HONOR ROLL\"], \"description\": \"A stem with <PERSON><PERSON>'s adlibs on CARNIVAL (at the time most likely called H00LIGANS) on Ye verse leaked on June 9, 2024.\", \"date\": 17178912, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f10af700f020c0fb7b4e89bce562b827\", \"url\": \"https://api.pillowcase.su/api/download/f10af700f020c0fb7b4e89bce562b827\", \"size\": \"946 kB\", \"duration\": 47.49}", "aliases": ["CARNIVAL", "HONOR ROLL"], "size": "946 kB"}, {"id": "carnival", "name": "🗑️ ¥$ - CARNIVAL [V7]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["TheLabCook", "Ojivolta", "LondonOnDaTrack", "Digital Nas", "Ye"], "notes": "Version with additional production from LondonOnDaTrack, terrible mix, loudass bass and hihats. Also offbeat vocals", "length": "", "fileDate": 17374176, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "http://music.froste.lol/song/dd3681dcff4520f28899b63499d3cd10/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/dd3681dcff4520f28899b63499d3cd10/play\", \"key\": \"CARNIVAL\", \"title\": \"\\ud83d\\uddd1\\ufe0f \\u00a5$ - CARNIVAL [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> Kid) (prod. TheLabCook, Ojivolta, LondonOnDaTrack, Digital Nas & Ye)\", \"aliases\": [\"HONOR ROLL\", \"H00LIGANS\"], \"description\": \"Version with additional production from LondonOnDaTrack, terrible mix, loudass bass and hihats. Also offbeat vocals\", \"date\": 17374176, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["HONOR ROLL", "H00LIGANS"], "size": ""}, {"id": "carnival-6", "name": "¥$ - CARNIVAL [V8]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["TheLabCook", "Ojivolta", "Digital Nas", "Ye"], "notes": "A version of <PERSON> from the Chi LP. Has the uncleared <PERSON><PERSON> and doesn't have bonus synths. For the fixed version go to Remasters/Edits", "length": "", "fileDate": 17073504, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "http://music.froste.lol/song/229670677a72da65978976dc7f706c93/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/229670677a72da65978976dc7f706c93/play\", \"key\": \"CARNIVAL\", \"title\": \"\\u00a5$ - CARNIVAL [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> Kid) (prod. TheLabCook, Ojivolta, Digital Nas & Ye)\", \"aliases\": [\"HONOR ROLL\", \"H00LIGANS\"], \"description\": \"A version of Carnival from the Chi LP. Has the uncleared <PERSON><PERSON> Sample and doesn't have bonus synths. For the fixed version go to Remasters/Edits\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["HONOR ROLL", "H00LIGANS"], "size": ""}, {"id": "carnival-7", "name": "¥$ - CARNIVAL [V18]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["TheLabCook", "Ojivolta"], "notes": "OG Filename: CARNIVAL M10 DECLICK MT001-<PERSON><PERSON><PERSON> MASTER-24BIT-48kHz\nPost-release mix for \"Carnival\" that leaked 10/17/24 after a groupbuy. Has better mixing.", "length": "4:24", "fileDate": 17291232, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/d14092c884990dd6b934aaffa1519cd0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d14092c884990dd6b934aaffa1519cd0\", \"key\": \"CARNIVAL\", \"title\": \"\\u00a5$ - CARNIVAL [V18]\", \"artists\": \"(feat. <PERSON> Ho<PERSON>gans, <PERSON> & <PERSON>) (prod. TheLabCook & Ojivolta)\", \"aliases\": [\"HONOR ROLL\", \"H00LIGANS\"], \"description\": \"OG Filename: CARNIVAL M10 DECLICK MT001-TUCCI MASTER-24BIT-48kHz\\nPost-release mix for \\\"Carnival\\\" that leaked 10/17/24 after a groupbuy. Has better mixing.\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2603d8716a1fc6fd18b7ee90e7e9c836\", \"url\": \"https://api.pillowcase.su/api/download/2603d8716a1fc6fd18b7ee90e7e9c836\", \"size\": \"9.24 MB\", \"duration\": 264.32}", "aliases": ["HONOR ROLL", "H00LIGANS"], "size": "9.24 MB"}, {"id": "carnival-8", "name": "¥$ - CARNIVAL [V19]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "Updated version of \"Carnival\" adding a sample of a Spanish guy screaming \"Puro pinche carnival y no mamadas\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earlier the same day it fully leaked. Different to music video version.", "length": "4:20", "fileDate": 17095968, "leakDate": "", "labels": ["Recording", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/0147737d308c6ad5070bb62bb898b5ac/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0147737d308c6ad5070bb62bb898b5ac/play\", \"key\": \"CARNIVAL\", \"title\": \"\\u00a5$ - CARNIVAL [V19]\", \"artists\": \"(feat. <PERSON> Ho<PERSON>gans, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"HONOR ROLL\", \"H00LIGANS\"], \"description\": \"Updated version of \\\"Carnival\\\" adding a sample of a Spanish guy screaming \\\"Puro pinche carnival y no mamadas\\\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earlier the same day it fully leaked. Different to music video version.\", \"date\": 17095968, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e91eb20b3772c9704c97da9a94aa8f1c\", \"url\": \"https://api.pillowcase.su/api/download/e91eb20b3772c9704c97da9a94aa8f1c\", \"size\": \"4.33 MB\", \"duration\": 260.33}", "aliases": ["HONOR ROLL", "H00LIGANS"], "size": "4.33 MB"}, {"id": "carnival-9", "name": "¥$ - CARNIVAL [V19]", "artists": ["The Hooligans", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "TheLabCook", "The Legendary Traxster", "Ojivolta", "Digital Nas", "Ty Dolla $ign"], "notes": "Updated version of \"Carnival\" adding a sample of a Spanish guy screaming \"Puro pinche carnival y no mamadas\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earlier the same day it fully leaked. Different to music video version.", "length": "0:12", "fileDate": 17095968, "leakDate": "", "labels": ["Recording", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/3e2bf46616f7459ead9e3a5b7620a1b7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3e2bf46616f7459ead9e3a5b7620a1b7/play\", \"key\": \"CARNIVAL\", \"title\": \"\\u00a5$ - CARNIVAL [V19]\", \"artists\": \"(feat. <PERSON> Ho<PERSON>gans, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, TheLabCook, The Legendary Traxster, Ojivolta, Digital Nas & Ty Dolla $ign)\", \"aliases\": [\"HONOR ROLL\", \"H00LIGANS\"], \"description\": \"Updated version of \\\"Carnival\\\" adding a sample of a Spanish guy screaming \\\"Puro pinche carnival y no mamadas\\\" (Pure fucking Carnival and no blowjobs). Played in the France stadium and Italy stadium of the VULTURES 1 tour. CDQ snippet leaked earlier the same day it fully leaked. Different to music video version.\", \"date\": 17095968, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ecbf05e52e1a671fa01b7bd3b7e7ee83\", \"url\": \"https://api.pillowcase.su/api/download/ecbf05e52e1a671fa01b7bd3b7e7ee83\", \"size\": \"377 kB\", \"duration\": 12.98}", "aliases": ["HONOR ROLL", "H00LIGANS"], "size": "377 kB"}, {"id": "fuk-sumn", "name": "¥$ - FUK SUMN [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "According to <PERSON><PERSON>, this acapella is used on a \"Quavo Carti version\". Full file has not leaked, only the acapella for <PERSON><PERSON>.", "length": "3:05", "fileDate": 17160768, "leakDate": "", "labels": ["Lossless", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/fb2c77e49d007c1924e97bcd3045397a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fb2c77e49d007c1924e97bcd3045397a\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"According to <PERSON><PERSON>, this acapella is used on a \\\"Quavo Carti version\\\". Full file has not leaked, only the acapella for <PERSON><PERSON>.\", \"date\": 17160768, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c6bcf85277607b7740b80c97c05697bb\", \"url\": \"https://api.pillowcase.su/api/download/c6bcf85277607b7740b80c97c05697bb\", \"size\": \"7.97 MB\", \"duration\": 185.15}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "7.97 MB"}, {"id": "fuk-sumn-11", "name": "¥$ - FUK SUMN [V6]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Uses the same beat as earlier versions and has <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on it. According to <PERSON><PERSON>, <PERSON><PERSON><PERSON>'s verse was recorded as a reference track for <PERSON> and being noted as to why <PERSON> was not on the song \"for so long\". A CDQ snippet leaked November 15th, 2023, with the full song later forceleaking.", "length": "3:25", "fileDate": 17162496, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/352355aea0ae47b38438d8a4e16ee402", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/352355aea0ae47b38438d8a4e16ee402\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Uses the same beat as earlier versions and has <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on it. According to <PERSON><PERSON>, <PERSON><PERSON><PERSON>'s verse was recorded as a reference track for <PERSON> and being noted as to why <PERSON> was not on the song \\\"for so long\\\". A CDQ snippet leaked November 15th, 2023, with the full song later forceleaking.\", \"date\": 17162496, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2969ba3e8453ad92282fe1e759ff09f2\", \"url\": \"https://api.pillowcase.su/api/download/2969ba3e8453ad92282fe1e759ff09f2\", \"size\": \"8.3 MB\", \"duration\": 205.74}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "8.3 MB"}, {"id": "fuk-sumn-12", "name": "¥$ - FUK SUMN [V7]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Version of \"Fuk Sumn\" that is close to the above version but with <PERSON>'s \"I'm tryna fuck sum right now\" lines being repeated less. Played at the Vultures City Event in Las Vegas.", "length": "2:41", "fileDate": 17025984, "leakDate": "", "labels": ["Recording", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/1f797964e42302501b7a3e7b925822d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f797964e42302501b7a3e7b925822d2\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Version of \\\"Fuk Sumn\\\" that is close to the above version but with <PERSON>'s \\\"I'm tryna fuck sum right now\\\" lines being repeated less. Played at the Vultures City Event in Las Vegas.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"id\": \"f23c2860d026af099a275ba64cc5f0ca\", \"url\": \"https://api.pillowcase.su/api/download/f23c2860d026af099a275ba64cc5f0ca\", \"size\": \"7.6 MB\", \"duration\": 161.83}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "7.6 MB"}, {"id": "fuk-sumn-13", "name": "¥$ - FUK SUMN [V10]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>"], "notes": "Alternate and likely updated version of \"Fuk Sumn,\" played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.", "length": "2:37", "fileDate": 17025984, "leakDate": "", "labels": ["Lossless", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/f482f915c7f831aa5430629524de719b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f482f915c7f831aa5430629524de719b\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8<PERSON> & Hubi)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Alternate and likely updated version of \\\"Fuk Sumn,\\\" played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d26ea3d5b9c1b1c31af01f55b26c637c\", \"url\": \"https://api.pillowcase.su/api/download/d26ea3d5b9c1b1c31af01f55b26c637c\", \"size\": \"7.53 MB\", \"duration\": 157.41}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "7.53 MB"}, {"id": "fuk-sumn-14", "name": "¥$ - FUK SUMN [V10]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>"], "notes": "Alternate and likely updated version of \"Fuk Sumn,\" played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.", "length": "1:12", "fileDate": 17025984, "leakDate": "", "labels": ["Lossless", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/08462a82b7d6b5cc0b41e859358220f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08462a82b7d6b5cc0b41e859358220f2\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V10]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8W & Hu<PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Alternate and likely updated version of \\\"Fuk Sumn,\\\" played by <PERSON><PERSON> has a new beat from previous versions. Full LQ played at the Las Vegas Vultures City afterparty on December 15th, 2023. Partial lossless iTunes file leaked July 31st, 2024. Has a faster intro compared to other versions.\", \"date\": 17025984, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cd7f19a363e8947d6049273828223547\", \"url\": \"https://api.pillowcase.su/api/download/cd7f19a363e8947d6049273828223547\", \"size\": \"6.16 MB\", \"duration\": 72}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "6.16 MB"}, {"id": "fuck-sumn", "name": "¥$ - FUCK SUMN [V12]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "<PERSON>"], "notes": "OG Filename: FUCK SUMN V2 - Prod.ShaunEnzo...\nUpdated version of \"Fuk Sumn\" with <PERSON> production. Unknown exactly when it was made. Posted on Instagram by <PERSON> himself.", "length": "0:11", "fileDate": 17094240, "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/a0dffea7881a979ebc9108bee2ab9089", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0dffea7881a979ebc9108bee2ab9089\", \"key\": \"FUCK SUMN\", \"title\": \"\\u00a5$ - FUCK SUMN [V12]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, <PERSON><PERSON><PERSON>, SH<PERSON>\\u00d8W, <PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUK SUMN\"], \"description\": \"OG Filename: FUCK SUMN V2 - Prod.ShaunEnzo...\\nUpdated version of \\\"Fuk Sumn\\\" with <PERSON> production. Unknown exactly when it was made. Posted on Instagram by <PERSON> himself.\", \"date\": 17094240, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e3259127b24023b6942e59ee3d9b2b9d\", \"url\": \"https://api.pillowcase.su/api/download/e3259127b24023b6942e59ee3d9b2b9d\", \"size\": \"5.19 MB\", \"duration\": 11.08}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUK SUMN"], "size": "5.19 MB"}, {"id": "fuk-sumn-16", "name": "¥$ - FUK SUMN [V13]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>"], "notes": "Version of \"Fuk Sumn\" with the updated beat and has the Quavo reference verse.", "length": "0:09", "fileDate": 17025984, "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/80f9020b8af8dd628b34a6ba9c0d67fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80f9020b8af8dd628b34a6ba9c0d67fc\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V13]\", \"artists\": \"(ref. <PERSON>ua<PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8W & <PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Version of \\\"Fuk Sumn\\\" with the updated beat and has the Quavo reference verse.\", \"date\": 17025984, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"id\": \"3696abbc0e22bfea81cac70daaa4c5d1\", \"url\": \"https://api.pillowcase.su/api/download/3696abbc0e22bfea81cac70daaa4c5d1\", \"size\": \"5.15 MB\", \"duration\": 9.04}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "5.15 MB"}, {"id": "fuk-sumn-17", "name": "¥$ - FUK SUMN [V16]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Version of \"Fuk Sumn\" with Qua<PERSON>'s reference verse added back with the new JPEGMAFIA production. Possibly made before the music video shoot. Snippet was posted on JPEGMAFIA's story on February 23rd, 2024, and previewed at Pitchfork Music Festival March 7th, 2024. Has no <PERSON><PERSON><PERSON> vocals.", "length": "1:35", "fileDate": 17097696, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/d44dd3eddeb56001c3d75d3249a6519b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d44dd3eddeb56001c3d75d3249a6519b\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V16]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Version of \\\"Fuk Sumn\\\" with Qua<PERSON>'s reference verse added back with the new JPEGMAFIA production. Possibly made before the music video shoot. Snippet was posted on JPEGMAFIA's story on February 23rd, 2024, and previewed at Pitchfork Music Festival March 7th, 2024. Has no Kanye vocals.\", \"date\": 17097696, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c1cbe21b59631bcb09a9bd7b96060579\", \"url\": \"https://api.pillowcase.su/api/download/c1cbe21b59631bcb09a9bd7b96060579\", \"size\": \"5.77 MB\", \"duration\": 95.66}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "5.77 MB"}, {"id": "fuk-sumn-18", "name": "¥$ - FUK SUMN [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "An updated version without the Q<PERSON>vo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.", "length": "0:34", "fileDate": 17051904, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/b83f938b9fd365aa937deedb406276c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b83f938b9fd365aa937deedb406276c2\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"An updated version without the Quavo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.\", \"date\": 17051904, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"id\": \"b68a045ef53276e6773660add46e38f0\", \"url\": \"https://api.pillowcase.su/api/download/b68a045ef53276e6773660add46e38f0\", \"size\": \"5.56 MB\", \"duration\": 34.13}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "5.56 MB"}, {"id": "fuk-sumn-19", "name": "¥$ - FUK SUMN [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "An updated version without the Q<PERSON>vo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.", "length": "0:22", "fileDate": 17051904, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/bad81214f2aabb2ce3cfe3bc98404d13", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bad81214f2aabb2ce3cfe3bc98404d13\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"An updated version without the Quavo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.\", \"date\": 17051904, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"id\": \"b069eedf15fc7aeb7eb0d0bd6e5774a8\", \"url\": \"https://api.pillowcase.su/api/download/b069eedf15fc7aeb7eb0d0bd6e5774a8\", \"size\": \"5.37 MB\", \"duration\": 22.73}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "5.37 MB"}, {"id": "fuk-sumn-20", "name": "¥$ - FUK SUMN [V18]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "An updated version without the Q<PERSON>vo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.", "length": "0:18", "fileDate": 17051904, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/c4904651021f5aac5c270009485cc22f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4904651021f5aac5c270009485cc22f\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"An updated version without the Quavo vocals and a beatswitch ending done by <PERSON>. The unreleased music video was recorded over this version.\", \"date\": 17051904, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"id\": \"ae18c0fcb305c01897699201ed8f9281\", \"url\": \"https://api.pillowcase.su/api/download/ae18c0fcb305c01897699201ed8f9281\", \"size\": \"5.3 MB\", \"duration\": 18.41}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "5.3 MB"}, {"id": "fuk-sumn-21", "name": "¥$ - FUK SUMN [V20]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Version played at the Chicago listening party. Has alternate production, with most of the main melody removed. Contains a beatswitch with pitched up Ye vocals and a new <PERSON> feature.", "length": "3:28", "fileDate": 17073504, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/98e3594c6e15fa0df56e98b6e5d74b8e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/98e3594c6e15fa0df56e98b6e5d74b8e\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V20]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, <PERSON><PERSON>, <PERSON> & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Version played at the Chicago listening party. Has alternate production, with most of the main melody removed. Contains a beatswitch with pitched up Ye vocals and a new Travis feature.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"715014f97dd3570ec6af1c20d8e9f5ef\", \"url\": \"https://api.pillowcase.su/api/download/715014f97dd3570ec6af1c20d8e9f5ef\", \"size\": \"8.34 MB\", \"duration\": 208.12}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "8.34 MB"}, {"id": "fuk-sumn-22", "name": "¥$ - FUK SUMN [V31]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "OG Filename: Fuk Sumn M11\nMix of \"Fuk Sumn\" leaked 10/17/24 after a groupbuy. Identical to the release", "length": "3:29", "fileDate": 17291232, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/6f163d8f301b8b668121c2426458d951", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f163d8f301b8b668121c2426458d951\", \"key\": \"FUK SUMN\", \"title\": \"\\u00a5$ - FUK SUMN [V31]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, <PERSON><PERSON> & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"OG Filename: Fuk Sumn M11\\nMix of \\\"Fuk Sumn\\\" leaked 10/17/24 after a groupbuy. Identical to the release\", \"date\": 17291232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c776596368feb7126ae8596094363a02\", \"url\": \"https://api.pillowcase.su/api/download/c776596368feb7126ae8596094363a02\", \"size\": \"8.36 MB\", \"duration\": 209.58}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "8.36 MB"}, {"id": "fuk-sumn-23", "name": "¥$ - FUK SUMN (Remix) [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. Presumably did not release due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFIA on his Outbreak set on June 28th, 2024.", "length": "1:22", "fileDate": 17252352, "leakDate": "", "labels": ["High Quality", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/b4cd9c9e873ebdc443bbd27fb4d0f188", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b4cd9c9e873ebdc443bbd27fb4d0f188\", \"key\": \"FUK SUMN (Remix)\", \"title\": \"\\u00a5$ - FUK SUMN (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & Quavo) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to <PERSON> this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. Presumably did not release due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by <PERSON><PERSON><PERSON><PERSON><PERSON> on his Outbreak set on June 28th, 2024.\", \"date\": 17252352, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4e771e57d6a87e33ef63f824c0918e94\", \"url\": \"https://api.pillowcase.su/api/download/4e771e57d6a87e33ef63f824c0918e94\", \"size\": \"6.32 MB\", \"duration\": 82.08}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "6.32 MB"}, {"id": "fuk-sumn-24", "name": "¥$ - FUK SUMN (Remix) [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. Presumably did not release due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFIA on his Outbreak set on June 28th, 2024.", "length": "4:05", "fileDate": 17252352, "leakDate": "", "labels": ["High Quality", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/9d9ce30dcda550ddf6e9662cdaf8903a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d9ce30dcda550ddf6e9662cdaf8903a\", \"key\": \"FUK SUMN (Remix)\", \"title\": \"\\u00a5$ - FUK SUMN (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & Qua<PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. Presumably did not release due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMA<PERSON><PERSON> on his Outbreak set on June 28th, 2024.\", \"date\": 17252352, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ef98c2847ce1b633e668e864cf135824\", \"url\": \"https://api.pillowcase.su/api/download/ef98c2847ce1b633e668e864cf135824\", \"size\": \"8.94 MB\", \"duration\": 245.86}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "8.94 MB"}, {"id": "fuk-sumn-25", "name": "¥$ - FUK SUMN (Remix) [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo"], "producers": ["Digital Nas", "Timbaland", "SHDØW", "<PERSON><PERSON>", "JPEGMAFIA"], "notes": "Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to Pop this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. Presumably did not release due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFIA on his Outbreak set on June 28th, 2024.", "length": "4:05", "fileDate": 17252352, "leakDate": "", "labels": ["High Quality", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/e9e7700ea1d7d1638c422c88384ec045", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9e7700ea1d7d1638c422c88384ec045\", \"key\": \"FUK SUMN (Remix)\", \"title\": \"\\u00a5$ - FUK SUMN (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. Digital Nas, Timbaland, SHD\\u00d8W, Hubi & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"Played at the Footprint center listening event and Rolling Loud. Has alternate production, and the Quavo verse added onto the outro of the song. According to <PERSON> this is a remix that was meant to be dropped in a pack like how VULTURES PACK and CARNIVAL PACK did. Presumably did not release due to the canceling of the YZYApp and general shifts in <PERSON>'s lifestyle. Partially played by JPEGMAFI<PERSON> on his Outbreak set on June 28th, 2024.\", \"date\": 17252352, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c98b2c7e90b26b9c2259942753f6aa22\", \"url\": \"https://api.pillowcase.su/api/download/c98b2c7e90b26b9c2259942753f6aa22\", \"size\": \"8.94 MB\", \"duration\": 245.4}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "8.94 MB"}, {"id": "field-trip-26", "name": "¥$ - FIELD TRIP [V12]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Played during the private LA listening party for VULTURES 2 on March 9th, 2024. No Ye verse was played however it was cut off early. Same version played in Phoenix. It's been said by <PERSON><PERSON> that a Ye verse does exist on the beatswitch of the song, but that it is a freestyle.", "length": "3:03", "fileDate": 17099424, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/c12275dec415387d2221fd1ffdf689e1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c12275dec415387d2221fd1ffdf689e1/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V12]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Played during the private LA listening party for VULTURES 2 on March 9th, 2024. No Ye verse was played however it was cut off early. Same version played in Phoenix. It's been said by <PERSON><PERSON> that a Ye verse does exist on the beatswitch of the song, but that it is a freestyle.\", \"date\": 17099424, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"621a2c3140a834af6395b21287602bbd\", \"url\": \"https://api.pillowcase.su/api/download/621a2c3140a834af6395b21287602bbd\", \"size\": \"3.1 MB\", \"duration\": 183.48}", "aliases": ["DISCONNECTED", "All Yours"], "size": "3.1 MB"}, {"id": "field-trip-27", "name": "¥$ - FIELD TRIP [V13]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Ojivolta", "Wheezy", "<PERSON><PERSON>"], "notes": "OG Filename: FIELD TRIP_3.10.24_OV W DRUMS\nPlayed at the Phoenix listening party on March 10th, 2024 in full.", "length": "3:12", "fileDate": 17294688, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/fc14df009ce2fa922ea0ad0064ea0b2b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/fc14df009ce2fa922ea0ad0064ea0b2b/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V13]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. Ojivolta, <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"OG Filename: FIELD TRIP_3.10.24_OV W DRUMS\\nPlayed at the Phoenix listening party on March 10th, 2024 in full.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"32f9263f384531bfe11b11d033691be2\", \"url\": \"https://api.pillowcase.su/api/download/32f9263f384531bfe11b11d033691be2\", \"size\": \"3.26 MB\", \"duration\": 192.86}", "aliases": ["DISCONNECTED", "All Yours"], "size": "3.26 MB"}, {"id": "field-trip-28", "name": "¥$ - FIELD TRIP [V14]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kodak Black"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version of \"Field Trip\" played at the Chase Center listening event and at Rolling Loud. Has an added verse from <PERSON><PERSON>, and a second <PERSON><PERSON> verse which was cut early.", "length": "4:05", "fileDate": 17102016, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/c4e76edf430e99b27b9c9bc2131f218f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c4e76edf430e99b27b9c9bc2131f218f/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version of \\\"Field Trip\\\" played at the Chase Center listening event and at Rolling Loud. Has an added verse from <PERSON><PERSON>, and a second <PERSON><PERSON> verse which was cut early.\", \"date\": 17102016, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d14d43fe815a502625b6547d3dea9297\", \"url\": \"https://api.pillowcase.su/api/download/d14d43fe815a502625b6547d3dea9297\", \"size\": \"4.09 MB\", \"duration\": 245.13}", "aliases": ["DISCONNECTED", "All Yours"], "size": "4.09 MB"}, {"id": "field-trip-29", "name": "¥$ - FIELD TRIP [V16]", "artists": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Wheezy"], "notes": "Version of \"Field Trip\" played at Ty Dolla $ign's birthday event on April 13th, 2024. <PERSON><PERSON>'s verse is now placed over the beatswitch section of the song. It's unknown if the song has Ye vocals at this point.", "length": "", "fileDate": 17129664, "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://pillowcase.su/f/5690917186824a0bebeeab407920fc14", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5690917186824a0bebeeab407920fc14\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V16]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON><PERSON><PERSON>) (prod. Wheezy)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version of \\\"Field Trip\\\" played at Ty Dolla $ign's birthday event on April 13th, 2024. <PERSON><PERSON>'s verse is now placed over the beatswitch section of the song. It's unknown if the song has Ye vocals at this point.\", \"date\": 17129664, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["DISCONNECTED", "All Yours"], "size": ""}, {"id": "field-trip-30", "name": "¥$ - FIELD TRIP [V17]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "<PERSON><PERSON>"], "notes": "Version of \"Field Trip\" played in an Abu Dhabi event. Has additional adlibs from <PERSON><PERSON> during the second <PERSON> hook. It's unknown if <PERSON><PERSON> is still on the song. Other changes are unknown.", "length": "1:13", "fileDate": 17141760, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/c64051693ad101552a7725fcfd0ff05d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c64051693ad101552a7725fcfd0ff05d/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V17]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version of \\\"Field Trip\\\" played in an Abu Dhabi event. Has additional adlibs from <PERSON><PERSON> during the second Don hook. It's unknown if <PERSON><PERSON> is still on the song. Other changes are unknown.\", \"date\": 17141760, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(0, 0, 0)\", \"rgb(243, 243, 243)\"], \"id\": \"59bbc170394f40d8e5f137b1d4106d2b\", \"url\": \"https://api.pillowcase.su/api/download/59bbc170394f40d8e5f137b1d4106d2b\", \"size\": \"1.35 MB\", \"duration\": 73.67}", "aliases": ["DISCONNECTED", "All Yours"], "size": "1.35 MB"}, {"id": "field-trip-31", "name": "¥$ - FIELD TRIP [V18]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "Real Hubi", "SHDØW", "<PERSON><PERSON>", "<PERSON>", "Timbaland", "Ojivolta", "The Legendary Traxster"], "notes": "Version played by <PERSON><PERSON><PERSON><PERSON> on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.", "length": "1:25", "fileDate": 17170272, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/a8fcb63190fa08d5fcbcc780b0116085/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a8fcb63190fa08d5fcbcc780b0116085/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>, <PERSON>, <PERSON><PERSON>\\u00d8W, <PERSON><PERSON>, <PERSON>, <PERSON>, Ojivolta & The Legendary Traxster)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version played by SHD\\u00d8W on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.\", \"date\": 17170272, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(0, 0, 0)\", \"rgb(243, 243, 243)\"], \"id\": \"378c97bfcee0b25c5b3a4f73d0f10951\", \"url\": \"https://api.pillowcase.su/api/download/378c97bfcee0b25c5b3a4f73d0f10951\", \"size\": \"1.53 MB\", \"duration\": 85.18}", "aliases": ["DISCONNECTED", "All Yours"], "size": "1.53 MB"}, {"id": "field-trip-32", "name": "¥$ - FIELD TRIP [V18]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["Wheezy", "Real Hubi", "SHDØW", "<PERSON><PERSON>", "<PERSON>", "Timbaland", "Ojivolta", "The Legendary Traxster"], "notes": "Version played by <PERSON><PERSON><PERSON><PERSON> on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.", "length": "0:20", "fileDate": 17170272, "leakDate": "", "labels": ["Recording", "Partial"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/998cdd9ebcd20ba2dfa04701075c2b06/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/998cdd9ebcd20ba2dfa04701075c2b06/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>, <PERSON>, <PERSON><PERSON>\\u00d8W, <PERSON><PERSON>, <PERSON>, <PERSON>, Ojivolta & The Legendary Traxster)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version played by SHD\\u00d8W on an Instagram livestream. Has new production compared to previous versions. Another snippet leaked May 30th 2024.\", \"date\": 17170272, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(0, 0, 0)\", \"rgb(243, 243, 243)\"], \"id\": \"e13b65b757b784dff0b0af070a738df0\", \"url\": \"https://api.pillowcase.su/api/download/e13b65b757b784dff0b0af070a738df0\", \"size\": \"503 kB\", \"duration\": 20.86}", "aliases": ["DISCONNECTED", "All Yours"], "size": "503 kB"}, {"id": "field-trip-33", "name": "¥$ - FIELD TRIP [V19]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "taydex"], "notes": "Version of \"Field Trip\" with production from <PERSON> & taydex. Snippet leaked January 1st, 2025.", "length": "0:11", "fileDate": 17356896, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/275e7bf69811dec0a42d446d5e3dc475/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/275e7bf69811dec0a42d446d5e3dc475/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V19]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Wes <PERSON> & taydex)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version of \\\"Field Trip\\\" with production from <PERSON> & taydex. Snippet leaked January 1st, 2025.\", \"date\": 17356896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"81440de6fcd65740721a0f578a895a99\", \"url\": \"https://api.pillowcase.su/api/download/81440de6fcd65740721a0f578a895a99\", \"size\": \"357 kB\", \"duration\": 11.7}", "aliases": ["DISCONNECTED", "All Yours"], "size": "357 kB"}, {"id": "field-trip-34", "name": "¥$ - FIELD TRIP [V21]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "???"], "producers": ["<PERSON><PERSON>"], "notes": "Version of \"Field Trip\" with production from <PERSON><PERSON>. Has multiple beat switches and additional adlibs. Snippet leaked November 18th, 2024.", "length": "0:15", "fileDate": 17318880, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/7dff5f60da10a80aa1d694977d5003d5/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7dff5f60da10a80aa1d694977d5003d5/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V21]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & ???) (prod. <PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version of \\\"Field Trip\\\" with production from <PERSON><PERSON>. Has multiple beat switches and additional adlibs. Snippet leaked November 18th, 2024.\", \"date\": 17318880, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"81e274fee13886b55df9898d30223157\", \"url\": \"https://api.pillowcase.su/api/download/81e274fee13886b55df9898d30223157\", \"size\": \"423 kB\", \"duration\": 15.88}", "aliases": ["DISCONNECTED", "All Yours"], "size": "423 kB"}, {"id": "field-trip-35", "name": "¥$ - FIELD TRIP [V22]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON>"], "notes": "Another alternate version of \"Field Trip\" with production from Cruza. Snippet leaked December 13th, 2024.", "length": "0:05", "fileDate": 17340480, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/705523f2bc75021a6fbe9d2340a918a4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/705523f2bc75021a6fbe9d2340a918a4/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V22]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Another alternate version of \\\"Field Trip\\\" with production from Cruza. Snippet leaked December 13th, 2024.\", \"date\": 17340480, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f55f225c7b77fa2d61dd1f708e9498bd\", \"url\": \"https://api.pillowcase.su/api/download/f55f225c7b77fa2d61dd1f708e9498bd\", \"size\": \"263 kB\", \"duration\": 5.9}", "aliases": ["DISCONNECTED", "All Yours"], "size": "263 kB"}, {"id": "field-trip-36", "name": "✨ ¥$ - FIELD TRIP [V25]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["Wheezy", "Real Hubi", "SHDØW", "<PERSON><PERSON>", "<PERSON>", "Timbaland", "Ojivolta", "The Legendary Traxster"], "notes": "OG Filename: FIELD TRIP YE REF (5.7.24)\nVersion with the newly recorded Durk verse, different mixing, and a mumble Ye verse. Has no <PERSON><PERSON>.", "length": "3:12", "fileDate": 17226432, "leakDate": "", "labels": ["Lossless", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/9292f1ee722b944a3282187f1e00aa86/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/9292f1ee722b944a3282187f1e00aa86/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u2728 \\u00a5$ - FIELD TRIP [V25]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, SH<PERSON>\\u00d8W, <PERSON><PERSON>, <PERSON>, <PERSON>, Ojivolta & The Legendary Traxster)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"OG Filename: FIELD TRIP YE REF (5.7.24)\\nVersion with the newly recorded Durk verse, different mixing, and a mumble Ye verse. Has no Kodak.\", \"date\": 17226432, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7d9f54412b4f3c626fd7886108423dc2\", \"url\": \"https://api.pillowcase.su/api/download/7d9f54412b4f3c626fd7886108423dc2\", \"size\": \"3.26 MB\", \"duration\": 192.91}", "aliases": ["DISCONNECTED", "All Yours"], "size": "3.26 MB"}, {"id": "field-trip-37", "name": "¥$ - FIELD TRIP [V26]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Kodak Black"], "producers": ["EJ PARIS", "Wheezy", "<PERSON><PERSON>", "<PERSON><PERSON>", "The Legendary Traxster", "AyoAA", "IRKO"], "notes": "Version of \"Field Trip\" played at the Korea LP, seemingly is the IRKO mix of the song but the Kodak bar saying \"Like how you riding that dick like a gangster\" is uncensored. Higher quality rip with less crowd noise later leaked.", "length": "2:43", "fileDate": 17243712, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/5e7a51c59a3f14fa0515acd1c8a52aeb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5e7a51c59a3f14fa0515acd1c8a52aeb/play\", \"key\": \"FIELD TRIP\", \"title\": \"\\u00a5$ - FIELD TRIP [V26]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, The Legendary Traxster, AyoAA & IRKO)\", \"aliases\": [\"DISCONNECTED\", \"All Yours\"], \"description\": \"Version of \\\"Field Trip\\\" played at the Korea LP, seemingly is the IRKO mix of the song but the Kodak bar saying \\\"Like how you riding that dick like a gangster\\\" is uncensored. Higher quality rip with less crowd noise later leaked.\", \"date\": 17243712, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5f9e046f3424ca8b6305392fde5c685e\", \"url\": \"https://api.pillowcase.su/api/download/5f9e046f3424ca8b6305392fde5c685e\", \"size\": \"2.79 MB\", \"duration\": 163.66}", "aliases": ["DISCONNECTED", "All Yours"], "size": "2.79 MB"}, {"id": "fuk-sumn-38", "name": "¥$ - FUK SUMN (REMIX) [V6]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Quavo"], "producers": ["Ye", "Timbaland", "<PERSON><PERSON>", "SHDØW", "VEYIS", "Digital Nas", "JPEGMAFIA"], "notes": "OG Filename: FUKK SUM 3.18.24 ME- F REF 5 ANT\nVersion of the \"FUK SUMN\" remix with the Quavo verse, added production, and a new feature from <PERSON><PERSON>, replacing a portion of <PERSON><PERSON><PERSON>'s pitched-up vocals. Meant for release on VULTURES 2. Original snippet leaked November 18th, 2024.", "length": "3:51", "fileDate": 17320608, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/a87970e09c8bb29d348f9fd0a46f0d73/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a87970e09c8bb29d348f9fd0a46f0d73/play\", \"key\": \"FUK SUMN (REMIX)\", \"title\": \"\\u00a5$ - FUK SUMN (REMIX) [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, Ant Clemons & Quavo) (prod. <PERSON>, <PERSON>baland, Hubi, SHD\\u00d8W, VEYIS, Digital Nas & JPEGMAFIA)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUCK SUMN\"], \"description\": \"OG Filename: FUKK SUM 3.18.24 ME- F REF 5 ANT\\nVersion of the \\\"FUK SUMN\\\" remix with the Quavo verse, added production, and a new feature from <PERSON><PERSON>, replacing a portion of <PERSON><PERSON><PERSON>'s pitched-up vocals. Meant for release on VULTURES 2. Original snippet leaked November 18th, 2024.\", \"date\": 17320608, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eb0901dde7cf97c546464ed870b97da0\", \"url\": \"https://api.pillowcase.su/api/download/eb0901dde7cf97c546464ed870b97da0\", \"size\": \"3.88 MB\", \"duration\": 231.6}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUCK SUMN"], "size": "3.88 MB"}, {"id": "melrose", "name": "✨ ¥$ - MELROSE [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: me<PERSON>rose_\n<PERSON> (a known and reliable seller) offered the song which is over 5 minute long, with a longer <PERSON><PERSON> verse version for groupbuy, confirming its existence. A snippet later posted by <PERSON><PERSON> and the song has been added to the MELROSE buy. Has fuller chorus, <PERSON> is doing more than just adlibs and one line and <PERSON><PERSON>'s verse is 3 times longer. Mixed terribly by <PERSON>. Features a line where <PERSON><PERSON> disses Ye - \"Cock it back aim it a nazi\".", "length": "5:29", "fileDate": 17205696, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/c5828c146386868b337df1a290ded5e3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c5828c146386868b337df1a290ded5e3/play\", \"key\": \"MELROSE\", \"title\": \"\\u2728 \\u00a5$ - MELROSE [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (Prod. TheLabCook & OjiVolta)\", \"aliases\": [\"Codeine\"], \"description\": \"OG Filename: melrose_\\n211 (a known and reliable seller) offered the song which is over 5 minute long, with a longer <PERSON><PERSON> verse version for groupbuy, confirming its existence. A snippet later posted by <PERSON><PERSON> and the song has been added to the MELROSE buy. Has fuller chorus, <PERSON> is doing more than just adlibs and one line and <PERSON><PERSON>'s verse is 3 times longer. Mixed terribly by <PERSON>. Features a line where <PERSON><PERSON> disses Ye - \\\"Cock it back aim it a nazi\\\".\", \"date\": 17205696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"12adcebdb429ca4a0834c14a5fef9ece\", \"url\": \"https://api.pillowcase.su/api/download/12adcebdb429ca4a0834c14a5fef9ece\", \"size\": \"5.44 MB\", \"duration\": 329.4}", "aliases": ["<PERSON>ine"], "size": "5.44 MB"}, {"id": "melrose-40", "name": "🗑️  ¥$ - MELROSE [V4]", "artists": ["Quavo", "<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: MELROSE - quavo\n Version of \"Melrose\" with all of the <PERSON><PERSON> <PERSON> vocals from the Febuary 20th version of the song + a Quavo feature. Uses the \"Codeine\" sample. YZY cord gb for the song, alongside \"Fear\" has opened on Dec 28, and a snippet leaked Dec 29, 2024. Full leaked on Jan 22nd, 2025. Quavo verse loud as fuck and features very inspiring lyricism such as \"Vultured up with the Vultures\". The song is slowed down compaired to MEROSE V1.", "length": "", "fileDate": 17375040, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "vultures", "originalUrl": "http://music.froste.lol/song/60236786b2ad447da41a0e394473d088/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/60236786b2ad447da41a0e394473d088/play\", \"key\": \"MELROSE\", \"title\": \"\\ud83d\\uddd1\\ufe0f  \\u00a5$ - MELROSE [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>) (Prod. TheLabCook & OjiVolta)\", \"aliases\": [\"Codeine\"], \"description\": \"OG Filename: MELROSE - quavo\\n Version of \\\"Melrose\\\" with all of the <PERSON>, <PERSON> vocals from the Febuary 20th version of the song + a Quavo feature. Uses the \\\"Codeine\\\" sample. YZY cord gb for the song, alongside \\\"Fear\\\" has opened on Dec 28, and a snippet leaked Dec 29, 2024. Full leaked on Jan 22nd, 2025. Quavo verse loud as fuck and features very inspiring lyricism such as \\\"Vultured up with the Vultures\\\". The song is slowed down compaired to MEROSE V1.\", \"date\": 17375040, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON>ine"], "size": ""}, {"id": "melrose-41", "name": "¥$ - MELROSE [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: MELROSE 3.8.24\nA song previewed on a Private VULTURES 2 LP on March 8th, 2024. Features Carti. Didn't evolve from <PERSON>'s 2020 demo named CODEINE, that uses the same sample. Not connected to BELIEVER. Said to be scrapped. A snippet has been shared by <PERSON><PERSON> on July 7th 2024, after the song has been voted for on a groupbuy poll. Mixed by <PERSON>. The song is slowed down compaired to MEROSE V1.", "length": "3:20", "fileDate": 17205696, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "vultures", "originalUrl": "https://music.froste.lol/song/421a2c6ceec7e7545a5adb15b480373d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/421a2c6ceec7e7545a5adb15b480373d/play\", \"key\": \"MELROSE\", \"title\": \"\\u00a5$ - MELROSE [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (Prod. TheLabCook & OjiVolta)\", \"aliases\": [\"Codeine\"], \"description\": \"OG Filename: MELROSE 3.8.24\\nA song previewed on a Private VULTURES 2 LP on March 8th, 2024. Features Carti. Didn't evolve from <PERSON>'s 2020 demo named CODEINE, that uses the same sample. Not connected to BELIEVER. Said to be scrapped. A snippet has been shared by <PERSON><PERSON> on July 7th 2024, after the song has been voted for on a groupbuy poll. Mixed by <PERSON>. The song is slowed down compaired to MEROSE V1.\", \"date\": 17205696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8b20efa8f8abd770ed402d34c736f8ec\", \"url\": \"https://api.pillowcase.su/api/download/8b20efa8f8abd770ed402d34c736f8ec\", \"size\": \"3.38 MB\", \"duration\": 200.66}", "aliases": ["<PERSON>ine"], "size": "3.38 MB"}]}