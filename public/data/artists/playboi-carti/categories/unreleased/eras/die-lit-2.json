{"id": "die-lit-2", "name": "Die Lit 2", "description": "(May 11, 2018) (<PERSON> Lit is officially released)\n(June 3rd, 2018) While previewing Buffy The Body, <PERSON><PERSON> says that Die Lit 2 is coming soon\n(Aug 2018) <PERSON><PERSON> mentions 'Whole Lotta Red' in an interview", "backgroundColor": "rgb(194, 38, 46)", "coverImage": "", "tracks": [{"id": "back-up", "name": "Back Up [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Initial solo version of \"Back Up\", has an open verse for Offset.", "length": "2:56", "fileDate": 15671232, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/2913125ccd8aa46e51abdb30c2438579/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2913125ccd8aa46e51abdb30c2438579/play\", \"key\": \"Back Up\", \"title\": \"Back Up [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Initial solo version of \\\"Back Up\\\", has an open verse for Offset.\", \"date\": 15671232, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"893e7b424d6dd79f5ca4af94cc199b37\", \"url\": \"https://api.pillowcase.su/api/download/893e7b424d6dd79f5ca4af94cc199b37\", \"size\": \"2.82 MB\", \"duration\": 176.38}", "aliases": [], "size": "2.82 MB"}, {"id": "buffy-the-body", "name": "Buffy the Body [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON> the Body 6.2.18\nSnippet leaked by PrivateFriend. Features a slightly different instrumental and an open verse.", "length": "3:13", "fileDate": 16651008, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/1fee0a3a739e2d60ca2f40bb1008a77c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1fee0a3a739e2d60ca2f40bb1008a77c/play\", \"key\": \"Buffy the Body\", \"title\": \"Buffy the Body [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>) \", \"aliases\": [\"Buffie The Body\", \"BUFF THE BODY\"], \"description\": \"OG Filename: Buffy the Body 6.2.18\\nSnippet leaked by PrivateFriend. Features a slightly different instrumental and an open verse.\", \"date\": 16651008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"de7125284aabe3d7a8cc37c47cfbb70d\", \"url\": \"https://api.pillowcase.su/api/download/de7125284aabe3d7a8cc37c47cfbb70d\", \"size\": \"3.1 MB\", \"duration\": 193.56}", "aliases": ["<PERSON><PERSON><PERSON>", "BUFF THE BODY"], "size": "3.1 MB"}, {"id": "buffy-the-body-3", "name": "<PERSON> The Body [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: buffy the body 6.12.18\nAlt mix.", "length": "3:25", "fileDate": 17285184, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/1819d4eeaf83a38d3d3919aa598015aa/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1819d4eeaf83a38d3d3919aa598015aa/play\", \"key\": \"Buffy The Body\", \"title\": \"Buffy The Body [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>) \", \"aliases\": [\"Buffie The Body\", \"BUFF THE BODY\"], \"description\": \"OG Filename: buffy the body 6.12.18\\nAlt mix.\", \"date\": 17285184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e6ec4559902355e11edd5adb395432cf\", \"url\": \"https://api.pillowcase.su/api/download/e6ec4559902355e11edd5adb395432cf\", \"size\": \"3.29 MB\", \"duration\": 205.62}", "aliases": ["<PERSON><PERSON><PERSON>", "BUFF THE BODY"], "size": "3.29 MB"}, {"id": "buffie-the-body", "name": "⭐ <PERSON><PERSON><PERSON> The Body [V3]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON> the body 7.2.18\nA throwaway from the 'Die Lit 2' sessions.", "length": "3:27", "fileDate": 15669504, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/d74962188cd1262e4a7c9e7fc5a61271/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d74962188cd1262e4a7c9e7fc5a61271/play\", \"key\": \"<PERSON><PERSON><PERSON> The Body\", \"title\": \"\\u2b50 <PERSON><PERSON>ie The Body [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>) \", \"aliases\": [\"Buffy The Body\", \"BUFF THE BODY\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> the body 7.2.18\\nA throwaway from the 'Die Lit 2' sessions.\", \"date\": 15669504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0dd353d77c1c3ae670964ae2c1df0e6e\", \"url\": \"https://api.pillowcase.su/api/download/0dd353d77c1c3ae670964ae2c1df0e6e\", \"size\": \"3.32 MB\", \"duration\": 207.32}", "aliases": ["<PERSON> The Body", "BUFF THE BODY"], "size": "3.32 MB"}, {"id": "mosh-pit", "name": "Mosh Pit [V2]", "artists": ["NGeeYL"], "producers": ["<PERSON> The Guru"], "notes": "OG Filename: Mosh Pit 7.18.18\nLeaked on October 10, 2024. and there's no version with Offset, this rumor was debunked by <PERSON><PERSON> himself on a live stream. On 19/07/18 NGeeYL tweeted \"I'm on carti ep ya'll go get it when it drop\" & \"Me & bra got summ else under our sleeves stay tuned\".", "length": "3:06", "fileDate": 15588288, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/31e6b178185a3298c3bcacdf52299104/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/31e6b178185a3298c3bcacdf52299104/play\", \"key\": \"Mosh Pit\", \"title\": \"Mosh Pit [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON> The Guru) \", \"aliases\": [\"Die Like This\"], \"description\": \"OG Filename: Mosh Pit 7.18.18\\nLeaked on October 10, 2024. and there's no version with Offset, this rumor was debunked by <PERSON><PERSON> himself on a live stream. On 19/07/18 <PERSON><PERSON><PERSON><PERSON><PERSON> tweeted \\\"I'm on carti ep ya'll go get it when it drop\\\" & \\\"Me & <PERSON> got summ else under our sleeves stay tuned\\\".\", \"date\": 15588288, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b3c934a80e111c19571d7e68a4b3173c\", \"url\": \"https://api.pillowcase.su/api/download/b3c934a80e111c19571d7e68a4b3173c\", \"size\": \"3.95 MB\", \"duration\": 186.96}", "aliases": ["Die Like This"], "size": "3.95 MB"}, {"id": "fuck-it-up", "name": "⭐ Fuck It Up [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carti 1 - means st pi'erre\nA throwaway from the 'Whole Lotta Red' sessions.", "length": "3:33", "fileDate": 15667776, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/a0ecc58fab1d4f1d3bdf7bce423660ef/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a0ecc58fab1d4f1d3bdf7bce423660ef/play\", \"key\": \"Fuck It Up\", \"title\": \"\\u2b50 Fuck It Up [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Did It Again\", \"Die Lit Freestyle\", \"Friday\"], \"description\": \"OG Filename: carti 1 - means st pi'erre\\nA throwaway from the 'Whole Lotta Red' sessions.\", \"date\": 15667776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f37a5849cc9cce05f8c1daa8507398a2\", \"url\": \"https://api.pillowcase.su/api/download/f37a5849cc9cce05f8c1daa8507398a2\", \"size\": \"4.2 MB\", \"duration\": 213.41}", "aliases": ["Did It Again", "Die Lit Freestyle", "Friday"], "size": "4.2 MB"}, {"id": "goku", "name": "⭐ Goku [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> 8.9.18\nA throwaway from the 'Whole Lotta Red' sessions. Has an open verse.", "length": "2:06", "fileDate": 15673824, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/7800b49b756b813d39e0357f39e4df74/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7800b49b756b813d39e0357f39e4df74/play\", \"key\": \"Goku\", \"title\": \"\\u2b50 Goku [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON>th<PERSON>\", \"R.I.P Fredo II\"], \"description\": \"OG Filename: Goku 8.9.18\\nA throwaway from the 'Whole Lotta Red' sessions. Has an open verse.\", \"date\": 15673824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b16fe5c07bc62e26b6ac18d7bd0deb53\", \"url\": \"https://api.pillowcase.su/api/download/b16fe5c07bc62e26b6ac18d7bd0deb53\", \"size\": \"2.82 MB\", \"duration\": 126.96}", "aliases": ["Asthma", "R.<PERSON>.<PERSON> II"], "size": "2.82 MB"}, {"id": "racks", "name": "Racks [Song 1]", "artists": ["RX Peso"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> 8.8.18 \nA throwaway from the Whole Lotta Red sessions. <PERSON><PERSON> later reused his verse for the song \"That's a 50\" by <PERSON>ice WRLD.", "length": "3:03", "fileDate": 15748128, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/208b3e89a09008e13da2049860c9c458/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/208b3e89a09008e13da2049860c9c458/play\", \"key\": \"Racks\", \"title\": \"Racks [Song 1]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Racks 8.8.18 \\nA throwaway from the Whole Lotta Red sessions. <PERSON><PERSON> later reused his verse for the song \\\"That's a 50\\\" by <PERSON><PERSON> WRLD.\", \"date\": 15748128, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9ec53f13414efeb20ecec9b43522178d\", \"url\": \"https://api.pillowcase.su/api/download/9ec53f13414efeb20ecec9b43522178d\", \"size\": \"2.94 MB\", \"duration\": 183.85}", "aliases": [], "size": "2.94 MB"}, {"id": "", "name": "??? [V1]", "artists": [], "producers": ["lumbyst", "Art Dealer"], "notes": "Snippet of the original version w/ different drums posted by lumbyst", "length": "", "fileDate": 15147648, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://www.youtube.com/watch?v=s3-JWPWf7L0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=s3-JWPWf7L0\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. lumbyst & Art Dealer) \", \"aliases\": [\"Rockstar\"], \"description\": \"Snippet of the original version w/ different drums posted by lumbyst\", \"date\": 15147648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Rockstar"], "size": ""}, {"id": "she-might", "name": "⭐ Redd Coldhearted - She Might [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: She Might 6.30.18\nSong was originally considered lost but a new snippet would later emerge from <PERSON> on January 13, 2023 showing the song was either recently found again or was never lost to begin with. Song would finally be leaked by <PERSON> the next day. Has a short open verse.", "length": "3:30", "fileDate": 16736544, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/4ebf5beefa21f8f33613f8eb940bf027/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4ebf5beefa21f8f33613f8eb940bf027/play\", \"key\": \"She Might\", \"title\": \"\\u2b50 Redd Coldhearted - She Might [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"ReddXTrack1\", \"Red\"], \"description\": \"OG Filename: She Might 6.30.18\\nSong was originally considered lost but a new snippet would later emerge from <PERSON> on January 13, 2023 showing the song was either recently found again or was never lost to begin with. Song would finally be leaked by <PERSON> the next day. Has a short open verse.\", \"date\": 16736544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fe97200ae3f8bd73409270af6d42c2ef\", \"url\": \"https://api.pillowcase.su/api/download/fe97200ae3f8bd73409270af6d42c2ef\", \"size\": \"3.38 MB\", \"duration\": 210.73}", "aliases": ["ReddXTrack1", "Red"], "size": "3.38 MB"}, {"id": "<PERSON><PERSON><PERSON>", "name": "✨ <PERSON> [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG version of the song. Previewed on a radio show.", "length": "1:59", "fileDate": 15333408, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/58fc6ef9f0790ac2de87dcdb7e521014/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/58fc6ef9f0790ac2de87dcdb7e521014/play\", \"key\": \"<PERSON><PERSON><PERSON>\", \"title\": \"\\u2728 <PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG version of the song. Previewed on a radio show.\", \"date\": 15333408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b7dda02361fa1230a88587c4024eff90\", \"url\": \"https://api.pillowcase.su/api/download/b7dda02361fa1230a88587c4024eff90\", \"size\": \"1.92 MB\", \"duration\": 119.98}", "aliases": [], "size": "1.92 MB"}, {"id": "buck-shots", "name": "A$AP Rocky - Buck Shots", "artists": ["<PERSON><PERSON><PERSON>", "Smooky MarGielaa"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON> <PERSON> <PERSON><PERSON><PERSON> SHOTS V4 - FNL MASTERING HOUSE - DIRTY - 5.19.18_1\nOGF for A$AP <PERSON>'s Buch Shots", "length": "2:48", "fileDate": 16555104, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/5251090f6adc131f41826d28470686a0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5251090f6adc131f41826d28470686a0/play\", \"key\": \"Buck Shots\", \"title\": \"A$AP Rocky - Buck Shots\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>y <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: AR - BUCK SHOTS V4 - FNL MASTERING HOUSE - DIRTY - 5.19.18_1\\nOGF for A$AP Rocky's Buch Shots\", \"date\": 16555104, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"90af74f0e825b9d78b0fae3022106e41\", \"url\": \"https://api.pillowcase.su/api/download/90af74f0e825b9d78b0fae3022106e41\", \"size\": \"2.69 MB\", \"duration\": 168.14}", "aliases": [], "size": "2.69 MB"}, {"id": "beam", "name": "88 <PERSON> & <PERSON> Brian - <PERSON><PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>", "Southside"], "notes": "OG Filename: RB x PBC - BEAM (rough mix)\nOGF for Beam.", "length": "2:30", "fileDate": 17137440, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/fdaadd87fcd220b9598a8a549d45fe8c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/fdaadd87fcd220b9598a8a549d45fe8c/play\", \"key\": \"Beam\", \"title\": \"88 <PERSON> & Rich Brian - Beam\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Murda Beatz & Southside)\", \"description\": \"OG Filename: RB x PBC - BEAM (rough mix)\\nOGF for Beam.\", \"date\": 17137440, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"86f23782bc6082139fd1f5cf130ba923\", \"url\": \"https://api.pillowcase.su/api/download/86f23782bc6082139fd1f5cf130ba923\", \"size\": \"2.41 MB\", \"duration\": 150.86}", "aliases": [], "size": "2.41 MB"}, {"id": "hang-up-da-phone", "name": "K$upreme - Hang Up Da Phone", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["ISOBeats"], "notes": "OG Filename: <PERSON><PERSON> Cant Pick Up rough\nOGF for Hang Up Da Phone", "length": "3:43", "fileDate": 16409952, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/8e79c0249eed54ce9e63ce99f2b4f4f1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8e79c0249eed54ce9e63ce99f2b4f4f1/play\", \"key\": \"Hang Up Da Phone\", \"title\": \"K$upreme - Hang Up Da Phone\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. ISOBeats)\", \"description\": \"OG Filename: <PERSON><PERSON> K <PERSON> Cant Pick Up rough\\nOGF for Hang Up Da Phone\", \"date\": 16409952, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d237e88915bdd38d466d868effba8c34\", \"url\": \"https://api.pillowcase.su/api/download/d237e88915bdd38d466d868effba8c34\", \"size\": \"3.58 MB\", \"duration\": 223.49}", "aliases": [], "size": "3.58 MB"}, {"id": "too-easy", "name": "Offset - Too Easy [V1]", "artists": ["<PERSON>"], "producers": ["Metro Boomin"], "notes": "First version of \"Too Easy\" with a <PERSON> Baby verse, before <PERSON><PERSON> replaced his verse.", "length": "3:25", "fileDate": 15793920, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://pillowcase.su/f/92dd15e097986ad9d55f4c6b693efa66", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/92dd15e097986ad9d55f4c6b693efa66\", \"key\": \"Too Easy\", \"title\": \"Offset - Too Easy [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> Boomin)\", \"description\": \"First version of \\\"Too Easy\\\" with a Lil Baby verse, before <PERSON><PERSON> replaced his verse.\", \"date\": 15793920, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5de888890384f1fca9f891da9ffc66b9\", \"url\": \"https://api.pillowcase.su/api/download/5de888890384f1fca9f891da9ffc66b9\", \"size\": \"4.23 MB\", \"duration\": 205.17}", "aliases": [], "size": "4.23 MB"}, {"id": "too-easy-16", "name": "Offset - Too Easy [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Metro Boomin"], "notes": "OG FIlename: Too Easy OFFSET CARTI 7.19.18\nLater version of \"Too Easy\" with a Car<PERSON> feature, replacing <PERSON>'s verse.", "length": "3:15", "fileDate": 15793920, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/121975db2799e2af5441f76fb00cf53c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/121975db2799e2af5441f76fb00cf53c/play\", \"key\": \"Too Easy\", \"title\": \"Offset - Too Easy [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Metro Boomin)\", \"description\": \"OG FIlename: Too Easy OFFSET CARTI 7.19.18\\nLater version of \\\"Too Easy\\\" with a <PERSON><PERSON> feature, replacing <PERSON>'s verse.\", \"date\": 15793920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"515282565b096799c0c3814820ed7208\", \"url\": \"https://api.pillowcase.su/api/download/515282565b096799c0c3814820ed7208\", \"size\": \"3.13 MB\", \"duration\": 195.59}", "aliases": [], "size": "3.13 MB"}, {"id": "canc-n", "name": "<PERSON><PERSON>ún [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Version without the second verse. Previewed in a video posted on instagram", "length": "0:51", "fileDate": 15304896, "leakDate": "", "labels": ["High Quality", "Snippet"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/46dae22b456e02ffd7d26dba0f6d53ad/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/46dae22b456e02ffd7d26dba0f6d53ad/play\", \"key\": \"Canc\\u00fan\", \"title\": \"Canc\\u00fan [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Money Jumpin'\"], \"description\": \"Version without the second verse. Previewed in a video posted on instagram\", \"date\": 15304896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2ab016cc89268cf82d4a2929b8838b5c\", \"url\": \"https://api.pillowcase.su/api/download/2ab016cc89268cf82d4a2929b8838b5c\", \"size\": \"821 kB\", \"duration\": 51.26}", "aliases": ["Money Jumpin'"], "size": "821 kB"}, {"id": "fuck-it-up-18", "name": "Fuck It Up [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Previewed in the leaked August 9th, 2018 Whole Lotta Red session footage. Has alternate vocals.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit-2", "originalUrl": "http://music.froste.lol/song/1e4c15fb2448dd689e9911a590755742/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/1e4c15fb2448dd689e9911a590755742/play\", \"key\": \"Fuck It Up\", \"title\": \"Fuck It Up [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Did It Again\", \"Friday\", \"Die Lit Freestyle\"], \"description\": \"Previewed in the leaked August 9th, 2018 Whole Lotta Red session footage. Has alternate vocals.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Did It Again", "Friday", "Die Lit Freestyle"], "size": ""}, {"id": "pop-bottles", "name": "Pop Bottles [V1] ", "artists": [], "producers": ["AJRuinedMyRecord"], "notes": "OG Filename: 01 Carti - Pop Bottles 7.11.18\nOriginal version of Pop Bottles previewed by <PERSON>. Said to have an open verse. Filename was also previewed by <PERSON>.", "length": "", "fileDate": 15320448, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://www.instagram.com/snippetmafia/p/DCNUT88xADG/?locale=zh-TW&hl=am-et&img_index=1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.instagram.com/snippetmafia/p/DCNUT88xADG/?locale=zh-TW&hl=am-et&img_index=1\", \"key\": \"Pop Bottles\", \"title\": \"Pop Bottles [V1] \", \"artists\": \"(prod. AJRuinedMyRecord)\", \"description\": \"OG Filename: 01 Carti - Pop Bottles 7.11.18\\nOriginal version of Pop Bottles previewed by <PERSON>. Said to have an open verse. Filename was also previewed by <PERSON>.\", \"date\": 15320448, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "she-might-20", "name": "<PERSON><PERSON> Coldhearted - She Might [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: reddxTrack1\nOG version of She Might", "length": "0:28", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/5f714a5f22c2180b0f169ff041c652ac/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5f714a5f22c2180b0f169ff041c652ac/play\", \"key\": \"She Might\", \"title\": \"Redd Coldhearted - She Might [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"ReddXTrack1\", \"Red\"], \"description\": \"OG Filename: reddxTrack1\\nOG version of She Might\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"de5b3a82fcaa7e461a985dd9fa92c803\", \"url\": \"https://api.pillowcase.su/api/download/de5b3a82fcaa7e461a985dd9fa92c803\", \"size\": \"455 kB\", \"duration\": 28.4}", "aliases": ["ReddXTrack1", "Red"], "size": "455 kB"}, {"id": "loose", "name": "RX Peso - Loose*", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "RX Peso song from the early Die Lit 2/WLR V1 Sessions. From 2018. Was up for GB in Carti Hub but people voted to not run the song. Song ended up being PB'ed and vaulted forever. The video from the studio surfaced on September 18, 2024. The song was recorded sometime in 2018.", "length": "0:14", "fileDate": 17266176, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit-2", "originalUrl": "https://music.froste.lol/song/f1188f49d5bf41270762eb5dd73926b3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f1188f49d5bf41270762eb5dd73926b3/play\", \"key\": \"Loose*\", \"title\": \"RX Peso - Loose*\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"description\": \"RX Peso song from the early Die Lit 2/WLR V1 Sessions. From 2018. Was up for GB in Carti Hub but people voted to not run the song. <PERSON> ended up being PB'ed and vaulted forever. The video from the studio surfaced on September 18, 2024. The song was recorded sometime in 2018.\", \"date\": 17266176, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"5b4749c9d88ab570ae767adf9edcbcd3\", \"url\": \"https://api.pillowcase.su/api/download/5b4749c9d88ab570ae767adf9edcbcd3\", \"size\": \"227 kB\", \"duration\": 14.14}", "aliases": [], "size": "227 kB"}]}