{"id": "die-lit", "name": "Die Lit", "description": "<PERSON> Lit is the debut studio album by <PERSON><PERSON><PERSON> and a big step in the sound he had been working on. After the success of his self-titled mixtape, <PERSON><PERSON> went even bigger, using catchy vocal styles and memorable hooks over spacey, hard-hitting beats from producers like <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. With a strong lineup of features, many consider <PERSON> Lit one of the best trap albums ever made. This album also marked the start of a change in <PERSON><PERSON>’s style. In the early days, his look and vibe were colorful and vibrant, matching the fun energy of his music. But with <PERSON>, his image started shifting toward a darker, punk-inspired feel. This wasn’t just in fashion—it showed in his performances, visuals, and the way he carried himself as an artist. More than just an album, <PERSON> helped set the stage for Whole Lotta Red. The wild energy, high-tempo beats, and chaotic feel of <PERSON>t paved the way for <PERSON><PERSON> to go even further. Whole Lotta Red would take these ideas to the next level, fully diving into punk energy, rougher beats, and a raw, aggressive sound that made it one of his most unique projects. Very underrated era in terms of leaks.", "backgroundColor": "rgb(0, 0, 0)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17Eh6KITn1WUh77dGzA5TarEabVu1RjFBkj6klszMwMXbab6RTpUg_zuBCJNwxezL7EnafvftXZ5pFBuz2FFhWLYDgxwfWnfSpLm-beyzdfk8hBa04G41IpBXXpAcIi80tDRBBYm_LkEM9O3FPgbmQ?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "3-times", "name": "⭐ 3 Times", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Carti - 3 Times\nOG Filename (Metadata): <PERSON>ti - 456\nPreviewed in @countingcasket's Instagram Live.", "length": "1:41", "fileDate": 16534368, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/73b6ebe8dc4acddf5e343b0f9f17a431/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/73b6ebe8dc4acddf5e343b0f9f17a431/play\", \"key\": \"3 Times\", \"title\": \"\\u2b50 3 Times\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"2*9\", \"Drop Top\"], \"description\": \"OG Filename: Carti - 3 Times\\nOG Filename (Metadata): Carti - 456\\nPreviewed in @countingcasket's Instagram Live.\", \"date\": 16534368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bac16fda13b40ab35f6a3def7ad69e35\", \"url\": \"https://api.pillowcase.su/api/download/bac16fda13b40ab35f6a3def7ad69e35\", \"size\": \"2.59 MB\", \"duration\": 101.52}", "aliases": ["2*9", "Drop Top"], "size": "2.59 MB"}, {"id": "ain-t-rockin-gold", "name": "✨ Ain't Rockin' Gold", "artists": ["<PERSON> Thug"], "producers": ["London on da Track"], "notes": "OG Filename: 01 <PERSON>t Rockin Gold 5.5.17\nLeaked on September 17, 2019.", "length": "2:07", "fileDate": 15686784, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/b971eea1f6bec8d137fca157b388a14f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b971eea1f6bec8d137fca157b388a14f/play\", \"key\": \"Ain't Rockin' Gold\", \"title\": \"\\u2728 Ain't Rockin' Gold\", \"artists\": \"(feat. <PERSON>) (prod. London on da Track)\", \"description\": \"OG Filename: 01 Aint Rockin Gold 5.5.17\\nLeaked on September 17, 2019.\", \"date\": 15686784, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"685802dc4c4adbbceb7dce201486483c\", \"url\": \"https://api.pillowcase.su/api/download/685802dc4c4adbbceb7dce201486483c\", \"size\": \"3.01 MB\", \"duration\": 127.49}", "aliases": [], "size": "3.01 MB"}, {"id": "alma-mater", "name": "⭐ Alma Mater", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON> 6.6.17\nA throwaway from the 'Die Lit' sessions. Leaked after a successful GB.", "length": "3:47", "fileDate": 15666912, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e6264519d5df0d64e380142cd3415407/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e6264519d5df0d64e380142cd3415407/play\", \"key\": \"Alma Mater\", \"title\": \"\\u2b50 Alma Mater\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON>\", \"Swear To God\"], \"description\": \"OG Filename: <PERSON> Mater 6.6.17\\nA throwaway from the 'Die Lit' sessions. Leaked after a successful GB.\", \"date\": 15666912, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c9b69f82f06d451e8c71bbc9ee30af57\", \"url\": \"https://api.pillowcase.su/api/download/c9b69f82f06d451e8c71bbc9ee30af57\", \"size\": \"4.6 MB\", \"duration\": 227.02}", "aliases": ["Dress", "Swear To God"], "size": "4.6 MB"}, {"id": "arm-and-leg", "name": "⭐ Arm and Leg [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON> and <PERSON><PERSON>(Final)\nLeaked on May 1, 2018. Unrelated to the Whole Lotta Red song of the same name.", "length": "3:52", "fileDate": 15252192, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/ce3e93772cc90ee86dcd07556d27936f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ce3e93772cc90ee86dcd07556d27936f/play\", \"key\": \"Arm and Leg\", \"title\": \"\\u2b50 Arm and Leg [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>boi <PERSON>- <PERSON> and Leg(Final)\\nLeaked on May 1, 2018. Unrelated to the Whole Lotta Red song of the same name.\", \"date\": 15252192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd25e47992be03e343143af1be9da7d1\", \"url\": \"https://api.pillowcase.su/api/download/bd25e47992be03e343143af1be9da7d1\", \"size\": \"4.69 MB\", \"duration\": 232.44}", "aliases": [], "size": "4.69 MB"}, {"id": "backend", "name": "Backend", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: BACKEND\nA throwaway from the 'Die Lit' sessions.", "length": "2:09", "fileDate": 16628544, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/fcf187959b405b4a2ec1776155c82349/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/fcf187959b405b4a2ec1776155c82349/play\", \"key\": \"Backend\", \"title\": \"Backend\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: BACKEND\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16628544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9f6ac1e062922ab2486be4051f91ffa5\", \"url\": \"https://api.pillowcase.su/api/download/9f6ac1e062922ab2486be4051f91ffa5\", \"size\": \"3.04 MB\", \"duration\": 129.67}", "aliases": [], "size": "3.04 MB"}, {"id": "big-sticks", "name": "Big Sticks [V1]", "artists": [], "producers": ["Metro Boomin"], "notes": "OG Filename: Carti x Metro1 2.14.18\nLeaked on July 23, 2022. A different song than the Whole Lotta Red song \"King Tut\".", "length": "3:17", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e5389f59bcb8916be48e06d8945a4cdb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e5389f59bcb8916be48e06d8945a4cdb/play\", \"key\": \"Big Sticks\", \"title\": \"Big Sticks [V1]\", \"artists\": \"(prod. <PERSON> Boomin)\", \"aliases\": [\"King Tut\"], \"description\": \"OG Filename: Carti x Metro1 2.14.18\\nLeaked on July 23, 2022. A different song than the Whole Lotta Red song \\\"King Tut\\\".\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c28e6736fa13a37182c57227fdd66e1d\", \"url\": \"https://api.pillowcase.su/api/download/c28e6736fa13a37182c57227fdd66e1d\", \"size\": \"4.13 MB\", \"duration\": 197.45}", "aliases": ["King <PERSON>"], "size": "4.13 MB"}, {"id": "blue-hunnids", "name": "🗑️ Blue Hunnids", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: PBC x <PERSON> (Hook)\nA throwaway from the 'Die Lit' sessions.", "length": "3:49", "fileDate": 15793920, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/63ef1acb37d0aaae8b33f3ac350ad446/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/63ef1acb37d0aaae8b33f3ac350ad446/play\", \"key\": \"Blue Hunnids\", \"title\": \"\\ud83d\\uddd1\\ufe0f Blue Hunnids\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: PBC x Blue Hunnids (Hook)\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15793920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"36f6673086daf5e4c10b33ae22309f2f\", \"url\": \"https://api.pillowcase.su/api/download/36f6673086daf5e4c10b33ae22309f2f\", \"size\": \"4.64 MB\", \"duration\": 229.78}", "aliases": [], "size": "4.64 MB"}, {"id": "broke-hoes", "name": "✨ Broke Hoes", "artists": ["<PERSON><PERSON>erre <PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Hoes 1.16.18\nOGF leaked on May 25, 2022.", "length": "2:34", "fileDate": 16337376, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/ad2ea87702ba0d1b0d9ae0b9406bb463/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ad2ea87702ba0d1b0d9ae0b9406bb463/play\", \"key\": \"Broke Hoes\", \"title\": \"\\u2728 Broke Hoes\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Carti World\", \"Hoes Get Played\"], \"description\": \"OG Filename: Broke Hoes 1.16.18\\nOGF leaked on May 25, 2022.\", \"date\": 16337376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"63c88418c6f7fa00ee7413824d4e159c\", \"url\": \"https://api.pillowcase.su/api/download/63c88418c6f7fa00ee7413824d4e159c\", \"size\": \"3.45 MB\", \"duration\": 154.9}", "aliases": ["Carti World", "<PERSON><PERSON>"], "size": "3.45 MB"}, {"id": "cake", "name": "⭐ Cake [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON>ake 11.21.17\nA throwaway from the 'Die Lit' sessions. Recorded on November 21, 2017.", "length": "4:22", "fileDate": 15709248, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/93862e1a4c3b062624c821a75353f8c1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/93862e1a4c3b062624c821a75353f8c1/play\", \"key\": \"Cake\", \"title\": \"\\u2b50 Cake [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Can't Relate\", \"No Relation\", \"Relate\"], \"description\": \"OG Filename: Cake 11.21.17\\nA throwaway from the 'Die Lit' sessions. Recorded on November 21, 2017.\", \"date\": 15709248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"90899bfcec16f33b065f40025bd1b60a\", \"url\": \"https://api.pillowcase.su/api/download/90899bfcec16f33b065f40025bd1b60a\", \"size\": \"5.16 MB\", \"duration\": 262.08}", "aliases": ["Can't Relate", "No Relation", "Relate"], "size": "5.16 MB"}, {"id": "no-relation", "name": "No Relation [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: No Relation 11.22.17-1\nOG Filename (Metadata): No Relation 11.22.17\nLeaked on November 7, 2019.", "length": "3:41", "fileDate": 15730848, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a31d376908d32b590219b51ac8fa3e79/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a31d376908d32b590219b51ac8fa3e79/play\", \"key\": \"No Relation\", \"title\": \"No Relation [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Cake\", \"Can't Relate\", \"Relate\"], \"description\": \"OG Filename: No Relation 11.22.17-1\\nOG Filename (Metadata): No Relation 11.22.17\\nLeaked on November 7, 2019.\", \"date\": 15730848, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"13d1c5b136093376035d5c25d99a032f\", \"url\": \"https://api.pillowcase.su/api/download/13d1c5b136093376035d5c25d99a032f\", \"size\": \"4.51 MB\", \"duration\": 221.5}", "aliases": ["Cake", "Can't Relate", "Relate"], "size": "4.51 MB"}, {"id": "cash-shit", "name": "⭐ Cash Shit", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON>\nA throwaway from the 'Die Lit' sessions.", "length": "2:31", "fileDate": 15255648, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/6e0db02016a97eae7113cf18490dceb8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6e0db02016a97eae7113cf18490dceb8/play\", \"key\": \"Cash Shit\", \"title\": \"\\u2b50 Cash Shit\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Cashin'\"], \"description\": \"OG Filename: <PERSON><PERSON>\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15255648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"baf4ea6ce22261afea5c76fbd7aa12d9\", \"url\": \"https://api.pillowcase.su/api/download/baf4ea6ce22261afea5c76fbd7aa12d9\", \"size\": \"3.4 MB\", \"duration\": 151.75}", "aliases": ["Cashin'"], "size": "3.4 MB"}, {"id": "kick-door", "name": "Kick Door [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: carti - kick door\nLeaked by @countingcaskets. Another version of Check Please.\nOGF leaked on Mar 29, 2024.", "length": "2:43", "fileDate": 16479072, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/59f83f33d17753e85c0820d669ddcddb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/59f83f33d17753e85c0820d669ddcddb/play\", \"key\": \"Kick Door\", \"title\": \"Kick Door [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"They Go Off\", \"Leash\", \"Hugh Hefner Tribute\", \"Check Please\"], \"description\": \"OG Filename: carti - kick door\\nLeaked by @countingcaskets. Another version of Check Please.\\nOGF leaked on Mar 29, 2024.\", \"date\": 16479072, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2289df3d44efe4e438b6f847be377122\", \"url\": \"https://api.pillowcase.su/api/download/2289df3d44efe4e438b6f847be377122\", \"size\": \"3.58 MB\", \"duration\": 163.51}", "aliases": ["They Go Off", "<PERSON><PERSON>", "<PERSON>", "Check Please"], "size": "3.58 MB"}, {"id": "check-please", "name": "Check Please [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "A version with less adlibs and no intro and without the autotune vocals in the background.", "length": "3:37", "fileDate": 17044992, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/5cb74fd52009f7a4224fb8372c044dff/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5cb74fd52009f7a4224fb8372c044dff/play\", \"key\": \"Check Please\", \"title\": \"Check Please [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"They Go Off\", \"Leash\", \"Hugh Hefner Tribute\", \"Kick Door\"], \"description\": \"A version with less adlibs and no intro and without the autotune vocals in the background.\", \"date\": 17044992, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"60a89616dbcd01c85326a9078735112d\", \"url\": \"https://api.pillowcase.su/api/download/60a89616dbcd01c85326a9078735112d\", \"size\": \"4.46 MB\", \"duration\": 217.97}", "aliases": ["They Go Off", "<PERSON><PERSON>", "<PERSON>", "Kick Door"], "size": "4.46 MB"}, {"id": "check-please-14", "name": "⭐ Check Please [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - Check Please (W: INTRO)\nOG Filename (Metadata): Check Please w intro\nA throwaway from the 'Die Lit' sessions.", "length": "3:37", "fileDate": 15251328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/5867e94ec12005cc8279bb59ac89bcf6/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5867e94ec12005cc8279bb59ac89bcf6/play\", \"key\": \"Check Please\", \"title\": \"\\u2b50 Check Please [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"They Go Off\", \"Leash\", \"Hugh Hefner Tribute\", \"Kick Door\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> - <PERSON> Please (W: INTRO)\\nOG Filename (Metadata): Check Please w intro\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15251328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3ea5d0b03ae2c9b788e75cb4be3593d3\", \"url\": \"https://api.pillowcase.su/api/download/3ea5d0b03ae2c9b788e75cb4be3593d3\", \"size\": \"4.46 MB\", \"duration\": 217.97}", "aliases": ["They Go Off", "<PERSON><PERSON>", "<PERSON>", "Kick Door"], "size": "4.46 MB"}, {"id": "choppa-sing", "name": "✨ <PERSON><PERSON> Sing", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: CartiBounce_A_052917\nA throwaway from the 'Die Lit' sessions.", "length": "3:32", "fileDate": 16000416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/ceac663da3239c7ab95ae7f9e02f45bb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ceac663da3239c7ab95ae7f9e02f45bb/play\", \"key\": \"Choppa Sing\", \"title\": \"\\u2728 Choppa Sing\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: CartiBounce_A_052917\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16000416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea71f087e092a1aad920fa6edddc7dac\", \"url\": \"https://api.pillowcase.su/api/download/ea71f087e092a1aad920fa6edddc7dac\", \"size\": \"4.36 MB\", \"duration\": 212.14}", "aliases": [], "size": "4.36 MB"}, {"id": "choppa-won-t-miss", "name": "✨ <PERSON><PERSON> Won't Miss [V2]", "artists": ["<PERSON> Thug", "<PERSON><PERSON>erre <PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Won't <PERSON> <PERSON><PERSON> Thug Pierre\nVersion featuring <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. It has been confirmed by <PERSON><PERSON><PERSON><PERSON> himself, that it's an official stem edit. Alongisde the beat, <PERSON><PERSON> bought the Pi'erre verse, and the verse was edited in in this version, he just didn't end up using it in the final one. There's a music video for this version, which has been seen by trusted community members. A christmas gift to the comm from <PERSON><PERSON>.", "length": "3:19", "fileDate": 17350848, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/7df55d2608d4612c9414049c2462a155/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7df55d2608d4612c9414049c2462a155/play\", \"key\": \"<PERSON><PERSON> Won't Miss\", \"title\": \"\\u2728 <PERSON><PERSON> Won't Miss [V2]\", \"artists\": \"(feat. <PERSON>hug & <PERSON>'er<PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> Won't <PERSON> <PERSON><PERSON>hug <PERSON>\\nVersion featuring <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. It has been confirmed by <PERSON><PERSON><PERSON><PERSON> himself, that it's an official stem edit. Alongisde the beat, <PERSON><PERSON> bought the Pi'erre verse, and the verse was edited in in this version, he just didn't end up using it in the final one. There's a music video for this version, which has been seen by trusted community members. A christmas gift to the comm from Avi.\", \"date\": 17350848, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0b8c5977c4856add24904d55011043af\", \"url\": \"https://api.pillowcase.su/api/download/0b8c5977c4856add24904d55011043af\", \"size\": \"4.16 MB\", \"duration\": 199.32}", "aliases": [], "size": "4.16 MB"}, {"id": "rockstar", "name": "Rockstar [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Earlier version of \"Rockstar\" with autotune.", "length": "1:34", "fileDate": 16719264, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/279319efd745377a85e3618c3ddd27d0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/279319efd745377a85e3618c3ddd27d0/play\", \"key\": \"Rockstar\", \"title\": \"Rockstar [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fell In Luv\", \"Real Rockstar\", \"<PERSON>riva<PERSON>\", \"Tanya\", \"Different Lifestyle\"], \"description\": \"Earlier version of \\\"Rockstar\\\" with autotune.\", \"date\": 16719264, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3376a15a4dd2c162783916764b9539d9\", \"url\": \"https://api.pillowcase.su/api/download/3376a15a4dd2c162783916764b9539d9\", \"size\": \"2.48 MB\", \"duration\": 94.44}", "aliases": ["Fell In Luv", "Real Rockstar", "Privacy", "<PERSON>", "Different Lifestyle"], "size": "2.48 MB"}, {"id": "rockstar-18", "name": "Rockstar [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> rough\nLeaked on March 5, 2021. Longer than the previous version.", "length": "3:03", "fileDate": 16149024, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/90ffd0564f8300bb1c659ca63255535d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/90ffd0564f8300bb1c659ca63255535d/play\", \"key\": \"Rockstar\", \"title\": \"Rockstar [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fell In Luv\", \"Real Rockstar\", \"<PERSON>riva<PERSON>\", \"Tanya\", \"Different Lifestyle\"], \"description\": \"OG Filename: <PERSON><PERSON> rough\\nLeaked on March 5, 2021. Longer than the previous version.\", \"date\": 16149024, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f3d5e34c96c999117e949663ea27ebc7\", \"url\": \"https://api.pillowcase.su/api/download/f3d5e34c96c999117e949663ea27ebc7\", \"size\": \"3.91 MB\", \"duration\": 183.98}", "aliases": ["Fell In Luv", "Real Rockstar", "Privacy", "<PERSON>", "Different Lifestyle"], "size": "3.91 MB"}, {"id": "privacy", "name": "Privacy [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> rough\nLeaked on November 7, 2019. Retitled to \"Privacy\" as seen by the OG File.", "length": "2:21", "fileDate": 15730848, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/79fdf59a5d0a3e37c7c70e49ecc2ac9b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/79fdf59a5d0a3e37c7c70e49ecc2ac9b/play\", \"key\": \"Privacy\", \"title\": \"Privacy [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fell In Luv\", \"Rockstar\", \"Real Rockstar\", \"Tanya\", \"Different Lifestyle\"], \"description\": \"OG Filename: <PERSON><PERSON> rough\\nLeaked on November 7, 2019. Retitled to \\\"Privacy\\\" as seen by the OG File.\", \"date\": 15730848, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"193223fbaeb562d6c3d5c28c3567351b\", \"url\": \"https://api.pillowcase.su/api/download/193223fbaeb562d6c3d5c28c3567351b\", \"size\": \"3.23 MB\", \"duration\": 141.48}", "aliases": ["Fell In Luv", "Rockstar", "Real Rockstar", "<PERSON>", "Different Lifestyle"], "size": "3.23 MB"}, {"id": "different-lifestyle", "name": "⭐ Different Lifestyle [V4]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Fell in Luv mix v6\nLeaked on December 29, 2018. Seemingly retitled to \"Different Lifestyle\".", "length": "2:47", "fileDate": 15460416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/1164a5f38b07a62ea88c3544bdddb82b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1164a5f38b07a62ea88c3544bdddb82b/play\", \"key\": \"Different Lifestyle\", \"title\": \"\\u2b50 Different Lifestyle [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fell In Luv\", \"Rockstar\", \"Rockstar\", \"Privacy\", \"Different Lifestyle\"], \"description\": \"OG Filename: Fell in Luv mix v6\\nLeaked on December 29, 2018. Seemingly retitled to \\\"Different Lifestyle\\\".\", \"date\": 15460416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"028dafcb98fc0bda9963cc7be0ec58d7\", \"url\": \"https://api.pillowcase.su/api/download/028dafcb98fc0bda9963cc7be0ec58d7\", \"size\": \"3.66 MB\", \"duration\": 167.93}", "aliases": ["Fell In Luv", "Rockstar", "Rockstar", "Privacy", "Different Lifestyle"], "size": "3.66 MB"}, {"id": "dog-food", "name": "✨ Dog Food [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON>1 RUFF\nA throwaway from the 'Die Lit' sessions. Leaked on April 29, 2023, with the open chorus.", "length": "3:06", "fileDate": 16827264, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e1d6ece1ea02dcf95b7fbfafcfe76e38/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e1d6ece1ea02dcf95b7fbfafcfe76e38/play\", \"key\": \"Dog Food\", \"title\": \"\\u2728 Dog Food [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> Pierre1 RUFF\\nA throwaway from the 'Die Lit' sessions. Leaked on April 29, 2023, with the open chorus.\", \"date\": 16827264, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2bd41755852bcaae297cc7aee0c63cc2\", \"url\": \"https://api.pillowcase.su/api/download/2bd41755852bcaae297cc7aee0c63cc2\", \"size\": \"3.95 MB\", \"duration\": 186.14}", "aliases": [], "size": "3.95 MB"}, {"id": "dog-food-22", "name": "Dog Food [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Dog Food 2.14.18\nLeaked on May 25, 2022.", "length": "2:10", "fileDate": 16534368, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/b974fad04c922f87877e2d9dd881039d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b974fad04c922f87877e2d9dd881039d/play\", \"key\": \"Dog Food\", \"title\": \"Dog Food [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Dog Food 2.14.18\\nLeaked on May 25, 2022.\", \"date\": 16534368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b37f17d947f26895ce2e687e0431a819\", \"url\": \"https://api.pillowcase.su/api/download/b37f17d947f26895ce2e687e0431a819\", \"size\": \"3.05 MB\", \"duration\": 130.08}", "aliases": [], "size": "3.05 MB"}, {"id": "dog-food-23", "name": "⭐ Dog Food [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Dog Food DC Main Mix-V3\nVersion of Dog Food with different mix. Leaked on October 14, 2018.", "length": "2:09", "fileDate": 15394752, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/b8599e215bb93f45e302af0e21407644/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b8599e215bb93f45e302af0e21407644/play\", \"key\": \"Dog Food\", \"title\": \"\\u2b50 Dog Food [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Dog Food DC Main Mix-V3\\nVersion of Dog Food with different mix. Leaked on October 14, 2018.\", \"date\": 15394752, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7d830d605d94e67120fd841cf6c428da\", \"url\": \"https://api.pillowcase.su/api/download/7d830d605d94e67120fd841cf6c428da\", \"size\": \"3.04 MB\", \"duration\": 129.67}", "aliases": [], "size": "3.04 MB"}, {"id": "dolce-gabbana", "name": "🗑️ Dolce & Gabbana*", "artists": [], "producers": ["Southside"], "notes": "OG Filename: Carti - 12.8.17 Untitled\nOG File Metadata: Carti x Sizzle 12.8.17\nLeaked on December 31, 2019, with an open verse.", "length": "3:06", "fileDate": 15777504, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/cd824c68c26e2cdc4c864a0cf00d8884/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cd824c68c26e2cdc4c864a0cf00d8884/play\", \"key\": \"Dolce & Gabbana*\", \"title\": \"\\ud83d\\uddd1\\ufe0f Dolce & Gabbana*\", \"artists\": \"(prod. Southside)\", \"aliases\": [\"Fashion\", \"No Cappin\"], \"description\": \"OG Filename: Carti - 12.8.17 Untitled\\nOG File Metadata: Carti x Sizzle 12.8.17\\nLeaked on December 31, 2019, with an open verse.\", \"date\": 15777504, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ebc87a21c1ad8ee8fba185ee5a03bfb2\", \"url\": \"https://api.pillowcase.su/api/download/ebc87a21c1ad8ee8fba185ee5a03bfb2\", \"size\": \"3.95 MB\", \"duration\": 186.58}", "aliases": ["Fashion", "No Cappin"], "size": "3.95 MB"}, {"id": "dope", "name": "Dope", "artists": [], "producers": ["DY Krazy"], "notes": "OG Filename: <PERSON><PERSON><PERSON>\nLeaked randomly on r/CARTILEAKS by u/Worldly-Tax7525.", "length": "1:15", "fileDate": 16708032, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/72dda1c69e5f4d4cfc133d7b5b23b39a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/72dda1c69e5f4d4cfc133d7b5b23b39a/play\", \"key\": \"Dope\", \"title\": \"Dope\", \"artists\": \"(prod. <PERSON><PERSON> Krazy)\", \"description\": \"OG Filename: DOPE\\nLeaked randomly on r/CARTILEAKS by u/Worldly-Tax7525.\", \"date\": 16708032, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"787e427b7cdfaaef0006de29dde01195\", \"url\": \"https://api.pillowcase.su/api/download/787e427b7cdfaaef0006de29dde01195\", \"size\": \"2.18 MB\", \"duration\": 75.86}", "aliases": [], "size": "2.18 MB"}, {"id": "oi", "name": "GVVAAN - Oi!", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Uses the \"Right Now\" beat. Made before or around the same time the beat was given to <PERSON><PERSON>. Has alternate production.", "length": "", "fileDate": 14937696, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://soundcloud.com/gvvaan/oi-oi-oi", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://soundcloud.com/gvvaan/oi-oi-oi\", \"key\": \"Oi!\", \"title\": \"GVVAAN - Oi!\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Uses the \\\"Right Now\\\" beat. Made before or around the same time the beat was given to Carti. Has alternate production.\", \"date\": 14937696, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "want-my-m-s", "name": "<PERSON><PERSON> <PERSON> - Want My M's", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "producers": ["Detrakz", "Priority Beatz", "StevieNickXX", "Squat Beatz"], "notes": "Leaked on August 29, 2019.", "length": "3:38", "fileDate": 15670368, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a04d3ea583d8eb536393ae05d22cae84/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a04d3ea583d8eb536393ae05d22cae84/play\", \"key\": \"Want My M's\", \"title\": \"Kap G - Want My M's\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, <PERSON><PERSON>, <PERSON> & S<PERSON><PERSON>z)\", \"description\": \"Leaked on August 29, 2019.\", \"date\": 15670368, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"73af39742657d15165024fbed376c156\", \"url\": \"https://api.pillowcase.su/api/download/73af39742657d15165024fbed376c156\", \"size\": \"4.47 MB\", \"duration\": 218.85}", "aliases": [], "size": "4.47 MB"}, {"id": "i-got", "name": "⭐ I Got", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG File: <PERSON><PERSON><PERSON>- I Got\nA throwaway from the 'Die Lit' sessions.", "length": "4:29", "fileDate": 15665184, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/71abfac035f66c0222d57f0810c04b2d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/71abfac035f66c0222d57f0810c04b2d/play\", \"key\": \"I Got\", \"title\": \"\\u2b50 I Got\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Tattoo\"], \"description\": \"OG File: <PERSON><PERSON><PERSON>- <PERSON> Got\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15665184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8b644d0f5b1e42f9a625049f69ecbe7b\", \"url\": \"https://api.pillowcase.su/api/download/8b644d0f5b1e42f9a625049f69ecbe7b\", \"size\": \"5.28 MB\", \"duration\": 269.64}", "aliases": ["Tattoo"], "size": "5.28 MB"}, {"id": "that-bitch", "name": "That Bitch (Freestyle) [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Carti - 123_v2\nA version reusing the 2nd verse and the chorus only", "length": "1:24", "fileDate": 17141760, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/70fac50de5b98566a8c7d8360b80296e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/70fac50de5b98566a8c7d8360b80296e\", \"key\": \"That Bitch (Freestyle)\", \"title\": \"That Bitch (Freestyle) [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Carti - 123_v2\\nA version reusing the 2nd verse and the chorus only\", \"date\": 17141760, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fd66d4b4df8b405e495bb49adbec5cde\", \"url\": \"https://api.pillowcase.su/api/download/fd66d4b4df8b405e495bb49adbec5cde\", \"size\": \"2.31 MB\", \"duration\": 84.07}", "aliases": [], "size": "2.31 MB"}, {"id": "goyard-shopping-bag", "name": "✨ Goyard Shopping Bag", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Goyard Shopping Bag 11.19.17\nOG File (Metadata): <PERSON><PERSON> Soss2 RUFF\nUses the beat that would go on to be used for \"Watch\" by <PERSON>.", "length": "2:58", "fileDate": 16736544, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d74853db0a1a461da46a324d424d402b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d74853db0a1a461da46a324d424d402b/play\", \"key\": \"Goyard Shopping Bag\", \"title\": \"\\u2728 Goyard Shopping Bag\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"Watch\"], \"description\": \"OG Filename: Goyard Shopping Bag 11.19.17\\nOG File (Metadata): <PERSON><PERSON>ss2 RUFF\\nUses the beat that would go on to be used for \\\"Watch\\\" by <PERSON>\", \"date\": 16736544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"868ce8dcc7f97ae0bef472812de1ec6b\", \"url\": \"https://api.pillowcase.su/api/download/868ce8dcc7f97ae0bef472812de1ec6b\", \"size\": \"3.82 MB\", \"duration\": 178.2}", "aliases": ["Watch"], "size": "3.82 MB"}, {"id": "lobby", "name": "⭐️ Lobby [V1]", "artists": [], "producers": ["Metro Boomin"], "notes": "OG Filename: Carti Metro open 2nd\nA throwaway from the 'Die Lit' sessions.", "length": "3:23", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/0b7af71e26250394c50971e2e2862bbc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0b7af71e26250394c50971e2e2862bbc/play\", \"key\": \"Lobby\", \"title\": \"\\u2b50\\ufe0f Lobby [V1]\", \"artists\": \"(prod. <PERSON> Boomin)\", \"aliases\": [\"I Walk In\"], \"description\": \"OG Filename: Carti Metro open 2nd\\nA throwaway from the 'Die Lit' sessions.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d867820aad61608d17c3f93d94d50aff\", \"url\": \"https://api.pillowcase.su/api/download/d867820aad61608d17c3f93d94d50aff\", \"size\": \"4.23 MB\", \"duration\": 203.74}", "aliases": ["I Walk In"], "size": "4.23 MB"}, {"id": "i-walk-in", "name": "✨ I Walk In [V2]", "artists": [], "producers": ["Metro Boomin"], "notes": "OG Filename: Carti Metro I Walk In\nLeaked on August 25, 2020.", "length": "3:27", "fileDate": 15983136, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d773d61565fb473fa67db52ff44dabc7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d773d61565fb473fa67db52ff44dabc7/play\", \"key\": \"I Walk In\", \"title\": \"\\u2728 I Walk In [V2]\", \"artists\": \"(prod. <PERSON> Boomin)\", \"aliases\": [\"Lobby\"], \"description\": \"OG Filename: Carti Metro I Walk In\\nLeaked on August 25, 2020.\", \"date\": 15983136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2fb22f25850ea111b7a41cde3b78b499\", \"url\": \"https://api.pillowcase.su/api/download/2fb22f25850ea111b7a41cde3b78b499\", \"size\": \"4.29 MB\", \"duration\": 207.86}", "aliases": ["Lobby"], "size": "4.29 MB"}, {"id": "issa-dub", "name": "✨ <PERSON><PERSON>", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> 12.6.17\nA throwaway from the 'Die Lit' sessions. Recorded the same day as \"Right Now\".", "length": "2:20", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/68cd381234b134e40939a31a97d04cb8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/68cd381234b134e40939a31a97d04cb8/play\", \"key\": \"<PERSON><PERSON> Dub\", \"title\": \"\\u2728 <PERSON>sa Dub\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"More Of Those\"], \"description\": \"OG Filename: Issa Dub 12.6.17\\nA throwaway from the 'Die Lit' sessions. Recorded the same day as \\\"Right Now\\\".\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"437d9232b55bb13b652cdfd36e0794ec\", \"url\": \"https://api.pillowcase.su/api/download/437d9232b55bb13b652cdfd36e0794ec\", \"size\": \"3.22 MB\", \"duration\": 140.9}", "aliases": ["More Of Those"], "size": "3.22 MB"}, {"id": "essentials", "name": "⭐️ Essentials", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Essentials Rough\nPreviewed in @countingcasket's Instagram Live. File with the OG quality and without Pi'erre tag surfaced on Oct 12, 2022. OGF leaked on Dec 25, 2022.", "length": "2:33", "fileDate": 16491168, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/498aa972bc759f46bd4ba10f7ef379f7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/498aa972bc759f46bd4ba10f7ef379f7/play\", \"key\": \"Essentials\", \"title\": \"\\u2b50\\ufe0f Essentials\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Canary Diamonds\", \"Wedding Ring\"], \"description\": \"OG Filename: Essentials Rough\\nPreviewed in @countingcasket's Instagram Live. File with the OG quality and without Pi'erre tag surfaced on Oct 12, 2022. OGF leaked on Dec 25, 2022.\", \"date\": 16491168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6ae7ca54a8d8928b4c3a92cf0bbe1d94\", \"url\": \"https://api.pillowcase.su/api/download/6ae7ca54a8d8928b4c3a92cf0bbe1d94\", \"size\": \"3.43 MB\", \"duration\": 153.89}", "aliases": ["Canary Diamonds", "Wedding Ring"], "size": "3.43 MB"}, {"id": "fashion-nova", "name": "✨ Fashion Nova", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Fashion Nova 11.30.17\nA throwaway from the 'Die Lit' sessions.", "length": "2:34", "fileDate": 16008192, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d9879a272b97112af49b7ed16cb7b4f5/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d9879a272b97112af49b7ed16cb7b4f5/play\", \"key\": \"Fashion Nova\", \"title\": \"\\u2728 Fashion Nova\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"Don't Miss\"], \"description\": \"OG Filename: Fashion Nova 11.30.17\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16008192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f822f15d4c306c80935340760012177b\", \"url\": \"https://api.pillowcase.su/api/download/f822f15d4c306c80935340760012177b\", \"size\": \"3.44 MB\", \"duration\": 154.68}", "aliases": ["Don't Miss"], "size": "3.44 MB"}, {"id": "fall-in-love", "name": "⭐ Fall In Love", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: CART<PERSON>-FALL IN LOVE ( PB mae3 beat ) <PERSON><PERSON><PERSON> b\nOG Filename (Metadata): CARTi-pierre track ( mae3 beat\nA throwaway from the 'Die Lit' sessions.", "length": "2:33", "fileDate": 15189984, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d7088e83e41eae9fe5e6d5a1c222cdac/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d7088e83e41eae9fe5e6d5a1c222cdac/play\", \"key\": \"Fall In Love\", \"title\": \"\\u2b50 Fall In Love\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Seeing Colors\", \"Movin' Different\"], \"description\": \"OG Filename: CARTi-FALL IN LOVE ( PB mae3 beat ) He<PERSON> west<PERSON> b\\nOG Filename (Metadata): CARTi-pierre track ( mae3 beat\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15189984, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7756318bbe53eb6331eed88d55b9f408\", \"url\": \"https://api.pillowcase.su/api/download/7756318bbe53eb6331eed88d55b9f408\", \"size\": \"3.42 MB\", \"duration\": 153.22}", "aliases": ["Seeing Colors", "Movin' Different"], "size": "3.42 MB"}, {"id": "fisher-price", "name": "🗑️ Fisher Price", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carti - fisher price\nA throwaway from the 'Die Lit' sessions.", "length": "6:05", "fileDate": 16011648, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/43969b10bab98447c26b94f893e54b20/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/43969b10bab98447c26b94f893e54b20/play\", \"key\": \"Fisher Price\", \"title\": \"\\ud83d\\uddd1\\ufe0f Fisher Price\", \"artists\": \"(prod. <PERSON><PERSON>er<PERSON>)\", \"description\": \"OG Filename: carti - fisher price\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16011648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eb79cdaa181967798587a2be42b8fde4\", \"url\": \"https://api.pillowcase.su/api/download/eb79cdaa181967798587a2be42b8fde4\", \"size\": \"6.81 MB\", \"duration\": 365.26}", "aliases": [], "size": "6.81 MB"}, {"id": "floor", "name": "Floor", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Pbc floor\nPreviewed in @countingcasket's Instagram Live. Leaked on July 23, 2022. It has a way too loud snare, making it hard to listen.", "length": "3:13", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/4b31a6ecf21953f84a245010716f2d57/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4b31a6ecf21953f84a245010716f2d57/play\", \"key\": \"Floor\", \"title\": \"Floor\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Big B's\"], \"description\": \"OG Filename: Pbc floor\\nPreviewed in @countingcasket's Instagram Live. Leaked on July 23, 2022. It has a way too loud snare, making it hard to listen.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b6864cf289697634296d8e8c29d92531\", \"url\": \"https://api.pillowcase.su/api/download/b6864cf289697634296d8e8c29d92531\", \"size\": \"4.07 MB\", \"duration\": 193.94}", "aliases": ["Big B's"], "size": "4.07 MB"}, {"id": "from-da-gutta", "name": "✨ From Da Gutta", "artists": ["<PERSON> Nudy"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON> From Da Gutta\nA throwaway from the 'Die Lit' sessions.", "length": "3:28", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/21535ba4c114fd649303f69b81f2e0cc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/21535ba4c114fd649303f69b81f2e0cc/play\", \"key\": \"From Da Gutta\", \"title\": \"\\u2728 From Da Gutta\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Ra<PERSON> Up\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x <PERSON><PERSON> From <PERSON> Gutta\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9a9dcbf41a790125f18611cd8bd4f013\", \"url\": \"https://api.pillowcase.su/api/download/9a9dcbf41a790125f18611cd8bd4f013\", \"size\": \"4.3 MB\", \"duration\": 208.44}", "aliases": ["Racks Up"], "size": "4.3 MB"}, {"id": "fuck-my-ex", "name": "✨ Fuck My Ex", "artists": [], "producers": [], "notes": "OG Filename: <PERSON>BC Fuck My Ex\nOG Filename: She My Ex 12.1.17\nA throwaway from the 'Die Lit' sessions. Has a Cut Open", "length": "2:15", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/2f4af15446d2b0d3fe382b7e9f60005d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2f4af15446d2b0d3fe382b7e9f60005d/play\", \"key\": \"Fuck My Ex\", \"title\": \"\\u2728 Fuck My Ex\", \"aliases\": [\"She My Ex\"], \"description\": \"OG Filename: PBC Fuck My Ex\\nOG Filename: She My Ex 12.1.17\\nA throwaway from the 'Die Lit' sessions. Has a Cut Open\", \"date\": 16585344, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cacbde26b76dcc83daa5eca88b8043c7\", \"url\": \"https://api.pillowcase.su/api/download/cacbde26b76dcc83daa5eca88b8043c7\", \"size\": \"3.14 MB\", \"duration\": 135.96}", "aliases": ["She My Ex"], "size": "3.14 MB"}, {"id": "fuck-school", "name": "⭐ Fuck School", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> 444_1\nA throwaway from the 'Die Lit' sessions.", "length": "3:29", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/040e9db2c482e3a62d3f587e88104483/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/040e9db2c482e3a62d3f587e88104483/play\", \"key\": \"Fuck School\", \"title\": \"\\u2b50 Fuck School\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Mad\", \"Rules\", \"Highschool\"], \"description\": \"OG Filename: Carti 444_1\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ecfdd645bf23780f5d98318ca1c63269\", \"url\": \"https://api.pillowcase.su/api/download/ecfdd645bf23780f5d98318ca1c63269\", \"size\": \"4.32 MB\", \"duration\": 209.64}", "aliases": ["Mad", "Rules", "Highschool"], "size": "4.32 MB"}, {"id": "jole5", "name": "✨ Jole5", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> - Jole5\nLeaked in early 2018. Title is unknown, beat name is <PERSON><PERSON><PERSON>.", "length": "3:13", "fileDate": 15175296, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/90df27a1317b2882cdb807a9d4bed916/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/90df27a1317b2882cdb807a9d4bed916/play\", \"key\": \"<PERSON>le<PERSON>\", \"title\": \"\\u2728 <PERSON>le5\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Hoes Fo Sho\", \"Unicorn\"], \"description\": \"OG Filename: <PERSON><PERSON> - <PERSON>\\nLeaked in early 2018. Title is unknown, beat name is <PERSON><PERSON><PERSON>.\", \"date\": 15175296, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7ba8ae0d9e8048d982b892c8b6b6d48b\", \"url\": \"https://api.pillowcase.su/api/download/7ba8ae0d9e8048d982b892c8b6b6d48b\", \"size\": \"2.52 MB\", \"duration\": 193.66}", "aliases": ["<PERSON><PERSON>", "Unicorn"], "size": "2.52 MB"}, {"id": "all-of-these", "name": "✨ All Of These*", "artists": [], "producers": ["DY Krazy"], "notes": "OG Filename: <PERSON><PERSON> 2\nLeaked on July 23, 2022.", "length": "1:34", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/25612eef3e1c997f8e92787b64c49018/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/25612eef3e1c997f8e92787b64c49018/play\", \"key\": \"All Of These*\", \"title\": \"\\u2728 All Of These*\", \"artists\": \"(prod. <PERSON><PERSON> Krazy)\", \"aliases\": [\"<PERSON><PERSON>\", \"<PERSON>\"], \"description\": \"OG Filename: <PERSON><PERSON> Dy 2\\nLeaked on July 23, 2022.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c7e200d107423577ec9b37490eb16a50\", \"url\": \"https://api.pillowcase.su/api/download/c7e200d107423577ec9b37490eb16a50\", \"size\": \"2.48 MB\", \"duration\": 94.63}", "aliases": ["<PERSON><PERSON>", "Hurt"], "size": "2.48 MB"}, {"id": "like-me", "name": "✨ Like Me", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON> ME\nUses the same beat as <PERSON>'s song \"Rude\". Leaked on July 23, 2022.", "length": "0:57", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/919548322049339cc1597cdd05b48321/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/919548322049339cc1597cdd05b48321/play\", \"key\": \"Like Me\", \"title\": \"\\u2728 Like Me\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Rude\", \"Meanstreet\"], \"description\": \"OG Filename: LIKE ME\\nUses the same beat as <PERSON>'s song \\\"Rude\\\". Leaked on July 23, 2022.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5124c835d8a7396faac2c9ce491847c1\", \"url\": \"https://api.pillowcase.su/api/download/5124c835d8a7396faac2c9ce491847c1\", \"size\": \"1.89 MB\", \"duration\": 57.31}", "aliases": ["<PERSON><PERSON>", "Meanstreet"], "size": "1.89 MB"}, {"id": "lil-bih", "name": "<PERSON>", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: PBC 7-20-17 sac1\nA throwaway from the 'Die Lit' sessions.", "length": "1:57", "fileDate": 16824672, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/6ede50171d50691abe1b3a5fc2458f32/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6ede50171d50691abe1b3a5fc2458f32/play\", \"key\": \"<PERSON> Bih\", \"title\": \"<PERSON> Bih\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: PBC 7-20-17 sac1\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16824672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"210f1faad21cd4ea8d41385901c7a022\", \"url\": \"https://api.pillowcase.su/api/download/210f1faad21cd4ea8d41385901c7a022\", \"size\": \"2.85 MB\", \"duration\": 117.91}", "aliases": [], "size": "2.85 MB"}, {"id": "lit", "name": "✨ Lit", "artists": ["Lotto Savage", "Loso Loaded"], "producers": ["CuB<PERSON>z", "<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Lit 2.12.18\nLeaked on May 25, 2022.", "length": "3:20", "fileDate": 16534368, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/687c5a37bb9269c88e5acdcac0462cca/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/687c5a37bb9269c88e5acdcac0462cca/play\", \"key\": \"Lit\", \"title\": \"\\u2728 Lit\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON><PERSON>) (prod. CuBeatz & Pi\\u2019<PERSON><PERSON>)\", \"description\": \"OG Filename: Lit 2.12.18\\nLeaked on May 25, 2022.\", \"date\": 16534368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f0da7d81e0963115fcb7b30788bdebef\", \"url\": \"https://api.pillowcase.su/api/download/f0da7d81e0963115fcb7b30788bdebef\", \"size\": \"4.17 MB\", \"duration\": 200.11}", "aliases": [], "size": "4.17 MB"}, {"id": "shawty-in-love", "name": "<PERSON><PERSON> In Love [V1]", "artists": [], "producers": ["TM88", "Southside"], "notes": "OG Filename: <PERSON><PERSON> x <PERSON> in love\nLeaked on February 22, 2018.", "length": "4:11", "fileDate": 15192576, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/ba1c1383b9d03746ef1551192176e935/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ba1c1383b9d03746ef1551192176e935/play\", \"key\": \"<PERSON><PERSON> In Love\", \"title\": \"<PERSON><PERSON> In Love [V1]\", \"artists\": \"(prod. TM88 & Southside)\", \"aliases\": [\"Luv With The Geek\", \"Shawty In Luv\"], \"description\": \"OG Filename: <PERSON><PERSON> x <PERSON><PERSON> in love\\nLeaked on February 22, 2018.\", \"date\": 15192576, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"36b1083c53c6878fa7c879fafc1b0596\", \"url\": \"https://api.pillowcase.su/api/download/36b1083c53c6878fa7c879fafc1b0596\", \"size\": \"5 MB\", \"duration\": 251.88}", "aliases": ["Luv With The Geek", "Shawty In Luv"], "size": "5 MB"}, {"id": "luv-with-the-geek", "name": "Luv With The Geek [V2]", "artists": [], "producers": ["TM88", "Southside"], "notes": "OG Filename: in luv with the geek v2.2 CARTI SS (gwag2)\nLeaked on June 6, 2019.", "length": "4:11", "fileDate": 15597792, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/481e53d1478b506ae1eefc24587bc417/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/481e53d1478b506ae1eefc24587bc417/play\", \"key\": \"Luv With The Geek\", \"title\": \"Luv With The Geek [V2]\", \"artists\": \"(prod. TM88 & Southside)\", \"aliases\": [\"Shawty In Love\", \"Shawty In Luv\"], \"description\": \"OG Filename: in luv with the geek v2.2 CARTI SS (gwag2)\\nLeaked on June 6, 2019.\", \"date\": 15597792, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c51a4d5861802c70e89f0265a79a0813\", \"url\": \"https://api.pillowcase.su/api/download/c51a4d5861802c70e89f0265a79a0813\", \"size\": \"5 MB\", \"duration\": 251.88}", "aliases": ["<PERSON><PERSON> In Love", "Shawty In Luv"], "size": "5 MB"}, {"id": "luv-with-the-geek-49", "name": "Luv With The Geek [V3]", "artists": [], "producers": ["TM88", "Southside"], "notes": "OG Filename: CARTI luv with the geek v3.1 (southside gwag2)\nLeaked on May 21, 2019.", "length": "3:44", "fileDate": 15583968, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/70b8e57c048342cbc76b0fc116672f71/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/70b8e57c048342cbc76b0fc116672f71/play\", \"key\": \"Luv With The Geek\", \"title\": \"Luv With The Geek [V3]\", \"artists\": \"(prod. TM88 & Southside)\", \"aliases\": [\"Shawty In Love\", \"Shawty In Luv\"], \"description\": \"OG Filename: CARTI luv with the geek v3.1 (southside gwag2)\\nLeaked on May 21, 2019.\", \"date\": 15583968, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3bdb71ad59e572876235d65b27603bbd\", \"url\": \"https://api.pillowcase.su/api/download/3bdb71ad59e572876235d65b27603bbd\", \"size\": \"4.56 MB\", \"duration\": 224.66}", "aliases": ["<PERSON><PERSON> In Love", "Shawty In Luv"], "size": "4.56 MB"}, {"id": "luv-with-the-geek-50", "name": "⭐ Luv With The Geek [V4]", "artists": [], "producers": ["TM88", "Southside"], "notes": "OG Filename: <PERSON><PERSON><PERSON> With The Geek V4\nLeaked on June 6, 2019.", "length": "3:45", "fileDate": 15597792, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e3ac7b449a95140f168de25016ac2f37/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e3ac7b449a95140f168de25016ac2f37/play\", \"key\": \"Luv With The Geek\", \"title\": \"\\u2b50 Luv With <PERSON> Geek [V4]\", \"artists\": \"(prod. TM88 & Southside)\", \"aliases\": [\"Shawty In Love\", \"Shawty In Luv\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> With The Geek V4\\nLeaked on June 6, 2019.\", \"date\": 15597792, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"692549b0bc79c00279c9ed51b9a2d587\", \"url\": \"https://api.pillowcase.su/api/download/692549b0bc79c00279c9ed51b9a2d587\", \"size\": \"4.58 MB\", \"duration\": 225.94}", "aliases": ["<PERSON><PERSON> In Love", "Shawty In Luv"], "size": "4.58 MB"}, {"id": "back-on-the-act", "name": "Back On The Act [V1]", "artists": ["Young Sizzle"], "producers": ["CHASETHEMONEY"], "notes": "OG Filename: carti sizzle - back on the act v1.1\nLeaked on June 6, 2019. Considered for Die Lit at some point according to bot<PERSON><PERSON>.", "length": "2:50", "fileDate": 15597792, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/ca727caaf610df2740341f140b3ffdea/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ca727caaf610df2740341f140b3ffdea/play\", \"key\": \"Back On The Act\", \"title\": \"Back On The Act [V1]\", \"artists\": \"(feat. <PERSON>) (prod. CHASETHEMONEY)\", \"aliases\": [\"Back At It\"], \"description\": \"OG Filename: carti sizzle - back on the act v1.1\\nLeaked on June 6, 2019. Considered for Die Lit at some point according to botmert.\", \"date\": 15597792, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a9b354f464f874e6ff90dcca0138f546\", \"url\": \"https://api.pillowcase.su/api/download/a9b354f464f874e6ff90dcca0138f546\", \"size\": \"3.7 MB\", \"duration\": 170.78}", "aliases": ["Back At It"], "size": "3.7 MB"}, {"id": "drip", "name": "✨️ Drip [V5]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "EVIL"], "notes": "OG Filename: <PERSON><PERSON> 12.30.17\nA throwaway from the 'Die Lit' sessions.", "length": "3:27", "fileDate": 15332544, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/242951e8c35d4c1116592d98b6c1730e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/242951e8c35d4c1116592d98b6c1730e/play\", \"key\": \"Drip\", \"title\": \"\\u2728\\ufe0f Drip [V5]\", \"artists\": \"(prod. IkeBeatz & EVIL)\", \"aliases\": [\"With The Reds\", \"Shawty Wanna Skate\", \"Red & Blue\", \"Red N Blue\"], \"description\": \"OG Filename: Drip 12.30.17\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15332544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7b0a5fa4ed54e735d3031319bb6bc8e3\", \"url\": \"https://api.pillowcase.su/api/download/7b0a5fa4ed54e735d3031319bb6bc8e3\", \"size\": \"4.28 MB\", \"duration\": 207.17}", "aliases": ["With The Reds", "<PERSON><PERSON>e", "Red & Blue", "Red N Blue"], "size": "4.28 MB"}, {"id": "gon-smash", "name": "✨ Gon Smash", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A throwaway from the 'Die Lit' sessions. Not from 2019 as advertised by the seller and allegedly from the same week as \"Uh Uh\" and \"Mileage\" in January. GB for 5k on Apr 3, 2024. Recorded at Paramount", "length": "1:56", "fileDate": 17121024, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/494cdbdc886950476cc8972013cb7666/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/494cdbdc886950476cc8972013cb7666/play\", \"key\": \"Gon Smash\", \"title\": \"\\u2728 Gon Smash\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"A throwaway from the 'Die Lit' sessions. Not from 2019 as advertised by the seller and allegedly from the same week as \\\"Uh Uh\\\" and \\\"Mileage\\\" in January. GB for 5k on Apr 3, 2024. Recorded at Paramount\", \"date\": 17121024, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bbed012c6213098aff6ec39fcdb04742\", \"url\": \"https://api.pillowcase.su/api/download/bbed012c6213098aff6ec39fcdb04742\", \"size\": \"2.82 MB\", \"duration\": 116.02}", "aliases": [], "size": "2.82 MB"}, {"id": "kate-moss", "name": "✨ <PERSON>", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> kev6 Paramount 1.2.18 idea \nA throwaway from the 'Die Lit' sessions.", "length": "", "fileDate": 16932672, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e5d7e72fa3ee3b23e160e1ec1d2b478a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e5d7e72fa3ee3b23e160e1ec1d2b478a\", \"key\": \"<PERSON>\", \"title\": \"\\u2728 <PERSON>\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> kev6 Paramount 1.2.18 idea \\nA throwaway from the 'Die Lit' sessions.\", \"date\": 16932672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "kids", "name": "Kids", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Kids 12.6.17\nA throwaway from the 'Die Lit' sessions.", "length": "2:15", "fileDate": 15385248, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/80cf0c3a0f580dd6d0ba76981e491bdd/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/80cf0c3a0f580dd6d0ba76981e491bdd/play\", \"key\": \"Kids\", \"title\": \"Kids\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Kids 12.6.17\\nA throwaway from the 'Die Lit' sessions.\", \"date\": 15385248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25cfbf7a6821747a2d104b04106d0311\", \"url\": \"https://api.pillowcase.su/api/download/25cfbf7a6821747a2d104b04106d0311\", \"size\": \"3.14 MB\", \"duration\": 135.43}", "aliases": [], "size": "3.14 MB"}, {"id": "kids-56", "name": "Kids [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Kids DC Main Mix - V1\nRelease ready file for \"Kids\".", "length": "2:15", "fileDate": 15385248, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/80cf0c3a0f580dd6d0ba76981e491bdd/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/80cf0c3a0f580dd6d0ba76981e491bdd/play\", \"key\": \"Kids\", \"title\": \"Kids [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Kids DC Main Mix - V1\\nRelease ready file for \\\"Kids\\\".\", \"date\": 15385248, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25cfbf7a6821747a2d104b04106d0311\", \"url\": \"https://api.pillowcase.su/api/download/25cfbf7a6821747a2d104b04106d0311\", \"size\": \"3.14 MB\", \"duration\": 135.43}", "aliases": [], "size": "3.14 MB"}, {"id": "icey", "name": "Melii <PERSON> <PERSON><PERSON> (Remix)", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename; CARTI ICEY 1.16.18\nLeaked on April 9, 2022.", "length": "3:08", "fileDate": 16494624, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/40f0b06ebecdcbfb0107389aee332a1c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/40f0b06ebecdcbfb0107389aee332a1c/play\", \"key\": \"<PERSON><PERSON> (Remix)\", \"title\": \"<PERSON><PERSON> <PERSON> <PERSON><PERSON> (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename; CARTI ICEY 1.16.18\\nLeaked on April 9, 2022.\", \"date\": 16494624, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"29965441e0bc47136e373741f5fe91e4\", \"url\": \"https://api.pillowcase.su/api/download/29965441e0bc47136e373741f5fe91e4\", \"size\": \"3.99 MB\", \"duration\": 188.79}", "aliases": [], "size": "3.99 MB"}, {"id": "flex", "name": "RX Peso - Flex [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> (original verse)\nUnreleased collaboration between <PERSON><PERSON><PERSON> and RX Peso.", "length": "2:40", "fileDate": 16526592, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/c0af5eba7fccb27b2eb7f43c01c10f9e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c0af5eba7fccb27b2eb7f43c01c10f9e/play\", \"key\": \"Flex\", \"title\": \"RX Peso - Flex [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Good Credit\"], \"description\": \"OG Filename: <PERSON><PERSON> (original verse)\\nUnreleased collaboration between <PERSON><PERSON><PERSON> and RX Peso.\", \"date\": 16526592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d10834527179129061124b08d6efb778\", \"url\": \"https://api.pillowcase.su/api/download/d10834527179129061124b08d6efb778\", \"size\": \"3.54 MB\", \"duration\": 160.56}", "aliases": ["Good Credit"], "size": "3.54 MB"}, {"id": "bands-up", "name": "<PERSON><PERSON><PERSON><PERSON> - Bands Up [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON>s Up\nBands up without the violins.", "length": "2:45", "fileDate": 17306784, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/964d08fe0ba3d48437917f25a79588fe/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/964d08fe0ba3d48437917f25a79588fe/play\", \"key\": \"Bands Up\", \"title\": \"<PERSON><PERSON>er<PERSON> - Bands Up [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON>s Up\\nBands up without the violins.\", \"date\": 17306784, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bbe6a7235ea7c8882fa662940e681ce8\", \"url\": \"https://api.pillowcase.su/api/download/bbe6a7235ea7c8882fa662940e681ce8\", \"size\": \"3.61 MB\", \"duration\": 165.02}", "aliases": [], "size": "3.61 MB"}, {"id": "bands-up-60", "name": "⭐️ <PERSON><PERSON>er<PERSON> - Bands Up [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "<PERSON><PERSON><PERSON><PERSON>'s version of Bands Up. Was originally meant to be on The Life Of Pi'erre 5 but was left off for unknown reasons.", "length": "", "fileDate": 15741216, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/056e766bd04f7df98f95a6cec651bdc0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/056e766bd04f7df98f95a6cec651bdc0\", \"key\": \"Bands Up\", \"title\": \"\\u2b50\\ufe0f <PERSON><PERSON>er<PERSON> - Bands Up [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"<PERSON><PERSON>er<PERSON>'s version of Bands Up. Was originally meant to be on The Life Of Pi'erre 5 but was left off for unknown reasons.\", \"date\": 15741216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "juice", "name": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Early version of \"Juice\" by <PERSON><PERSON><PERSON><PERSON> that features some vocals from <PERSON><PERSON> at the start.", "length": "", "fileDate": 16553376, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/91f566b8b82d77b4df1ca3b06d7188e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/91f566b8b82d77b4df1ca3b06d7188e4\", \"key\": \"Juice\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> <PERSON> - <PERSON>ice [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON>'s\", \"C Notes\"], \"description\": \"Early version of \\\"Juice\\\" by <PERSON><PERSON><PERSON><PERSON> that features some vocals from <PERSON><PERSON> at the start.\", \"date\": 16553376, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON><PERSON><PERSON>'s", "C Notes"], "size": ""}, {"id": "want-to", "name": "<PERSON>d Coldhearted - Want To (Remix)", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["GuyATL", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Want To 11.28.17\nPreviewed in @countingcasket's Instagram Live, <PERSON><PERSON> was likely cut from the final release due to Swoosh <PERSON> & <PERSON> beefing.", "length": "3:14", "fileDate": 15722208, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/0b7b820567b32707a90715b5d30c65cf/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0b7b820567b32707a90715b5d30c65cf/play\", \"key\": \"Want To (Remix)\", \"title\": \"Redd Coldhearted - Want To (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Want To 11.28.17\\nPreviewed in @countingcasket's Instagram Live, <PERSON><PERSON> was likely cut from the final release due to <PERSON>woosh <PERSON> & <PERSON> beefing.\", \"date\": 15722208, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1642aaa86b40476ca4dc9c8984aef32c\", \"url\": \"https://api.pillowcase.su/api/download/1642aaa86b40476ca4dc9c8984aef32c\", \"size\": \"4.09 MB\", \"duration\": 194.98}", "aliases": [], "size": "4.09 MB"}, {"id": "all-of-them", "name": "<PERSON> - All Of Them [V4]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: <PERSON> The Kid Ft. Play<PERSON><PERSON> - All Of Them\nA remix made by RTK, has different mix and intro.", "length": "3:12", "fileDate": 16745184, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/70321f59863b3dcd011381a73816f59f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/70321f59863b3dcd011381a73816f59f/play\", \"key\": \"All Of Them\", \"title\": \"<PERSON> The Kid - All Of Them [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: Rich The Kid Ft. PlayB<PERSON> - All Of Them\\nA remix made by RTK, has different mix and intro.\", \"date\": 16745184, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"83a3a1fa3cc42714ff1f942d84549c61\", \"url\": \"https://api.pillowcase.su/api/download/83a3a1fa3cc42714ff1f942d84549c61\", \"size\": \"4.04 MB\", \"duration\": 192.07}", "aliases": [], "size": "4.04 MB"}, {"id": "killa", "name": "⭐️ <PERSON>a", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> 11.19.17\nOG File Metadata: <PERSON><PERSON> RUFF\nA throwaway from the 'Die Lit' sessions. Leaked on June 6, 2019, with an open verse.", "length": "3:12", "fileDate": 15597792, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/be58ccdc9f615efbcb0f497aa343bfd8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/be58ccdc9f615efbcb0f497aa343bfd8/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"\\u2b50\\ufe0f Kill<PERSON>\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Dropped Out\", \"Graduation Party\"], \"description\": \"OG Filename: Killa 11.19.17\\nOG File Metadata: <PERSON><PERSON>ss RUFF\\nA throwaway from the 'Die Lit' sessions. Leaked on June 6, 2019, with an open verse.\", \"date\": 15597792, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d3c5dd413958f75c1870f9af55281166\", \"url\": \"https://api.pillowcase.su/api/download/d3c5dd413958f75c1870f9af55281166\", \"size\": \"4.05 MB\", \"duration\": 192.41}", "aliases": ["Dropped Out", "Graduation Party"], "size": "4.05 MB"}, {"id": "last-year", "name": "Last Year", "artists": [], "producers": ["?"], "notes": "OG Filename: 01 Last Year 12.10.17\nA throwaway from the 'Die Lit' sessions. Recorded on December 10, 2017. Only has Intro and Chorus.", "length": "3:46", "fileDate": 15793920, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a021e2b459814f588242d8a09171249b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a021e2b459814f588242d8a09171249b/play\", \"key\": \"Last Year\", \"title\": \"Last Year\", \"artists\": \"(prod. ?)\", \"description\": \"OG Filename: 01 Last Year 12.10.17\\nA throwaway from the 'Die Lit' sessions. Recorded on December 10, 2017. Only has Intro and Chorus.\", \"date\": 15793920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"621f7acee7399e35b43cd219dd0beaba\", \"url\": \"https://api.pillowcase.su/api/download/621f7acee7399e35b43cd219dd0beaba\", \"size\": \"4.59 MB\", \"duration\": 226.39}", "aliases": [], "size": "4.59 MB"}, {"id": "no-lie", "name": "⭐ No Lie", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: NO LIE\nLeaked on January 3, 2019.", "length": "3:01", "fileDate": 15464736, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e2b097aa98d17b67ea74abffa970f5d3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e2b097aa98d17b67ea74abffa970f5d3/play\", \"key\": \"No Lie\", \"title\": \"\\u2b50 No Lie\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: NO LIE\\nLeaked on January 3, 2019.\", \"date\": 15464736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2d3c9e6ffe84d1e811571d815ea089ab\", \"url\": \"https://api.pillowcase.su/api/download/2d3c9e6ffe84d1e811571d815ea089ab\", \"size\": \"3.87 MB\", \"duration\": 181.22}", "aliases": [], "size": "3.87 MB"}, {"id": "pool", "name": "🗑️ Pool", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Carti - S3 (ref)\nLeaked on July 23, 2022. Has open verse.", "length": "4:06", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/4a3486106e600c736a33d406f50fa96b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4a3486106e600c736a33d406f50fa96b/play\", \"key\": \"Pool\", \"title\": \"\\ud83d\\uddd1\\ufe0f Pool\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"S3\"], \"description\": \"OG Filename: Carti - S3 (ref)\\nLeaked on July 23, 2022. Has open verse.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8db0cc260c99c98362c45a1474c60f86\", \"url\": \"https://api.pillowcase.su/api/download/8db0cc260c99c98362c45a1474c60f86\", \"size\": \"4.91 MB\", \"duration\": 246.24}", "aliases": ["S3"], "size": "4.91 MB"}, {"id": "celine", "name": "✨ Celine", "artists": [], "producers": ["Southside"], "notes": "A throwaway from the 'Die Lit' sessions. Unrelated to the Whole Lotta Red song of the same name.", "length": "3:54", "fileDate": 15973632, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/51108b019d951be87888c1c2b0ea9595/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/51108b019d951be87888c1c2b0ea9595/play\", \"key\": \"Celine\", \"title\": \"\\u2728 Celine\", \"artists\": \"(prod. South<PERSON>)\", \"aliases\": [\"Molly My Bean\"], \"description\": \"A throwaway from the 'Die Lit' sessions. Unrelated to the Whole Lotta Red song of the same name.\", \"date\": 15973632, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"066e9b8c9326798172bb2666737f7e78\", \"url\": \"https://api.pillowcase.su/api/download/066e9b8c9326798172bb2666737f7e78\", \"size\": \"4.72 MB\", \"duration\": 234.62}", "aliases": ["<PERSON>"], "size": "4.72 MB"}, {"id": "noon", "name": "<PERSON> Nudy - <PERSON><PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["BL$$D"], "notes": "OG Filename: <PERSON><PERSON><PERSON> x Carti\nLeaked on August 20, 2022.", "length": "3:40", "fileDate": 16609536, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d290ad4a516aaf5a6e691bc74e6132f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d290ad4a516aaf5a6e691bc74e6132f1\", \"key\": \"Noon\", \"title\": \"<PERSON> Nudy - Noon\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. BL$$D)\", \"aliases\": [\"Show Ya\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x <PERSON>\\nLeaked on August 20, 2022.\", \"date\": 16609536, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2b15f3bb193d58ece5a8d4c5b2451af4\", \"url\": \"https://api.pillowcase.su/api/download/2b15f3bb193d58ece5a8d4c5b2451af4\", \"size\": \"4.49 MB\", \"duration\": 220.31}", "aliases": ["Show Ya"], "size": "4.49 MB"}, {"id": "mine", "name": "⭐ Mine", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON>- Mine\nLeaked on January 1, 2019.", "length": "2:57", "fileDate": 15463008, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/eddf61b9904587a8389c4b695d76e4e3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/eddf61b9904587a8389c4b695d76e4e3/play\", \"key\": \"Mine\", \"title\": \"\\u2b50 Mine\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"A Lot On My Mind\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON>\\nLeaked on January 1, 2019.\", \"date\": 15463008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bedf3dd9689dd3284f156354bb1f2bf4\", \"url\": \"https://api.pillowcase.su/api/download/bedf3dd9689dd3284f156354bb1f2bf4\", \"size\": \"3.81 MB\", \"duration\": 177.34}", "aliases": ["A Lot On My Mind"], "size": "3.81 MB"}, {"id": "r-i-p-peep", "name": "✨ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Peep [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 12.6.17\nHas the same hook and beat as <PERSON><PERSON><PERSON><PERSON><PERSON> but has completely different verses and no <PERSON> Nudy feature with alternate production.", "length": "2:45", "fileDate": 16148160, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/bf9791cd970fabafc318f28ee4d01b32/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/bf9791cd970fabafc318f28ee4d01b32/play\", \"key\": \"R.I.P<PERSON> Peep\", \"title\": \"\\u2728 R.<PERSON><PERSON><PERSON><PERSON> Peep [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON>.<PERSON><PERSON><PERSON>\", \"Notice Me\"], \"description\": \"OG Filename: R.I.P. Peep 12.6.17\\nHas the same hook and beat as R.I<PERSON><PERSON> but has completely different verses and no Young Nudy feature with alternate production.\", \"date\": 16148160, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ff9022c73eea1a16ce138ee8183cf41b\", \"url\": \"https://api.pillowcase.su/api/download/ff9022c73eea1a16ce138ee8183cf41b\", \"size\": \"3.61 MB\", \"duration\": 165.31}", "aliases": ["<PERSON><PERSON><PERSON><PERSON>", "Notice Me"], "size": "3.61 MB"}, {"id": "r-i-p-peep-72", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Peep 12.12.17\nDifferent mix of R.I.P Peep", "length": "2:45", "fileDate": 17116704, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a7e0400cce060bfb697a184587df93df/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a7e0400cce060bfb697a184587df93df/play\", \"key\": \"R.I.P. Peep\", \"title\": \"R.<PERSON><PERSON><PERSON><PERSON> Peep [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON><PERSON><PERSON> Fred<PERSON>\", \"Notice Me\"], \"description\": \"OG Filename: R.I.P. Peep 12.12.17\\nDifferent mix of R.I.P Peep\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea6709c0f9860d0099fd2f7f767e0593\", \"url\": \"https://api.pillowcase.su/api/download/ea6709c0f9860d0099fd2f7f767e0593\", \"size\": \"3.61 MB\", \"duration\": 165.31}", "aliases": ["<PERSON><PERSON><PERSON><PERSON>", "Notice Me"], "size": "3.61 MB"}, {"id": "no-feel", "name": "No Feel", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: NO FEEL 11.29.17\nA throwaway recorded on November 29, 2017. Leaked November 7, 2019.", "length": "3:19", "fileDate": 15730848, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e4d953b82cd7fd2cbf56bb3ababb75ca/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e4d953b82cd7fd2cbf56bb3ababb75ca/play\", \"key\": \"No Feel\", \"title\": \"No Feel\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: NO FEEL 11.29.17\\nA throwaway recorded on November 29, 2017. Leaked November 7, 2019.\", \"date\": 15730848, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b1ded36e470446c26060d6e96a9ab717\", \"url\": \"https://api.pillowcase.su/api/download/b1ded36e470446c26060d6e96a9ab717\", \"size\": \"4.16 MB\", \"duration\": 199.22}", "aliases": [], "size": "4.16 MB"}, {"id": "supersonic", "name": "Supersonic [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Carti - 11.12.17\nFeatures the song \"Poppin The Trunk\" by IndigoChildRick in the intro. Leaked the same day as \"Fall In Love\"", "length": "2:21", "fileDate": 15189984, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/08e223977c1b571641a38814f1862091/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/08e223977c1b571641a38814f1862091/play\", \"key\": \"Supersonic\", \"title\": \"Supersonic [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"He Ain't Got It\", \"Sonic The Hedgehog\"], \"description\": \"OG Filename: Carti - 11.12.17\\nFeatures the song \\\"Poppin The Trunk\\\" by IndigoChildRick in the intro. Leaked the same day as \\\"Fall In Love\\\"\", \"date\": 15189984, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e10dfaa902cbc76361155f2cb6684ece\", \"url\": \"https://api.pillowcase.su/api/download/e10dfaa902cbc76361155f2cb6684ece\", \"size\": \"3.23 MB\", \"duration\": 141.1}", "aliases": ["He Ain't Got It", "Sonic The Hedgehog"], "size": "3.23 MB"}, {"id": "supersonic-75", "name": "⭐ Supersonic [V2]", "artists": ["SahBabii"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> x sah\nOG Filename (Metadata): CARTISAHB\nLater version of the song with a SahBabii feature that leaked on Dec 29, 2019.", "length": "2:59", "fileDate": 15775776, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/7b958d062c05c3fb4176273d4edd94c8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7b958d062c05c3fb4176273d4edd94c8/play\", \"key\": \"Supersonic\", \"title\": \"\\u2b50 Supersonic [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"He Ain't Got It\", \"Sonic The Hedgehog\"], \"description\": \"OG Filename: <PERSON><PERSON> x sah\\nOG Filename (Metadata): CARTISAHB\\nLater version of the song with a SahBabii feature that leaked on Dec 29, 2019.\", \"date\": 15775776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a409b0cf319448cf3812b5e90c7874db\", \"url\": \"https://api.pillowcase.su/api/download/a409b0cf319448cf3812b5e90c7874db\", \"size\": \"3.84 MB\", \"duration\": 179.47}", "aliases": ["He Ain't Got It", "Sonic The Hedgehog"], "size": "3.84 MB"}, {"id": "2038", "name": "⭐ 2038*", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> x Uzi nye7 1.3.18 Paramount\nA throwaway from the Die Lit/16*29 sessions. <PERSON> was considered to be on Die Lit under the name \"2038\". Seemingly untitled. Was recorded during the January 16*29 sessions.", "length": "2:22", "fileDate": 15446592, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/fc1868a63856d252eee99fbee8f340bc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/fc1868a63856d252eee99fbee8f340bc/play\", \"key\": \"2038*\", \"title\": \"\\u2b50 2038*\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Throw It Up\", \"16*29\", \"16\", \"29\"], \"description\": \"OG Filename: <PERSON><PERSON> x Uzi nye7 1.3.18 Paramount\\nA throwaway from the Die Lit/16*29 sessions. <PERSON> was considered to be on Die Lit under the name \\\"2038\\\". Seemingly untitled. Was recorded during the January 16*29 sessions.\", \"date\": 15446592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"473e2a5d6623af3d889216371fbbde7e\", \"url\": \"https://api.pillowcase.su/api/download/473e2a5d6623af3d889216371fbbde7e\", \"size\": \"2.74 MB\", \"duration\": 142.37}", "aliases": ["Throw It Up", "16*29", "16", "29"], "size": "2.74 MB"}, {"id": "texas", "name": "⭐ Texas [V1]", "artists": [], "producers": ["<PERSON>", "Southside"], "notes": "OG Filename: Texas 12.4.17\nLeaked on March 5, 2021. Beat was later used on <PERSON><PERSON>'s \"Southside and Guwop (Outro)\". Has an open verse for <PERSON><PERSON>.", "length": "3:26", "fileDate": 16149024, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/9a8ceb79eaf07432a4fc38acd8b9a0a1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/9a8ceb79eaf07432a4fc38acd8b9a0a1/play\", \"key\": \"Texas\", \"title\": \"\\u2b50 Texas [V1]\", \"artists\": \"(prod. <PERSON> & Southside)\", \"description\": \"OG Filename: Texas 12.4.17\\nLeaked on March 5, 2021. Beat was later used on <PERSON><PERSON> Mane's \\\"Southside and Guwop (Outro)\\\". Has an open verse for <PERSON><PERSON>.\", \"date\": 16149024, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"815dcb5b9ec3d54687dd216afd0b3a80\", \"url\": \"https://api.pillowcase.su/api/download/815dcb5b9ec3d54687dd216afd0b3a80\", \"size\": \"4.27 MB\", \"duration\": 206.5}", "aliases": [], "size": "4.27 MB"}, {"id": "toke", "name": "To<PERSON> [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Toke 2.9.18 SWITCH\nEarly version of \"Toke\".", "length": "2:38", "fileDate": 16572384, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d7570629205237d4bbb0f42a61d07e63/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d7570629205237d4bbb0f42a61d07e63/play\", \"key\": \"Toke\", \"title\": \"Toke [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Bust Down\", \"Toke Shit\"], \"description\": \"OG Filename: Toke 2.9.18 SWITCH\\nEarly version of \\\"Toke\\\".\", \"date\": 16572384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"68316a6512c8d8e72ea99fb36d47c1f1\", \"url\": \"https://api.pillowcase.su/api/download/68316a6512c8d8e72ea99fb36d47c1f1\", \"size\": \"3.51 MB\", \"duration\": 158.71}", "aliases": ["Bust Down", "Toke <PERSON>"], "size": "3.51 MB"}, {"id": "toke-79", "name": "<PERSON><PERSON> [V4]", "artists": ["G Herbo"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Main Mix - V7 Herb Only-11\nLater version of \"Toke\" featuring <PERSON>.", "length": "3:27", "fileDate": 15393888, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/4ea9a6fd44c1a9d5cbfccea3564f0e2b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4ea9a6fd44c1a9d5cbfccea3564f0e2b/play\", \"key\": \"Toke\", \"title\": \"Toke [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Bust Down\", \"Toke Shit\"], \"description\": \"OG Filename: Toke DC Main Mix - V7 Herb Only-11\\nLater version of \\\"Toke\\\" featuring <PERSON><PERSON>.\", \"date\": 15393888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"174a047e3929813d8295d5fd57461262\", \"url\": \"https://api.pillowcase.su/api/download/174a047e3929813d8295d5fd57461262\", \"size\": \"4.29 MB\", \"duration\": 207.55}", "aliases": ["Bust Down", "Toke <PERSON>"], "size": "4.29 MB"}, {"id": "toke-shit", "name": "⭐ Toke Shit [V5]", "artists": ["G Herbo"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Toke Shit x FATOL RUFF MIX 04\nSeemingly an even later version of \"Toke\" with a slightly different structure, improved mix and vocal effects on <PERSON>'s verse.", "length": "3:27", "fileDate": 16736544, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/687e8428fd2bf3cccc9bee39e5ef66b4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/687e8428fd2bf3cccc9bee39e5ef66b4/play\", \"key\": \"Toke Shit\", \"title\": \"\\u2b50 Toke Shit [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Bust Down\", \"Toke\"], \"description\": \"OG Filename: Toke Shit x FATOL RUFF MIX 04\\nSeemingly an even later version of \\\"Toke\\\" with a slightly different structure, improved mix and vocal effects on <PERSON>'s verse.\", \"date\": 16736544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"69bff7fc430da75f75b11073389b203f\", \"url\": \"https://api.pillowcase.su/api/download/69bff7fc430da75f75b11073389b203f\", \"size\": \"4.29 MB\", \"duration\": 207.34}", "aliases": ["Bust Down", "<PERSON><PERSON>"], "size": "4.29 MB"}, {"id": "trap", "name": "Trap", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Trap\nPreviewed in #CashCarti's Snapchat story. Leaked on May 26, 2022. Recorded during the same session as work.", "length": "2:56", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/39c1751ebc4aebd1ad90166a3647b779/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/39c1751ebc4aebd1ad90166a3647b779/play\", \"key\": \"Trap\", \"title\": \"Trap\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Classroom\"], \"description\": \"OG Filename: Trap\\nPreviewed in #CashCarti's Snapchat story. Leaked on May 26, 2022. Recorded during the same session as work.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"709c7224f2bbdd3e710f7711986e51c1\", \"url\": \"https://api.pillowcase.su/api/download/709c7224f2bbdd3e710f7711986e51c1\", \"size\": \"3.8 MB\", \"duration\": 176.78}", "aliases": ["Classroom"], "size": "3.8 MB"}, {"id": "uber", "name": "✨ Uber", "artists": [], "producers": ["Southside"], "notes": "OG Filename: 01 Uber\nThrowaway from Die lit sessions.", "length": "3:40", "fileDate": 16638912, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a9c77c4734b6347b18860d296c514914/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a9c77c4734b6347b18860d296c514914/play\", \"key\": \"Uber\", \"title\": \"\\u2728 Uber\", \"artists\": \"(prod. South<PERSON>)\", \"description\": \"OG Filename: 01 Uber\\nThrowaway from Die lit sessions.\", \"date\": 16638912, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"14852851f9ca911f3174dd56bf8727eb\", \"url\": \"https://api.pillowcase.su/api/download/14852851f9ca911f3174dd56bf8727eb\", \"size\": \"4.49 MB\", \"duration\": 220.22}", "aliases": [], "size": "4.49 MB"}, {"id": "vlone-jacket", "name": "✨ VLone Jacket", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Jacket\nEdited version leaked on September 23, 2020 and the full version leaked March 20, 2021.", "length": "3:17", "fileDate": 16161984, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/04e139cdc861058ac203fb4c04eeefe4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/04e139cdc861058ac203fb4c04eeefe4/play\", \"key\": \"VLone Jacket\", \"title\": \"\\u2728 VLone Jacket\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Guns Out\"], \"description\": \"OG Filename: <PERSON><PERSON> Jack<PERSON>\\nEdited version leaked on September 23, 2020 and the full version leaked March 20, 2021.\", \"date\": 16161984, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f2b38ed02d808967d2f177848afdef4\", \"url\": \"https://api.pillowcase.su/api/download/8f2b38ed02d808967d2f177848afdef4\", \"size\": \"4.12 MB\", \"duration\": 197.28}", "aliases": ["Guns Out"], "size": "4.12 MB"}, {"id": "high-school", "name": "⭐  High School", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: CARTI 222_01.30.18\nOG File Metadata: <PERSON><PERSON> Highschool\nUses the same beat as <PERSON>'s \"New York in June\". Leaked on July 23, 2022.", "length": "2:55", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/53159b30d76cdd9559505271ef7bdc48/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/53159b30d76cdd9559505271ef7bdc48/play\", \"key\": \"High School\", \"title\": \"\\u2b50  High School\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Way Back\", \"New York In June\", \"Way Back In Highschool\", \"222\"], \"description\": \"OG Filename: CARTI 222_01.30.18\\nOG File Metadata: Carti Pierre Highschool\\nUses the same beat as <PERSON>'s \\\"New York in June\\\". Leaked on July 23, 2022.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a7aab1ebc0aceee9a25731cd90760144\", \"url\": \"https://api.pillowcase.su/api/download/a7aab1ebc0aceee9a25731cd90760144\", \"size\": \"3.78 MB\", \"duration\": 175.63}", "aliases": ["Way Back", "New York In June", "Way Back In Highschool", "222"], "size": "3.78 MB"}, {"id": "way-too-geeked", "name": "✨ Way Too Geeked", "artists": [], "producers": ["<PERSON>", "Lil 88"], "notes": "OG Filename: <PERSON><PERSON><PERSON> x <PERSON> [Way Too Geeked] x AJ\nA throwaway from the 'Die Lit' sessions. Does NOT have <PERSON> Flores's SSN. Leaked on July 23, 2022.", "length": "2:22", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/11b42879b1a313d93ee16d791430baf9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/11b42879b1a313d93ee16d791430baf9/play\", \"key\": \"Way Too Geeked\", \"title\": \"\\u2728 Way Too Geeked\", \"artists\": \"(prod. <PERSON> & <PERSON> 88) \", \"aliases\": [\"150117025\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> x Canon [Way Too Geeked] x AJ\\nA throwaway from the 'Die Lit' sessions. Does NOT have <PERSON>'s SSN. Leaked on July 23, 2022.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"49a670fb099b621d41360c261a7c2de7\", \"url\": \"https://api.pillowcase.su/api/download/49a670fb099b621d41360c261a7c2de7\", \"size\": \"3.25 MB\", \"duration\": 142.56}", "aliases": ["150117025"], "size": "3.25 MB"}, {"id": "work", "name": "Work", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Work\nBeat would later be used by <PERSON><PERSON><PERSON> on the track \"Like This\". Leaked on March 5, 2021. Recorded during the same session as trap.", "length": "2:59", "fileDate": 16149024, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/08ce03fd67b7aa771898901db9c380dc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/08ce03fd67b7aa771898901db9c380dc/play\", \"key\": \"Work\", \"title\": \"Work\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Like This\"], \"description\": \"OG Filename: Work\\nBeat would later be used by <PERSON><PERSON><PERSON> on the track \\\"Like This\\\". Leaked on March 5, 2021. Recorded during the same session as trap.\", \"date\": 16149024, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d98151d8e633a3127bdd27ed81866708\", \"url\": \"https://api.pillowcase.su/api/download/d98151d8e633a3127bdd27ed81866708\", \"size\": \"3.84 MB\", \"duration\": 179.74}", "aliases": ["Like This"], "size": "3.84 MB"}, {"id": "choppa", "name": "<PERSON> [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: nufye 2 bounce\nDemo version and mostly open verse, also has a different <PERSON> verse. Leaked on October 20, 2019.", "length": "6:11", "fileDate": 15715296, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/12365b9b169fc11428240ebe3f1fa4c7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/12365b9b169fc11428240ebe3f1fa4c7/play\", \"key\": \"Choppa\", \"title\": \"<PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: nufye 2 bounce\\nDemo version and mostly open verse, also has a different <PERSON> verse. Leaked on October 20, 2019.\", \"date\": 15715296, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"979955116df3a5abcabf451e098209b6\", \"url\": \"https://api.pillowcase.su/api/download/979955116df3a5abcabf451e098209b6\", \"size\": \"6.92 MB\", \"duration\": 371.74}", "aliases": [], "size": "6.92 MB"}, {"id": "choppa-88", "name": "✨ <PERSON> [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carticompleted\nLeaked on October 23, 2019", "length": "5:21", "fileDate": 15717888, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e54a1686d0e6c804a5270581f7c4aa93/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e54a1686d0e6c804a5270581f7c4aa93/play\", \"key\": \"Choppa\", \"title\": \"\\u2728 <PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Freeze\", \"Choppa Right Beneath Me\"], \"description\": \"OG Filename: carticompleted\\nLeaked on October 23, 2019\", \"date\": 15717888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4156fe585c4f96ba7295c44d0165d740\", \"url\": \"https://api.pillowcase.su/api/download/4156fe585c4f96ba7295c44d0165d740\", \"size\": \"6.11 MB\", \"duration\": 321.14}", "aliases": ["Freeze", "Choppa Right Beneath Me"], "size": "6.11 MB"}, {"id": "bartier-cardi", "name": "✨ Cardi B - <PERSON><PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["30 Roc", "Ch<PERSON>ze Beatz"], "notes": "OG Filename: Carti x Cardi VERSE 12.8.17\nOriginal version of \"Bartier Cardi\" that features <PERSON><PERSON><PERSON>ti instead of 21 Savage.", "length": "1:50", "fileDate": 15793920, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/bdf5df4ed342d24caeef51bf8ead20f3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/bdf5df4ed342d24caeef51bf8ead20f3/play\", \"key\": \"Bart<PERSON> Cardi\", \"title\": \"\\u2728 <PERSON>i B - Bartier Cardi\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. 30 R<PERSON> & Cheeze Beatz)\", \"description\": \"OG Filename: Carti x Cardi VERSE 12.8.17\\nOriginal version of \\\"Bartier Cardi\\\" that features <PERSON><PERSON><PERSON> instead of 21 Savage.\", \"date\": 15793920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a921581f9e0ee7e8fe904dcb2c1f573e\", \"url\": \"https://api.pillowcase.su/api/download/a921581f9e0ee7e8fe904dcb2c1f573e\", \"size\": \"2.73 MB\", \"duration\": 110.09}", "aliases": [], "size": "2.73 MB"}, {"id": "shoota", "name": "✨ <PERSON> - <PERSON> [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Maaly Raw"], "notes": "OG Filename: <PERSON><PERSON>\nOriginal version of the Die Lit song \"Shoota\" with two Uzi verses. Was originally an Uzi song before being given to <PERSON><PERSON>. OGF leaked on Aug 20, 2023.", "length": "2:55", "fileDate": 15252192, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/78447b9eac84e8ad8a09e87fee67584f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/78447b9eac84e8ad8a09e87fee67584f/play\", \"key\": \"Shoot<PERSON>\", \"title\": \"\\u2728 <PERSON><PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Rocket\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON>aly <PERSON>a\\nOriginal version of the Die Lit song \\\"Shoota\\\" with two Uzi verses. Was originally an Uzi song before being given to <PERSON><PERSON>. OGF leaked on Aug 20, 2023.\", \"date\": 15252192, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b84943ff1d74300c7171275d9594a7b2\", \"url\": \"https://api.pillowcase.su/api/download/b84943ff1d74300c7171275d9594a7b2\", \"size\": \"3.27 MB\", \"duration\": 175.78}", "aliases": ["Rocket"], "size": "3.27 MB"}, {"id": "up", "name": "✨ Young Thug - Up [V1]", "artists": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Southside"], "notes": "Original version of \"Up\" featuring a <PERSON><PERSON><PERSON> verse. First previewed by <PERSON> Thug on October 25, 2017.", "length": "5:27", "fileDate": 15685920, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/320c6d7c4f638f3f81e1ecafd7d17214/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/320c6d7c4f638f3f81e1ecafd7d17214/play\", \"key\": \"Up\", \"title\": \"\\u2728 Young Thug - Up [V1]\", \"artists\": \"(feat. <PERSON> & <PERSON><PERSON><PERSON>) (prod. South<PERSON>)\", \"aliases\": [\"Let's Go Up\"], \"description\": \"Original version of \\\"Up\\\" featuring a <PERSON><PERSON><PERSON> verse. First previewed by <PERSON> on October 25, 2017.\", \"date\": 15685920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fe4ac7f0286996327b99bc4ec7f55457\", \"url\": \"https://api.pillowcase.su/api/download/fe4ac7f0286996327b99bc4ec7f55457\", \"size\": \"5.71 MB\", \"duration\": 327.79}", "aliases": ["Let's Go Up"], "size": "5.71 MB"}, {"id": "top", "name": "Top [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Top DC Main Mix V2 Bonus\nLeaked on May 29, 2018. Features a slightly different beat and chorus length.", "length": "2:01", "fileDate": 15275520, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/68c1a2155706e5109a623f84e00cb246/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/68c1a2155706e5109a623f84e00cb246/play\", \"key\": \"Top\", \"title\": \"Top [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Top DC Main Mix V2 Bonus\\nLeaked on May 29, 2018. Features a slightly different beat and chorus length.\", \"date\": 15275520, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d787c9c2e7087fee89441cbcb92701da\", \"url\": \"https://api.pillowcase.su/api/download/d787c9c2e7087fee89441cbcb92701da\", \"size\": \"2.92 MB\", \"duration\": 121.9}", "aliases": [], "size": "2.92 MB"}, {"id": "top-93", "name": "Top [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Top 11.26.17\nOriginal file for \"Top\". Leaked on October 12, 2022.", "length": "2:56", "fileDate": 16655328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/aee8b0eab62baa59c861ca9a9634bf65/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/aee8b0eab62baa59c861ca9a9634bf65/play\", \"key\": \"Top\", \"title\": \"Top [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Top 11.26.17\\nOriginal file for \\\"Top\\\". Leaked on October 12, 2022.\", \"date\": 16655328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cc2f49dfab15e1b4c9533f40a97dc929\", \"url\": \"https://api.pillowcase.su/api/download/cc2f49dfab15e1b4c9533f40a97dc929\", \"size\": \"3.78 MB\", \"duration\": 176.04}", "aliases": [], "size": "3.78 MB"}, {"id": "ysl", "name": "Gunna - YSL", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: YSL\nOG File for YSL", "length": "2:36", "fileDate": 16343424, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/1814eefe580a2c1c7ae7cd4271f61d5a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1814eefe580a2c1c7ae7cd4271f61d5a\", \"key\": \"YSL\", \"title\": \"<PERSON><PERSON> <PERSON> <PERSON><PERSON>\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: YSL\\nOG File for YSL\", \"date\": 16343424, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1bf36d74f3569ab94ea99170544d4c43\", \"url\": \"https://api.pillowcase.su/api/download/1bf36d74f3569ab94ea99170544d4c43\", \"size\": \"3.47 MB\", \"duration\": 156.14}", "aliases": [], "size": "3.47 MB"}, {"id": "top-5", "name": "Kap G - Top 5 [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["BosaGotBeats"], "notes": "OG Filename: PBC x Means KAP TOP5 5.9.18\nOG File for \"Top 5\".", "length": "2:37", "fileDate": 17137440, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/0515032b3f52c92ab986eec9c35b559b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0515032b3f52c92ab986eec9c35b559b/play\", \"key\": \"Top 5\", \"title\": \"Kap G - Top 5 [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>otBeat<PERSON>)\", \"description\": \"OG Filename: PBC x Means KAP TOP5 5.9.18\\nOG File for \\\"Top 5\\\".\", \"date\": 17137440, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"89c421027a8b0253cd25d43810639241\", \"url\": \"https://api.pillowcase.su/api/download/89c421027a8b0253cd25d43810639241\", \"size\": \"3.49 MB\", \"duration\": 157.92}", "aliases": [], "size": "3.49 MB"}, {"id": "hit-a-lick", "name": "<PERSON> - Hit A Lick [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: pLAYBOI cARTI -Hit A Lick v1\nOriginal file for \"Hit a Lick\".", "length": "2:21", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/880b090183f51d7892aee58fecb3335c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/880b090183f51d7892aee58fecb3335c/play\", \"key\": \"Hit A Lick\", \"title\": \"<PERSON> - Hit A Lick [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: pLAYBOI cARTI -Hit A Lick v1\\nOriginal file for \\\"Hit a Lick\\\".\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c4cfd0ac4d01a3315989fce0403fb523\", \"url\": \"https://api.pillowcase.su/api/download/c4cfd0ac4d01a3315989fce0403fb523\", \"size\": \"3.23 MB\", \"duration\": 141.65}", "aliases": [], "size": "3.23 MB"}, {"id": "summer-bummer", "name": "<PERSON> - <PERSON> Bummer", "artists": ["A$AP Rocky", "<PERSON><PERSON><PERSON>"], "producers": ["Boi-1da", "T-Minus", "<PERSON><PERSON><PERSON>", "BigWhiteBeatz", "<PERSON>"], "notes": "Accidently uploaded to <PERSON>'s Vevo YouTube Channel on July 12, 2017.", "length": "4:13", "fileDate": 14998176, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a55b2d3404d02dd8f47279d6e8acdc1c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a55b2d3404d02dd8f47279d6e8acdc1c/play\", \"key\": \"Summer Bummer\", \"title\": \"<PERSON> - Summer Bummer\", \"artists\": \"(feat. A$<PERSON> & <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>B<PERSON><PERSON> & <PERSON>)\", \"description\": \"Accidently uploaded to <PERSON>'s Vevo YouTube Channel on July 12, 2017.\", \"date\": 14998176, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"45ccaeb1e8e5bf0376780bbb3d9a3eed\", \"url\": \"https://api.pillowcase.su/api/download/45ccaeb1e8e5bf0376780bbb3d9a3eed\", \"size\": \"5.02 MB\", \"duration\": 253.31}", "aliases": [], "size": "5.02 MB"}, {"id": "yo-pi-erre", "name": "<PERSON><PERSON>er<PERSON> - <PERSON>erre [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON> x <PERSON>\nLeaked on October 7, 2022.", "length": "2:43", "fileDate": 16651008, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/2f4644353b874d68fd1bb5a979f2b45a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2f4644353b874d68fd1bb5a979f2b45a/play\", \"key\": \"Yo Pi'erre\", \"title\": \"<PERSON>'er<PERSON> - <PERSON>erre [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)\", \"description\": \"OG Filename: <PERSON>\\nLeaked on October 7, 2022.\", \"date\": 16651008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d672d196e0fddd988f0386d30ee0edde\", \"url\": \"https://api.pillowcase.su/api/download/d672d196e0fddd988f0386d30ee0edde\", \"size\": \"3.59 MB\", \"duration\": 163.75}", "aliases": [], "size": "3.59 MB"}, {"id": "yo-pi-erre-99", "name": "<PERSON><PERSON>er<PERSON> - <PERSON>erre [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON> (Outside) - main (alt eq) (mastered ts)\nOG file for Yo Pi<PERSON>erre", "length": "2:47", "fileDate": 16691616, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/438e0f338d19e6afc890649bb383c338/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/438e0f338d19e6afc890649bb383c338/play\", \"key\": \"Yo Pi'erre\", \"title\": \"<PERSON>'er<PERSON> - Yo <PERSON>'erre [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)\", \"description\": \"OG Filename: <PERSON> <PERSON> (Outside) - main (alt eq) (mastered ts)\\nOG file for Yo Pi'erre\", \"date\": 16691616, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"048581264369f8e30ae381a845c5e4b9\", \"url\": \"https://api.pillowcase.su/api/download/048581264369f8e30ae381a845c5e4b9\", \"size\": \"3.65 MB\", \"duration\": 167.86}", "aliases": [], "size": "3.65 MB"}, {"id": "raf", "name": "A$AP Mob - RAF [V2]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo", "<PERSON>", "A$AP Rocky"], "producers": ["Dun Deal", "<PERSON> \"C Sik\" <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: RAF - main (mastered ts)\nVery similar to released version but slightly different mix.", "length": "4:16", "fileDate": 15159744, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/5fae9a6536225406e01f6ae886800b4e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5fae9a6536225406e01f6ae886800b4e/play\", \"key\": \"RAF\", \"title\": \"A$AP Mob - RAF [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>$AP <PERSON>) (prod. <PERSON><PERSON>, <PERSON> \\\"<PERSON>\\\" <PERSON>, <PERSON>)\", \"description\": \"OG Filename: RAF - main (mastered ts)\\nVery similar to released version but slightly different mix.\", \"date\": 15159744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"528be7a39e425d7813c4894e114336f0\", \"url\": \"https://api.pillowcase.su/api/download/528be7a39e425d7813c4894e114336f0\", \"size\": \"5.07 MB\", \"duration\": 256.56}", "aliases": [], "size": "5.07 MB"}, {"id": "raf-101", "name": "A$AP Mob - RAF [V3]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo", "<PERSON>", "A$AP Rocky"], "producers": ["Dun Deal", "<PERSON> \"C Sik\" <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: RAF (New - 5 8 17) - main (mastered ts)\nPretty much the same as the above version with slight changes.", "length": "4:16", "fileDate": 16616448, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/b56061dc9c83b0e0649e899c227c4ce9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b56061dc9c83b0e0649e899c227c4ce9/play\", \"key\": \"RAF\", \"title\": \"A$AP Mob - RAF [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>$AP <PERSON>) (prod. <PERSON><PERSON>, <PERSON> \\\"<PERSON>\\\" <PERSON>, <PERSON>)\", \"description\": \"OG Filename: RAF (New - 5 8 17) - main (mastered ts)\\nPretty much the same as the above version with slight changes.\", \"date\": 16616448, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9efe9d396102b888897f89a4d8af1d8d\", \"url\": \"https://api.pillowcase.su/api/download/9efe9d396102b888897f89a4d8af1d8d\", \"size\": \"5.07 MB\", \"duration\": 256.56}", "aliases": [], "size": "5.07 MB"}, {"id": "raf-102", "name": "A$AP Mob - RAF [V4]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>", "Quavo", "<PERSON>", "A$AP Rocky"], "producers": ["Dun Deal", "<PERSON> \"C Sik\" <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: RAF (<PERSON>2 Only - 5 10 17) - main (mastered ts)\nVersion with different arrangment on <PERSON>'s verse. Overall shorter track-length.", "length": "3:35", "fileDate": 16616448, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/1be9fa18be0ef9a15ac9c5416e262585/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1be9fa18be0ef9a15ac9c5416e262585/play\", \"key\": \"RAF\", \"title\": \"A$AP Mob - RAF [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>$AP <PERSON>) (prod. <PERSON><PERSON>, <PERSON> \\\"<PERSON>\\\" <PERSON>, <PERSON>)\", \"description\": \"OG Filename: RAF (<PERSON> V2 Only - 5 10 17) - main (mastered ts)\\nVersion with different arrangment on <PERSON>'s verse. Overall shorter track-length.\", \"date\": 16616448, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8462ac6ed945eba30183169ce18b3539\", \"url\": \"https://api.pillowcase.su/api/download/8462ac6ed945eba30183169ce18b3539\", \"size\": \"4.41 MB\", \"duration\": 215.35}", "aliases": [], "size": "4.41 MB"}, {"id": "paid-in-full", "name": "SAFE - Paid In Full [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Pluss"], "notes": "OG Filename: Safe - Paid In Full ft. Playboi Carti\nOG Filename (Metadata): Paid In Full ft Playboi Carti12.12.17 ALT VERSE - For SC\nOG version of Paid In Full. Has different tags and speed.", "length": "3:25", "fileDate": 17137440, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/95d51d1b4e6d7a9861ad3711c2c2607c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/95d51d1b4e6d7a9861ad3711c2c2607c/play\", \"key\": \"Paid In Full\", \"title\": \"SAFE - Paid In Full [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Pluss)\", \"description\": \"OG Filename: Safe - Paid In Full ft. <PERSON><PERSON><PERSON>\\nOG Filename (Metadata): Paid In Full ft Playboi Carti12.12.17 ALT VERSE - For SC\\nOG version of Paid In Full. Has different tags and speed.\", \"date\": 17137440, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4b0d460365e473c7d8e52cb23b6a0aa2\", \"url\": \"https://api.pillowcase.su/api/download/4b0d460365e473c7d8e52cb23b6a0aa2\", \"size\": \"4.25 MB\", \"duration\": 205.15}", "aliases": [], "size": "4.25 MB"}, {"id": "uh-uh", "name": "Chief <PERSON><PERSON> <PERSON> <PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> x Sosa akai9 1.3.18 Paramount\nOG File for Uh Uh", "length": "2:46", "fileDate": 16572384, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/4f3345b06b83c9bc2fa14e886b5d27e3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4f3345b06b83c9bc2fa14e886b5d27e3/play\", \"key\": \"Uh Uh\", \"title\": \"<PERSON> <PERSON><PERSON> <PERSON> <PERSON>\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>ti x Sosa akai9 1.3.18 Paramount\\nOG File for Uh Uh\", \"date\": 16572384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"877808560d60f38acf948822f2cce483\", \"url\": \"https://api.pillowcase.su/api/download/877808560d60f38acf948822f2cce483\", \"size\": \"3.64 MB\", \"duration\": 166.9}", "aliases": [], "size": "3.64 MB"}, {"id": "no-time", "name": "No Time", "artists": ["<PERSON><PERSON>"], "producers": ["​Ineza Beats", "<PERSON>"], "notes": "OG Filename: No Time f Gunna DC Main Mix-V3\nOG version of No TIme", "length": "3:26", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/cc9fbec4135297cf1fbde6096dc36340/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cc9fbec4135297cf1fbde6096dc36340/play\", \"key\": \"No Time\", \"title\": \"No Time\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. \\u200bIneza Beats & Don Cannon)\", \"description\": \"OG Filename: No Time f Gunna DC Main Mix-V3\\nOG version of No TIme\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"65f373c97a95ca91e1c5c0fcff12597b\", \"url\": \"https://api.pillowcase.su/api/download/65f373c97a95ca91e1c5c0fcff12597b\", \"size\": \"4.27 MB\", \"duration\": 206.45}", "aliases": [], "size": "4.27 MB"}, {"id": "poke-it-out", "name": "Poke It Out", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Poke It Out-Main Mix V3 CD\nA version of \"Poke It Up\" with an extra line at the end of the song.", "length": "4:50", "fileDate": 16403904, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/c7e8889620fb540bcf41e7c733a96908/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c7e8889620fb540bcf41e7c733a96908/play\", \"key\": \"Poke It Out\", \"title\": \"Poke It Out\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Poke It Out-Main Mix V3 CD\\nA version of \\\"Poke It Up\\\" with an extra line at the end of the song.\", \"date\": 16403904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"688be0f37f46d032fe9036f173769aeb\", \"url\": \"https://api.pillowcase.su/api/download/688be0f37f46d032fe9036f173769aeb\", \"size\": \"5.62 MB\", \"duration\": 290.64}", "aliases": [], "size": "5.62 MB"}, {"id": "pull-up", "name": "Pull Up", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Pull Up 6.6.17\nOriginal filename for \"Pull Up\".", "length": "3:38", "fileDate": 16655328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/cd198c30427c563bb65e440620ccc7e5/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cd198c30427c563bb65e440620ccc7e5/play\", \"key\": \"Pull Up\", \"title\": \"Pull Up\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Pull Up 6.6.17\\nOriginal filename for \\\"Pull Up\\\".\", \"date\": 16655328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5a5bae09bd0fc13dea7be44bcb5497ef\", \"url\": \"https://api.pillowcase.su/api/download/5a5bae09bd0fc13dea7be44bcb5497ef\", \"size\": \"4.46 MB\", \"duration\": 218.35}", "aliases": [], "size": "4.46 MB"}, {"id": "r-i-p", "name": "<PERSON><PERSON><PERSON><PERSON> [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON>_x_Circle_House_11.30.17_-_2\nLeaked on October 7, 2022. Has different mixing and an open verse.", "length": "3:18", "fileDate": 16651008, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/fba0bcc96c3b26d8939b87fed3a7e495/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/fba0bcc96c3b26d8939b87fed3a7e495/play\", \"key\": \"R.I.P\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Carti_x_Circle_House_11.30.17_-_2\\nLeaked on October 7, 2022. Has different mixing and an open verse.\", \"date\": 16651008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0707b945517ff9796f2b11c9af16355c\", \"url\": \"https://api.pillowcase.su/api/download/0707b945517ff9796f2b11c9af16355c\", \"size\": \"4.15 MB\", \"duration\": 198.96}", "aliases": [], "size": "4.15 MB"}, {"id": "r-i-p-109", "name": "<PERSON><PERSON><PERSON><PERSON> [V4]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: R.I.P. 12.12.17\nSlightly early version of \"R.I.P.\" used for the music video. Features worse vocal mixing and an additional pause.", "length": "3:12", "fileDate": 16756416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/1092006665cc048fca8848005a853437/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1092006665cc048fca8848005a853437/play\", \"key\": \"R.I.P\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: R.I.P. 12.12.17\\nSlightly early version of \\\"R.I.P.\\\" used for the music video. Features worse vocal mixing and an additional pause.\", \"date\": 16756416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9a8eecfb6eaa6b0cf862707f9fb093ac\", \"url\": \"https://api.pillowcase.su/api/download/9a8eecfb6eaa6b0cf862707f9fb093ac\", \"size\": \"4.05 MB\", \"duration\": 192.79}", "aliases": [], "size": "4.05 MB"}, {"id": "r-i-p-fredo", "name": "<PERSON><PERSON><PERSON><PERSON> [V3]", "artists": ["<PERSON> Nudy"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Notice F Nudy DC Main Mix- V5\nLeaked on May 29, 2018 with slightly different production and chorus length.", "length": "2:45", "fileDate": 15275520, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/5234d46ef8ccd9dbee7318e30334ca55/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5234d46ef8ccd9dbee7318e30334ca55/play\", \"key\": \"<PERSON><PERSON><PERSON><PERSON>\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"R.I.P Peep\"], \"description\": \"OG Filename: Notice F Nudy DC Main Mix- V5\\nLeaked on May 29, 2018 with slightly different production and chorus length.\", \"date\": 15275520, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ac4cb696ad36ef44d36c79b051531c6\", \"url\": \"https://api.pillowcase.su/api/download/5ac4cb696ad36ef44d36c79b051531c6\", \"size\": \"3.61 MB\", \"duration\": 165.1}", "aliases": ["<PERSON><PERSON><PERSON><PERSON>"], "size": "3.61 MB"}, {"id": "time", "name": "Time", "artists": [], "producers": ["Art Dealer"], "notes": "OG Filename: Time 3.12.18\nHas a open verse. Leaked on October 12, 2022", "length": "3:40", "fileDate": 16655328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/3504a7f43762d45901318cdf806b3cb3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3504a7f43762d45901318cdf806b3cb3/play\", \"key\": \"Time\", \"title\": \"Time\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Long Time - Intro\"], \"description\": \"OG Filename: Time 3.12.18\\nHas a open verse. Leaked on October 12, 2022\", \"date\": 16655328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"381053a21b4fe8ceac367783d5412609\", \"url\": \"https://api.pillowcase.su/api/download/381053a21b4fe8ceac367783d5412609\", \"size\": \"4.5 MB\", \"duration\": 220.61}", "aliases": ["Long Time - Intro"], "size": "4.5 MB"}, {"id": "green-purple", "name": "<PERSON> - Green & Purple [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["TIGGI", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Green and Purple_Ruff 1_mp3\nVersion of Green & Purple with ruff mixing", "length": "5:18", "fileDate": 16714080, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/cc47d96e8fa24461865a2d03a86ab60a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cc47d96e8fa24461865a2d03a86ab60a/play\", \"key\": \"Green & Purple\", \"title\": \"<PERSON> & <PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Sevn <PERSON>)\", \"description\": \"OG Filename: Green and Purple_Ruff 1_mp3\\nVersion of Green & Purple with ruff mixing\", \"date\": 16714080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"106d692f33b4dd82f994a1c9ed536553\", \"url\": \"https://api.pillowcase.su/api/download/106d692f33b4dd82f994a1c9ed536553\", \"size\": \"6.07 MB\", \"duration\": 318.89}", "aliases": [], "size": "6.07 MB"}, {"id": "green-purple-113", "name": "<PERSON> - Green & Purple [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["TIGGI", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Green and Purple_Ruff 5\nVersion of Green & Purple with ruff mixing", "length": "4:38", "fileDate": 16714080, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/72322d2d417e2475ba606b02f525c6fb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/72322d2d417e2475ba606b02f525c6fb/play\", \"key\": \"Green & Purple\", \"title\": \"<PERSON> & <PERSON> [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Sevn <PERSON>)\", \"description\": \"OG Filename: Green and Purple_Ruff 5\\nVersion of Green & Purple with ruff mixing\", \"date\": 16714080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0ea5b4a957fa2ab2d18b70f0e0cc6fbb\", \"url\": \"https://api.pillowcase.su/api/download/0ea5b4a957fa2ab2d18b70f0e0cc6fbb\", \"size\": \"5.42 MB\", \"duration\": 278.5}", "aliases": [], "size": "5.42 MB"}, {"id": "green-purple-114", "name": "<PERSON> - Green & Purple [V6]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["TIGGI", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Green and Purple_Mix 6\nOG version of Green & Purple", "length": "4:38", "fileDate": 16714080, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/541fb08a56bfec53814f619e6dae862c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/541fb08a56bfec53814f619e6dae862c/play\", \"key\": \"Green & Purple\", \"title\": \"<PERSON> & <PERSON> [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Sevn <PERSON>)\", \"description\": \"OG Filename: Green and Purple_Mix 6\\nOG version of Green & Purple\", \"date\": 16714080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f53458b14a1471124e65b2dfb587aa44\", \"url\": \"https://api.pillowcase.su/api/download/f53458b14a1471124e65b2dfb587aa44\", \"size\": \"5.42 MB\", \"duration\": 278.5}", "aliases": [], "size": "5.42 MB"}, {"id": "green-purple-115", "name": "<PERSON> - Green & Purple [V9]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["TIGGI", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Green And Purple_FINAL MIX 3_MASTER_HIGH\nOG master of Green & Purple", "length": "4:38", "fileDate": "", "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/ca33c269fddc485d8d7a515091cf9b9c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ca33c269fddc485d8d7a515091cf9b9c\", \"key\": \"Green & Purple\", \"title\": \"<PERSON> & <PERSON> [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Sevn <PERSON>)\", \"description\": \"OG Filename: Green And Purple_FINAL MIX 3_MASTER_HIGH\\nOG master of Green & Purple\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d19156495fc6dbe15445575f09d0cad9\", \"url\": \"https://api.pillowcase.su/api/download/d19156495fc6dbe15445575f09d0cad9\", \"size\": \"5.42 MB\", \"duration\": 278.39}", "aliases": [], "size": "5.42 MB"}, {"id": "lean-4-real", "name": "Lean 4 Real [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["IndigoChildRick"], "notes": "OG Filename: LEAN4REAL 12.12.17\nOG file for \"Lean 4 Real\" mv version. Has a rougher mix, different clap sound & extra adlibs. This version was used in the leaked music video.", "length": "2:56", "fileDate": 16739136, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/0f7bbec50fe4d1588f8d9fcb66cde644/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0f7bbec50fe4d1588f8d9fcb66cde644/play\", \"key\": \"Lean 4 Real\", \"title\": \"Lean 4 Real [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. IndigoChildRick)\", \"description\": \"OG Filename: LEAN4REAL 12.12.17\\nOG file for \\\"Lean 4 Real\\\" mv version. Has a rougher mix, different clap sound & extra adlibs. This version was used in the leaked music video.\", \"date\": 16739136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea5f4310e9675b55f98621474097359c\", \"url\": \"https://api.pillowcase.su/api/download/ea5f4310e9675b55f98621474097359c\", \"size\": \"3.8 MB\", \"duration\": 176.93}", "aliases": [], "size": "3.8 MB"}, {"id": "rockstar-117", "name": "Rockstar [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON> <PERSON> <PERSON><PERSON>-Rockstar RUFF\nOG mix for Love Hurts", "length": "2:59", "fileDate": 15251328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/251d846e63cc2f9f5c33775b04606201", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/251d846e63cc2f9f5c33775b04606201\", \"key\": \"Rockstar\", \"title\": \"Rockstar [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Love Hurts\"], \"description\": \"OG Filename: <PERSON> <PERSON><PERSON>Rockstar RUFF\\nOG mix for Love Hurts\", \"date\": 15251328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"41822f02f79a98f154b3b63704b3d2ed\", \"url\": \"https://api.pillowcase.su/api/download/41822f02f79a98f154b3b63704b3d2ed\", \"size\": \"3.85 MB\", \"duration\": 179.98}", "aliases": ["Love Hurts"], "size": "3.85 MB"}, {"id": "rockstar-118", "name": "Rockstar [V3]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Carti_trav 1 2 04.30.18 x AJ\nOG File (Metadata): <PERSON><PERSON> x <PERSON> [Rockstar] x 04\nOG file for the \"Love Hurts\" soundcloud version of the song", "length": "3:00", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d24c5eea297ba7ac5f347f0cacd8e3cf/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d24c5eea297ba7ac5f347f0cacd8e3cf/play\", \"key\": \"Rockstar\", \"title\": \"Rockstar [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Love Hurts\"], \"description\": \"OG Filename: Carti_trav 1 2 04.30.18 x AJ\\nOG File (Metadata): <PERSON><PERSON> x <PERSON> [Rockstar] x 04\\nOG file for the \\\"Love Hurts\\\" soundcloud version of the song\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"164b24e5678758a4591b565636f705ba\", \"url\": \"https://api.pillowcase.su/api/download/164b24e5678758a4591b565636f705ba\", \"size\": \"3.85 MB\", \"duration\": 180.19}", "aliases": ["Love Hurts"], "size": "3.85 MB"}, {"id": "shawty", "name": "<PERSON><PERSON> [V1]", "artists": ["<PERSON><PERSON> Coldhearted"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON>d <PERSON> rough\nOpen verse for <PERSON><PERSON>. Leaked on October 12, 2022", "length": "3:26", "fileDate": 16655328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/8d9dcb8554d32b3009ccc312360098f3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8d9dcb8554d32b3009ccc312360098f3/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V1]\", \"artists\": \"(feat. <PERSON><PERSON>heart<PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Let It Go Pt. 2\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> rough\\nOpen verse for Carti. Leaked on October 12, 2022\", \"date\": 16655328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c559041dc36924efd843cc2317fcc3b1\", \"url\": \"https://api.pillowcase.su/api/download/c559041dc36924efd843cc2317fcc3b1\", \"size\": \"4.28 MB\", \"duration\": 206.83}", "aliases": ["Let It Go Pt. 2"], "size": "4.28 MB"}, {"id": "middle-of-the-summer", "name": "Middle Of The Summer [V3]", "artists": ["<PERSON><PERSON> Coldhearted"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Leaked on May 29, 2019 and has extra lines from Redd Coldhearted.", "length": "3:23", "fileDate": 15590880, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/67bec1c095eddf906a3a65724f2f3468/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/67bec1c095eddf906a3a65724f2f3468/play\", \"key\": \"Middle Of The Summer\", \"title\": \"Middle Of The Summer [V3]\", \"artists\": \"(feat. <PERSON><PERSON> Coldhearted) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Leaked on May 29, 2019 and has extra lines from Redd Coldhearted.\", \"date\": 15590880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ffe378761ec7fdff1c2263a0ad9e17d0\", \"url\": \"https://api.pillowcase.su/api/download/ffe378761ec7fdff1c2263a0ad9e17d0\", \"size\": \"4.23 MB\", \"duration\": 203.86}", "aliases": [], "size": "4.23 MB"}, {"id": "mileage", "name": "Mileage", "artists": ["Chief <PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> x Sosa - Mileage 1.3.18\nOG File for Mileage", "length": "2:31", "fileDate": 16633728, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/8179cd7b4426ef17e23b87198e18a247/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8179cd7b4426ef17e23b87198e18a247/play\", \"key\": \"Mileage\", \"title\": \"Mileage\", \"artists\": \"(feat. Chief <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> x Sosa - Mileage 1.3.18\\nOG File for Mileage\", \"date\": 16633728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ddffd80556e2b374ec2ae209b9f8db60\", \"url\": \"https://api.pillowcase.su/api/download/ddffd80556e2b374ec2ae209b9f8db60\", \"size\": \"3.39 MB\", \"duration\": 151.1}", "aliases": [], "size": "3.39 MB"}, {"id": "fell-in-luv", "name": "Fell In Luv [V6]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Fell In Love 12.12.17\nOG version for Fell in Luv. Same as released version but doesn't have the other guy on it.", "length": "3:32", "fileDate": 16733088, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/8c867a82d5ada4ad9df74e99dbf6f46f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8c867a82d5ada4ad9df74e99dbf6f46f\", \"key\": \"Fell In Luv\", \"title\": \"Fell In Luv [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Rockstar\", \"Real Rockstar\", \"<PERSON>\", \"Privacy\", \"Different Lifestyle\"], \"description\": \"OG Filename: Fell In Love 12.12.17\\nOG version for Fell in Luv. Same as released version but doesn't have the other guy on it.\", \"date\": 16733088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a5f194e71327c0a0ceb5c13019dd95a\", \"url\": \"https://api.pillowcase.su/api/download/4a5f194e71327c0a0ceb5c13019dd95a\", \"size\": \"4.37 MB\", \"duration\": 212.88}", "aliases": ["Rockstar", "Real Rockstar", "<PERSON>", "Privacy", "Different Lifestyle"], "size": "4.37 MB"}, {"id": "fell-in-luv-123", "name": "Fell In Luv [V7]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: 01 Fell In Luv 2.9.18\nLeaked on January 3, 2019. Referred to by fans as \"<PERSON>\". <PERSON><PERSON> later scrapped this version and went back to rework V6 for the album.", "length": "3:32", "fileDate": 15464736, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/c2bca74806f5a2fcf3f3e0474aff913c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c2bca74806f5a2fcf3f3e0474aff913c\", \"key\": \"Fell In Luv\", \"title\": \"Fell In Luv [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Rockstar\", \"Real Rockstar\", \"<PERSON>\", \"Privacy\", \"Different Lifestyle\"], \"description\": \"OG Filename: 01 Fell In Luv 2.9.18\\nLeaked on January 3, 2019. Referred to by fans as \\\"Tanya\\\". <PERSON><PERSON> later scrapped this version and went back to rework V6 for the album.\", \"date\": 15464736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"612bf81b98d841813987ed7b6f467eaa\", \"url\": \"https://api.pillowcase.su/api/download/612bf81b98d841813987ed7b6f467eaa\", \"size\": \"4.37 MB\", \"duration\": 212.88}", "aliases": ["Rockstar", "Real Rockstar", "<PERSON>", "Privacy", "Different Lifestyle"], "size": "4.37 MB"}, {"id": "flatbed-freestyle", "name": "FlatBed Freestyle", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: PBC ama1 4.26.18\nOriginal file for \"FlatBed Freestyle\".", "length": "3:14", "fileDate": 16634592, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/d5f815177765dece38bc9d7b134dac74", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d5f815177765dece38bc9d7b134dac74\", \"key\": \"FlatBed Freestyle\", \"title\": \"FlatBed Freestyle\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: PBC ama1 4.26.18\\nOriginal file for \\\"FlatBed Freestyle\\\".\", \"date\": 16634592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"da4925038c17104e3695678dda6daa89\", \"url\": \"https://api.pillowcase.su/api/download/da4925038c17104e3695678dda6daa89\", \"size\": \"4.09 MB\", \"duration\": 194.81}", "aliases": [], "size": "4.09 MB"}, {"id": "foreign", "name": "✨ Foreign [V2]", "artists": ["<PERSON><PERSON>erre <PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Foreign 1.19.18 excerpt\nLeaked May 10, 2020. Features <PERSON><PERSON>er<PERSON> and a small beatswitch.", "length": "3:00", "fileDate": 15890688, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/f051ea91288401717f6d197bbd8243ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f051ea91288401717f6d197bbd8243ce\", \"key\": \"Foreign\", \"title\": \"\\u2728 Foreign [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Foreign 1.19.18 excerpt\\nLeaked May 10, 2020. Features <PERSON>'erre <PERSON> and a small beatswitch.\", \"date\": 15890688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7dcece2dcc773a9ed4a979052c61d3f4\", \"url\": \"https://api.pillowcase.su/api/download/7dcece2dcc773a9ed4a979052c61d3f4\", \"size\": \"3.85 MB\", \"duration\": 180.11}", "aliases": [], "size": "3.85 MB"}, {"id": "home", "name": "Home [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Home DC Main MIx- V5\nOG mix of Home (KOD)", "length": "2:42", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/2875dcfd3de3fb6e358fb20fe1f37cd4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2875dcfd3de3fb6e358fb20fe1f37cd4/play\", \"key\": \"Home\", \"title\": \"Home [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Home (KOD)\"], \"description\": \"OG Filename: Home DC Main MIx- V5\\nOG mix of Home (KOD)\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd7fa8108aed22da87f4a98ba16d1234\", \"url\": \"https://api.pillowcase.su/api/download/bd7fa8108aed22da87f4a98ba16d1234\", \"size\": \"3.56 MB\", \"duration\": 162.19}", "aliases": ["Home (KOD)"], "size": "3.56 MB"}, {"id": "right-now", "name": "Right Now [V2]", "artists": ["<PERSON><PERSON>erre <PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Right Now 11.22.17\nOG File (Metadata): I'm On the Block 11.22.17\nLeaked on October 9, 2022.", "length": "3:07", "fileDate": 16652736, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/d94cd6f01d308a1f38549661e2aeefe1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d94cd6f01d308a1f38549661e2aeefe1/play\", \"key\": \"Right Now\", \"title\": \"Right Now [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"I'm On The Block\"], \"description\": \"OG Filename: Right Now 11.22.17\\nOG File (Metadata): I'm On the Block 11.22.17\\nLeaked on October 9, 2022.\", \"date\": 16652736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"615575f606ce8ff136a01fbd582400d7\", \"url\": \"https://api.pillowcase.su/api/download/615575f606ce8ff136a01fbd582400d7\", \"size\": \"3.97 MB\", \"duration\": 187.37}", "aliases": ["I'm On The Block"], "size": "3.97 MB"}, {"id": "right-now-128", "name": "Right Now [V3]", "artists": ["<PERSON><PERSON>erre <PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: 01 Right Now 12.6.17 AUTOTUNE\nOG File (Metadata): Right Now 12.6.17\nSimillar to the released version, but longer and different mix and master.", "length": "3:34", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e9015505d5edb0bcede2ae082bb49d0f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e9015505d5edb0bcede2ae082bb49d0f/play\", \"key\": \"Right Now\", \"title\": \"Right Now [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"I'm On The Block\"], \"description\": \"OG Filename: 01 Right Now 12.6.17 AUTOTUNE\\nOG File (Metadata): Right Now 12.6.17\\nSimillar to the released version, but longer and different mix and master.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4414279ccde2533916b0e3ef58ed2bb4\", \"url\": \"https://api.pillowcase.su/api/download/4414279ccde2533916b0e3ef58ed2bb4\", \"size\": \"4.4 MB\", \"duration\": 214.51}", "aliases": ["I'm On The Block"], "size": "4.4 MB"}, {"id": "stuck", "name": "<PERSON><PERSON><PERSON><PERSON> - Stuck [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Uses the \"Choppa Won't Miss\" beat. Made before the beat was given to <PERSON><PERSON>, later the vocals were used for V2.", "length": "", "fileDate": 16885152, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/6f81dc6ac2eaaf8df4875c52e18d8930", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6f81dc6ac2eaaf8df4875c52e18d8930\", \"key\": \"Stuck\", \"title\": \"<PERSON><PERSON>er<PERSON> - <PERSON>uck [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Choppa Won't Miss\", \"I'm Up\"], \"description\": \"Uses the \\\"Choppa Won't Miss\\\" beat. Made before the beat was given to <PERSON><PERSON>, later the vocals were used for V2.\", \"date\": 16885152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON><PERSON> Won't Miss", "I'm Up"], "size": ""}, {"id": "choppa-won-t-miss-130", "name": "<PERSON><PERSON> Won't Miss [V3]", "artists": ["<PERSON> Thug"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Wont Miss 5.5.17\nOG File for \"Choppa Won't Miss\".", "length": "3:37", "fileDate": 15255648, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/931060a76d5131dd7957eb07aa0c2f82/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/931060a76d5131dd7957eb07aa0c2f82/play\", \"key\": \"<PERSON><PERSON> Won't Miss\", \"title\": \"<PERSON><PERSON> Won't Miss [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Choppa Wont Miss 5.5.17\\nOG File for \\\"Choppa Won't Miss\\\".\", \"date\": 15255648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"472fe486da40508d056f011fbb3123a2\", \"url\": \"https://api.pillowcase.su/api/download/472fe486da40508d056f011fbb3123a2\", \"size\": \"4.46 MB\", \"duration\": 217.99}", "aliases": [], "size": "4.46 MB"}, {"id": "foreign-131", "name": "Foreign [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Playboi Carti x Foreign\nOG file for \"Foreign\".", "length": "2:24", "fileDate": 15255648, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/0a3fe55f6e4fe7b93833eb628bbf9a92", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0a3fe55f6e4fe7b93833eb628bbf9a92\", \"key\": \"Foreign\", \"title\": \"Foreign [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Playboi <PERSON>ti x Foreign\\nOG file for \\\"Foreign\\\".\", \"date\": 15255648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"56b263a8fd0f724f7ea7be5d56e84b9b\", \"url\": \"https://api.pillowcase.su/api/download/56b263a8fd0f724f7ea7be5d56e84b9b\", \"size\": \"3.28 MB\", \"duration\": 144.48}", "aliases": [], "size": "3.28 MB"}, {"id": "i-m-on-the-block", "name": "I'm On The Block [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: IM ON THE BLOCK 11.21.17\nLeaked on October 9, 2022. Has an open verse.", "length": "2:46", "fileDate": 16652736, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "die-lit", "originalUrl": "https://plwcse.top/f/1ee4a326d621f8f9557002ac90b86521", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://plwcse.top/f/1ee4a326d621f8f9557002ac90b86521\", \"key\": \"I'm On The Block\", \"title\": \"I'm On The Block [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Right Now\"], \"description\": \"OG Filename: IM ON THE BLOCK 11.21.17\\nLeaked on October 9, 2022. Has an open verse.\", \"date\": 16652736, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"471234c35126f139aa0e4f52032ea794\", \"url\": \"https://api.pillowcase.su/api/download/471234c35126f139aa0e4f52032ea794\", \"size\": \"3.64 MB\", \"duration\": 166.54}", "aliases": ["Right Now"], "size": "3.64 MB"}, {"id": "arm-and-leg-133", "name": "Arm and Leg [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Previewed by <PERSON><PERSON><PERSON><PERSON> on Instagram. Features different verses and hook structure. Unrelated to the Whole Lotta Red song of the same name.", "length": "0:07", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/563114846c74a609c4dfeac34c9537ed/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/563114846c74a609c4dfeac34c9537ed/play\", \"key\": \"Arm and Leg\", \"title\": \"Arm and Leg [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Previewed by <PERSON><PERSON><PERSON><PERSON> on Instagram. Features different verses and hook structure. Unrelated to the Whole Lotta Red song of the same name.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"9e3fa94f549c9a15fe168844096bb131\", \"url\": \"https://api.pillowcase.su/api/download/9e3fa94f549c9a15fe168844096bb131\", \"size\": \"1.09 MB\", \"duration\": 7.37}", "aliases": [], "size": "1.09 MB"}, {"id": "blowin-minds", "name": "Blowin' Minds (Freestyle) [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A throwaway freestyle. Original snip leaked Dec 8, 2023\nNew snippet leaked Feb 25, 2025 after song was put up for groupbuy", "length": "0:10", "fileDate": 17404416, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/42c37ec05e93ef50ee9e18dcd5dd0d7d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/42c37ec05e93ef50ee9e18dcd5dd0d7d/play\", \"key\": \"Blowin' Minds (Freestyle)\", \"title\": \"Blowin' Minds (Freestyle) [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"A throwaway freestyle. Original snip leaked Dec 8, 2023\\nNew snippet leaked Feb 25, 2025 after song was put up for groupbuy\", \"date\": 17404416, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dda9db54704b30b9eddfcebdf826b8f5\", \"url\": \"https://api.pillowcase.su/api/download/dda9db54704b30b9eddfcebdf826b8f5\", \"size\": \"1.13 MB\", \"duration\": 10.39}", "aliases": [], "size": "1.13 MB"}, {"id": "blowin-minds-135", "name": "Blowin' Minds (Freestyle) [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A throwaway freestyle. Original snip leaked Dec 8, 2023\nNew snippet leaked Feb 25, 2025 after song was put up for groupbuy", "length": "0:09", "fileDate": 17404416, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/c955d0c2593cd11dd31cc46a1d88aa7b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c955d0c2593cd11dd31cc46a1d88aa7b/play\", \"key\": \"Blowin' Minds (Freestyle)\", \"title\": \"Blowin' Minds (Freestyle) [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"A throwaway freestyle. Original snip leaked Dec 8, 2023\\nNew snippet leaked Feb 25, 2025 after song was put up for groupbuy\", \"date\": 17404416, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"47fd4df56e8d5d241578fa6d9bda3cfd\", \"url\": \"https://api.pillowcase.su/api/download/47fd4df56e8d5d241578fa6d9bda3cfd\", \"size\": \"1.11 MB\", \"duration\": 9.01}", "aliases": [], "size": "1.11 MB"}, {"id": "blowin-minds-136", "name": "Blowin' Minds [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Solo version of Blowin Minds, that is mixed and is listenable. Has different mix from the released A$AP MOB version. Added to the Blowin Minds gb on Mar 8, 2025. Has 30s of additional unheard vocals from the release version.", "length": "0:05", "fileDate": 17413920, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/bfb3b97c04f70c22aaa4358ce766c8b0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/bfb3b97c04f70c22aaa4358ce766c8b0/play\", \"key\": \"Blowin' Minds\", \"title\": \"Blowin' Minds [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Solo version of Blowin Minds, that is mixed and is listenable. Has different mix from the released A$AP MOB version. Added to the Blowin Minds gb on Mar 8, 2025. Has 30s of additional unheard vocals from the release version.\", \"date\": 17413920, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"068debd88993440d26759f65dd5d58b9\", \"url\": \"https://api.pillowcase.su/api/download/068debd88993440d26759f65dd5d58b9\", \"size\": \"1.06 MB\", \"duration\": 5.96}", "aliases": [], "size": "1.06 MB"}, {"id": "check-please-137", "name": "Check Please [V4]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "Previewed in @pierrebourne's on Instagram Live.", "length": "0:25", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/eb61507c075ab8f999e5578b26370be7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/eb61507c075ab8f999e5578b26370be7/play\", \"key\": \"Check Please\", \"title\": \"Check Please [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"They Go Off\", \"Leash\", \"Hugh Hefner Tribute\", \"Kick Door\"], \"description\": \"Previewed in @pierrebourne's on Instagram Live.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7d4e1b4910c5a258703e240e3740c65c\", \"url\": \"https://api.pillowcase.su/api/download/7d4e1b4910c5a258703e240e3740c65c\", \"size\": \"1.37 MB\", \"duration\": 25.39}", "aliases": ["They Go Off", "<PERSON><PERSON>", "<PERSON>", "Kick Door"], "size": "1.37 MB"}, {"id": "check-please-138", "name": "Check Please [V?]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "Previewed in the summer of 2017. Unknown if this version has leaked", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/video/0a44aa941a5ccdc4ec5e6a273d914d44/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/video/0a44aa941a5ccdc4ec5e6a273d914d44/play\", \"key\": \"Check Please\", \"title\": \"Check Please [V?]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON>)\", \"aliases\": [\"They Go Off\", \"Leash\", \"Hugh <PERSON>fner Tribute\", \"Kick Door\"], \"description\": \"Previewed in the summer of 2017. Unknown if this version has leaked\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["They Go Off", "<PERSON><PERSON>", "<PERSON>", "Kick Door"], "size": ""}, {"id": "get-in", "name": "🥇 Get In*", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Snippet leaked by <PERSON><PERSON><PERSON><PERSON> (<PERSON>n) in Carti Hub. Further information was shared by him, on Sep 4, 2024. Said to be unfinished and have just a single verse and a hook.", "length": "0:45", "fileDate": 17175456, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/f12eb3962acf94dba57302ebe261d352", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f12eb3962acf94dba57302ebe261d352\", \"key\": \"Get In*\", \"title\": \"\\ud83e\\udd47 Get In*\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Track 3\"], \"description\": \"Snippet leaked by two<PERSON><PERSON><PERSON> (Neon) in Carti Hub. Further information was shared by him, on Sep 4, 2024. Said to be unfinished and have just a single verse and a hook.\", \"date\": 17175456, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3ccf27d9cafec98494ea8703aa68a7f0\", \"url\": \"https://api.pillowcase.su/api/download/3ccf27d9cafec98494ea8703aa68a7f0\", \"size\": \"1.69 MB\", \"duration\": 45.06}", "aliases": ["Track 3"], "size": "1.69 MB"}, {"id": "don-t-care", "name": "Don't Care*", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A throwaway from the 'Die Lit' sessions.", "length": "0:06", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/0128a1e30f3aa7f2b6e6b7f9b02d1534/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0128a1e30f3aa7f2b6e6b7f9b02d1534/play\", \"key\": \"Don't Care*\", \"title\": \"Don't Care*\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"A throwaway from the 'Die Lit' sessions.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f4310197d447464e87f84901cbadbfca\", \"url\": \"https://api.pillowcase.su/api/download/f4310197d447464e87f84901cbadbfca\", \"size\": \"1.08 MB\", \"duration\": 6.92}", "aliases": [], "size": "1.08 MB"}, {"id": "drip-141", "name": "Drip [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "EVIL"], "notes": "OG version of Drip, open for pb/gb, unknown whether its v1 or v2", "length": "0:15", "fileDate": 17043264, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/1d5cf71a7a085f3e44646a9852ec345e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1d5cf71a7a085f3e44646a9852ec345e/play\", \"key\": \"Drip\", \"title\": \"Drip [V1]\", \"artists\": \"(prod. IkeBeatz & EVIL)\", \"aliases\": [\"With The Reds\"], \"description\": \"OG version of Drip, open for pb/gb, unknown whether its v1 or v2\", \"date\": 17043264, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0d9083047c3097e2a73ae7100455e60a\", \"url\": \"https://api.pillowcase.su/api/download/0d9083047c3097e2a73ae7100455e60a\", \"size\": \"1.22 MB\", \"duration\": 15.84}", "aliases": ["With The Reds"], "size": "1.22 MB"}, {"id": "drip-142", "name": "Drip [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "EVIL"], "notes": "OG version of Drip, open for pb/gb, unknown whether its v1 or v2", "length": "0:15", "fileDate": 17043264, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/dff15e9fa055b80715847bea064dacd0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/dff15e9fa055b80715847bea064dacd0/play\", \"key\": \"Drip\", \"title\": \"Drip [V2]\", \"artists\": \"(prod. IkeBeatz & EVIL)\", \"aliases\": [\"With The Reds\"], \"description\": \"OG version of Drip, open for pb/gb, unknown whether its v1 or v2\", \"date\": 17043264, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea973e0f08f578d3cb7109d476c59370\", \"url\": \"https://api.pillowcase.su/api/download/ea973e0f08f578d3cb7109d476c59370\", \"size\": \"1.22 MB\", \"duration\": 15.6}", "aliases": ["With The Reds"], "size": "1.22 MB"}, {"id": "drip-143", "name": "Drip [V3]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "EVIL"], "notes": "Might be a part of either of the other previewed OG versions of Drip, but also might be a seperate version.", "length": "0:12", "fileDate": 17043264, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/486a9fa5c0be73e9b22e17f7eeb0ecd7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/486a9fa5c0be73e9b22e17f7eeb0ecd7/play\", \"key\": \"Drip\", \"title\": \"Drip [V3]\", \"artists\": \"(prod. IkeBeatz & EVIL)\", \"aliases\": [\"With The Reds\"], \"description\": \"Might be a part of either of the other previewed OG versions of Drip, but also might be a seperate version.\", \"date\": 17043264, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"df29b82a2cbfe4a60e66dad1a8cd7929\", \"url\": \"https://api.pillowcase.su/api/download/df29b82a2cbfe4a60e66dad1a8cd7929\", \"size\": \"1.17 MB\", \"duration\": 12.91}", "aliases": ["With The Reds"], "size": "1.17 MB"}, {"id": "drip-144", "name": "Drip [V4]", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "EVIL"], "notes": "Might be a part of either of the other previewed OG versions of Drip, but also might be a seperate version.", "length": "0:12", "fileDate": 17075232, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/49dccf1ccd13fe9b06b38efdce00df95/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/49dccf1ccd13fe9b06b38efdce00df95/play\", \"key\": \"Drip\", \"title\": \"Drip [V4]\", \"artists\": \"(prod. IkeBeatz & EVIL)\", \"aliases\": [\"With The Reds\"], \"description\": \"Might be a part of either of the other previewed OG versions of Drip, but also might be a seperate version.\", \"date\": 17075232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ad2c56ff4f649bf154867acc0edfc113\", \"url\": \"https://api.pillowcase.su/api/download/ad2c56ff4f649bf154867acc0edfc113\", \"size\": \"1.17 MB\", \"duration\": 12.91}", "aliases": ["With The Reds"], "size": "1.17 MB"}, {"id": "poke-it-out-145", "name": "Poke It Out [V?]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Unknown version of Poke It Out with a horrible ass laugh and verse from <PERSON><PERSON>. (Keep this trash in the vault) previewed probably by Caskets.", "length": "0:11", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/70433755c4ce4383154824aaa90b97fc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/70433755c4ce4383154824aaa90b97fc/play\", \"key\": \"Poke It Out\", \"title\": \"Poke It Out [V?]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Unknown version of Poke It Out with a horrible ass laugh and verse from <PERSON><PERSON>. (Keep this trash in the vault) previewed probably by Caskets.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"b6f482af2e84b0af36eb97c70fb1f2ef\", \"url\": \"https://api.pillowcase.su/api/download/b6f482af2e84b0af36eb97c70fb1f2ef\", \"size\": \"1.06 MB\", \"duration\": 11.94}", "aliases": [], "size": "1.06 MB"}, {"id": "lean-4-real-146", "name": "Lean 4 Real [V1]", "artists": [], "producers": ["IndigoChildRick"], "notes": "A snippet of an early version of \"Lean 4 Real\" surfaced of IndigoChildRick doing a reference over the song, some of which was used by <PERSON><PERSON> in the final.", "length": "0:33", "fileDate": 16844544, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/ddf2ea8fda214fc8253fb4b3008cb4c8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ddf2ea8fda214fc8253fb4b3008cb4c8/play\", \"key\": \"Lean 4 Real\", \"title\": \"Lean 4 Real [V1]\", \"artists\": \"(ref. IndigoChildRick) (prod. IndigoChildRick)\", \"description\": \"A snippet of an early version of \\\"Lean 4 Real\\\" surfaced of IndigoChildRick doing a reference over the song, some of which was used by <PERSON><PERSON> in the final.\", \"date\": 16844544, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"a74ad43d1119dbacf7213576a038a661\", \"url\": \"https://api.pillowcase.su/api/download/a74ad43d1119dbacf7213576a038a661\", \"size\": \"1.23 MB\", \"duration\": 33.19}", "aliases": [], "size": "1.23 MB"}, {"id": "lean-4-real-147", "name": "Lean 4 Real [V1]", "artists": [], "producers": ["IndigoChildRick"], "notes": "A snippet of an early version of \"Lean 4 Real\" surfaced of IndigoChildRick doing a reference over the song, some of which was used by <PERSON><PERSON> in the final.", "length": "0:06", "fileDate": 16844544, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/2b77d9da4da40f479537e47ef3b8a96e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2b77d9da4da40f479537e47ef3b8a96e/play\", \"key\": \"Lean 4 Real\", \"title\": \"Lean 4 Real [V1]\", \"artists\": \"(ref. IndigoChildRick) (prod. IndigoChildRick)\", \"description\": \"A snippet of an early version of \\\"Lean 4 Real\\\" surfaced of IndigoChildRick doing a reference over the song, some of which was used by <PERSON><PERSON> in the final.\", \"date\": 16844544, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"4fea9db8aff8a369b7aceb2904019b5a\", \"url\": \"https://api.pillowcase.su/api/download/4fea9db8aff8a369b7aceb2904019b5a\", \"size\": \"1.07 MB\", \"duration\": 6.66}", "aliases": [], "size": "1.07 MB"}, {"id": "plugs", "name": "Plugs", "artists": [], "producers": [], "notes": "Snippet of the studio session of a throwaway posted on @govwok's Instagram.", "length": "0:14", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/a6dd63506cbead3e86d3fa2400ab35d2/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a6dd63506cbead3e86d3fa2400ab35d2/play\", \"key\": \"Plugs\", \"title\": \"Plugs\", \"aliases\": [\"We Gon' Call\"], \"description\": \"Snippet of the studio session of a throwaway posted on @govwok's Instagram.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"819ae81dc915c18de3a744edeb89438f\", \"url\": \"https://api.pillowcase.su/api/download/819ae81dc915c18de3a744edeb89438f\", \"size\": \"1.21 MB\", \"duration\": 14.81}", "aliases": ["We Gon' Call"], "size": "1.21 MB"}, {"id": "pull-up-149", "name": "Pull Up [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Snippet of \"Pull Up\" from May 2017. Sounds the same as the released version, but could have some unpreviewed differences.", "length": "1:02", "fileDate": 14946336, "leakDate": "", "labels": ["Low Quality", "Partial"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/313cbec1c51ea5c2f2dafa8838b8f25e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/313cbec1c51ea5c2f2dafa8838b8f25e/play\", \"key\": \"Pull Up\", \"title\": \"Pull Up [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Snippet of \\\"Pull Up\\\" from May 2017. Sounds the same as the released version, but could have some unpreviewed differences.\", \"date\": 14946336, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"cc6380e566ab8ed980848e4e0eb1b11a\", \"url\": \"https://api.pillowcase.su/api/download/cc6380e566ab8ed980848e4e0eb1b11a\", \"size\": \"1.97 MB\", \"duration\": 62.62}", "aliases": [], "size": "1.97 MB"}, {"id": "r-i-p-150", "name": "🥇 <PERSON><PERSON><PERSON><PERSON> [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Later version of \"R.I.P\" closer to the final but still containing alternate production and vocal structure.", "length": "0:25", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/506c804936bb084a6bb3dc74b9e2010f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/506c804936bb084a6bb3dc74b9e2010f/play\", \"key\": \"R.I.P\", \"title\": \"\\ud83e\\udd47 R.<PERSON><PERSON> [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Later version of \\\"R.I.P\\\" closer to the final but still containing alternate production and vocal structure.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"8206ab49183389e89cf14f84d00f1cde\", \"url\": \"https://api.pillowcase.su/api/download/8206ab49183389e89cf14f84d00f1cde\", \"size\": \"1.37 MB\", \"duration\": 25.34}", "aliases": [], "size": "1.37 MB"}, {"id": "toke-151", "name": "Toke [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A snippet of <PERSON><PERSON> talking while an earlier version of the Toke beat plays. Was uploaded on September 2nd, 2017. <PERSON><PERSON> could've possibly recorded on the beat around this time, but this isn't confirmed.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/video/06948cef815359b5a2910c824bc71a30/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/video/06948cef815359b5a2910c824bc71a30/play\", \"key\": \"Toke\", \"title\": \"Toke [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"A snippet of <PERSON><PERSON> talking while an earlier version of the Toke beat plays. Was uploaded on September 2nd, 2017. <PERSON><PERSON> could've possibly recorded on the beat around this time, but this isn't confirmed.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "toke-shit-152", "name": "Toke <PERSON> [V6]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Version of Toke Shit with a <PERSON> Bibby verse.", "length": "0:20", "fileDate": 16844544, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/825b0f9a8cd3810464db64730890b7a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/825b0f9a8cd3810464db64730890b7a9\", \"key\": \"Toke Shit\", \"title\": \"Toke Shit [V6]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Bust Down\", \"Toke\"], \"description\": \"Version of Toke Shit with a Lil Bibby verse.\", \"date\": 16844544, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"10963dbf5b884ed921bf35a136fada2a\", \"url\": \"https://api.pillowcase.su/api/download/10963dbf5b884ed921bf35a136fada2a\", \"size\": \"597 kB\", \"duration\": 20.14}", "aliases": ["Bust Down", "<PERSON><PERSON>"], "size": "597 kB"}, {"id": "many-crates", "name": "Many Crates*", "artists": [], "producers": ["Southside"], "notes": "A throwaway from the 'Die Lit' Sessions. Recorded on the same day as Trap.", "length": "0:05", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/10d98934b9278dbc6c95153f18895c72/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/10d98934b9278dbc6c95153f18895c72/play\", \"key\": \"Many Crates*\", \"title\": \"Many Crates*\", \"artists\": \"(prod. South<PERSON>)\", \"aliases\": [\"She On The Lean\"], \"description\": \"A throwaway from the 'Die Lit' Sessions. Recorded on the same day as Trap.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"b4425baf848dbc4a2bd73740a43a6f5a\", \"url\": \"https://api.pillowcase.su/api/download/b4425baf848dbc4a2bd73740a43a6f5a\", \"size\": \"1.06 MB\", \"duration\": 5.93}", "aliases": ["She On The Lean"], "size": "1.06 MB"}, {"id": "lover-s-freestyle", "name": "Lover's Freestyle*", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A throwaway from the 'Die Lit' Sessions.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Partial"], "links": [], "eraId": "die-lit", "originalUrl": "https://www.youtube.com/watch?v=tK-tAh3-T6w&ab_channel=Vlo23-", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=tK-tAh3-T6w&ab_channel=Vlo23-\", \"key\": \"Lover's Freestyle*\", \"title\": \"Lover's Freestyle*\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"A throwaway from the 'Die Lit' Sessions.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "super-soaker", "name": "🏆 Super Soaker", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "A throwaway from the Die Lit sessions. Engineered by <PERSON><PERSON><PERSON> and was recorded at Mean Street Studios on Nov 19, 2017 in a session with <PERSON> and <PERSON> alongside songs like Dog Food, Watch, Goyard Shopping Bag, and more. This song potentionally could have been meant for the scrapped VLone Mixtape.", "length": "0:18", "fileDate": 15202944, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/9aa02ebbbdbf61618db76faa39cc5c6a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/9aa02ebbbdbf61618db76faa39cc5c6a/play\", \"key\": \"Super Soaker\", \"title\": \"\\ud83c\\udfc6 Super Soaker\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & Oogie <PERSON>)\", \"aliases\": [\"Dior\"], \"description\": \"A throwaway from the Die Lit sessions. Engineered by <PERSON><PERSON><PERSON> and was recorded at Mean Street Studios on Nov 19, 2017 in a session with <PERSON> and <PERSON> alongside songs like Dog Food, Watch, Goyard Shopping Bag, and more. This song potentionally could have been meant for the scrapped VLone Mixtape.\", \"date\": 15202944, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"cb196334712bd12c4f57f1a6addfe81c\", \"url\": \"https://api.pillowcase.su/api/download/cb196334712bd12c4f57f1a6addfe81c\", \"size\": \"762 kB\", \"duration\": 18.77}", "aliases": ["<PERSON><PERSON>"], "size": "762 kB"}, {"id": "world-is-mine", "name": "🏆 World Is Mine", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "<PERSON><PERSON><PERSON><PERSON> produced throwaway. From the same session as <PERSON><PERSON> and <PERSON><PERSON> (Jan 16, 2018).", "length": "2:39", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Partial"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/1745d38cc626d3711ec4c6c0b979fa75/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1745d38cc626d3711ec4c6c0b979fa75/play\", \"key\": \"World Is Mine\", \"title\": \"\\ud83c\\udfc6 World Is Mine\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Back On That\", \"Mine\", \"U Mine\"], \"description\": \"<PERSON><PERSON><PERSON><PERSON> produced throwaway. From the same session as <PERSON><PERSON> and <PERSON> (Jan 16, 2018).\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"bcf071ef793355324cbae79998b1b305\", \"url\": \"https://api.pillowcase.su/api/download/bcf071ef793355324cbae79998b1b305\", \"size\": \"3.53 MB\", \"duration\": 159.9}", "aliases": ["Back On That", "Mine", "U Mine"], "size": "3.53 MB"}, {"id": "flex-157", "name": "RX Peso - Flex [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Alternate version of \"Flex\" with a different RX Peso verse.", "length": "0:08", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/4ac2fc51b322184b8d53580838271f23", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4ac2fc51b322184b8d53580838271f23\", \"key\": \"Flex\", \"title\": \"RX Peso - Flex [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Good Credit\"], \"description\": \"Alternate version of \\\"Flex\\\" with a different RX Peso verse.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"59281d6073148a9754e7b428d8597647\", \"url\": \"https://api.pillowcase.su/api/download/59281d6073148a9754e7b428d8597647\", \"size\": \"1.1 MB\", \"duration\": 8.18}", "aliases": ["Good Credit"], "size": "1.1 MB"}, {"id": "i-m-on-them-beans", "name": "I'm On Them Beans*", "artists": [], "producers": [], "notes": "Relatively unknown Die Lit era throwaway.", "length": "0:20", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://pillowcase.su/f/258da6b863ab0721ed527375c428a2e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/258da6b863ab0721ed527375c428a2e1\", \"key\": \"I'm On Them Beans*\", \"title\": \"I'm On Them Beans*\", \"description\": \"Relatively unknown Die Lit era throwaway.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"b31d07197905cf1e82c6342535c532c3\", \"url\": \"https://api.pillowcase.su/api/download/b31d07197905cf1e82c6342535c532c3\", \"size\": \"1.13 MB\", \"duration\": 20.53}", "aliases": [], "size": "1.13 MB"}, {"id": "fell-in-luv-159", "name": "Fell In Luv [V5]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: fell in luv new beat bnc1\nPreviewed on <PERSON><PERSON><PERSON>'s IG Live", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Partial"], "links": [], "eraId": "die-lit", "originalUrl": "http://music.froste.lol/video/c4dbacd1ccc6200500b635bf448ed5f8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/video/c4dbacd1ccc6200500b635bf448ed5f8/play\", \"key\": \"Fell In Luv\", \"title\": \"Fell In Luv [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Rockstar\", \"Real Rockstar\", \"<PERSON>\", \"Privacy\", \"Different Lifestyle\"], \"description\": \"OG Filename: fell in luv new beat bnc1\\nPreviewed on Trell<PERSON>'s IG Live\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Rockstar", "Real Rockstar", "<PERSON>", "Privacy", "Different Lifestyle"], "size": ""}, {"id": "middle-of-the-summer-160", "name": "Middle Of The Summer [V2]", "artists": ["<PERSON><PERSON> Coldhearted"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Previewed by <PERSON><PERSON> <PERSON>'s verse ended up being replaced by <PERSON><PERSON>.", "length": "0:09", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/5dd93ce0b9d6abf90af3a5c99f1de4b5/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5dd93ce0b9d6abf90af3a5c99f1de4b5/play\", \"key\": \"Middle Of The Summer\", \"title\": \"Middle Of The Summer [V2]\", \"artists\": \"(feat. <PERSON><PERSON>hearted) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Previewed by <PERSON><PERSON>'s verse ended up being replaced by <PERSON><PERSON> Coldhearted.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"431b684eea014d0667d3c5082a1c2da0\", \"url\": \"https://api.pillowcase.su/api/download/431b684eea014d0667d3c5082a1c2da0\", \"size\": \"1.11 MB\", \"duration\": 9.17}", "aliases": [], "size": "1.11 MB"}, {"id": "jump", "name": "🥇 Jump", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Previewed in @pierrebourne's Instagram Story.", "length": "0:06", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/cf0f2452824c6c4593525380358538b7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cf0f2452824c6c4593525380358538b7/play\", \"key\": \"Jump\", \"title\": \"\\ud83e\\udd47 Jump\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Gorgeous\", \"Up\"], \"description\": \"Previewed in @pierrebourne's Instagram Story.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"594fa1fd6ed014482b4956f548fa4972\", \"url\": \"https://api.pillowcase.su/api/download/594fa1fd6ed014482b4956f548fa4972\", \"size\": \"1.07 MB\", \"duration\": 6.45}", "aliases": ["Gorgeous", "Up"], "size": "1.07 MB"}, {"id": "paid-in-full-162", "name": "SAFE - Paid In Full [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Pluss"], "notes": "Version of Paid In Full with an alt carti verse, snippet surfaced Apr 2 2025.", "length": "", "fileDate": 17435520, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "die-lit", "originalUrl": "https://music.froste.lol/song/e7d6557c6126c3eb98fcaf0fb8141f10", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e7d6557c6126c3eb98fcaf0fb8141f10\", \"key\": \"Paid In Full\", \"title\": \"SAFE - Paid In Full [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Pluss)\", \"description\": \"Version of Paid In Full with an alt carti verse, snippet surfaced Apr 2 2025.\", \"date\": 17435520, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}]}