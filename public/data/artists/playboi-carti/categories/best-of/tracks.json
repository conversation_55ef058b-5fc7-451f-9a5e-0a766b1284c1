[{"era": "The High Chronical$", "tracks": [{"id": "generated_id_The_High_Chronical$_0_0", "title": "Living Reckless [V2]", "artists": "(prod. <PERSON>)", "length": null, "ogFilename": null, "notes": "Version very similar to release. Found on Soulseek.", "tags": [], "aliases": [], "originalContent": {"artists": "(prod. <PERSON>)"}}]}, {"era": "Young Mi$fit", "tracks": [{"id": "c815d61d910c19b1828157ec98bdbc2a", "title": "$teeze [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:50", "ogFilename": "5. <PERSON><PERSON><PERSON> [Pro. By TDEEZY]", "notes": "OG Filename (Metadata): <PERSON>eeze [Pro. By TDEEZY] OG File for track 5 from the Young Mi$fit mixtape, \"$teeze\".", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/0b3fb67c22b15299f13f7ebc61861df6/play", "key": "$teeze", "description": "OG Filename: 5. <PERSON><PERSON><PERSON> [Pro. By TDEEZY]\nOG Filename (Metadata): <PERSON><PERSON><PERSON> [Pro. By TDEEZY]\nOG File for track 5 from the Young Mi$fit mixtape, \"$teeze\".", "date": 16194816, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/c815d61d910c19b1828157ec98bdbc2a", "size": "2.91 MB", "duration": 170.68, "originalContent": {"url": "https://music.froste.lol/song/0b3fb67c22b15299f13f7ebc61861df6/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}]}, {"era": "Sen$ation", "tracks": [{"id": "generated_id_Sen$ation_2_0", "title": "10kk a Couple Thousand$ For The Hoe$", "artists": "(prod. $ir <PERSON>)", "length": null, "ogFilename": "10 a Verse.L (1)", "notes": "OG Filename (Metadata): 10 a Vers Was originally uploaded to SoundCloud in 2013 but later taken down. OG File leaked on July 6, 2024. A song carti dropped and reposted 5 times. The Song Was Originally Titled 10 a Verse, <PERSON><PERSON> forgot to change the SoundCloud url after changing the name, so that's why it still shows up as 10 a Verse.", "tags": ["OG File"], "aliases": ["(10 a Verse)"], "originalContent": {"artists": "(prod. $ir <PERSON>)"}}, {"id": "generated_id_Sen$ation_2_1", "title": "Faster", "artists": "(prod. Na<PERSON> Gawd)", "length": null, "ogFilename": null, "notes": "Samples \"Find Your Way\" from the Final Fantasy VIII soundtrack.", "tags": [], "aliases": [], "originalContent": {"artists": "(prod. Na<PERSON> Gawd)"}}, {"id": "59c5d044901b9d1160444c60c0f25140", "title": "Outchea", "artists": "(prod. <PERSON><PERSON>)", "length": "3:37", "ogFilename": "Outchea", "notes": "Was released alongside a music video in 2013 but seemingly taken down everywhere. Released on Mar 28 9:30 pm and was the lead single for $ensation.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/35727d443a5708a4d7b1f36adfa4579b/play", "key": "Outchea", "description": "OG Filename: <PERSON><PERSON><PERSON>\nWas released alongside a music video in 2013 but seemingly taken down everywhere. Released on Mar 28 9:30 pm and was the lead single for $ensation.", "date": 13644288, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/59c5d044901b9d1160444c60c0f25140", "size": "3.27 MB", "duration": 217.56, "originalContent": {"url": "https://music.froste.lol/song/35727d443a5708a4d7b1f36adfa4579b/play", "artists": "(prod. <PERSON><PERSON>)"}}, {"id": "9c4f76ef0dbb6f3b6a8146d4d80e155e", "title": "So Cold", "artists": "(feat. A$AP Rocky) (prod. J Endo Productions)", "length": "2:04", "ogFilename": "socold", "notes": "From 2013. <PERSON><PERSON><PERSON> implies it is from a project titled \"Sen$ation\". Likely the first time <PERSON> and <PERSON><PERSON> collaborated. Has no <PERSON> verse only backing vocals.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/7d8d347636fda47d70eeee0aa2a39f0d/play", "key": "So Cold", "description": "OG Filename: socold\nFrom 2013. Metadata implies it is from a project titled \"Sen$ation\". Likely the first time <PERSON> and <PERSON><PERSON> collaborated. Has no <PERSON> verse only backing vocals.", "date": 16728768, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/9c4f76ef0dbb6f3b6a8146d4d80e155e", "size": "2.52 MB", "duration": 124.01, "originalContent": {"url": "https://music.froste.lol/song/7d8d347636fda47d70eeee0aa2a39f0d/play", "artists": "(feat. A$AP Rocky) (prod. J Endo Productions)"}}, {"id": "f2e9d6699381ac93fc7b3a4e4d82df83", "title": "Terror Shit", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "4:18", "ogFilename": null, "notes": "From 2013.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/e1d50bc62718f2127977192183ccce03/play", "key": "Terror Shit", "description": "From 2013.", "date": 14413248, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f2e9d6699381ac93fc7b3a4e4d82df83", "size": "5.66 MB", "duration": 258.24, "originalContent": {"url": "https://music.froste.lol/song/e1d50bc62718f2127977192183ccce03/play", "artists": "(prod. <PERSON><PERSON><PERSON>)"}}, {"id": "175e14fb6fda7cb11d34dc4e81bf9670", "title": "This Ca$h", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "3:46", "ogFilename": null, "notes": "Originally released around May 31, 2013", "tags": [], "aliases": ["(Cash)"], "type": "track", "originalUrl": "https://music.froste.lol/song/a64e857ff974a83a2a5b165e1ee98d5b/play", "key": "This Ca$h", "description": "Originally released around May 31, 2013", "date": null, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/175e14fb6fda7cb11d34dc4e81bf9670", "size": "5.15 MB", "duration": 226.59, "originalContent": {"url": "https://music.froste.lol/song/a64e857ff974a83a2a5b165e1ee98d5b/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}, {"id": "69ef5f86cd2bbc2eadde5b62a1717271", "title": "YUNGXANHOE", "artists": "(prod. 454 & Ethereal)", "length": "2:56", "ogFilename": "Ps2 GLITCHES x XAN HOE", "notes": "Made in November - December 2013. Released as a single on Play<PERSON><PERSON>'s Soundcloud on January 8th, 2014. The same instrumental was also used by XXXTENTACION on his track \"Bloodstains\"", "tags": ["OG File"], "aliases": ["(XAN HOE)"], "type": "track", "originalUrl": "https://music.froste.lol/song/334edba7ceee3cbf4410ea55a1eab5aa/play", "key": "YUNGXANHOE", "description": "OG Filename: Ps2 GLITCHES x XAN HOE\nMade in November - December 2013. Released as a single on Play<PERSON><PERSON>'s Soundcloud on January 8th, 2014. The same instrumental was also used by XXXTENTACION on his track \"Bloodstains\"", "date": 16208640, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/69ef5f86cd2bbc2eadde5b62a1717271", "size": "2.94 MB", "duration": 176.69, "originalContent": {"url": "https://music.froste.lol/song/334edba7ceee3cbf4410ea55a1eab5aa/play", "artists": "(prod. 454 & Ethereal)"}}]}, {"era": "Awful Records", "tracks": [{"id": "6f6fe95345ade6e33774ae8c89bb8892", "title": "Soul", "artists": "(prod. ICYTWAT)", "length": "3:20", "ogFilename": null, "notes": "Original version of <PERSON>'s <PERSON><PERSON>, with a different beat and a different vocal take, only sharing the lyrics from <PERSON><PERSON>. <PERSON>ti. Has a beatswitch. Most of the track is just adlibs. A higher bitrate version surfaced on Jan 15, 2025 which sounds much better n clearer.", "tags": [], "aliases": ["(<PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e2e301358321dea4f77414214364f15e/play", "key": "Soul", "description": "Original version of <PERSON>'s <PERSON><PERSON>, with a different beat and a different vocal take, only sharing the lyrics from <PERSON><PERSON>. <PERSON>ti. Has a beatswitch. Most of the track is just adlibs. A higher bitrate version surfaced on Jan 15, 2025 which sounds much better n clearer.", "date": 14620608, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6f6fe95345ade6e33774ae8c89bb8892", "size": "3.28 MB", "duration": 200.18, "originalContent": {"url": "https://music.froste.lol/song/e2e301358321dea4f77414214364f15e/play", "artists": "(prod. ICYTWAT)"}}, {"id": "8af87953c08142473a286d1d5bfdd758", "title": "Southside Freestyle [V2]", "artists": "(prod. 019dexter)", "length": "2:43", "ogFilename": "Carti - Southside Freestyle", "notes": "Leaked on February 26, 2017. Originally released by Awful Records on SoundCloud on November 18, 2014.", "tags": ["OG File", "Lossless"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/b55d208636f894bf86970c563a0c8846/play", "key": "Southside Freestyle", "description": "OG Filename: <PERSON><PERSON> - Southside Freestyle\nLeaked on February 26, 2017. Originally released by Awful Records on SoundCloud on November 18, 2014.", "date": 14880672, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/8af87953c08142473a286d1d5bfdd758", "size": "2.69 MB", "duration": 163.68, "originalContent": {"url": "https://music.froste.lol/song/b55d208636f894bf86970c563a0c8846/play", "artists": "(prod. 019dexter)"}}, {"id": "380c0fb512e49381a0b7f5f3c2865300", "title": "VVS Diamonds", "artists": "(prod. <PERSON>)", "length": "2:12", "ogFilename": "VVS DIAMONDS", "notes": "A throwaway from the 'Ca$h Carti' sessions.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/5f7807a32962200def1f52f6acc08c18/play", "key": "VVS Diamonds", "description": "OG Filename: VVS DIAMONDS\nA throwaway from the 'Ca$h Carti' sessions.", "date": 15257376, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/380c0fb512e49381a0b7f5f3c2865300", "size": "2.19 MB", "duration": 132.1, "originalContent": {"url": "https://music.froste.lol/song/5f7807a32962200def1f52f6acc08c18/play", "artists": "(prod. <PERSON>)"}}, {"id": "691e4f80971db3c90a0dbfc4d5942193", "title": "<PERSON><PERSON>", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)", "length": "5:02", "ogFilename": null, "notes": "A throwaway from the 'Ca$h Carti' sessions. Recorded on April 14, 2015.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/e1d5d74774988addd0e65e5818898646/play", "key": "<PERSON><PERSON>", "description": "A throwaway from the 'Ca$h Carti' sessions. Recorded on April 14, 2015.", "date": 15424128, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/691e4f80971db3c90a0dbfc4d5942193", "size": "4.92 MB", "duration": 302.86, "originalContent": {"url": "https://music.froste.lol/song/e1d5d74774988addd0e65e5818898646/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)"}}, {"id": "6b3a5708425aa4ba16083414ac1343f0", "title": "Load It", "artists": "(feat. A$AP Rocky & A$AP Ferg)", "length": "2:02", "ogFilename": "<PERSON><PERSON> feat. <PERSON><PERSON> Rocky & Asap Ferg idea 01.04.15", "notes": "Leaked on April 5, 2022. Included in the groupbuy with Other Shit OG.", "tags": ["OG File", "Lossless"], "aliases": ["(Idea", "Freestyle)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d374fb4d0feada706ca251619a828506/play", "key": "Load It", "description": "OG Filename: <PERSON><PERSON> feat. <PERSON><PERSON> Rocky & Asap Ferg idea 01.04.15\nLeaked on April 5, 2022. Included in the groupbuy with Other Shit OG.", "date": 16491168, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/6b3a5708425aa4ba16083414ac1343f0", "size": "2.04 MB", "duration": 122.88, "originalContent": {"url": "https://music.froste.lol/song/d374fb4d0feada706ca251619a828506/play", "artists": "(feat. A$AP Rocky & A$AP Ferg)"}}]}, {"era": "<PERSON> The Mixtape", "tracks": [{"id": "6a86c72a19597ff1542e8411c72d6b52", "title": "Nobody", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "3:41", "ogFilename": "Nobody prod. <PERSON><PERSON><PERSON>", "notes": "A song from Chucky Era. Debuted in Ethereal’s mix “Awful Vibes”, hosted by Pomp<PERSON>Clout on SoundCloud. The second verse was cut in the mix and was never released. The full song leaked on September 25, 2022.", "tags": ["OG File"], "aliases": ["(These Niggas Mad At Me)"], "type": "track", "originalUrl": "https://music.froste.lol/song/8964e955146cd37434c7bbfa28867811/play", "key": "Nobody", "description": "OG Filename: Nobody prod. <PERSON><PERSON><PERSON>\nA song from Chucky Era. Debuted in Ethereal’s mix “Awful Vibes”, hosted by Pomp<PERSON>Clout on SoundCloud. The second verse was cut in the mix and was never released. The full song leaked on September 25, 2022.", "date": 16640640, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/6a86c72a19597ff1542e8411c72d6b52", "size": "4.31 MB", "duration": 221.69, "originalContent": {"url": "https://music.froste.lol/song/8964e955146cd37434c7bbfa28867811/play", "artists": "(prod. <PERSON><PERSON><PERSON>) "}}, {"id": "218a946b52b1394e3bff7e460174eed8", "title": "Not Comin' [V2]", "artists": "(prod. ICYTWAT & Lord <PERSON>bu)", "length": "2:08", "ogFilename": null, "notes": "Recorded at the Diamond Factory Studios. Was rumored to be from 2017 but said by waterfalls to be from 2015.", "tags": [], "aliases": ["(<PERSON><PERSON>", "Money)"], "type": "track", "originalUrl": "https://music.froste.lol/song/4c454770e6eab646a3e4945f81c69f6b/play", "key": "Not Comin'", "description": "Recorded at the Diamond Factory Studios. Was rumored to be from 2017 but said by waterfalls to be from 2015.", "date": 15928704, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/218a946b52b1394e3bff7e460174eed8", "size": "2.81 MB", "duration": 128.1, "originalContent": {"url": "https://music.froste.lol/song/4c454770e6eab646a3e4945f81c69f6b/play", "artists": "(prod. ICYTWAT & Lord <PERSON>bu)"}}, {"id": "423e2adc8eafd32bd5dae68ee0eff04e", "title": "Too Many", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)", "length": "3:14", "ogFilename": "TOO MANY mix a", "notes": "Me<PERSON><PERSON> produced throwaway.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/2b421f0bee0e298e7b9088c07b6d42f8/play", "key": "Too Many", "description": "OG Filename: TOO MANY mix a\nMexiko Dro produced throwaway.", "date": 16702848, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/423e2adc8eafd32bd5dae68ee0eff04e", "size": "3.88 MB", "duration": 194.66, "originalContent": {"url": "https://music.froste.lol/song/2b421f0bee0e298e7b9088c07b6d42f8/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "b4438f466f694696561262e8824604b0", "title": "PLAYBOIFRE$H FREESTYLE [V1]", "artists": "(prod. <PERSON>) (feat. <PERSON>)", "length": "3:45", "ogFilename": null, "notes": "OG version of Playboifre$h Freestyle, featuring <PERSON>.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/f28d5d7a260e712f377b3b609824d8ab/play", "key": "PLAYBOIFRE$H FREESTYLE", "description": "OG version of Playboifre$h Freestyle, featuring <PERSON>.", "date": 16285536, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b4438f466f694696561262e8824604b0", "size": "4.37 MB", "duration": 225.17, "originalContent": {"url": "https://music.froste.lol/song/f28d5d7a260e712f377b3b609824d8ab/play", "artists": "(prod. <PERSON>) (feat. <PERSON>)"}}, {"id": "d9b00584e19ecc8662e2fa03cd532fb4", "title": "Numbers", "artists": "(feat. Friday <PERSON><PERSON><PERSON>) (prod. 808 Mafia)", "length": "1:50", "ogFilename": null, "notes": "Recorded in 2015.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/7f80a311dfe117a68ab7a36481727033/play", "key": "Numbers", "description": "Recorded in 2015.", "date": 15278976, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d9b00584e19ecc8662e2fa03cd532fb4", "size": "2.53 MB", "duration": 110.33, "originalContent": {"url": "https://music.froste.lol/song/7f80a311dfe117a68ab7a36481727033/play", "artists": "(feat. Friday <PERSON><PERSON><PERSON>) (prod. 808 Mafia)"}}, {"id": "ebc3d335347d746a59ea42ee2585989a", "title": "Leanin [V1]", "artists": "(prod. ICYTWAT & Lord <PERSON>bu)", "length": "2:46", "ogFilename": "Carti - <PERSON>nin (Prod ICYTWAT)", "notes": "Reuses the hook from Fuck It Up. Uses the beat that would be used for \"Not Comin\"", "tags": ["OG File"], "aliases": ["(Not Comin)"], "type": "track", "originalUrl": "https://music.froste.lol/song/03d391f861b195ffad189ed2c11cc64a/play", "key": "Leanin", "description": "OG Filename: <PERSON><PERSON> <PERSON><PERSON> (Prod ICYTWAT)\nReuses the hook from Fuck It Up. Uses the beat that would be used for \"Not Comin\"", "date": 16728768, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ebc3d335347d746a59ea42ee2585989a", "size": "3.42 MB", "duration": 166.08, "originalContent": {"url": "https://music.froste.lol/song/03d391f861b195ffad189ed2c11cc64a/play", "artists": "(prod. ICYTWAT & Lord <PERSON>bu)"}}, {"id": "4d7949676941f69ca7280029f3f8a161", "title": "Faith", "artists": "(prod. MexikoDro & StoopidXool)", "length": "3:15", "ogFilename": "Carti2", "notes": "Previewed in @countingcasket's Instagram Live. Verse was reused in <PERSON> \"Dallas\".", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/2ed8d504373941a9c0a363cd9986c6e4/play", "key": "Faith", "description": "OG Filename: Carti2\nPreviewed in @countingcasket's Instagram Live. Verse was reused in <PERSON> \"Dallas\".", "date": 16700256, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4d7949676941f69ca7280029f3f8a161", "size": "3.89 MB", "duration": 195.07, "originalContent": {"url": "https://music.froste.lol/song/2ed8d504373941a9c0a363cd9986c6e4/play", "artists": "(prod. MexikoDro & StoopidXool)"}}, {"id": "97ea017ec369097bb29e111509a2e26e", "title": "CHECK!", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "3:50", "ogFilename": null, "notes": "A throwaway from the 'Ca$h Carti' sessions. Possibly made for the unreleased <PERSON><PERSON> and <PERSON> collab EP but this isn't confirmed.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/573093f630dcdade334ca7586c536c4b/play", "key": "CHECK!", "description": "A throwaway from the 'Ca$h Carti' sessions. Possibly made for the unreleased <PERSON><PERSON> and <PERSON> collab EP but this isn't confirmed.", "date": 16200000, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/97ea017ec369097bb29e111509a2e26e", "size": "4.46 MB", "duration": 230.92, "originalContent": {"url": "https://music.froste.lol/song/573093f630dcdade334ca7586c536c4b/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}]}, {"era": "Ca$h Carti Season", "tracks": [{"id": "6131be881aea550be7231d288b454e6a", "title": "Finesse Remix", "artists": "(prod. Chinatown & ​captaincrunch)", "length": "3:01", "ogFilename": "<PERSON><PERSON>ha Bizness Finesse Remix rough mix-1", "notes": "Chinatown released the beat on SoundCloud as \"Aston Martin\". Leaked on May 26, 2022.", "tags": ["OG File"], "aliases": ["(Fell In Love", "Aston Martin)"], "type": "track", "originalUrl": "https://music.froste.lol/song/99667cb3b6026e6d72ef8354ce34eb90/play", "key": "Finesse Remix", "description": "OG Filename: <PERSON><PERSON> Finesse Remix rough mix-1\nChinatown released the beat on SoundCloud as \"Aston Martin\". Leaked on May 26, 2022.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6131be881aea550be7231d288b454e6a", "size": "4.59 MB", "duration": 181, "originalContent": {"url": "https://music.froste.lol/song/99667cb3b6026e6d72ef8354ce34eb90/play", "artists": "(prod. Chinatown & ​captaincrunch)"}}, {"id": "08bd4ec16d536bf332d1ee7c0e26f2bd", "title": "On Top [V2]", "artists": "(prod. MexikoDro & StoopidXool)", "length": "3:53", "ogFilename": "playboi carti - #1", "notes": "Original solo version. Has an open verse at the end.", "tags": ["OG File"], "aliases": ["(Dats My Dawg)"], "type": "track", "originalUrl": "https://music.froste.lol/song/72738969cbc356a6aeef83531f5e1f76/play", "key": "On Top", "description": "OG Filename: playboi carti - #1\nOriginal solo version. Has an open verse at the end.", "date": 15372288, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/08bd4ec16d536bf332d1ee7c0e26f2bd", "size": "5.42 MB", "duration": 233.17, "originalContent": {"url": "https://music.froste.lol/song/72738969cbc356a6aeef83531f5e1f76/play", "artists": "(prod. MexikoDro & StoopidXool)"}}, {"id": "dc91114ca933430c730c4146956b8f6b", "title": "On The Block*", "artists": "(feat. <PERSON><PERSON> & <PERSON><PERSON>) (prod. Chief Ke<PERSON> & DP Beats)", "length": "1:40", "ogFilename": "<PERSON><PERSON>i", "notes": "OG File Metadata: fredo gleesh carti (prod. sosa Leaked by countingcaskets along with <PERSON><PERSON> and the untitled MexikoDro track. Was likely recorded on Jan 4, 2016 as revealed in a tweet by @BigE_Records.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON> On Me)"], "type": "track", "originalUrl": "https://music.froste.lol/song/41b6303ef61c5a553bfcb53a48a80dfc/play", "key": "On The Block*", "description": "OG Filename: <PERSON><PERSON> carti\nOG File Metadata: fredo gleesh carti (prod. sosa\nLeaked by countingcaskets along with <PERSON><PERSON> and the untitled MexikoDro track. Was likely recorded on Jan 4, 2016 as revealed in a tweet by @BigE_Records.", "date": 16728768, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/dc91114ca933430c730c4146956b8f6b", "size": "3.3 MB", "duration": 100.27, "originalContent": {"url": "https://music.froste.lol/song/41b6303ef61c5a553bfcb53a48a80dfc/play", "artists": "(feat. <PERSON><PERSON> & <PERSON><PERSON>) (prod. Chief Ke<PERSON> & DP Beats)"}}, {"id": "49fb8c46cc6e220c02a2ddb5fafe41b3", "title": "Turn Up", "artists": "(prod. <PERSON> Cannon & Maaly Raw)", "length": "3:09", "ogFilename": null, "notes": "\"Turn Up,\" a grail also known as the unreleased Maaly & Cannon beat from 2016, was supposed to be the blind group buy on January 31, 2025. Later that same day, the song was force leaked by Soul.", "tags": [], "aliases": ["(Hit", "The Don Maaly Snippet)"], "type": "track", "originalUrl": "https://music.froste.lol/song/f38e828bae2b554dd9ffe476346b6c06/play", "key": "Turn Up", "description": "\"Turn Up,\" a grail also known as the unreleased Maaly & Cannon beat from 2016, was supposed to be the blind group buy on January 31, 2025. Later that same day, the song was force leaked by Soul.", "date": 17382816, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/49fb8c46cc6e220c02a2ddb5fafe41b3", "size": "4.73 MB", "duration": 189.38, "originalContent": {"url": "https://music.froste.lol/song/f38e828bae2b554dd9ffe476346b6c06/play", "artists": "(prod. <PERSON> Cannon & Maaly Raw)"}}, {"id": "c193549fd9f50b6ecbb68cb52668e7d9", "title": "Top Me Off", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)", "length": "2:58", "ogFilename": "<PERSON><PERSON> - top me off", "notes": "Leaked on September 25, 2022.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/4b92784ff5aa31925fc9abb821739f9f/play", "key": "Top Me Off", "description": "OG Filename: <PERSON><PERSON> <PERSON> top me off\nLeaked on September 25, 2022.", "date": 16640640, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/c193549fd9f50b6ecbb68cb52668e7d9", "size": "4.55 MB", "duration": 178.8, "originalContent": {"url": "https://music.froste.lol/song/4b92784ff5aa31925fc9abb821739f9f/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7bed94dd21f27db6e9ce407851eb8c64", "title": "Drop", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON>)", "length": "2:43", "ogFilename": null, "notes": "A throwaway from the 'Ca$h Carti' sessions.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/2ea4a7dba0103bf4c9fc06315536dcb4/play", "key": "Drop", "description": "A throwaway from the 'Ca$h Carti' sessions.", "date": 15242688, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7bed94dd21f27db6e9ce407851eb8c64", "size": "4.31 MB", "duration": 163.32, "originalContent": {"url": "https://music.froste.lol/song/2ea4a7dba0103bf4c9fc06315536dcb4/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON>)"}}, {"id": "f7a201e2b1ab73d3fb8d34dcc3083938", "title": "Make Some Blow (v1)", "artists": "(prod. MexikoDro & StoopidXool)", "length": "3:10", "ogFilename": "makesomeBlowCarti", "notes": "<PERSON> first uploaded by 50kgold to his soundcloud with a lot of producer tags. OG file later leaked with way less tags.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/554169d1d1fc51956ffb2c69718ee666/play", "key": "Make Some Blow (v1)", "description": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> first uploaded by 50kgold to his soundcloud with a lot of producer tags. OG file later leaked with way less tags.", "date": 16316640, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/f7a201e2b1ab73d3fb8d34dcc3083938", "size": "4.75 MB", "duration": 190.78, "originalContent": {"url": "https://music.froste.lol/song/554169d1d1fc51956ffb2c69718ee666/play", "artists": "(prod. MexikoDro & StoopidXool)"}}]}, {"era": "Collaboration with Digital Nas", "tracks": [{"id": "4183e7e0ada0365fa1d8c842b2cc2526", "title": "Answer My Phone", "artists": "(prod. <PERSON> Nas)", "length": "4:28", "ogFilename": "ANSWER MY PHONE MBM 1", "notes": "Previewed in @CashCarti's Snapchat Story. Leaked on July 17, 2022. Has open verse", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON><PERSON>' <PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/b90c40762c7e03fdc6b8ec2e83d344eb/play", "key": "Answer My Phone", "description": "OG Filename: ANSWER MY PHONE MBM 1\nPreviewed in @CashCarti's Snapchat Story. Leaked on July 17, 2022. Has open verse", "date": 16580160, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4183e7e0ada0365fa1d8c842b2cc2526", "size": "4.43 MB", "duration": 268.9, "originalContent": {"url": "https://music.froste.lol/song/b90c40762c7e03fdc6b8ec2e83d344eb/play", "artists": "(prod. <PERSON> Nas)"}}, {"id": "e6030608e611a6f576ab0cc2112c5ebe", "title": "Rollin' Up", "artists": "(feat. <PERSON>) (prod. Digital Nas)", "length": "4:40", "ogFilename": "CARTI X LUXXK", "notes": "Previewed in a Snapchat Story. Song was released by Lil L under the name <PERSON><PERSON> & Luxxk (feat. <PERSON><PERSON>) on Feb 16, 2022, song wasnt found by fans untill Mar 16, 2022", "tags": ["OG File"], "aliases": ["(Burnin Up)"], "type": "track", "originalUrl": "https://music.froste.lol/song/5d606b981f4b0d6ec840ad29fbd3f136/play", "key": "Rollin' Up", "description": "OG Filename: CARTI X LUXXK\nPreviewed in a Snapchat Story. Song was released by Lil L under the name <PERSON><PERSON> & Luxxk (feat. <PERSON><PERSON>) on Feb 16, 2022, song wasnt found by fans untill Mar 16, 2022", "date": 16449696, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/e6030608e611a6f576ab0cc2112c5ebe", "size": "4.61 MB", "duration": 280.18, "originalContent": {"url": "https://music.froste.lol/song/5d606b981f4b0d6ec840ad29fbd3f136/play", "artists": "(feat. <PERSON>) (prod. Digital Nas)"}}, {"id": "18c8341b355be18f38451c71b9222811", "title": "Yah [V3]", "artists": "(prod. <PERSON> Nas)", "length": "2:48", "ogFilename": "Playboi <PERSON> rough mix kk", "notes": "Later version of \"Yah\", featuring a much better mix than V1.", "tags": ["OG File"], "aliases": ["(Call Up The Troops)"], "type": "track", "originalUrl": "https://music.froste.lol/song/60b1d6aa50935618a05e9d324eb717e7/play", "key": "<PERSON>h", "description": "OG Filename: <PERSON><PERSON><PERSON> rough mix kk\nLater version of \"Yah\", featuring a much better mix than V1.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/18c8341b355be18f38451c71b9222811", "size": "2.83 MB", "duration": 168.67, "originalContent": {"url": "https://music.froste.lol/song/60b1d6aa50935618a05e9d324eb717e7/play", "artists": "(prod. <PERSON> Nas)"}}, {"id": "06fe26420329818c71e900d24f534997", "title": "<PERSON><PERSON>", "artists": "(prod. <PERSON> Nas)", "length": "2:13", "ogFilename": "CARTIER rough", "notes": "Uses the same beat that would be used for \"Shut Up\". Unrelated to \"Cartier\" from the Whole Lotta Red sessions.", "tags": ["OG File"], "aliases": ["(Shut Up)"], "type": "track", "originalUrl": "https://music.froste.lol/song/622dc8ae99897c1a6354fb33a97e5283/play", "key": "<PERSON><PERSON>", "description": "OG Filename: CARTIER rough\nUses the same beat that would be used for \"Shut Up\". Unrelated to \"Cartier\" from the Whole Lotta Red sessions.", "date": 16728768, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/06fe26420329818c71e900d24f534997", "size": "2.26 MB", "duration": 133.01, "originalContent": {"url": "https://music.froste.lol/song/622dc8ae99897c1a6354fb33a97e5283/play", "artists": "(prod. <PERSON> Nas)"}}]}, {"era": "<PERSON><PERSON><PERSON>", "tracks": [{"id": "94ec493778b1c78d6315848682ba0149", "title": "2900", "artists": "(prod. Southside & Pi'erre Bourne)", "length": "4:03", "ogFilename": "Carti - 2900", "notes": "Previewed by @playboicarti's on his Snapchat Story.", "tags": ["OG File"], "aliases": ["(2900 My Block)"], "type": "track", "originalUrl": "https://music.froste.lol/song/aa8659361f3d23e76bbf6219c1f46c84/play", "key": "2900", "description": "OG Filename: Carti - 2900\nPreviewed by @play<PERSON>ica<PERSON><PERSON>'s on his Snapchat Story.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/94ec493778b1c78d6315848682ba0149", "size": "5.94 MB", "duration": 243.91, "originalContent": {"url": "https://music.froste.lol/song/aa8659361f3d23e76bbf6219c1f46c84/play", "artists": "(prod. Southside & Pi'erre Bourne)"}}, {"id": "generated_id_<PERSON><PERSON><PERSON>_<PERSON>ti_7_1", "title": "Brand New [V1]", "artists": "(prod. Chinatown)", "length": null, "ogFilename": "Brand New 1.13.17-2", "notes": "A throwaway from the 'Play<PERSON><PERSON>ti' sessions. Beat would later be reused on Designer Shoes.", "tags": ["OG File"], "aliases": ["(Brand New Bihh", "Designer Shoes 2016)"], "originalContent": {"artists": "(prod. Chinatown)"}}, {"id": "163c40097bb2b44551dc05676fc06734", "title": "From <PERSON>", "artists": null, "length": "2:19", "ogFilename": null, "notes": "<PERSON><PERSON><PERSON><PERSON> from Self Titled.", "tags": [], "aliases": ["(<PERSON>uch", "Sleeping On A Couch", "From The Mud)"], "type": "track", "originalUrl": "https://music.froste.lol/song/3f3c174a599010521424b1e5c82819c9/play", "key": "From <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> from Self Titled.", "date": 16628544, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/163c40097bb2b44551dc05676fc06734", "size": "4.27 MB", "duration": 139.56, "originalContent": {"url": "https://music.froste.lol/song/3f3c174a599010521424b1e5c82819c9/play", "artists": null}}, {"id": "b000236d975600877279040e6f4b6fdc", "title": "Go Up [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:45", "ogFilename": "<PERSON><PERSON> new 1st verse", "notes": "A throwaway from the Self Titled sessions", "tags": ["OG File"], "aliases": ["(<PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/76469cbfce1be9933b8439cdba69da19/play", "key": "Go Up", "description": "OG Filename: <PERSON><PERSON> new 1st verse\nA throwaway from the Self Titled sessions", "date": 15770592, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b000236d975600877279040e6f4b6fdc", "size": "4.68 MB", "duration": 165.22, "originalContent": {"url": "https://music.froste.lol/song/76469cbfce1be9933b8439cdba69da19/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "71e4cc904cd50349437b43d866eb289b", "title": "If Money Ain't Involved [V1]", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:02", "ogFilename": "<PERSON><PERSON> If Money Aint Involved rough open", "notes": "Leaked on March 5, 2021. Engineered by <PERSON><PERSON>.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/4e09faac6a81f0cbef9f8a6be924f98f/play", "key": "If Money Ain't Involved", "description": "OG Filename: <PERSON><PERSON> If Money Aint Involved rough open\nLeaked on March 5, 2021. Engineered by <PERSON><PERSON>.", "date": 16149024, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/71e4cc904cd50349437b43d866eb289b", "size": "4.97 MB", "duration": 182.96, "originalContent": {"url": "https://music.froste.lol/song/4e09faac6a81f0cbef9f8a6be924f98f/play", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "3142581128f0aad5fbc51ef27e7cfbed", "title": "In My Car [V2]", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "3:25", "ogFilename": "LiL Boat x Carti", "notes": "A throwaway from the 'Playboi Carti' sessions.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/366a2c0211d4cfe0fb522bcf896445bc", "key": "In My Car", "description": "OG Filename: LiL Boat x Carti\nA throwaway from the 'Playboi Carti' sessions.", "date": 15881184, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/3142581128f0aad5fbc51ef27e7cfbed", "size": "5.32 MB", "duration": 205.27, "originalContent": {"url": "https://pillowcase.su/f/366a2c0211d4cfe0fb522bcf896445bc", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}, {"id": "00ea2b3542d708601caad7b5025ea5e1", "title": "At The Gate [V4]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:44", "ogFilename": "<PERSON><PERSON> At The Gate new 1st verse", "notes": "A version with the open verse filled with a new verse. Leaked February 22, 2025 after a succesful buy in the Carti Tracker Discord (thank us later).", "tags": ["OG File"], "aliases": ["(Let Em In", "Buzz 'Em In)"], "type": "track", "originalUrl": "https://music.froste.lol/song/0e58959312e548ef659d95a761a74123/play", "key": "At The Gate", "description": "OG Filename: <PERSON><PERSON> At The Gate new 1st verse\nA version with the open verse filled with a new verse. Leaked February 22, 2025 after a succesful buy in the Carti Tracker Discord (thank us later).", "date": 17401824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/00ea2b3542d708601caad7b5025ea5e1", "size": "4.67 MB", "duration": 164.3, "originalContent": {"url": "https://music.froste.lol/song/0e58959312e548ef659d95a761a74123/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "a73fb2f1ae3a49aeebea36198c083180", "title": "No Charm [V1]", "artists": "(prod. <PERSON>)", "length": "2:33", "ogFilename": "No Charm v1", "notes": "Leaked on January 3, 2019. Sound wise it is very similar to Location.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/513991cc7ebd8238a690895f89c2d47d/play", "key": "No Charm", "description": "OG Filename: No Charm v1\nLeaked on January 3, 2019. Sound wise it is very similar to Location.", "date": 15464736, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/a73fb2f1ae3a49aeebea36198c083180", "size": "4.5 MB", "duration": 153.84, "originalContent": {"url": "https://music.froste.lol/song/513991cc7ebd8238a690895f89c2d47d/play", "artists": "(prod. <PERSON>)"}}, {"id": "3bcdd0c634dee8064f96c12f6c73b914", "title": "One Day [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:56", "ogFilename": "ONE DAY KY MIX 2", "notes": "Found in sessions leaked by KAPPER, alternate mix", "tags": ["OG File", "Lossless"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/2ac3086f122c83ec80fe5e1cd1b5a1f9/play", "key": "One Day", "description": "OG Filename: ONE DAY KY MIX 2\nFound in sessions leaked by KAPPER, alternate mix", "date": 17083008, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/3bcdd0c634dee8064f96c12f6c73b914", "size": "4.86 MB", "duration": 176.23, "originalContent": {"url": "https://music.froste.lol/song/2ac3086f122c83ec80fe5e1cd1b5a1f9/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "96ade3e49c6830bcd3e46d380096556d", "title": "Places", "artists": "(prod. <PERSON>)", "length": "3:03", "ogFilename": null, "notes": "Snippet leaked by PrivateFriend and previewed in @countingcasket's Instagram Live. Leaked on July 23, 2022. Samples \"Bad of the Heart\" by <PERSON>. File that leaked claiming the song was <PERSON> was fake", "tags": [], "aliases": ["(Pvris", "I'm In P<PERSON><PERSON> Baby)"], "type": "track", "originalUrl": "https://music.froste.lol/song/5f5509835737d7b860febb883ba80992/play", "key": "Places", "description": "Snippet leaked by PrivateFriend and previewed in @countingcasket's Instagram Live. Leaked on July 23, 2022. Samples \"Bad of the Heart\" by <PERSON>. File that leaked claiming the song was <PERSON> was fake", "date": 16585344, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/96ade3e49c6830bcd3e46d380096556d", "size": "4.98 MB", "duration": 183.94, "originalContent": {"url": "https://music.froste.lol/song/5f5509835737d7b860febb883ba80992/play", "artists": "(prod. <PERSON>)"}}, {"id": "d007ba94bce03e4d15113a0f190af775", "title": "<PERSON><PERSON> [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:12", "ogFilename": "ROLLIE HOOK - <PERSON><PERSON>", "notes": "OG Filename (Metadata): R<PERSON>L<PERSON> HOOK Original version of \"Fell In Love\" with just a hook.", "tags": ["OG File"], "aliases": ["(Fell In Love", "<PERSON><PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/c01260ded948c357a3ef7b551856c68c/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON><PERSON><PERSON> HOOK - <PERSON>ti\nOG Filename (Metadata): R<PERSON><PERSON><PERSON> HOOK\nOriginal version of \"Fell In Love\" with just a hook.", "date": 16851456, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d007ba94bce03e4d15113a0f190af775", "size": "4.16 MB", "duration": 132.6, "originalContent": {"url": "https://music.froste.lol/song/c01260ded948c357a3ef7b551856c68c/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "f6477833c448631b3222efa282345d2b", "title": "Squad [V5]", "artists": "(with <PERSON>) (prod. Chinatown)", "length": "3:22", "ogFilename": "UZI CARTI SQAUD MM", "notes": "Version featuring a bonus uzi verse & different mix. File name is misspelled.", "tags": ["OG File"], "aliases": ["(I'M WIT' THE SQUAD", "FIREARM)"], "type": "track", "originalUrl": "https://music.froste.lol/song/71fd4a8fab862193d627cd226dc2480f/play", "key": "Squad", "description": "OG Filename: UZI CARTI SQAUD MM\nVersion featuring a bonus uzi verse & different mix. File name is misspelled.", "date": 16789248, "available": ["OG File", "rgb(0, 0, 0)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f6477833c448631b3222efa282345d2b", "size": "3.7 MB", "duration": 202.32, "originalContent": {"url": "https://music.froste.lol/song/71fd4a8fab862193d627cd226dc2480f/play", "artists": "(with <PERSON>) (prod. Chinatown)"}}, {"id": "dc360e2f6fd8669c8c76a6bbfd663d2c", "title": "They Hatin'", "artists": "(prod. <PERSON>)", "length": "2:33", "ogFilename": "<PERSON><PERSON> They Hatin rough", "notes": "A throwaway from the 'Playboi Carti' sessions. Leaked on July 22, 2022.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/a9d5d6d53a693d0bead064b61ddd0862/play", "key": "They Hatin'", "description": "OG Filename: <PERSON><PERSON> They Hatin rough\nA throwaway from the 'Playboi Carti' sessions. Leaked on July 22, 2022.", "date": 16584480, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/dc360e2f6fd8669c8c76a6bbfd663d2c", "size": "4.49 MB", "duration": 153.24, "originalContent": {"url": "https://music.froste.lol/song/a9d5d6d53a693d0bead064b61ddd0862/play", "artists": "(prod. <PERSON>)"}}, {"id": "9b9bcaa3c0f72cd24f6402f55d7b7e79", "title": "Walk Inside My Mansion", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. DJ Du<PERSON> & Zaytoven)", "length": "3:53", "ogFilename": "Walk Inside My Mansion", "notes": "Leaked a day after the release of Self Titled. Was seen on an older tracklist leaked by caskets", "tags": ["OG File"], "aliases": ["(Mansion)"], "type": "track", "originalUrl": "https://music.froste.lol/song/3fca5329dc9e38cd481e9cf0900f8205/play", "key": "Walk Inside My Mansion", "description": "OG Filename: Walk Inside My Mansion\nLeaked a day after the release of Self Titled. Was seen on an older tracklist leaked by caskets", "date": 14922144, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/9b9bcaa3c0f72cd24f6402f55d7b7e79", "size": "5.77 MB", "duration": 233.23, "originalContent": {"url": "https://music.froste.lol/song/3fca5329dc9e38cd481e9cf0900f8205/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. DJ Du<PERSON> & Zaytoven)"}}, {"id": "generated_id_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_7_14", "title": "We So Proud Of Him", "artists": "(prod. BasedTJ & 6Silky)", "length": null, "ogFilename": "Playboi Carti - 1", "notes": "Leaked on May 31, 2017.", "tags": ["OG File"], "aliases": ["(Telly)"], "originalContent": {"artists": "(prod. BasedTJ & 6Silky)"}}, {"id": "5a6e1318cd68fed503fd625c3fd75383", "title": "<PERSON> and Me", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:21", "ogFilename": "<PERSON><PERSON> and Me rough", "notes": "Leaked on November 27, 2019.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/1b8635bc6aa92fe066d3a244dfe51c4d/play", "key": "<PERSON> and Me", "description": "OG Filename: <PERSON><PERSON> and <PERSON> rough\nLeaked on November 27, 2019.", "date": 15748128, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5a6e1318cd68fed503fd625c3fd75383", "size": "5.27 MB", "duration": 201.72, "originalContent": {"url": "https://music.froste.lol/song/1b8635bc6aa92fe066d3a244dfe51c4d/play", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "43178ac43a51002b2e1b809e9e3b8e32", "title": "<PERSON> - Break The Bank [V2]", "artists": "(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:27", "ogFilename": "<PERSON><PERSON> the Bank - bass", "notes": "A bonus to JB groupbuy. Has better mix and bass.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/782b4fd8ea1f6631b8dea61f8e576f41/play", "key": "Break The Bank", "description": "OG Filename: <PERSON><PERSON> Break the Bank - bass\nA bonus to JB groupbuy. Has better mix and bass.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/43178ac43a51002b2e1b809e9e3b8e32", "size": "3.79 MB", "duration": 207.7, "originalContent": {"url": "https://music.froste.lol/song/782b4fd8ea1f6631b8dea61f8e576f41/play", "artists": "(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "a159830aa1d52209919afa0f02d4f4f2", "title": "<PERSON><PERSON><PERSON><PERSON> - TMZ [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Southside)", "length": "3:00", "ogFilename": null, "notes": "Unreleased collaboration between <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> previewed in full on @pierrebourne's Instagram live. <PERSON> has yet to leak in CDQ.", "tags": ["Low Quality"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/58fb3af049772ee087d8d720f6924cfd/play", "key": "TMZ", "description": "Unreleased collaboration between <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> previewed in full on @pierrebourne's Instagram live. <PERSON> has yet to leak in CDQ.", "date": null, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Low Quality", "rgb(255, 255, 255)", "rgb(231, 0, 0)"], "url": "https://api.pillowcase.su/api/download/a159830aa1d52209919afa0f02d4f4f2", "size": "4.93 MB", "duration": 180.79, "originalContent": {"url": "https://music.froste.lol/song/58fb3af049772ee087d8d720f6924cfd/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Southside)"}}, {"id": "7a6357f7a53a689fd722411192c76a67", "title": "<PERSON> - Swag <PERSON>", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. DP Beats)", "length": "2:52", "ogFilename": "Uzi Carti DP Swag Disease", "notes": "A throwaway from the Luv Is Rage 2/Self Titled sessions. The song was once offered for a group buy at $6,000, but for some reason, it was turned down. On November 2, 2024, it was force leaked to pressure the seller into offering other songs for group buys. Songs instrumental is looped, unlike the leaked instrumental. Unofficially released by DP Beats in SC, under the name \"400 Degrees (1600 Block)\".", "tags": ["OG File"], "aliases": ["(400 Degrees", "1600 Block)"], "type": "track", "originalUrl": "https://music.froste.lol/song/fa1c42fa51f1d65a36f21a2d8bd7fe54/play", "key": "Swag Disease", "description": "OG Filename: <PERSON><PERSON> DP Swag Disease\nA throwaway from the Luv Is Rage 2/Self Titled sessions. The song was once offered for a group buy at $6,000, but for some reason, it was turned down. On November 2, 2024, it was force leaked to pressure the seller into offering other songs for group buys. Songs instrumental is looped, unlike the leaked instrumental. Unofficially released by DP Beats in SC, under the name \"400 Degrees (1600 Block)\".", "date": 17305056, "available": ["OG File", "rgb(153, 153, 153)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/7a6357f7a53a689fd722411192c76a67", "size": "3.22 MB", "duration": 172.15, "originalContent": {"url": "https://music.froste.lol/song/fa1c42fa51f1d65a36f21a2d8bd7fe54/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. DP Beats)"}}, {"id": "d9bdb9d19554e0c718a9b73fa34b041c", "title": "A$AP Rocky - Speaking In Tongues", "artists": "(feat. <PERSON><PERSON><PERSON> & KEY!)", "length": "2:15", "ogFilename": "<PERSON>", "notes": "Unreleased collaboration between <PERSON><PERSON><PERSON>, A$AP Rocky & KEY!", "tags": ["OG File"], "aliases": ["(Told Her)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d6f1b4700f00cc963da1b252f0de303d/play", "key": "Speaking In Tongues", "description": "OG Filename: <PERSON> Key\nUnreleased collaboration between <PERSON><PERSON><PERSON>, A$AP Rocky & KEY!", "date": 16702848, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d9bdb9d19554e0c718a9b73fa34b041c", "size": "4.21 MB", "duration": 135.77, "originalContent": {"url": "https://music.froste.lol/song/d6f1b4700f00cc963da1b252f0de303d/play", "artists": "(feat. <PERSON><PERSON><PERSON> & KEY!)"}}, {"id": "generated_id_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_7_20", "title": "Walk On Water", "artists": "(prod. <PERSON>)", "length": null, "ogFilename": null, "notes": "Original version of \"Walk On Water\" with an alternate beat. <PERSON>. Leaked right after being pbd on April 2, 2025.", "tags": [], "aliases": [], "type": "track", "originalUrl": "http://music.froste.lol/song/be10cadae98f57b3a5a3a19de45f62ec/play", "key": "Walk On Water", "description": "Original version of \"Walk On Water\" with an alternate beat. <PERSON>. Leaked right after being pbd on April 2, 2025.", "date": 17435520, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "http://music.froste.lol/song/be10cadae98f57b3a5a3a19de45f62ec/play", "artists": "(prod. <PERSON>)"}}, {"id": "34c4df890771532ce20bed577354c1dc", "title": "A$AP Mob - IDFWTN [V2]", "artists": "(with <PERSON><PERSON><PERSON>, A$AP Rocky, A$AP Nast & <PERSON><PERSON>) (prod. <PERSON><PERSON> 1st & <PERSON> of Loudpvck)", "length": "3:29", "ogFilename": null, "notes": "Unrealesed collab between Carti, Rocky, Nast & Gleesh, presumably from the A$AP Mob collab project. Recorded in 2017.", "tags": [], "aliases": ["(Good Gas)"], "type": "track", "originalUrl": "https://music.froste.lol/song/627794a9d556e73f2efc2afce708f2f0/play", "key": "IDFWTN", "description": "Unrealesed collab between Carti, Rocky, Nast & Gleesh, presumably from the A$AP Mob collab project. Recorded in 2017.", "date": 17313696, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/34c4df890771532ce20bed577354c1dc", "size": "5.4 MB", "duration": 209.98, "originalContent": {"url": "https://music.froste.lol/song/627794a9d556e73f2efc2afce708f2f0/play", "artists": "(with <PERSON><PERSON><PERSON>, A$AP Rocky, A$AP Nast & <PERSON><PERSON>) (prod. <PERSON><PERSON> 1st & <PERSON> of Loudpvck)"}}, {"id": "56ad95e2625414b7dabe9a43f38af5f2", "title": "Early Morning* [V1]", "artists": "(prod. <PERSON><PERSON>)", "length": "3:08", "ogFilename": "carti untld", "notes": "The song by itself is untitled, \"Early Morning\" is just a fan given name. Uses the same beat as two songs by <PERSON><PERSON>, an untitled leak and an unreleased snippet supposedly titled “Cutthroat Records”. Most of the song is unfinished and was supposed to have a <PERSON><PERSON> feature.", "tags": ["OG File"], "aliases": ["(4G", "Untitled", "Cutthroat Records)"], "type": "track", "originalUrl": "https://music.froste.lol/song/51b6ac285ddb27c64d9c2ec53b3ccba0/play", "key": "Early Morning*", "description": "OG Filename: carti untld\nThe song by itself is untitled, \"Early Morning\" is just a fan given name. Uses the same beat as two songs by <PERSON><PERSON>, an untitled leak and an unreleased snippet supposedly titled “Cutthroat Records”. Most of the song is unfinished and was supposed to have a <PERSON><PERSON> feature.", "date": 15093216, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/56ad95e2625414b7dabe9a43f38af5f2", "size": "5.06 MB", "duration": 188.57, "originalContent": {"url": "https://music.froste.lol/song/51b6ac285ddb27c64d9c2ec53b3ccba0/play", "artists": "(prod. <PERSON><PERSON>)"}}, {"id": "aac9e15045a22647525f75aecf851ae9", "title": "Xans & Percs", "artists": "(prod. Hit-<PERSON>)", "length": "3:39", "ogFilename": "<PERSON><PERSON> x Hit 1.1.17", "notes": "Previewed in @playboicarti's Instagram Story.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/9987f56e8636e51c535be8aef982f8b7/play", "key": "Xans & Percs", "description": "OG Filename: <PERSON><PERSON> x Hit 1.1.17\nPreviewed in @playboicarti's Instagram Story.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/aac9e15045a22647525f75aecf851ae9", "size": "5.55 MB", "duration": 219.5, "originalContent": {"url": "https://music.froste.lol/song/9987f56e8636e51c535be8aef982f8b7/play", "artists": "(prod. Hit-<PERSON>)"}}, {"id": "6aca6ff5a6c60eac311011b54a39b197", "title": "Run It Up", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:33", "ogFilename": "<PERSON><PERSON> It Up open 2nd rough", "notes": "Leaked on January 14, 2019.", "tags": ["OG File"], "aliases": ["(007)"], "type": "track", "originalUrl": "https://music.froste.lol/song/8bd7fd84667ea83d60521fdf33ba46d9/play", "key": "Run It Up", "description": "OG Filename: <PERSON><PERSON> It Up open 2nd rough\nLeaked on January 14, 2019.", "date": 15474240, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6aca6ff5a6c60eac311011b54a39b197", "size": "4.49 MB", "duration": 153.53, "originalContent": {"url": "https://music.froste.lol/song/8bd7fd84667ea83d60521fdf33ba46d9/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>) "}}, {"id": "de39521da897166f9a2f45f3954f85b0", "title": "<PERSON><PERSON>", "artists": "(prod. <PERSON>i<PERSON>)", "length": "2:12", "ogFilename": "RONDO", "notes": "<PERSON><PERSON><PERSON><PERSON> from Self Titled. Was rumored to feature <PERSON> but was later revealed by waterfalls to be a lie.", "tags": ["OG File"], "aliases": ["(Keep That 9", "I Ball)"], "type": "track", "originalUrl": "https://music.froste.lol/song/04118ce3dbf4de525eea6c5685f16d70/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON><PERSON><PERSON>owa<PERSON> from Self Titled. Was rumored to feature <PERSON> but was later revealed by waterfalls to be a lie.", "date": 16728768, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/de39521da897166f9a2f45f3954f85b0", "size": "4.15 MB", "duration": 132.12, "originalContent": {"url": "https://music.froste.lol/song/04118ce3dbf4de525eea6c5685f16d70/play", "artists": "(prod. <PERSON>i<PERSON>)"}}, {"id": "142f8870936c44449e2ac8a7995bb185", "title": "Redbana Santana [V1]", "artists": "(prod. <PERSON>)", "length": "2:27", "ogFilename": "<PERSON><PERSON> rough", "notes": "Leaked in early march of 2018.", "tags": ["OG File"], "aliases": ["(Shake 'N Bake", "Red Bandana Santana", "Red Bandana)"], "type": "track", "originalUrl": "https://music.froste.lol/song/bbaf10198470b362113b897ab0e9b5c9/play", "key": "Redbana Santana", "description": "OG Filename: <PERSON><PERSON> rough\nLeaked in early march of 2018.", "date": 15203808, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/142f8870936c44449e2ac8a7995bb185", "size": "4.4 MB", "duration": 147.36, "originalContent": {"url": "https://music.froste.lol/song/bbaf10198470b362113b897ab0e9b5c9/play", "artists": "(prod. <PERSON>)"}}, {"id": "4689ae4edb4f11ba1101cf5007731e42", "title": "Other Shit [V2]", "artists": "(feat. <PERSON>) (prod. Hit-Boy & Maaly Raw)", "length": "2:49", "ogFilename": "Uzi Carti Other Sht ROUGH", "notes": "An alternate mix of \"Other Shit\", still featuring <PERSON><PERSON>.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/1cf23b3a74a2ebf495d4895b32d32a60/play", "key": "Other Shit", "description": "OG Filename: <PERSON><PERSON> Other Sht ROUGH\nAn alternate mix of \"Other Shit\", still featuring <PERSON><PERSON>.", "date": 16726176, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4689ae4edb4f11ba1101cf5007731e42", "size": "4.75 MB", "duration": 169.73, "originalContent": {"url": "https://music.froste.lol/song/1cf23b3a74a2ebf495d4895b32d32a60/play", "artists": "(feat. <PERSON>) (prod. Hit-Boy & Maaly Raw)"}}, {"id": "2334a54ace02992238e02de68e97c879", "title": "No Limit", "artists": "(feat. <PERSON>) (prod. Honorable C.N.O.T.E.)", "length": "3:13", "ogFilename": null, "notes": "Leaked in March of 2017.", "tags": [], "aliases": ["(<PERSON><PERSON>", "Juice)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d4db155108a36305dc490e7114b09a80/play", "key": "No Limit", "description": "Leaked in March of 2017.", "date": 14909184, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2334a54ace02992238e02de68e97c879", "size": "5.13 MB", "duration": 193.15, "originalContent": {"url": "https://music.froste.lol/song/d4db155108a36305dc490e7114b09a80/play", "artists": "(feat. <PERSON>) (prod. Honorable C.N.O.T.E.)"}}, {"id": "f78aec436acd94d4287d0280b24c9fea", "title": "Might Just F.O.Y.B.", "artists": "(prod. IZAÏAH)", "length": "3:17", "ogFilename": "MIGHT JUST FOYB MASTER", "notes": "Leaked in November of 2016.", "tags": ["OG File"], "aliases": ["(F.O.Y.B.)"], "type": "track", "originalUrl": "https://music.froste.lol/song/065e36d03fbec4cb4dd44de5517c7ea0/play", "key": "Might Just F.O.Y.B.", "description": "OG Filename: MIGHT JUST FOYB MASTER\nLeaked in November of 2016.", "date": 14804640, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f78aec436acd94d4287d0280b24c9fea", "size": "5.2 MB", "duration": 197.59, "originalContent": {"url": "https://music.froste.lol/song/065e36d03fbec4cb4dd44de5517c7ea0/play", "artists": "(prod. IZAÏAH)"}}, {"id": "generated_id_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_7_30", "title": "Lookin' [V1]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "Lookin 11.22.16", "notes": "Leaked on May 25. 2022. No Uzi feature. Has an extra <PERSON><PERSON> verse.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/cad0287e1f65574b0502512750338c7f/play", "key": "Lookin'", "description": "OG Filename: Lookin 11.22.16\nLeaked on May 25. 2022. No Uzi feature. Has an extra <PERSON><PERSON> verse.", "date": 16534368, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/cad0287e1f65574b0502512750338c7f/play", "artists": "(prod. <PERSON><PERSON><PERSON>)"}}, {"id": "d38baab0ee319fb2d10dd05bf224c2d9", "title": "At The Gate [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:45", "ogFilename": "<PERSON><PERSON> At The Gate edit C room 3 11 17-1-1", "notes": "OG Filename: <PERSON><PERSON> At The Gate edit C room A throwaway from the 'Self Titled' sessions.", "tags": ["OG File"], "aliases": ["(Buzz 'Em In", "Let 'Em In)"], "type": "track", "originalUrl": "https://music.froste.lol/song/5942ebe60dfdebc648ddb7cdf0493d6b/play", "key": "At The Gate", "description": "OG Filename: <PERSON><PERSON> At The Gate edit C room 3 11 17-1-1\nOG Filename: <PERSON><PERSON> At The Gate edit  C room\nA throwaway from the 'Self Titled' sessions.", "date": 15989184, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d38baab0ee319fb2d10dd05bf224c2d9", "size": "4.69 MB", "duration": 165.94, "originalContent": {"url": "https://music.froste.lol/song/5942ebe60dfdebc648ddb7cdf0493d6b/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7b11ea12d2b86ba2331b2d7cd494b60c", "title": "Snitches", "artists": "(prod. Southside)", "length": "2:01", "ogFilename": "carti sizzle snitches v1.5 (legend418)", "notes": "A throwaway from the 'Playboi Carti' sessions", "tags": ["OG File"], "aliases": ["(Pull Up With A Stick", "Legend", "<PERSON><PERSON>", "Kick)"], "type": "track", "originalUrl": "https://music.froste.lol/song/29f8abb3be2b5d077b49cd8ddcd5129e/play", "key": "Snitches", "description": "OG Filename: carti sizzle snitches v1.5 (legend418)\nA throwaway from the 'Playboi Carti' sessions", "date": 15230592, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/7b11ea12d2b86ba2331b2d7cd494b60c", "size": "3.99 MB", "duration": 121.7, "originalContent": {"url": "https://music.froste.lol/song/29f8abb3be2b5d077b49cd8ddcd5129e/play", "artists": "(prod. Southside)"}}, {"id": "86beabc1649c22298277f3bb9b333bbd", "title": "Foreign South America", "artists": "(prod. Chinatown)", "length": "2:44", "ogFilename": "foreign south america 9.24.16", "notes": "Leaked on September 16, 2020 and the beat was used on <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s \"Foreign\".", "tags": ["OG File"], "aliases": ["(Foreign)"], "type": "track", "originalUrl": "https://music.froste.lol/song/0c9d23fcc85f01eb92101d9eca48f9de/play", "key": "Foreign South America", "description": "OG Filename: foreign south america 9.24.16\nLeaked on September 16, 2020 and the beat was used on <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s \"Foreign\".", "date": 16002144, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/86beabc1649c22298277f3bb9b333bbd", "size": "4.68 MB", "duration": 164.81, "originalContent": {"url": "https://music.froste.lol/song/0c9d23fcc85f01eb92101d9eca48f9de/play", "artists": "(prod. Chinatown)"}}, {"id": "894c1617b8b6b8e3a08122769b457639", "title": "Damn Shame", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)", "length": "4:02", "ogFilename": null, "notes": "Features a different mix.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/2610f243b44c6a1e5b943253948f23fa/play", "key": "Damn Shame", "description": "Features a different mix.", "date": null, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/894c1617b8b6b8e3a08122769b457639", "size": "5.92 MB", "duration": 242.54, "originalContent": {"url": "https://music.froste.lol/song/2610f243b44c6a1e5b943253948f23fa/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "f2404dbca0083f388d46bfd2b32a7740", "title": "Check [V1]", "artists": "(prod. DP Beats)", "length": "3:13", "ogFilename": "Check 9.20.16", "notes": "A throwaway from the 'Playboi Carti' sessions.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/ff696cc47e54836a8b76aaac033ac9d6/play", "key": "Check", "description": "OG Filename: Check 9.20.16\nA throwaway from the 'Playboi <PERSON>ti' sessions.", "date": 15974496, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f2404dbca0083f388d46bfd2b32a7740", "size": "5.13 MB", "duration": 193.06, "originalContent": {"url": "https://music.froste.lol/song/ff696cc47e54836a8b76aaac033ac9d6/play", "artists": "(prod. DP Beats)"}}, {"id": "173602e9db8f701353b485d7dbf926d9", "title": "<PERSON><PERSON><PERSON> [V3]", "artists": "(feat. A$AP Ferg) (prod. Honorable C.N.O.T.E.)", "length": "2:31", "ogFilename": null, "notes": "The order of <PERSON><PERSON> & Ferg’s verses are swapped which could mean this was <PERSON><PERSON>'s song at first and <PERSON><PERSON>’s verse is almost entirely different from the final.", "tags": [], "aliases": ["(Bad Man", "<PERSON> Man)"], "type": "track", "originalUrl": "https://music.froste.lol/song/7131c28da13095d2e7d099ca73218f69/play", "key": "<PERSON><PERSON><PERSON>", "description": "The order of <PERSON><PERSON> & Ferg’s verses are swapped which could mean this was <PERSON><PERSON>'s song at first and <PERSON><PERSON>’s verse is almost entirely different from the final.", "date": 15128640, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/173602e9db8f701353b485d7dbf926d9", "size": "4.46 MB", "duration": 151.18, "originalContent": {"url": "https://music.froste.lol/song/7131c28da13095d2e7d099ca73218f69/play", "artists": "(feat. A$AP Ferg) (prod. Honorable C.N.O.T.E.)"}}]}, {"era": "TMB Collab", "tracks": [{"id": "generated_id_TMB_Collab_8_0", "title": "Dancer", "artists": "(prod. TrapMoneyB<PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "Snippet Leaked by Plankton on Sep 30, 2023 along with a information that Carti x TMB tape existed. Preivewed again by <PERSON><PERSON> on July 26, 2024. New CDQ snippet surfaced 3/14/2025 via RPC. Song leaked later that day after a succesful GB.", "tags": [], "aliases": [], "originalContent": {"artists": "(prod. TrapMoneyB<PERSON><PERSON>)"}}, {"id": "394f74ec0d20e7fb24461e2226d9e53a", "title": "Act A Foolie [V2]", "artists": "(prod. TrapMoneyB<PERSON><PERSON>)", "length": "2:51", "ogFilename": null, "notes": "Version without the tag", "tags": ["Lossless"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/bd03cbdcda862baf1c7465ab2f53d165/play", "key": "Act A Foolie", "description": "Version without the tag", "date": ********, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/394f74ec0d20e7fb24461e2226d9e53a", "size": "3.18 MB", "duration": 171.72, "originalContent": {"url": "https://music.froste.lol/song/bd03cbdcda862baf1c7465ab2f53d165/play", "artists": "(prod. TrapMoneyB<PERSON><PERSON>)"}}]}, {"era": "16*29 [V1]", "tracks": [{"id": "e61fa307208d5af58c5e6b29acf26a5d", "title": "Big Bank [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON> Bourne & Don Cannon)", "length": "2:22", "ogFilename": "<PERSON><PERSON> x <PERSON><PERSON> meggriff1.4.18", "notes": "A throwaway from the 16*29 sessions. Track was originally titled after the instrumental.", "tags": ["OG File"], "aliases": ["(<PERSON>')"], "type": "track", "originalUrl": "https://music.froste.lol/song/7c7ff8bb7c0168754f69e512b99276a4/play", "key": "Big Bank", "description": "OG Filename: <PERSON><PERSON> x <PERSON>zi meggriff1.4.18\nA throwaway from the 16*29 sessions. Track was originally titled after the instrumental.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/e61fa307208d5af58c5e6b29acf26a5d", "size": "2.74 MB", "duration": 142.2, "originalContent": {"url": "https://music.froste.lol/song/7c7ff8bb7c0168754f69e512b99276a4/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON> Bourne & Don Cannon)"}}, {"id": "cd235b6cc8c1ff2fe244073f8bf9e1dd", "title": "Home [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:46", "ogFilename": "Home w <PERSON>zi 1.3.18", "notes": "Original version of the Die Lit song \"Home (KOD)\" with an Uzi feature that was scrapped in the end.", "tags": ["OG File"], "aliases": ["(Home (KOD)", "King Of Diamonds)"], "type": "track", "originalUrl": "https://music.froste.lol/song/caee804917239e758ff2b7fe3dc379e8/play", "key": "Home", "description": "OG Filename: Home w Uzi 1.3.18\nOriginal version of the Die Lit song \"Home (KOD)\" with an Uzi feature that was scrapped in the end.", "date": 16701984, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/cd235b6cc8c1ff2fe244073f8bf9e1dd", "size": "4.09 MB", "duration": 226.46, "originalContent": {"url": "https://music.froste.lol/song/caee804917239e758ff2b7fe3dc379e8/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "04fa2235ad1ea15466a112fbbc9511d5", "title": "Candy [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:37", "ogFilename": "<PERSON><PERSON> x <PERSON><PERSON> 1.4.18", "notes": "OG Filename (Metadata): <PERSON><PERSON> A throwaway from the 16*29 sessions. This version has a carti feature. The beat was titled \"X Candy\".", "tags": ["OG File"], "aliases": ["(X Candy", "Like I'm Swoosh)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d676d65e1fc76db249d68e0c0bc1e651/play", "key": "<PERSON>", "description": "OG Filename: Carti x Uzi Candy 1.4.18\nOG Filename (Metadata): Carti Uzi X Candy\nA throwaway from the 16*29 sessions. This version has a carti feature. The beat was titled \"X Candy\".", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/04fa2235ad1ea15466a112fbbc9511d5", "size": "2.98 MB", "duration": 157.63, "originalContent": {"url": "https://music.froste.lol/song/d676d65e1fc76db249d68e0c0bc1e651/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "b3cf10e4a827dd227b9fe531f33a9f7c", "title": "My Division*", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "1:12", "ogFilename": "<PERSON><PERSON> x <PERSON><PERSON> nye5 1.4.18", "notes": "An untitled throwaway from the 16*29 sessions. <PERSON><PERSON><PERSON> was meant to record a verse for it.", "tags": ["OG File"], "aliases": ["(No Mercy)"], "type": "track", "originalUrl": "https://music.froste.lol/song/195ec714a9e8b7ae8252761ac14da3ec/play", "key": "My Division*", "description": "OG Filename: <PERSON><PERSON> x <PERSON>zi nye5 1.4.18\nAn untitled throwaway from the 16*29 sessions. <PERSON><PERSON><PERSON> was meant to record a verse for it.", "date": 16263936, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b3cf10e4a827dd227b9fe531f33a9f7c", "size": "1.62 MB", "duration": 72.1, "originalContent": {"url": "https://music.froste.lol/song/195ec714a9e8b7ae8252761ac14da3ec/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}]}, {"era": "Die Lit", "tracks": [{"id": "bac16fda13b40ab35f6a3def7ad69e35", "title": "3 Times", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "1:41", "ogFilename": "Carti - 3 Times", "notes": "OG Filename (Metadata): Carti - 456 Previewed in @countingcasket's Instagram Live.", "tags": ["OG File"], "aliases": ["(2*9", "Drop Top)"], "type": "track", "originalUrl": "https://music.froste.lol/song/73b6ebe8dc4acddf5e343b0f9f17a431/play", "key": "3 Times", "description": "OG Filename: Carti - 3 Times\nOG Filename (Metadata): <PERSON>ti - 456\nPreviewed in @countingcasket's Instagram Live.", "date": 16534368, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/bac16fda13b40ab35f6a3def7ad69e35", "size": "2.59 MB", "duration": 101.52, "originalContent": {"url": "https://music.froste.lol/song/73b6ebe8dc4acddf5e343b0f9f17a431/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "c9b69f82f06d451e8c71bbc9ee30af57", "title": "<PERSON>", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:47", "ogFilename": "<PERSON> 6.6.17", "notes": "A throwaway from the 'Die Lit' sessions. Leaked after a successful GB.", "tags": ["OG File"], "aliases": ["(Dress", "Swear To <PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e6264519d5df0d64e380142cd3415407/play", "key": "<PERSON>", "description": "OG Filename: <PERSON> 6.6.17\nA throwaway from the 'Die Lit' sessions. Leaked after a successful GB.", "date": 15666912, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/c9b69f82f06d451e8c71bbc9ee30af57", "size": "4.6 MB", "duration": 227.02, "originalContent": {"url": "https://music.froste.lol/song/e6264519d5df0d64e380142cd3415407/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "bd25e47992be03e343143af1be9da7d1", "title": "Arm and Leg [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:52", "ogFilename": "<PERSON><PERSON><PERSON>- Arm and Leg(Final)", "notes": "Leaked on May 1, 2018. Unrelated to the Whole Lotta Red song of the same name.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/ce3e93772cc90ee86dcd07556d27936f/play", "key": "Arm and Leg", "description": "OG Filename: <PERSON><PERSON><PERSON> and <PERSON><PERSON>(Final)\nLeaked on May 1, 2018. Unrelated to the Whole Lotta Red song of the same name.", "date": 15252192, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/bd25e47992be03e343143af1be9da7d1", "size": "4.69 MB", "duration": 232.44, "originalContent": {"url": "https://music.froste.lol/song/ce3e93772cc90ee86dcd07556d27936f/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "90899bfcec16f33b065f40025bd1b60a", "title": "Cake [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "4:22", "ogFilename": "Cake 11.21.17", "notes": "A throwaway from the 'Die Lit' sessions. Recorded on November 21, 2017.", "tags": ["OG File"], "aliases": ["(Can't Relate", "No Relation", "Relate)"], "type": "track", "originalUrl": "https://music.froste.lol/song/93862e1a4c3b062624c821a75353f8c1/play", "key": "Cake", "description": "OG Filename: <PERSON>ake 11.21.17\nA throwaway from the 'Die Lit' sessions. Recorded on November 21, 2017.", "date": 15709248, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/90899bfcec16f33b065f40025bd1b60a", "size": "5.16 MB", "duration": 262.08, "originalContent": {"url": "https://music.froste.lol/song/93862e1a4c3b062624c821a75353f8c1/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "baf4ea6ce22261afea5c76fbd7aa12d9", "title": "Cash Shit", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:31", "ogFilename": "<PERSON><PERSON>", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(Cashin')"], "type": "track", "originalUrl": "https://music.froste.lol/song/6e0db02016a97eae7113cf18490dceb8/play", "key": "Cash Shit", "description": "OG Filename: <PERSON><PERSON>\nA throwaway from the 'Die Lit' sessions.", "date": 15255648, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/baf4ea6ce22261afea5c76fbd7aa12d9", "size": "3.4 MB", "duration": 151.75, "originalContent": {"url": "https://music.froste.lol/song/6e0db02016a97eae7113cf18490dceb8/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "3ea5d0b03ae2c9b788e75cb4be3593d3", "title": "Check Please [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON> & Play<PERSON>i <PERSON>)", "length": "3:37", "ogFilename": "<PERSON><PERSON><PERSON> - Check Please (W: INTRO)", "notes": "OG Filename (Metadata): Check Please w intro A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(They Go Off", "<PERSON><PERSON>", "<PERSON>", "Kick Door)"], "type": "track", "originalUrl": "https://music.froste.lol/song/5867e94ec12005cc8279bb59ac89bcf6/play", "key": "Check Please", "description": "OG Filename: <PERSON><PERSON><PERSON> - Check Please (W: INTRO)\nOG Filename (Metadata): Check Please w intro\nA throwaway from the 'Die Lit' sessions.", "date": 15251328, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/3ea5d0b03ae2c9b788e75cb4be3593d3", "size": "4.46 MB", "duration": 217.97, "originalContent": {"url": "https://music.froste.lol/song/5867e94ec12005cc8279bb59ac89bcf6/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON> & Play<PERSON>i <PERSON>)"}}, {"id": "028dafcb98fc0bda9963cc7be0ec58d7", "title": "Different Lifestyle [V4]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:47", "ogFilename": "Fell in Luv mix v6", "notes": "Leaked on December 29, 2018. Seemingly retitled to \"Different Lifestyle\".", "tags": ["OG File"], "aliases": ["(Fell In Luv", "Rockstar", "Rockstar", "Privacy", "Different Lifestyle)"], "type": "track", "originalUrl": "https://music.froste.lol/song/1164a5f38b07a62ea88c3544bdddb82b/play", "key": "Different Lifestyle", "description": "OG Filename: Fell in Luv mix v6\nLeaked on December 29, 2018. Seemingly retitled to \"Different Lifestyle\".", "date": 15460416, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/028dafcb98fc0bda9963cc7be0ec58d7", "size": "3.66 MB", "duration": 167.93, "originalContent": {"url": "https://music.froste.lol/song/1164a5f38b07a62ea88c3544bdddb82b/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7d830d605d94e67120fd841cf6c428da", "title": "Dog Food [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:09", "ogFilename": "Dog Food DC Main Mix-V3", "notes": "Version of Dog Food with different mix. Leaked on October 14, 2018.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/b8599e215bb93f45e302af0e21407644/play", "key": "Dog Food", "description": "OG Filename: Dog Food DC Main Mix-V3\nVersion of Dog Food with different mix. Leaked on October 14, 2018.", "date": 15394752, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/7d830d605d94e67120fd841cf6c428da", "size": "3.04 MB", "duration": 129.67, "originalContent": {"url": "https://music.froste.lol/song/b8599e215bb93f45e302af0e21407644/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "8b644d0f5b1e42f9a625049f69ecbe7b", "title": "I Got", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "4:29", "ogFilename": null, "notes": "OG File: <PERSON><PERSON><PERSON>- I Got A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(Tattoo)"], "type": "track", "originalUrl": "https://music.froste.lol/song/71abfac035f66c0222d57f0810c04b2d/play", "key": "I Got", "description": "OG File: <PERSON><PERSON><PERSON>- I Got\nA throwaway from the 'Die Lit' sessions.", "date": 15665184, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/8b644d0f5b1e42f9a625049f69ecbe7b", "size": "5.28 MB", "duration": 269.64, "originalContent": {"url": "https://music.froste.lol/song/71abfac035f66c0222d57f0810c04b2d/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "d867820aad61608d17c3f93d94d50aff", "title": "Lobby [V1]", "artists": "(prod. <PERSON> Boomin)", "length": "3:23", "ogFilename": "Carti Metro open 2nd", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(I Walk In)"], "type": "track", "originalUrl": "https://music.froste.lol/song/0b7af71e26250394c50971e2e2862bbc/play", "key": "Lobby", "description": "OG Filename: Carti Metro open 2nd\nA throwaway from the 'Die Lit' sessions.", "date": null, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d867820aad61608d17c3f93d94d50aff", "size": "4.23 MB", "duration": 203.74, "originalContent": {"url": "https://music.froste.lol/song/0b7af71e26250394c50971e2e2862bbc/play", "artists": "(prod. <PERSON> Boomin)"}}, {"id": "6ae7ca54a8d8928b4c3a92cf0bbe1d94", "title": "Essentials", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:33", "ogFilename": "Essentials Rough", "notes": "Previewed in @countingcasket's Instagram Live. File with the OG quality and without Pi'erre tag surfaced on Oct 12, 2022. OGF leaked on Dec 25, 2022.", "tags": ["OG File"], "aliases": ["(Canary Diamonds", "Wedding Ring)"], "type": "track", "originalUrl": "https://music.froste.lol/song/498aa972bc759f46bd4ba10f7ef379f7/play", "key": "Essentials", "description": "OG Filename: Essentials Rough\nPreviewed in @countingcasket's Instagram Live. File with the OG quality and without Pi'erre tag surfaced on Oct 12, 2022. OGF leaked on Dec 25, 2022.", "date": 16491168, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6ae7ca54a8d8928b4c3a92cf0bbe1d94", "size": "3.43 MB", "duration": 153.89, "originalContent": {"url": "https://music.froste.lol/song/498aa972bc759f46bd4ba10f7ef379f7/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7756318bbe53eb6331eed88d55b9f408", "title": "Fall In Love", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:33", "ogFilename": "CARTi-FALL IN LOVE ( PB mae3 beat ) <PERSON><PERSON> <PERSON><PERSON> b", "notes": "OG Filename (Metadata): CARTi-pierre track ( mae3 beat A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(Seeing Colors", "Movin' Different)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d7088e83e41eae9fe5e6d5a1c222cdac/play", "key": "Fall In Love", "description": "OG Filename: CART<PERSON>-FALL IN LOVE ( PB mae3 beat ) <PERSON><PERSON><PERSON> b\nOG Filename (Metadata): CARTi-pierre track ( mae3 beat\nA throwaway from the 'Die Lit' sessions.", "date": 15189984, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7756318bbe53eb6331eed88d55b9f408", "size": "3.42 MB", "duration": 153.22, "originalContent": {"url": "https://music.froste.lol/song/d7088e83e41eae9fe5e6d5a1c222cdac/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "ecfdd645bf23780f5d98318ca1c63269", "title": "Fuck School", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:29", "ogFilename": "<PERSON><PERSON> 444_1", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(Mad", "Rules", "Highschool)"], "type": "track", "originalUrl": "https://music.froste.lol/song/040e9db2c482e3a62d3f587e88104483/play", "key": "Fuck School", "description": "OG Filename: <PERSON><PERSON> 444_1\nA throwaway from the 'Die Lit' sessions.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ecfdd645bf23780f5d98318ca1c63269", "size": "4.32 MB", "duration": 209.64, "originalContent": {"url": "https://music.froste.lol/song/040e9db2c482e3a62d3f587e88104483/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "692549b0bc79c00279c9ed51b9a2d587", "title": "Luv With The Geek [V4]", "artists": "(prod. TM88 & Southside)", "length": "3:45", "ogFilename": "Play<PERSON><PERSON> - Luv With The Geek V4", "notes": "Leaked on June 6, 2019.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>", "<PERSON><PERSON> In Luv)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e3ac7b449a95140f168de25016ac2f37/play", "key": "Luv With The Geek", "description": "OG Filename: <PERSON><PERSON><PERSON> With The Geek V4\nLeaked on June 6, 2019.", "date": 15597792, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/692549b0bc79c00279c9ed51b9a2d587", "size": "4.58 MB", "duration": 225.94, "originalContent": {"url": "https://music.froste.lol/song/e3ac7b449a95140f168de25016ac2f37/play", "artists": "(prod. TM88 & Southside)"}}, {"id": "generated_id_Die_Lit_10_14", "title": "<PERSON><PERSON><PERSON><PERSON> - Bands Up [V3]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "<PERSON><PERSON><PERSON><PERSON>'s version of Bands Up. Was originally meant to be on The Life Of Pi'erre 5 but was left off for unknown reasons.", "tags": [], "aliases": [], "originalContent": {"artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "d3c5dd413958f75c1870f9af55281166", "title": "<PERSON><PERSON>", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:12", "ogFilename": "Killa 11.19.17", "notes": "OG File Metadata: <PERSON><PERSON> RUFF A throwaway from the 'Die Lit' sessions. Leaked on June 6, 2019, with an open verse.", "tags": ["OG File"], "aliases": ["(Dropped Out", "Graduation Party)"], "type": "track", "originalUrl": "https://music.froste.lol/song/be58ccdc9f615efbcb0f497aa343bfd8/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON> 11.19.17\nOG File Metadata: <PERSON><PERSON> RUFF\nA throwaway from the 'Die Lit' sessions. Leaked on June 6, 2019, with an open verse.", "date": 15597792, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d3c5dd413958f75c1870f9af55281166", "size": "4.05 MB", "duration": 192.41, "originalContent": {"url": "https://music.froste.lol/song/be58ccdc9f615efbcb0f497aa343bfd8/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "2d3c9e6ffe84d1e811571d815ea089ab", "title": "No Lie", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:01", "ogFilename": "NO LIE", "notes": "Leaked on January 3, 2019.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/e2b097aa98d17b67ea74abffa970f5d3/play", "key": "No Lie", "description": "OG Filename: NO LIE\nLeaked on January 3, 2019.", "date": 15464736, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2d3c9e6ffe84d1e811571d815ea089ab", "size": "3.87 MB", "duration": 181.22, "originalContent": {"url": "https://music.froste.lol/song/e2b097aa98d17b67ea74abffa970f5d3/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "bedf3dd9689dd3284f156354bb1f2bf4", "title": "Mine", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:57", "ogFilename": "Playboi Carti- Mine", "notes": "Leaked on January 1, 2019.", "tags": ["OG File"], "aliases": ["(A Lot On My Mind)"], "type": "track", "originalUrl": "https://music.froste.lol/song/eddf61b9904587a8389c4b695d76e4e3/play", "key": "Mine", "description": "OG Filename: <PERSON><PERSON><PERSON>- Mine\nLeaked on January 1, 2019.", "date": 15463008, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/bedf3dd9689dd3284f156354bb1f2bf4", "size": "3.81 MB", "duration": 177.34, "originalContent": {"url": "https://music.froste.lol/song/eddf61b9904587a8389c4b695d76e4e3/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "a409b0cf319448cf3812b5e90c7874db", "title": "Supersonic [V2]", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:59", "ogFilename": "<PERSON><PERSON> x sah", "notes": "OG Filename (Metadata): CARTISAHB Later version of the song with a SahBabii feature that leaked on Dec 29, 2019.", "tags": ["OG File"], "aliases": ["(He Ain't Got It", "Sonic The Hedgehog)"], "type": "track", "originalUrl": "https://music.froste.lol/song/7b958d062c05c3fb4176273d4edd94c8/play", "key": "Supersonic", "description": "OG Filename: <PERSON><PERSON> x sah\nOG Filename (Metadata): CARTISAHB\nLater version of the song with a SahBabii feature that leaked on Dec 29, 2019.", "date": 15775776, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/a409b0cf319448cf3812b5e90c7874db", "size": "3.84 MB", "duration": 179.47, "originalContent": {"url": "https://music.froste.lol/song/7b958d062c05c3fb4176273d4edd94c8/play", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "473e2a5d6623af3d889216371fbbde7e", "title": "2038*", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:22", "ogFilename": "<PERSON><PERSON> x <PERSON><PERSON> nye7 1.3.18 Paramount", "notes": "A throwaway from the Die Lit/16*29 sessions. <PERSON> was considered to be on Die Lit under the name \"2038\". Seemingly untitled. Was recorded during the January 16*29 sessions.", "tags": ["OG File"], "aliases": ["(Throw It Up", "16*29", "16", "29)"], "type": "track", "originalUrl": "https://music.froste.lol/song/fc1868a63856d252eee99fbee8f340bc/play", "key": "2038*", "description": "OG Filename: <PERSON><PERSON> x Uzi nye7 1.3.18 Paramount\nA throwaway from the Die Lit/16*29 sessions. <PERSON> was considered to be on Die Lit under the name \"2038\". Seemingly untitled. Was recorded during the January 16*29 sessions.", "date": 15446592, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/473e2a5d6623af3d889216371fbbde7e", "size": "2.74 MB", "duration": 142.37, "originalContent": {"url": "https://music.froste.lol/song/fc1868a63856d252eee99fbee8f340bc/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "815dcb5b9ec3d54687dd216afd0b3a80", "title": "Texas [V1]", "artists": "(prod. Jake One & Southside)", "length": "3:26", "ogFilename": "Texas 12.4.17", "notes": "Leaked on March 5, 2021. Beat was later used on <PERSON><PERSON> Mane's \"Southside and Guwop (Outro)\". Has an open verse for <PERSON><PERSON>.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/9a8ceb79eaf07432a4fc38acd8b9a0a1/play", "key": "Texas", "description": "OG Filename: Texas 12.4.17\nLeaked on March 5, 2021. Beat was later used on <PERSON><PERSON>'s \"Southside and Guwop (Outro)\". Has an open verse for <PERSON><PERSON>.", "date": 16149024, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/815dcb5b9ec3d54687dd216afd0b3a80", "size": "4.27 MB", "duration": 206.5, "originalContent": {"url": "https://music.froste.lol/song/9a8ceb79eaf07432a4fc38acd8b9a0a1/play", "artists": "(prod. Jake One & Southside)"}}, {"id": "69bff7fc430da75f75b11073389b203f", "title": "Toke <PERSON> [V5]", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:27", "ogFilename": "Toke Shit x FATOL RUFF MIX 04", "notes": "Seemingly an even later version of \"Toke\" with a slightly different structure, improved mix and vocal effects on <PERSON>'s verse.", "tags": ["OG File"], "aliases": ["(Bust Down", "Toke)"], "type": "track", "originalUrl": "https://music.froste.lol/song/687e8428fd2bf3cccc9bee39e5ef66b4/play", "key": "Toke <PERSON>", "description": "OG Filename: Toke Shit x FATOL RUFF MIX 04\nSeemingly an even later version of \"Toke\" with a slightly different structure, improved mix and vocal effects on <PERSON>'s verse.", "date": 16736544, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/69bff7fc430da75f75b11073389b203f", "size": "4.29 MB", "duration": 207.34, "originalContent": {"url": "https://music.froste.lol/song/687e8428fd2bf3cccc9bee39e5ef66b4/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "a7aab1ebc0aceee9a25731cd90760144", "title": "High School", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:55", "ogFilename": "CARTI 222_01.30.18", "notes": "OG File Metadata: <PERSON><PERSON>school Uses the same beat as <PERSON>'s \"New York in June\". Leaked on July 23, 2022.", "tags": ["OG File"], "aliases": ["(Way Back", "New York In June", "Way Back In Highschool", "222)"], "type": "track", "originalUrl": "https://music.froste.lol/song/53159b30d76cdd9559505271ef7bdc48/play", "key": "High School", "description": "OG Filename: CARTI 222_01.30.18\nOG File Metadata: <PERSON><PERSON> Highschool\nUses the same beat as <PERSON>'s \"New York in June\". Leaked on July 23, 2022.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/a7aab1ebc0aceee9a25731cd90760144", "size": "3.78 MB", "duration": 175.63, "originalContent": {"url": "https://music.froste.lol/song/53159b30d76cdd9559505271ef7bdc48/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7dcece2dcc773a9ed4a979052c61d3f4", "title": "Foreign [V2]", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)", "length": "3:00", "ogFilename": "Foreign 1.19.18 excerpt", "notes": "Leaked May 10, 2020. Features <PERSON><PERSON>er<PERSON> and a small beatswitch.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/f051ea91288401717f6d197bbd8243ce", "key": "Foreign", "description": "OG Filename: Foreign 1.19.18 excerpt\nLeaked May 10, 2020. Features <PERSON><PERSON>er<PERSON> and a small beatswitch.", "date": 15890688, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7dcece2dcc773a9ed4a979052c61d3f4", "size": "3.85 MB", "duration": 180.11, "originalContent": {"url": "https://pillowcase.su/f/f051ea91288401717f6d197bbd8243ce", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)"}}, {"id": "fe4ac7f0286996327b99bc4ec7f55457", "title": "Young Thug - Up [V1]", "artists": "(feat. <PERSON> & <PERSON><PERSON><PERSON>) (prod. Southside)", "length": "5:27", "ogFilename": null, "notes": "Original version of \"Up\" featuring a <PERSON><PERSON><PERSON> verse. First previewed by <PERSON> Thug on October 25, 2017.", "tags": ["OG File"], "aliases": ["(Let's Go Up)"], "type": "track", "originalUrl": "https://music.froste.lol/song/320c6d7c4f638f3f81e1ecafd7d17214/play", "key": "Up", "description": "Original version of \"Up\" featuring a <PERSON><PERSON><PERSON> verse. First previewed by <PERSON> Thug on October 25, 2017.", "date": 15685920, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/fe4ac7f0286996327b99bc4ec7f55457", "size": "5.71 MB", "duration": 327.79, "originalContent": {"url": "https://music.froste.lol/song/320c6d7c4f638f3f81e1ecafd7d17214/play", "artists": "(feat. <PERSON> & <PERSON><PERSON><PERSON>) (prod. Southside)"}}, {"id": "b84943ff1d74300c7171275d9594a7b2", "title": "<PERSON> - <PERSON>a [V1]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Maaly <PERSON>)", "length": "2:55", "ogFilename": "<PERSON><PERSON>", "notes": "Original version of the Die Lit song \"Shoot<PERSON>\" with two Uzi verses. Was originally an Uzi song before being given to <PERSON><PERSON>. OGF leaked on Aug 20, 2023.", "tags": ["OG File"], "aliases": ["(Rocket)"], "type": "track", "originalUrl": "https://music.froste.lol/song/78447b9eac84e8ad8a09e87fee67584f/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON>\nOriginal version of the Die Lit song \"Shoota\" with two Uzi verses. Was originally an Uzi song before being given to <PERSON><PERSON>. OGF leaked on Aug 20, 2023.", "date": 15252192, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b84943ff1d74300c7171275d9594a7b2", "size": "3.27 MB", "duration": 175.78, "originalContent": {"url": "https://music.froste.lol/song/78447b9eac84e8ad8a09e87fee67584f/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Maaly <PERSON>)"}}, {"id": "a921581f9e0ee7e8fe904dcb2c1f573e", "title": "Cardi B - <PERSON><PERSON>", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc & Cheeze Beatz)", "length": "1:50", "ogFilename": "Carti x Cardi VERSE 12.8.17", "notes": "Original version of \"Bartier Cardi\" that features <PERSON><PERSON><PERSON> instead of 21 <PERSON>.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/bdf5df4ed342d24caeef51bf8ead20f3/play", "key": "<PERSON><PERSON>", "description": "OG Filename: Carti x Cardi VERSE 12.8.17\nOriginal version of \"Bartier Cardi\" that features <PERSON><PERSON><PERSON>ti instead of 21 Savage.", "date": 15793920, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/a921581f9e0ee7e8fe904dcb2c1f573e", "size": "2.73 MB", "duration": 110.09, "originalContent": {"url": "https://music.froste.lol/song/bdf5df4ed342d24caeef51bf8ead20f3/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. 30 Roc & Cheeze Beatz)"}}, {"id": "4156fe585c4f96ba7295c44d0165d740", "title": "<PERSON> [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "5:21", "ogFilename": "carticompleted", "notes": "Leaked on October 23, 2019", "tags": ["OG File"], "aliases": ["(<PERSON>ze", "Choppa Right Beneath Me)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e54a1686d0e6c804a5270581f7c4aa93/play", "key": "<PERSON><PERSON>", "description": "OG Filename: carticompleted\nLeaked on October 23, 2019", "date": 15717888, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4156fe585c4f96ba7295c44d0165d740", "size": "6.11 MB", "duration": 321.14, "originalContent": {"url": "https://music.froste.lol/song/e54a1686d0e6c804a5270581f7c4aa93/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "49a670fb099b621d41360c261a7c2de7", "title": "Way Too Geeked", "artists": "(prod. <PERSON> & Lil 88)", "length": "2:22", "ogFilename": "Playboi Carti x Canon [Way Too Geeked] x AJ", "notes": "A throwaway from the 'Die Lit' sessions. Does NOT have <PERSON> Flores's SSN. Leaked on July 23, 2022.", "tags": ["OG File"], "aliases": ["(150117025)"], "type": "track", "originalUrl": "https://music.froste.lol/song/11b42879b1a313d93ee16d791430baf9/play", "key": "Way Too Geeked", "description": "OG Filename: <PERSON><PERSON><PERSON> x <PERSON> [Way Too Geeked] x AJ\nA throwaway from the 'Die Lit' sessions. Does NOT have <PERSON> Flores's SSN. Leaked on July 23, 2022.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/49a670fb099b621d41360c261a7c2de7", "size": "3.25 MB", "duration": 142.56, "originalContent": {"url": "https://music.froste.lol/song/11b42879b1a313d93ee16d791430baf9/play", "artists": "(prod. <PERSON> & Lil 88) "}}, {"id": "8f2b38ed02d808967d2f177848afdef4", "title": "VLone Jacket", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:17", "ogFilename": "<PERSON><PERSON> Jack<PERSON>", "notes": "Edited version leaked on September 23, 2020 and the full version leaked March 20, 2021.", "tags": ["OG File"], "aliases": ["(Guns Out)"], "type": "track", "originalUrl": "https://music.froste.lol/song/04e139cdc861058ac203fb4c04eeefe4/play", "key": "VLone Jacket", "description": "OG Filename: <PERSON><PERSON> Jacket\nEdited version leaked on September 23, 2020 and the full version leaked March 20, 2021.", "date": 16161984, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/8f2b38ed02d808967d2f177848afdef4", "size": "4.12 MB", "duration": 197.28, "originalContent": {"url": "https://music.froste.lol/song/04e139cdc861058ac203fb4c04eeefe4/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "14852851f9ca911f3174dd56bf8727eb", "title": "Uber", "artists": "(prod. Southside)", "length": "3:40", "ogFilename": "01 Uber", "notes": "<PERSON><PERSON><PERSON><PERSON> from Die lit sessions.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/a9c77c4734b6347b18860d296c514914/play", "key": "Uber", "description": "OG Filename: 01 Uber\nThrowaway from Die lit sessions.", "date": 16638912, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/14852851f9ca911f3174dd56bf8727eb", "size": "4.49 MB", "duration": 220.22, "originalContent": {"url": "https://music.froste.lol/song/a9c77c4734b6347b18860d296c514914/play", "artists": "(prod. Southside)"}}, {"id": "ff9022c73eea1a16ce138ee8183cf41b", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:45", "ogFilename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 12.6.17", "notes": "Has the same hook and beat as <PERSON><PERSON><PERSON><PERSON> but has completely different verses and no <PERSON> Nudy feature with alternate production.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON><PERSON><PERSON><PERSON>", "Notice Me)"], "type": "track", "originalUrl": "https://music.froste.lol/song/bf9791cd970fabafc318f28ee4d01b32/play", "key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 12.6.17\nHas the same hook and beat as <PERSON><PERSON><PERSON><PERSON><PERSON> but has completely different verses and no <PERSON> Nudy feature with alternate production.", "date": 16148160, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ff9022c73eea1a16ce138ee8183cf41b", "size": "3.61 MB", "duration": 165.31, "originalContent": {"url": "https://music.froste.lol/song/bf9791cd970fabafc318f28ee4d01b32/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "066e9b8c9326798172bb2666737f7e78", "title": "Celine", "artists": "(prod. Southside)", "length": "3:54", "ogFilename": null, "notes": "A throwaway from the 'Die Lit' sessions. Unrelated to the Whole Lotta Red song of the same name.", "tags": [], "aliases": ["(<PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/51108b019d951be87888c1c2b0ea9595/play", "key": "Celine", "description": "A throwaway from the 'Die Lit' sessions. Unrelated to the Whole Lotta Red song of the same name.", "date": 15973632, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/066e9b8c9326798172bb2666737f7e78", "size": "4.72 MB", "duration": 234.62, "originalContent": {"url": "https://music.froste.lol/song/51108b019d951be87888c1c2b0ea9595/play", "artists": "(prod. Southside)"}}, {"id": "generated_id_Die_Lit_10_33", "title": "<PERSON>", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "<PERSON>ti kev6 Paramount 1.2.18 idea", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/e5d7e72fa3ee3b23e160e1ec1d2b478a", "key": "<PERSON>", "description": "OG Filename: <PERSON><PERSON> kev6 Paramount 1.2.18 idea \nA throwaway from the 'Die Lit' sessions.", "date": 16932672, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/e5d7e72fa3ee3b23e160e1ec1d2b478a", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "bbed012c6213098aff6ec39fcdb04742", "title": "Gon Smash", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "1:56", "ogFilename": null, "notes": "A throwaway from the 'Die Lit' sessions. Not from 2019 as advertised by the seller and allegedly from the same week as \"Uh Uh\" and \"Mileage\" in January. GB for 5k on Apr 3, 2024. Recorded at Paramount", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/494cdbdc886950476cc8972013cb7666/play", "key": "Gon Smash", "description": "A throwaway from the 'Die Lit' sessions. Not from 2019 as advertised by the seller and allegedly from the same week as \"Uh Uh\" and \"Mileage\" in January. GB for 5k on Apr 3, 2024. Recorded at Paramount", "date": 17121024, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/bbed012c6213098aff6ec39fcdb04742", "size": "2.82 MB", "duration": 116.02, "originalContent": {"url": "https://music.froste.lol/song/494cdbdc886950476cc8972013cb7666/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7b0a5fa4ed54e735d3031319bb6bc8e3", "title": "Drip [V5]", "artists": "(prod. IkeBeatz & EVIL)", "length": "3:27", "ogFilename": "Drip 12.30.17", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(With The Reds", "<PERSON><PERSON>e", "Red & Blue", "Red N Blue)"], "type": "track", "originalUrl": "https://music.froste.lol/song/242951e8c35d4c1116592d98b6c1730e/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON> 12.30.17\nA throwaway from the 'Die Lit' sessions.", "date": 15332544, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7b0a5fa4ed54e735d3031319bb6bc8e3", "size": "4.28 MB", "duration": 207.17, "originalContent": {"url": "https://music.froste.lol/song/242951e8c35d4c1116592d98b6c1730e/play", "artists": "(prod. IkeBeatz & EVIL)"}}, {"id": "f0da7d81e0963115fcb7b30788bdebef", "title": "Lit", "artists": "(feat. <PERSON><PERSON> & <PERSON><PERSON>) (prod. CuB<PERSON>z & <PERSON><PERSON>erre <PERSON>)", "length": "3:20", "ogFilename": "Lit 2.12.18", "notes": "Leaked on May 25, 2022.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/687c5a37bb9269c88e5acdcac0462cca/play", "key": "Lit", "description": "OG Filename: Lit 2.12.18\nLeaked on May 25, 2022.", "date": 16534368, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f0da7d81e0963115fcb7b30788bdebef", "size": "4.17 MB", "duration": 200.11, "originalContent": {"url": "https://music.froste.lol/song/687c5a37bb9269c88e5acdcac0462cca/play", "artists": "(feat. <PERSON><PERSON> & <PERSON><PERSON>) (prod. CuB<PERSON>z & <PERSON><PERSON>erre <PERSON>)"}}, {"id": "5124c835d8a7396faac2c9ce491847c1", "title": "Like Me", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "0:57", "ogFilename": "LIKE ME", "notes": "Uses the same beat as <PERSON>'s song \"Rude\". Leaked on July 23, 2022.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>", "Meanstreet)"], "type": "track", "originalUrl": "https://music.froste.lol/song/919548322049339cc1597cdd05b48321/play", "key": "Like Me", "description": "OG Filename: <PERSON><PERSON><PERSON> ME\nUses the same beat as <PERSON>'s song \"Rude\". Leaked on July 23, 2022.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5124c835d8a7396faac2c9ce491847c1", "size": "1.89 MB", "duration": 57.31, "originalContent": {"url": "https://music.froste.lol/song/919548322049339cc1597cdd05b48321/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "c7e200d107423577ec9b37490eb16a50", "title": "All Of These*", "artists": "(prod. DY Krazy)", "length": "1:34", "ogFilename": "Carti Dy 2", "notes": "Leaked on July 23, 2022.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>", "Hurt)"], "type": "track", "originalUrl": "https://music.froste.lol/song/25612eef3e1c997f8e92787b64c49018/play", "key": "All Of These*", "description": "OG Filename: <PERSON><PERSON> 2\nLeaked on July 23, 2022.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/c7e200d107423577ec9b37490eb16a50", "size": "2.48 MB", "duration": 94.63, "originalContent": {"url": "https://music.froste.lol/song/25612eef3e1c997f8e92787b64c49018/play", "artists": "(prod. DY Krazy)"}}, {"id": "7ba8ae0d9e8048d982b892c8b6b6d48b", "title": "Jole5", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:13", "ogFilename": "Carti - Jole5", "notes": "Leaked in early 2018. Title is unknown, beat name is <PERSON><PERSON><PERSON>.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>", "Unicorn)"], "type": "track", "originalUrl": "https://music.froste.lol/song/90df27a1317b2882cdb807a9d4bed916/play", "key": "Jole5", "description": "OG Filename: <PERSON><PERSON> - Jole5\nLeaked in early 2018. Title is unknown, beat name is <PERSON><PERSON><PERSON>.", "date": 15175296, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7ba8ae0d9e8048d982b892c8b6b6d48b", "size": "2.52 MB", "duration": 193.66, "originalContent": {"url": "https://music.froste.lol/song/90df27a1317b2882cdb807a9d4bed916/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "cacbde26b76dcc83daa5eca88b8043c7", "title": "Fuck My Ex", "artists": null, "length": "2:15", "ogFilename": "PBC Fuck My Ex", "notes": "OG Filename: She My Ex 12.1.17 A throwaway from the 'Die Lit' sessions. Has a Cut Open", "tags": [], "aliases": ["(<PERSON> My Ex)"], "type": "track", "originalUrl": "https://music.froste.lol/song/2f4af15446d2b0d3fe382b7e9f60005d/play", "key": "Fuck My Ex", "description": "OG Filename: <PERSON>BC Fuck My Ex\nOG Filename: She My Ex 12.1.17\nA throwaway from the 'Die Lit' sessions. Has a Cut Open", "date": 16585344, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/cacbde26b76dcc83daa5eca88b8043c7", "size": "3.14 MB", "duration": 135.96, "originalContent": {"url": "https://music.froste.lol/song/2f4af15446d2b0d3fe382b7e9f60005d/play", "artists": null}}, {"id": "9a9dcbf41a790125f18611cd8bd4f013", "title": "From Da Gutta", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:28", "ogFilename": "Nudy <PERSON> From Da Gutta", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON> Up)"], "type": "track", "originalUrl": "https://music.froste.lol/song/21535ba4c114fd649303f69b81f2e0cc/play", "key": "From Da Gutta", "description": "OG Filename: <PERSON><PERSON><PERSON> From Da Gutta\nA throwaway from the 'Die Lit' sessions.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/9a9dcbf41a790125f18611cd8bd4f013", "size": "4.3 MB", "duration": 208.44, "originalContent": {"url": "https://music.froste.lol/song/21535ba4c114fd649303f69b81f2e0cc/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "f822f15d4c306c80935340760012177b", "title": "Fashion Nova", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:34", "ogFilename": "Fashion Nova 11.30.17", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": ["(Don't <PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d9879a272b97112af49b7ed16cb7b4f5/play", "key": "Fashion Nova", "description": "OG Filename: Fashion Nova 11.30.17\nA throwaway from the 'Die Lit' sessions.", "date": 16008192, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f822f15d4c306c80935340760012177b", "size": "3.44 MB", "duration": 154.68, "originalContent": {"url": "https://music.froste.lol/song/d9879a272b97112af49b7ed16cb7b4f5/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>) "}}, {"id": "437d9232b55bb13b652cdfd36e0794ec", "title": "<PERSON><PERSON>", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:20", "ogFilename": "<PERSON><PERSON> 12.6.17", "notes": "A throwaway from the 'Die Lit' sessions. Recorded the same day as \"Right Now\".", "tags": ["OG File"], "aliases": ["(More Of Those)"], "type": "track", "originalUrl": "https://music.froste.lol/song/68cd381234b134e40939a31a97d04cb8/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON> 12.6.17\nA throwaway from the 'Die Lit' sessions. Recorded the same day as \"Right Now\".", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/437d9232b55bb13b652cdfd36e0794ec", "size": "3.22 MB", "duration": 140.9, "originalContent": {"url": "https://music.froste.lol/song/68cd381234b134e40939a31a97d04cb8/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "2fb22f25850ea111b7a41cde3b78b499", "title": "I Walk In [V2]", "artists": "(prod. <PERSON> Boomin)", "length": "3:27", "ogFilename": "Carti Metro I Walk In", "notes": "Leaked on August 25, 2020.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d773d61565fb473fa67db52ff44dabc7/play", "key": "I Walk In", "description": "OG Filename: Carti Metro I Walk In\nLeaked on August 25, 2020.", "date": 15983136, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2fb22f25850ea111b7a41cde3b78b499", "size": "4.29 MB", "duration": 207.86, "originalContent": {"url": "https://music.froste.lol/song/d773d61565fb473fa67db52ff44dabc7/play", "artists": "(prod. <PERSON> Boomin)"}}, {"id": "868ce8dcc7f97ae0bef472812de1ec6b", "title": "Goyard Shopping Bag", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:58", "ogFilename": "Goyard Shopping Bag 11.19.17", "notes": "OG File (Metadata): <PERSON><PERSON> Soss2 RUFF Uses the beat that would go on to be used for \"Watch\" by <PERSON>.", "tags": ["OG File"], "aliases": ["(Watch)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d74853db0a1a461da46a324d424d402b/play", "key": "Goyard Shopping Bag", "description": "OG Filename: Goyard Shopping Bag 11.19.17\nOG File (Metadata): <PERSON><PERSON> Soss2 RUFF\nUses the beat that would go on to be used for \"Watch\" by <PERSON>.", "date": 16736544, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/868ce8dcc7f97ae0bef472812de1ec6b", "size": "3.82 MB", "duration": 178.2, "originalContent": {"url": "https://music.froste.lol/song/d74853db0a1a461da46a324d424d402b/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>) "}}, {"id": "2bd41755852bcaae297cc7aee0c63cc2", "title": "Dog Food [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:06", "ogFilename": "Carti Pierre1 RUFF", "notes": "A throwaway from the 'Die Lit' sessions. Leaked on April 29, 2023, with the open chorus.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/e1d6ece1ea02dcf95b7fbfafcfe76e38/play", "key": "Dog Food", "description": "OG Filename: <PERSON><PERSON>1 RUFF\nA throwaway from the 'Die Lit' sessions. Leaked on April 29, 2023, with the open chorus.", "date": 16827264, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2bd41755852bcaae297cc7aee0c63cc2", "size": "3.95 MB", "duration": 186.14, "originalContent": {"url": "https://music.froste.lol/song/e1d6ece1ea02dcf95b7fbfafcfe76e38/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "0b8c5977c4856add24904d55011043af", "title": "<PERSON><PERSON> Won't Miss [V2]", "artists": "(feat. <PERSON> & <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:19", "ogFilename": "<PERSON><PERSON> Won't <PERSON> <PERSON><PERSON>", "notes": "Version featuring <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. It has been confirmed by <PERSON><PERSON><PERSON><PERSON> himself, that it's an official stem edit. <PERSON><PERSON>de the beat, <PERSON><PERSON> bought the Pi'erre verse, and the verse was edited in in this version, he just didn't end up using it in the final one. There's a music video for this version, which has been seen by trusted community members. A christmas gift to the comm from <PERSON><PERSON>.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/7df55d2608d4612c9414049c2462a155/play", "key": "<PERSON><PERSON> Won't Miss", "description": "OG Filename: <PERSON><PERSON> Won't <PERSON> <PERSON><PERSON> Thug Pierre\nVersion featuring <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. It has been confirmed by <PERSON><PERSON><PERSON><PERSON> himself, that it's an official stem edit. Alongisde the beat, <PERSON><PERSON> bought the Pi'erre verse, and the verse was edited in in this version, he just didn't end up using it in the final one. There's a music video for this version, which has been seen by trusted community members. A christmas gift to the comm from <PERSON><PERSON>.", "date": 17350848, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/0b8c5977c4856add24904d55011043af", "size": "4.16 MB", "duration": 199.32, "originalContent": {"url": "https://music.froste.lol/song/7df55d2608d4612c9414049c2462a155/play", "artists": "(feat. <PERSON> & <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "ea71f087e092a1aad920fa6edddc7dac", "title": "Choppa Sing", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:32", "ogFilename": "CartiBounce_A_052917", "notes": "A throwaway from the 'Die Lit' sessions.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/ceac663da3239c7ab95ae7f9e02f45bb/play", "key": "Choppa Sing", "description": "OG Filename: CartiBounce_A_052917\nA throwaway from the 'Die Lit' sessions.", "date": 16000416, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ea71f087e092a1aad920fa6edddc7dac", "size": "4.36 MB", "duration": 212.14, "originalContent": {"url": "https://music.froste.lol/song/ceac663da3239c7ab95ae7f9e02f45bb/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "63c88418c6f7fa00ee7413824d4e159c", "title": "Broke Hoes", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)", "length": "2:34", "ogFilename": "Broke Hoes 1.16.18", "notes": "OGF leaked on May 25, 2022.", "tags": ["OG File"], "aliases": ["(Carti World", "<PERSON><PERSON> Get Played)"], "type": "track", "originalUrl": "https://music.froste.lol/song/ad2ea87702ba0d1b0d9ae0b9406bb463/play", "key": "Broke Hoes", "description": "OG Filename: <PERSON><PERSON> Hoes 1.16.18\nOGF leaked on May 25, 2022.", "date": 16337376, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/63c88418c6f7fa00ee7413824d4e159c", "size": "3.45 MB", "duration": 154.9, "originalContent": {"url": "https://music.froste.lol/song/ad2ea87702ba0d1b0d9ae0b9406bb463/play", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)"}}, {"id": "685802dc4c4adbbceb7dce201486483c", "title": "Ain't Rockin' Gold", "artists": "(feat. <PERSON>) (prod. London on da Track)", "length": "2:07", "ogFilename": "01 Aint Rockin Gold 5.5.17", "notes": "Leaked on September 17, 2019.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/b971eea1f6bec8d137fca157b388a14f/play", "key": "Ain't Rockin' Gold", "description": "OG Filename: 01 <PERSON>t Rockin Gold 5.5.17\nLeaked on September 17, 2019.", "date": 15686784, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/685802dc4c4adbbceb7dce201486483c", "size": "3.01 MB", "duration": 127.49, "originalContent": {"url": "https://music.froste.lol/song/b971eea1f6bec8d137fca157b388a14f/play", "artists": "(feat. <PERSON>) (prod. London on da Track)"}}]}, {"era": "Die Lit 2", "tracks": [{"id": "0dd353d77c1c3ae670964ae2c1df0e6e", "title": "<PERSON><PERSON><PERSON> The <PERSON> [V3]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "3:27", "ogFilename": "<PERSON><PERSON><PERSON> the body 7.2.18", "notes": "A throwaway from the 'Die Lit 2' sessions.", "tags": ["OG File"], "aliases": ["(<PERSON> The Body", "BUFF THE BODY)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d74962188cd1262e4a7c9e7fc5a61271/play", "key": "<PERSON><PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON><PERSON> the body 7.2.18\nA throwaway from the 'Die Lit 2' sessions.", "date": 15669504, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/0dd353d77c1c3ae670964ae2c1df0e6e", "size": "3.32 MB", "duration": 207.32, "originalContent": {"url": "https://music.froste.lol/song/d74962188cd1262e4a7c9e7fc5a61271/play", "artists": "(prod. <PERSON><PERSON><PERSON>) "}}, {"id": "f37a5849cc9cce05f8c1daa8507398a2", "title": "Fuck It Up [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:33", "ogFilename": "carti 1 - means st pi'erre", "notes": "A throwaway from the 'Whole Lotta Red' sessions.", "tags": ["OG File"], "aliases": ["(Did It Again", "Die Lit Freestyle", "Friday)"], "type": "track", "originalUrl": "https://music.froste.lol/song/a0ecc58fab1d4f1d3bdf7bce423660ef/play", "key": "Fuck It Up", "description": "OG Filename: carti 1 - means st pi'erre\nA throwaway from the 'Whole Lotta Red' sessions.", "date": 15667776, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f37a5849cc9cce05f8c1daa8507398a2", "size": "4.2 MB", "duration": 213.41, "originalContent": {"url": "https://music.froste.lol/song/a0ecc58fab1d4f1d3bdf7bce423660ef/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "b16fe5c07bc62e26b6ac18d7bd0deb53", "title": "Goku [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:06", "ogFilename": "Goku 8.9.18", "notes": "A throwaway from the 'Whole Lotta Red' sessions. Has an open verse.", "tags": ["OG File"], "aliases": ["(Asthma", "<PERSON><PERSON><PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/7800b49b756b813d39e0357f39e4df74/play", "key": "<PERSON>ku", "description": "OG Filename: <PERSON><PERSON> 8.9.18\nA throwaway from the 'Whole Lotta Red' sessions. Has an open verse.", "date": 15673824, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b16fe5c07bc62e26b6ac18d7bd0deb53", "size": "2.82 MB", "duration": 126.96, "originalContent": {"url": "https://music.froste.lol/song/7800b49b756b813d39e0357f39e4df74/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "fe97200ae3f8bd73409270af6d42c2ef", "title": "<PERSON><PERSON> Coldhearted - She Might [V2]", "artists": "(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "3:30", "ogFilename": "She Might 6.30.18", "notes": "Song was originally considered lost but a new snippet would later emerge from <PERSON> on January 13, 2023 showing the song was either recently found again or was never lost to begin with. Song would finally be leaked by <PERSON> the next day. Has a short open verse.", "tags": ["OG File"], "aliases": ["(ReddXTrack1", "Red)"], "type": "track", "originalUrl": "https://music.froste.lol/song/4ebf5beefa21f8f33613f8eb940bf027/play", "key": "She Might", "description": "OG Filename: She Might 6.30.18\nSong was originally considered lost but a new snippet would later emerge from <PERSON> on January 13, 2023 showing the song was either recently found again or was never lost to begin with. Song would finally be leaked by <PERSON> the next day. Has a short open verse.", "date": 16736544, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/fe97200ae3f8bd73409270af6d42c2ef", "size": "3.38 MB", "duration": 210.73, "originalContent": {"url": "https://music.froste.lol/song/4ebf5beefa21f8f33613f8eb940bf027/play", "artists": "(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}, {"id": "b7dda02361fa1230a88587c4024eff90", "title": "<PERSON> [V1]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "1:59", "ogFilename": null, "notes": "OG version of the song. Previewed on a radio show.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/58fc6ef9f0790ac2de87dcdb7e521014/play", "key": "<PERSON><PERSON><PERSON>", "description": "OG version of the song. Previewed on a radio show.", "date": 15333408, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/b7dda02361fa1230a88587c4024eff90", "size": "1.92 MB", "duration": 119.98, "originalContent": {"url": "https://music.froste.lol/song/58fc6ef9f0790ac2de87dcdb7e521014/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}]}, {"era": "Whole Lotta Red [V1]", "tracks": [{"id": "9f9e161c76507deaed17924b9c090178", "title": "<PERSON><PERSON>ún [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:22", "ogFilename": "Cancun 9.12.18", "notes": "A throwaway from the 'Whole Lotta Red' sessions. One of the most famous unreleased songs by <PERSON><PERSON>.", "tags": ["OG File"], "aliases": ["(Money Jumpin'", "My Stummy Hurt)"], "type": "track", "originalUrl": "https://music.froste.lol/song/832844c455944c7343931a6d10e259b8/play", "key": "Cancún", "description": "OG Filename: Cancun 9.12.18\nA throwaway from the 'Whole Lotta Red' sessions. One of the most famous unreleased songs by <PERSON><PERSON>.", "date": 15674688, "available": ["OG File", "rgb(0, 0, 0)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/9f9e161c76507deaed17924b9c090178", "size": "3.07 MB", "duration": 142.5, "originalContent": {"url": "https://music.froste.lol/song/832844c455944c7343931a6d10e259b8/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7b408772891f75e80b82b9f9a565b43e", "title": "Hellcat [V4]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:40", "ogFilename": null, "notes": "Leaked on May 26, 2019 and is said to be the most complete version.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/00df125b555ca397edffc92168669716/play", "key": "Hellcat", "description": "Leaked on May 26, 2019 and is said to be the most complete version.", "date": 15588288, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7b408772891f75e80b82b9f9a565b43e", "size": "3.54 MB", "duration": 220.94, "originalContent": {"url": "https://music.froste.lol/song/00df125b555ca397edffc92168669716/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_2", "title": "i got ur back slatt, i promise u! [V1]", "artists": "(prod. 16yrold)", "length": null, "ogFilename": "PBC x Circle House 9.9.18-2", "notes": "Leaked on August 20, 2019.", "tags": ["OG File"], "aliases": ["(Red On Red", "What You Talking Bout", "<PERSON><PERSON>", "\"i got ur back slatt", "i promise u !\")"], "originalContent": {"artists": "(prod. 16yrold)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_3", "title": "Juice <PERSON>LD - <PERSON> [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Gezin & Nobu)", "length": null, "ogFilename": "<PERSON><PERSON> x <PERSON><PERSON> 10.10.18", "notes": "Track was never planned to be released. Uploaded to the producers SoundCloud in March 2019. Later removed for unknown reasons.", "tags": ["OG File"], "aliases": ["(Want To)"], "originalContent": {"artists": "(feat. <PERSON><PERSON><PERSON>) (prod. Gezin & Nobu)"}}, {"id": "b8156924ebcdd16c36bb5c9926be2369", "title": "Pop Bottles [V2]", "artists": "(prod. AJRuinedMyRecord)", "length": "1:57", "ogFilename": "Pop Bottles 8.12.18", "notes": "A throwaway from the 'Whole Lotta Red' sessions. This version has a new verse. Originally leaked in LQ on January 16, 2019, and later surfaced two months later in CDQ. OGF surfaced on October 10, 2024.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/012abce30101b3a71c1487d7acbde881/play", "key": "Pop Bottles", "description": "OG Filename: <PERSON> Bottles 8.12.18\nA throwaway from the 'Whole Lotta Red' sessions. This version has a new verse. Originally leaked in LQ on January 16, 2019, and later surfaced two months later in CDQ. OGF surfaced on October 10, 2024.", "date": 15532992, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b8156924ebcdd16c36bb5c9926be2369", "size": "1.88 MB", "duration": 117.26, "originalContent": {"url": "https://music.froste.lol/song/012abce30101b3a71c1487d7acbde881/play", "artists": "(prod. AJRuinedMyRecord)"}}, {"id": "5ee09721c943a0153464423039ea90ad", "title": "<PERSON> [V5]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:25", "ogFilename": "Reggie 9.9.18", "notes": "Version of Fashion Killa with 2 carti verses and different mix. Seems like carti later went back to the Reggie title. Unknown if earlier or later than <PERSON> fashion killer", "tags": ["OG File"], "aliases": ["(Fashion Killer", "Choppa Go", "Fashion Killa)"], "type": "track", "originalUrl": "https://music.froste.lol/song/57cdda03066e1cdab7d85f6b7cb61650/play", "key": "<PERSON>", "description": "OG Filename: Reggie 9.9.18\nVersion of Fashion Killa with 2 carti verses and different mix. Seems like carti later went back to the <PERSON> title. Unknown if earlier or later than <PERSON><PERSON><PERSON> fashion killer", "date": 15531264, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5ee09721c943a0153464423039ea90ad", "size": "3.11 MB", "duration": 145.42, "originalContent": {"url": "https://music.froste.lol/song/57cdda03066e1cdab7d85f6b7cb61650/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "3e4cc92e7b1687fef525f5e7ac3d4541", "title": "Skeleton [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:03", "ogFilename": "Skeleton 8.9.18", "notes": "A throwaway from the 'Whole Lotta Red' sessions. Features an open verse. A finished version featuring <PERSON><PERSON> Ruler exists.", "tags": ["OG File"], "aliases": ["(Skeletons", "Whole Lotta Red", "Off Red)"], "type": "track", "originalUrl": "https://music.froste.lol/song/761bbdbfae73d9f1544588ab1fe10ac4/play", "key": "Skeleton", "description": "OG Filename: Skeleton 8.9.18\nA throwaway from the 'Whole Lotta Red' sessions.\nFeatures an open verse. A finished version featuring <PERSON><PERSON> The Ruler exists.", "date": 15687648, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/3e4cc92e7b1687fef525f5e7ac3d4541", "size": "3.73 MB", "duration": 183.77, "originalContent": {"url": "https://music.froste.lol/song/761bbdbfae73d9f1544588ab1fe10ac4/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>) "}}, {"id": "7330a42bcba47643cebee06a1477e2fa", "title": "Whole Lotta Red [V3]", "artists": "(feat. <PERSON><PERSON>x602 & <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)", "length": "2:14", "ogFilename": null, "notes": "A throwaway from the 'Whole Lotta Red' sessions.", "tags": [], "aliases": ["(Coupe)"], "type": "track", "originalUrl": "https://music.froste.lol/song/499448ea7cf94119d763661cffffd9ce/play", "key": "Whole Lotta Red", "description": "A throwaway from the 'Whole Lotta Red' sessions.", "date": 15729120, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7330a42bcba47643cebee06a1477e2fa", "size": "2.15 MB", "duration": 134.16, "originalContent": {"url": "https://music.froste.lol/song/499448ea7cf94119d763661cffffd9ce/play", "artists": "(feat. <PERSON><PERSON>x602 & <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>) "}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_8", "title": "Woah [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "PBC x Stankonia 8.20.18 <PERSON><PERSON><PERSON><PERSON> C Take", "notes": "OG File (Metadata): PBC x Stankonia 8.20.18 <PERSON><PERSON><PERSON> Leaked on March 22, 2021 and has a different vocal take.", "tags": ["OG File"], "aliases": ["(Keep Going", "<PERSON>)"], "originalContent": {"artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_9", "title": "Young Nudy & Pi'er<PERSON> - <PERSON><PERSON> [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "NUDY_PIERRE_x_CARTI-_PISSY_PAMPER_03", "notes": "The most famous <PERSON><PERSON><PERSON> leak, despite not being his own song. Was planned to release on Young Nudy and <PERSON><PERSON><PERSON><PERSON>'s collaborative album \"Sli'merre\" but was removed due to sample clearance issues.", "tags": ["OG File"], "aliases": ["(<PERSON>", "<PERSON>", "They Tryna <PERSON>)"], "originalContent": {"artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_10", "title": "Designer Shoes [V2]", "artists": "(prod. Chinatown)", "length": null, "ogFilename": "carti china designer shoes", "notes": "Has a different verse compared to the original and an instrumental break at the end. Leaked on May 31, 2022. Was seen on a V1 tracklist from April.", "tags": ["OG File"], "aliases": ["(Pop My Shit", "Brand New Bih V2", "Bring A Friend)"], "originalContent": {"artists": "(prod. Chinatown)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_11", "title": "Free PDE [V2]", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "Free PDE 2.12.19", "notes": "A throwaway from the 'Whole Lotta Red' sessions. Has a different line on the hook, \"<PERSON>ma die lit on that piru\". Seen on a V1 tracklist from April.", "tags": ["OG File"], "aliases": ["(Bouldercrest", "Piru)"], "originalContent": {"artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_12", "title": "Friends", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "FRIENDS", "notes": "\"Friends\" is a unreleased song which was originally on \"Whole Lotta Red\" and was meant to be released as the paperwork was filed for it's release. Additionally, it previewed in a currently unreleased Whole Lotta Red 'documentary'. Privately purchased & leaked for free by Hells on March 22, 2023. Recorded before June 2019, and present on tracklists with songs from WLR v1 era. Supposed \"OG File\" leaked d on October 9th, 2024 as a bonus for a MUSIC era blind GB.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>", "Came In That Bih)"], "originalContent": {"artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_13", "title": "Not Real [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "Not Real 5.23.19", "notes": "This version is the release ready mix of Not Real. Though it does fix some of version 2's issues, it isn't really much different. Leaked by a random during the reopening of the new Carti Tracker Discord server. The mixing is rough so the quality is affected from the distortion. The beat is higher quality than the other two versions tho. This version was intended to be released on May 31, 2019.", "tags": ["OG File"], "aliases": ["(<PERSON>", "No Stylist", "molly.jpg", "Diamonds", "molly.jpeg)"], "originalContent": {"artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_14", "title": "<PERSON><PERSON><PERSON>", "artists": "(prod. EarlOnTheBeat & MitchGoneMad)", "length": null, "ogFilename": "PBC X 02.12.19 x A Room x AJ 1", "notes": "Previewed by @twoninehundred on December 8, 2020. Leaked by @countingcaskets on April 28, 2022. <PERSON> was originally believed to be produced by AJRuinedMyRecord but was disproven by MitchGoneMad in an Instagram comment. According to <PERSON> (Carti Associate), <PERSON><PERSON> isn't saying \"Movie Time, Let's Get The Mac\", but \"<PERSON>, Let's Get The Money\" Seen on a V1 tracklist from April. Later put on a Deluxe tracklist.", "tags": ["OG File"], "aliases": ["(<PERSON>", "Movie Time", "Let's Get The Mac", "Places)"], "originalContent": {"artists": "(prod. EarlOnTheBeat & MitchGoneMad)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_15", "title": "<PERSON><PERSON> [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "A throwaway from the 'Whole Lotta Red' sessions. Seen on a V1 tracklist from April", "tags": [], "aliases": ["(<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>)"], "originalContent": {"artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "2b5ec1c68b2fcc39167bde7e62f5afb7", "title": "Few Times", "artists": "(prod. London on da Track)", "length": "3:56", "ogFilename": null, "notes": "Song was scrapped and no official bounces were created; The leaked file is a fanmade completed mix.", "tags": ["Self-Bo<PERSON><PERSON>", "Lossless"], "aliases": ["(Dreams", "<PERSON>)"], "type": "track", "originalUrl": "https://pillowcase.su/f/3ecd6d3076c15c2146f31fe0b69c9a1f", "key": "Few Times", "description": "Song was scrapped and no official bounces were created; The leaked file is a fanmade completed mix.", "date": 16827264, "available": ["Self-Bo<PERSON><PERSON>", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/2b5ec1c68b2fcc39167bde7e62f5afb7", "size": "4.15 MB", "duration": 236.56, "originalContent": {"url": "https://pillowcase.su/f/3ecd6d3076c15c2146f31fe0b69c9a1f", "artists": "(prod. London on da Track)"}}, {"id": "9d76b406941ac131ceac1dd2491ddc71", "title": "<PERSON><PERSON> - <PERSON>", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. ?)", "length": "2:32", "ogFilename": "PBC X KANI Tony <PERSON> 2.11.19", "notes": "OG Filename (Metadata): PBC X KANI <PERSON> 2.11.1 OGF for <PERSON>", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/d159bcb78c4a913975f538f23aa09ba3/play", "key": "<PERSON>", "description": "OG Filename: PBC X KANI <PERSON> 2.11.19 \nOG Filename (Metadata): PBC X KANI <PERSON> 2.11.1\nOGF for <PERSON>", "date": 16569792, "available": ["OG File", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/9d76b406941ac131ceac1dd2491ddc71", "size": "2.44 MB", "duration": 152.06, "originalContent": {"url": "https://music.froste.lol/song/d159bcb78c4a913975f538f23aa09ba3/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. ?)"}}, {"id": "05dc2f3c186dd48d8d70c7597970a5e2", "title": "<PERSON><PERSON>", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:23", "ogFilename": "carti means 1", "notes": "Recorded in a session with Red Ice, Not Real [V2], Rockstar Tracks, and VVV. Considered to be the first WLR V2 Song", "tags": ["OG File"], "aliases": ["(Pop<PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/4bfc6d24593795625b25d2df10c90165/play", "key": "<PERSON><PERSON>", "description": "OG Filename: carti means 1\nRecorded in a session with Red Ice, Not Real [V2], Rockstar Tracks, and VVV. Considered to be the first WLR V2 Song", "date": 16832448, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/05dc2f3c186dd48d8d70c7597970a5e2", "size": "3.26 MB", "duration": 203.88, "originalContent": {"url": "https://music.froste.lol/song/4bfc6d24593795625b25d2df10c90165/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "3c224a3639ca44c740552ad1b5cb3092", "title": "VVV", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:01", "ogFilename": "carti means st 2", "notes": "Recorded in a session with <PERSON> Ice, Not Real [V2], Xan, and Rockstar Tracks. Considered to be the first WLR V2 Session. Leaked by <PERSON><PERSON>.", "tags": ["OG File"], "aliases": ["(Triple V)"], "type": "track", "originalUrl": "https://music.froste.lol/song/8f95b025b17e62afeed091cd466d4669/play", "key": "VVV", "description": "OG Filename: carti means st 2\nRecorded in a session with Red Ice, Not Real [V2], Xan, and Rockstar Tracks. Considered to be the first WLR V2 Session. Leaked by <PERSON>w.", "date": 16892928, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/3c224a3639ca44c740552ad1b5cb3092", "size": "2.91 MB", "duration": 181.93, "originalContent": {"url": "https://music.froste.lol/song/8f95b025b17e62afeed091cd466d4669/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "01dcc9687a7d2fb4a176e61d76c00272", "title": "<PERSON><PERSON> [V3]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:53", "ogFilename": "01 V Westwood 4.4.19", "notes": "Alternate mix of <PERSON><PERSON><PERSON> dated April 4th, 2019. It is unknown whether it is earlier or later than the second version. <PERSON> later used the beat for his song \"Peephole\".", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/3501d58a62ee26444e3c2ebe7678b794/play", "key": "<PERSON><PERSON>", "description": "OG Filename: 01 V Westwood 4.4.19\nAlternate mix of <PERSON><PERSON><PERSON> dated April 4th, 2019. It is unknown whether it is earlier or later than the second version. <PERSON> later used the beat for his song \"Peephole\".", "date": 16660512, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/01dcc9687a7d2fb4a176e61d76c00272", "size": "3.74 MB", "duration": 233.44, "originalContent": {"url": "https://music.froste.lol/song/3501d58a62ee26444e3c2ebe7678b794/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_21", "title": "Rockstar Tracks", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "carti 5 means st", "notes": "Recorded in a session with <PERSON> Ice, Not Real [V2], <PERSON>an, and VVV. Considered to be the first WLR V2 Session.", "tags": ["OG File"], "aliases": ["(I'm A Rockstar)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e25417d302013aedeae9d10879a9d827/play", "key": "Rockstar Tracks", "description": "OG Filename: carti 5 means st\nRecorded in a session with Red Ice, Not Real [V2], Xan, and VVV. Considered to be the first WLR V2 Session.", "date": 16824672, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "https://music.froste.lol/song/e25417d302013aedeae9d10879a9d827/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_22", "title": "Red Ice", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "carti 3 means st", "notes": "Recorded in a session with Rockstar Tracks, Not Real [V2], Xan, and VVV. Considered to be the first WLR V2 session.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/b07e9973af183a9ddc79f56450a00c31/play", "key": "Red Ice", "description": "OG Filename: carti 3 means st\nRecorded in a session with Rockstar Tracks, Not Real [V2], Xan, and VVV. Considered to be the first WLR V2 session.", "date": 16824672, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "https://music.froste.lol/song/b07e9973af183a9ddc79f56450a00c31/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_23", "title": "Not Real [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "Reworked version of the song with added a new verse over the open. Said to be still titled Not Real in 2019. Song was seen on a V2 tracklist, however, this song was recorded during the V1 'transition' era. A release ready mix was set to release on May 31, 2019 as a single. Has worse vocal mixing on the second verse. But sounds better than V3.", "tags": [], "aliases": ["(<PERSON>", "No Stylist", "molly.jpeg", "Diamonds)"], "type": "track", "originalUrl": "https://music.froste.lol/song/0237e47f58126c057a64d74c8f5bf2a0/play", "key": "Not Real", "description": "Reworked version of the song with added a new verse over the open. Said to be still titled Not Real in 2019. Song was seen on a V2 tracklist, however, this song was recorded during the V1 'transition' era. A release ready mix was set to release on May 31, 2019 as a single. Has worse vocal mixing on the second verse. But sounds better than V3.", "date": 17165952, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/0237e47f58126c057a64d74c8f5bf2a0/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_24", "title": "Don't <PERSON>orry", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Maaly Raw)", "length": null, "ogFilename": null, "notes": "A throwaway from the 'Whole Lotta Red' sessions.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/6c105f54fc2d4fb8695ed149e3210944/play", "key": "Don't <PERSON>orry", "description": "A throwaway from the 'Whole Lotta Red' sessions.", "date": 15642720, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/6c105f54fc2d4fb8695ed149e3210944/play", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Maaly Raw)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_25", "title": "Designer Shoes [V1]", "artists": "(prod. Chinatown)", "length": null, "ogFilename": "carti china bring a friend", "notes": "Shares the beat with the 2017 throwaway \"Brand New\". This version shares the same title as the beat, as bring a friend is not this versions official name. Leaked on Nov 14, 2023. Seen on a V1 tracklist from April.", "tags": ["OG File"], "aliases": ["(Bring a Friend)"], "type": "track", "originalUrl": "https://music.froste.lol/song/0903deef20736709e74f09a006c1782c/play", "key": "Designer Shoes", "description": "OG Filename: carti china bring a friend\nShares the beat with the 2017 throwaway \"Brand New\". This version shares the same title as the beat, as bring a friend is not this versions official name. Leaked on Nov 14, 2023. Seen on a V1 tracklist from April.", "date": 16999200, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/0903deef20736709e74f09a006c1782c/play", "artists": "(prod. Chinatown)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_26", "title": "<PERSON> - RAF", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "01 Carti x Jordan RAF 10.10.18", "notes": "Previewed by @Countingcaskets on Instagram Live and said it was not <PERSON><PERSON><PERSON> but <PERSON>'s song. Uses the same beat as <PERSON><PERSON><PERSON>'s \"<PERSON><PERSON>\", though <PERSON><PERSON> has some production differences. <PERSON> most likely later on gave the song to <PERSON><PERSON>, due to it's apperance as track 16 on the WLR TL from April 2019. Leaked September 7, 2022.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/5f5941a3f28a92e486077f0a807fdba4/play", "key": "RAF", "description": "OG Filename: 01 <PERSON><PERSON> x <PERSON> RAF 10.10.18\nPreviewed by @Countingcaskets on Instagram Live and said it was not <PERSON>eb<PERSON> but <PERSON>'s song. Uses the same beat as <PERSON><PERSON><PERSON>'s \"<PERSON><PERSON>\", though <PERSON><PERSON> has some production differences. <PERSON> most likely later on gave the song to <PERSON><PERSON>, due to it's apperance as track 16 on the WLR TL from April 2019. Leaked September 7, 2022.", "date": 16625088, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/5f5941a3f28a92e486077f0a807fdba4/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}, {"id": "2e6bca789d21d21257f3e137a2e42d70", "title": "Shopping Spree", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>heB<PERSON>)", "length": "1:39", "ogFilename": "Shopping Spree 11.22.18", "notes": "OG File Metadata: <PERSON><PERSON><PERSON> 11.22.18 Previewed in @countingcasket's Instagram Live. Von said it was on WLR v1.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON><PERSON>", "Bags On Me", "500 Degrees)"], "type": "track", "originalUrl": "https://music.froste.lol/song/cf096fa04ff6c52dc7a2a31ec7bd3434/play", "key": "Shopping Spree", "description": "OG Filename: Shopping Spree 11.22.18\nOG File Metadata: <PERSON><PERSON><PERSON> 11.22.18\nPreviewed in @countingcasket's Instagram Live. <PERSON> said it was on WLR v1.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2e6bca789d21d21257f3e137a2e42d70", "size": "2.38 MB", "duration": 99.76, "originalContent": {"url": "https://music.froste.lol/song/cf096fa04ff6c52dc7a2a31ec7bd3434/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>heB<PERSON>)"}}, {"id": "bafb777f2762567028207dede825b238", "title": "Switching Lanes [V1]", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)", "length": "2:43", "ogFilename": null, "notes": "Was meant for <PERSON><PERSON> before he gave it to <PERSON><PERSON><PERSON><PERSON> for \"The Life Of Pi'erre 5\".", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/1a61f427b36e3d4fdee41cd6b2c86e66/play", "key": "Switching Lanes", "description": "Was meant for <PERSON><PERSON> before he gave it to <PERSON><PERSON><PERSON><PERSON> for \"The Life Of Pi'erre 5\".", "date": 15649632, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/bafb777f2762567028207dede825b238", "size": "2.62 MB", "duration": 163.51, "originalContent": {"url": "https://music.froste.lol/song/1a61f427b36e3d4fdee41cd6b2c86e66/play", "artists": "(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>er<PERSON>)"}}, {"id": "ad7a5bf2c71ab0ffe3db3c8315ce0f86", "title": "<PERSON><PERSON> [Song 2]", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON>)", "length": "2:47", "ogFilename": null, "notes": "Leaked on February 28, 2021.", "tags": [], "aliases": ["(Bags", "Team)"], "type": "track", "originalUrl": "https://music.froste.lol/song/3c5e657c1f46475852c32e810520ab88/play", "key": "<PERSON><PERSON>", "description": "Leaked on February 28, 2021.", "date": 16144704, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ad7a5bf2c71ab0ffe3db3c8315ce0f86", "size": "3.46 MB", "duration": 167.29, "originalContent": {"url": "https://music.froste.lol/song/3c5e657c1f46475852c32e810520ab88/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON>)"}}, {"id": "43d47d9905c26c930a683af142b6d3ba", "title": "One Day (Remix)", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:55", "ogFilename": null, "notes": "Played by <PERSON> on his \"Sound 42\" radio show on the same day \"Certified Lover Boy\" released. <PERSON> is said to have recorded around the same time as <PERSON> 1993.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/56f7da35bbe0931b889ebf06d84ef51f/play", "key": "One Day (Remix)", "description": "Played by <PERSON> on his \"Sound 42\" radio show on the same day \"Certified Lover Boy\" released. <PERSON> is said to have recorded around the same time as <PERSON> 1993.", "date": 16306272, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/43d47d9905c26c930a683af142b6d3ba", "size": "2.8 MB", "duration": 175.1, "originalContent": {"url": "https://music.froste.lol/song/56f7da35bbe0931b889ebf06d84ef51f/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "1436a4037d3a282f647f3794358eeb06", "title": "Not Real [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:37", "ogFilename": "Not Real", "notes": "Original version from October 2018 with an open verse. Said to be titled Not Real before WLR [V2] in 2020. On March 31, 2020, leakers revealed that alongside the name being called \"Not Real\" and it being the first track on the April 2019 WLR TL, they also mentioned that the OG Filename was also titled \"Not Real\". Song was recorded at Means St Studios sometime in October 2018.", "tags": [], "aliases": ["(molly.jpeg", "<PERSON>", "No Stylist", "molly.jpg", "Look At These Diamonds They Shine)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e7178a921fbdb8eb71783e82f735d706/play", "key": "Not Real", "description": "OG Filename: Not Real\nOriginal version from October 2018 with an open verse. Said to be titled Not Real before WLR [V2] in 2020. On March 31, 2020, leakers revealed that alongside the name being called \"Not Real\" and it being the first track on the April 2019 WLR TL, they also mentioned that the OG Filename was also titled \"Not Real\". Song was recorded at Means St Studios sometime in October 2018.", "date": 15597792, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/1436a4037d3a282f647f3794358eeb06", "size": "3.47 MB", "duration": 157.36, "originalContent": {"url": "https://music.froste.lol/song/e7178a921fbdb8eb71783e82f735d706/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "2c600d1e352c3053dc09b5f594414799", "title": "Nine Nine", "artists": "(prod. <PERSON> Boomin)", "length": "2:19", "ogFilename": null, "notes": "Leaked on November 10, 2020.", "tags": [], "aliases": ["(Lies", "Nines", "One Of The Nines)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d6ba175faeb8003d18248d495cafc3c2/play", "key": "Nine Nine", "description": "Leaked on November 10, 2020.", "date": 16049664, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2c600d1e352c3053dc09b5f594414799", "size": "3.02 MB", "duration": 139.86, "originalContent": {"url": "https://music.froste.lol/song/d6ba175faeb8003d18248d495cafc3c2/play", "artists": "(prod. <PERSON> Boomin)"}}, {"id": "generated_id_Whole_Lotta_Red_[V1]_12_33", "title": "Juice WRLD - That's A 50 [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "Thats a 50 PBC 8.24.18", "notes": "The second version of the song with vocals from <PERSON><PERSON><PERSON>. <PERSON><PERSON>'s vocals were taken from \"Racks\" with RX Peso. OGF leaked on Mar 30, 2024", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/ea3d715cad0e81c41cfc6f10bf6a4cbb/play", "key": "That's A 50", "description": "OG Filename: Thats a 50 PBC 8.24.18\nThe second version of the song with vocals from <PERSON><PERSON><PERSON>. <PERSON><PERSON>'s vocals were taken from \"Racks\" with RX Peso. OGF leaked on Mar 30, 2024", "date": 15670368, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/ea3d715cad0e81c41cfc6f10bf6a4cbb/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}, {"id": "ca57a38599c8d5550953334f8b581baf", "title": "Fashion Killer [V2]", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:24", "ogFilename": "FashionKiller Feature PBC x Stankonia 8.24.18 SR", "notes": "Features a verse from <PERSON> and new <PERSON><PERSON><PERSON> verse.", "tags": ["OG File"], "aliases": ["(<PERSON>", "Choppa Go)"], "type": "track", "originalUrl": "https://music.froste.lol/song/fea6a3e734a65a555bf7126504d21844/play", "key": "Fashion Killer", "description": "OG Filename: FashionKiller Feature PBC x Stankonia 8.24.18 SR\nFeatures a verse from <PERSON> and new <PERSON><PERSON><PERSON> verse.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ca57a38599c8d5550953334f8b581baf", "size": "3.1 MB", "duration": 144.74, "originalContent": {"url": "https://music.froste.lol/song/fea6a3e734a65a555bf7126504d21844/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "4af34717382ab8abca317cc555a5e235", "title": "Butterfly Doors", "artists": "(prod. <PERSON>)", "length": "2:43", "ogFilename": "Butterfly Doors 8.9.18", "notes": "Previewed in the Leaked Die Lit/Whole Lotta Red session footage.", "tags": ["OG File"], "aliases": ["(War", "7AM)"], "type": "track", "originalUrl": "https://music.froste.lol/song/bc02a2457c982f742eb91aba60bfa1ad/play", "key": "Butterfly Doors", "description": "OG Filename: Butterfly Doors 8.9.18\nPreviewed in the Leaked Die Lit/Whole Lotta Red session footage.", "date": 15675552, "available": ["OG File", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4af34717382ab8abca317cc555a5e235", "size": "2.61 MB", "duration": 163.15, "originalContent": {"url": "https://music.froste.lol/song/bc02a2457c982f742eb91aba60bfa1ad/play", "artists": "(prod. <PERSON>) "}}, {"id": "1b9c587862b475fee540f4e242982952", "title": "Back Up [V2]", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "2:56", "ogFilename": "Back Up 9.8.18", "notes": "A throwaway from the 'Whole Lotta Red' sessions. This version has new vocals from Offset. OGF surfaced on October 10, 2024.", "tags": ["OG File"], "aliases": ["(<PERSON><PERSON><PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e60510a7dbcfeeaa9a6bd3b6ba586603/play", "key": "Back Up", "description": "OG Filename: Back Up 9.8.18\nA throwaway from the 'Whole Lotta Red' sessions. This version has new vocals from <PERSON>set. OGF surfaced on October 10, 2024.", "date": 15670368, "available": ["OG File", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/1b9c587862b475fee540f4e242982952", "size": "3.61 MB", "duration": 176.4, "originalContent": {"url": "https://music.froste.lol/song/e60510a7dbcfeeaa9a6bd3b6ba586603/play", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>) "}}, {"id": "4b98a57800664ca8c2e2e329ac3457a6", "title": "Back Room", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:00", "ogFilename": "BackRoom PBC x Astro 8.22.18 SR", "notes": "Leaked on April 11, 2020.", "tags": ["OG File"], "aliases": ["(Backroom", "Count That <PERSON>", "Blackroom)"], "type": "track", "originalUrl": "https://music.froste.lol/song/bed4862152775a57d6187c3fd99512ac/play", "key": "Back Room", "description": "OG Filename: BackRoom PBC x Astro 8.22.18 SR\nLeaked on April 11, 2020.", "date": 15865632, "available": ["OG File", "rgb(0, 0, 0)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4b98a57800664ca8c2e2e329ac3457a6", "size": "3.68 MB", "duration": 180.96, "originalContent": {"url": "https://music.froste.lol/song/bed4862152775a57d6187c3fd99512ac/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>) "}}]}, {"era": "Trippie Redd Collab EP", "tracks": [{"id": "8649675a133e95e4230a5288315cd906", "title": "Arm Leg [V2]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:10", "ogFilename": "Arm Leg ft. <PERSON><PERSON>", "notes": "A throwaway from the 'Whole Lotta Red' sessions. Technically CDQ but the file is heavily distorted to the point where it is practically HQ.", "tags": ["OG File"], "aliases": ["(That Bitch!", "Arm & Leg)"], "type": "track", "originalUrl": "https://music.froste.lol/song/a90f591c1fad31cbf65ee4c8283a4c04/play", "key": "Arm Leg", "description": "OG Filename: Arm Leg ft. <PERSON><PERSON>\nA throwaway from the 'Whole Lotta Red' sessions. Technically CDQ but the file is heavily distorted to the point where it is practically HQ.", "date": 15696288, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/8649675a133e95e4230a5288315cd906", "size": "8.82 MB", "duration": 130.35, "originalContent": {"url": "https://music.froste.lol/song/a90f591c1fad31cbf65ee4c8283a4c04/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "7026394cf161fc1bfd78133c88d5a400", "title": "Exotic [V1]", "artists": "(prod. <PERSON><PERSON><PERSON>)", "length": "2:49", "ogFilename": null, "notes": "Leaked on May 22, 2019.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/de7549bb2e87db1abe9c126eac798bdb/play", "key": "Exotic", "description": "Leaked on May 22, 2019.", "date": 15584832, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7026394cf161fc1bfd78133c88d5a400", "size": "9.45 MB", "duration": 169.87, "originalContent": {"url": "https://music.froste.lol/song/de7549bb2e87db1abe9c126eac798bdb/play", "artists": "(prod. <PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_<PERSON><PERSON>_Redd_Collab_EP_13_2", "title": "Arm and Leg [V1]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)", "length": null, "ogFilename": "01 Arm and Leg 2.3.19", "notes": "Open Verse version that was given to <PERSON><PERSON>. Has a few more <PERSON><PERSON> lines that were cut and also isn't distorted. Not to be confused with the other Arm and Leg from Die Lit era.", "tags": ["OG File"], "aliases": ["(That Bitch!", "Arm & Leg)"], "type": "track", "originalUrl": "https://music.froste.lol/song/897fad45a2dd6cd5b3d700695df5d608/play", "key": "Arm and Leg", "description": "OG Filename: 01 Arm and Leg 2.3.19\nOpen Verse version that was given to <PERSON><PERSON>d. Has a few more Carti lines that were cut and also isn't distorted. Not to be confused with the other Arm and Leg from Die Lit era.", "date": 16628544, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/897fad45a2dd6cd5b3d700695df5d608/play", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>)"}}]}, {"era": "Whole Lotta Red [V2]", "tracks": [{"id": "2ce2038715a167e9ea9a822cae63f800", "title": "2MANY [V1]", "artists": "(prod. <PERSON>)", "length": "3:47", "ogFilename": "2MANY", "notes": "OG File Metadata: 2many 10.16.19 Previewed on May 4, 2020. Leaked after a $8,000 groupbuy by <PERSON>.", "tags": ["OG File"], "aliases": ["(Taking My Swag", "2 Many)"], "type": "track", "originalUrl": "https://music.froste.lol/song/b6f6d99b80d5a4f168e4bf7065e9bc50/play", "key": "2MANY", "description": "OG Filename: 2MANY\nOG File Metadata: 2many 10.16.19\nPreviewed on May 4, 2020. Leaked after a $8,000 groupbuy by <PERSON>.", "date": 16637184, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2ce2038715a167e9ea9a822cae63f800", "size": "3.73 MB", "duration": 227.3, "originalContent": {"url": "https://music.froste.lol/song/b6f6d99b80d5a4f168e4bf7065e9bc50/play", "artists": "(prod. <PERSON>)"}}, {"id": "fe1189dee85a930566a1c2a4a47987ba", "title": "Headshot [V1]", "artists": "(prod. <PERSON>)", "length": "2:44", "ogFilename": "8. headshot 2.3.20", "notes": "Leaked on April 14, 2020. Heads Off was later seen on multiple July Donda tracklists, under the name \"Heads Off\", showing <PERSON>'s interest in making his own version of the song but <PERSON> never actually recorded on the song according to Waterfalls.", "tags": ["OG File"], "aliases": ["(Heads Off)"], "type": "track", "originalUrl": "https://music.froste.lol/song/578ea726c434a1c36cea91b781bd1320/play", "key": "Headshot", "description": "OG Filename: 8. headshot 2.3.20\nLeaked on April 14, 2020. Heads Off was later seen on multiple July Donda tracklists, under the name \"Heads Off\", showing <PERSON>'s interest in making his own version of the song but <PERSON> never actually recorded on the song according to Waterfalls.", "date": 15868224, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/fe1189dee85a930566a1c2a4a47987ba", "size": "2.73 MB", "duration": 164.7, "originalContent": {"url": "https://music.froste.lol/song/578ea726c434a1c36cea91b781bd1320/play", "artists": "(prod. <PERSON>)"}}, {"id": "8ff50133f9cbd9d449aaea7ff403f265", "title": "M.A.D. [V2]", "artists": "(prod. jetsonmade)", "length": "2:31", "ogFilename": "m.a.d 1.3.20", "notes": "Previewed in @bloodyhimbloodywho's Instagram Story and was originally thought to feature <PERSON>. Privately purchased by community member <PERSON><PERSON> and leaked afterwards for free on March 22, 2023. Supposed OG file leaked on October 9, 2024 as apart of 2024 Blind gb. Recored in Mean St Studios mixed with Generation Now which share the same studio.", "tags": ["OG File"], "aliases": ["(Money & Drugs", "Money N Drugs)"], "type": "track", "originalUrl": "https://music.froste.lol/song/1ddd383c729171dd1efcf6bcf520dc98/play", "key": "M.A.D.", "description": "OG Filename: m.a.d 1.3.20\nPreviewed in @bloodyhimbloodywho's Instagram Story and was originally thought to feature <PERSON>. Privately purchased by community member <PERSON><PERSON> and leaked afterwards for free on March 22, 2023. Supposed OG file leaked on October 9, 2024 as apart of 2024 Blind gb. Recored in Mean St Studios mixed with Generation Now which share the same studio.", "date": 16794432, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/8ff50133f9cbd9d449aaea7ff403f265", "size": "2.52 MB", "duration": 151.59, "originalContent": {"url": "https://music.froste.lol/song/1ddd383c729171dd1efcf6bcf520dc98/play", "artists": "(prod. jetsonmade)"}}, {"id": "7264d5c62565e57bd74fedd995972ec5", "title": "SRT [V1]", "artists": "(feat. <PERSON>) (prod. <PERSON> Finessin & Outtatown)", "length": "3:20", "ogFilename": "SRT (Brandon x outtatown) Ruff", "notes": "A throwaway from the 'Whole Lotta Red' sessions. Was allegedly part of a Working On Dying tape at one time.", "tags": ["OG File"], "aliases": ["(Brand New SRT)"], "type": "track", "originalUrl": "https://music.froste.lol/song/ae11c8c22037f9fa552ccc086a4c19d5/play", "key": "SRT", "description": "OG Filename: SR<PERSON> (<PERSON> x outtatown) Ruff\nA throwaway from the 'Whole Lotta Red' sessions. Was allegedly part of a Working On Dying tape at one time.", "date": 15977088, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7264d5c62565e57bd74fedd995972ec5", "size": "3.3 MB", "duration": 200.1, "originalContent": {"url": "https://music.froste.lol/song/ae11c8c22037f9fa552ccc086a4c19d5/play", "artists": "(feat. <PERSON>) (prod. <PERSON> Finessin & Outtatown)"}}, {"id": "4a6b6ad508e53a6f3ff84436f845b3c5", "title": "u kan do it too. [V1]", "artists": "(prod. <PERSON> & Art Dealer)", "length": "3:46", "ogFilename": "u kan do it too. MAIN SING MASTER", "notes": "\"U Kan Do It Too\" is an unreleased throwaway from \"Whole Lotta Red\" which was intended to be the lead single before being scrapped for \"@ MEH\". The file was mixed and mastered for release and had all the paperwork ready for the release, as well as the ASCAP being filed. OG file leaked on October 10th, 2024. Song was recorded sometime before October 2019, and later submitted for copyright on October 10, 2019. Listed on the DSPs as \"u can do it too.'\" on October 25, 2019, the cover art for the song was revealed to be simply a black square. It's unknown at this time if this was meant to be the cover or just a placeholder. The song was recorded before October 2019, meaning that though it was technically recorded during the 2019 V1 sessions, it was meant to release as the lead single for V2.", "tags": ["OG File", "Lossless"], "aliases": ["(U Can Do It Too", "Too Much Money", "Jump Out)"], "type": "track", "originalUrl": "https://music.froste.lol/song/9b6f79222eb9486517f4399de342f34f/play", "key": "u kan do it too.", "description": "OG Filename: u kan do it too. MAIN SING MASTER\n\"U Kan Do It Too\" is an unreleased throwaway from \"Whole Lotta Red\" which was intended to be the lead single before being scrapped for \"@ MEH\". The file was mixed and mastered for release and had all the paperwork ready for the release, as well as the ASCAP being filed. OG file leaked on October 10th, 2024. Song was recorded sometime before October 2019, and later submitted for copyright on October 10, 2019. Listed on the DSPs as \"u can do it too.'\" on October 25, 2019, the cover art for the song was revealed to be simply a black square. It's unknown at this time if this was meant to be the cover or just a placeholder. The song was recorded before October 2019, meaning that though it was technically recorded during the 2019 V1 sessions, it was meant to release as the lead single for V2.", "date": 16799616, "available": ["OG File", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/4a6b6ad508e53a6f3ff84436f845b3c5", "size": "3.73 MB", "duration": 226.95, "originalContent": {"url": "https://music.froste.lol/song/9b6f79222eb9486517f4399de342f34f/play", "artists": "(prod. <PERSON> & Art Dealer)"}}, {"id": "64dc282a95b748a55df957f7eec2799a", "title": "<PERSON> <PERSON><PERSON> [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Finessin & Lukrative)", "length": "4:32", "ogFilename": "Sauce (Brandon x 1lukrative) Ruff v2", "notes": "Was originally incorrectly thought to feature a <PERSON><PERSON> impersonator or possibly <PERSON>.", "tags": ["OG File"], "aliases": ["(Sauce Real Hard)"], "type": "track", "originalUrl": "https://music.froste.lol/song/571b27106424ada300e52ecf078c0af5/play", "key": "Sauce", "description": "OG Filename: <PERSON><PERSON> (<PERSON> x 1lukrative) Ruff v2\nWas originally incorrectly thought to feature a <PERSON><PERSON> impersonator or possibly <PERSON>.", "date": 16793568, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/64dc282a95b748a55df957f7eec2799a", "size": "4.45 MB", "duration": 272.09, "originalContent": {"url": "https://music.froste.lol/song/571b27106424ada300e52ecf078c0af5/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> Finessin & Lukrative)"}}, {"id": "a02bd97a6c2ffaa23bf1005adee15d14", "title": "<PERSON><PERSON> - High As Us [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": "2:44", "ogFilename": "BANS FEAT. CARTI - HIGH AS US APR 3", "notes": "Leaked on May 22, 2020. Features one of <PERSON><PERSON> most controversial baby voice verses.", "tags": ["OG File", "Lossless"], "aliases": ["(High Like Us", "Not Like Us)"], "type": "track", "originalUrl": "https://music.froste.lol/song/c446080eadce44e9dc8dfbf6347a286e/play", "key": "High As Us", "description": "OG Filename: BANS FEAT. CARTI - HIGH AS US APR 3\nLeaked on May 22, 2020. Features one of <PERSON><PERSON> most controversial baby voice verses.", "date": 15901056, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/a02bd97a6c2ffaa23bf1005adee15d14", "size": "2.72 MB", "duration": 164.06, "originalContent": {"url": "https://music.froste.lol/song/c446080eadce44e9dc8dfbf6347a286e/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>) "}}, {"id": "3b5f97ebb76f9012f47b4c990189e304", "title": "<PERSON><PERSON><PERSON> - New Feelings", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)", "length": "3:13", "ogFilename": "New Feelings Guap <PERSON> x <PERSON>", "notes": "Said to have a Future feature that has yet to be teased. Song was Previewed on June 9, 2022 and sold June 16, 2022. <PERSON><PERSON><PERSON> would upload the song on his Soundcloud on August 14, 2022 but it was soon taken down.", "tags": ["OG File"], "aliases": ["(Codeine Cup)"], "type": "track", "originalUrl": "https://music.froste.lol/song/f64bff064c66452f7b9dfb4793e67bf0/play", "key": "New Feelings", "description": "OG Filename: New Feelings Guap <PERSON> x <PERSON><PERSON>\nSaid to have a Future feature that has yet to be teased. Song was Previewed on June 9, 2022 and sold June 16, 2022. <PERSON><PERSON><PERSON> would upload the song on his Soundcloud on August 14, 2022 but it was soon taken down.", "date": 16604352, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/3b5f97ebb76f9012f47b4c990189e304", "size": "3.2 MB", "duration": 193.92, "originalContent": {"url": "https://music.froste.lol/song/f64bff064c66452f7b9dfb4793e67bf0/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)"}}, {"id": "4e81803671e6cb58360f4a043dc39ebf", "title": "X Files [V1]", "artists": "(prod. <PERSON><PERSON>)", "length": "3:30", "ogFilename": null, "notes": "Solo version of \"Teen X\" with an alternate verse instead of <PERSON> and a Maaly Raw tag. Leaked after a successful groupbuy. Uses the same beat as green light by <PERSON><PERSON>.", "tags": [], "aliases": ["(Teen X)"], "type": "track", "originalUrl": "https://music.froste.lol/song/44e016e267d6a5dadd211e4dc405e786/play", "key": "X Files", "description": "Solo version of \"Teen X\" with an alternate verse instead of <PERSON> and a Maaly Raw tag. Leaked after a successful groupbuy. Uses the same beat as green light by <PERSON><PERSON>.", "date": 16851456, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/4e81803671e6cb58360f4a043dc39ebf", "size": "3.46 MB", "duration": 210.36, "originalContent": {"url": "https://music.froste.lol/song/44e016e267d6a5dadd211e4dc405e786/play", "artists": "(prod. <PERSON><PERSON>)"}}, {"id": "b43197645890a4ebc4cf6030608dc146", "title": "Pass It [V1]", "artists": "(feat. <PERSON>) (prod. <PERSON> Finessin & Outtatown)", "length": "2:23", "ogFilename": null, "notes": "A throwaway from the Whole Lotta Red sessions. Has a different Uzi verse, compared to the version that leaked with the release of Pink Tape.", "tags": [], "aliases": ["(Ask Me", "Patience", "<PERSON> Bastard)"], "type": "track", "originalUrl": "https://music.froste.lol/song/f9b6be222da9e88dc2d8a54c74fd57c9/play", "key": "Pass It", "description": "A throwaway from the Whole Lotta Red sessions. Has a different Uzi verse, compared to the version that leaked with the release of Pink Tape.", "date": 17053632, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/b43197645890a4ebc4cf6030608dc146", "size": "2.39 MB", "duration": 143.41, "originalContent": {"url": "https://music.froste.lol/song/f9b6be222da9e88dc2d8a54c74fd57c9/play", "artists": "(feat. <PERSON>) (prod. <PERSON> Finessin & Outtatown)"}}, {"id": "236dbef219b2d303529bec75f5483310", "title": "My Cup", "artists": "(prod. <PERSON>)", "length": "3:20", "ogFilename": "PBC - My Cup (Prod <PERSON>)", "notes": "OG File Metadata: rs_ Previewed by @c9rti on Instagram. Leaked on July 23, 2022. File is dated as 2021, but the song was clearly recorded during the V2 sessions, though this song could've been reworked for Narcissist or the WLR Deluxe.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/e8ee7bd7cbec8573ef538e76d43ef3a2/play", "key": "My Cup", "description": "OG Filename: PBC - <PERSON> Cup (Prod <PERSON>)\nOG File Metadata: rs_\nPreviewed by @c9rti on Instagram. Leaked on July 23, 2022. File is dated as 2021, but the song was clearly recorded during the V2 sessions, though this song could've been reworked for Narcissist or the WLR Deluxe.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/236dbef219b2d303529bec75f5483310", "size": "3.3 MB", "duration": 200.06, "originalContent": {"url": "https://music.froste.lol/song/e8ee7bd7cbec8573ef538e76d43ef3a2/play", "artists": "(prod. <PERSON>)"}}, {"id": "6d49fdbf66b7459622cdda9a57e52c7f", "title": "<PERSON><PERSON>", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "2:19", "ogFilename": null, "notes": "A throwaway from the Whole Lotta Red sessions. A music video leaked alongside the song, which the CDQ audio was ripped from. The OG file is not out yet. Some believe that this song was recorded after @MEH.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/6d8b25790703e17a6eed8f45cc4454c0/play", "key": "<PERSON><PERSON>", "description": "A throwaway from the Whole Lotta Red sessions. A music video leaked alongside the song, which the CDQ audio was ripped from. The OG file is not out yet. Some believe that this song was recorded after @MEH.", "date": 16185312, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6d49fdbf66b7459622cdda9a57e52c7f", "size": "2.33 MB", "duration": 139.73, "originalContent": {"url": "https://music.froste.lol/song/6d8b25790703e17a6eed8f45cc4454c0/play", "artists": "(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}, {"id": "65199bdc60c5b574f77a0ebf557c58b3", "title": "<PERSON><PERSON>", "artists": "(feat. CA<PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)", "length": "3:36", "ogFilename": "BALLIN", "notes": "Leaked by @countingcaskets.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/21456ae562a027f8b586673d03b84026/play", "key": "<PERSON><PERSON>", "description": "OG Filename: <PERSON><PERSON><PERSON>\nLeaked by @countingcaskets.", "date": 16479072, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/65199bdc60c5b574f77a0ebf557c58b3", "size": "3.55 MB", "duration": 216.07, "originalContent": {"url": "https://music.froste.lol/song/21456ae562a027f8b586673d03b84026/play", "artists": "(feat. CA<PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)"}}]}, {"era": "Whole Lotta Red [V3]", "tracks": [{"id": "477c6860888dc4c470c3ce81474ff6eb", "title": "K Suave - Perky97 [V4]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. PinkGrillz)", "length": "2:39", "ogFilename": "<PERSON> <PERSON><PERSON>97 (VDM_MIX-3)", "notes": "OG Mix of Perky97", "tags": ["OG File", "Lossless"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/69e03ad5ec62fb54641001154bba4130/play", "key": "Perky97", "description": "OG Filename: <PERSON><PERSON>ky97 (VDM_MIX-3)\nOG Mix of Perky97", "date": 15778368, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/477c6860888dc4c470c3ce81474ff6eb", "size": "5.54 MB", "duration": 159.31, "originalContent": {"url": "https://music.froste.lol/song/69e03ad5ec62fb54641001154bba4130/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. PinkGrillz)"}}, {"id": "1ab2d6c36e3af4743f2790ae18d5a0b9", "title": "Rover Sport [V1]", "artists": "(prod. <PERSON> 88, star boy & Outtatown)", "length": "3:48", "ogFilename": "bring yo friends-1", "notes": "OG Filename: <PERSON>OVER SPORT Previewed by Instagram user @govwok. OG file leaked on July 23, 2022. Titled 'Bring Yo Friends' for the November 2020 WLR tracklist, and later the WLR Deluxe, however, it was originally titled Rover Sport. As the file name was seen in the WLR Documentary. Recorded in July of 2020.", "tags": ["OG File"], "aliases": ["(Bring Yo Friends)"], "type": "track", "originalUrl": "https://music.froste.lol/song/7f2970424133be18c18ed56cc1533608/play", "key": "Rover Sport", "description": "OG Filename: bring yo friends-1\nOG Filename: ROVER SPORT\nPreviewed by Instagram user @govwok. OG file leaked on July 23, 2022. Titled 'Bring Yo Friends' for the November 2020 WLR tracklist, and later the WLR Deluxe, however, it was originally titled Rover Sport. As the file name was seen in the WLR Documentary. Recorded in July of 2020.", "date": 16585344, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/1ab2d6c36e3af4743f2790ae18d5a0b9", "size": "6.65 MB", "duration": 228.94, "originalContent": {"url": "https://music.froste.lol/song/7f2970424133be18c18ed56cc1533608/play", "artists": "(prod. <PERSON> 88, star boy & Outtatown)"}}]}, {"era": "Whole Lotta Red [V4]", "tracks": [{"id": "8fed36e0d863d4080d152f9aa6a1f0cd", "title": "<PERSON> - Solo Dolo Pt. IV [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)", "length": "3:11", "ogFilename": "carti x cudi SoloDoloFlip (day1)", "notes": "Previewed by <PERSON> during a show in Paris. Samples \"Solo Dolo\" by <PERSON>. Confirmed to be from Whole Lotta Red era by waterfalls. This song was scrapped from <PERSON><PERSON>'s album INSANO due to <PERSON><PERSON> not clearing the song. Password-protected file leaked on Sep 29, 2023. Seen on <PERSON><PERSON>'s compute and the password leaked after a successful GB revealing that all this time the password was \"BellyFat69!\"", "tags": ["OG File", "Lossless"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/282f6c668c99db6e585041c0930e37b1/play", "key": "Solo Dolo Pt. IV", "description": "OG Filename: carti x cudi <PERSON> (day1)\nPreviewed by <PERSON> during a show in Paris. Samples \"Solo Dolo\" by <PERSON>. Confirmed to be from Whole Lotta Red era by waterfalls. This song was scrapped from <PERSON><PERSON>'s album INSANO due to <PERSON><PERSON> not clearing the song. Password-protected file leaked on Sep 29, 2023. Seen on <PERSON><PERSON>'s compute and the password leaked after a successful GB revealing that all this time the password was \"BellyFat69!\"", "date": 17131392, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/8fed36e0d863d4080d152f9aa6a1f0cd", "size": "3.1 MB", "duration": 191.35, "originalContent": {"url": "https://music.froste.lol/song/282f6c668c99db6e585041c0930e37b1/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)"}}, {"id": "6f23737be602b48e5c0de983aa87dbbb", "title": "<PERSON> Yachty - Flex Up [V1]", "artists": "(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. <PERSON><PERSON>, ATL Jacob & Pyrex)", "length": "3:00", "ogFilename": "pbc ref", "notes": "First bounce of flex up without <PERSON><PERSON> on it. Has only <PERSON><PERSON> and <PERSON>. Most likely a reference for <PERSON><PERSON>, due to his absense on this version.", "tags": ["OG File", "Lossless"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/78b32b708236672572fcfded60d9e48a/play", "key": "Flex Up", "description": "OG Filename: pbc ref\nFirst bounce of flex up without <PERSON><PERSON> on it. Has only <PERSON><PERSON> and <PERSON>. Most likely a reference for <PERSON><PERSON>, due to his absense on this version.", "date": ********, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/6f23737be602b48e5c0de983aa87dbbb", "size": "1.48 MB", "duration": 180.02, "originalContent": {"url": "https://music.froste.lol/song/78b32b708236672572fcfded60d9e48a/play", "artists": "(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. <PERSON><PERSON>, ATL Jacob & Pyrex)"}}, {"id": "c9450cc71cf15fd2996e267d5275bb8b", "title": "Proud of You [V3]", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)", "length": "3:36", "ogFilename": "KAP G- CARTI", "notes": "Contains a Kap G feature. <PERSON><PERSON> <PERSON> is said to have recorded his vocals sometime in December 2020.", "tags": ["OG File"], "aliases": ["(Pop", "Pac)"], "type": "track", "originalUrl": "https://music.froste.lol/song/d6223b5d0779e5dd6f0dd86b1616fb62/play", "key": "Proud of You", "description": "OG Filename: KAP G- CARTI\nContains a Kap G feature. <PERSON><PERSON> <PERSON> is said to have recorded his vocals sometime in December 2020.", "date": 16722720, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/c9450cc71cf15fd2996e267d5275bb8b", "size": "3.5 MB", "duration": 216.03, "originalContent": {"url": "https://music.froste.lol/song/d6223b5d0779e5dd6f0dd86b1616fb62/play", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>) "}}]}, {"era": "Whole Lotta Red (Deluxe)", "tracks": [{"id": "generated_id_Whole_Lotta_Red_(Deluxe)_17_0", "title": "Money", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>, Outtatown & star boy)", "length": null, "ogFilename": "Money #1", "notes": "A throwaway from the Post-Whole Lotta Red sessions. According to very reputable sources, while recording the song, <PERSON><PERSON> had a cinnamon Yankee candle lit in the studio. 1st song recorded from the session.", "tags": [], "aliases": ["(YSL Cheetah)"], "originalContent": {"artists": "(prod. <PERSON><PERSON><PERSON><PERSON>, Outtatown & star boy)"}}, {"id": "2ccf116be2e9b31c7fad5ab1a25505de", "title": "Pour Me Up", "artists": "(prod. <PERSON><PERSON>ill)", "length": "2:23", "ogFilename": "Pour Me Up #3_1", "notes": "A throwaway from the Whole Lotta Red sessions. 3rd song recorded from the session.", "tags": [], "aliases": ["(Can't Sleep", "<PERSON><PERSON> Feel How U Feel)"], "type": "track", "originalUrl": "https://music.froste.lol/song/2dde98f21ca168b72a0a70aa7728d20a/play", "key": "Pour Me Up", "description": "OG Filename: <PERSON><PERSON> Me Up #3_1\nA throwaway from the Whole Lotta Red sessions. 3rd song recorded from the session.", "date": 16519680, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/2ccf116be2e9b31c7fad5ab1a25505de", "size": "2.51 MB", "duration": 143.26, "originalContent": {"url": "https://music.froste.lol/song/2dde98f21ca168b72a0a70aa7728d20a/play", "artists": "(prod. <PERSON><PERSON>ill)"}}, {"id": "generated_id_Whole_Lotta_Red_(Deluxe)_17_2", "title": "Medusa*", "artists": "(prod. F1LTHY & Lukrative)", "length": null, "ogFilename": null, "notes": "A throwaway from the post-'Whole Lotta Red' sessions. Unofficial bounce using the ProTools sessions. The community gave it the name \"Going Outta Style,\" not to be confused with the 'Whole Lotta Red V2' track \"Goin' Outta Style.\" It was later confirmed to be a completely different, untitled song. Notably, it was the fifth song recorded from that session and was made in a single take. There’s also another version featuring <PERSON><PERSON><PERSON>, though the verse might be fake, as it might be from a leaker like <PERSON><PERSON> who pretended to be part of <PERSON><PERSON>’s team to get him to record on it. Though soul has said that the <PERSON><PERSON><PERSON> verse was order by the label, but nothing has came since.", "tags": ["Self-Bo<PERSON><PERSON>"], "aliases": ["(Goin' Outta Style", "Going Out Of Style", "Rager)"], "originalContent": {"artists": "(prod. F1LTHY & Lukrative)"}}, {"id": "f289234e7f0f0be3a8962888556ec75a", "title": "Ridin' [V1]", "artists": "(prod. KP Beatz & Fluxury)", "length": "4:06", "ogFilename": "<PERSON><PERSON><PERSON> #9", "notes": "Often thought to be from 2020, but the metadata of the official file, is said to point at it being recorded on Feb 21, 2021. 9th song from the session.", "tags": ["Self-Bo<PERSON><PERSON>"], "aliases": ["(<PERSON>", "Lord Can You Spare Me", "Country Mart)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6c6429519d1c9dcf3f323f6ca686f183", "key": "Ridin'", "description": "OG Filename: <PERSON><PERSON><PERSON> #9\nOften thought to be from 2020, but the metadata of the official file, is said to point at it being recorded on Feb 21, 2021. 9th song from the session.", "date": 16719264, "available": ["Self-Bo<PERSON><PERSON>", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f289234e7f0f0be3a8962888556ec75a", "size": "4.51 MB", "duration": 246.91, "originalContent": {"url": "https://pillowcase.su/f/6c6429519d1c9dcf3f323f6ca686f183", "artists": "(prod. KP Beatz & Fluxury)"}}, {"id": "generated_id_Whole_Lotta_Red_(Deluxe)_17_4", "title": "Rockstar Shit*", "artists": "(prod. <PERSON><PERSON>ill)", "length": null, "ogFilename": "PBC #2_1", "notes": "A throwaway from the Whole Lotta Red sessions. 2nd song from the session. Real name is unknown.", "tags": ["OG File"], "aliases": ["(Cheater", "<PERSON><PERSON><PERSON><PERSON>)"], "originalContent": {"artists": "(prod. <PERSON><PERSON>ill)"}}, {"id": "73faec57a05a0c65953d4b1546e2b0a2", "title": "<PERSON> - Playground*", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. TM88)", "length": "2:58", "ogFilename": "KEED_X_PLAYBOI_TM", "notes": "Leaked by @countingcaskets. A couple months after it leaked, sources \"claimed\" that it was recorded the day after WLR (26 of December, 2020). Not to mention that <PERSON><PERSON>'s take matches the flow he had during this period of time. Track is technically unnamed.", "tags": ["OG File"], "aliases": ["(<PERSON>", "Million Dollars On Bonds)"], "type": "track", "originalUrl": "https://music.froste.lol/song/2762b9ef84125d7fb1e8201c3d3e1922/play", "key": "Playground*", "description": "OG Filename: KEED_X_PLAYBOI_TM\nLeaked by @countingcaskets. A couple months after it leaked, sources \"claimed\" that it was recorded the day after WLR (26 of December, 2020). Not to mention that <PERSON><PERSON>'s take matches the flow he had during this period of time. Track is technically unnamed.", "date": 16733952, "available": ["OG File", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/73faec57a05a0c65953d4b1546e2b0a2", "size": "3.08 MB", "duration": 178.35, "originalContent": {"url": "https://music.froste.lol/song/2762b9ef84125d7fb1e8201c3d3e1922/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. TM88)"}}, {"id": "f64fca17718ab0d7cb29c3b0c45dad41", "title": "Popular [V2]", "artists": "(prod. F1LTHY)", "length": "2:54", "ogFilename": null, "notes": "A throwaway from the Whole Lotta Red sessions. Completely different take from the first version he did.", "tags": [], "aliases": ["(Brand New", "breakoff)"], "type": "track", "originalUrl": "https://pillowcase.su/f/0d375b98e9e8da3ea09ba7a10ea0fda1", "key": "Popular", "description": "A throwaway from the Whole Lotta Red sessions. Completely different take from the first version he did.", "date": 16728768, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f64fca17718ab0d7cb29c3b0c45dad41", "size": "2.83 MB", "duration": 174.05, "originalContent": {"url": "https://pillowcase.su/f/0d375b98e9e8da3ea09ba7a10ea0fda1", "artists": "(prod. F1LTHY)"}}, {"id": "d4111ffb9d944bec8fc1809f56e0f0f8", "title": "Popular [V1]", "artists": "(prod. F1LTHY)", "length": "2:50", "ogFilename": "PBC Popular #7_1", "notes": "A throwaway from the Whole Lotta Red sessions. 7th song recorded from the session", "tags": ["OG File"], "aliases": ["(breakoff)"], "type": "track", "originalUrl": "https://pillowcase.su/f/5e3f364e44c71aa2bec6fce802f561fe", "key": "Popular", "description": "OG Filename: PBC Popular #7_1\nA throwaway from the Whole Lotta Red sessions. 7th song recorded from the session", "date": 16719264, "available": ["OG File", "rgb(0, 0, 0)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/d4111ffb9d944bec8fc1809f56e0f0f8", "size": "2.77 MB", "duration": 170.14, "originalContent": {"url": "https://pillowcase.su/f/5e3f364e44c71aa2bec6fce802f561fe", "artists": "(prod. F1LTHY)"}}, {"id": "f34bfbf34ea9edaa2b88b72ad5a92d67", "title": "Never Seen Shit", "artists": "(prod. F1LTHY, Lukrative & Lucian)", "length": "1:59", "ogFilename": "Never Seen Shit #4", "notes": "A throwaway from the Whole Lotta Red sessions. 4th song recorded from the session", "tags": [], "aliases": ["(Hijack)"], "type": "track", "originalUrl": "https://pillowcase.su/f/6f8680157ed8309c4ff1648806f9fd5d", "key": "Never Seen Shit", "description": "OG Filename: <PERSON> Seen Shit #4\nA throwaway from the Whole Lotta Red sessions. 4th song recorded from the session", "date": 16719264, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/f34bfbf34ea9edaa2b88b72ad5a92d67", "size": "1.95 MB", "duration": 119.18, "originalContent": {"url": "https://pillowcase.su/f/6f8680157ed8309c4ff1648806f9fd5d", "artists": "(prod. F1LTHY, Lukrative & Lucian)"}}, {"id": "73d5de06b91c1df6511707aa8e1d61a2", "title": "Can't Fake It [V2]", "artists": "(prod. F1LTHY & Lukrative)", "length": "2:21", "ogFilename": "Cant Fake It - adlibs", "notes": "A throwaway from the Whole Lotta Red sessions. According to metadata, from 2021. 8th song from the session.", "tags": ["OG File"], "aliases": ["(Outside)"], "type": "track", "originalUrl": "https://pillowcase.su/f/39343140ecc098649f2b5c336f1c0e35", "key": "Can't Fake It", "description": "OG Filename: <PERSON>t Fake It - adlibs\nA throwaway from the Whole Lotta Red sessions. According to metadata, from 2021. 8th song from the session.", "date": 16519680, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/73d5de06b91c1df6511707aa8e1d61a2", "size": "2.3 MB", "duration": 141, "originalContent": {"url": "https://pillowcase.su/f/39343140ecc098649f2b5c336f1c0e35", "artists": "(prod. F1LTHY & Lukrative)"}}, {"id": "76f1591ef91b02a0a1e9595aca9dc591", "title": "Too Many Bags", "artists": "(prod. F1LTHY, Lukrative & Lucian)", "length": "2:20", "ogFilename": "Too Many Bags #6_1", "notes": "A throwaway from Post- Whole Lotta red sessions. 6th song recorded from the session.", "tags": [], "aliases": ["(<PERSON>", "Groovy <PERSON>)"], "type": "track", "originalUrl": "https://music.froste.lol/song/3302c7bc1a12d4284c8525a767754631", "key": "Too Many Bags", "description": "OG Filename: Too Many Bags #6_1\nA throwaway from Post- Whole Lotta red sessions. 6th song recorded from the session.", "date": 16719264, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/76f1591ef91b02a0a1e9595aca9dc591", "size": "2.47 MB", "duration": 140.28, "originalContent": {"url": "https://music.froste.lol/song/3302c7bc1a12d4284c8525a767754631", "artists": "(prod. F1LTHY, Lukrative & Lucian)"}}]}, {"era": "Ye - DONDA", "tracks": [{"id": "07de30d432a9150657b924498940fe55", "title": "Ye - Made It [V4]", "artists": "(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Ojivolta)", "length": "4:27", "ogFilename": "Made It - 12.23.20 JA Classic Only", "notes": "Version of \"Made It\" featuring <PERSON><PERSON><PERSON>, with Ojivolta production and <PERSON> vocals similar to their work on \"God Breathed\". Likely made in the same session as \"Off The Grid\". <PERSON><PERSON><PERSON>'s verse is 40 seconds long, and said to be \"excellent\". Song without <PERSON><PERSON><PERSON> on it leaked on September 10, 2023 and day after version with <PERSON><PERSON> leaked in full.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/20d26a99f1cbc32b0698c95cd3156f8d", "key": "Made It", "description": "OG Filename: Made It - 12.23.20 JA Classic Only\nVersion of \"Made It\" featuring <PERSON><PERSON><PERSON>, with Ojivolta production and <PERSON> vocals similar to their work on \"God Breathed\". Likely made in the same session as \"Off The Grid\". <PERSON><PERSON><PERSON>'s verse is 40 seconds long, and said to be \"excellent\". Song without <PERSON><PERSON><PERSON> on it leaked on September 10, 2023 and day after version with <PERSON><PERSON> leaked in full.", "date": 16943904, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/07de30d432a9150657b924498940fe55", "size": "11.6 MB", "duration": 267.34, "originalContent": {"url": "https://pillowcase.su/f/20d26a99f1cbc32b0698c95cd3156f8d", "artists": "(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. Ojivolta)"}}]}, {"era": "Narcissist", "tracks": [{"id": "ec77545c68909d277c3e52b14335b8c6", "title": "Sights", "artists": "(with A$AP Rocky) (prod. Evilgiane & BABYXSOSA)", "length": "4:23", "ogFilename": "pbc x flacko 5.2.21 2", "notes": "On September 2, 2022, A$AP Rocky uploaded the song to his Instagram along with its music video. On September 7, 2022, controversy started when opiumbaby called out and dissed <PERSON> for \"leaking the song\", saying that <PERSON><PERSON> didn’t approve the track's release and that it was <PERSON><PERSON>'s song, not <PERSON>'s. <PERSON> later confirmed this. OGF with an open verse would end up leaking on September 9, 2022. <PERSON> later performed the song at Rolling Loud New York on September 25, 2022. Much later in 2023, when <PERSON> was asked about the song in an interview, he explained that he posted it because \"They just kept leaking it, so we just said 'fuck it' and put it out\", despite the fact that the song was completely unheard up until the moment <PERSON> leaked it himself. The track samples \"If\" by <PERSON>'s Child and \"Dirty White Speaks / Make Em Say Huh\" by <PERSON>.", "tags": ["OG File"], "aliases": ["(OUR DE$TINY)"], "type": "track", "originalUrl": "https://pillowcase.su/f/0f3f40422b2831e7264ba10fd2b34622", "key": "Sights", "description": "OG Filename: pbc x flacko 5.2.21 2\nOn September 2, 2022, A$AP Rocky uploaded the song to his Instagram along with its music video. On September 7, 2022, controversy started when opiumbaby called out and dissed <PERSON> for \"leaking the song\", saying that <PERSON><PERSON> didn’t approve the track's release and that it was <PERSON><PERSON>'s song, not <PERSON>'s. <PERSON> later confirmed this. OGF with an open verse would end up leaking on September 9, 2022. <PERSON> later performed the song at Rolling Loud New York on September 25, 2022. Much later in 2023, when <PERSON> was asked about the song in an interview, he explained that he posted it because \"They just kept leaking it, so we just said 'fuck it' and put it out\", despite the fact that the song was completely unheard up until the moment <PERSON> leaked it himself.\n\nThe track samples \"If\" by <PERSON>'s Child and \"Dirty White Speaks / Make Em Say Huh\" by <PERSON>.", "date": 16626816, "available": ["OG File", "rgb(255, 255, 255)", "rgb(0, 0, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/ec77545c68909d277c3e52b14335b8c6", "size": "4.28 MB", "duration": 263.07, "originalContent": {"url": "https://pillowcase.su/f/0f3f40422b2831e7264ba10fd2b34622", "artists": "(with A$AP Rocky) (prod. Evilgiane & BABYXSOSA)"}}, {"id": "9869268e6db48d0922e469f866537c1e", "title": "Made It This Far [V2]", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON> Nas)", "length": "3:45", "ogFilename": "made it this far (ft. vory)", "notes": "Previewed on Digital Nas's Stream during the 2021 Donda Sessions in Mercedes-Benz Stadium. Confirmed to be titled \"Made It This Far\" by <PERSON>. Privately purchased and leaked for free by Hells on March 23, 2023. <PERSON><PERSON> said that <PERSON><PERSON> was furious when Digital Nas filmed the snippet.", "tags": [], "aliases": ["(24 Songs", "Off The Grid", "We Made It This Far", "Made It)"], "type": "track", "originalUrl": "https://pillowcase.su/f/b569941e140826d5f771ab6092eadc45", "key": "Made It This Far", "description": "OG Filename: made it this far (ft. vory) \nPreviewed on Digital Nas's Stream during the 2021 Donda Sessions in Mercedes-Benz Stadium. Confirmed to be titled \"Made It This Far\" by <PERSON>. Privately purchased and leaked for free by Hells on March 23, 2023. <PERSON><PERSON> said that <PERSON><PERSON> was furious when Digital Nas filmed the snippet.", "date": 16795296, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/9869268e6db48d0922e469f866537c1e", "size": "3.67 MB", "duration": 225.18, "originalContent": {"url": "https://pillowcase.su/f/b569941e140826d5f771ab6092eadc45", "artists": "(feat. <PERSON><PERSON>) (prod. <PERSON> Nas)"}}, {"id": "5a6afe7e4a1f1316ef243caae3173447", "title": "Stop Breathing (Tour Version) [V2]", "artists": null, "length": "4:51", "ogFilename": null, "notes": "CDQ version of the Narcissist Tour Version of \"Stop Breathing\".", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/7aab0a9cb61d2106d6805799c2571fbe", "key": "Stop Breathing (Tour Version)", "description": "CDQ version of the Narcissist Tour Version of \"Stop Breathing\".", "date": 16931808, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5a6afe7e4a1f1316ef243caae3173447", "size": "4.73 MB", "duration": 291.17, "originalContent": {"url": "https://pillowcase.su/f/7aab0a9cb61d2106d6805799c2571fbe", "artists": null}}, {"id": "489911a5b583e6eb9951aa0aef0e1d22", "title": "Stop Breathing (Tour Version) [V1]", "artists": null, "length": "4:34", "ogFilename": null, "notes": "Earlier version.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/7c7e0652bb522abe0fda866285656277", "key": "Stop Breathing (Tour Version)", "description": "Earlier version.", "date": 16931808, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/489911a5b583e6eb9951aa0aef0e1d22", "size": "4.46 MB", "duration": 274.13, "originalContent": {"url": "https://pillowcase.su/f/7c7e0652bb522abe0fda866285656277", "artists": null}}, {"id": "7fd78f82c5848ba1bebadbd60fa96b1e", "title": "Rockstar Made (Tour Version)", "artists": null, "length": "4:06", "ogFilename": null, "notes": "CDQ version of the Narcissist Tour Version of \"Rockstar Made\"", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/32649f7484ba57154444043b4aa4a49e", "key": "Rockstar Made (Tour Version)", "description": "CDQ version of the Narcissist Tour Version of \"Rockstar Made\"", "date": 16433280, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/7fd78f82c5848ba1bebadbd60fa96b1e", "size": "4.01 MB", "duration": 246.47, "originalContent": {"url": "https://pillowcase.su/f/32649f7484ba57154444043b4aa4a49e", "artists": null}}, {"id": "068f9a5826a6eeadfe03e1dffe0c4ce2", "title": "New Tank (Tour Version)", "artists": null, "length": "2:18", "ogFilename": null, "notes": "CDQ version of the Narcissist Tour Version of \"New Tank\"", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/875d2ee177c70755c73a7c9d63dccdf9", "key": "New Tank (Tour Version)", "description": "CDQ version of the Narcissist Tour Version of \"New Tank\"", "date": 16433280, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/068f9a5826a6eeadfe03e1dffe0c4ce2", "size": "2.29 MB", "duration": 138.76, "originalContent": {"url": "https://pillowcase.su/f/875d2ee177c70755c73a7c9d63dccdf9", "artists": null}}, {"id": "c98614d4ff8ab667d243c82423898060", "title": "F33l Lik3 Dyin (Tour Version)", "artists": null, "length": "4:24", "ogFilename": null, "notes": "King Vamp/Narcissist Tour version of F33l Lik3 Dyin.", "tags": ["Performance"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/b045ac250573ddbf04079e4a4a49c988/play", "key": "F33l Lik3 Dyin (Tour Version)", "description": "King Vamp/Narcissist Tour version of F33l Lik3 Dyin.", "date": 16365888, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Performance", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/c98614d4ff8ab667d243c82423898060", "size": "4.29 MB", "duration": 264.07, "originalContent": {"url": "https://music.froste.lol/song/b045ac250573ddbf04079e4a4a49c988/play", "artists": null}}, {"id": "generated_id_Narcissist_19_7", "title": "<PERSON>'er<PERSON> - Switching Lanes / Punk Monk [Festival Mix]", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>, F1LTHY, Lukrative, & Lucian [Neilaworld])", "length": null, "ogFilename": null, "notes": "<PERSON>'erre remix over the \"Punk Monk\" beat using his verse and chorus from \"Switching Lanes\". Played live on the TLOP5 tour.", "tags": ["Performance"], "aliases": ["(Switching Lanes x <PERSON> Monk)"], "type": "track", "originalUrl": "https://youtu.be/uURBG4J-aBw?si=57RceqzF963sCCB1", "key": "Switching Lanes / Punk Monk", "description": "<PERSON>'erre remix over the \"Punk Monk\" beat using his verse and chorus from \"Switching Lanes\". Played live on the TLOP5 tour.", "date": 16356384, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Performance", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "originalContent": {"url": "https://youtu.be/uURBG4J-aBw?si=57RceqzF963sCCB1", "artists": "(prod. <PERSON><PERSON><PERSON><PERSON>, F1LTHY, Lukrative, & Lucian [Neilaworld])"}}, {"id": "generated_id_Narcissist_19_8", "title": "<PERSON> - Remember My Name [V1]", "artists": "(ref. <PERSON>) (prod. <PERSON> & Pillenn2k)", "length": null, "ogFilename": "Remember My Name (<PERSON>) Ruff", "notes": "Peformed live by <PERSON> multiple times. Then, he confirmed the song had a <PERSON><PERSON> feature. Version without <PERSON><PERSON> feature (which was the one probably played during the concert) surfaced on Apr 3, 2025", "tags": [], "aliases": ["(No Brain)"], "type": "track", "originalUrl": "https://music.froste.lol/song/8b6db41a2a18f4fae4dfaa519e469c32", "key": "Remember My Name", "description": "OG Filename: Remember My Name (<PERSON>) <PERSON><PERSON>eformed live by <PERSON> multiple times. Then, he confirmed the song had a Carti feature. Version without <PERSON><PERSON> feature (which was the one probably played during the concert) surfaced on Apr 3, 2025", "date": 17436384, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/8b6db41a2a18f4fae4dfaa519e469c32", "artists": "(ref. <PERSON>) (prod. <PERSON> & Pillenn2k)"}}, {"id": "034e1be60ac5434b3594ccd2a1351208", "title": "Future - Hold Up", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> & <PERSON>)", "length": "3:08", "ogFilename": null, "notes": "A song from the 'Narcissist' sessions. Leaked after a succesful GB.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/f6a5e9b50b34085aeaa022d202ffb8a7", "key": "Hold Up", "description": "A song from the 'Narcissist' sessions. Leaked after a succesful GB.", "date": 16837632, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/034e1be60ac5434b3594ccd2a1351208", "size": "3.08 MB", "duration": 188.16, "originalContent": {"url": "https://pillowcase.su/f/f6a5e9b50b34085aeaa022d202ffb8a7", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON> & <PERSON>)"}}, {"id": "generated_id_Narcissist_19_10", "title": "A$AP Rocky - GO [V2]", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "Previewed by A$AP Rocky during Smokers Club Fest 2022. Leaked randomly on 12/6/2022", "tags": [], "aliases": ["(Count Up A Check)"], "type": "track", "originalUrl": "http://music.froste.lol/song/03ee300d3200560ebc7fc1fd7d151ba5/play", "key": "GO", "description": "Previewed by A$AP Rocky during Smokers Club Fest 2022. Leaked randomly on 12/6/2022", "date": 16702848, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "http://music.froste.lol/song/03ee300d3200560ebc7fc1fd7d151ba5/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)"}}]}, {"era": "Guapo Era", "tracks": [{"id": "823df9620584c7402488b2f6101c80a4", "title": "KILLERS", "artists": "(prod. <PERSON><PERSON>)", "length": "2:22", "ogFilename": "pbc 3-2-22", "notes": "According to <PERSON><PERSON>'s manager, it was planned to be the lead single for \"MUSIC\" for unspecified amount of time and was supposed to drop 3 weeks after it leaked. Leaked after a succesful groupbuy to which half of the sum contributed the community goat Hells, later also previewed on the OVO radio. Recorded on March 2nd 2022. The OG File, that leaked has the title changed. The file sent out to DJs was pbc 3-2-22, but was (supposedly) named KILLERS. This is a different song from WICKED.", "tags": ["OG File"], "aliases": ["(Pockets", "250 Dash", "Wicked)"], "type": "track", "originalUrl": "https://music.froste.lol/song/ec81a6c75eace9153153282ba637dffb/play", "key": "KILLERS", "description": "OG Filename: pbc 3-2-22\nAccording to <PERSON><PERSON>'s manager, it was planned to be the lead single for \"MUSIC\" for unspecified amount of time and was supposed to drop 3 weeks after it leaked. Leaked after a succesful groupbuy to which half of the sum contributed the community goat Hells, later also previewed on the OVO radio. Recorded on March 2nd 2022. The OG File, that leaked has the title changed. The file sent out to DJs was pbc 3-2-22, but was (supposedly) named <PERSON><PERSON><PERSON><PERSON>. This is a different song from WICKED.", "date": 16935264, "available": ["OG File", "rgb(0, 0, 0)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/823df9620584c7402488b2f6101c80a4", "size": "2.67 MB", "duration": 142.3, "originalContent": {"url": "https://music.froste.lol/song/ec81a6c75eace9153153282ba637dffb/play", "artists": "(prod. <PERSON><PERSON>)"}}, {"id": "6b25c7c68fd823b7130b55796c0eeb2e", "title": "WAKE UP F1LTHY [V1]", "artists": "(feat. <PERSON>) (prod. BNYX & F1LTHY)", "length": "2:48", "ogFilename": "pbc 10-30-22 5 F1 TRAV", "notes": "Original 2022 version of the released track. Has alternate intial takes by <PERSON><PERSON>, and more <PERSON>' adlibs throughout the song. Previewed by <PERSON><PERSON>. Leaked in full as a bonus to a group buy on August 12, 2024. Re-ripped, the file is not the OG File quality.", "tags": [], "aliases": ["(WAKE UP", "Different Hoes", "Racks Up)"], "type": "track", "originalUrl": "https://pillowcase.su/f/d445be96b2a648423eb7a8277ea5cd3e", "key": "WAKE UP F1LTHY", "description": "OG Filename: pbc 10-30-22 5 F1 TRAV\nOriginal 2022 version of the released track. Has alternate intial takes by <PERSON><PERSON>, and more <PERSON>' adlibs throughout the song. Previewed by <PERSON><PERSON>. Leaked in full as a bonus to a group buy on August 12, 2024. Re-ripped, the file is not the OG File quality.", "date": 17234208, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/6b25c7c68fd823b7130b55796c0eeb2e", "size": "3.03 MB", "duration": 168.96, "originalContent": {"url": "https://pillowcase.su/f/d445be96b2a648423eb7a8277ea5cd3e", "artists": "(feat. <PERSON>) (prod. BNYX & F1LTHY)"}}, {"id": "7a81a56f27e45c41e33fcc24aaf3644b", "title": "HEARD Y'ALL GETTING MONEY", "artists": "(prod. <PERSON>)", "length": "1:29", "ogFilename": null, "notes": "Snippet shared by the same crypto scammer as the one who shared a CDQ snip of 00CACTUS. Most likely finished. Recorded in 2023. Uses the same beat as <PERSON> by <PERSON><PERSON>. Confirmed by an insider to be called <PERSON>ll Getting Money. Was heavily promoted by <PERSON> in many tweets similarly to <PERSON> Day around new year's 2024. Leaked in full on June 15, 2024 with a <PERSON><PERSON> verse edited on it. We have all the vocals but the open is unleaked so it's a partial leak.", "tags": ["Partial"], "aliases": [], "type": "track", "originalUrl": "https://pillowcase.su/f/0d641d479509c02872cbfb7ed9ae898a", "key": "HEARD Y'ALL GETTING MONEY", "description": "Snippet shared by the same crypto scammer as the one who shared a CDQ snip of 00CACTUS. Most likely finished. Recorded in 2023. Uses the same beat as <PERSON> by <PERSON><PERSON>. Confirmed by an insider to be called <PERSON>ll Getting Money. Was heavily promoted by <PERSON> in many tweets similarly to <PERSON> Day around new year's 2024. Leaked in full on June 15, 2024 with a <PERSON><PERSON> verse edited on it. We have all the vocals but the open is unleaked so it's a partial leak.", "date": 17184096, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/7a81a56f27e45c41e33fcc24aaf3644b", "size": "1.83 MB", "duration": 89.47, "originalContent": {"url": "https://pillowcase.su/f/0d641d479509c02872cbfb7ed9ae898a", "artists": "(prod. <PERSON>)"}}]}, {"era": "MUSIC - Cave Sessions", "tracks": [{"id": "86eba88d4c2aca9f326180e14c1337e7", "title": "DOCTOR", "artists": "(prod. F1LTHY, SLOWBURNZ & DJH)", "length": "4:19", "ogFilename": "pbc 6-19-23 2 DY", "notes": "Leaked after a successful 8k GB in Carti Hub. Recorded during the same session as 'POP OUT'. Was originally going to be previewed live instead of 'POP OUT', but the song got replaced, and ultimately scrapped. File is re-ripped.", "tags": [], "aliases": ["(COPS)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e1ac337e50d2e9cded1ff35250a57f3a", "key": "DOCTOR", "description": "OG Filename: pbc 6-19-23 2 <PERSON><PERSON>\nLeaked after a successful 8k GB in Carti Hub. Recorded during the same session as 'POP OUT'. Was originally going to be previewed live instead of 'POP OUT', but the song got replaced, and ultimately scrapped. File is re-ripped.", "date": 17426016, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/86eba88d4c2aca9f326180e14c1337e7", "size": "4.19 MB", "duration": 259.54, "originalContent": {"url": "https://music.froste.lol/song/e1ac337e50d2e9cded1ff35250a57f3a", "artists": "(prod. F1LTHY, SLOWBURNZ & DJH)"}}, {"id": "235ba3eaecd2a8bebf3a1db0b1b39284", "title": "MÉNAGE [V2]", "artists": "(with <PERSON>) (prod. F1LTHY)", "length": "2:51", "ogFilename": null, "notes": "From 2023, the og file we got was fake. Before the song leaked, it was believed to be AI. A new CDQ snippet leaked on April 30, 2024, along with it becoming a GB option for $15,000. Force leaked the same day by Shogani. The version, that leaked is an edit. The real file has only 1:20 of vocals and open and this version has the previous vocals looped over the open instead, making it techincally just partially leaked, as the open is still unleaked. One of the biggest grails of the com before it leaked. The sessions for this song are in the hands of leakers, who say that the song was 'never finished'. TLDR; we have all the vocals to the song, but the leaker just put the vocals from the first verse of the song over the open verse.", "tags": ["Partial"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/5ccfa62a1d4ae0ced6725edd84703ba5", "key": "MÉNAGE", "description": "From 2023, the og file we got was fake. Before the song leaked, it was believed to be AI. A new CDQ snippet leaked on April 30, 2024, along with it becoming a GB option for $15,000. Force leaked the same day by Shogani. The version, that leaked is an edit. The real file has only 1:20 of vocals and open and this version has the previous vocals looped over the open instead, making it techincally just partially leaked, as the open is still unleaked. One of the biggest grails of the com before it leaked. The sessions for this song are in the hands of leakers, who say that the song was 'never finished'.\n\nTLDR; we have all the vocals to the song, but the leaker just put the vocals from the first verse of the song over the open verse.", "date": 17144352, "available": ["Partial", "rgb(255, 255, 255)", "rgb(191, 144, 0)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/235ba3eaecd2a8bebf3a1db0b1b39284", "size": "2.78 MB", "duration": 171.17, "originalContent": {"url": "https://music.froste.lol/song/5ccfa62a1d4ae0ced6725edd84703ba5", "artists": "(with <PERSON>) (prod. F1LTHY)"}}, {"id": "generated_id_MUSIC_-_Cave_Sessions_21_2", "title": "ON B5*", "artists": "(prod. F1LTHY & Lukrative)", "length": null, "ogFilename": "pbc 6-16-23 2 f1", "notes": "Leaked after a successful GB in Carti Hub. Recorded 3 days before Pop Out.", "tags": ["OG File"], "aliases": ["(<PERSON>)"], "originalContent": {"artists": "(prod. F1LTHY & Lukrative)"}}, {"id": "24e2e6cd5f35ff7971a85a299d4d9efe", "title": "<PERSON>ert - Pass It [V4]", "artists": "(with <PERSON><PERSON><PERSON>)", "length": "2:44", "ogFilename": null, "notes": "A throwaway from the 16*29 'Whole Lotta Red' sessions, accidentally uploaded to streaming services on <PERSON><PERSON>'s 'Pink Tape' before being removed. The song was originally on 'Pink Tape' and on tracklists until <PERSON><PERSON> asked for it to be removed around the time of Summer Smash 2023. Although <PERSON><PERSON> recorded his verse back in 2019-2020 during the Whole Lotta Red V2 sessions, <PERSON><PERSON> recorded his verse that would have dropped on Pink Tape on June 16th, 2023 (via URU)", "tags": ["Lossless"], "aliases": ["(Ask Me", "Patience", "<PERSON> Bastard)"], "type": "track", "originalUrl": "https://music.froste.lol/song/f1734c0862a672a3e4f39a9137c1045f/play", "key": "Pass It", "description": "A throwaway from the 16*29 'Whole Lotta Red' sessions, accidentally uploaded to streaming services on <PERSON><PERSON>'s 'Pink Tape' before being removed. The song was originally on 'Pink Tape' and on tracklists until <PERSON><PERSON> asked for it to be removed around the time of Summer Smash 2023. Although <PERSON><PERSON> recorded his verse back in 2019-2020 during the Whole Lotta Red V2 sessions, <PERSON><PERSON> recorded his verse that would have dropped on Pink Tape on June 16th, 2023 (via URU)", "date": 16880832, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/24e2e6cd5f35ff7971a85a299d4d9efe", "size": "2.68 MB", "duration": 164.88, "originalContent": {"url": "https://music.froste.lol/song/f1734c0862a672a3e4f39a9137c1045f/play", "artists": "(with <PERSON><PERSON><PERSON>) "}}, {"id": "generated_id_MUSIC_-_Cave_Sessions_21_4", "title": "Lil Uzi Vert - VETEMENTS JEANS [V1]", "artists": "(with <PERSON><PERSON><PERSON>) (prod. Maaly Raw, Ambezza & OUTOFAIR)", "length": null, "ogFilename": null, "notes": "OG filename (snippet): 09 Vete<PERSON> <PERSON><PERSON> (feat.<PERSON><PERSON> A song played at <PERSON><PERSON>'s birthday event. Said to be recorded in 2021 and Track 9 on an early version of Pink Tape, but it seems though that the voice <PERSON><PERSON> uses in it is his 2023 era voice. This was furtherly confirmed by 'acknowledged1600' and <PERSON>, claiming that it was recorded on June 28, 2023, and engineered by <PERSON>. Seemingly one day before <PERSON><PERSON> recorded some features for the Pink Tape, which of course, didn't end up working at all. 320kbps file leaked on Apr 12, 2024 following a scam gb for a 237 kb/s degraded file earlier. WE LOVE U RAAHIM !!!", "tags": [], "aliases": ["(<PERSON>", "By Myself", "Vetements)"], "originalContent": {"artists": "(with <PERSON><PERSON><PERSON>) (prod. Maaly Raw, Ambezza & OUTOFAIR)"}}, {"id": "5655c4d5c589dee54dd73ef4da18e212", "title": "SURVIVOR", "artists": "(prod. F1LTHY)", "length": "2:34", "ogFilename": "pbc 8-22-23 3 F1", "notes": "Leaked after a successful 9k GB in Cartihub. Made 1 week before 'HOMICIDE55555'.", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/cd392bff6e74199024a7ff71617e27bc/play", "key": "SURVIVOR", "description": "OG Filename: pbc 8-22-23 3 F1\nLeaked after a successful 9k GB in Cartihub. Made 1 week before 'HOMICIDE55555'.", "date": 17424288, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "url": "https://api.pillowcase.su/api/download/5655c4d5c589dee54dd73ef4da18e212", "size": "2.51 MB", "duration": 154.35, "originalContent": {"url": "https://music.froste.lol/song/cd392bff6e74199024a7ff71617e27bc/play", "artists": "(prod. F1LTHY)"}}, {"id": "generated_id_MUSIC_-_Cave_Sessions_21_6", "title": "PROBLEM CHILD [V1]", "artists": "(with <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>z, TyMaz & Ambezza)", "length": null, "ogFilename": "pbc X trav 3 8.17.23_1", "notes": "A song played at <PERSON><PERSON>'s birthday event and recorded on August 17th. Was speculated to release on a summer copy of MUSIC. Played again at an event, speculated to be a private Listening Party for 'I AM MUSIC' . Posted by Homixide Beno! on Jan 28, 2024, although previewing no new parts. A CDQ snippet leaked on Jun 8, 2024. Another CDQ snippet leaked on Jun 12, 2024. <PERSON> was supposed to preview this on his radioshow on <PERSON><PERSON> birthday, but <PERSON><PERSON> did not let him, than again when BACKR00MS dropped, but <PERSON><PERSON> didn't let him again. Filename seen on DJ<PERSON>'s phone Leaked on Jun 12, 2024 by the CARTIONSOLANA scammers. <PERSON> is part of the song not edited. As the OGF name implies there have been more songs recorded with travis during this session or alt mixes (atleast 2 more)", "tags": [], "aliases": ["(00Cactus)"], "type": "track", "originalUrl": "http://music.froste.lol/song/c8e63a5e43d8d8fb5a562109735b46c6/play", "key": "PROBLEM CHILD", "description": "OG Filename: pbc X trav 3 8.17.23_1\nA song played at <PERSON><PERSON>'s birthday event and recorded on August 17th. Was speculated to release on a summer copy of MUSIC. Played again at an event, speculated to be a private Listening Party for 'I AM MUSIC' . Posted by Homixide Beno! on Jan 28, 2024, although previewing no new parts. A CDQ snippet leaked on Jun 8, 2024. Another CDQ snippet leaked on Jun 12, 2024. <PERSON> Izzo was supposed to preview this on his radioshow on <PERSON><PERSON> birthday, but <PERSON><PERSON> did not let him, than again when BACKR00MS dropped, but <PERSON><PERSON> didn't let him again. Filename seen on <PERSON><PERSON>'s phone Leaked on Jun 12, 2024 by the CARTIONSOLANA scammers. <PERSON> is part of the song not edited. As the OGF name implies there have been more songs recorded with travis during this session or alt mixes (atleast 2 more)", "date": 16945632, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "http://music.froste.lol/song/c8e63a5e43d8d8fb5a562109735b46c6/play", "artists": "(with <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>z, TyMaz & Ambezza)"}}]}, {"era": "MUSIC V2 - I AM MUSIC Sessions", "tracks": [{"id": "generated_id_MUSIC_V2_-_I_AM_MUSIC_Sessions_22_0", "title": "PISSY PAMPER [Remix/RL Miami version]", "artists": null, "length": null, "ogFilename": null, "notes": "Version played during <PERSON><PERSON>'s Rolling Loud performance in Miami", "tags": ["Performance"], "aliases": []}, {"id": "generated_id_MUSIC_V2_-_I_AM_MUSIC_Sessions_22_1", "title": "LONG TIME [Remix/Wireless version]", "artists": null, "length": null, "ogFilename": null, "notes": "Version played during <PERSON><PERSON>'s Wireless performance in London", "tags": ["Performance"], "aliases": []}, {"id": "generated_id_MUSIC_V2_-_I_AM_MUSIC_Sessions_22_2", "title": "POP YO SHI TWIN", "artists": "(feat. <PERSON>) (prod. F1LTHY)", "length": null, "ogFilename": null, "notes": "Snippet leaked on November 13th, 2024. Recorded sometime in 2024. It would be revealed, that the real name of the song is 'POP YO SHI TWIN', on April 2, 2025. Has a short yatchy feature. It was also stated that there other songs made with <PERSON><PERSON>, in response to this one not being good enough.", "tags": [], "aliases": ["(DOYA)"], "type": "track", "originalUrl": "https://music.froste.lol/song/e73277952ee463b9a7293e132c295efc", "key": "POP YO SHI TWIN", "description": "Snippet leaked on November 13th, 2024. Recorded sometime in 2024. It would be revealed, that the real name of the song is 'POP YO SHI TWIN', on April 2, 2025. Has a short yatchy feature. It was also stated that there other songs made with <PERSON><PERSON>, in response to this one not being good enough.", "date": 17445024, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["CD Quality", "rgb(255, 255, 255)", "rgb(76, 175, 80)"], "originalContent": {"url": "https://music.froste.lol/song/e73277952ee463b9a7293e132c295efc", "artists": "(feat. <PERSON>) (prod. F1LTHY)"}}, {"id": "generated_id_MUSIC_V2_-_I_AM_MUSIC_Sessions_22_3", "title": "H00DBYAIR [V2]", "artists": "(prod. <PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "A single dropped on <PERSON><PERSON>'s opium_00pium IG. High bitrate rip of H00DBYAIR. Beat was meant for <PERSON><PERSON>'s mixtapes \"Candyman,\" but was given to <PERSON><PERSON> some time in 2023 likely. Has 5 additional lines cut from the streaming release for some strange reason (none of them were offensive either...)", "tags": [], "aliases": ["(HBA", "Tundra)"], "type": "track", "originalUrl": "http://music.froste.lol/song/871a9d1f7151993bc6df87d048cdccc2/play", "key": "H00DBYAIR", "description": "A single dropped on <PERSON><PERSON>'s opium_00pium IG. High bitrate rip of H00DBYAIR. Beat was meant for <PERSON><PERSON>'s mixtapes \"Candyman,\" but was given to <PERSON><PERSON> some time in 2023 likely. Has 5 additional lines cut from the streaming release for some strange reason (none of them were offensive either...)", "date": 17030304, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "http://music.froste.lol/song/871a9d1f7151993bc6df87d048cdccc2/play", "artists": "(prod. <PERSON><PERSON>)"}}]}, {"era": "MUSIC V3 - Final Sessions", "tracks": [{"id": "f196e25f495b4a104199a3cdb4478726", "title": "<PERSON><PERSON> - <PERSON><PERSON> [V3]", "artists": "(feat. <PERSON><PERSON><PERSON>)", "length": "3:00", "ogFilename": null, "notes": "Original mix, which was released on the 28th, was later changed to match the MV version of the song. It includes <PERSON><PERSON>'s adlibs from the trailer snippet.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/cf2fa6888dc0af66a4c22e64b5ab5fc9/play", "key": "<PERSON><PERSON>", "description": "Original mix, which was released on the 28th, was later changed to match the MV version of the song. It includes <PERSON><PERSON>'s adlibs from the trailer snippet.", "date": 17379360, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/f196e25f495b4a104199a3cdb4478726", "size": "2.92 MB", "duration": 180.12, "originalContent": {"url": "https://music.froste.lol/song/cf2fa6888dc0af66a4c22e64b5ab5fc9/play", "artists": "(feat. <PERSON><PERSON><PERSON>)"}}, {"id": "generated_id_MUSIC_V3_-_Final_Sessions_23_1", "title": "KETAMINE (Remix/Festival Version)", "artists": "(prod. <PERSON><PERSON>)", "length": null, "ogFilename": null, "notes": "ISSA SPORT", "tags": ["Performance"], "aliases": ["(Issa Sport)"], "type": "track", "originalUrl": "http://music.froste.lol/song/44bbc7f6d5a5db728373672d9851e065/play", "key": "KETAMINE (Remix/Festival Version)", "description": "ISSA SPORT", "date": 17184096, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Performance", "rgb(0, 0, 0)", "rgb(22, 22, 29)"], "originalContent": {"url": "http://music.froste.lol/song/44bbc7f6d5a5db728373672d9851e065/play", "artists": "(prod. <PERSON><PERSON>)"}}, {"id": "generated_id_MUSIC_V3_-_Final_Sessions_23_2", "title": "DIFFERENT DAY (Remix/Festival Version)", "artists": "(prod. KP Beatz, Ambezza & OUTOFAIR)", "length": null, "ogFilename": null, "notes": "Festival version of DIFFERENT DAY.", "tags": ["Performance"], "aliases": [], "type": "track", "originalUrl": "http://music.froste.lol/song/e07643081ec89d4ec08300158ab8aa25/play", "key": "DIFFERENT DAY (Remix/Festival Version)", "description": "Festival version of DIFFERENT DAY.", "date": 17184096, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Performance", "rgb(0, 0, 0)", "rgb(22, 22, 29)"], "originalContent": {"url": "http://music.froste.lol/song/e07643081ec89d4ec08300158ab8aa25/play", "artists": "(prod. KP Beatz, Ambezza & OUTOFAIR)"}}, {"id": "40e3e829aedae19d5c6562155f89b792", "title": "PLAY THIS [V2]", "artists": "(prod. <PERSON><PERSON><PERSON> & V-Ron)", "length": "1:24", "ogFilename": null, "notes": "On November 21, 2024, <PERSON><PERSON><PERSON> released a new track titled “PLAY THIS” through his finsta account, @opium_00pium. <PERSON><PERSON> also announced that he would also be dropping more music on Friday, November 22, 2024. A tweet from <PERSON><PERSON> from August 10, 2023 cited some of the lyrics from this song.", "tags": [], "aliases": [], "type": "track", "originalUrl": "https://music.froste.lol/song/2d2ae80c1c367aaccdaa29774c34500a", "key": "PLAY THIS", "description": "On November 21, 2024, <PERSON><PERSON><PERSON> released a new track titled “PLAY THIS” through his finsta account, @opium_00pium. <PERSON><PERSON> also announced that he would also be dropping more music on Friday, November 22, 2024. A tweet from <PERSON><PERSON> from August 10, 2023 cited some of the lyrics from this song.", "date": ********, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "url": "https://api.pillowcase.su/api/download/40e3e829aedae19d5c6562155f89b792", "size": "1.4 MB", "duration": 84.79, "originalContent": {"url": "https://music.froste.lol/song/2d2ae80c1c367aaccdaa29774c34500a", "artists": "(prod. <PERSON><PERSON><PERSON> & V-Ron)"}}]}, {"era": "VULTURES", "tracks": [{"id": "12adcebdb429ca4a0834c14a5fef9ece", "title": "¥$ - MELROSE [V3]", "artists": "(feat. <PERSON><PERSON><PERSON>) (Prod. TheLabCook & OjiVolta)", "length": "5:29", "ogFilename": "mel<PERSON>_", "notes": "211 (a known and reliable seller) offered the song which is over 5 minute long, with a longer <PERSON><PERSON> verse version for groupbuy, confirming its existence. A snippet later posted by <PERSON><PERSON> and the song has been added to the MELROSE buy. Has fuller chorus, <PERSON> is doing more than just adlibs and one line and <PERSON><PERSON>'s verse is 3 times longer. Mixed terribly by <PERSON>. Features a line where <PERSON><PERSON> disses Ye - \"Cock it back aim it a nazi\".", "tags": ["OG File", "Lossless"], "aliases": ["(Codeine)"], "type": "track", "originalUrl": "https://music.froste.lol/song/c5828c146386868b337df1a290ded5e3/play", "key": "MELROSE", "description": "OG Filename: me<PERSON>rose_\n<PERSON> (a known and reliable seller) offered the song which is over 5 minute long, with a longer <PERSON><PERSON> verse version for groupbuy, confirming its existence. A snippet later posted by <PERSON><PERSON> and the song has been added to the MELROSE buy. Has fuller chorus, <PERSON> is doing more than just adlibs and one line and <PERSON><PERSON>'s verse is 3 times longer. Mixed terribly by <PERSON>. Features a line where <PERSON><PERSON> disses Ye - \"Cock it back aim it a nazi\".", "date": 17205696, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/12adcebdb429ca4a0834c14a5fef9ece", "size": "5.44 MB", "duration": 329.4, "originalContent": {"url": "https://music.froste.lol/song/c5828c146386868b337df1a290ded5e3/play", "artists": "(feat. <PERSON><PERSON><PERSON>) (Prod. TheLabCook & OjiVolta)"}}, {"id": "7d9f54412b4f3c626fd7886108423dc2", "title": "¥$ - FIELD TRIP [V25]", "artists": "(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Ojivolta & The Legendary Traxster)", "length": "3:12", "ogFilename": "FIELD TRIP YE REF (5.7.24)", "notes": "Version with the newly recorded Durk verse, different mixing, and a mumble Ye verse. Has no <PERSON><PERSON>.", "tags": ["Lossless"], "aliases": ["(DISCONNECTED", "All Yours)"], "type": "track", "originalUrl": "https://music.froste.lol/song/9292f1ee722b944a3282187f1e00aa86/play", "key": "FIELD TRIP", "description": "OG Filename: FIELD TRIP YE REF (5.7.24)\nVersion with the newly recorded Durk verse, different mixing, and a mumble Ye verse. Has no <PERSON><PERSON>.", "date": 17226432, "available": ["Full", "rgb(255, 255, 255)", "rgb(7, 55, 99)"], "quality": ["Lossless", "rgb(243, 243, 243)", "rgb(69, 188, 255)"], "url": "https://api.pillowcase.su/api/download/7d9f54412b4f3c626fd7886108423dc2", "size": "3.26 MB", "duration": 192.91, "originalContent": {"url": "https://music.froste.lol/song/9292f1ee722b944a3282187f1e00aa86/play", "artists": "(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Ojivolta & The Legendary Traxster)"}}]}, {"era": "004KT", "tracks": [{"id": "generated_id_004KT_25_0", "title": "Brand New [V2]", "artists": "(prod. BNYX & HK)", "length": null, "ogFilename": "Brand New v2_YB_HK_Bynx", "notes": "Rumured to be for 004KT with an open verse meant for <PERSON><PERSON>", "tags": ["OG File"], "aliases": [], "type": "track", "originalUrl": "http://music.froste.lol/song/b65e85b07b95c8e43147a60c094ee59e/play", "key": "Brand New", "description": "OG Filename: Brand New v2_YB_HK_Bynx\nRumured to be for 004KT with an open verse meant for <PERSON><PERSON>", "date": 17289504, "available": ["OG File", "rgb(255, 255, 255)", "rgb(22, 22, 29)"], "quality": ["High Quality", "rgb(0, 0, 0)", "rgb(255, 193, 7)"], "originalContent": {"url": "http://music.froste.lol/song/b65e85b07b95c8e43147a60c094ee59e/play", "artists": "(prod. BNYX & HK)"}}]}]