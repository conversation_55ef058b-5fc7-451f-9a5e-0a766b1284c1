// SecurityUtils.js - 安全工具函数和URL验证

// 支持的音频平台白名单
const AUDIO_PLATFORMS_WHITELIST = [
  'spotify.com', 'open.spotify.com',
  'music.apple.com',
  'youtube.com', 'youtu.be', 'www.youtube.com',
  'soundcloud.com', 'www.soundcloud.com'
];

// 支持的图片平台白名单
const IMAGE_PLATFORMS_WHITELIST = [
  'imgur.com', 'i.imgur.com',
  'flickr.com', 'www.flickr.com',
  'photos.google.com',
  'ibb.co', 'i.ibb.co',
  'postimg.cc', 'i.postimg.cc'
];

// 用户输入净化函数
export function sanitizeUserInput(input) {
  if (typeof input !== 'string') return '';
  
  // 移除HTML标签
  let sanitized = input.replace(/<[^>]*>/g, '');
  
  // HTML实体编码
  sanitized = sanitized
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
  
  // 移除潜在的JavaScript事件处理器
  sanitized = sanitized.replace(/on\w+=/gi, '');
  
  // 移除data:URI和javascript:伪协议
  sanitized = sanitized.replace(/data:/gi, 'invalid:');
  sanitized = sanitized.replace(/javascript:/gi, 'invalid:');
  
  return sanitized.trim();
}

// 基础URL验证函数
export function validateExternalUrl(url, type) {
  try {
    const parsedUrl = new URL(url);
    
    // 仅允许HTTPS协议
    if (parsedUrl.protocol !== 'https:') {
      return {
        valid: false,
        reason: 'Only HTTPS links are supported'
      };
    }
    
    // 域名白名单验证
    const whitelist = type === 'audio' 
      ? AUDIO_PLATFORMS_WHITELIST 
      : IMAGE_PLATFORMS_WHITELIST;
      
    const domainValid = whitelist.some(domain => 
      parsedUrl.hostname === domain || 
      parsedUrl.hostname.endsWith('.' + domain)
    );
    
    if (!domainValid) {
      return {
        valid: false,
        reason: `Unsupported ${type === 'audio' ? 'music' : 'image'} platform`
      };
    }
    
    // 路径验证
    if (!parsedUrl.pathname || parsedUrl.pathname === '/') {
      return {
        valid: false,
        reason: 'Invalid link path'
      };
    }
    
    return {
      valid: true,
      normalizedUrl: parsedUrl.toString()
    };
    
  } catch (error) {
    return {
      valid: false,
      reason: 'Invalid URL format'
    };
  }
}

// Spotify链接验证
export function validateSpotifyUrl(url) {
  const spotifyPattern = /^https:\/\/open\.spotify\.com\/(track|album|playlist)\/[a-zA-Z0-9]{22}(\?.*)?$/;
  
  if (!spotifyPattern.test(url)) {
    return {
      valid: false,
      reason: 'Invalid Spotify link format'
    };
  }
  
  const matches = url.match(/\/(track|album|playlist)\/([a-zA-Z0-9]{22})/);
  const contentType = matches[1];
  const contentId = matches[2];
  
  return {
    valid: true,
    metadata: {
      platform: 'spotify',
      contentType,
      contentId,
      normalizedUrl: `https://open.spotify.com/${contentType}/${contentId}`
    }
  };
}

// YouTube链接验证
export function validateYouTubeUrl(url) {
  // 处理短链接
  let normalizedUrl = url;
  if (url.includes('youtu.be')) {
    const videoId = url.split('/').pop().split('?')[0];
    normalizedUrl = `https://www.youtube.com/watch?v=${videoId}`;
  }
  
  const youtubePattern = /^https:\/\/(www\.)?youtube\.com\/watch\?v=[a-zA-Z0-9_-]{11}(&.*)?$/;
  
  if (!youtubePattern.test(normalizedUrl)) {
    return {
      valid: false,
      reason: 'Invalid YouTube link format'
    };
  }
  
  const videoId = normalizedUrl.match(/v=([a-zA-Z0-9_-]{11})/)[1];
  
  return {
    valid: true,
    metadata: {
      platform: 'youtube',
      contentType: 'video',
      contentId: videoId,
      normalizedUrl: `https://www.youtube.com/watch?v=${videoId}`
    }
  };
}

// Apple Music链接验证
export function validateAppleMusicUrl(url) {
  const applePattern = /^https:\/\/music\.apple\.com\/[a-z]{2}\/(album|song)\/[^\/]+\/\d+(\?.*)?$/;
  
  if (!applePattern.test(url)) {
    return {
      valid: false,
      reason: 'Invalid Apple Music link format'
    };
  }
  
  return {
    valid: true,
    metadata: {
      platform: 'apple-music',
      contentType: 'music',
      normalizedUrl: url.split('?')[0] // 移除查询参数
    }
  };
}

// SoundCloud链接验证
export function validateSoundCloudUrl(url) {
  const soundcloudPattern = /^https:\/\/(www\.)?soundcloud\.com\/[^\/]+\/[^\/]+(\?.*)?$/;
  
  if (!soundcloudPattern.test(url)) {
    return {
      valid: false,
      reason: 'Invalid SoundCloud link format'
    };
  }
  
  return {
    valid: true,
    metadata: {
      platform: 'soundcloud',
      contentType: 'track',
      normalizedUrl: url.split('?')[0]
    }
  };
}

// 图片链接验证
export async function validateImageUrl(url) {
  const urlValidation = validateExternalUrl(url, 'image');
  if (!urlValidation.valid) {
    return urlValidation;
  }
  
  try {
    // 使用HEAD请求检查内容类型
    const response = await fetch(url, { 
      method: 'HEAD',
      mode: 'cors'
    });
    
    if (!response.ok) {
      return {
        valid: false,
        reason: 'Unable to access image link'
      };
    }
    
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.startsWith('image/')) {
      return {
        valid: false,
        reason: 'Link is not a valid image'
      };
    }
    
    return {
      valid: true,
      metadata: {
        contentType,
        normalizedUrl: urlValidation.normalizedUrl
      }
    };
  } catch (error) {
    // 如果CORS失败，仍然允许链接但给出警告
    return {
      valid: true,
      metadata: {
        contentType: 'image/unknown',
        normalizedUrl: urlValidation.normalizedUrl
      },
      warning: 'Could not verify image format due to CORS restrictions'
    };
  }
}

// 主要的内容URL验证函数
export async function validateContentUrl(url, contentType) {
  if (!url || !url.startsWith('https://')) {
    return {
      valid: false,
      reason: 'Link must use HTTPS'
    };
  }
  
  if (contentType === 'song') {
    if (url.includes('spotify.com')) {
      return validateSpotifyUrl(url);
    } else if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return validateYouTubeUrl(url);
    } else if (url.includes('music.apple.com')) {
      return validateAppleMusicUrl(url);
    } else if (url.includes('soundcloud.com')) {
      return validateSoundCloudUrl(url);
    } else {
      return {
        valid: false,
        reason: 'Unsupported music platform'
      };
    }
  } else if (contentType === 'image') {
    return await validateImageUrl(url);
  }
  
  return {
    valid: false,
    reason: 'Content type not selected'
  };
}

// 生成唯一提交ID
export function generateSubmissionId() {
  return 'sub_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 安全的本地存储操作
export function secureLocalStorage() {
  return {
    save(key, data) {
      try {
        const sanitizedData = {
          ...data,
          title: sanitizeUserInput(data.title || ''),
          description: sanitizeUserInput(data.description || ''),
          tags: (data.tags || []).map(tag => sanitizeUserInput(tag)).filter(Boolean)
        };
        
        const existing = JSON.parse(localStorage.getItem(key) || '[]');
        existing.unshift(sanitizedData);
        
        // 限制存储数量
        if (existing.length > 100) {
          existing.splice(100);
        }
        
        localStorage.setItem(key, JSON.stringify(existing));
        return true;
      } catch (error) {
        console.error('Local storage save failed:', error);
        return false;
      }
    },
    
    get(key) {
      try {
        return JSON.parse(localStorage.getItem(key) || '[]');
      } catch (error) {
        console.error('Local storage get failed:', error);
        return [];
      }
    }
  };
}
