// DynamicDataLoader.js - 动态数据加载和管理

class DynamicDataLoader {
  constructor() {
    this.artistsData = null;
    this.categoriesData = {};
    this.erasData = {};
  }

  // 加载艺术家数据
  async loadArtistsData() {
    if (!this.artistsData) {
      try {
        const response = await fetch('/src/data/artists.json');
        if (response.ok) {
          this.artistsData = await response.json();
        } else {
          // 如果无法加载，使用硬编码的基础数据
          this.artistsData = {
            artists: [
              {
                id: 'ye',
                name: 'Ye',
                categories: [
                  { id: 'released', name: 'Released', hasData: true },
                  { id: 'unreleased', name: 'Unreleased', hasData: true },
                  { id: 'art', name: 'Art', hasData: true },
                  { id: 'recent', name: 'Recent', hasData: true }
                ]
              },
              {
                id: 'playboi-carti',
                name: '<PERSON><PERSON><PERSON>',
                categories: [
                  { id: 'released', name: 'Released', hasData: true },
                  { id: 'unreleased', name: 'Unreleased', hasData: true },
                  { id: 'art', name: 'Art', hasData: true },
                  { id: 'recent', name: 'Recent', hasData: true }
                ]
              }
            ]
          };
        }
      } catch (error) {
        console.error('Failed to load artists data:', error);
        // 使用硬编码的基础数据作为回退
        this.artistsData = {
          artists: [
            {
              id: 'ye',
              name: 'Ye',
              categories: [
                { id: 'released', name: 'Released', hasData: true },
                { id: 'unreleased', name: 'Unreleased', hasData: true },
                { id: 'art', name: 'Art', hasData: true },
                { id: 'recent', name: 'Recent', hasData: true }
              ]
            },
            {
              id: 'playboi-carti',
              name: 'Playboi Carti',
              categories: [
                { id: 'released', name: 'Released', hasData: true },
                { id: 'unreleased', name: 'Unreleased', hasData: true },
                { id: 'art', name: 'Art', hasData: true },
                { id: 'recent', name: 'Recent', hasData: true }
              ]
            }
          ]
        };
      }
    }
    return this.artistsData;
  }

  // 获取艺术家的分类
  async getArtistCategories(artistId) {
    const artistsData = await this.loadArtistsData();
    const artist = artistsData.artists.find(a => a.id === artistId);
    return artist ? artist.categories : [];
  }

  // 获取分类的专辑/时期
  async getCategoryEras(artistId, categoryId) {
    const cacheKey = `${artistId}-${categoryId}`;
    
    if (!this.erasData[cacheKey]) {
      try {
        // 尝试加载真实的时期数据
        const response = await fetch(`/public/data/artists/${artistId}/categories/${categoryId}/eras.json`);
        if (response.ok) {
          this.erasData[cacheKey] = await response.json();
        } else {
          // 使用示例数据
          this.erasData[cacheKey] = this.getDefaultEras(artistId, categoryId);
        }
      } catch (error) {
        console.warn(`Failed to load eras for ${artistId}/${categoryId}:`, error);
        this.erasData[cacheKey] = this.getDefaultEras(artistId, categoryId);
      }
    }
    
    return this.erasData[cacheKey];
  }

  // 获取默认的时期数据
  getDefaultEras(artistId, categoryId) {
    const defaultEras = {
      'ye': {
        'released': [
          { id: 'graduation', name: 'Graduation' },
          { id: 'mbdtf', name: 'My Beautiful Dark Twisted Fantasy' },
          { id: 'yeezus', name: 'Yeezus' },
          { id: 'pablo', name: 'The Life of Pablo' },
          { id: 'ye', name: 'ye' },
          { id: 'ksg', name: 'Kids See Ghosts' },
          { id: 'jik', name: 'Jesus Is King' },
          { id: 'donda', name: 'Donda' }
        ],
        'unreleased': [
          { id: 'ww3', name: 'WW3' },
          { id: 'bully', name: 'Bully' },
          { id: 'turbografx16', name: 'TurboGrafx16' },
          { id: 'yandhi', name: 'Yandhi' },
          { id: 'donda2', name: 'Donda 2' }
        ],
        'recent': [
          { id: 'ww3', name: 'WW3' },
          { id: 'bully', name: 'Bully' }
        ]
      },
      'playboi-carti': {
        'released': [
          { id: 'self-titled', name: 'Playboi Carti' },
          { id: 'die-lit', name: 'Die Lit' },
          { id: 'wlr', name: 'Whole Lotta Red' }
        ],
        'unreleased': [
          { id: 'narcissist', name: 'Narcissist' },
          { id: 'music', name: 'Music' },
          { id: 'v2', name: 'V2' }
        ],
        'recent': [
          { id: 'music', name: 'Music' },
          { id: 'narcissist', name: 'Narcissist' }
        ]
      }
    };

    return defaultEras[artistId]?.[categoryId] || [];
  }

  // 更新分类选择器
  async updateCategorySelector(artistId) {
    const categorySelect = document.getElementById('category-select');
    if (!categorySelect) return;

    const categories = await this.getArtistCategories(artistId);
    
    // 清空现有选项
    categorySelect.innerHTML = '<option value="">Select Category</option>';
    
    // 添加分类选项
    categories.forEach(category => {
      const option = document.createElement('option');
      option.value = category.id;
      option.textContent = category.name;
      categorySelect.appendChild(option);
    });
    
    // 启用选择器
    categorySelect.disabled = false;
    
    // 如果有预填数据，设置选中值
    const formManager = window.formContextManager;
    if (formManager && formManager.formData.categoryId) {
      categorySelect.value = formManager.formData.categoryId;
      this.updateEraSelector(artistId, formManager.formData.categoryId);
    }
  }

  // 更新专辑/时期选择器
  async updateEraSelector(artistId, categoryId) {
    const eraSelect = document.getElementById('era-select');
    const eraGroup = document.getElementById('era-group');
    
    if (!eraSelect || !eraGroup) return;

    const eras = await this.getCategoryEras(artistId, categoryId);
    
    // 清空现有选项
    eraSelect.innerHTML = '<option value="">Select existing or create new</option>';
    
    // 添加专辑/时期选项
    eras.forEach(era => {
      const option = document.createElement('option');
      option.value = era.id;
      option.textContent = era.name;
      eraSelect.appendChild(option);
    });
    
    // 显示专辑/时期选择组
    eraGroup.style.display = 'block';
    
    // 如果有预填数据，设置选中值
    const formManager = window.formContextManager;
    if (formManager && formManager.formData.eraId) {
      eraSelect.value = formManager.formData.eraId;
    }
  }

  // 获取艺术家名称
  getArtistName(artistId) {
    const artistNames = {
      'ye': 'Ye (Kanye West)',
      'playboi-carti': 'Playboi Carti'
    };
    return artistNames[artistId] || artistId;
  }

  // 获取分类名称
  getCategoryName(categoryId) {
    const categoryNames = {
      'released': 'Released',
      'unreleased': 'Unreleased',
      'art': 'Art',
      'recent': 'Recent',
      'best-of': 'Best Of'
    };
    return categoryNames[categoryId] || categoryId;
  }

  // 从URL获取艺术家ID
  getArtistIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    const artistIndex = pathParts.indexOf('artists');
    return artistIndex >= 0 && pathParts.length > artistIndex + 1 
      ? pathParts[artistIndex + 1] 
      : '';
  }

  // 从URL获取分类ID
  getCategoryIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    const artistIndex = pathParts.indexOf('artists');
    return artistIndex >= 0 && pathParts.length > artistIndex + 2 
      ? pathParts[artistIndex + 2] 
      : '';
  }

  // 预填表单数据基于当前页面
  getContextualPrefilledData() {
    const artistId = this.getArtistIdFromUrl();
    const categoryId = this.getCategoryIdFromUrl();
    
    return {
      artistId: artistId || '',
      categoryId: categoryId || ''
    };
  }
}

// 导出单例实例
export const dynamicDataLoader = new DynamicDataLoader();

// 也将其设置为全局变量以便在其他脚本中使用
if (typeof window !== 'undefined') {
  window.dynamicDataLoader = dynamicDataLoader;
}
