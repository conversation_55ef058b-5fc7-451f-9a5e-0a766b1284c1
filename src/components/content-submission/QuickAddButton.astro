---
// QuickAddButton.astro - 快速添加内容按钮组件
export interface Props {
  variant?: 'primary' | 'secondary' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  artistId?: string;
  categoryId?: string;
  className?: string;
  text?: string;
}

const { 
  variant = 'primary', 
  size = 'md', 
  artistId = '', 
  categoryId = '', 
  className = '',
  text = 'Add Content'
} = Astro.props;

const sizeClasses = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-2.5 text-sm',
  lg: 'px-6 py-3 text-base'
};

const variantClasses = {
  primary: 'bg-primary hover:bg-primary-hover text-white border-primary',
  secondary: 'bg-transparent hover:bg-white/10 text-white border-white/30 hover:border-white/50',
  icon: 'bg-primary/10 hover:bg-primary/20 text-primary border-primary/30 hover:border-primary w-10 h-10 p-0 rounded-full'
};
---

<button 
  class={`quick-add-btn inline-flex items-center justify-center border rounded-lg font-medium transition-all duration-200 ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
  data-action="add-content"
  data-artist-id={artistId}
  data-category-id={categoryId}
  title={variant === 'icon' ? text : undefined}
  aria-label={variant === 'icon' ? text : undefined}
>
  {variant === 'icon' ? (
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
  ) : (
    <>
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      {text}
    </>
  )}
</button>

<style>
  .quick-add-btn {
    cursor: pointer;
    user-select: none;
  }

  .quick-add-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.25);
  }

  .quick-add-btn:active {
    transform: translateY(0);
  }

  .quick-add-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .quick-add-btn:disabled:hover {
    transform: none;
    box-shadow: none;
  }

  /* 确保在深色背景下的可见性 */
  .quick-add-btn.primary {
    background: #16a34a;
    border-color: #16a34a;
  }

  .quick-add-btn.primary:hover {
    background: #22c55e;
    border-color: #22c55e;
  }

  .quick-add-btn.secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
  }

  .quick-add-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .quick-add-btn.icon {
    background: rgba(29, 185, 84, 0.1);
    color: #1DB954;
    border-color: rgba(29, 185, 84, 0.3);
  }

  .quick-add-btn.icon:hover {
    background: rgba(29, 185, 84, 0.2);
    border-color: #1DB954;
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .quick-add-btn:not(.icon) {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
    
    .quick-add-btn:not(.icon) svg {
      width: 1rem;
      height: 1rem;
    }
  }
</style>

<script>
  // 为所有快速添加按钮添加事件监听器
  document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.quick-add-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        
        const artistId = button.dataset.artistId || '';
        const categoryId = button.dataset.categoryId || '';
        
        // 打开内容提交模态框
        if (typeof window.openContentSubmissionModal === 'function') {
          window.openContentSubmissionModal({
            artistId,
            categoryId
          });
        } else {
          console.warn('Content submission modal not available');
        }
      });
    });
  });
</script>
