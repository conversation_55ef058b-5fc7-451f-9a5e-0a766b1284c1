---
/**
 * DownloadModal.astro - 弹窗式下载进度显示
 * 暗黑风格，带呼吸灯效果
 */
---

<!-- iOS最新玻璃效果下载弹窗 -->
<div id="download-modal" class="download-modal hidden">
  <div class="modal-backdrop"></div>
  <div class="modal-content">
    <!-- 歌曲信息 -->
    <div class="track-info">
      <div class="track-title" id="download-track-title">Unknown Track</div>
    </div>

    <!-- iOS风格进度圆环 -->
    <div class="ios-progress-circle">
      <!-- 呼吸灯背景 -->
      <div class="breathing-glow"></div>

      <!-- 进度环 -->
      <svg class="progress-ring" viewBox="0 0 80 80">
        <circle class="progress-track" cx="40" cy="40" r="35"/>
        <circle class="progress-fill" cx="40" cy="40" r="35"/>
      </svg>

      <!-- 中心百分比 -->
      <div class="progress-text" id="download-progress-percent">0%</div>
    </div>

    <!-- 取消按钮 -->
    <button class="cancel-button" id="cancel-download">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>
</div>

<style>
  .download-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
  }

  .modal-content {
    position: relative;
    width: 240px;
    height: 240px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    animation: modalFadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    backdrop-filter: blur(60px) saturate(1.9) brightness(1.1);
    -webkit-backdrop-filter: blur(60px) saturate(1.9) brightness(1.1);
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.08),
      0 4px 16px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .track-info {
    text-align: center;
    width: 180px;
    margin-bottom: 8px;
  }

  .track-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .ios-progress-circle {
    position: relative;
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .breathing-glow {
    position: absolute;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(29, 185, 84, 0.3) 0%, rgba(29, 185, 84, 0.15) 40%, rgba(29, 185, 84, 0.03) 70%, transparent 100%);
    animation: breathingGlow 3s ease-in-out infinite;
  }

  @keyframes breathingGlow {
    0%, 100% {
      transform: scale(0.8);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.7;
    }
  }

  .progress-ring {
    width: 90px;
    height: 90px;
    transform: rotate(-90deg);
    position: relative;
    z-index: 2;
  }

  .progress-track {
    fill: none;
    stroke: rgba(255, 255, 255, 0.08);
    stroke-width: 2.5;
  }

  .progress-fill {
    fill: none;
    stroke: #1DB954;
    stroke-width: 2.5;
    stroke-linecap: round;
    stroke-dasharray: 251.33;
    stroke-dashoffset: 251.33;
    transition: stroke-dashoffset 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 0 6px rgba(29, 185, 84, 0.4));
  }

  .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 600;
    font-variant-numeric: tabular-nums;
    z-index: 3;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .cancel-button {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .cancel-button:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.18);
    transform: scale(1.05);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  .cancel-button svg {
    width: 16px;
    height: 16px;
    stroke: rgba(255, 255, 255, 0.8);
    stroke-width: 2;
    transition: stroke 0.3s ease;
  }

  .cancel-button:hover svg {
    stroke: rgba(255, 255, 255, 0.95);
  }

  .hidden {
    display: none !important;
  }

  /* 响应式设计 */
  @media (max-width: 640px) {
    .modal-content {
      width: 200px;
      height: 200px;
      border-radius: 28px;
    }

    .track-info {
      width: 160px;
    }

    .track-title {
      font-size: 13px;
    }

    .ios-progress-circle {
      width: 75px;
      height: 75px;
    }

    .breathing-glow {
      width: 110px;
      height: 110px;
    }

    .progress-ring {
      width: 75px;
      height: 75px;
    }

    .progress-text {
      font-size: 14px;
    }

    .cancel-button {
      width: 32px;
      height: 32px;
    }

    .cancel-button svg {
      width: 14px;
      height: 14px;
    }
  }
</style>
