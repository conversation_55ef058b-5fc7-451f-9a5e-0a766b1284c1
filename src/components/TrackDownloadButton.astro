---
/**
 * TrackDownloadButton.astro - 曲目下载按钮组件
 * 集成到现有的曲目列表中
 */

interface Props {
  track: {
    id: string;
    title: string;
    artists?: string | string[];
    url?: string;
    originalUrl?: string;
    originalContent?: any;
    size?: string;
  };
  className?: string;
}

const { track, className = "" } = Astro.props;

// 获取下载URL，优先使用url，然后是originalUrl
const downloadUrl = track.url || track.originalUrl;
const trackId = track.id || `track_${Math.random().toString(36).substr(2, 9)}`;
---

{downloadUrl && (
  <div class={`download-button-container ${className}`}
       data-track-id={trackId}
       data-track-title={track.title}
       data-download-url={downloadUrl}
       data-track-audio={track.url || ''}
       data-track-original-url={track.originalUrl || ''}
       data-track-original-content={JSON.stringify(track.originalContent || {})}>

    <!-- 简洁的下载按钮 -->
    <button class="download-btn" data-action="download" title="Download track">
      <svg class="download-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7,10 12,15 17,10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
    </button>

    <!-- 文件大小显示 -->
    {track.size && (
      <div class="file-size">{track.size}</div>
    )}
  </div>
)}

<style is:global>
  .download-button-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  /* 下载按钮 */
  .download-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: #b3b3b3;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .download-btn:hover {
    background: rgba(29, 185, 84, 0.2);
    color: #1DB954;
    transform: scale(1.1);
  }

  .download-btn:active {
    transform: scale(0.95);
  }

  .download-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2;
  }

  /* 文件大小 */
  .file-size {
    color: #888;
    font-size: 0.65rem;
    font-variant-numeric: tabular-nums;
    text-align: center;
    min-width: 40px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .download-btn,
    .download-progress,
    .download-complete,
    .download-error {
      width: 24px;
      height: 24px;
    }

    .download-icon,
    .complete-icon,
    .error-icon {
      width: 12px;
      height: 12px;
    }

    .progress-ring {
      width: 20px;
      height: 20px;
    }

    .progress-text {
      font-size: 0.45rem;
    }

    .file-size {
      font-size: 0.6rem;
    }
  }
</style>
